<template>
  <div>
    <div class="zwfw_pop">
      <div class="title">
        <div class="title_text">主题聚焦</div>
        <img class="close_btn" src="@/assets/leader/img/zwfw/close.png" @click="close" />
      </div>
      <div class="pop_content">
        <BlockBox title="最多跑一次事项" class="content_box" :isListBtns="false" :blockHeight="342">
          <div class="ycsx_chart" ref="ycsxChart"></div>
        </BlockBox>
        <BlockBox
          title="部门及时办结率TOP5"
          class="content_box"
          :isListBtns="false"
          :blockHeight="342"
        >
          <div class="top5_content">
            <div class="top5_top" style="width: 350px">
              <div class="top5_item">
                <div class="item_text">TOP1</div>
                <div class="top5_chart" ref="top1Chart"></div>
              </div>
              <div class="top5_item">
                <div class="item_text">TOP2</div>
                <div class="top5_chart" ref="top2Chart"></div>
              </div>
              <div class="top5_item">
                <div class="item_text">TOP3</div>
                <div class="top5_chart" ref="top3Chart"></div>
              </div>
            </div>
            <div class="top5_top" style="width: 250px">
              <div class="top5_item">
                <div class="item_text">TOP4</div>
                <div class="top5_chart" ref="top4Chart"></div>
              </div>
              <div class="top5_item">
                <div class="item_text">TOP5</div>
                <div class="top5_chart" ref="top5Chart"></div>
              </div>
            </div>
          </div>
          <!-- <RingPerChart4 :data="data" :options="options" /> -->
        </BlockBox>
        <BlockBox title="事项分类统计" class="content_box" :isListBtns="false" :blockHeight="342">
          <div class="fxtj">
            <div class="fxtj_title">
              <div class="title_text">申报材料</div>
            </div>
            <div class="fxtj_head">
              <ul>
                <li v-for="(item, index) in clsbList" :key="index">
                  <span>{{ item.name }}</span>
                  <div class="num_box">
                    <countTo
                      ref="countTo"
                      :startVal="$countTo.startVal"
                      :decimals="$countTo.decimals(item.num)"
                      :endVal="item.num"
                      :duration="$countTo.duration"
                    />
                    <span>件</span>
                  </div>
                </li>
              </ul>
            </div>
            <div class="fxtj_title">
              <div class="title_text">审批结果</div>
            </div>
            <div class="fxtj_head">
              <ul>
                <li v-for="(item, index) in clsbList" :key="index">
                  <span>{{ item.name }}</span>
                  <div class="num_box">
                    <countTo
                      ref="countTo"
                      :startVal="$countTo.startVal"
                      :decimals="$countTo.decimals(item.num)"
                      :endVal="item.num"
                      :duration="$countTo.duration"
                    />
                    <span>件</span>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </BlockBox>
        <BlockBox title="大厅办件量TOP10" class="content_box" :isListBtns="false" :blockHeight="342">
          <div class="table_content">
            <swiper-table
              :data="sxqhList"
              :titles="['排名', '事项', '取号量']"
              :widths="['82px', '320px', '120px']"
              content-height="220px"
            />
          </div>
        </BlockBox>
      </div>
    </div>
    <div>
      <transition name="fade">
      <div
        class="bg-header"
      ></div>
    </transition>
    </div>
  </div>
</template>

<script>
import BlockBox from '@/components/leader/common/blockBox.vue'
import echarts from 'echarts'
import SwiperTable from '@/components/leader/leader_zwjs/SwiperTable.vue'
import 'swiper/css/swiper.css'
export default {
  name: 'ztjjPop',
  components: {
    BlockBox,
    SwiperTable,
  },
  data() {
    return {
      ycsxData: [
        { name: '权力事项', value: 16257 },
        { name: '公共服务事项', value: 2296 },
        { name: '就近跑一次事项', value: 1851 },
        { name: '不用跑一次事项', value: 720 },
      ],
      options: {
        gradientColor: ['#D7BC2F', '#D7BC2F'],
      },
      sxqhList: [
        ['NO.1', '社保卡业务', '200.15万件'],
        ['NO.2', '人员首次参保、续保、停保、退保', '116.02万件'],
        ['NO.3', '机动车登记（含注册、登记、转移）', '104.29万件'],
        ['NO.4', '延期申报申请审批（申报受理）', '57.95万件'],
        ['NO.5', '失业金领取', '48.5万件'],
        ['NO.6', '身份证补办', '38.1万件'],
        ['NO.7', '不动产登记', '28.78万件'],
        ['NO.8', '房屋租赁', '25.11万件'],
        ['NO.9', '就业创业证申领', '10.13万件'],
        ['NO.10', '医保查询', '8.99万件'],
      ],
      clsbList: [
        {
          num: 98,
          name: '材料待取件',
        },
        {
          num: 200,
          name: '材料已取件',
        },
      ],
      spjgList: [
        {
          num: 98,
          name: '结果待取件',
        },
        {
          num: 200,
          name: '结果已取件',
        },
      ],
    }
  },
  mounted() {
    this.ycsxChartMethod(this.$refs.ycsxChart)
    this.drawTop5(this.$refs.top1Chart, '#D7BC2F', 92, '教育局')
    this.drawTop5(this.$refs.top2Chart, '#28C7FF', 88, '规划局')
    this.drawTop5(this.$refs.top3Chart, '#FF8943', 81, '人社局')
    this.drawTop5(this.$refs.top4Chart, '#3DF477', 78, '国土局')
    this.drawTop5(this.$refs.top5Chart, '#9243FF', 75, '环保局')
  },
  methods: {
    close() {
      this.$emit('closeEmit')
    },
    drawTop5(id, color, per, name) {
      let option = {
        // backgroundColor: '#fff',
        title: [
          {
            text: `${per}%`,
            textStyle: {
              color: '#FFFFFF',
              fontSize: 18,
            },
            itemGap: 20,
            left: '24%',
            top: '30%',
          },
          {
            text: name,
            textStyle: {
              color: '#C2DDFC',
              fontSize: 12,
              fontWeight: 'normal',
            },
            itemGap: 20,
            left: '24.5%',
            top: '52%',
          },
        ],
        grid: [
          {
            top: '10%',
            width: '50%',
            left: '45%',
            containLabel: true,
          },
        ],
        angleAxis: {
          polarIndex: 0,
          min: 0,
          max: 100,
          show: false,
          // boundaryGap: ['40%', '40%'],
          startAngle: 90,
        },
        radiusAxis: {
          type: 'category',
          show: true,
          axisLabel: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
        },
        polar: [
          {
            center: ['50%', '50%'], //中心点位置
            radius: '180%', //图形大小
          },
        ],
        series: [
          {
            type: 'bar',
            z: 10,
            name: name,
            data: [per],
            showBackground: false,
            backgroundStyle: {
              borderWidth: 10,
              width: 10,
            },
            coordinateSystem: 'polar',
            roundCap: true,
            barWidth: 6, //大的占比环
            itemStyle: {
              normal: {
                opacity: 1,
                color: color,
              },
            },
          },
        ],
      }
      let myChart = echarts.init(id, 'ucdc')
      myChart.setOption(option)
      window.addEventListener('resize', function () {
        myChart.resize()
      })
    },
    ycsxChartMethod(id) {
      let seriesData = this.ycsxData
      let total = seriesData
        .map((item) => {
          return item.value
        })
        .reduce((sum, currentValue) => {
          return (sum += currentValue)
        }, 0)
      let option = {
        color: ['#2EF6FF', '#6D5AE2', '#079AE9', '#FFBA4F'],
        title: [
          {
            top: 90,
            left: 54,
            text: '{val|' + total + '}',
            textStyle: {
              rich: {
                val: {
                  fontSize: 28,
                  fontWeight: 'bold',
                  color: '#fff',
                  padding: [10, 40],
                },
                name: {
                  fontSize: 14,
                  color: '#C2DDFC',
                  padding: [0, 60],
                },
              },
            },
          },
        ],
        grid: {
          top: '5%',
          left: 0,
          right: '1%',
          bottom: 5,
          containLabel: true,
        },
        legend: {
          show: true,
          itemWidth: 14,
          itemHeight: 10,
          itemGap: 18,
          right: 20,
          top: 60,
          textStyle: {
            fontSize: 14,
            color: '#C2DDFC',
          },
          orient: 'vertical',
          icon: 'circle',
          formatter: (name) => {
            if (seriesData.length) {
              const item = seriesData.filter((item) => item.name === name)[0]
              return `${name}：${item.value}`
            }
          },
        },
        series: [
          {
            name: '需求类型占比',
            type: 'pie',
            center: ['30%', '50%'],
            radius: ['60%', '80%'],
            itemStyle: {
              normal: {
                borderWidth: 5,
                // borderColor: "#fff"
              },
            },
            label: {
              normal: {
                show: false,
                position: 'center',
                formatter: '{value|{c}}\n{label|{b}}',
                rich: {
                  value: {
                    padding: 5,
                    align: 'center',
                    verticalAlign: 'middle',
                    fontSize: 32,
                  },
                  label: {
                    align: 'center',
                    verticalAlign: 'middle',
                    fontSize: 16,
                  },
                },
              },
              emphasis: {
                show: false,
                textStyle: {
                  fontSize: '12',
                },
              },
            },
            labelLine: {
              show: false,
              length: 0,
              length2: 0,
            },
            data: seriesData,
          },
          {
            name: '',
            type: 'gauge',
            center: ['30%', '50%'],
            radius: '54%',
            // splitNumber: 10, // 刻度数量
            // min: 0, // 最小刻度
            // max: dataX, // 最大刻度
            // 仪表盘轴线相关配置
            axisLine: {
              lineStyle: {
                color: [
                  [
                    1,
                    {
                      type: 'radial',
                      x: 0.5,
                      y: 0.59,
                      r: 0.6,
                      colorStops: [
                        {
                          offset: 0.72,
                          color: '#032046',
                        },
                        {
                          offset: 0.94,
                          color: '#086989',
                        },
                        {
                          offset: 0.98,
                          color: '#0FAFCB',
                        },
                        {
                          offset: 1,
                          color: '#0EA4C1',
                        },
                      ],
                    },
                  ],
                ],
                width: 1,
              },
            },
            // 分隔线
            splitLine: {
              show: false,
            },
            // 刻度线
            axisTick: {
              show: false,
            },
            // 刻度标签
            axisLabel: {
              show: false,
            },

            detail: {
              show: false,
            },
          },
        ],
      }
      let myChart = echarts.init(id, 'ucdc')
      myChart.setOption(option)
      window.addEventListener('resize', function () {
        myChart.resize()
      })
    },
  },
}
</script>
<style lang="less" scoped>
 .bg-header {
    position: absolute;
    width: 100%;
    height: 1080px;
    background: rgba(2, 14, 50, 0.75);
    z-index: 999999;
    top:0;
    left:0;
  }
.zwfw_pop {
  width: 1251px;
  height: 839px;
  position: absolute;
  z-index: 99999999;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border-radius: 11px;
  border: 1px solid;
  background: #001638;
  border-image: linear-gradient(360deg, rgba(57, 133, 255, 0.5), rgba(0, 205, 255, 0)) 1 1;
  .title {
    margin: 0 auto;
    width: 634px;
    height: 74px;
    background: url(~@/assets/csts/title1.png) no-repeat;
    text-align: center;
    margin-bottom: 37px;
    .title_text {
      display: inline-block;
      line-height: 74px;
      font-size: 40px;
      font-family: YouSheBiaoTiHei;
      color: #ffffff;
      background: linear-gradient(180deg, #ffffff 0%, #2aebff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .close_btn {
      position: absolute;
      top: 10px;
      right: 30px;
      width: 44px;
      height: 44px;
      cursor: pointer;
    }
  }
  .pop_content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    .content_box {
      width: 533px;
      .ycsx_chart {
        width: 450px;
        height: 250px;
      }
      .top5_content {
        display: flex;
        flex-direction: column;
        align-items: center;
        .top5_top {
          display: flex;
          justify-content: space-between;
          .top5_item {
            display: flex;
            flex-direction: column;
            justify-content: center;
            .item_text {
              font-size: 18px;
              font-family: SourceHanSansCN-Heavy, SourceHanSansCN;
              font-weight: 800;
              color: #ffffff;
              line-height: 27px;
              margin-bottom: 12px;
            }
            .top5_chart {
              width: 95px;
              height: 95px;
            }
          }
        }
      }
      .table_content {
        margin-top: 24px;
        margin-left: 5px;
        overflow: hidden;
        width: 522px;
        .item_cont1 {
          flex: 1;
          ::v-deep .row-item {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #37c1ff;
            line-height: 20px;
          }
          ::v-deep .swiper-item {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
          }
        }
      }
      .fxtj {
        display: flex;
        flex-direction: column;
        margin-left: 21px;
        margin-top: 24px;
        .fxtj_title {
          background: url(~@/assets/leader/img/zwfw/bg9.png) no-repeat center / 100% 100%;
          width: 492px;
          height: 17px;
          .title_text {
            font-size: 22px;
            font-family: PangMenZhengDao;
            color: #ffbc6c;
            line-height: 17px;
            letter-spacing: 2px;
            text-align: left;
            margin-left: 29px;
          }
        }
        .fxtj_head {
          // padding: 0 14px 0 13px;
          margin-top: 13px;
          margin-bottom: 14px;
          margin-left: 4px;
          width: 482px;
          ul {
            display: flex;
            justify-content: space-between;
            li {
              width: 236px;
              height: 96px;
              background: url(~@/assets/leader/img/zwfw/bg10.png) no-repeat center / 100% 100%;
              background-size: 100% 100%;
              // line-height: 37px;
              text-align: center;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              & span:first-of-type {
                font-size: 16px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #ffffff;
                line-height: 22px;
              }
              .num_box {
                display: flex;
                align-items: flex-end;
                & span:first-of-type {
                  font-size: 36px;
                  font-family: DIN-BlackItalic, DIN;
                  font-weight: normal;
                  color: #6fdcff;
                  line-height: 44px;
                }
                & span:last-of-type {
                  font-size: 14px;
                  font-family: PingFangSC-Medium, PingFang SC;
                  font-weight: 500;
                  color: #6fdcff;
                  line-height: 20px;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>