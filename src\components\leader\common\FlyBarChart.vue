<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
// import resize from '@/utils/resize';
import echarts from 'echarts';
import { RocketBox, Rocket } from '@/assets/svg/rocket.js';

export default {
  // mixins: [resize],
  name: 'FlyBarChart',
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    color: {
      type: String,
      default: '#fff'
    },
    echartData: {
      type: Object,
      default() {
        return {
          name: ['市容环境', '交通管理', '公积金', '医保', '物业管理', '社保咨询'],
          value: [2450, 2135, 1623, 1387, 1092, 951]
        };
      }
    }
  },
  data() {
    return {
      chart: null
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart(this.echartData);
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart(data) {
      this.chart = echarts.init(this.$el);
      let i = 0
      let empindex=0
      let flyindex=0
      let colorArr = ['rgba(209, 173, 82, 1)', 'rgba(149, 153, 162, 1)', 'rgba(214, 159, 120, 1)', 'rgba(75, 206, 255, 1)','rgba(75, 206, 255, 1)','rgba(75, 206, 255, 1)','rgba(75, 206, 255, 1)','rgba(75, 206, 255, 1)']
      const option = {
        grid: {
          left: '5%',
          right: '10%',
          bottom: '2%',
          top: '0%',
          containLabel: true
        },
        tooltip: {
          show: true,
          trigger: 'axis',
          formatter: function(param) {
            return param[0].value;
          },
          textStyle: {
            color: '#FFCC50',
            fontSize: 22
          }
        },
        xAxis: {
          show: false,
          type: 'value',
          axisLabel: {
            show: true,
            textStyle: {
              color: '#fff'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: '#3E3E3E'
            }
          },
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          }
        },
        yAxis: [
          {
            type: 'category',
            inverse: true,
            axisLabel: {
              show: true,
              textStyle: {
                color: 'rgba(255, 255, 255, 0.85)',
                fontSize: 22
              }
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: true
            },
            data: data.name
          },
          {
            type: 'category',
            inverse: true,
            axisLabel: {
              show: true,
              textStyle: {
                color: 'rgba(255, 255, 255, 0.85)',
                fontSize: 22
              }
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: true
            },
            data: data.value
          }
        ],
        series: [
          {
            name: '排名',
            type: 'bar',
            itemStyle: {
              normal: {
                barBorderRadius: 20,
                 color: function () {
                    if (i === colorArr.length) {
                      i = 0
                    }
                    return colorArr[i++]
                  },
                // color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                //   {
                //     offset: 0,
                //     color: '#1582CE'
                //   },
                //   {
                //     offset: 1,
                //     color: '#4BCEFF'
                //   }
                // ])
              },
              emphasis: {
                barBorderRadius: 20,
                color: function () {
                    if (empindex === colorArr.length) {
                      empindex = 0
                    }
                    return colorArr[empindex++]
                  },
                // color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                //   {
                //     offset: 0,
                //     color: '#FFDD68'
                //   },
                //   {
                //     offset: 1,
                //     color: '#CCA524'
                //   }
                // ])
              }
            },
            label: {
              show: false,
              position: 'right',
              color: '#1798ff',
              offset: [10, 0],
              fontSize: 22
            },
            z: 2,
            barWidth: 16,
            data: data.value
          },
          {
            name: 'fly',
            type: 'pictorialBar',
            barGap: '-100%',
            symbolPosition: 'end',
            symbol: RocketBox,
            symbolSize: [35, 20],
            symbolOffset: [8, 0],
            silent: true,
            itemStyle: {
               color: function () {
                    if (flyindex === colorArr.length) {
                      flyindex = 0
                    }
                    return colorArr[flyindex++]
                  },
            },
            emphasis: {
              itemStyle: { color: '#CCA524' }
            },
            z: 4,
            data: data.value
          },
          {
            name: 'rocket',
            type: 'pictorialBar',
            barGap: '-100%',
            symbolPosition: 'end',
            symbol: Rocket,
            symbolSize: [12, 12],
            symbolOffset: [2, 0],
            silent: true,
            itemStyle: {
              color: '#FFFFFF'
            },
            z: 5,
            data: data.value,
            symbolRotate: 315
          }

          // {
          //   name: '排名',
          //   type: 'bar',
          //   itemStyle: {
          //     normal: {
          //       barBorderRadius: 20,
          //       color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          //         {
          //           offset: 0,
          //           color: '#1582CE'
          //         },
          //         {
          //           offset: 1,
          //           color: '#4BCEFF'
          //         }
          //       ])
          //     },

          //   },
          //   z: 2,
          //   barWidth: 10,
          //   data: data.value
          // },
        ]
      };
      this.chart.setOption(option);
    }
  },
  watch: {
    echartData(val) {
      this.initChart(val);
    }
  }
};
</script>
