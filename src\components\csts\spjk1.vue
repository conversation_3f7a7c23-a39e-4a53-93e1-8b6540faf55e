<template>
  <div class="ai_waring">
    <div class="title">
      <span>视频监控</span>
      <img @click="closeEmit" class="close_btn" src="@/assets/shzl/map/close_btn.png" alt="" />
    </div>
    <div class="content">
      <div><span>简称：</span><span>gl21954894894</span></div>
      <div><span>地址：</span><span>南京市鼓楼区中央门街道南京西路22号</span></div>
      <div><span>状态：</span><span>在线</span></div>
      <div><span>类型：</span><span>公共交通</span></div>
      <div class="pic">
        <!-- <hlsVideo class="video_" :src="testUrl1"></hlsVideo> -->
        <LivePlayer class="video_" :videoUrl="testUrl1" fluent autoplay live stretch></LivePlayer>
      </div>
      <div class="btns">
        <div class="item">任务指派</div>
        <div class="item">连线网格员</div>
        <div class="item" @click="step">指挥调度</div>
      </div>
    </div>
  </div>
</template>

<script>
import hlsVideo from '@/components/leader/hlsVideo'
import LivePlayer from '@liveqing/liveplayer'
export default {
  components: {
    hlsVideo,
    LivePlayer
  },
  name: 'wgyPop',
  data() {
    return {
      // testUrl1: 'http://**********:8087/pulllive/pulllive_32010199001329000011/hls.m3u8',
      // testUrl1: 'http://**********:8088/firstfloor/stream1/hls.m3u8'
      testUrl1: require('@/assets/leader/video/video1.mp4')
    }
  },
  methods: {
    closeEmit() {
      this.$emit('closeEmit')
    },
    moreXl() {
      this.$emit('moreXl')
    },
    step() {
      window.open('http://**********:8090/zhddcp-dist/#/zsdd', '_blank')
    }
  }
}
</script>

<style lang="less" scoped>
.ai_waring {
  width: 413px;
  background: url(~@/assets/shzl/map/ai_popBg.png) no-repeat center / 100% 100%;
  background-size: 100% 100%;
  z-index: 1000;
  padding-top: 1px;

  .title {
    background: url(~@/assets/shzl/map/title_bg.png) no-repeat center / 100% 100%;
    background-size: 100% 100%;
    width: 375px;
    height: 47px;
    margin: 20px auto 0;
    line-height: 47px;
    text-align: left;
    text-indent: 27px;
    font-size: 18px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #ffffff;
    position: relative;
    span {
      background: linear-gradient(180deg, #ffffff 0%, #2aa6ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .close_btn {
      position: absolute;
      top: 11px;
      right: 25px;
      cursor: pointer;
    }
  }
  .content {
    padding: 20px 50px 45px 48px;
    & > div {
      display: flex;
      justify-content: space-between;
      &:not(:last-of-type) {
        margin-bottom: 14px;
      }
      & span:first-of-type {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
      }
      & span:last-of-type {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
      }
    }
    .pic {
      margin-top: 18px;
      position: relative;
      .video_ {
        width: 316px;
        height: 169px;
      }
      & .more_ {
        position: absolute;
        top: 8px;
        right: 9px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #000000;
        background: linear-gradient(180deg, #ffffff 0%, #73b3ff 100%);
        box-shadow: 0px 2px 4px 0px rgba(1, 19, 56, 0.5);
        border-radius: 10px;
        padding: 2px 6px;
      }
    }
  }
  .btns {
    display: flex;
    justify-content: space-between;
    .item {
      width: 100px;
      height: 33px;
      line-height: 33px;
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #ffffff;
      background: url('~@/assets/jcyj/btn.png') no-repeat center / 100% 100%;
      cursor: pointer;
    }
  }
}
</style>
