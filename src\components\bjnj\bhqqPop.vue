<template>
	<div class="event" v-if="show">
		<div class="title">
			<span>驳回</span>
			<i class="el-icon-close close" @click="closeFn"></i>
		</div>
		<div class="content">您确定驳回调度请求吗?</div>
		<div class="btns">
			<!-- <div class="btn" @click="handle(0)">事件详情</div>
      <div class="btn" @click="handle(1)">一般处置</div>
      <div class="btn" @click="handle(2)">应急处置</div> -->
			<div class="btn" v-for="(item, index) in btns" :key="index" @click="handle(index)">{{ item }}</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'EventDialog',
	props: {
		value: {
			type: Boolean,
			default: false,
		},
		list: {
			type: Object,
			default: () => {
				return {
					title: '地震灾害',
					type: '突发事件',
					time: '2023/06/02 08:56:32',
					address: '南京市鼓楼区宁海路199号',
					describe: 'XX附近发生地震伴有泥石流',
				}
			},
		},
		btns: {
			type: Array,
			default: () => ['取消', '确定'],
		},
	},
	computed: {
		show() {
			return this.value
		},
	},
	methods: {
		closeFn() {
			this.$emit('input', false)
		},
		handle(i) {
			this.$emit('handle', i)
		},
	},
}
</script>

<style lang="less" scoped>
.event {
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	z-index: 1111;
	width: 413px;
	height: 262px;
	margin: 0 auto;
	box-sizing: border-box;
	background: #001c40;
	border: 1px solid #023164;

	.title {
		width: 100%;
		height: 47px;
		line-height: 47px;
		padding: 0 16px;
		margin: 0 auto;
		box-sizing: border-box;
		background: #104993;
		border-radius: 0px 0px 0px 0px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		span {
			font-size: 18px;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			background: linear-gradient(180deg, #ffffff 0%, #2aa6ff 100%);
			background-clip: text;
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}

		.close {
			font-size: 16px;
			box-sizing: border-box;
			color: #80caff;
			cursor: pointer;
		}
	}
	.content {
		font-family: PingFangSC, PingFang SC;
		font-size: 22px;
		color: #ffffff;
		line-height: 26px;
		text-align: center;
		font-style: normal;
		margin-top: 38px;
	}
	.btns {
		margin-top: 75px;
		display: flex;
		justify-content: center;
		gap: 10px;
		.btn {
			border-radius: 4px 4px 4px 4px;
			border: 1px solid #d0deee;
			padding: 9px 16px;
			margin: 0 4px;

			display: flex;
			justify-content: center;
			align-items: center;

			cursor: pointer;

			font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
			font-weight: normal;
			font-size: 14px;
			color: #d0deee;
			line-height: 14px;
			text-align: center;
			font-style: normal;
			text-transform: none;

			&:last-child {
				background: #159aff;
				color: #fff;
				border: 1px solid #159aff;
			}

			&:first-child:hover {
				background: #159aff;
				border: 1px solid #159aff;
				color: #fff;
			}
		}
	}
}
</style>
