<template>
  <div class="map-tools">
    <ul>
      <li
        v-for="(it, i) of btns"
        :key="i"
        :style="{
          background: `url(${it.icon}) no-repeat`,
          opacity: it.disabled ? '1' : '0.3',
          cursor: it.disabled ? 'pointer' : 'not-allowed'
        }"
        @click="clickTool(i)"
      ></li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'Tools',
  props: {
    btns: {
      type: Array,
      default: () => {
        return [
          {
            icon: require('@/assets/map/icon2.png'),
            disabled: false
          },
          {
            icon: require('@/assets/map/icon1.png'),
            disabled: false
          },
          {
            icon: require('@/assets/map/icon3.png'),
            disabled: true
          }
        ]
      }
    }
  },
  methods: {
    clickTool(i) {
      this.$emit('handleClick', i)
    }
  }
}
</script>

<style lang="less" scoped>
.map-tools {
  position: absolute;
  top: 741px;
  right: 539px;
  z-index: 1000;
  ul {
    display: flex;
    flex-direction: column;
    gap: 16px;
    li {
      width: 44px;
      height: 44px;
      cursor: pointer;
    }
  }
}
</style>
