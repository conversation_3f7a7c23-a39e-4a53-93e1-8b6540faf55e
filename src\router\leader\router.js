/*
 * @Author: fanjialong
 * @Date: 2023-07-21 09:34:10
 * @LastEditors: likang
 * @LastEditTime: 2024-08-28 14:21:47
 * @FilePath: /sjcz_nj_dp/src/router/leader/router.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const routes1 = [
  {
    path: '/',
    name: 'Home',
    redirect: '/singleLogin'
  },
  {
    path: '/singleLogin',
    name: 'singleLogin',
    component: () => import('@/views/leader/singleLogin.vue')
  },
  {
    path: '/zl',
    name: 'zl',
    component: () => import('@/views/leader/zl.vue'),
    meta: {
      title: '实时态势',
      pageTitle: '实时态势' 
    }
  },
  // {
  //   path: '/zl2',
  //   name: 'zl2',
  //   component: () => import('@/views/leader/zl2.vue'),
  //   meta: {
  //     title: '总览',
  //     pageTitle: '总览'
  //   }
  // },
  // {
  //   path: '/zt',
  //   name: 'zt',
  //   component: () => import('@/views/leader/zt.vue'),
  //   meta: {
  //     title: '总览',
  //     pageTitle: '总览'
  //   }
  // },
  {
    path: '/zhdd2',
    name: 'zhdd2',
    component: () => import('@/views/leader/zhdd2.vue'),
    meta: {
      title: '指挥调度',
      pageTitle: '指挥调度'
    }
  },
  // {
  //   path: '/zyzx',
  //   name: 'zyzx',
  //   component: () => import('@/views/leader/zyzx.vue'),
  //   meta: {
  //     title: '作业进度',
  //     pageTitle: '作业进度'
  //   }
  // },
  {
    path: '/zyjd',
    name: 'zyjd',
    component: () => import('@/views/leader/zyjd.vue'),
    meta: {
      title: '作业进度',
      pageTitle: '作业进度'
    }
  },
  // {
  //   path: '/zyzxOld2',
  //   name: 'zyzxOld2',
  //   component: () => import('@/views/leader/zyzxOld2.vue'),
  //   meta: {
  //     title: '作业进度',
  //     pageTitle: '作业进度'
  //   }
  // },
  // {
  //   path: '/zyzxNew',
  //   name: 'zyzxNew',
  //   component: () => import('@/views/leader/zyzxNew.vue'),
  //   meta: {
  //     title: '作业进度',
  //     pageTitle: '作业进度'
  //   }
  // },
  {
    path: '/mskx',
    name: 'mskx',
    component: () => import('@/views/leader/mskx.vue'),
    meta: {
      title: '麦收快讯',
      pageTitle: '麦收快讯'
    }
  },
  {
    path: '/jxhfz',
    name: 'mskx',
    component: () => import('@/views/leader/jxhfz.vue'),
    meta: {
      title: '机械化发展',
      pageTitle: '机械化发展'
    }
  },
  {
    path: '/invite',
    name: 'invite',
    component: () => import('@/views/leader/invite.vue'),
    meta: {
      title: '会商',
      pageTitle: '会商'
    }
  },
  {
    path: '/PCmeeting',
    name: 'PCmeeting',
    component: () => import('@/views/leader/PCmeeting.vue'),
    meta: {
      title: '大屏会商',
      pageTitle: '大屏会商'
    }
  },
  // {
  //   path: '/hszl',
  //   name: 'hszl',
  //   component: () => import('@/views/leader/Hszl.vue'),
  //   meta: {
  //     title: '湖熟总览',
  //     pageTitle: '湖熟总览'
  //   }
  // },
  // {
  //   path: '/djyl',
  //   name: 'djyl',
  //   component: () => import('@/views/leader/Djyl.vue'),
  //   meta: {
  //     title: '党建引领',
  //     pageTitle: '党建引领'
  //   }
  // },
  // {
  //   path: '/zhdd',
  //   name: 'zhdd',
  //   component: () => import('@/views/leader/Zhdd.vue'),
  //   meta: {
  //     title: '指挥调度',
  //     pageTitle: '指挥调度'
  //   }
  // },
  // {
  //   path: '/aqjg',
  //   name: 'aqjg',
  //   component: () => import('@/views/leader/Aqjg.vue'),
  //   meta: {
  //     title: '安全监管',
  //     pageTitle: '安全监管'
  //   }
  // },
  // {
  //   path: '/fxyp',
  //   name: 'fxyp',
  //   component: () => import('@/views/leader/fxyp.vue'),
  //   meta: {
  //     title: '分析研判',
  //     pageTitle: '分析研判'
  //   }
  // },
  // {
  //   path: '/tsgz',
  //   name: 'tsgz',
  //   component: () => import('@/views/leader/tsgz.vue'),
  //   meta: {
  //     title: '态势感知',
  //     pageTitle: '态势感知'
  //   }
  // },
  // {
  //   path: '/zhny',
  //   name: 'zhny',
  //   component: () => import('@/views/leader/zhny.vue'),
  //   meta: {
  //     title: '智慧农业',
  //     pageTitle: '智慧农业'
  //   }
  // },
  // {
  //   path: '/tymh',
  //   name: 'tymh',
  //   component: () => import('@/views/leader/tymh.vue'),
  //   meta: {
  //     title: '统一门户',
  //     pageTitle: '统一门户'
  //   }
  // },
  // {
  //   path: '/testRhtx',
  //   name: 'testRhtx',
  //   component: () => import('@/views/leader/testRhtx.vue'),
  //   meta: {
  //     title: '融合通信',
  //     pageTitle: '融合通信'
  //   }
  // },
  {
    path: '/map',
    name: 'map',
    component: () => import('@/views/leader/mapView.vue'),
    meta: {
      title: '地图',
      pageTitle: '地图'
    }
  }
]
export default routes1
