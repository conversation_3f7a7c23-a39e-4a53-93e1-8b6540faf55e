<template>
  <div class="img-upload">
    <el-upload class="attachments-uploader" accept="image/*" :before-upload="beforeUploadAttachments"
      :action="baseUrl + '/api/fileRelation/uploadFiles'" :on-success="uploadAttachmentsSuccess"
      :headers="{ Authorization: token }" name="files" :data="uploadParam" :file-list="attachmentsList"
      :disabled="disabled || attachmentsList.length == 5" :show-file-list="false">
      <i class="el-icon-plus attachments-uploader-icon"></i>
    </el-upload>

    <template v-if="attachmentsList.length">
      <el-tooltip v-for="(item, index) in attachmentsList" class="item" effect="dark" :content="item.fileName"
        placement="top" :key="index">
        <div class="attachments_box">
          <el-image class="attachments" :src="baseUrl + item.fileUrl"
            :preview-src-list="[baseUrl + item.fileUrl]"></el-image>
          <i class="el-icon-delete delete_icon" @click="deleteFileByNum(item)"></i>
        </div>
      </el-tooltip>
    </template>
    <p>支持上传.jpg、.png等图片格式,最多5张;</p>
  </div>
</template>

<script>
// import util from "@/libs/util.js";
import { bjnjUrl } from '@/utils/leader/const'
import http from '@/utils/leader/request'

export default {
  name: "fileUpload",
  props: {
    // 随机字符串
    annexNum: {
      type: String,
      default: "",
    },
    disabled: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      baseUrl: bjnjUrl,
      token: localStorage.token,
      uploadParam: {
        annexNum: this.annexNum,
        fileType: 1,
      },
      attachmentsList: [] // 回显的文件列表
    };
  },
  watch: {
    // 防止出现获取不到annexNum
    annexNum:{
      immediate: true,
      handler(annexNum){
        if (annexNum) {
          this.queryAnnexByAnnexNum();
        }
      }
    }
  },
  methods: {
    beforeUploadAttachments(file) {
      const isIMG = file.type.includes('image');
      const isLt2M = file.size / 1024 / 1024 < 10;

      if (!isIMG) {
        this.$message.error('上传头像图片只能是图片格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 10MB!');
      }
      return isIMG && isLt2M;
    },
    uploadAttachmentsSuccess(res) {
      if (res.code === 0) {
        if(!this.annexNum){
          this.uploadParam.annexNum = res.data[0].annexNum
          // 没有annexNum时将创建的annexNum返回出去
          this.$emit('getAnnexNum', res.data[0].annexNum)
        }
        this.attachmentsList.push(res.data[0])
        this.$Message.success(res.msg);
      } else {
        this.$Message.error("上传失败" + res.msg || "");
      }
    },
    deleteFileByNum(item) {
      if(!this.disabled){
        this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          http.delete(bjnjUrl + "/api/fileRelation/tFileRelations/" + item.id).then((res) => {
            if (res.code == 0) {
              this.$Message.success("删除成功");
              this.attachmentsList.splice(this.attachmentsList.indexOf(item), 1);
            }
          });
        })
      }
    },
    queryAnnexByAnnexNum() {
      http.get(bjnjUrl + "/api/fileRelation/queryAnnexByAnnexNum?annexNum=" + this.annexNum).then((res) => {
        this.attachmentsList = res.data;
      });
    }
  },
};
</script>

<style lang="less" scoped>
.img-upload {
  text-align: left;
  .attachments-uploader {
    display: inline-block;
  }

  /deep/.attachments-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  /deep/.attachments-uploader .el-upload:hover {
    border-color: #409EFF;
  }

  .attachments-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 150px;
    height: 150px;
    line-height: 150px;
    text-align: center;
  }

  .attachments_box {
    width: 150px;
    height: 150px;
    display: inline-block;
    margin: 0 5px;
    position: relative;

    .delete_icon {
      position: absolute;
      bottom: 0;
      right: 0;
      font-size: 20px;
      width: 30px;
      height: 30px;
      border-radius: 15px;
      background-color: aliceblue;
      text-align: center;
      line-height: 30px;
      cursor: pointer;
    }
  }

  .attachments {
    width: 150px;
    height: 150px;
  }
}
</style>
