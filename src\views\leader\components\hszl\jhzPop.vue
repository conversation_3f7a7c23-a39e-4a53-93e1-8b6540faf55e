<template>
  <div class="tkBox">
    <div class="tkTitle">
      <span>魅力菊展</span>
    </div>
    <img @click="closeEmitai" src="@/assets/hszl/tkGb.png" class="tkGb" />
    <BlockBox
      title="客流统计"
      subtitle="event monitoring"
      class="tkList"
      :isListBtns="false"
      :blockHeight="150"
    >
      <AroundSources :data="gdzlData" />
    </BlockBox>
    <BlockBox
      title="园区收入"
      subtitle="Air quality changes in the past week"
      class="tkList"
      :isListBtns="false"
      :blockHeight="150"
    >
      <AroundSources :data="yqsrData" />
    </BlockBox>
    <BlockBox title="实时监控" subtitle="land use" class="tkList" :isListBtns="false" :blockHeight="50"></BlockBox>
    <div class="time_video">
      <hlsVideo style="height: 100%;width: calc(50% - 10px);" :src="testUrl1"></hlsVideo>
      <hlsVideo style="height: 100%;width: calc(50% - 10px);" :src="testUrl2"></hlsVideo>
    </div>
  </div>
</template>

<script>
import BlockBox from '@/components/leader/common/blockBox.vue'
import SwiperTable from '@/components/leader/leader_zwjs/SwiperTable.vue'
// import airJcPopVue from '../../../../components/tsgz/airJcPop.vue'
import { getTdsy, getStjscg, getAirWeek, getStjc, getStzlcg } from '@/api/hs/hs.hszl.js'
import hlsVideo from '@/components/leader/hlsVideo'
import LivePlayer from '@liveqing/liveplayer'
import {
  getJhzVideo
} from '@/api/hs/hs.zhny.js'
import AroundSources from '@/views/leader/components/hszl/AroundSources.vue'

export default {
  name: 'hswhtk',
  components: {
    BlockBox,
    SwiperTable,
    hlsVideo,
    LivePlayer,
    AroundSources
  },
  data () {
    return {
      yqsrData: [
        {
          num: 499,
          tit: '累计销售',
          // img: require('@/assets/csts/icon.png'),
          // url: require('@/assets/zhdd/icon5.png'),
          icon: require('@/assets/fxyp/icon_gh1.png'),
          unit: '亿'
        },
        {
          num: 34,
          tit: '当日销售',
          // img: require('@/assets/csts/icon.png'),
          // url: require('@/assets/zhdd/icon6.png'),
          icon: require('@/assets/fxyp/icon_gh1.png'),
          unit: '亿'
        },
      ],
      gdzlData: [
        {
          num: 499,
          tit: '总客流数',
          // img: require('@/assets/csts/icon.png'),
          // url: require('@/assets/zhdd/icon5.png'),
          icon: require('@/assets/fxyp/icon_gh1.png'),
          unit: 'W'
        },
        {
          num: 34,
          tit: '今日客流数',
          // img: require('@/assets/csts/icon.png'),
          // url: require('@/assets/zhdd/icon6.png'),
          icon: require('@/assets/fxyp/icon_gh1.png'),
        },
        {
          num: 0,
          tit: '实时客流数',
          // img: require('@/assets/csts/icon.png'),
          // url: require('@/assets/zhdd/icon6.png'),
          icon: require('@/assets/fxyp/icon_gh1.png'),
        },
      ],
      testUrl1: '',
      testUrl2: '',
      box1BottomData: {
        data: [
          ['product', '量'],
          ['08.31', 50],
          ['09.01', 90],
          ['09.02', 80],
          ['09.03', 20],
          ['09.04', 30],
          ['09.05', 40],
          ['09.06', 20],
        ],
        options: {},
      },
      box4BottomData: {
        data: [
          ['product', '事件总数'],
          ['山林面积', 861],
          ['水域面积', 1026],
          ['耕地面积', 334],
        ],
        options: {
          colors: ['#80CF83', '#CDCF72', '#0280DF', '#13A4C0'],
          legend: {
            top: 10,
            // left: 100,
            // orient: 'vertical',
            itemWidth: 10,
            itemHeight: 10,
            itemRadius: 2,
            itemPadding: 5,
            itemDistance: 12,
            itemMarginTop: 12,
            textColor: 'rgba(255, 255, 255, 0.85)',
            fontWeight: '400',
            fontSize: '12px',
            fontFamily: 'DINPro-Medium',
            letterSpacing: '3px',
          },
          position: ['50%', '60%'],
          bgImg: {
            width: '60%',
            height: '60%',
            top: '42%',
            left: '50%',
          },
          unit: '亩',
          title: {
            fontSize: '16px',
            top: 80,
          },
          subtitle: {
            fontSize: '14px',
            top: 100,
          },
        },
        initOptions: {
          legend: {
            left: -20,
            // itemWidth: 8,
            labelFormatter: function () {
              return (
                '<span style="color:#fff;fontSize:14px">' +
                this.name +
                ' ' +
                '<span style="color:#fff;fontSize:14px">' +
                this.y +
                '亩' +
                '</span>' +
                ' ' +
                '<span style="color:#fff;fontSize:14px">' +
                Math.round(this.percentage) +
                '%' +
                '</span>'
              )
            },
          },
        },
      },
      stjsValue1: '',
      stjsValue2: '',
      stjsValue3: '',
      stjcValue1: '',
      stjcValue2: '',
      stjcValue3: '',
      stjcPics: [
        { pic: require('@/assets/hszl/hswhImg5.png'), type: '1' },
        { pic: require('@/assets/hszl/hswhImg6.png'), type: '2' },
        { pic: require('@/assets/hszl/hswhImg4.png'), type: '4' },
      ],
      stjsData: [],
      checkId: '',
      isSczttkShow: false,
    }
  },
  created () {
    // this.getTdsyFn()
    this.queryExhibition()

  },
  methods: {
    async queryExhibition () {
      let res = await getJhzVideo()
      this.testUrl1 = res.data.cameraDataVLO[0].url
      this.testUrl2 = res.data.cameraDataVLO[1].url
      console.log(res.data, '视频')
      this.gdzlData[0].num = res.data.visitDataVLO.totalVisitNum
      this.gdzlData[1].num = res.data.visitDataVLO.todayVisitNum

    },
    closeEmitai () {
      this.$emit('closeEmitai')
    },
    async getTdsyFn () {
      let res = await getTdsy()
      if (res?.code == '200') {
        this.box4BottomData.data = []
        this.box4BottomData.data = [
          ['product', ''],
          ...res.result.map((item) => [item.name, Number(item.sysvalue)]),
        ]
      }
    },

    showStjs (item) {
      this.checkId = item.type
      this.isSczttkShow = true
    },
  },
}
</script>

<style lang="less" scoped>
.tkBox {
  width: 1135px;
  height: 710px;
  background: url(~@/assets/hszl/tkBg.png) no-repeat center / 100% 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10000;
  padding: 0 43px;
  .tkTitle {
    width: 634px;
    height: 74px;
    background: url(~@/assets/hszl/tkTitle.png) no-repeat center / 100% 100%;
    margin: 0 auto;
    margin-top: 22px;
    line-height: 74px;
    span {
      font-size: 36px;
      font-family: PingFangSC, PingFang SC;
      color: #ffffff;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .tkGb {
    position: absolute;
    top: 38px;
    right: 43px;
  }
  .time_video {
    width: 100%;
    height: 350px;
    display: flex;
    justify-content: space-between;
    padding: 0 10px;
  }
  .tkList {
    float: left;
    margin: 0 32px;
    margin-top: 26px;
    .wrapper {
      width: 460px;
      height: 263px;
      .box6_top {
        display: flex;
        margin-top: 22px;
        margin-left: 11px;
        // justify-content: space-between;
        & > img {
          width: 59px;
          height: 44px;
          margin-right: 40px;
        }
        & .box6_top1 {
          display: flex;
          // justify-content: space-between;
          & span:first-of-type {
            font-size: 20px;
            font-family: Arial-BoldMT, Arial;
            font-weight: normal;
            color: #6fdcff;
            line-height: 23px;
          }
          & span:nth-of-type(2) {
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #86daf7;
            line-height: 22px;
            margin-left: 111px;
          }
          & span:last-of-type {
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #86daf7;
            line-height: 22px;
            margin-left: 94px;
          }
        }
        & .box6_top2 {
          display: flex;
          justify-content: space-between;
          & div:first-of-type {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: rgba(255, 255, 255, 0.7);
            line-height: 16px;
          }
          & div:nth-of-type(2) {
            margin-left: 26px;
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: rgba(255, 255, 255, 0.7);
            line-height: 16px;
          }
          & div:last-of-type {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: rgba(255, 255, 255, 0.7);
            line-height: 16px;
            margin-left: 30px;
          }
        }
      }
      .box6_bottom {
        width: 426px;
        height: 152px;
        margin: 40px 0 0 11px;
        background: url(~@/assets/hszl/hswhImg.png) no-repeat center / 100% 100%;
        .box6_bottom1 {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #b3daff;
          line-height: 22px;
          text-align: left;
          margin-top: -10px;
        }
        .box6_bottom2 {
          .box6_left {
            float: left;
            margin: 30px 0 0 43px;
            .box6_left1 {
              font-size: 25px;
              font-family: DINCondensed-Bold, DINCondensed;
              font-weight: bold;
              color: #ffffff;
              line-height: 30px;
              .span1 {
                font-size: 14px;
                font-family: DINCondensed-Bold, DINCondensed;
                font-weight: bold;
                color: #ffffff;
                line-height: 17px;
                margin-left: 2px;
              }
              .span2 {
                font-size: 14px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                color: #ff5113;
                line-height: 20px;
                margin-left: 9px;
              }
            }
            .box6_left2 {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #cbe5ff;
              line-height: 20px;
              text-align: left;
              margin-top: 4px;
            }
          }
          .box6_right {
            float: right;
            margin: 30px 9px 0 0;
            .box6_right1 {
              font-size: 25px;
              font-family: DINCondensed-Bold, DINCondensed;
              font-weight: bold;
              color: #ffffff;
              line-height: 30px;
              .span1 {
                font-size: 14px;
                font-family: DINCondensed-Bold, DINCondensed;
                font-weight: bold;
                color: #ffffff;
                line-height: 17px;
                margin-left: 2px;
              }
              .span2 {
                font-size: 14px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                color: #38cf47;
                line-height: 20px;
                margin-left: 9px;
              }
            }
            .box6_right2 {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #cbe5ff;
              line-height: 20px;
              text-align: left;
              margin-top: 4px;
            }
          }
        }
      }
      .tlBox {
        width: 100%;
        position: absolute;
        top: 10px;
        left: 50%;
        transform: translateX(-50%);
        .tlItem {
          float: left;
          margin-left: 40px;
          .tlList1 {
            float: left;
            width: 10px;
            height: 10px;
            border: 1px solid;
            border-image: linear-gradient(180deg, rgba(153, 228, 250, 1), rgba(96, 196, 243, 1)) 1 1;
            margin: 5px 5px;
          }
          .tlList2 {
            float: left;
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 20px;
          }
          .tlList3 {
            float: left;
            width: 10px;
            height: 10px;
            background: linear-gradient(180deg, #01ffc5 0%, rgba(0, 237, 185, 0) 100%);
            border: 1px solid;
            border-image: linear-gradient(180deg, rgba(153, 250, 241, 1), rgba(96, 243, 229, 0)) 1 1;
            margin: 5px 5px;
          }
          .tlList4 {
            float: left;
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 20px;
          }
          .tlList5 {
            float: left;
            width: 10px;
            height: 10px;
            background: #80cf83;
            margin: 5px 5px;
          }
          .tlList6 {
            float: left;
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 20px;
            .span1 {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #1effff;
              line-height: 20px;
              margin-left: 10px;
            }
            .span2 {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #d7bf5c;
              line-height: 20px;
              margin-left: 7px;
            }
          }
          .tlList7 {
            float: left;
            width: 10px;
            height: 10px;
            background: #cdcf72;
            margin: 5px 5px;
          }
          .tlList8 {
            float: left;
            width: 10px;
            height: 10px;
            background: #0280df;
            margin: 5px 5px;
          }
        }
      }
      .tlBox {
        top: 220px;
      }
    }
    .wrapper1 {
      padding: 40px 11px 0 11px;
      .wrapperList {
        width: 140px;
        height: 190px;
        background: url(~@/assets/hszl/hswhImg3.png) no-repeat center / 100% 100%;
        float: left;
        margin: 0 3px;
        .wrapperImg {
          width: 105px;
          height: 108px;
          background: url(~@/assets/hszl/hswhImg2.png) no-repeat center / 100% 100%;
          margin: 0 auto;
          margin-top: 7px;
          img {
            margin-top: 12px;
          }
        }
        .wrapperName {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #caecff;
          line-height: 20px;
          text-shadow: 0px 0px 1px #00132e;
          margin-top: -9px;
        }
        .wrapperNum {
          font-size: 24px;
          font-family: PingFangSC, PingFang SC;
          font-weight: normal;
          color: #ffffff;
          line-height: 29px;
          margin-top: 12px;
          span {
            font-size: 13px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #caecff;
            line-height: 18px;
            text-shadow: 0px 0px 1px #00132e;
            margin-left: 1px;
          }
        }
      }
    }
  }
}
</style>
