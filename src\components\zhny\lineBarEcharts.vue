<template>
  <div class="lineBarEcharts_box" ref="chartRef"></div>
</template>

<script>
export default {
  name: 'lineBarEcharts',
  props: {
    data: {
      type: Array,
      default: (item) => {
        return [
          ['product', '增速', '生产总值'],
          ['2016', 40, 8],
          ['2017', 36, 7],
          ['2018', 21, 4],
          ['2019', 34, 6],
          ['2020', 42, 6],
        ]
      },
    },
  },
  data() {
    return {
      // 生产总值图表配置项
      options: {
        yNameLeft: '总量（亿元）',
        yNameRight: '增速',
        xName: '年度',
      },
    }
  },
  mounted() {},
  watch: {
    data: {
      deep: true,
      immediate: true,
      handler(val) {
        this.$nextTick(() => {
          this.linebarChart(this.$refs.chartRef, this.data, this.options)
        })
      },
    },
  },
  methods: {
    // 柱状折线图
    linebarChart(id, data, linebarOptions) {
      let option = {
        tooltip: {
          show: true,
          trigger: 'axis',
          textStyle: {
            fontSize: 14,
          },
        },
        dataset: {
          source: data || [
            ['product', '增速', '生产总值'],
            ['2016', 40, 8],
            ['2017', 36, 7],
            ['2018', 21, 4],
            ['2019', 34, 6],
          ],
        },
        grid: {
          left: '12%',
          top: '25%',
          bottom: '20%',
        },
        legend: {
          icon: 'roundRect',
          itemGap: 12,
          itemWidth: 14,
          itemHeight: 14,
          top: '5%',
          right: 'center',
          textStyle: {
            fontSize: 14,
            color: 'rgba(255, 255, 255, 0.65)',
          },
        },
        xAxis: {
          name: linebarOptions.xName || '万',
          nameTextStyle: {
            padding: [40, 0, 0, -30],
            fontSize: 14,
            color: 'rgba(255, 255, 255, 0.6)',
          },
          type: 'category',
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.6)',
            },
          },
          axisLabel: {
            show: true,
            rotate: 0,
            interval: 0,
            textStyle: {
              padding: [4, 0, 0, 0],
              fontSize: 14,
              color: 'rgba(194, 221, 252, 1)',
            },
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: [
          {
            name: linebarOptions.yNameLeft || '万',
            nameTextStyle: {
              padding: [0, 0, 20, 30],
              fontSize: 14,
              color: '#BEE4FF',
            },
            // max: 80,
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: true,
              textStyle: {
                fontSize: 14,
                color: '#C2DDFC',
              },
            },
            splitLine: {
              lineStyle: {
                type: 'dashed',
                color: 'rgba(255, 255, 255, 0.15)',
              },
            },
          },
          {
            name: linebarOptions.yNameRight || '千万',
            nameTextStyle: {
              padding: [0, -20, 20, 30],
              fontSize: 14,
              color: '#BEE4FF',
            },
            type: 'value',
            // max: 80,
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },

            axisLabel: {
              color: '#BEE4FF',
              fontSize: 14,
              show: true,
              interval: 'auto',
              formatter: '{value}%',
            },
            // splitNumber: 5,
            splitLine: {
              lineStyle: {
                type: 'dashed',
                color: 'rgba(255, 255, 255, 0.15)',
              },
            },
            // max: 10,
          },
        ],
        series: [
          {
            type: 'line',
            yAxisIndex: 1,
            smooth: false,
            symbol: 'circle',
            showSymbol: true,
            symbolSize: 12,
            itemStyle: {
              normal: {
                color: linebarOptions.lineItemStyle || '#1EBCA0', //折点颜色
                shadowBlur: 2,
                shadowColor: 'rgba(0, 0, 0, .12)',
                shadowOffsetX: 2,
                shadowOffsetY: 2,
                lineStyle: {
                  color: linebarOptions.lineIineStyle || '#1EBCA0', //折线颜色
                },
              },
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: linebarOptions.areaStyle0 || 'rgba(216, 116, 56, 0.3)', // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: linebarOptions.areaStyle100 || 'rgba(216, 116, 56, 0)', // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          },
          {
            type: 'bar',
            yAxisIndex: 0,
            barWidth: 16,
            itemStyle: {
              color: linebarOptions.barItemStyle || '#248FDA',
            },
          },
        ],
      }
      let myChart = this.$echarts.init(id, 'ucdc')
      myChart.setOption(option)
      window.addEventListener('resize', function () {
        myChart.resize()
      })
    },
  },
}
</script>

<style lang="less" scoped>
.lineBarEcharts_box {
  width: 100%;
  height: 100%;
}
</style>