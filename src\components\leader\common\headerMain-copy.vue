<template>
	<div class="header">
		<div class="title">
			<!-- home切换 -->
			<div class="home_icon" v-if="false">
				<img src="@/assets/leader/img/component/header/home_icon.png" alt @click="showChildMenu = true" />
				<div class="icons" v-if="showChildMenu" @mouseleave="showChildMenu = false">
					<ul>
						<li v-for="(it, i) of icons" :key="i" @click="clickIcon(i)">{{ it }}</li>
					</ul>
				</div>
			</div>
			<!-- 中间标题 -->
			<div class="middle">
				<div class="middle1">全国农机作业指挥调度平台</div>
				<!-- <div class="middle2">National Agricultural Machinery Operation Command and Dispatch System</div> -->
				<div class="middle2">[{{ title1 }}{{ title2 }}{{ title3 }}{{ title4 }}]</div>
				<img src="@/assets/mskx/bg_head1.png" />
			</div>
			<!-- 下拉 -->
			<div class="dropBox">
				<!-- <img class="dropImg1" src="@/assets/bjnj/qy.png" alt=""> -->
				<img class="dropImg2" src="@/assets/bjnj/gb.png" @click="tcdl" alt="" />
				<div class="dropDown">
					<span class="dropName1" :title="username">{{ areaName }}</span>
					<span class="dropName2">您好！</span>
					<!-- <img src="@/assets/head/xlsj.png" alt /> -->
				</div>
			</div>
			<!-- 左边导航tab -->
			<div class="left_tab">
				<div
					v-for="(item, index) in leftBtns"
					:key="index"
					:class="['header_left', currentIndex == index ? 'active' : '']"
					@click="changeBtn(index, 'header_left')"
				>
					<span>{{ item }}</span>
				</div>
			</div>
			<!-- 右边导航tab -->
			<div class="right_tab">
				<div
					v-for="(item, index) in rightBtns"
					:key="index"
					:class="['header_right', currentIndex == index + 2 ? 'active' : '']"
					@click="changeBtn(index, 'header_right')"
				>
					<span>{{ item }}</span>
				</div>
			</div>
			<!-- 天气 -->
			<!-- <div class="weather">
        <img src="@/assets/leader/img/component/header/weather_icon.png" alt />
        <span>{{ tq.temperature }}°c</span>
        <span>{{ tq.weather }}</span>
      </div>-->
			<!-- 时间 -->
			<div class="time">
				<span class="time1">{{ time }}</span>
				<span class="time2">{{ time1 }}</span>
			</div>
		</div>
	</div>
</template>

<script>
// import { tianqiapi } from '@/api/common/common'
// import { tymhUrl } from '@/utils/leader/const'
import { screenLogout, cscpCurrentUserDetails } from '@/api/bjnj/zhdd.js'
export default {
	props: {
		icons: {
			type: Array,
			default() {
				return ['城市体征', '统一门户', '指挥调度', '监测预警', '事件处置', '考核研判']
			},
		},
	},
	data() {
		return {
			areaName: '',
			roleNames: '',
			username: '',
			currentIndex: 0,
			leftBtns: ['总览', '作业统计'],
			rightBtns: ['指挥调度', '作业进度'],
			timer: null,
			time: '',
			time1: '',
			time2: '',
			title1: '',
			title2: '',
			title3: '',
			title4: '',
			tq: {},
			showChildMenu: false,
			bgShow2: 0,
			bgShow3: 0,
			bgShow5: 0,
		}
	},
	methods: {
		async cscpCurrentUserDetails(data) {
			console.log(987654321)
			let res = await cscpCurrentUserDetails(data)
			this.title1 = ''
			this.title2 = ''
			this.title3 = ''
			this.title4 = ''
			console.log(res)
			if (res?.code == '0') {
				this.roleNames = res.data.roleNames
				this.username = res.data.username
				if (res.data.areaLevel == 0) {
					this.title1 = '全国'
				} else if (res.data.areaLevel == 1) {
					this.title2 = res.data.areaName
				} else if (res.data.areaLevel == 2) {
					this.title3 = res.data.areaName
				} else if (res.data.areaLevel == 3) {
					this.title4 = res.data.areaName
				}
				if (res.data.areaName) {
					if (res.data.areaLevel == 0) {
						this.areaName = '全国'
					} else {
						this.areaName = res.data.areaName
					}
				}
			}
		},
		tcdl() {
			let cookies = document.cookie.split(';')
			for (let i = 0; i < cookies.length; i++) {
				let cookie = cookies[i]
				let eqPos = cookie.indexOf('=')
				let name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie
				document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/'
			}
			if (cookies.length > 0) {
				for (let i = 0; i < cookies.length; i++) {
					let cookie = cookies[i]
					let eqPos = cookie.indexOf('=')
					let name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie
					let domain = location.host.substr(location.host.indexOf('.'))
					document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; domain=' + domain
				}
			}
			this.screenLogout()
		},
		async screenLogout(data) {
			let res = await screenLogout(data)
			console.log(res)
			if (res?.code == '0') {
				localStorage.setItem('token', '')
				window.location.href = res.data
			}
		},
		changeBtn(index, type) {
			console.log('index12346789', index)
			// if (this.title1) {
			//   this.title2 = ''
			//   this.title3 = ''
			//   this.title4 = ''
			// } else if (this.title2) {
			//   this.title3 = ''
			//   this.title4 = ''
			// } else if (this.title3) {
			//   this.title4 = ''
			// }

			this.cscpCurrentUserDetails()
			if (type == 'header_left') {
				if (this.currentIndex == index) {
				} else {
					this.$store.commit('invokerightShow4', false)
				}
				this.currentIndex = index
			} else {
				// if (index + 4 == 7) {
				//   return  this.$router.push('/tymh')
				// }
				if (this.currentIndex == index + 2) {
				} else {
					this.$store.commit('invokerightShow4', false)
				}
				this.currentIndex = index + 2
			}
			this.$store.commit('setCurrentIndex', this.currentIndex)

			switch (this.currentIndex) {
				case 0:
					this.$router.push('/zl')
					break
				case 1:
					this.$router.push('/mskx')
					break
				case 2:
					this.$router.push('/zhdd2')
					break
				case 3:
					this.$router.push('/zyzx')
					break
				default:
					break
			}
		},
		//  时间格式化
		getTime() {
			let myDate = new Date()
			let wk = myDate.getDay()
			let weeks = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
			this.time = this.dayjs().format('YYYY/MM/DD')
			this.time1 = this.dayjs().format('HH:mm:ss')
			this.time2 = weeks[wk]
		},
		// 天气
		// gettq () {
		//   tianqiapi().then(res => {
		//     // console.log(999, res)
		//     this.tq = res.lives[0]
		//   })
		// },
		clickIcon(i) {
			console.log(i)
		},
	},
	watch: {
		$route(to, from) {
			console.log(to.path)
			switch (to.path) {
				case '/zl':
					this.currentIndex = 0
					break
				case '/mskx':
					this.currentIndex = 1
					break
				case '/zhdd2':
					this.currentIndex = 2
					break
				case '/zyzx':
					this.currentIndex = 3
					break
				// case '/zhdd':
				//   this.currentIndex = 4
				//   break
				// case '/aqjg':
				//   this.currentIndex = 5
				//   break
				// case '/fxyp':
				//   this.currentIndex = 5
				//   break
				// case '/tymh':
				//   this.currentIndex = 6
				//   break
				default:
					break
			}
		},
		invokeRHtx(newValue, oldValue) {
			console.log('融合通信状态：', 'newValue' + newValue, 'oldValue' + oldValue)
		},
		invokerightShow2(newValue) {
			this.bgShow2 = newValue
			console.log('this.bgShow2', this.bgShow2)
			if (this.bgShow2.level == 'province') {
				this.title1 = ''
				this.title2 = this.bgShow2.name
			}
			if (this.bgShow2.level == 'city') {
				this.title1 = ''
				this.title3 = '●' + this.bgShow2.name
			}
			if (this.bgShow2.level == 'district') {
				this.title1 = ''
				this.title4 = '●' + this.bgShow2.name
			}
		},
		invokerightShow3(newValue) {
			console.log('newValue123', newValue)
			if (newValue == '100000' || newValue == '0') {
				this.title1 = '全国'
			}
			console.log('this.title4', this.title4)
			console.log('this.title3', this.title3)
			console.log('this.title2', this.title2)
			console.log('this.title1', this.title1)
			if (this.title4) {
				this.title4 = ''
			} else if (this.title3) {
				this.title3 = ''
			} else if (this.title2) {
				this.title2 = ''
				this.title1 = '全国'
			}
		},
		invokerightShow5(newValue) {
			this.bgShow5 = newValue
			console.log('this.bgShow5', this.bgShow5)
			if (this.bgShow5.level == '1') {
				if (this.title1) {
					this.title1 = ''
					this.title2 = this.bgShow5.areaName
				}
			}
			if (this.bgShow5.level == '2') {
				if (this.title1) {
					this.title1 = ''
					this.title3 = this.bgShow5.areaName
				} else {
					this.title3 = '●' + this.bgShow5.areaName
				}
			}
			if (this.bgShow5.level == '3') {
				if (this.title1) {
					this.title1 = ''
					this.title4 = this.bgShow5.areaName
				} else {
					this.title4 = '●' + this.bgShow5.areaName
				}
			}
		},
		headerMainCurrentIndex(newValue) {
			if (newValue == -1) {
			} else {
				this.changeBtn(newValue)
			}
		},
	},
	mounted() {
		this.cscpCurrentUserDetails()
		this.getTime()
		this.timer = setInterval(this.getTime, 1000)
		this.$store.commit('setCurrentIndex', this.currentIndex)
		// this.gettq()
		// this.tqtime = setInterval(this.gettq, 3600000)
	},
	beforeDestroy() {
		clearInterval(this.timer)
	},
	computed: {
		invokeRHtx() {
			return this.$store.state.rhtxState
		},
		invokerightShow2() {
			return this.$store.state.rightShow2
		},
		invokerightShow3() {
			return this.$store.state.rightShow3
		},
		invokerightShow5() {
			return this.$store.state.rightShow5
		},
		headerMainCurrentIndex() {
			return this.$store.state.headerMainCurrentIndex
		},
	},
}
</script>

<style lang="less" scoped>
.header {
	position: relative;
	z-index: 1003;
	.title {
		display: flex;
		z-index: 999;
		position: relative;
		.weather {
			display: flex;
			align-items: center;
			position: absolute;
			top: 49px;
			left: 50px;
			img {
				width: 53px;
				height: 39px;
			}
			& > span:first-of-type {
				font-size: 20px;
				font-family: PangMenZhengDao;
				color: #ffffff;
				line-height: 23px;
				text-shadow: 0px 2px 0px rgba(0, 32, 56, 0.5);
				margin-left: 8px;
			}
			& > span:last-of-type {
				font-size: 18px;
				font-family: PangMenZhengDao;
				color: #ffffff;
				line-height: 21px;
				letter-spacing: 1px;
				text-shadow: 0px 2px 0px rgba(0, 32, 56, 0.5);
				margin-left: 12px;
			}
		}
		.left_tab {
			position: absolute;
			left: 207px;
			top: 7px;
			display: flex;
			z-index: 99;
			.header_left {
				width: 187px;
				height: 39px;
				background: url(~@/assets/head/icon12.png) no-repeat;
				flex-shrink: 0;
				font-size: 20px;
				line-height: 39px;
				text-align: center;
				cursor: pointer;
				padding-left: 5px;
				span {
					display: inline-block;
					width: 87px;
					height: 29px;
					font-family: YouSheBiaoTiHei;
					font-size: 22px;
					color: #b0e0ff;
					line-height: 29px;
					text-shadow: 0px 1px 0px rgba(0, 32, 56, 0);
					text-align: center;
					font-style: normal;
					background-image: -webkit-gradient(linear, 0 0, 0 bottom, from(#ffffff), to(#89d9ff));
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
				}
				&:nth-of-type(2) {
					margin-left: 6px;
				}
				&:nth-of-type(3) {
					margin-left: 6px;
				}
				&:nth-of-type(4) {
					margin-left: 6px;
				}
				&.active {
					background: url(~@/assets/head/icon11.png) no-repeat;
					span {
						display: inline-block;
						width: 87px;
						height: 29px;
						font-family: YouSheBiaoTiHei;
						font-size: 22px;
						color: #b0e0ff;
						line-height: 29px;
						text-shadow: 0px 1px 0px rgba(0, 32, 56, 0);
						text-align: center;
						font-style: normal;
						background-image: -webkit-gradient(linear, 0 0, 0 bottom, from(#ffffff), to(#fff076));
						-webkit-background-clip: text;
						-webkit-text-fill-color: transparent;
					}
				}
			}
		}
		.right_tab {
			display: flex;
			position: absolute;
			right: 225px;
			top: 7px;
			z-index: 99;
			.header_right {
				width: 187px;
				height: 39px;
				background: url(~@/assets/head/icon13.png) no-repeat;
				flex-shrink: 0;
				font-size: 20px;
				line-height: 39px;
				text-align: center;
				cursor: pointer;
				padding-right: 5px;
				span {
					display: inline-block;
					width: 87px;
					height: 29px;
					font-family: YouSheBiaoTiHei;
					font-size: 22px;
					color: #b0e0ff;
					line-height: 29px;
					text-shadow: 0px 1px 0px rgba(0, 32, 56, 0);
					text-align: center;
					font-style: normal;
					background-image: -webkit-gradient(linear, 0 0, 0 bottom, from(#ffffff), to(#89d9ff));
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
				}
				&:nth-of-type(2) {
					margin-left: 6px;
				}
				&:nth-of-type(3) {
					margin-left: 6px;
				}
				&:nth-of-type(4) {
					margin-left: 6px;
				}
				&.active {
					background: url(~@/assets/head/icon14.png) no-repeat;
					span {
						display: inline-block;
						width: 87px;
						height: 29px;
						font-family: YouSheBiaoTiHei;
						font-size: 22px;
						color: #b0e0ff;
						line-height: 29px;
						text-shadow: 0px 1px 0px rgba(0, 32, 56, 0);
						text-align: center;
						font-style: normal;
						background-image: -webkit-gradient(linear, 0 0, 0 bottom, from(#ffffff), to(#fff076));
						-webkit-background-clip: text;
						-webkit-text-fill-color: transparent;
					}
				}
			}
		}
		.middle {
			position: absolute;
			left: 0px;
			top: 0px;
			z-index: 0;
			width: 1920px;
			height: 200px;
			font-size: 48px;
			font-family: PangMenZhengDao;
			color: #ffffff;
			line-height: 100px;
			letter-spacing: 6px;
			text-shadow: -2px -4px 0px rgba(0, 33, 57, 0.5);
			background: url(~@/assets/bjnj/dbBg.png) no-repeat center / 100% 100%;

			// background: linear-gradient(to top, rgba(0, 14, 35, 0) 0%, #000e23 100%);
			// -webkit-background-clip: text;
			// -webkit-text-fill-color: transparent;
			// img {
			//   // margin-top: -14px;
			// }
			img {
				width: 1920px;
				height: 141px;
				position: absolute;
				left: 0%;
				top: 0;
				z-index: -1;
			}
			span {
				font-size: 48px;
				font-family: PangMenZhengDao;
				color: #ffffff;
				line-height: 55px;
				letter-spacing: 2px;
				text-shadow: -2px -4px 0px rgba(0, 33, 57, 0.5);
				// -webkit-background-clip: text;
				// -webkit-text-fill-color: transparent;
			}
			.middle1 {
				width: 605px;
				height: 57px;
				// font-family: YouSheBiaoTiHei;
				font-size: 40px;
				color: #ffffff;
				line-height: 57px;
				letter-spacing: 6px;
				text-shadow: 0px 5px 0px rgba(0, 0, 0, 0);
				text-align: left;
				font-style: normal;
				background-image: -webkit-gradient(linear, 0 0, 0 bottom, from(#ffffff), to(#84d9fb));
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
				margin: 0 auto;
				margin-top: 14px;
				text-align: center;
			}
			.middle2 {
				// width: 384px;
				height: 24px;
				font-family: DINOT, DINOT;
				font-weight: normal;
				font-size: 20px;
				font-weight: 600;
				color: #3b4e5a;
				line-height: 22px;
				text-shadow: 0px 5px 0px rgba(0, 0, 0, 0);
				text-align: left;
				font-style: normal;
				text-transform: uppercase;
				background-image: -webkit-gradient(linear, 0 0, 0 bottom, from(#ecf5ff), to(#addaf4));
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
				margin: 0 auto;
				margin-top: -5px;
				letter-spacing: 0px;
				text-align: center;
			}
		}
		.time {
			position: absolute;
			top: 15px;
			right: 21px;
			.time1 {
				display: inline-block;
				font-family: YouSheBiaoTiHei;
				font-size: 14px;
				color: #7bf3fb;
				line-height: 18px;
				text-shadow: 0px 2px 0px rgba(0, 32, 56, 0.5);
				text-align: right;
				font-style: normal;
				margin-right: 4px;
				vertical-align: middle;
				margin-top: -7px;
			}
			.time2 {
				display: inline-block;
				font-family: YouSheBiaoTiHei;
				font-size: 20px;
				color: #ffffff;
				line-height: 26px;
				text-shadow: 0px 2px 0px rgba(0, 32, 56, 0.5);
				text-align: right;
				font-style: normal;
			}
		}
		.dropBox {
			position: absolute;
			.dropImg1 {
				float: left;
				margin: 19px 0 0 21px;
			}
			.dropDown {
				float: left;
				margin: 16px 0 0 16px;
				.dropName1 {
					float: left;
					width: 50px;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
					font-family: YouSheBiaoTiHei;
					font-size: 16px;
					color: #ffffff;
					line-height: 22px;
					// letter-spacing: 6px;
					text-shadow: 0px 5px 0px rgba(0, 0, 0, 0.5);
					text-align: left;
					font-style: normal;
					background: linear-gradient(180deg, #ffffff 0%, #28cfff 100%);
					background-clip: text;
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
					margin-right: 17px;
					// margin-top: 3px;
				}
				.dropName2 {
					float: left;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 16px;
					color: #ffffff;
					line-height: 22px;
					text-shadow: 0px 2px 3px rgba(0, 48, 72, 0.5);
					text-align: left;
					font-style: normal;
				}
			}
			.dropImg2 {
				margin: 17px 0 0 17px;
				float: left;
			}
		}
	}
}
.home_icon {
	position: absolute;
	top: 49px;
	right: 20px;
	display: flex;
	align-items: center;
	// margin-left: 24px;
	& span:first-of-type {
		font-size: 18px;
		font-family: PangMenZhengDao;
		color: #7bf3fb;
		line-height: 21px;
		text-shadow: 0px 2px 0px rgba(0, 32, 56, 0.5);
	}
	& > div {
		margin-left: 11px;
		width: 1px;
		height: 20px;
		border: 1px solid;
		border-image: linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(123, 243, 251, 1)) 1 1;
	}
	& span:last-of-type {
		margin-left: 11px;
		font-size: 22px;
		font-family: PangMenZhengDao;
		color: #ffffff;
		line-height: 25px;
		text-shadow: 0px 2px 0px rgba(0, 32, 56, 0.5);
	}
	img {
		margin-left: 14px;
		width: 44px;
		height: 44px;
		cursor: pointer;
	}
	.icons {
		position: absolute;
		width: 284px;
		height: 193px;
		background: url(~@/assets/head/icon_bg.png) no-repeat;
		right: 0;
		top: 50px;
		margin-left: -11px;
		border: none;
		padding: 26px 12px 16px;
		ul {
			display: flex;
			flex-wrap: wrap;
			gap: 8px;
			li {
				width: 126px;
				height: 45px;
				background: url(~@/assets/head/icon_normal.png) no-repeat;
				font-size: 18px;
				font-family: PangMenZhengDao;
				color: #cdf6ff;
				line-height: 45px;
				letter-spacing: 1px;
				cursor: pointer;
				&:hover {
					background: url(~@/assets/head/icon_active.png) no-repeat;
					color: #fff6cd;
				}
			}
		}
	}
}
</style>
