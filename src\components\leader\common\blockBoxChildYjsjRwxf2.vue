<template>
	<div class="child-block" :style="{ height: blockHeight + 'px' }">
		<div class="content">
			<slot></slot>
			<div class="cont">
				<div class="contList">
					<div class="contLeft"><span>*</span>受灾类型：</div>
					<div class="contRight">{{ dataObj.affectedType }}</div>
				</div>
				<div class="contList">
					<div class="contLeft"><span>*</span>受灾作物：</div>
					<div class="contRight">{{ dataObj.affectedCrop }}</div>
				</div>
				<div class="contList">
					<div class="contLeft"><span>*</span>受灾区域：</div>
					<div class="contRight">{{ dataObj.areaname }}</div>
				</div>
				<div class="contList">
					<div class="contLeft"><span>*</span>受灾面积：</div>
					<div class="contRight">{{ dataObj.affectedArea }}</div>
				</div>
				<div class="contList">
					<div class="contLeft"><span>*</span>所需农机：</div>
					<div class="contRight" :title="dataObj.itemsNew">{{ dataObj.itemsNew }}</div>
				</div>
				<div class="contList">
					<div class="contLeft"><span>*</span>请求时间：</div>
					<div class="contRight">{{ dataObj.requestDate }}</div>
				</div>
				<div class="contList">
					<div class="contLeft"><span>*</span>请求详情：</div>
				</div>
				<div class="contList1">
					<div class="contContent">{{ dataObj.comments }}</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		contentNumberUnitOne: {
			type: String,
			default: '万',
		},

		contentDataList: {
			type: Array,
			default: () => [],
		},

		blockHeight: {
			type: Number,
			default: 350,
		},
		dataObj: {
			type: Object,
			default: () => {},
		},
		
	},
	data() {
		return {
			currentIndex: 0,
			isActive: true,
			isOpenOtherData: false,
			otherDataList: [],
			currentTabIndex: 0,
			activeNames: ['1'],
		}
	},
	computed: {
		otherArgmachNumber() {
			let otherArgmachNumber = 0

			this.otherDataList.forEach((item) => {
				otherArgmachNumber += Number(item.number)
			})
			return otherArgmachNumber
		},
	},
	methods: {
	},
}
</script>

<style lang="less" scoped>
.child-block {
	width: 455px;
	box-sizing: border-box;
	z-index: 101;
	position: relative;
	padding: 16px 16px 0 22px;

	.content {
		position: relative;
		width: 100%;
		height: 100%;
		// height: calc(100% - 74px);
		display: flex;
		justify-content: flex-start;
		flex-wrap: wrap;
		align-items: center;
		overflow: auto;

		&::-webkit-scrollbar {
			width: 4px; /* 滚动条宽度 */
		}

		//轨道
		&::-webkit-scrollbar-track {
			background: #04244e; /* 滚动条轨道背景 */
			border-radius: 10px;
			padding: 5px 0;
		}

		//滑块
		&::-webkit-scrollbar-thumb {
			background: #82aed0; /* 滑块背景颜色 */
			border-radius: 10px;
			height: 15px;
		}

		//悬停滑块颜色
		&::-webkit-scrollbar-thumb:hover {
			background: #ff823b; /* 鼠标悬停时滑块颜色 */
		}

		//箭头
		&::-webkit-scrollbar-button {
			display: none; /* 隐藏滚动条的箭头按钮 */
		}

		&.no-active-content {
			height: 0;
			display: none;
		}

		.cont {
			width: 100%;
			height: 100%;
			padding-top: 4px;
			position: relative;

			.contList {
					width: 100%;
					height: 56px;
					line-height: 56px;
					padding: 0 27px 0 23px;

					&:nth-child(odd) {
						background: rgba(0, 101, 183, 0.15);
					}

					.contLeft {
						height: 100%;
						float: left;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 14px;
						color: #abdcf4;
						text-align: left;
						font-style: normal;

						span {
							height: 100%;
							font-family: PingFangSC, PingFang SC;
							font-weight: 500;
							font-size: 16px;
							color: #8bc7ff;
							text-align: left;
							font-style: normal;
							margin-right: 11px;
						}
					}

					.contRight {
						width: 270px;
						height: 56px;
						float: right;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 14px;
						color: #ffffff;
						text-align: right;
						font-style: normal;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}

					.contContent {
						padding: 12px 0;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 14px;
						color: #ffffff;
						line-height: 20px;
						text-align: right;
						font-style: normal;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 14px;
						color: #ffffff;
						line-height: 20px;
						text-align: right;
						font-style: normal;
					}
				}

				.contList1 {
					width: 100%;
					height: 96px;
					padding: 0 16px;

					display: flex;
					justify-content: flex-start;
					flex-direction: column;
					align-items: flex-start;
					background: rgba(0, 101, 183, 0.15);

					span {
						font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
						font-weight: normal;
						font-size: 14px;
						color: #d0deee;
						text-align: left;
						font-style: normal;
						text-transform: none;
						margin-bottom: 8px;
					}

					.contContent {
						width: 100%;
						font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
						font-weight: normal;
						font-size: 14px;
						color: #ffffff;
						line-height: 20px;
						padding-left: 8px;

						text-align: left;
						font-style: normal;
						text-transform: none;
					}
				}
		}
	}
}
</style>
