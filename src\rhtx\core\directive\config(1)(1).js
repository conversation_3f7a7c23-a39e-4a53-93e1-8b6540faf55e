//可同时有多个映射关系
// http映射   {host:目标origin}
const API_URL_MAP = {
	//本地
	//localhost说明：本地前端浏览器访问的是localhost就填写localhost，如果本地是127.0.0.1那就是127.0.0.1
	//8080说明：本地浏览器访问的端口号
  // 'localhost:8080': 'https://*************:8000',
	//线上
	//************说明：此ip为在浏览器访问线上前端页面的ip
	//8080说明：此port为在浏览器访问线上前端页面的port
  // '************:8080': 'https://*************:8000'
};

// ws接口映射   {host:目标ws地址}
const WS_URL_MAP = {
	//本地
	//localhost说明：本地前端浏览器访问的是localhost就填写localhost，如果本地是127.0.0.1那就是127.0.0.1
	//8080说明：本地浏览器访问的端口号
  // 'localhost:8080': 'wss://*************:8000/wss',
  //线上
	//************说明：此ip为在浏览器访问线上前端页面的ip
	//8080说明：此port为在浏览器访问线上前端页面的port
  // '************:8080': 'wss://*************:8000/wss'
};

export { API_URL_MAP, WS_URL_MAP };
