<template>
  <div v-if="show">
    <div class="ai_waring">
      <div class="title">
        <div class="tabs">
          <div
            class="tab"
            :class="{ active: tabActive === i }"
            v-for="(it, i) of tabs"
            :key="i"
            @click="changeTab(i)"
          >
            <span>{{ it }}</span>
          </div>
        </div>
      </div>
      <div class="close_btn" @click="closeEmitai"></div>
      <div class="content">
        <div class="check_types">
          <div class="jjcd">
            <span>紧急程度：</span>
            <Select v-model="proityValue">
              <Option v-for="item in urgentOptions" :value="item.value" :key="item.value">{{
                item.label
              }}</Option>
            </Select>
          </div>
          <div class="jjcd">
            <span>事件类型：</span>
            <Select v-model="proityValue">
              <Option v-for="item in eventTypeOptions" :value="item.value" :key="item.value">{{
                item.label
              }}</Option>
            </Select>
          </div>
          <div class="jjcd">
            <span>是否超时：</span>
            <Select v-model="timeOutValue">
              <Option v-for="item in timeOutOptions" :value="item.value" :key="item.value">{{
                item.label
              }}</Option>
            </Select>
          </div>
          <div class="type_btns">
            <div>查询</div>
            <div>重置</div>
          </div>
        </div>
        <div class="table_box">
          <SwiperTableMap
            :titles="[
              '序号',
              '事件编号',
              '紧急程度',
              '事件内容',
              '事件类型',
              '所属网格',
              '上报时间',
              '超时时间',
              '事件状态',
              '操作'
            ]"
            :widths="['5%', '14%', '7%', '18%', '8%', '8%', '14%', '8%', '10%', '8%']"
            :data="list"
            :contentHeight="'510px'"
            :settled="settled"
            @operate="operate"
          ></SwiperTableMap>
        </div>
        <!-- <div class="fy_page">
          <Page :total="100"></Page>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
import SwiperTableMap from '../table/SwiperTableMap.vue'
import SwiperTableMapFinish from '@/components/shzl/SwiperTableMapFinish.vue'
// api
import { getEventType, getUnDoneEventList, getDoneEventList } from '@/api/hs/hs.api.js'

export default {
  name: 'EventStatistic',
  components: { SwiperTableMap, SwiperTableMapFinish },
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tabActive: 0,
      tabs: ['未办结', '办结'],
      btnsArr: [
        {
          name: '未办结',
          normalBg: require('@/assets/shzl/map/left_n.png'),
          activeBg: require('@/assets/shzl/map/left_a.png')
        },
        {
          name: '已办结',
          normalBg: require('@/assets/shzl/map/right_n.png'),
          activeBg: require('@/assets/shzl/map/right_a.png')
        }
      ],
      list: [],
      urgentOptions: [
        {
          value: '0',
          label: '普通'
        },
        {
          value: '1',
          label: '突发'
        },
        {
          value: '2',
          label: '重要'
        },
        {
          value: '3',
          label: '紧急'
        }
      ],
      eventTypeOptions: [
        {
          value: '0',
          label: '普通'
        }
      ],
      timeOutOptions: [
        {
          value: '0',
          label: '是'
        },
        {
          value: '1',
          label: '否'
        }
      ],
      proityValue: '',
      timeOutValue: '',
      settled: true
    }
  },
  computed: {
    show() {
      return this.value
    }
  },
  watch: {
    value(newVal) {
      if (newVal) {
        // 获取未办结列表
        this.getUnDoneEventListFn()
        // 获取已办结列表
        this.getDoneEventListFn()
      }
    }
  },
  methods: {
    operate(i, id, it) {
      this.$emit('operate', i, id, it, this.tabActive)
    },
    closeEmitai() {
      this.$emit('input', false)
    },
    dealMethod() {
      console.log(1111)
      this.$parent.dealMethod()
    },
    dwMethod() {
      this.$parent.dwMethod()
    },
    changeTab(i) {
      this.tabActive = i
      if (i === 0) {
        this.settled = true
        this.list = this.unDoneList
      } else {
        this.settled = false
        this.list = this.doneList
      }
    },
    async getEventTypeFn() {
      const res = await getEventType()
      this.eventTypeOptions = res.result.children.map(item => {
        return {
          value: item.categoryCode,
          label: item.categoryName
        }
      })
    },
    async getUnDoneEventListFn() {
      const res = await getUnDoneEventList()
      this.unDoneList = res.result.data.map((it, i) => [
        i + 1,
        it.orderNo,
        it.priority,
        it.orderContent,
        it.eventCategoryName,
        it.deptName,
        it.reportTime,
        it.timeoutState === '正常' ? '-' : it.syTime,
        it.state,
        it.id,
        it.claim,
        it.curRecordSoloId
      ])
      this.list = this.unDoneList
    },
    async getDoneEventListFn() {
      const res = await getDoneEventList()
      this.doneList = res.result.data.map((it, i) => [
        i + 1,
        it.orderNo,
        it.priority,
        it.orderContent,
        it.eventCategoryName,
        it.deptName,
        it.reportTime,
        it.timeoutState === '正常' ? '-' : it.syTime,
        it.state,
        it.id
      ])
    }
  },
  mounted() {
    // 获取事件类型
    this.getEventTypeFn()
    // 获取未办结列表
    this.getUnDoneEventListFn()
    // 获取已办结列表
    this.getDoneEventListFn()
  }
}
</script>

<style lang="less" scoped>
.bg-header {
  position: absolute;
  width: 100%;
  height: 1080px;
  background: rgba(2, 14, 50, 0.75);
  z-index: 999999;
  top: 0;
  left: 0;
}
/deep/ .ivu-select {
  width: 168px;
}
/deep/ .ivu-select-selection {
  width: 168px;
  height: 34px;
  background: rgba(0, 74, 143, 0.4)
    linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
}
/deep/ .ivu-select-selection .ivu-select-selected-value {
  font-size: 13px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 34px;
}

/deep/ .ivu-input {
  background: rgba(0, 74, 143, 0.4)
    linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 22px;
}
/deep/ .ivu-select-placeholder {
  font-size: 13px !important;
  line-height: 34px !important;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff !important;
}
/deep/ .ivu-page-item {
  background: transparent;
  color: #fff;
}
/deep/ .ivu-page-item a {
  color: #fff;
}
/deep/ .ivu-page-prev,
/deep/ .ivu-page-next {
  background: transparent;
}
.ai_waring {
  width: 1435px;
  height: 759px;
  background: rgba(9, 19, 34, 0.95);
  box-shadow: 0px 0px 22px 0px rgba(11, 54, 138, 0.35), 0px 2px 8px 0px rgba(0, 0, 0, 0.7),
    inset 0px 0px 8px 0px rgba(17, 146, 214, 0.35);
  border-radius: 11px;
  border: 1px solid #015c8c;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1100;
  .title {
    margin: 0 auto 23px;
    width: 635px;
    height: 76px;
    line-height: 76px;
    background: url(~@/assets/map/dialog/title2.png) no-repeat;
    display: grid;
    place-items: center;
    .tabs {
      display: flex;
      gap: 16px;
      .tab {
        width: 223px;
        height: 46px;
        line-height: 46px;
        background: url('~@/assets/map/dialog/tab_normal.png') no-repeat center / 100% 100%;
        display: grid;
        place-items: center;
        font-size: 28px;
        font-family: PingFangSC, PingFang SC;
        color: #ffffff;
        cursor: pointer;
        &.active {
          background: url('~@/assets/map/dialog/tab_active.png') no-repeat;
          span {
            font-size: 36px;
            font-family: PingFangSC, PingFang SC;
            color: #ffffff;
            line-height: 36px;
            background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
      }
    }
  }
  .close_btn {
    position: absolute;
    width: 23px;
    height: 23px;
    top: 25px;
    right: 40px;
    background: url(~@/assets/map/dialog/btn3.png) no-repeat;
    cursor: pointer;
  }
  .content {
    padding: 0 30px;
    .btns {
      // border: 1px solid red;
      margin: 0 auto;
      display: flex;
      justify-content: center;
      & > div {
        width: 154px;
        height: 53px;
        span {
          font-size: 24px;
          font-family: PingFangSC, PingFang SC;
          color: #ffffff;
          line-height: 53px;
          background: linear-gradient(180deg, #ffffff 0%, #ffeba0 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
    .check_types {
      text-align: left;
      display: flex;
      .jjcd {
        display: flex;
        align-items: center;
        margin-right: 24px;
        span {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
        }
      }
      .report_time {
        margin-left: 26px;
        display: flex;
        align-items: center;
        span {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
      }
      .istime_out {
        margin-left: 26px;
        align-items: center;
        display: flex;
        span {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
      }
      .type_btns {
        margin-left: 27px;
        display: flex;
        align-items: center;
        & div {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 43px;
          background: url(~@/assets/shzl/map/type_btn.png) no-repeat center / 100% 100%;
          background-size: 100% 100%;
          text-align: center;
          cursor: pointer;
          &:first-of-type {
            width: 98px;
            height: 43px;
          }
          &:nth-of-type(2) {
            margin-left: 20px;
            width: 98px;
            height: 43px;
          }
          &:nth-of-type(3) {
            margin-left: 20px;
            width: 98px;
            height: 43px;
          }
        }
      }
    }
    .table_box {
      margin-top: 31px;
      overflow: hidden;
    }
    .fy_page {
      margin-top: 20px;
    }
  }
}
</style>
