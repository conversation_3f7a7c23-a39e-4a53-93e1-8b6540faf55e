<template>
  <div class="preview-dialog">
    <div class="preview-dialog-head">
      <div class="dialog-title">预约会议</div>
      <div class="close-icon" @click="closeDialog"></div>
    </div>
    <div class="dialog-content">
      <el-form :model="previewFormData" label-position="right" label-width="100px">
        <el-form-item label="会议名称：" prop="meetingName">
          <el-input
            :disabled="type === 'detail'"
            v-model="previewFormData.meetingName"
            placeholder="请输入会议主题"
          ></el-input>
        </el-form-item>
        <el-form-item label="开始时间：" prop="beginFullTime">
          <div class="form-time-content">
            <div class="form-time-content-item">
              <el-date-picker
                :disabled="type === 'detail'"
                v-model="beginDate"
                type="date"
                placeholder="选择开始日期"
                :clearable="false"
              >
              </el-date-picker>
              <el-time-picker
                :disabled="type === 'detail'"
                v-model="beginTime"
                format="HH:mm"
                :clearable="false"
              ></el-time-picker>
            </div>
            <div class="form-time-content-item">
              <el-date-picker
                :disabled="type === 'detail'"
                v-model="endDate"
                type="date"
                placeholder="选择结束日期"
                :clearable="false"
              >
              </el-date-picker>
              <el-time-picker
                :disabled="type === 'detail'"
                v-model="endTime"
                format="HH:mm"
                :clearable="false"
              ></el-time-picker>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="时区：" prop="timeZone">
          <el-select
            :disabled="type === 'detail'"
            v-model="previewFormData.timeZone"
            placeholder="请选择时区"
          >
            <el-option
              v-for="(item, index) of timeZoneList"
              :label="item.label"
              :value="item.value"
              :key="'t' + index"
            ></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="参会人：">
          <div class="select-box" @click="openTreeSelect">
            <span v-if="!inviteList.length">请选择参会人</span>
            <div class="select-box-icon" :class="{'expand': treePickerShow}"></div>
          </div>
        </el-form-item>
        <div class="chosen-area">
          <div class="chosen-area-total">共{{ inviteNum }}人</div>
          <div class="chosen-list" v-for="(item, index) of inviteList" :key="'c' + index">
            <span class="chosen-list-name">{{ item.name }}</span>
            <span class="chosen-list-icon" @click="removeChosenItem(item)"></span>
          </div>
        </div> -->
        <el-form-item label="参会人：">
          <div class="custom-select">
            <div class="custom-tree-select" ref="customSelect"  @click.stop="openTreeSelect" style="width: 100%;display: flex;align-items: center;justify-content: space-between;">
              <!-- <div class="select-option" @click="openTreeSelect" ref="custom" tabindex="0">
                <span :style="{ visibility: inviteList.length > 0 ? 'hidden' : 'visible' }">请选择参会人</span>
                <i class="el-icon-arrow-up" v-show="treePickerShow"></i>
                <i class="el-icon-arrow-down" v-show="!treePickerShow"></i>
              </div> -->
              <span v-if="!inviteList.length ">请选择参会人</span>
              <div class="chosen-area" v-if="inviteList.length > 0">
                <div class="chosen-list" v-for="(item, index) of inviteList" :key="'c' + index">
                  <span class="chosen-list-name">{{ item.name }}</span>
                  <span class="chosen-list-icon" @click.stop="removeChosenItem(item)"></span>
                </div>
              </div>
              <i class="el-icon-arrow-up" v-show="treePickerShow"></i>
              <i class="el-icon-arrow-down" v-show="!treePickerShow"></i>
            </div>
            <transition name="picker">
              <div class="tree-picker" v-show="treePickerShow">
                <el-tree
                  class="tree-content"
                  :data="treeData"
                  :props="defaultProps"
                  :default-expand-all="false"
                  :default-expanded-keys="defaultExpandKeys"
                  node-key="id"
                  highlight-current
                  show-checkbox
                  ref="tree"
                  @check="handleTreeCheck"
                >
                  <span class="custom-tree-node" slot-scope="{ node, data }">
                    {{ node.label }}
                  </span>
                </el-tree>
              </div>
            </transition>
          </div>
        </el-form-item>
      </el-form>
      <!-- <transition name="picker">
        <div class="tree-picker" v-show="treePickerShow">
          <el-tree
            class="tree-content"
            :data="treeData"
            :props="defaultProps"
            :default-expand-all="false"
            :default-expanded-keys="defaultExpandKeys"
            node-key="id"
            highlight-current
            show-checkbox
            ref="tree"
            @check="handleTreeCheck"
          >
            <span class="custom-tree-node" slot-scope="{ node, data }">
              {{ node.label }}
            </span>
          </el-tree>
        </div>
      </transition> -->
      <!-- <div class="dialog-content-item">
        <div class="dialog-content-item-title">会议设置</div>
        <div class="dialog-content-item-content">
          <div class="setting-item">

          </div>
        </div>
      </div> -->
      <div class="dialog-content-bottom">
        <div class="preview-btn" @click="previewMeeting" v-if="type === 'add'">预约会议</div>
        <div class="preview-btn" @click="updatePreviewMeeting" v-else-if="type === 'update'">
          修改会议
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getOrgTree } from '@/api/bjnj/zhdd.js'
import { previewMeeting, updatePreviewMeeting, getMeetingData } from '@/api/bjnj/preview.js'
export default {
  name: 'previewDialog',
  props: {
    type: {
      type: String,
      default: 'add'
    },
    roomNum: {
      type: String,
      default: ''
    },
    areaId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      org: '1478649882954985474',
      previewFormData: {
        meetingName: '',
        beginFullTime: '',
        endFullTime: '',
        timeZone: ''
      },
      beginDate: '',
      endDate: '',
      beginTime: '',
      endTime: '',
      inviteList: [],
      timeZoneList: [
        {
          label: '（GMT+8:00）中国标准时间-北京',
          value: 'Asia/Shanghai'
        }
      ],
      selectValue: '',
      treePickerShow: false,
      treeData: [],
      defaultProps: {
        children: 'childList',
        label: 'name'
      },
      defaultExpandKeys: []
    }
  },
  computed: {
    inviteNum() {
      return this.inviteList.length
    },
    username() {
      return this.$store.state.username
    },
    mobile() {
      return this.$store.state.mobile
      // return '17630502601'
    }
  },
  mounted() {
    this.getTreeData()
    document.addEventListener("click", this.handleClickOutside);
    if (this.type !== 'add') {
      this.getPreviewData()
    }
  },
  methods: {
    // 获取组织树
    getTreeData() {
      let params = {
        areaId: this.areaId
      }
      console.log('params===>', params)
      getOrgTree(params).then(res => {
        console.log('组织树数据', res)
        this.treeData = res.data
        this.defaultExpandKeys = [this.treeData[0].id]
        console.log('treedata', this.treeData)
      })
    },
    // 获取数据
    getPreviewData() {
      let param = {
        room: this.roomNum
      }
      getMeetingData(param).then(res => {
        if (res.code === 200) {
          console.log('获取会议详情', res.data)
          this.previewFormData.meetingName = res.data.roomAlias
          this.previewFormData.timeZone = res.data.zone
          this.beginDate = new Date(res.data.appointmentStart)
          this.beginTime = new Date(res.data.appointmentStart)
          this.endDate = new Date(res.data.appointmentEnd)
          this.endTime = new Date(res.data.appointmentEnd)
          if (res.data.inviteParticipants && res.data.inviteParticipants.length > 0) {
            this.inviteList = res.data.inviteParticipants.map(item => {
              return {
                identity: item.identity,
                name: item.participantName
              }
            })
          }
        }
      })
    },
    // 组织树选择
    handleTreeCheck() {
      let arr = this.$refs.tree.getCheckedNodes()
      let allCheckedNodes = []
      arr.forEach(item => {
        if (item.deptFlag == 1) {
          allCheckedNodes.push(item)
        }
      })
      this.inviteList = allCheckedNodes.map(item => {
        return {
          id: item.id,
          name: item.name,
          identity: item.mobile
        }
      })
    },
    // 节点取消选中
    removeChosenItem(item) {
      const _this = this
      function removeItem(ele) {
        let index = _this.inviteList.findIndex(item => {
          return ele.identity === item.identity
        })
        if (index > -1) {
          _this.inviteList.splice(index, 1)
        }
      }
      if (this.type !== 'detail') {
        if (item.id) {
          this.$refs['tree'].setChecked(item.id, false)
          removeItem(item)
        } else {
          removeItem(item)
        }
      }
    },
    // 预约弹窗关闭
    closeDialog() {
      this.$emit('previewDialogClose')
    },
    openTreeSelect() {
      console.log('参会人选择')
      if (this.type !== 'detail') {
        this.treePickerShow = !this.treePickerShow
      }
    },
    // 校验表单内容
    validateFormData() {
      let beginFullTime =
        this.dayjs(this.beginDate).format('YYYY-MM-DD') +
        ' ' +
        this.dayjs(this.beginTime).format('HH:mm:ss')
      let endFullTime =
        this.dayjs(this.endDate).format('YYYY-MM-DD') +
        ' ' +
        this.dayjs(this.endTime).format('HH:mm:ss')
      let result = true
      if (this.previewFormData.meetingName.length <= 0) {
        this.$messageNew.message('error', {
          message: '会议主题不能为空'
        })
        result = false
      } else if (this.previewFormData.meetingName.length > 20) {
        this.$messageNew.message('error', {
          message: '会议主题不能超过20个字符'
        })
        result = false
      } else if (!this.beginDate) {
        this.$messageNew.message('error', {
          message: '请选择开始日期'
        })
        result = false
      } else if (!this.beginTime) {
        this.$messageNew.message('error', {
          message: '请选择开始时间'
        })
        result = false
      } else if (!this.endDate) {
        this.$messageNew.message('error', {
          message: '请选择结束日期'
        })
        result = false
      } else if (!this.endTime) {
        this.$messageNew.message('error', {
          message: '请选择结束时间'
        })
        result = false
      } else if (new Date(endFullTime).getTime() - Date.now() < 0) {
        this.$messageNew.message('error', {
          message: '结束时间不能在当前时间之前'
        })
        result = false
      } else if (new Date(beginFullTime).getTime() - Date.now() < 0) {
        this.$messageNew.message('error', {
          message: '开始时间不能在当前时间之前'
        })
        result = false
      } else if (new Date(endFullTime).getTime() - new Date(beginFullTime).getTime() < 0) {
        this.$messageNew.message('error', {
          message: '结束时间不能在开始时间之前'
        })
        result = false
      } else if (new Date(endFullTime).getTime() - new Date(beginFullTime).getTime() > 10800000) {
        this.$messageNew.message('error', {
          message: '预约会议时长不能超过3个小时'
        })
        result = false
      } else if (this.previewFormData.timeZone == '') {
        this.$messageNew.message('error', {
          message: '时区不能为空'
        })
        result = false
      } else {
        result = true
      }
      this.previewFormData.beginFullTime = beginFullTime
      this.previewFormData.endFullTime = endFullTime
      return result
    },
    // 点击预约会议接口
    previewMeeting() {
      if (this.validateFormData()) {
        let previewParam = {
          owner: this.mobile,
          ownerName: this.username,
          roomAlias: this.previewFormData.meetingName,
          org: this.org,
          inviteParticipants: this.inviteList.map(item => {
            return {
              identity: item.identity,
              participantName: item.name,
              org: '1478649882954985474'
            }
          }),
          appointmentStart: new Date(this.previewFormData.beginFullTime).toISOString(),
          appointmentEnd: new Date(this.previewFormData.endFullTime).toISOString(),
          zone: this.previewFormData.timeZone,
          autoRecord: false,
          mute: false,
          inviteOther: false,
          joinBefore: false,
          waitRoom: false,
          isPassword: false,
          password: ''
        }
        console.log('previewParam', previewParam)
        debugger
        previewMeeting(previewParam)
          .then(res => {
            console.log('预约会议', res)
            if (res.code === 200) {
              this.$messageNew.message('success', {
                message: '预约会议成功'
              })
              this.closeDialog()
            }
          })
          .catch(err => {
            console.log('预约会议失败', err)
          })
      }
    },
    // 修改预约会议接口
    updatePreviewMeeting() {
      if (this.validateFormData()) {
        let previewParam = {
          room: this.roomNum,
          owner: this.mobile,
          ownerName: this.username,
          roomAlias: this.previewFormData.meetingName,
          org: this.org,
          inviteParticipants: this.inviteList.map(item => {
            return {
              identity: item.identity,
              participantName: item.name,
              org: '1478649882954985474'
            }
          }),
          appointmentStart: new Date(this.previewFormData.beginFullTime).toISOString(),
          appointmentEnd: new Date(this.previewFormData.endFullTime).toISOString(),
          zone: this.previewFormData.timeZone,
          autoRecord: false,
          mute: false,
          inviteOther: false,
          joinBefore: false,
          waitRoom: false,
          isPassword: false,
          password: ''
        }
        console.log('previewParam', previewParam)
        updatePreviewMeeting(previewParam)
          .then(res => {
            console.log('修改会议信息', res)
            if (res.code === 200) {
              this.$messageNew.message('success', {
                message: '修改会议信息成功'
              })
              this.closeDialog()
            }
          })
          .catch(err => {
            console.log('修改会议信息失败', err)
          })
      }
    },
        // 点击其他地方关闭下拉框
    handleClickOutside(event) {
      if (!this.$refs.customSelect.contains(event.target)) {
        this.treePickerShow = false;
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.preview-dialog {
  position: absolute;
  z-index: 4500;
  width: 769px;
  min-height: 761px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: url('~@/assets/service/preview_dialog_bg.png') no-repeat center / 100% 100%;

  &-head {
    height: 60px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 48px 0 38px;
    margin-bottom: 30px;
    .dialog-title {
      font-family: YouSheBiaoTiHei;
      font-size: 28px;
      color: #ffffff;
    }
    .close-icon {
      cursor: pointer;
      width: 32px;
      height: 32px;
      background: url('~@/assets/service/close.png') no-repeat center / 100% 100%;
    }
  }
  .dialog-content {
    width: 100%;
    padding: 0 72px;
    position: relative;
    &-item {
      margin-top: 12px;
      width: 100%;
      display: flex;
      flex-wrap: nowrap;
      align-items: flex-start;
      &-title {
        width: 100px;
        text-align: right;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #c2ddfc;
        padding-right: 12px;
      }
      &-content {
        width: calc(100% - 100px);
      }
    }
    &-bottom {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 281px;
      .preview-btn {
        cursor: pointer;
        width: 108px;
        line-height: 31px;
        text-align: center;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #ffffff;
        background: url('~@/assets/service/option_btn_bg.png') no-repeat center / 100% 100%;
      }
    }
    ::v-deep .el-input__inner {
      &::placeholder {
        color: rgba(194, 221, 252, 0.7) !important;
      }
      border-radius: 5px;
      border: 1px solid #cbe5ff;
      background-color: transparent;
      height: 36px;
      line-height: 36px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: rgba(194, 221, 252, 1);
    }
    ::v-deep .el-form {
      .el-form-item {
        margin-bottom: 24px;
        &:nth-child(4) {
          margin-bottom: 0px !important;
        }
        &__label {
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 16px;
          color: #c2ddfc;
        }
        &__content {
          text-align: left;
          .el-select {
            width: 100%;
          }
        }
      }
    }
    .form-time-content {
      width: 100%;
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      &-item {
        flex-shrink: 0;
        &:first-child {
          position: relative;
          margin-right: 44px;
          &::after {
            content: '';
            width: 20px;
            height: 1px;
            position: absolute;
            right: -32px;
            top: 50%;
            transform: translateY(-50%);
            background-color: #d8d8d8;
          }
        }
        ::v-deep .el-date-editor {
          .el-input__inner {
            padding-left: 5px;
          }
          .el-input__prefix {
            display: none;
          }
          .el-input__suffix {
            &-inner {
              padding-right: 2px;
              display: flex;
              align-items: center;
              height: 100%;
            }
            .el-input__icon {
              display: inline-block;
              width: 14px;
              height: 14px;
            }
          }
        }
        ::v-deep .el-date-editor--date {
          width: 133px;
          margin-right: 8px;
          .el-input__suffix {
            .el-input__icon {
              background: url('~@/assets/service/date_icon.png') no-repeat center / 100% 100%;
            }
          }
        }
        ::v-deep .el-date-editor--time {
          width: 100px;
          .el-input__suffix {
            .el-input__icon {
              background: url('~@/assets/service/time_icon.png') no-repeat center / 100% 100%;
            }
          }
        }
      }
    }
    .select-box {
      cursor: pointer;
      width: 100%;
      height: 36px;
      line-height: 36px;
      border-radius: 5px;
      border: 1px solid #cbe5ff;
      padding-left: 15px;
      color: rgba(194, 221, 252, 0.7) !important;
      position: relative;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      &-icon {
        position: absolute;
        width: 14px;
        height: 8px;
        top: 13px;
        right: 4px;
        transform: rotate(0deg);
        transition: transform .3s;
        background: url('~@/assets/service/arrow-icon.png') no-repeat center / 100% 100%;
        &.expand{
          transform: rotate(-180deg);
        }
      }
    }
    .chosen-area {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      padding-top: 6px;
      padding-bottom: 6px;
      // padding-left: 100px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      max-height: 88px;
      overflow-y: auto;
      &::-webkit-scrollbar {
        width: 2px;
        height: 2px;
      }
      &::-webkit-scrollbar-track {
        background: #0a3368;
      }
      &::-webkit-scrollbar-thumb {
        background: #3170ed;
        border-radius: 4px;
      }
      &-total {
        line-height: 26px;
        margin: 6px 20px 6px 0;
      }
      .chosen-list {
        margin: 6px 20px 6px 0;
        background: rgba(46, 92, 148, 0.5);
        border-radius: 13px;
        border: 1px solid rgba(203, 229, 255, 0.5);
        padding: 0 12px;
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        height: 26px;
        &-name {
          flex-shrink: 0;
          white-space: nowrap;
        }
        &-icon {
          flex-shrink: 0;
          margin-left: 8px;
          cursor: pointer;
          width: 14px;
          height: 14px;
          background: url('~@/assets/service/chosen_del_icon.png') no-repeat center / 100% 100%;
        }
      }
    }
    .tree-picker {
      position: absolute;
      max-height: 300px;
      background: rgba(15, 43, 87, 0.9);
      overflow-y: auto;
      width: 539px;
      left: 0;
      // right: 68px;
      // top: 236px;
      &::-webkit-scrollbar {
        width: 4px;
        height: 4px;
      }
      &::-webkit-scrollbar-track {
        background: #719bebd6;
      }
      &::-webkit-scrollbar-thumb {
        background: #3170ed;
        border-radius: 8px;
      }
      .custom-tree-node {
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #ffffff;
        width: 100%;
        padding: 0;
        text-align: left;
        &-label {
          white-space: nowrap;
        }
        &-num {
          font-size: 12px;
          color: #76b2ff;
        }
      }
      ::v-deep {
        .el-tree {
          background: transparent !important;
          .el-tree-node {
            &:focus > .el-tree-node__content {
              background: #1e539a !important;
            }
          }
          .el-tree-node__content {
            position: relative;
            height: 36px;
            &:not(:last-child) {
              margin-bottom: 10px;
            }
            &:hover {
              background: #1e539a !important;
              border-radius: 5px !important;
            }
            .el-tree-node__label {
              font-family: Source Han Sans CN;
              font-weight: 400;
              font-size: 16px;
              color: #ffffff;
            }
            .el-checkbox {
              position: absolute;
              margin: 0;
              font-size: 17px;
              top: 6px;
              right: 12px;
              .el-checkbox__inner {
                border: 1px solid #cbe5ff;
                background: transparent;
              }
              .is-checked {
                .el-checkbox__inner {
                  background-color: #409eff !important;
                }
              }
            }
          }
        }
        .el-tree--highlight-current {
          .el-tree-node.is-current > .el-tree-node__content {
            background-color: #1e539a !important;
          }
        }
      }
    }
    .picker-enter-active,
    .picker-leave-active {
      transition: height 0.2s ease;
    }
    .picker-enter-from,
    .picker-leave-to {
      height: 0px;
    }
  }
}
::v-deep .tree-content{
  .el-tree-node:focus>.el-tree-node__content {
    background-color: #1e539a !important;
  }
}
.custom-select{
  width: 100%;
  position: relative;
  // height: 36px;
  // line-height: 36px;
  border-radius: 5px;
  border: 1px solid #cbe5ff;
  padding-left: 15px;
  color: rgba(194, 221, 252, 0.7) !important;
  font-family: Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  padding: 0 15px;
}
</style>
