<template>
  <div>
    <div id="svgmap" ref="myEchart" class="mapContainer"></div>
  </div>
</template>

<script>
import * as echarts from 'shzl-datav/node_modules/echarts'
// import { svg } from './svg.js'; //重庆
import { svg } from './hsNew.js'
import { getHsZlSq } from '@/api/hs/hs.hszl.js'
export default {
  data() {
    return {
      myChart: null,
      option: null,
      selectName: '',
      areaTotal: '',
      peoTotal: '',
    }
  },
  mounted() {
    let that = this
    this.myChart = echarts.init(document.getElementById('svgmap'))
    this.init()
    console.log('this.myChart', this.myChart)
    this.myChart.on('click', function (event) {
      if (that.selectName == event.name) {
        that.selectName = ''
        that.myChart.clear()
        that.init()
        return
      }
      console.log(event)
      that.selectName = event.name

      that.getHsZlSqFn(that.selectName)

      // that.myChart.setOption(that.option);
      // that.myChart.dispatchAction({
      //   type: 'highlight',
      //   geoIndex: 0,
      //   name: event.name
      // });
    })
  },
  methods: {
    init() {
      var that = this
      echarts.registerMap('organ_diagram', { svg: svg })

      this.option = {
        // backgroundColor: '#040b1c',
        geo: {
          left: 10,
          right: 10,
          map: 'organ_diagram',
          itemStyle: {
            borderWidth: 0,
          },
          emphasis: {
            focus: 'none',
            itemStyle: {
              areaColor: '#03AEB6',
            },
            label: {
              show: false,
              position: [10, 10],
              backgroundColor: {
                image: require('@/assets/img/map/tooltip_bg.png'),
              },
              width: 273,
              height: 114,
              formatter: `{a|{a}}\n {b|区域面积：}{c|${this.areaTotal}}{d|平方公里}\n{b| 人口总数：}{c|${this.peoTotal}}{d|人}`,
              rich: {
                a: {
                  color: '#fff',
                  fontSize: 20,
                  align: 'left',
                  padding: [6, 0, 0, 20],
                },
                b: {
                  color: '#fff',
                  fontSize: 18,
                  fontWeight: 'bold',
                  padding: [14, 0, 0, 26],
                },
                c: {
                  color: '#9fff6b',
                  fontSize: 22,
                  padding: [14, 0, 0, 6],
                },
                d: {
                  color: '#fff',
                  padding: [14, 0, 0, 6],
                  fontSize: 18,
                },
              },
            },
          },
          regions: [
            {
              name: that.selectName,
              itemStyle: {
                areaColor: '#03AEB6',
              },
              label: {
                show: true,
                position: [10, 10],
                backgroundColor: {
                  image: require('@/assets/img/map/tooltip_bg.png'),
                },
                width: 273,
                height: 114,
                // padding: [0, 0, 0, 60],
                formatter: `{a|{a}}\n {b|区域面积：}{c|${this.areaTotal}}{d|平方公里}\n{b| 人口总数：}{c|${this.peoTotal}}{d|人}`,
                // backgroundColor: 'RGBA(12, 33, 75, 0.9)',
                // borderColor: '#8C8D8E',
                // borderWidth: 1,
                // borderRadius: 4,
                rich: {
                  a: {
                    color: '#fff',
                    fontSize: 20,
                    align: 'left',
                    padding: [6, 0, 0, 20],
                  },
                  b: {
                    color: '#fff',
                    fontSize: 18,
                    fontWeight: 'bold',
                    padding: [14, 0, 0, 26],
                  },
                  c: {
                    color: '#9fff6b',
                    fontSize: 22,
                    padding: [14, 0, 0, 6],
                  },
                  d: {
                    color: '#fff',
                    padding: [14, 0, 0, 6],
                    fontSize: 18,
                  },
                },
              },
            },
          ],
        },
      }
      this.myChart.setOption(this.option)
    },
    async getHsZlSqFn(name) {
      let res = await getHsZlSq(name)
      if (res?.code == '200') {
        // console.log(res)
        this.peoTotal = res.result.population
        this.areaTotal = res.result.area
        this.myChart.clear()
        this.init()
      }
    },
  },
}
</script>
<style scoped lang="scss">
.mapContainer {
  position: absolute;
  z-index: 101;
  // display: block;
  width: 669px;
  height: 700px;
  left: 50%;
  top: 8%;
  transform: translateX(-50%);
}
</style>
