<template>
  <div class="dialog" v-if="show">
    <div class="title">
      <span>文件通知详情</span>
    </div>
    <div class="close_btn" @click="$emit('input', false)"></div>
    <div class="content">
      <subtitle-box title="文件通知详情" enTitle="Document Notification Details">
        <div class="wrap">
          <twoColumnList :list="data1" liWidth="100%" labelWidth="120px" @view="yulan" />
        </div>
      </subtitle-box>
      <subtitle-box title="已通知详情" enTitle="Notified Details" height="531px">
        <div class="wrap2">
          <SwiperTable
            :titles="['用户名', '用户电话号', '部门名', '是否接通']"
            :widths="['25%', '25%', '25%', '25%']"
            :data="data2"
            :contentHeight="'458px'"
            :isNeedOperate="false"
          ></SwiperTable>
        </div>
      </subtitle-box>
    </div>
    <iframe
      class="pdf_box"
      src="http://*************:9000/ldzhupload/ldzhupload_20230425113249yagl.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=CzMinIO%2F20230425%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20230425T033249Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=0ec9e14373813063612afcb740280bc822eff0eb4e54cc79959f3901b529da01"
      frameborder="0"
      v-if="isShowPDF"
    ></iframe>
  </div>
</template>

<script>
import { twoColumnList } from '../list'
import subtitleBox from '@/views/leader/components/DialogSubtitleBox.vue'
import SwiperTable from '../table/SwiperTable.vue'
export default {
  name: 'DocumentNotificationDetails',
  components: { twoColumnList, subtitleBox, SwiperTable },
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    show() {
      return this.value
    }
  },
  watch: {
    value(newVal) {
      if (newVal) {
        this.isShowPDF = false
      }
    }
  },
  data() {
    return {
      data1: [
        {
          label: '文件通知标题: ',
          value: '滨江开发区深化提升安全生产专项整治三年行动方案'
        },
        {
          label: '文件通知备注: ',
          value: '滨江开发区深化提升安全生产专项整治三年行动方案'
        },
        {
          label: '文件通知文件: ',
          value: '预览'
        }
      ],
      data2: [
        ['李进', '138********', '经发事业部', '否'],
        ['李晓飞', '138********', '经发事业部', '否']
      ],
      isShowPDF: false
    }
  },
  methods: {
    yulan(it) {
      console.log(it)
      if (it.value !== '预览') {
        return
      }
      if (it.value === '预览') {
        this.isShowPDF = true
      }
    }
  }
}
</script>

<style lang="less" scoped>
.dialog {
  width: 960px;
  height: 854px;
  background: rgba(9, 19, 34, 0.95);
  box-shadow: 0px 0px 22px 0px rgba(11, 54, 138, 0.35), 0px 2px 8px 0px rgba(0, 0, 0, 0.7),
    inset 0px 0px 8px 0px rgba(17, 146, 214, 0.35);
  border-radius: 11px;
  border: 1px solid #015c8c;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1003;
  .title {
    margin: 0 auto 23px;
    width: 635px;
    height: 76px;
    line-height: 76px;
    background: url(~@/assets/map/dialog/title2.png) no-repeat;
    display: grid;
    place-items: center;
    span {
      font-size: 36px;
      font-family: PingFangSC, PingFang SC;
      line-height: 36px;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .close_btn {
    position: absolute;
    width: 23px;
    height: 23px;
    top: 25px;
    right: 40px;
    background: url(~@/assets/map/dialog/btn3.png) no-repeat;
    cursor: pointer;
  }
  .content {
    .wrap {
      height: 100%;
      padding: 20px;
    }
    .wrap2 {
      height: 100%;
      padding: 20px 60px;
    }
  }
  .pdf_box {
    position: absolute;
    left: 50%;
    top: 50%;
    z-index: 10000;
    width: 90%;
    height: 90%;
    transform: translate(-50%, -50%);
  }
}
</style>
