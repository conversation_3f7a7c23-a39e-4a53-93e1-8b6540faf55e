<template>
  <div class="tkBox">
    <div class="tkTitle"><span>经济发展</span></div>
    <img @click="closeEmitai" src="@/assets/hszl/tkGb.png" class="tkGb" />
    <BlockBox
      title="生产总值"
      subtitle="Gross product"
      class="tkList"
      :isListBtns="false"
      :blockHeight="296"
    >
      <div class="wrapper">
        <lineBarEcharts :data="box1Data" />
        <!-- <TrendBarLinechart02 :data="box1BottomData.data" :options="box1BottomData.options" />
        <div class="tlBox tlBox2">
          <div class="tlList1"></div>
          <div class="tlList2">增速</div>
          <div class="tlList3"></div>
          <div class="tlList4">生产总值</div>
        </div> -->
      </div>
    </BlockBox>
    <BlockBox
      title="三次产业占比"
      subtitle="The proportion of three industries"
      class="tkList"
      :isListBtns="false"
      :blockHeight="296"
    >
      <div class="wrapper">
        <PieChart3D
          type="2"
          :data="box4BottomData.data"
          :options="box4BottomData.options"
          :init-option="box4InitOption"
        />
      </div>
    </BlockBox>
    <BlockBox
      title="一般公共预算收入"
      subtitle="General public budget revenue"
      class="tkList"
      :isListBtns="false"
      :blockHeight="296"
    >
      <div class="wrapper">
        <TowerChart
          :options="options"
          :data="data"
          :init-option="{
            yAxis: {
              name: '总收入：万元',
            },
          }"
        />
      </div>
    </BlockBox>
    <BlockBox
      title="重大项目"
      subtitle="Major project"
      class="tkList"
      :isListBtns="false"
      :blockHeight="296"
    >
      <div class="wrapper">
        <div class="zdxm_info">
          <img :src="ldtjDataFirstUrl" alt="" />
          <div>
            <div class="title" :title="ldtjDataFirst.projectName">
              {{ ldtjDataFirst.projectName }}
            </div>
            <div class="content" :title="ldtjDataFirst.synopsis">
              {{ ldtjDataFirst.synopsis }}
            </div>
            <div class="xq_btn" @click="zdxmchange1">详情></div>
          </div>
        </div>
        <swiper-table
          :data="ldtjData"
          :titles="['项目名称', '项目类型', '项目状态', '计划投资', '操作']"
          :widths="['35%', '20%', '15%', '25%', '15%']"
          content-height="220px"
          @change="zdxmchange"
        />
      </div>
    </BlockBox>
    <zdxmPop
      :title="zdxmTitle"
      :zdxmData="zdxmData"
      v-if="isZdxmPopShow"
      @close="isZdxmPopShow = false"
    />
  </div>
</template>

<script>
import BlockBox from '@/components/leader/common/blockBox.vue'
import SwiperTable from '@/components/djyl/SwiperTable.vue'
import lineBarEcharts from '@/components/zhny/lineBarEcharts.vue'
import zdxmPop from '@/components/hszl/zdxmPop.vue'
import { getSczz, getYbggyssr, getSccyzb, getZdxm } from '@/api/hs/hs.hszl.js'

export default {
  name: 'jjfztk',
  components: {
    BlockBox,
    SwiperTable,
    lineBarEcharts,
    zdxmPop,
  },
  data() {
    return {
      options: {
        barColor: '#02C4DD',
        showBarLabel: true,
        yAxis: {
          name: '总收入：万元',
        },
      },
      data: [
        ['produce', ''],
        ['2018', '26513'],
        ['2019', '28416'],
        ['2020', '33370'],
        ['2021', '35336'],
        ['2022', '23400'],
      ],
      ldtjData: [
        ['湖熟街道全域…', '区重大项目', '在建', '7000万元'],
        ['湖熟街道全域…', '区重大项目', '在建', '10000万元'],
        ['湖熟街道全域…', '区重大项目', '在建', '10000万元'],
        ['湖熟街道全域…', '区重大项目', '在建', '10000万元'],
        ['湖熟街道全域…', '区重大项目', '在建', '10000万元'],
        ['湖熟街道全域…', '区重大项目', '在建', '10000万元'],
      ],
      ldtjDataAll: [],
      box4BottomData: {
        data: [
          ['product', '事件总数'],
          ['第一产业', 861],
          ['第二产业', 1026],
          ['第三产业', 334],
        ],
        options: {
          colors: ['#80CF83', '#CDCF72', '#0280DF', '#13A4C0'],
          legend: {
            show: true,
            top: 10,
            // left: 100,
            // orient: 'vertical',
            itemWidth: 10,
            itemHeight: 10,
            itemRadius: 2,
            itemPadding: 5,
            itemDistance: 12,
            itemMarginTop: 12,
            textColor: 'rgba(255, 255, 255, 0.85)',
            fontWeight: '400',
            fontSize: '12px',
            fontFamily: 'DINPro-Medium',
            letterSpacing: '3px',
            formatter: 123,
          },
          position: ['50%', '40%'],
          bgImg: {
            width: '60%',
            height: '60%',
            top: '42%',
            left: '50%',
          },
          unit: '亿元',
          title: {
            fontSize: '16px',
            top: 80,
          },
          subtitle: {
            fontSize: '14px',
            top: 100,
          },
        },
      },
      box1BottomData: {
        data: [
          ['product', '总量：亿元', '', '增速'],
          ['2018', 55.08, 100, 6.8],
          ['2019', 53.81, 100, 12.3],
          ['2020', 46.02, 100, 3.6],
          ['2021', 44.62, 100, 8.63],
          ['2022', 40.89, 100, 13.05],
        ],
        options: {
          legend: {
            orient: 'vertical',
            show: false,
          },
        },
      },
      box1Data: [],
      isZdxmPopShow: false,
      zdxmTitle: '',
      zdxmData: {
        tbAppendixList: [],
        synopsis: '',
      },
      ldtjDataFirstUrl: '',
      ldtjDataFirst: {
        projectName: '',
        synopsis: '',
      },
    }
  },
  computed: {
    box4InitOption() {
      return {
          legend: {
            left: 0,
            // itemWidth: 8,
            labelFormatter: function () {
              return (
                '<span style="color:#fff;fontSize:14px">' +
                this.name +
                ' ' +
                '<span style="color:#fff;fontSize:14px">' +
                this.y +
                '亿元' +
                '</span>' +
                ' ' +
                '<span style="color:#fff;fontSize:14px">' +
                Math.round(this.percentage) +
                '%' +
                '</span>'
              )
            },
          },
      }
    },
  },
  created() {
    this.getSczzFn()
    this.getYbggyssrFn()
    this.getSccyzbFn()
    this.getZdxmFn()
  },
  methods: {
    closeEmitai() {
      this.$emit('closeEmitai')
    },
    async getSczzFn() {
      let res = await getSczz()
      if (res?.code == '200') {
        this.box1Data = []
        this.box1Data = [
          ['product', '增速', '生产总值'],
          ...res.result.map((item) => [item.value1, Number(item.value4), Number(item.value2)]),
        ]
      }
    },
    async getYbggyssrFn() {
      let res = await getYbggyssr()
      if (res?.code == '200') {
        this.data = []
        this.data = [
          ['product', ''],
          ...res.result.map((item) => [item.name, Number(item.sysvalue)]),
        ]
      }
    },
    async getSccyzbFn() {
      let res = await getSccyzb()
      if (res?.code == '200') {
        this.box4BottomData.data = []
        this.box4BottomData.data = [
          ['product', '事件总数'],
          ...res.result.map((item) => [item.name, Number(item.sysvalue)]),
        ]
      }
    },
    async getZdxmFn() {
      let res = await getZdxm()
      if (res?.code == '200') {
        this.ldtjData = []
        this.ldtjDataAll = res.result.data
        this.ldtjDataFirst = res.result.data[0]
        this.ldtjDataFirstUrl = res.result.data[0].tbAppendixList[0].url
        this.ldtjData = [
          ...res.result.data.map((item) => [
            item.projectName,
            item.type,
            item.status,
            item.invest + '亿元',
            '详情',
          ]),
        ]
      }
    },
    zdxmchange(val, i) {
      // console.log(val, i)
      this.isZdxmPopShow = true
      this.zdxmData = this.ldtjDataAll[i]
      this.zdxmTitle = val[0]
    },
    zdxmchange1() {
      this.isZdxmPopShow = true
      this.zdxmData = this.ldtjDataAll[0]
      this.zdxmTitle = this.ldtjDataAll[0].projectName
    },
  },
}
</script>

<style lang="less" scoped>
.tkBox {
  width: 1135px;
  height: 843px;
  background: url(~@/assets/hszl/tkBg.png) no-repeat center / 100% 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10000;
  padding: 0 43px;
  .tkTitle {
    width: 634px;
    height: 74px;
    background: url(~@/assets/hszl/tkTitle.png) no-repeat center / 100% 100%;
    margin: 0 auto;
    margin-top: 22px;
    line-height: 74px;
    span {
      font-size: 36px;
      font-family: PingFangSC, PingFang SC;
      color: #ffffff;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .tkGb {
    position: absolute;
    top: 38px;
    right: 43px;
  }
  .tkList {
    float: left;
    margin: 0 32px;
    margin-top: 26px;
    .zdxm_info {
      height: 100px;
      display: flex;
      margin: 10px 0;
      position: relative;
      img {
        width: 100px;
        height: 100px;
        flex-shrink: 0;
        margin-right: 10px;
      }
      .title {
        font-size: 16px;
        color: #fff;
        text-align: left;
        font-weight: 600;
      }
      .content {
        width: 304px;
        height: 66px;
        font-size: 14px;
        color: #eee;
        text-align: left;
        white-space: pre-line; /*允许换行*/
        overflow: hidden;
        text-overflow: ellipsis; /*省略号*/
        display: -webkit-box;
        -webkit-box-orient: vertical; /*行向垂直排列*/
        -webkit-line-clamp: 3; /*限制2行*/
      }
      .xq_btn {
        position: absolute;
        bottom: 0;
        right: 0;
        color: #37c1ff;
        font-size: 16px;
        cursor: pointer;
      }
    }
    .wrapper {
      width: 460px;
      height: 263px;
      position: relative;
      .tlBox {
        width: 100%;
        position: absolute;
        top: 10px;
        left: 50%;
        transform: translateX(-50%);
        .tlItem {
          float: left;
          margin-left: 30px;
        }
        .tlList1 {
          float: left;
          width: 10px;
          height: 10px;
          border: 1px solid;
          border-image: linear-gradient(180deg, rgba(153, 228, 250, 1), rgba(96, 196, 243, 1)) 1 1;
          margin: 5px 5px;
        }
        .tlList2 {
          float: left;
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 20px;
        }
        .tlList3 {
          float: left;
          width: 10px;
          height: 10px;
          background: linear-gradient(180deg, #01ffc5 0%, rgba(0, 237, 185, 0) 100%);
          border: 1px solid;
          border-image: linear-gradient(180deg, rgba(153, 250, 241, 1), rgba(96, 243, 229, 0)) 1 1;
          margin: 5px 5px;
        }
        .tlList4 {
          float: left;
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 20px;
        }
        .tlList5 {
          float: left;
          width: 10px;
          height: 10px;
          background: #80cf83;
          margin: 5px 5px;
        }
        .tlList6 {
          float: left;
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 20px;
          .span1 {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #1effff;
            line-height: 20px;
            margin-left: 10px;
          }
          .span2 {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #d7bf5c;
            line-height: 20px;
            margin-left: 7px;
          }
        }
        .tlList7 {
          float: left;
          width: 10px;
          height: 10px;
          background: #cdcf72;
          margin: 5px 5px;
        }
        .tlList8 {
          float: left;
          width: 10px;
          height: 10px;
          background: #0280df;
          margin: 5px 5px;
        }
      }
      .tlBox1 {
        top: 220px;
      }
      .tlBox2 {
        width: auto;
      }
    }
  }
}
</style>
