import http from '@/utils/leader/request'
import { baseUrl, baseUrl21, baseUrls, baseUrl2, baseTokenUrl, bjnjUrl } from '@/utils/leader/const'

// 获取token
export const getToken = () => {
	return http.get(baseUrl + '/api/zl/getToken')
}
// /oauth/token?grant_type=client_credentials&client_id=531f73e1-b172-47c6-b497-e265b581c2c8&client_secret=508f656bc3867a36e566f519b132de1e

// export const getToken1 = () => {
//   return http.post(baseTokenUrl + '/oauth/token?grant_type=client_credentials&client_id=531f73e1-b172-47c6-b497-e265b581c2c8&client_secret=508f656bc3867a36e566f519b132de1e',
//     {
//       "grant_type": 'client_credentials',
//       "client_id": '8a667de3-d46a-4a9a-9bf2-a6ed0841b5ad',
//       "client_secret": 'c53faaa03329a58be19b6123b6685360'
//     })
// }
export const getToken1 = () => {
	return http.post(
		baseTokenUrl +
			'/oauth/token?grant_type=client_credentials&client_id=c69925f7-4176-4a97-ac22-f48d4ed43e48&client_secret=0e432d7b4b5bfc088cccc0b2559b3177',
		{
			grant_type: 'client_credentials',
			client_id: 'c69925f7-4176-4a97-ac22-f48d4ed43e48',
			client_secret: '0e432d7b4b5bfc088cccc0b2559b3177',
		},
	)
}

// 查询农机总数量
export const getNjCount2 = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/agmach/total/count', params)
}

// 查询农机总数量
export const getNjCount = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/portal/data/statistics', params)
}

// 查询农机总数量
export const getNjCount3 = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/loc/lat/agmach/count/statistic', params)
}

// 查询当日农机活跃数量
export const getNjTodayCount = (data) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/agmach/today/online/count', data)
}

// 查询当前在线农机数量统计信息
export const getNjOnlineCount = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/active/count', params)
}

// 查询昨日在线农机数量统计信息
export const getYesterdayNjOnlineCount = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/active/count', params)
}

// N 日农机活跃数量
export const getNjCurrent7Count = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/active/count/scope', params)
}

// 昨日作业农机数量
export const getYesterdayNjCurrent7Count = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v2/daas/api/agmach/v2/work/count', params)
}

// 查询当前各车企农机数量统计信息
export const getVehiclesCount1 = () => {
	return http.get(baseUrl + '/daas/api/agmach/v1/enterprise/agmach/statistic')
}

// 各类型农机每日活跃数量统计
export const getNjVehicles = () => {
	return http.get(baseUrl + '/daas/api/agmach/v1/type/proportion/day')
}

// 农机类型
export const getAgmachTypeList = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/type/list', params)
}

// 指定区域下每日农机类型统计
export const getNjAreaDayType2 = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/area/type/proportion/day', params)
}

// 每日各区域农机分布数量
export const getAreaDistribute2 = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/area/distribution/count/day', params)
}

// 每日各区域农机分布数量
export const getAreaDistribute = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/distribution/latest', params)
}

// 指定类型下农机范围时间段活跃农机数量变化趋势
export const getNjTrend = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/type/agmach/num/trend', params)
}

// 不同吞吐量每日农机作业面积 top5
export const getRankTop5 = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/throughput/workarea/rank', params)
}

// 指定(某类型/某地区/某农机)范围时间内每日全量工况信息
export const getStatistics = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/statistics', params)
}

// 指定(某类型/某地区/某农机)范围时间内每日全量工况信息
export const getNjAreaDayType3 = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/type/proportion/count', params)
}

// 分页查询农机实时位置列表
export const getNjLocation2 = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/loc/page/list', params, { Host: 'agrimachcloud.com' })
}

// 分页查询农机实时位置列表
export const getNjLocation1 = (params) => {
	return http.get(baseUrl + '/daas/apiagmach/v1/daas/apiagmach/v1/oc/lon/lat/page/list', params, { Host: 'agrimachcloud.com' })
}

// 热力
export const getNjLocationrl = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/loc/lon/lat/page/list', params, { Host: 'agrimachcloud.com' })
}

// 聚合
export const getNjLocation = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/loc/lon/lat/last/page/list', params, { Host: 'agrimachcloud.com' })
}

// 聚合
export const getNjLocations = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/loc/lat/distribution/last/list', params, { Host: 'agrimachcloud.com' })
}

// 烘干中心地图打点
export const getDryingMap = (params) => {
	return http.get(bjnjUrl + '/api/tDryingCenters/getMap', params)
}

// 应急救灾中心地图打点
export const getEmergencyMap = (params) => {
	return http.get(bjnjUrl + '/api/tEmergencyCenters/getMap', params)
}

// 获取站内资源的中心成员
export const queryPageByTeamId = (data) => {
	return http.post(bjnjUrl + '/api/tEmergencyPeoples/queryPageByTeamId', data)
}

// 获取站内资源的农机资源
export const queryPageById = (data) => {
	return http.post(bjnjUrl + '/api/tAgmachInfos/queryPageById', data)
}

// 应急救灾中心地图打点
export const getVehiclesCount = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/enterprise/agmach/count/statistic', params)
}

// 不同吞吐量每日农机作业面积 top5
export const throughputRank = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/throughput/workarea/rank', params)
}

//指定日期范围内农机工作数量最多区域-作业重点
export const importantArea = (params) => {
	// return http.get(baseUrl + '/daas/api/agmach/v1/work/important/area', params)
	return http.get(bjnjUrl + '/api/screen/work/important/area', params)
}
//每小时各区域农机分布数量-作业热点
export const distributionhour = (params) => {
	// return http.get(baseUrl + '/daas/api/agmach/v1/area/distribution/count/hour', params)
	return http.get(bjnjUrl + '/api/screen/area/distribution/count/hour', params)
}

// 获取某行政区划的农机拥有量数据
export const api_getCurrentHaveNj = (params) => {
	return http.get(bjnjUrl + '', params)
}

// 获取某机械化发展情况数据
export const api_getJxhfzqkByYear = (params) => {
	return http.get(bjnjUrl + '/api/report/queryScreenValueByType', params)
}

//根据code获取各行政区划的数据
/**
 *
 * @param {*} params
 * type: [1]返回的行政区划编码；[2]返回的是行政区划名称
 * year：当前年份
 * areaId：当前行政区划Id
 * code：机械化发展情况的code
 * @returns
 */
export const api_getAreaListByCondition = (params) => {
	return http.get(bjnjUrl + '/api/report/queryAreaListByType', params)
}

//根据年份范围获取底部农机数据
export const api_getArgmachTrendData = (params) => {
	return http.get(bjnjUrl + '/api/report/queryAreaListByType', params)
}

export const api_getTopDescripteDataList = (params) => {
	return http.get(bjnjUrl + '/api/report/queryNowAndPastDataByCondition', params)
}

//获取某行政区划的机械化率数据
export const api_getMechRate = (params) => {
	return http.get(bjnjUrl + '', params)
}

//获取农机服务产业数据
export const api_getMechServiceIndustryData = (params) => {
	return http.get(bjnjUrl + '', params)
}

//获取各行政区划的子行政区划机械总动力数据
export const api_getMechTotalPowerData = (params) => {
	// return http.get(baseUrl + '/daas/api/agmach/v1/area/distribution/count/hour', params)
	return http.get(bjnjUrl + '/api/screen/area/distribution/count/hour', params)
}

//获取各行政区划的子行政区划耕种收综合机械化率数据
export const api_getMechRateData = (params) => {
	// return http.get(baseUrl + '/daas/api/agmach/v1/area/distribution/count/hour', params)
	return http.get(bjnjUrl + '/api/screen/area/distribution/count/hour', params)
}

//获取各行政区划的子行政区划机械作业面积
export const api_getMechWorkFaceData = (params) => {
	// return http.get(baseUrl + '/daas/api/agmach/v1/area/distribution/count/hour', params)
	return http.get(bjnjUrl + '/api/screen/area/distribution/count/hour', params)
}

// 指定(某类型/某地区/某农机)范围时间内每日全量工况信息
export const getNjAreaDayTypes = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/term/info/page/list', params)
}

// 指定(某类型/某地区/某农机)范围时间内每日全量工况信息
export const getNjAreaDayTypes2 = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/loc/periphery/last/list', params)
}

//查询农机历史轨迹信息
export const locHistory = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/loc/history', params)
}

//查询农机历史轨迹信息
export const locHistory2 = (params) => {
	// return http.get(baseUrl + '/daas/api/agmach/v1/global/terminal/user', params)
	return http.get(bjnjUrl + '/api/screen/getAgmachUserInfoNew', params)
}

//查询农机历史轨迹信息
export const getNjAreaDayType = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/global/agmachtype/count', params)
}

//查询农机数量3.4.12-大屏
export const agmachCount = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/loc/lat/agmach/count/statistic', params)
}

//查询车企农机数量统计信息3.4.31-大屏
export const cqCount = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/global/enterprise/agmach/count/statistic', params)
}

//查询各农机类型数量3.4.29-大屏
export const agmachtypeCount = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/global/agmachtype/count', params)
}

//查询各农机类型位置列表信息3.4.30
export const agmachtypeLocList = (params) => {
	// return http.get(baseUrl + '/daas/api/agmach/v2/loc/status/identification', params)
	return http.get(baseUrl21 + '/daas/api/agmach/v2/loc/status/identification', params)
}

//查询各农机基础信息agmachId为终端元数据agmachId
export const agmachToTermId = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v2/agmach/to/term/id', params)
}

//查询各农机基础信息agmachId为终端元数据agmachId
export const trend = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/work/trend', params)
}

//查询各农机基础信息agmachId为终端元数据agmachId
export const work = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/area/work', params)
}

//查询各农机基础信息agmachId为终端元数据agmachId
export const identification = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/loc/witih/status/identification', params)
}

//查询各农机基础信息agmachId为终端元数据agmachId
export const count = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/work/count', params)
}

//查询统计某日各小时时间段内农机在线数量(统计某日各小时时间段内农机在线数量)
export const onlineCounts = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/each/hour/online/counts', params)
}

//查询统计某日各小时时间段内农机在线数量(统计某日各小时时间段内农机在线数量)
export const geojson = (params) => {
	return http.get(baseUrls + '/api/v1/flow/run/agmach/v1/area/geojson', params)
}

//查询统计某日各小时时间段内农机在线数量(统计某日各小时时间段内农机在线数量)
export const exit = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/area/exit', params)
}

//查询统计某日各小时时间段内农机在线数量(统计某日各小时时间段内农机在线数量)
export const onlyExit = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/area/only/exit', params)
}

//查询统计某日各小时时间段内农机在线数量(统计某日各小时时间段内农机在线数量)
export const exitCount = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/area/exit/count', params)
}

//查询统计某日各小时时间段内农机在线数量(统计某日各小时时间段内农机在线数量)
export const levelCount = (params) => {
	return http.get(baseUrl + '/daas/api/agmach/v1/area/exit/level/count', params)
}

//查询统计某日各小时时间段内农机在线数量(统计某日各小时时间段内农机在线数量)
export const workTrend = (params) => {
	return http.get(baseUrl2 + '/api-integration-service/api/v1/flow/run/agmach/v1/work/trend', params)
}

//新优化

export function api_getCurrentWorkStatisticData(params) {
	// return http.get(bjnjUrl + '/daas/api/agmach/v2/today/work/count', params)
	return http.get(baseUrl21 + '/daas/api/agmach/v2/today/work/count', params)
}

//获取当日作业农机各类型数量数据
export function api_getCurrentWorkMachine(params) {
	return http.get(baseUrl21 + '/daas/api/agmach/v2/today/work/type/count', params)
}

//获取跨区作业农机数量数据
export function api_getCrossWorkStatisticMachine(params) {
	return http.get(baseUrl21 + '/daas/api/agmach/v2/kuaqu/work/count', params)
}
//获取跨区作业农机各类型数据
export function api_getCrossWorkMachine(params) {
	return http.get(baseUrl21 + '/daas/api/agmach/v2/kuaqu/work/type/count', params)
}
//获取网联农机数量数据
export function api_getCurrentStatisticInternetMachine(params) {
	return http.get(baseUrl21 + '/daas/api/agmach/v2/loc/lat/agmach/count/statistic', params)
}
//获取网联农机各类型数据
export function api_getCurrentInternetMachine(params) {
	// return http.get(baseUrl + '/daas/api/agmach/v1/global/agmachtype/count', params)

	// /daas/api/agmach/v2/daas/api/agmach/v2/global/agmachtype/count
	return http.get(baseUrl + '/daas/api/agmach/v2/daas/api/agmach/v2/global/agmachtype/count', params)
}

//获取当日在线农机各区域数据
export function api_getCurrentWorkArgmachByRegionData(params) {
	return http.get(baseUrl21 + '/daas/api/agmach/v2/today/work/area/count', params)
	// return http.get(baseUrl21 + '/daas/api/agmach/v2/loc/status/identification', params)
}

//获取跨区作业农机各区域数据
export function api_getCrossWorArgmachByRegionData(params) {
	return http.get(baseUrl21 + '/daas/api/agmach/v2/kuaqu/work/area/count', params)
}

//获取网联农机农机各区域数据
export function api_getInternetByRegionData(params) {
	return http.get(baseUrl + '/daas/api/agmach/v1/area/distribution/count', params)

	// return http.get(baseUrl + '/daas/api/agmach/v2/daas/api/agmach/v2/global/agmachtype/count', params)
	// return http.get(baseUrl + '/daas/api/agmach/v2/loc/status/identification', params)
}

//获取N日在线农机统计数据
export function api_getZxnjNData(params) {
	// return http.get(baseUrl + '/daas/api/agmach/v1/active/count', params)
	return http.get(baseUrl + '/daas/api/agmach/v2/daas/api/agmach/v2/online/count', params)
}

//获取N日作业农机统计数据
export function api_getWorkNData(params) {
	// return http.get(baseUrl + '/daas/api/agmach/v1/active/count/scope', params)
	return http.get(baseUrl + '/daas/api/agmach/v2/daas/api/agmach/v2/work/count', params)
}

//获取某行政区划的流入流出数据
export function api_getInOrOutData(params) {
	return http.get(baseUrl + '', params)
}

//获取实时态势某行政区划的统计数据
export function api_getSstsTopDescriptionData(params) {
	return http.get(baseUrl + '', params)
}
//农场监控
export const getNcjkToken = () => {
	return http.post(bjnjUrl + '/api/ezopen/accessToken')
}
export const getNcjkUrl = (data) => {
	return http.post(bjnjUrl + '/api/ezopen/url/' + data)
}

//获取机械化发展情况表单数据
export function api_getJxhfzKeyFormData(data) {
	return http.post(bjnjUrl + '/api/report/queryListByCondition', data)
}

//设置机械化发展情况表单数据
export function api_setJxhfzKeyFormData(data) {
	return http.post(bjnjUrl + '/api/report/saveOrUpdateReportList', data)
}
