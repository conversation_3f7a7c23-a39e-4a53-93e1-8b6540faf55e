<template>
	<div v-if="value" :class="['address-book', { 'address-book-full': isFull }]">
		<div class="dialog-head">
			<div class="dialog-head-left">
				<div class="invited">视频会商</div>
			</div>
			<div class="full-btns">
				<div class="full-btn" v-show="false"></div>
				<div class="full-btn" @click="closeDialog"></div>
			</div>
		</div>
		<div class="dialog-content">
			<div class="dialog-content-left">
				<div class="left-item left-item1">
					<div class="left-item-title">
						<div class="label">快捷入口</div>
						<!-- <div class="option" @click="openHistoryDialog">历史会议</div> -->
					</div>
					<div class="left-item-content">
						<div class="enter-list enter-list1" @click="quickLaunch">
							<div class="enter-list-icon"></div>
							<div class="enter-list-label">快速会议</div>
						</div>
						<div class="enter-list enter-list2" @click="join">
							<div class="enter-list-icon"></div>
							<div class="enter-list-label">加入会议</div>
						</div>
						<!-- <div class="enter-list enter-list3" @click="preview">
              <div class="enter-list-icon"></div>
              <div class="enter-list-label">预约会议</div>
            </div> -->
					</div>
				</div>
				<div class="left-item left-item2">
					<div class="left-item-title">
						<div class="label">通讯录</div>
						<div></div>
					</div>
					<div class="left-item-content">
						<div :class="['search-bar', isInputFocus ? 'search-bar-input' : 'search-bar-cascader']">
							<el-input class="search-input" v-model="filterText" placeholder="请输入名称进行搜索" @focus="inputFocus">
								<i slot="suffix" class="el-input__icon el-icon-search"></i>
							</el-input>
							<el-cascader
								v-model="pickerValue"
								:options="pickerOptions"
								:props="pickerProps"
								clearable
								@visible-change="cascaderFocus"
							>
							</el-cascader>
						</div>
						<el-tree
							class="tree-content"
							:style="{
								height: treeHeight + 'px',
							}"
							:data="treeData"
							:props="defaultProps"
							:default-expand-all="false"
							:default-expanded-keys="defaultExpandKeys"
							node-key="id"
							highlight-current
							show-checkbox
							:filter-node-method="filterNode"
							ref="tree"
							@node-click="handleNodeClick"
							@check="handleTreeCheck"
						>
							<span class="custom-tree-node" slot-scope="{ node, data }">
								<template v-if="data.deptFlag == 2">
									<span class="custom-tree-node-label" :title="node.label">{{ node.label }}</span>
								</template>
								<span class="custom-tree-node-user" v-else>
									<span :class="['custom-tree-node-icon', data.userStatus === 0 ? 'icon-online' : 'icon-offline']"></span>
									<span class="custom-tree-node-label" :title="node.label">{{ node.label }}</span>
									<span :class="['custom-tree-node-status', data.userStatus === 0 ? 'status-online' : 'status-offline']">{{
										data.userStatus === 0 ? '在线' : '离线'
									}}</span>
									<span
										:class="['custom-tree-node-status-pc', data.userStatus === 0 ? 'status-pc-online' : 'status-pc-offline']"
										@click.stop="launchVideoMeetingPC(data)"
									></span>
									<span
										v-if="false"
										:class="['custom-tree-node-status-mobile', data.mobile ? 'status-mobile-online' : 'status-mobile-offline']"
										@click="launchPointToPoint(data.mobile, 'mobile')"
									></span>
								</span>
							</span>
						</el-tree>
						<div class="chosen">
							<div class="chosen-title" @click="expandChosenArea">
								<div class="chosen-title-icon"></div>
								<div class="chosen-title-label">已选{{ chosenList.length }}人</div>
							</div>
							<div ref="chosen" id="chosen-content" class="chosen-content">
								<div class="chosen-item" v-for="(item, index) of chosenList" :key="'c' + index">
									<span class="chosen-item-name">{{ item.name }}</span>
									<span class="chosen-item-icon" @click="removeChosenItem(item)"></span>
								</div>
							</div>
						</div>
						<div class="options">
							<div class="options-btn" @click="reset">重置</div>
							<div class="options-btn" @click="launch">发起会议</div>
						</div>
					</div>
				</div>
			</div>
			<div class="dialog-content-right">
				<div class="date">今天（{{ dayjs().format('MM月DD日') }}）</div>
				<div class="meeting-preview">
					<div class="meeting-preview-list" v-for="(item, index) of previewList" :key="'p' + index">
						<div class="meeting-preview-list-group">
							<div class="meeting-title">
								<div class="title">{{ item.roomName }}</div>
								<div :class="['status', item.status === '进行中' ? 'status-doing' : 'status-done']">{{ item.status }}</div>
							</div>
							<div class="meeting-message">
								<div class="meeting-message-item">
									<div class="meeting-message-item-title">发起人：</div>
									<div class="meeting-message-item-value">{{ item.creator }}</div>
								</div>
								<div class="meeting-message-item">
									<div class="meeting-message-item-title">时间：</div>
									<div class="meeting-message-item-value">{{ item.begingTime }}~{{ item.endTime }}</div>
								</div>
								<div class="meeting-message-item">
									<div class="meeting-message-item-title">会议ID：</div>
									<div class="meeting-message-item-value">{{ item.roomNum }}</div>
								</div>
							</div>
						</div>
						<div class="preview-options">
							<div class="option-btn" @click="enterMeeting(item)">进入会议</div>
							<div class="more-btn" @click.stop="openMoreDialog(item, $event)"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<PreviewMoreOption
			v-if="previewOptionShow"
			:previewMeetingData="curPreviewData"
			:offset="previewOptionOffset"
			:option-list="previewOptionList"
			@endMeeting="endMeeting"
			@checkMeetingDetail="checkMeetingDetail"
			@updateMeetingDetail="updateMeetingDetail"
			@cancelMeeting="cancelMeeting"
		></PreviewMoreOption>
		<PreviewDialog
			:type="previewDialogType"
			:areaId="areaId"
			:roomNum="previewDialogRoomNum"
			v-if="previewDialogShow"
			@previewDialogClose="closePreviewDialog"
		></PreviewDialog>
		<HistoryMeeting v-if="historyMeetingShow" @historyDialogClose="historyMeetingShow = false"></HistoryMeeting>
	</div>
</template>

<script>
import { getOrgTree } from '@/api/bjnj/zhdd.js'
import { debounce } from './tool/index'
import { getPreviewList, delPreviewedMeeting } from '@/api/bjnj/preview.js'
import PreviewMoreOption from './previewMoreOption.vue'
import PreviewDialog from './previewDialog.vue'
import HistoryMeeting from './historyMeeting.vue'

let previewTimeInterval = null
export default {
	name: 'meetingEnterDialog',
	components: {
		PreviewMoreOption,
		PreviewDialog,
		HistoryMeeting,
	},
	props: {
		value: {
			type: Boolean,
			default: false,
		},
		areaId: {
			type: String,
			default: '',
		},
	},
	data() {
		return {
			// 是否全屏
			isFull: false,
			// 搜索焦点
			isInputFocus: true,
			filterText: '',
			// 树
			treeHeight: 407,
			treeData: [],
			treeFlatData: [],
			defaultProps: {
				children: 'childList',
				label: 'name',
			},
			defaultExpandKeys: [],
			pickerValue: '',
			pickerOptions: [],
			pickerProps: {
				checkStrictly: true,
				value: 'name',
				label: 'name',
			},
			isChosenExpanded: false,
			// 已选区
			chosenList: [],
			// 会议预约
			previewList: [],
			previewOptionShow: false,
			previewOptionOffset: {
				left: '0px',
				top: '0px',
				zIndex: 4100,
			},
			curPreviewData: null,
			previewOptionList: [],
			previewDialogShow: false,
			previewDialogType: 'add',
			previewDialogRoomNum: '',
			historyMeetingShow: false,
			tempFirstLevel: false,
		}
	},
	watch: {
		filterText(val) {
			const filterTreeNode = (value) => {
				this.$refs.tree.filter(value)
			}
			debounce(filterTreeNode)(val, 1000)
		},
		pickerValue(val) {
			if (val && val.length > 0) {
				this.tempFirstLevel = false
				this.$refs.tree.filter(val.join('-'))
			} else {
				this.$refs.tree.filter(this.filterText)
			}
		},
		value(newVal) {
			if (newVal) {
				this.getAddressData()
				this.getTreeData()
				this.getPreviewList()
				this.reset()
				this.filterText = ''
				this.isChosenExpanded = false
				if (previewTimeInterval) {
					clearInterval(previewTimeInterval)
					previewTimeInterval = null
				}
				previewTimeInterval = setInterval(() => {
					this.getPreviewList()
				}, 30000)
				this.$nextTick(() => {
					this.setAreaSlide()
				})
			}
		},
	},
	computed: {
		username() {
			return this.$store.state.username
		},
		mobile() {
			return this.$store.state.mobile
			// return '17630502601'
		},
	},
	methods: {
		// 获取预约会议列表
		getPreviewList() {
			let param = {
				identity: this.mobile,
				queryTime: new Date().toISOString(),
			}
			getPreviewList(param).then((res) => {
				console.log('预约会议列表', res)
				if (res.code === 200) {
					if (res.data && res.data.length > 0) {
						this.previewList = res.data.map((item) => {
							let options = []
							if (item.owner === this.mobile) {
								if (item.status === 0) {
									options = ['查看会议详情', '修改会议信息', '取消会议']
								} else {
									options = ['结束会议']
								}
							} else {
								options = ['查看会议详情']
							}
							return {
								roomName: item.roomAlias,
								creator: item.owner,
								status: item.status === 0 ? '待开始' : item.status === 1 ? '进行中' : '已结束',
								begingTime: this.dayjs(item.reserveStart).format('HH:mm'),
								endTime: this.dayjs(item.reserveEnd).format('HH:mm'),
								roomNum: item.roomName,
								options: options,
							}
						})
					} else {
						this.previewList = []
					}
				}
			})
		},
		// 获取省市区三级行政数据
		getAddressData() {
			fetch('./json/address.json')
				.then((res) => {
					return res.json()
				})
				.then((result) => {
					this.pickerOptions = result
				})
		},
		// 输入框获得焦点
		inputFocus() {
			this.isInputFocus = true
		},
		// 级联框获得焦点
		cascaderFocus(e) {
			if (e) {
				this.isInputFocus = false
			}
		},
		// 获取组织树
		getTreeData() {
			let params = {
				areaId: this.areaId,
			}
			getOrgTree(params).then((res) => {
				console.log('组织树数据', res)
				this.treeData = res.data
				this.treeFlat(res.data)
				this.defaultExpandKeys = [this.treeData[0].id]
				console.log('treedata', this.treeData)
			})
		},
		handleNodeClick(data, node) {
			console.log('node1111', node)
		},
		// 树节点过滤函数
		filterNode(value, data) {
			if (!value) return true
			let valueArr = value.split('-')
			if (valueArr.length > 1) {
				if (valueArr.length >= 3) {
					if (data.name.indexOf(valueArr[0]) !== -1) {
						this.tempFirstLevel = true
					}
					return data.name.indexOf(valueArr[valueArr.length - 1]) !== -1 && this.tempFirstLevel
				} else {
					return data.name.indexOf(valueArr[valueArr.length - 1]) !== -1
				}
			} else {
				return data.name.indexOf(valueArr[0]) !== -1
			}
		},
		// 组织树某一项被选择是触发
		handleTreeCheck(checkedNodes) {
			// console.log('当前选择', checkedNodes);
			let arr = this.$refs.tree.getCheckedNodes()
			console.log('当前选择', arr)
			let allCheckedNodes = []
			arr.forEach((item) => {
				if (item.deptFlag == 1) {
					allCheckedNodes.push(item)
				}
			})
			this.chosenList = allCheckedNodes.map((item) => {
				let { area, belong } = this.getUserArea(item.parentId)
				return {
					id: item.id,
					area: area ? area : '暂无',
					address: belong ? belong : '暂无',
					name: item.name,
					mobile: item.mobile,
					isPC: item.userStatus === 0 ? true : false,
					isPhone: item.mobile ? true : false,
				}
			})
			if (this.isChosenExpanded) {
				this.$nextTick(() => {
					this.treeHeight = 407 - this.$refs['chosen'].clientHeight
				})
			}
		},
		treeFlat(data) {
			function treeToArray(tree) {
				let res = []
				for (const item of tree) {
					const { childList, ...i } = item
					if (childList && childList.length > 0) {
						res = res.concat(treeToArray(childList))
					}
					res.push(i)
				}
				return res
			}
			this.treeFlatData = treeToArray(data)
			// console.log('treeFlatData', this.treeFlatData)
		},
		getUserArea(parentId) {
			let area = null,
				belong = null
			if (this.treeFlatData.length > 0) {
				this.treeFlatData.forEach((item) => {
					if (item.id === parentId) {
						area = item?.area
						belong = item?.name
					}
				})
			}
			return {
				area,
				belong,
			}
		},
		openHistoryDialog() {
			this.historyMeetingShow = true
		},
		removeChosenItem(item) {
			const _this = this
			function removeItem(ele) {
				let index = _this.chosenList.findIndex((item) => {
					return ele.id === item.id
				})
				if (index > -1) {
					_this.chosenList.splice(index, 1)
				}
			}
			if (item.id) {
				this.$refs['tree'].setChecked(item.id, false)
				removeItem(item)
			} else {
				removeItem(item)
			}
		},
		reset() {
			console.log('reset', this.chosenList)
			if (this.chosenList.length > 0) {
				this.chosenList.forEach((item) => {
					if (item.id) {
						this.$nextTick(() => {
							this.$refs['tree'].setChecked(item.id, false)
						})
					}
				})
				this.chosenList = []
			}
		},
		launchPointToPoint(mobile, type) {
			if (mobile) {
				this.$emit('launchPointToPoint', {
					mobile,
					type,
				})
			}
		},
		launchVideoMeetingPC(data) {
			if (data.userStatus === 0) {
				this.$emit('launchMeeting', [data])
			} else {
				this.$messageNew.error({
					message: '当前人员处于离线状态',
				})
			}
		},
		// 发起会议
		launch() {
			this.$emit('launchMeeting', this.chosenList)
		},
		quickLaunch() {
			this.$emit('quickLaunch', this.chosenList)
		},
		// 加入会议
		join() {
			this.$emit('openJoinMeetingDialog')
		},
		// 预约会议
		preview() {
			this.previewDialogType = 'add'
			this.previewDialogShow = true
		},
		// 关闭弹窗
		closeDialog() {
			if (previewTimeInterval) {
				clearInterval(previewTimeInterval)
				previewTimeInterval = null
			}
			this.$bus.emit('LaunchMeetingDialogClose')
			this.$emit('input', false)
		},
		// 已选区域伸缩与扩展
		expandChosenArea() {
			this.isChosenExpanded = !this.isChosenExpanded
			if (this.isChosenExpanded) {
				// 已选区处于展开状态
				this.setAreaExpand()
			} else {
				// 已选区处于收缩状态
				this.setAreaSlide()
			}
		},
		setAreaExpand() {
			this.$refs['chosen'].style.height = 'auto'
			console.log('展开状态chosenHeight', this.$refs['chosen'].clientHeight)
			this.$nextTick(() => {
				this.treeHeight = 407 - this.$refs['chosen'].clientHeight
			})
		},
		setAreaSlide() {
			this.$refs['chosen'].style.height = 0
			this.treeHeight = 407
			this.$nextTick(() => {
				this.treeHeight = 407
			})
			console.log('收缩状态chosenHeight', this.$refs['chosen'].clientHeight)
		},
		// 预约会议点击更多按钮
		openMoreDialog(meetingData, e) {
			console.log('鼠标事件', e)
			if (this.previewOptionShow) {
				if (this.curPreviewData.roomNum === meetingData.roomNum) {
					this.previewOptionShow = false
				} else {
					this.previewOptionShow = false
					this.curPreviewData = meetingData
					this.previewOptionOffset.left = e.target.offsetLeft + 'px'
					this.previewOptionOffset.top = e.target.offsetTop + 16 + 'px'
					this.previewOptionList = meetingData.options
					this.previewOptionShow = true
				}
			} else {
				this.curPreviewData = meetingData
				this.previewOptionOffset.left = e.target.offsetLeft + 'px'
				this.previewOptionOffset.top = e.target.offsetTop + 16 + 'px'
				this.previewOptionList = meetingData.options
				this.previewOptionShow = true
			}
		},
		enterMeeting(previewData) {
			this.$emit('openJoinMeetingDialog', previewData)
		},
		closePreviewDialog() {
			this.previewDialogShow = false
			this.getPreviewList()
		},
		endMeeting(e) {
			let param = {
				org: '1478649882954985474',
				identity: this.mobile,
				room: e.roomNum,
			}
			delPreviewedMeeting(param).then((res) => {
				if (res.code === 200) {
					this.$messageNew.message('success', {
						message: '已成功结束会议',
					})
					this.previewOptionShow = false
					this.getPreviewList()
				}
			})
		},
		checkMeetingDetail(e) {
			this.previewDialogType = 'detail'
			this.previewDialogRoomNum = e.roomNum
			this.previewOptionShow = false
			this.previewDialogShow = true
		},
		updateMeetingDetail(e) {
			this.previewDialogType = 'update'
			this.previewDialogRoomNum = e.roomNum
			this.previewOptionShow = false
			this.previewDialogShow = true
		},
		cancelMeeting(e) {
			let param = {
				org: '1478649882954985474',
				identity: this.mobile,
				room: e.roomNum,
			}
			delPreviewedMeeting(param).then((res) => {
				if (res.code === 200) {
					this.$messageNew.message('success', {
						message: '已成功结束会议',
					})
					this.previewOptionShow = false
					this.getPreviewList()
				}
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.address-book {
	width: 1397px;
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	z-index: 4000;
	background: url('~@/assets/service/enter-dialog.png') no-repeat center / 100% 100%;

	.dialog-head {
		height: 55px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 40px 0 38px;
		&-left {
			display: flex;
			align-items: center;
			.invited {
				font-family: YouSheBiaoTiHei;
				font-size: 28px;
				color: #ffffff;
				line-height: 36px;
				margin-right: 40px;
			}
		}
		.full-btns {
			display: flex;
			align-items: center;
			.full-btn {
				&:not(:last-child) {
					margin-right: 10px;
				}
				width: 32px;
				height: 32px;
				cursor: pointer;
				&:nth-child(1) {
					background: url('~@/assets/service/full1.png') no-repeat center / 100% 100%;
				}
				&:nth-child(2) {
					background: url('~@/assets/service/close.png') no-repeat center / 100% 100%;
				}
			}
		}
	}
	.dialog-content {
		width: 100%;
		padding: 10px 8px;
		display: flex;
		align-items: flex-start;
		&-left {
			width: 336px;
			.left-item {
				width: 100%;
				padding: 20px 0 0;
				background: #0a3368;
				border-radius: 5px;
				&1 {
					padding: 20px 20px 0 !important;
					margin-bottom: 10px;
					.left-item-content {
						height: 112px;
						display: flex;
						align-items: center;
						gap: 70px;
						// justify-content: space-between;
						.enter-list {
							display: flex;
							flex-direction: column;
							align-items: center;
							cursor: pointer;
							&-icon {
								width: 48px;
								height: 48px;
								margin-bottom: 10px;
							}
							&-label {
								font-family: Source Han Sans CN;
								font-weight: 500;
								font-size: 14px;
								color: #ffffff;
								white-space: nowrap;
							}
							&1 {
								.enter-list-icon {
									background: url('~@/assets/service/a1.png') no-repeat center / 100% 100%;
								}
							}
							&2 {
								.enter-list-icon {
									background: url('~@/assets/service/a2.png') no-repeat center / 100% 100%;
								}
							}
							&3 {
								.enter-list-icon {
									background: url('~@/assets/service/a3.png') no-repeat center / 100% 100%;
								}
							}
						}
					}
				}
				&2 {
					border-radius: 0 0 0 55px;
					.left-item-title {
						width: calc(100% - 40px);
						margin-left: 20px;
					}
					.left-item-content {
						padding-top: 20px;
						.search-bar {
							width: 100%;
							padding: 0 20px;
							display: flex;
							flex-wrap: nowrap;
							align-items: center;
							justify-content: space-between;
							&-input {
								::v-deep {
									.search-input {
										width: 192px !important;
									}
									.el-cascader {
										width: 96px !important;
									}
								}
							}
							&-cascader {
								::v-deep .search-input {
									width: 96px;
								}
								::v-deep .el-cascader {
									width: 192px;
								}
							}
							.search-input {
								transition: width 0.2s ease;
							}
							::v-deep .el-input {
								flex-shrink: 0;
								.el-input__inner {
									height: 36px;
									line-height: 36px;
									border: 1px solid rgba(203, 229, 255, 0.5) !important;
									background-color: transparent !important;
									font-size: 16px !important;
									color: #ffffff !important;
									font-family: Source Han Sans CN;
									font-weight: 400;
								}
								.el-input__icon {
									line-height: 36px;
									color: #00aaff;
								}
							}
							::v-deep .el-cascader {
								flex-shrink: 0;
								transition: width 0.2s ease;
							}
						}

						.tree-content {
							// transition: height 0.2s ease;
							padding: 20px 10px 0 20px;
							margin-bottom: 20px;
							overflow: auto;
							// 滚动条样式
							&::-webkit-scrollbar {
								width: 2px;
								height: 2px;
							}
							&::-webkit-scrollbar-track {
								background: #0a3368;
							}
							&::-webkit-scrollbar-thumb {
								background: #3170ed;
								border-radius: 4px;
							}
						}
						.custom-tree-node {
							font-family: Source Han Sans CN;
							font-weight: 400;
							font-size: 16px;
							color: #ffffff;
							width: 100%;
							padding-left: 12px;
							text-align: left;
							&-label {
								display: inline-block;
								width: fit-content;
								max-width: 200px;
								overflow: hidden;
								text-overflow: ellipsis;
								white-space: nowrap;
							}
							&-num {
								font-size: 12px;
								color: #76b2ff;
							}
							&-user {
								display: flex;
								justify-content: flex-start;
								align-items: center;
								.custom-tree-node-icon {
									flex-shrink: 0;
									display: inline-block;
									width: 24px;
									height: 24px;
									margin-right: 8px;
								}
								.custom-tree-node-label {
									max-width: 70px;
								}
								.icon-online {
									background: url('~@/assets/service/ava_on.png') no-repeat center / 100% 100%;
								}
								.icon-offline {
									background: url('~@/assets/service/ava_off.png') no-repeat center / 100% 100%;
								}
								.custom-tree-node-status {
									flex-shrink: 0;
									display: inline-block;
									font-family: Source Han Sans CN;
									font-weight: 400;
									font-size: 10px;
									width: 38px;
									height: 16px;
									line-height: 16px;
									text-align: center;
									margin: 0 7px 0 9px;
									&-pc {
										flex-shrink: 0;
										display: inline-block;
										width: 14px;
										height: 14px;
										margin-right: 8px;
									}
									&-mobile {
										flex-shrink: 0;
										display: inline-block;
										width: 14px;
										height: 14px;
									}
								}
								.status-online {
									color: #38f539;
									background: url('~@/assets/service/status_on.png') no-repeat center / 100% 100%;
								}
								.status-offline {
									color: #c6c6c6;
									background: url('~@/assets/service/status_off.png') no-repeat center / 100% 100%;
								}
								.status-pc-online {
									background: url('~@/assets/service/isPC.png') no-repeat center / 100% 100%;
								}
								.status-pc-offline {
									background: url('~@/assets/service/isPC1.png') no-repeat center / 100% 100%;
								}
								.status-mobile-online {
									background: url('~@/assets/service/isPhone.png') no-repeat center / 100% 100%;
								}
								.status-mobile-offline {
									background: url('~@/assets/service/isPhone1.png') no-repeat center / 100% 100%;
								}
							}
						}
						::v-deep {
							.el-tree {
								background: transparent !important;
								.el-tree-node {
									&:focus > .el-tree-node__content {
										background: #1e539a !important;
									}
								}
								.el-tree-node__content {
									position: relative;
									height: 36px;
									.is-leaf {
										opacity: 0;
									}
									&:not(:last-child) {
										margin-bottom: 10px;
									}
									&:hover {
										background: #1e539a !important;
										border-radius: 5px !important;
									}
									.el-tree-node__label {
										font-family: Source Han Sans CN;
										font-weight: 400;
										font-size: 16px;
										color: #ffffff;
									}
									.el-checkbox {
										position: absolute;
										margin: 0;
										font-size: 17px;
										top: 6px;
										right: 12px;
										.el-checkbox__inner {
											border: 1px solid #cbe5ff;
											background: transparent;
										}
										.is-checked {
											.el-checkbox__inner {
												background-color: #409eff !important;
											}
										}
									}
									.el-tree-node__expand-icon {
										width: 17px;
										height: 17px;
										background: url('~@/assets/service/tree_expand.png') no-repeat center / 100% 100%;
										&::before {
											content: '';
										}
									}
									.expanded {
										transform: rotate(0deg) !important;
										background: url('~@/assets/service/tree_slide.png') no-repeat center / 100% 100% !important;
									}
								}
							}
							.el-tree--highlight-current {
								.el-tree-node.is-current > .el-tree-node__content {
									background-color: #1e539a !important;
								}
							}
						}
						.chosen {
							width: 100%;
							padding: 0 8px;
							.chosen-title {
								width: 100%;
								background: rgba(27, 103, 160, 0.2);
								height: 54px;
								display: flex;
								align-items: center;
								padding-left: 14px;
								cursor: pointer;
								&-icon {
									width: 14px;
									height: 14px;
									background: url('~@/assets/service/filter_icon.png') no-repeat center / 100% 100%;
									margin-right: 10px;
								}
								&-label {
									font-family: PangMenZhengDao;
									font-size: 18px;
									color: #ffffff;
								}
							}
							.chosen-content {
								// transition: height 0.2s ease;
								overflow: auto;
								max-height: 407px;
								width: 100%;
								padding-left: 14px;
								background: rgba(27, 103, 160, 0.2);
								&::-webkit-scrollbar {
									width: 2px;
									height: 2px;
								}
								&::-webkit-scrollbar-track {
									background: #0a3368;
								}
								&::-webkit-scrollbar-thumb {
									background: #3170ed;
									border-radius: 4px;
								}
								display: flex;
								flex-wrap: wrap;
								.chosen-item {
									margin: 5px 6px 5px 6px;
									background: rgba(46, 92, 148, 0.5);
									border-radius: 13px;
									border: 1px solid rgba(203, 229, 255, 0.5);
									// border-image: linear-gradient(180deg, rgba(46, 92, 148, 1), rgba(111, 153, 205, 1)) 1 1;
									padding: 0 12px;
									display: flex;
									flex-wrap: nowrap;
									align-items: center;
									height: 26px;
									&-name {
										flex-shrink: 0;
										font-family: SourceHanSansCN, SourceHanSansCN;
										font-weight: 400;
										font-size: 14px;
										color: #ffffff;
										white-space: nowrap;
									}
									&-icon {
										flex-shrink: 0;
										margin-left: 8px;
										cursor: pointer;
										width: 14px;
										height: 14px;
										background: url('~@/assets/service/chosen_del_icon.png') no-repeat center / 100% 100%;
									}
								}
							}
						}
						.options {
							height: 70px;
							width: 100%;
							display: flex;
							align-items: center;
							justify-content: center;
							&-btn {
								&:not(:last-child) {
									margin-right: 12px;
								}
								cursor: pointer;
								width: 108px;
								line-height: 31px;
								text-align: center;
								font-family: Source Han Sans CN;
								font-weight: 400;
								font-size: 16px;
								color: #ffffff;
								background: url('~@/assets/service/option_btn_bg.png') no-repeat center / 100% 100%;
							}
						}
					}
				}
				&-title {
					width: 100%;
					height: 32px;
					display: flex;
					align-items: center;
					justify-content: space-between;
					background: url('~@/assets/service/item-title.png') no-repeat center / 100% 100%;
					padding-left: 27px;
					.label {
						font-family: YouSheBiaoTiHei;
						font-size: 20px;
						color: #ffffff;
						text-align: left;
					}
					.option {
						cursor: pointer;
						font-family: Source Han Sans CN;
						font-weight: 500;
						font-size: 14px;
						color: #87bbfc;
					}
				}
				&-content {
					width: 100%;
				}
			}
		}
		&-right {
			text-align: left;
			width: calc(100% - 336px);
			padding: 34px 38px 34px 40px;
			height: auto;
			max-height: 818px;
			overflow: auto;
			&::-webkit-scrollbar {
				width: 2px;
				height: 2px;
			}
			&::-webkit-scrollbar-track {
				background: #0a3368;
			}
			&::-webkit-scrollbar-thumb {
				background: #3170ed;
				border-radius: 4px;
			}
			.date {
				font-family: Source Han Sans CN;
				font-weight: 500;
				font-size: 18px;
				color: #ffffff;
				margin-bottom: 30px;
			}
			.meeting-preview {
				width: 100%;
				&-list {
					width: 100%;
					height: 120px;
					padding: 0 32px;
					background: linear-gradient(180deg, #124586 0%, rgba(2, 17, 37, 0.29) 100%);
					border-radius: 12px;
					display: flex;
					align-items: center;
					justify-content: space-between;
					flex-wrap: nowrap;
					&:not(:last-child) {
						margin-bottom: 10px;
					}
					&-group {
						width: auto;
						flex-shrink: 0;
						.meeting-title {
							display: flex;
							flex-wrap: nowrap;
							align-items: center;
							margin-bottom: 20px;
							.title {
								white-space: nowrap;
								font-family: Source Han Sans CN;
								font-weight: 400;
								font-size: 20px;
								color: #ffffff;
								margin-right: 11px;
							}
							.status {
								width: 62px;
								line-height: 20px;
								text-align: center;
								font-family: Source Han Sans CN;
								font-weight: 400;
								font-size: 12px;
								&-doing {
									background: url('~@/assets/service/a4.png') no-repeat center / 100% 100%;
									color: #00c4ff;
								}
								&-done {
									background: url('~@/assets/service/a5.png') no-repeat center / 100% 100%;
									color: #ffad00;
								}
							}
						}
						.meeting-message {
							background: rgba(0, 81, 143, 0.8);
							border-radius: 6px;
							display: flex;
							flex-wrap: nowrap;
							align-items: center;
							height: 32px;
							&-item {
								padding: 0 20px;
								display: flex;
								flex-wrap: nowrap;
								align-items: center;
								font-family: Source Han Sans CN;
								font-weight: 400;
								font-size: 16px;
								line-height: 20px;
								&-title {
									white-space: nowrap;
									color: #c2ddfc;
								}
								&-value {
									white-space: nowrap;
									color: #ffffff;
								}
								&:nth-child(1) {
									margin-left: -8px;
								}
								&:nth-child(3) {
									margin-right: -8px;
								}
								&:not(:last-child) {
									border-right: 1px solid;
									border-image: linear-gradient(180deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.1)) 1 1;
								}
							}
						}
					}
					.preview-options {
						width: auto;
						flex-shrink: 0;
						display: flex;
						flex-wrap: nowrap;
						align-items: center;
						.option-btn {
							margin-right: 30px;
							cursor: pointer;
							width: 108px;
							line-height: 31px;
							text-align: center;
							font-family: Source Han Sans CN;
							font-weight: 400;
							font-size: 16px;
							color: #ffffff;
							background: url('~@/assets/service/option_btn_bg.png') no-repeat center / 100% 100%;
						}
						.more-btn {
							width: 20px;
							height: 20px;
							background: url('~@/assets/service/preview_more_btn.png') no-repeat center / 100% 100%;
							cursor: pointer;
						}
					}
				}
			}
		}
	}
}
::v-deep .tree-content {
	.el-tree-node:focus > .el-tree-node__content {
		background-color: #1e539a !important;
	}
}
</style>
