<template>
  <div class="header">
    <div class="header-con">
      <div class="header-left">
        <!-- <img src="@/assets/special/img/header/headerNav.png" alt="" />
        <span>{{ $route.meta.title }}</span> -->
      </div>
      <div class="middle">标题</div>
      <div class="header-right">
        <div class="time">{{ time }}</div>
        <div class="today">
          <div class="week">{{ week }}</div>
          <div class="date">{{ date }}</div>
        </div>
        <div class="home">
          <!-- <img src="@/assets/special/img/header/home.png" alt="" /> -->
        </div>
      </div>
    </div>

    <transition name="fade">
      <div class="top-dropdown" v-show="isShowTitleList">
        <div
          :class="[{ active: $route.path == item.path }, 'dropdown-item']"
          v-for="(item, index) in titleList"
          :key="index"
          @click="routeChange(index, item.path)"
        >
          <!-- <img
            src="@/assets/special/img/header/unselected.png"
            alt=""
            class="item-unselected-img"
          />
          <img src="@/assets/special/img/header/selected.png" alt="" class="item-selected-img" /> -->
          <span>{{ item.name }}</span>
        </div>
      </div>
    </transition>
    <!-- 遮罩层 -->
    <transition name="fade">
      <div
        class="bg-header"
        v-show="isShowTitleList"
        @click.stop="isShowTitleList = !isShowTitleList"
      ></div>
    </transition>
  </div>
</template>

<script>
export default {
  props: {
    //   菜单列表
    titleList: {
      type: Array,
      default: () => {
        return [
          {
            name: '生态环保',
            path: '/special/ecotope'
          },
          {
            name: '突发事件',
            path: '/special/emergency'
          },
          {
            name: '校园安全',
            path: '/special/campusSecurity'
          },
          {
            name: '矛盾纠纷',
            path: '/special/disputes'
          },
          {
            name: '智慧水利',
            path: '/special/smartWater'
          },
          {
            name: '社会保障',
            path: '/special/socialSecurity'
          },
          {
            name: '安全隐患',
            path: '/special/safetyPeril'
          },
          {
            name: '城市文明',
            path: '/special/cityBrightCity'
          },
          {
            name: '五车治理',
            path: '/special/fiveCar'
          }
        ]
      }
    }
  },
  data() {
    return {
      timer: null,
      time: '',
      date: '',
      week: '',
      isShowTitleList: false
    }
  },
  beforeDestroy() {
    this.dataDestroy()
    this.timer&&clearInterval(this.timer)
  },
  mounted() {
    this.dateFormat()
    this.timer = setInterval(() => {
      this.dateFormat()
    }, 1000)
  },
  methods: {
    // 路由跳转
    routeChange(idx, path) {
      if (this.$route.path == path) {
        return
      }
      this.$router.push(path)
    },
    titleShow() {
      this.isShowTitleList = true
    },
    dataDestroy() {
      if (this.timer) {
        clearInterval(this.timer) // 在Vue实例销毁前，清除我们的定时器
      }
    },
    dateShow() {},
    dateFormat() {
      let date = new Date()
      let year = date.getFullYear()
      let month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
      let day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
      let hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
      let minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
      let seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()

      this.date = year + '-' + month + '-' + day + ' '
      this.time = hours + ':' + minutes + ':' + seconds
      this.week = '星期' + '日一二三四五六'.charAt(date.getDay())
    }
  }
}
</script>

<style lang="less" scoped>
.header {
  // background: url(~@/assets/special/img/header/header_middles.png) no-repeat;
  background-size: 100% 100%;
  width: 100%;
  z-index: 1001;
  position: relative;
  padding: 0 24px 0 42px;

  .header-con {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 89px;
    .header-left {
      z-index: 999;
      cursor: pointer;
      img {
        width: 31px;
        height: 31px;
      }
      span {
        font-family: FZSKJW--GB1-0, FZSKJW--GB1;
        color: #fff;
        font-size: 22px;
        vertical-align: top;
        margin-left: 20px;
      }
    }
    .header-right {
      display: flex;
      align-items: center;
      .time {
        height: 43px;
        font-size: 36px;
        font-family: DIN-Light, DIN;
        font-weight: 300;
        color: #ffffff;
        line-height: 43px;
      }
      .today {
        padding: 0 34px 0 14px;
        color: #a9c6cc;
        font-size: 14px;
        font-family: DIN-Light, DIN;
        font-weight: 300;
      }
      .home {
        img {
          width: 52px;
          height: 59px;
        }
      }
    }

    .middle {
      height: 89px;
      font-size: 40px;
      font-family: FZSKJW--GB1-0, FZSKJW--GB1;
      font-weight: normal;
      color: #fff;
      line-height: 89px;
      position: absolute;
      margin: 0 auto;
      left: 0;
      right: 0;
      text-align: center;
    }
  }
  .bg-header {
    position: absolute;
    width: 100%;
    height: 1080px;
    background: rgba(2, 14, 50, 0.75);
    z-index: 9999;
    top:0;
    left:0;
  }
  .top-dropdown {
    // background: url(~@/assets/special/img/header/headerMain.png) no-repeat;
    width: 235px;
    // height: 523px;
    z-index: 10000;
    position: absolute;
    top: 40px;
    left: 80px;
    background-size: 100%;
    padding-top: 17px;
    .dropdown-item {
      height: 55px;
      color: #fff;
      font-size: 20px;
      display: flex;
      align-items: center;
      border: 1px solid;
      border-image: linear-gradient(
          270deg,
          rgba(219, 235, 252, 0.17),
          rgba(200, 223, 250, 0.17),
          rgba(181, 211, 248, 0)
        )
        1 1;
      img.item-unselected-img {
        margin-left: 25px;
        width: 20px;
        height: 20px;
        display: block;
      }
      img.item-selected-img {
        display: none;
      }
      span {
        margin-left: 13px;
        font-family: PingFangSC-Regular, PingFang SC;
      }

      &.active {
        // background: url(~@/assets/special/img/header/dropSelected.png) no-repeat;
        img.item-selected-img {
          width: 40px;
          height: 40px;
          display: block;
        }
        img.item-unselected-img {
          display: none;
        }
        span {
          font-family: AppleSystemUIFont;
          color: #dbdbdb;
          line-height: 23px;
          text-shadow: 0px 0px 5px rgba(0, 0, 0, 0.5);
          background: linear-gradient(180deg, #eeeeee 0%, #ffca14 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
  }
}
</style>
