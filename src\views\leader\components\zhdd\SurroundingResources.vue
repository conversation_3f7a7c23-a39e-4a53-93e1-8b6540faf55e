<template>
  <!-- 指挥调度-周边资源 -->
  <div class="surrounding-resources">
    <div class="close" @click="$emit('closeEmit')"></div>
    <BlockBox
      title="周边资源"
      subtitle="Surrounding Resources"
      class="box"
      :isListBtns="false"
      :blockHeight="902"
    >
      <div class="wrap">
        <div class="slider">
          <div class="title">
            <div class="label">
              <div class="icon"></div>
              <div class="name">检测范围</div>
            </div>
            <div class="locate">
              <div class="icon"></div>
              <div class="name" :title="currentAddress ? currentAddress : '南京市湖熟街道'">{{ currentAddress ? currentAddress : '南京市湖熟街道' }}</div>
            </div>
          </div>
          <el-slider
            v-model="sliderVal"
            :marks="marks"
            :format-tooltip="formatTooltip"
            @change="changeSlider"
          ></el-slider>
        </div>
        <div class="cont">
          <ul class="tabs">
            <li
              :class="{ active: activeIdx === i }"
              v-for="(it, i) of tabs"
              :key="i"
              @click="checkType(it, i)"
            >
              <div class="label">{{ it.name }}</div>
              <div class="count">{{ it.count }}</div>
            </li>
          </ul>
          <div class="list-wrap">
            <el-input
              v-model="searchVal"
              placeholder="搜索"
              suffix-icon="el-icon-search"
            ></el-input>
            <ul class="list">
              <li v-for="(it, i) of lists" :key="i">
                <div class="check" :class="{ checked: it.checked }" @click="check(it, i)"></div>
                <div class="icon" :style="{ backgroundImage: `url(${it.icon})` }"></div>
                <div class="info">
                  <div class="des">
                    <div class="name">{{ it.userName }}</div>
                    <div class="grid">{{ it.deptName }}</div>
                  </div>
                  <div class="btns">
                    <div class="btn"></div>
                    <div class="btn"></div>
                    <div class="btn" @click="voiceCall(0, it)"></div>
                    <div class="btn" @click="videoCall(0, it)"></div>
                  </div>
                </div>
              </li>
            </ul>
            <div class="operate">
              <div class="voice-call" @click="voiceCall(1)">
                <div class="icon"></div>
                <div class="label">语音通话</div>
              </div>
              <div class="video-call" @click="videoCall(1)">
                <div class="icon"></div>
                <div class="label">视频通话</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </BlockBox>
  </div>
</template>

<script>
import BlockBox from '@/components/leader/common/blockBox.vue'
import { surroundingResourceTabs } from '@/views/leader/components/zhdd/index.d.js'
export default {
  name: 'SurroundingResources',
  components: {
    BlockBox
  },
  props: {
    currentAddress: {
      type: String,
      default: '南京市湖熟街道'
    },
    lists: {
      type: Array,
      default: () => {
        return [
          { checked: true, icon: require('@/assets/zhdd/icon13.png') },
          { checked: false, icon: require('@/assets/zhdd/icon13.png') },
          { checked: false, icon: require('@/assets/zhdd/icon13.png') },
          { checked: false, icon: require('@/assets/zhdd/icon13.png') },
          { checked: false, icon: require('@/assets/zhdd/icon13.png') },
          { checked: false, icon: require('@/assets/zhdd/icon13.png') }
        ]
      }
    },
    tabs: {
      type: Array,
      default: () => {
        return [
          {
            label: '人员信息',
            count: 0
          },
          {
            label: '物资信息',
            count: 0
          },
          {
            label: '车辆信息',
            count: 0
          },
          {
            label: '医疗机构',
            count: 0
          },
          {
            label: '避难场所',
            count: 0
          },
          {
            label: '监控摄像',
            count: 0
          },
          {
            label: '无人机',
            count: 0
          }
        ]
      }
    }
  },
  watch: {
    sliderVal(newVal) {
      this.$emit('changeDistance', newVal)
    }
  },
  data() {
    return {
      sliderVal: 50,
      marks: {
        0: '500m',
        100: '1000m'
      },
      activeIdx: 0,
      searchVal: ''
    }
  },
  methods: {
    formatTooltip(val) {
      return val * 5 + 500 + 'm'
    },
    check(it, i) {
      it.checked = !it.checked
    },
    voiceCall(type, item) {
      // 0 单人接入 | 1 多人接入 (开发中...)
      if (type === 0) {
        this.$emit('voiceCall', this.lists, type, item)
      } 
      // else {
      //   this.$emit('voiceCall', this.lists, type, item)
      // }
    },
    videoCall(type, item) {
      // 0 单人接入 | 1 多人接入 (开发中...)
      if (type === 0) {
        this.$emit('videoCall', this.lists, type, item)
      } 
      // else {
      //   this.$emit('videoCall', this.lists, type, item)
      // }
    },
    checkType(it, i) {
      this.activeIdx = i
      this.$emit('checkTab', it, i)
    },
    changeSlider(val) {
      console.log(val)
      this.$emit('changeSlider', val)
    }
  }
}
</script>

<style lang="less" scoped>
.surrounding-resources {
  position: absolute;
  top: 123px;
  right: 0;
  width: 538px;
  padding-left: 28px;
  padding-right: 50px;
  z-index: 1003;
  background: linear-gradient(270deg, #060c10 0%, rgba(0, 0, 0, 0.1) 100%);
  .close {
    position: absolute;
    top: -4px;
    right: 46px;
    width: 31px;
    height: 31px;
    background-image: url('~@/assets/zhdd/close.png');
    cursor: pointer;
    z-index: 2000;
  }
  .wrap {
    width: 100%;
    height: 100%;
    padding-left: 24px;
    .slider {
      height: 126px;
      padding: 13px 0 0 0;
      .title {
        padding: 0 22px 0 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        .label {
          display: flex;
          align-items: center;
          gap: 9px;
          .icon {
            width: 16px;
            height: 16px;
            background-image: url('~@/assets/zhdd/icon11.png');
          }
          .name {
            font-size: 18px;
            font-family: PingFangSC, PingFang SC;
            color: #ffffff;
          }
        }
        .locate {
          display: flex;
          align-items: center;
          gap: 3px;
          max-width: 240px;
          .icon {
            width: 21px;
            height: 21px;
            background-image: url('~@/assets/zhdd/icon12.png');
          }
          .name {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            width: calc(100% - 24px);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
    .cont {
      height: calc(100% - 126px);
      .tabs {
        height: 73px;
        display: flex;
        flex-wrap: wrap;
        gap: 13px 11px;
        margin-bottom: 16px;
        li {
          display: flex;
          // justify-content: space-between;
          align-items: center;
          gap: 10px;
          width: 100px;
          height: 30px;
          padding: 0 0 0 11px;
          background-image: url('~@/assets/zhdd/bg6.png');
          cursor: pointer;
          .label {
            font-size: 13px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ccf4ff;
          }
          .count {
            font-size: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
          }
          &.active {
            background-image: url('~@/assets/zhdd/bg7.png');
            .label {
              color: #fff2cc;
            }
          }
        }
      }
      .list-wrap {
        width: 436px;
        height: 636px;
        padding: 18px 0 20px 14px;
        text-align: left;
        border: 1px solid;
        border-image: linear-gradient(
            270deg,
            rgba(5, 155, 238, 0.3),
            rgba(5, 155, 238, 0.6),
            rgba(5, 155, 238, 0.3)
          )
          1 1;
        /deep/ .el-input {
          width: 281px;
          height: 36px;
          border-radius: 3px;
        }
        /deep/ .el-input__inner {
          height: 34px;
          line-height: 34px;
          border: 1px solid #89c7ff;
          color: #ffffff;
          background-color: transparent;
        }
        /deep/ .el-input__icon {
          line-height: 36px;
          cursor: pointer;
        }
        .list {
          height: 516px;
          overflow-y: scroll;
          &::-webkit-scrollbar {
            /*滚动条整体样式*/
            width: 6px;
            background: transparent;
            // border: 1px solid #999;
            /*高宽分别对应横竖滚动条的尺寸*/
            // height: 1px;
          }

          &::-webkit-scrollbar-thumb {
            /*滚动条里面小方块*/
            border-radius: 2px;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
            background: rgb(45, 124, 228);
            height: 100px;
          }
          li {
            display: flex;
            align-items: center;
            height: 86px;
            padding: 16px 37px 16px 7px;
            border-bottom: 1px solid rgba(132, 157, 208, 0.3);
            .check {
              width: 24px;
              height: 24px;
              border-radius: 3px;
              border: 1px solid #25ddff;
              margin-right: 8px;
              cursor: pointer;
              &.checked {
                background: url('~@/assets/zhdd/icon14.png') no-repeat center;
              }
            }
            .icon {
              width: 54px;
              height: 54px;
              margin-right: 3px;
            }
            .info {
              flex: 1;
              display: flex;
              flex-direction: column;
              margin-top: -5px;
              .des {
                display: flex;
                justify-content: space-between;
                align-items: center;
                .name {
                  font-size: 18px;
                  font-family: PingFangSC, PingFang SC;
                  color: #ffffff;
                }
                .grid {
                  font-size: 14px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: 400;
                  color: #ffffff;
                }
              }
              .btns {
                display: flex;
                justify-content: flex-end;
                gap: 9px;
                .btn {
                  width: 60px;
                  height: 24px;
                  cursor: pointer;
                  &:nth-child(1) {
                    background-image: url('~@/assets/zhdd/icon15.png');
                  }
                  &:nth-child(2) {
                    background-image: url('~@/assets/zhdd/icon16.png');
                  }
                  &:nth-child(3) {
                    background-image: url('~@/assets/zhdd/icon17.png');
                  }
                  &:nth-child(4) {
                    background-image: url('~@/assets/zhdd/icon18.png');
                  }
                }
              }
            }
          }
        }
        .operate {
          margin-top: 15px;
          margin-left: 50px;
          display: flex;
          gap: 17px;
          .voice-call,
          .video-call {
            width: 150px;
            height: 33px;
            padding-left: 26px;
            display: flex;
            align-items: center;
            background: url('~@/assets/zhdd/btn.png') no-repeat;
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #ffffff;
            cursor: pointer;
            .icon {
              width: 22px;
              height: 22px;
              margin-right: 7px;
            }
          }
          .voice-call {
            .icon {
              background: url('~@/assets/zhdd/icon19.png') no-repeat;
            }
          }
          .video-call {
            .icon {
              background: url('~@/assets/zhdd/icon20.png') no-repeat;
            }
          }
        }
      }
    }
  }
}
/deep/ .el-slider__marks-text {
  color: #fff;
}
/deep/ .el-slider__marks-text {
  &:nth-child(1) {
    padding-left: 25px;
  }
  &:nth-child(2) {
    padding-right: 25px;
  }
}
/deep/ .el-slider__bar {
  background: linear-gradient(270deg, #549eea 0%, rgba(140, 205, 246, 0) 100%);
  border-radius: 0px 100px 100px 0px;
}
/deep/ .el-slider__button {
  border: 2px solid #fff;
  background-color: #409eff;
}
</style>
