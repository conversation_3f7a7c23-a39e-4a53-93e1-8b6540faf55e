import http from '@/utils/leader/request';
import { finaUrl } from '@/utils/leader/const';

// 汇集数据总量，汇集数据数量变化趋势
export const collectedData = () => {
  return http.get(
    finaUrl + '/api/sjjsc/collectedData'
  );
};
// 数据目录变化趋势
export const dataDirChangeTrend = () => {
  return http.get(
    finaUrl + '/api/sjjsc/dataDirChangeTrend'
  );
};
// 数据共享情况
export const dataSharing = () => {
  return http.get(
    finaUrl + '/api/sjjsc/dataSharing'
  );
};
// 数据标准化治理
export const dataStandardization = () => {
  return http.get(
    finaUrl + '/api/sjjsc/dataStandardization'
  );
};
// 高频使用资源排名
export const highFrequencyResources = () => {
  return http.get(
    finaUrl + '/api/sjjsc/highFrequencyResources'
  );
};
//  对接部门数，建设子平台数，归集库数据量，归集库存储量，上线目录总数
export const overview = () => {
  return http.get(
    finaUrl + '/api/sjjsc/overview'
  );
};
//  资源共享情况
export const resourceSharing = () => {
  return http.get(
    finaUrl + '/api/sjjsc/resourceSharing'
  );
};
//  部门数据目录
export const deptDataCatalogue = () => {
  return http.get(
    finaUrl + '/api/sjjsc/deptDataCatalogue'
  );
};
