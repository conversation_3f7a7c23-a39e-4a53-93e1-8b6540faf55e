<template>
  <div class="drone-video" v-if="show">
    <div class="close_btn" @click="$emit('input', false)"></div>
    <div class="title">
      <span>视频监控</span>
    </div>
    <div class="content">
      <hlsVideo class="video_" :src="videoUrl" :cameraCode="cameraCode"></hlsVideo>
    </div>
  </div>
</template>

<script>
import hlsVideo from '@/components/leader/hlsVideo'
// import LivePlayer from '@liveqing/liveplayer'
export default {
  name: 'VideoSingle',
  components: {
    //   LivePlayer,
    hlsVideo
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    videoUrl: {
      type: String,
      default: ''
    },
    cameraCode: {
      type: String,
      default: ''
    }
  },
  computed: {
    show() {
      return this.value
    }
  },
  data() {
    return {}
  }
}
</script>

<style lang="less" scoped>
.drone-video {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1101;
  width: 673px;
  height: 500px;
  background: rgba(9, 19, 34, 0.9);
  box-shadow: 0px 0px 22px 0px rgba(11, 54, 138, 0.35), 0px 2px 8px 0px rgba(0, 0, 0, 0.7),
    inset 0px 0px 8px 0px rgba(17, 146, 214, 0.35);
  border-radius: 11px;
  border: 1px solid #015c8c;
  .close_btn {
    position: absolute;
    width: 23px;
    height: 23px;
    top: 25px;
    right: 40px;
    background: url(~@/assets/map/dialog/btn3.png) no-repeat;
    cursor: pointer;
  }
  .title {
    margin: 0 auto 23px;
    width: 635px;
    height: 76px;
    line-height: 76px;
    background: url(~@/assets/map/dialog/title2.png) no-repeat;
    display: grid;
    place-items: center;
    span {
      font-size: 36px;
      font-family: PingFangSC, PingFang SC;
      line-height: 36px;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .content {
    width: 100%;
    height: calc(100% - 100px);
    padding: 20px;
  }
}
</style>
