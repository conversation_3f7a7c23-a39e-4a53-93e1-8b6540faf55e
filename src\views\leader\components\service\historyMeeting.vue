<template>
  <div class="histroy-dialog">
    <div class="histroy-dialog-head">
      <div class="dialog-title">历史会议</div>
      <div class="close-icon" @click="closeDialog"></div>
    </div>
    <div class="dialog-content">
      <div class="form-content">
        <el-form ref="historyFrom" :model="formData" label-position="right" label-width="76px">
          <el-row :gutter="48">
            <el-col :span="8">
              <el-form-item label="会议主题" prop="roomName">
                <el-input v-model="formData.roomName" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="会议ID" prop="roomName">
                <el-input v-model="formData.roomNum" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="发起人" prop="roomName">
                <el-input v-model="formData.creator" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="8">
              <el-form-item label="创建时间">
                <el-date-picker
                  v-model="formData.timeRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <div class="form-btns">
                <div class="form-btn form-btn-reset" @click="reset">重置</div>
                <div class="form-btn form-btn-search" @click="search">查询</div>
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="btns">
        <div class="btn btn-del" @click="removeAll">删除</div>
      </div>
      <el-table :data="tableData" :height="342" @selection-change="selectChange">
        <el-table-column type="selection" width="57px"> </el-table-column>
        <el-table-column type="index" label="序号" width="80px"> </el-table-column>
        <el-table-column prop="roomNum" label="会议ID" min-width="100px"> </el-table-column>
        <el-table-column prop="roomName" show-overflow-tooltip label="会议主题" min-width="140px"> </el-table-column>
        <el-table-column prop="creator" show-overflow-tooltip label="发起人" min-width="70px"> </el-table-column>
        <el-table-column prop="peopleNum" label="参会人数" min-width="70px"> </el-table-column>
        <el-table-column prop="beginTime" label="开始时间" min-width="140px"> </el-table-column>
        <el-table-column prop="endTime" label="结束时间" min-width="140px"> </el-table-column>
        <el-table-column prop="duration" label="会议时长" min-width="70px"> </el-table-column>
        <el-table-column label="操作" min-width="60px">
          <template slot-scope="scope">
            <div class="table-btns">
              <div class="table-btn" @click="delHistoryRecord(scope.row.roomNum)">删除</div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="dialog-content-foot">
        <el-pagination
          background
          layout="prev, pager, next, sizes, jumper"
          :page-size="pageData.pageSize"
          :current-page="pageData.page"
          :total="pageData.total"
          :page-sizes="[6]"
          @current-change="pageCurrentChange"
          @size-change="pageSizeChange"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { timeToMinute } from "./tool/index"
import { queryHistoryMeeting, delHistoryMeeting } from "@/api/bjnj/preview.js"
export default {
  name: 'historyMeeting',
  props: {},
  data() {
    return {
      formData: {
        roomName: '',
        roomNum: '',
        creator: '',
        timeRange: []
      },
      meetingStatusList: [
        {
          label: '待开始',
          value: 0
        },
        {
          label: '进行中',
          value: 1
        },
        {
          label: '已结束',
          value: 2
        }
      ],
      tableData: [],
      pageData: {
        pageSize: 6,
        page: 1,
        total: 0
      },
      selection: []
    }
  },
  computed: {
    username() {
      return this.$store.state.username
    },
    mobile() {
      return this.$store.state.mobile
      // return '17630502601'
    }
  },
  mounted() {
    this.getHistoryList()
  },
  methods: {
    getHistoryList() {
      let params = {
        roomAlias: this.formData.roomName,
        roomId: this.formData.roomNum,
        owner: this.formData.creator,
        startTime: this.formData.timeRange.length > 0 ? new Date(this.formData.timeRange[0]).toISOString() : '0001-01-01T00:00:00Z',
        endTime: this.formData.timeRange.length > 0 ? new Date(this.formData.timeRange[1]).toISOString() : '0001-01-01T00:00:00Z',
        page: this.pageData.page,
        pageSize: this.pageData.pageSize
      }
      queryHistoryMeeting(params).then(res => {
        console.log('获取会议历史记录', res);
        if(res.data && res.data.length > 0) {
          this.tableData = res.data.map(item => {
            return {
              roomNum: item.roomName,
              roomName: item.roomAlias,
              creator: item.owner,
              peopleNum: item.realNumParticipants,
              beginTime: this.dayjs(item.reserveStart).format('YYYY-MM-DD HH:mm'),
              endTime: this.dayjs(item.reserveEnd).format('YYYY-MM-DD HH:mm'),
              duration: timeToMinute((new Date(item.reserveEnd).getTime() - new Date(item.reserveStart).getTime()) / 1000)
            }
          })
          this.pageData.total = res.data.length
        } else {
          this.tableData = []
          this.pageData.total = 0
        }
      })
    },
    selectChange(selection) {
      this.selection = selection;
    },
    removeAll() {
      if(this.selection.length > 0) {
        this.selection.forEach(item => {
          if(item.roomNum) {
            this.delHistoryRecord(item.roomNum)
          }
        })
      }
    },
    delHistoryRecord(roomNum) {
      let param = {
        room: roomNum
      }
      delHistoryMeeting(param).then(res => {
        if(res.code === 200) {
          this.$messageNew.message('success', {
            message: '删除成功'
          })
          this.getHistoryList()
        }
      })
    },
    reset() {
      this.formData = {
        roomName: '',
        roomNum: '',
        creator: '',
        timeRange: []
      }
      this.pageData = {
        pageSize: 6,
        page: 1,
        total: 6
      }
      this.getHistoryList()
    },
    search() {
      this.pageData.page = 1
      this.getHistoryList()
    },
    closeDialog() {
      this.$emit('historyDialogClose')
    },
    pageCurrentChange(val) {
      this.pageData.page = val
      this.getHistoryList()
    },
    pageSizeChange(size) {
      this.pageData.pageSize = size
      this.getHistoryList()
    }
  }
}
</script>

<style lang="scss" scoped>
.histroy-dialog {
  position: absolute;
  z-index: 4500;
  width: 1244px;
  min-height: 696px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: url('~@/assets/service/history_bg.png') no-repeat center / 100% 100%;
  &-head {
    height: 60px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 48px 0 38px;
    margin-bottom: 30px;
    .dialog-title {
      font-family: YouSheBiaoTiHei;
      font-size: 28px;
      color: #ffffff;
    }
    .close-icon {
      cursor: pointer;
      width: 32px;
      height: 32px;
      background: url('~@/assets/service/close.png') no-repeat center / 100% 100%;
    }
  }
  .dialog-content {
    width: 100%;
    padding: 0 34px;
    .form-content {
      width: 100%;
      padding: 20px 26px 0;
      border: 1px dashed #5e90d0;
      border-radius: 13px;
      ::v-deep .el-input__inner {
        &::placeholder {
          color: rgba(194, 221, 252, 0.7) !important;
        }
        border-radius: 5px;
        border: 1px solid #cbe5ff;
        background-color: transparent;
        height: 36px;
        line-height: 36px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: rgba(194, 221, 252, 1);
      }
      ::v-deep .el-form {
        .el-form-item {
          margin-bottom: 24px;
          &:nth-child(2) {
            margin-bottom: 0px !important;
          }
          &__label {
            font-family: Source Han Sans CN;
            font-weight: 400;
            font-size: 16px;
            color: #c2ddfc;
          }
          &__content {
            text-align: left;
            .el-select {
              width: 100%;
            }
          }
        }
      }
      ::v-deep .el-date-editor {
        width: 267px;
        .el-range-input {
          &::placeholder {
            color: rgba(194, 221, 252, 0.7) !important;
          }
          border-radius: 5px;
          // border: 1px solid #cbe5ff;
          background-color: transparent;
          height: 36px;
          line-height: 36px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 16px;
          color: rgba(194, 221, 252, 1);
        }
        .el-range-separator {
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 16px;
          color: rgba(194, 221, 252, 1);
          line-height: 28px;
          padding: 0 5px 0 0;
        }
        .el-input__prefix {
          display: none;
        }
        .el-input__suffix {
          &-inner {
            padding-right: 2px;
            display: flex;
            align-items: center;
            height: 100%;
          }
          .el-input__icon {
            display: inline-block;
            width: 14px;
            height: 14px;
          }
        }
      }
      ::v-deep .el-date-editor--date {
        width: 133px;
        margin-right: 8px;
        .el-input__suffix {
          .el-input__icon {
            background: url('~@/assets/service/date_icon.png') no-repeat center / 100% 100%;
          }
        }
      }
      .form-btns {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        .form-btn {
          width: 64px;
          height: 36px;
          border-radius: 5px;
          line-height: 36px;
          text-align: center;
          cursor: pointer;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 16px;
          &-reset {
            border: 1px solid #00aaff;
            color: #00aaff;
            margin-right: 10px;
          }
          &-search {
            background: #00aaff;
            color: #ffffff;
          }
        }
      }
    }
    .btns {
      margin-top: 20px;
      margin-bottom: 12px;
      width: 100%;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      .btn {
        width: 64px;
        height: 36px;
        border-radius: 5px;
        line-height: 36px;
        text-align: center;
        cursor: pointer;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        &-del {
          border: 1px solid #ff6060;
          color: #ff6060;
        }
      }
    }
    .table-btns {
      width: 100%;
      display: flex;
      align-items: center;
      .table-btn {
        cursor: pointer;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #FF6060;
      }
    }
    ::v-deep .el-table {
      color: #ffffff;
      .el-table__header {
        tr {
          background-color: rgba(255, 255, 255, 0.1);
          .el-table__cell {
            background-color: rgba(255, 255, 255, 0.1);
            height: 46px !important;
            border: none !important;
            .cell {
              font-family: Source Han Sans CN;
              font-weight: 500;
              font-size: 16px;
              color: #9ddeff;
            }
          }
        }
      }
      .el-table__body {
        tr {
          background-color: transparent;
          .el-table__cell {
            background-color: transparent;
            height: 46px !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            .cell {
              font-family: Source Han Sans CN;
              font-weight: 500;
              font-size: 16px;
              color: #ffffff;
            }
          }
        }
      }
    }
    ::v-deep .el-table,
    .el-table__expanded-cell {
      background-color: transparent !important;
    }
    &-foot {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      ::v-deep .el-pagination {
        .btn-prev {
          background: transparent;
          color: #ffffff;
          border: 1px solid rgba(255, 255, 255, 0.5);
        }
        .btn-next {
          background: transparent;
          color: #ffffff;
          border: 1px solid rgba(255, 255, 255, 0.5);
        }
        .el-input__inner {
          &::placeholder {
            color: rgba(194, 221, 252, 0.7) !important;
          }
          border-radius: 5px;
          border: 1px solid #cbe5ff;
          background-color: transparent;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 16px;
          color: rgba(194, 221, 252, 1);
        }
        .el-pagination__jump {
          color: #fff;
        }
      }
      ::v-deep .is-background {
        li:not(.disabled).active {
          background-color: #409eff;
          color: #fff;
        }
        li {
          background: transparent;
          color: #fff;
          border: 1px solid rgba(255, 255, 255, 0.5);
        }
      }
    }
  }
}
</style>
