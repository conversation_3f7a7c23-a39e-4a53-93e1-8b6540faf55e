<template>
  <div class="container">
    <!-- 切换地图/影像地图 -->
    <!-- <div class="toggleBtn">
      <div
        class="btn"
        :class="{ active: plugin[1].defaultType === index }"
        v-for="(item, index) in mapBtn"
        :key="index"
        @click="toggle(index)"
      >
        <img :src="plugin[1].defaultType === index ? item.icon : item.icon_a" />
        <span class="label">{{ item.name }}</span>
      </div>
    </div> -->
    <el-amap
      :plugin="plugin"
      class="map-content"
      :center="center"
      :zoom="zoom"
      :mapStyle="mapStyle"
      ref="map1"
      v-if="showMap"
    >
      <el-amap-info-window
        :position="window.position"
        :content="window.content"
        :visible="window.visible"
        :isCustom="true"
      >
      </el-amap-info-window>
      <el-amap-marker
        v-for="(marker, idx) in markers"
        :key="`marker-${idx}`"
        :position="marker.position"
        :events="marker.events"
        :draggable="false"
        :icon="marker.icon"
      />
      <el-amap-polygon
        strokeColor="#3DDCFF"
        strokeOpacity="0.5"
        fillColor="#3DDCFF"
        fillOpacity="0.2"
        v-for="(polygon, index) in polygons"
        :key="index"
        strokeWeight="6"
        :path="polygon.De.path"
      >
      </el-amap-polygon>
    </el-amap>
    <!-- 地图打点切换 -->
    <div class="iconBtns">
      <div
        class="icon"
        v-for="(item, index) in icons"
        :key="index"
        @click="handleIconClick(index)"
        :style="{ backgroundImage: `url(${item.icon})` }"
      >
        {{ item.name }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'wghcl',
  props: {
    showZlPop: {
      type: Boolean,
      default: false
    },
    markers: {
      type: Array,
      default: () => []
    },
    icons: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    icons(newValue) {
      console.log('icons', newValue);
      console.time('update');
    },
    markers(newValue) {
      console.log('markers', newValue);
    }
  },
  data() {
    return {
      iconBtns: [{ name: '1' }, { name: '2' }],
      windowList: [],
      window: {
        position: [0, 0],
        content: '',
        visible: false
      },
      mapBtn: [
        {
          name: '地图'
          // icon: require('@/assets/img/map/001.png'),
          // icon_a: require('@/assets/img/map/001s.png')
        },
        {
          name: '影像地图'
          // icon: require('@/assets/img/map/002.png'),
          // icon_a: require('@/assets/img/map/002s.png')
        }
      ],
      events: {
        // click(e) {
        //   if (!self.showZlPop) return;
        //   self.window.visible = false;
        //   const { lng, lat } = e.lnglat;
        //   self.window.position = [lng, lat];
        //   self.window.content = `
        //     <div style="color:#333;padding:20px;background-color:#fff;position:relative;border-radius: 5px">
        //       <i class="el-icon-close close-map-window-btn" style="position:absolute;top:5px;right:5px;"></i>
        //       <div style="display:flex;align-items:center;justify-content: space-between; margin-bottom: 2px;">
        //         <div>2020年累计生产总值（万元）:</div>
        //         <div>1611768</div>
        //       </div>
        //       <div style="display:flex;align-items:center;justify-content: space-between; margin-bottom: 2px;">
        //         <div>一般公共预算收入（万元）:</div>
        //         <div>115219</div>
        //       </div>
        //       <div style="display:flex;align-items:center;justify-content: space-between; margin-bottom: 2px;">
        //         <div>实际利用外资（万美元）:</div>
        //         <div>1138</div>
        //       </div>
        //       <div style="display:flex;align-items:center;justify-content: space-between; margin-bottom: 2px;">
        //         <div>社会消费品零售总额（万元）:</div>
        //         <div>492271</div>
        //       </div>
        //       <div style="display:flex;align-items:center;justify-content: space-between; margin-bottom: 0px;">
        //         <div>居民人均可支配收入（元）:</div>
        //         <div>32105</div>
        //       </div>
        //     </div>
        //   `;
        //   self.window.visible = true;
        //   setTimeout(() => {
        //     const a = document.querySelectorAll('.close-map-window-btn')[0];
        //     a.onclick = () => {
        //       self.window.visible = false;
        //     };
        //   }, 100);
        // }
      },
      plugin: [
        {
          enableHighAccuracy: true, //是否使用高精度定位，默认:true
          timeout: 100, //超过10秒后停止定位，默认：无穷大
          maximumAge: 0, //定位结果缓存0毫秒，默认：0
          convert: true, //自动偏移坐标，偏移后的坐标为高德坐标，默认：true
          showButton: false, //显示定位按钮，默认：true
          buttonPosition: 'RB', //定位按钮停靠位置，默认：'LB'，左下角
          showMarker: true, //定位成功后在定位到的位置显示点标记，默认：true
          showCircle: true, //定位成功后用圆圈表示定位精度范围，默认：true
          panToLocation: true, //定位成功后将定位到的位置作为地图中心点，默认：true
          zoomToAccuracy: true, //定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：f
          extensions: 'all',
          pName: 'Geolocation'
        },
        {
          pName: 'MapType',
          defaultType: 0,
          showTraffic: false,
          showRoad: true
        }
      ],
      center: [116.571588,33.273831],
      zoom: 9,
      mapStyle: 'amap://styles/d3ec7c1ebe50a3c4ce2a079e656b1a78',
      polygons: [],
      district: null,
      showMap: false
    };
  },
  created() {
    this.showMap = true;
  },
  updated() {
    console.log('map:updated');
    console.timeEnd('update');
  },
  mounted() {
    this.$nextTick(() => {
      setTimeout(() => {
        this.drawBounds();
      }, 3000);
    });
  },
  methods: {
    drawBounds() {
      console.log(1111);
      var that = this;
      //加载行政区划插件`
      if (!self.district) {
        //实例化DistrictSearch
        var opts = {
          subdistrict: 0, //获取边界不需要返回下级行政区
          extensions: 'all', //返回行政区边界坐标组等具体信息
          level: 'district' //查询行政级别为 市
        };
        console.log('self.AMap', self.AMap.DistrictSearch);
        self.district = new self.AMap.DistrictSearch(opts);
      }
      //行政区查询
      // let code = self.listDate.city + "";
      self.district.search('蒙城县', function(status, result) {
        that.polygons = [];
        var bounds = result.districtList[0].boundaries;
        if (bounds) {
          console.log(222);
          for (var i = 0, l = bounds.length; i < l; i++) {
            //生成行政区划polygon
            var polygon = new self.AMap.Polygon({
              strokeWeight: 1,
              path: bounds[i],
              fillOpacity: 0.4,
              fillColor: '#000001',
              strokeColor: '#0091ea'
            });
            that.polygons.push(polygon);
          }
        }
        self.AMap.Polygon.bind(that.polygons);
        // that.$refs.map1.$amap.setFitView(that.polygons); //视口自适应
      });
    },
    showWindowInfo(position, content) {
      this.window = {
        position: position,
        content: content,
        visible: true
      };
    },
    toggle(type) {
      if (this.plugin[1].defaultType === type) {
        return false;
      }
      this.showMap = false;
      this.plugin[1].defaultType = type;
      this.$nextTick(() => {
        this.showMap = true;
        this.drawBounds();
      });
    },
    handleIconClick(i) {
      this.$emit('clickIcon', i);
    },
    drawMarker(markersList) {
      console.log(markersList);
      this.markers = markersList.map(it => ({
        icon: it.icon,
        position: it.position,
        offset: [0, 0],
        events: {
          click: () => {
            console.log(document.querySelectorAll('.close-map-window-btn'));
            console.log('点击标记');
            this.showWindowInfo(it.position, it.popup);
            this.$parent.sureCamera(it);
            this.$nextTick(() => {
              setTimeout(() => {
                const a = document.querySelectorAll('.close-map-window-btn')[0];
                console.log(a);
                a.onclick = () => {
                  this.window.visible = false;
                };
              });
            });
          }
        }
      }));
    }
  }
};
</script>

<style lang="scss" scoped>
/* 图标大小修改 */
::v-deep .amap-icon img {
  max-width: 50px !important;
  max-height: 50px !important;
}
::v-deep .amap-maptypecontrol {
  display: none;
}
.container {
  width: 1920px;
  height: 1080px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  .map-content {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    position: absolute;
    z-index: 100;
  }
  .toggleBtn {
    // border: 1px solid red;
    position: absolute;
    top: calc(70px + 1.5625rem + 4.8125rem + 20px);
    right: calc(2.5rem + 30.625rem + 10px);
    z-index: 999;
    // background: red;
    padding: 10px;
    border-radius: 10px;
    background: rgba(0, 0, 0, 0.5);
    .btn {
      font-size: 20px;
      color: #5783b1;
      display: flex;
      align-items: center;
      // margin-bottom: 20px;
      font-weight: 800;
      img {
        width: 30px;
        height: 30px;
        margin-right: 10px;
      }
      height: 53px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: 100% 100%;
      &:nth-of-type(1) {
        &.active {
          // background-image: url(~@/assets/img/map/bg1.png);
          color: #30e2ff;
        }
      }
      &:nth-of-type(2) {
        &.active {
          // background-image: url(~@/assets/img/map/bg2.png);
          color: #30e2ff;
        }
      }
    }
  }
  .iconBtns {
    position: absolute;
    bottom: 70px;
    right: calc(2.5rem + 30.625rem + 30px);
    z-index: 999;
    padding: 10px;
    border-radius: 10px;
    .icon {
      width: 140px;
      height: 42px;
      margin-bottom: 9px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: 100% 100%;
      &:hover {
        cursor: pointer;
        opacity: 0.8;
      }
    }
  }
}
</style>
