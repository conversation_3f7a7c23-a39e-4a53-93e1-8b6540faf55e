/*
 * @Author: fanjialong <EMAIL>
 * @Date: 2023-04-17 15:06:09
 * @LastEditors: luyu
 * @LastEditTime: 2024-02-23 16:41:02
 * @FilePath: /hs_dp/src/api/hs/hs.tsgz.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import http from '@/utils/leader/request'
import {
  ssjcUrl,
  xtyyUrl
} from '@/utils/leader/const'

// 实时预警
export const getSsyj = () => {
  return http.get(ssjcUrl + '/api/eventWarning/getEventWarnings?type=WLGZ&page=1&size=10')
}

// 指挥调度网格员列表
export const wglListJk = () => {
  return http.get(xtyyUrl + `/api/tUserInfo/queryAllGridMan`)
}

// 指挥调度网格员列表 区
export const queryOrgStreetTree = data => {
  return http.get(xtyyUrl + `/api/tOrder/queryOrgStreetTree`, data)
}

export const get12345Work = (data) => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryByModule`, data)
}

export const queryCountVos = (data) => {
  return http.get(xtyyUrl + `/api/collectHisCitycases/queryCountVos`, data)
}

export const queryBusinessCount = (data) => {
  return http.get(xtyyUrl + `/api/zwbCnsCinfo/queryBusinessCount`, data)
}

export const queryOrderCount = (data) => {
  return http.get(xtyyUrl + `/api/zwbCnsCinfo/queryOrderCount`, data)
}

export const querySatisfaction = (data) => {
  return http.get(xtyyUrl + `/api/zwbCnsCinfo/querySatisfaction`, data)
}

export const getOrderVoRank = (data) => {
  return http.get(xtyyUrl + `/api/tOrder/getOrderVo`, data)
}

// 预警列表接口：  传page size
export const queryByPageSsyj = (params) => {
  return http.get(ssjcUrl + `/api/eventWarning/queryByPage`, params)
}

// 转工单：
export const alarmToOrder = (data) => {
  return http.post(xtyyUrl + `/api/tOrder/alarmToOrder`, data)
}
