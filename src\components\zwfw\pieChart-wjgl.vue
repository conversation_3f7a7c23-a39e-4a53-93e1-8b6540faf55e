<template>
  <div class="warp">
    <div class="charts" ref="piechart" />
    <div class="middle">
      <div class="middle_num">45%</div>
    </div>
    <div class="legend">
      <div
        class="item"
        v-for="(item, index) in resData"
        :key="index"
        @mouseenter="highlight(index)"
        @mouseleave="downplay(index)"
        @click="select({ item, index })"
        :class="{ active: activeIndex.includes(index) }"
      >
        <div class="dot" :style="{ borderColor: item.color }">
          <div class="inner-dot" :style="{ background: item.color }" />
        </div>
        <div class="label">{{ item.label }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      charts: null,
      //chartColor: this.color,
      activeIndex: [],
    }
  },
  props: {
    data: {
      type: Array,
      default: () => {
        return [
          ['product', '法律咨询'],
          ['特种设备作业人员考核', 1234],
          ['特种设备作业人员复核', 234],
          ['特种设备检验监测人员资格认定，特种设备作业人员资格认定', 1001],
        ]
      },
    },
    // icon: {
    //   type: [Object, String],
    //   default: () => require('@/assets/img/cqyz/wzrh/block5-icon.png')
    // },
    color: {
      type: Array,
      default: () => ['#2EF6FF', '#FFBA4F', '#6D5AE2'],
    },
  },
  computed: {
    resData() {
      const res = this.data
        .filter((it, index) => index > 0)
        .map((item, index) => ({
          label: item[0],
          value: parseFloat(item[1]),
          color: this.color[index],
        }))
      const total = res.reduce((pre, item) => (pre += item.value), 0)
      res.forEach((item) => {
        item.rate = Math.round((item.value / total) * 100) + '%'
      })
      return res
    },
  },
  watch: {
    data: {
      deep: true,
      immediate: true,
      handler(val) {
        this.$nextTick(() => {
          this.initchart(val)
        })
      },
    },
  },
  methods: {
    highlight(index) {
      this.charts.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: index,
      })
    },
    downplay(index) {
      this.charts.dispatchAction({
        type: 'downplay',
        seriesIndex: 0,
        dataIndex: index,
      })
    },
    select({ item, index }) {
      if (this.activeIndex.includes(index)) {
        const i = this.activeIndex.findIndex((item) => item === index)
        this.activeIndex.splice(i, 1)
      } else {
        this.activeIndex.push(index)
      }
      this.charts.dispatchAction({
        type: 'legendToggleSelect',
        // 图例名称
        name: item.label,
      })
    },
    initchart(data) {
      if (this.charts) {
        this.charts.clear()
      }
      this.charts = this.$echarts.init(this.$refs.piechart)
      this.activeIndex = data.filter((it, i) => i > 0).map((it, i) => i)
      const options = {
        color: this.color,
        dataset: {
          source: data,
        },
        legend: {
          show: false,
          data: data.filter((it, index) => index > 0).map((item) => ({ name: item[0] })),
        },
        series: [
          {
            type: 'pie',
            radius: ['58px', '70px'],
            center: [83, '50%'],
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            animationType: 'scale',
            animationEasing: 'elasticOut',
            animationDuration: 2000,
            animationDelay: function (idx) {
              return idx * 100
            },
          },
          {
            type: 'pie',
            radius: ['55px', '63px'],
            center: [83, '50%'],
            data: [{ name: '', value: 100 }],
            itemStyle: {
              color: 'rgba(0,0,0,0.25)',
            },
            ledend: {
              show: false,
            },
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            silent: true,
          },
        ],
      }
      this.charts.setOption(options)
    },
  },
}
</script>

<style lang="scss" scoped>
.warp {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  position: relative;
  // padding: 0 32px;
  .charts {
    width: 166px;
    height: 165px;
    position: relative;
    margin-right: 19px;
    z-index: 1;
    @keyframes roate {
      0% {
        transform: rotate(0);
      }
      100% {
        transform: rotate(360deg);
      }
    }
    &::before {
      width: 100%;
      height: 100%;
      content: '';
      position: absolute;
      display: block;
      top: 0;
      left: 0;
      background: url(~@/assets/leader/img/zwfw/block5-bg.png) no-repeat center / 100% 100%;
      animation: rotate 5s infinite ease-in-out;
    }
  }
  .middle {
    position: absolute;
    z-index: 2;
    width: 88px;
    height: 88px;
    left: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .middle_num {
      font-size: 20px;
      font-family: DIN-BlackItalic, DIN;
      font-weight: normal;
      color: #ffffff;
      line-height: 24px;
    }
    .middle_key {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #ffffff;
      line-height: 20px;
    }
  }
  .legend {
    width: 260px;
    .item {
      width: 215px;
      // height: 22.8px;
      display: flex;
      // align-items: center;
      padding: 0 14px 0 11px;
      margin-bottom: 8px;
      justify-content: space-between;
      &:not(.active) {
        filter: grayscale(100%);
      }
      .dot {
        width: 16.8px;
        height: 16.8px;
        border-radius: 50%;
        border: 1px solid;
        position: relative;
        margin-right: 12px;
        .inner-dot {
          width: 10px;
          height: 10px;
          border-radius: 50%;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
      .info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .label {
          text-align: left;
          font-size: 16px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 25px;
        }
      }

      .label {
        width: 160px;
        text-align: left;
        font-size: 16px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 25px;
      }
      .value {
        text-align: right;
        width: 30px;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffba4f;
        line-height: 20px;
        text-shadow: 0px 0px 1px #00132e;
      }

      .rate {
        text-align: right;
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #2ef6ff;
        line-height: 25px;
        text-shadow: 0px 0px 1px #00132e;
      }
    }
  }
}
</style>
