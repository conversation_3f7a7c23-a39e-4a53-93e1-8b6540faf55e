<template>
  <div :class="['point-dialog', { 'point-dialog-full': isFull }]" v-if="value">
    <div class="point-head">
      <div class="point-head-left">
        <div class="invited">{{ invitedMobile }}</div>
        <div class="call-duration">
          [通话时长<span style="color: #1EFF00;">{{ meetingDuration }}</span
          >]
        </div>
      </div>
      <div class="full-btn" @click="isFull = !isFull"></div>
    </div>
    <div class="point-contain">
      <div id="room-contain" :class="['meeting-room-contain', 'blur']"></div>
      <InstantMessage
        v-if="isInstantMessageShow"
        ref="chatComponent"
        :messageList="messageList"
        :chatStyle="chatStyle"
        :isMonitorIn="true"
        @messageSend="sendMessage"
      ></InstantMessage>
      <div class="point-contain-mask" v-if="isMaskShow">
        <span>正在等待对方接受通话…</span>
      </div>
      <div class="point-foot">
        <div class="point-foot-group">
          <div class="foot-btn" v-if="localMicrophoneShow" @click.stop="changeLocalMicroStatus">
            <div class="foot-btn-icon mic-on"></div>
            <div>麦克风</div>
          </div>
          <div class="foot-btn" v-else @click.stop="changeLocalMicroStatus">
            <div class="foot-btn-icon mic-off"></div>
            <div>麦克风</div>
          </div>
          <div class="foot-btn" v-if="localCameraShow" @click.stop="changeLocalCameraStatus">
            <div class="foot-btn-icon cam-on"></div>
            <div>摄像头</div>
          </div>
          <div class="foot-btn" v-else @click.stop="changeLocalCameraStatus">
            <div class="foot-btn-icon cam-off"></div>
            <div>摄像头</div>
          </div>
          <div class="foot-btn" v-if="isParticipantMicrophoneEnable" @click.stop="mutePaticipant">
            <div class="foot-btn-icon other-on"></div>
            <div>禁音</div>
          </div>
          <div class="foot-btn" v-else @click.stop="unMutePaticipant">
            <div class="foot-btn-icon other-off"></div>
            <div>禁音</div>
          </div>
        </div>
        <div class="point-foot-group">
          <div class="foot-btn" @click="hangOff">
            <div class="foot-btn-icon hang-off"></div>
            <div>挂断</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { sendInviteMessage, generateWxLink } from '@/api/bjnj/zhdd.js'
import util from '@/libs/util.js'
let meetingInterval = null
export default {
  name: 'pointToPoint',
  components: {
    InstantMessage: () => import("./instantMessage.vue")
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    invitedMobile: {
      type: String,
      default: ''
    },
    inviteWay: {
      type: String,
      default: 'mobile'
    },
    userId: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      // 是否全屏
      isFull: false,
      // 是否显示邀请中蒙层
      isMaskShow: false,
      // 通话时长
      duration: 0,
      // 租价内client实例
      liveClient: null,
      // 创建的会议号码
      roomNum: '',
      // 保存当前页面触发的全部事件
      pageEventList: [],
      // 保存会议中绑定的音视频轨道
      trackList: [],
      // 房间与会者列表
      participants: [],
      // 本地用户设备状态
      localMicrophoneShow: true,
      localCameraShow: true,
      isParticipantMicrophoneEnable: true,
      // 即时消息
      isInstantMessageShow: false,
      messageList: [
        {
          fromName: '系统消息',
          content: '会议内文本聊天已启用'
        }
      ],
      chatStyle: {
        width: '295px',
        height: '184px',
        bottom: '86px'
      },
      roomName: ""
    }
  },
  computed: {
    meetingDuration() {
      let minute, second
      if (Math.floor(this.duration / 60) > 0) {
        minute =
          Math.floor(this.duration / 60) <= 9
            ? '0' + Math.floor(this.duration / 60)
            : Math.floor(this.duration / 60)
      } else {
        minute = '00'
      }
      if (Math.floor(this.duration % 60) > 0) {
        second =
          Math.floor(this.duration % 60) <= 9
            ? '0' + Math.floor(this.duration % 60)
            : Math.floor(this.duration % 60)
      } else {
        second = '00'
      }
      return `${minute}分${second}秒`
    },
    participantLength() {
      return this.participants.length
    },
    username() {
      return this.$store.state.username
    }
  },
  watch: {
    async value(newVal) {
      if (newVal) {
        this.duration = 0
        this.liveClient = null
        this.pageEventList = []
        this.trackList = []
        this.participants = []
        this.messageList = []
        this.roomName = ""
        console.log('点对点视频弹窗打开')
        this.liveClient = window['liveClient']
        this.liveClient.roomName = this.userId + '的视频会议'
        if (!this.liveClient) {
          this.$messageNew.error({
            message: '会话初始化失败请刷新页面重试'
          })
          return
        }
        // 监听事件
        this.initLiveClientEvent()
        // 创建房间
        await this.createRoom()
      }
    }
  },
  beforeDestroy() {
    if (this.liveClient) {
      this.leaveRoom()
      this.closeDialog()
    }
  },
  methods: {
    // 创建会议房间
    async createRoom() {
      await this.liveClient.createRoom()
    },
    // 加入会议房间
    async joinMeeting() {
      let joinRoomParams = {
        roomNum: this.roomNum,
        cameraStatus: true,
        microphoneStatus: true,
        echoCancellation: true,
        noiseSuppression: true
      }
      await this.liveClient.joinRoom(joinRoomParams)
    },
    // 修改本地与会者状态
    async changeLocalMicroStatus() {
      await this.liveClient.changeMicrophoneStatus()
    },
    async changeLocalCameraStatus() {
      await this.liveClient.changeCameraStatus()
    },
    async mutePaticipant() {
      if (this.participantLength <= 0) {
        return
      }
      let index = this.participants.findIndex(item => {
        return item.isLocal === false
      })
      if (index >= 0) {
        await this.liveClient.changeParticipantMicrophoneStatus(
          this.participants[index].identity,
          true
        )
      }
    },
    async unMutePaticipant() {
      if (this.participantLength <= 0) {
        return
      }
      let index = this.participants.findIndex(item => {
        return item.isLocal === false
      })
      if (index >= 0) {
        await this.liveClient.changeParticipantMicrophoneStatus(
          this.participants[index].identity,
          false
        )
      }
    },
    // 保存liveClient监听的事件到数组中，方便离开页面时解除绑定
    addEventToList(eventName) {
      if (this.pageEventList.indexOf(eventName) < 0) {
        this.pageEventList.push(eventName)
      }
    },
    // 向被邀请人发送短信
    sendInviteMessage() {
      let params = {
        toUser: this.invitedMobile,
        message: ``
      }
      let params2 = {
        query: encodeURI(
          `roomNum=${this.roomNum}&tenantId=1478649882954985474&identity=${this.invitedMobile}`
        )
      }
      let header = {
        signature: util.encryptWxLink(
          `roomNum=${this.roomNum}&tenantId=1478649882954985474&identity=${this.invitedMobile}`
        )
      }
      generateWxLink(params2, header).then(res => {
        console.log('链接地址', res)
        if (res.code == 0) {
          params.message = res.data['url_link']
          sendInviteMessage(params).then(res => {
            this.$messageNew.success({
              message: '短信发送成功'
            })
          })
        } else {
          this.$messageNew.error({
            message: '生成小程序链接失败'
          })
        }
      })
    },
    // 初始化组件内client事件监听
    initLiveClientEvent() {
      if (!this.liveClient) {
        return
      }
      this.liveClient.on('errorPush', e => {
        this.addEventToList('errorPush')
        console.log('出错位置' + e.type + ':', e.message)
      })
      this.liveClient.on('roomCreatedSuccess', async e => {
        this.addEventToList('roomCreatedSuccess')
        this.roomNum = e
        console.log('房间创建成功', this.roomNum)
        await this.joinMeeting()
        // 发送短信邀请
      })
      this.liveClient.on('roomCreatedFail', e => {
        this.addEventToList('roomCreatedFail')
        // 房间创建失败
        console.log('房间创建失败', e)
      })
      this.liveClient.on('roomConnected', async () => {
        this.addEventToList('roomConnected')
        console.log('加入会话成功')
        let [org, roomName, roomHost] = this.liveClient.room.metadata.split('-')
        this.roomName = roomName
        // 向被邀请人发送邀请
        if (this.inviteWay === 'mobile') {
          this.sendInviteMessage()
        }
        // 显示邀请中
        this.isMaskShow = true
        // 会议计时开始
        meetingInterval = setInterval(() => {
          this.duration++
        }, 1000)
        this.isInstantMessageShow = true
        try {
          console.log(
            '修改用户名称',
            this.liveClient.room?.localParticipant.identity,
            this.username
          )
          await this.updateParticipantName([
            this.username,
            this.liveClient.room?.localParticipant.identity
          ])
        } catch (e) {
          console.error('修改用户名称失败', e)
        }
      })
      this.liveClient.on('roomDisconnected', e => {
        this.addEventToList('roomDisconnected')
        console.log('房间链接已关闭', e)
        this.isInstantMessageShow = false
        this.closeDialog()
        // 结束会议
        this.$emit('input', false)
      })
      this.liveClient.on('sendTextFailed', e => {
        this.addEventToList('sendTextFailed')
        // this.$message({
        //   message: "即时消息发送失败" + e,
        //   type: "error",
        // });
      })
      this.liveClient.on('sendTextSuccess', e => {
        this.addEventToList('sendTextSuccess')
        this.$nextTick(() => {
          this.$refs['chatComponent'].clearInput()
        })
      })
      this.liveClient.on('messageReceived', e => {
        this.addEventToList('messageReceived')
        this.messageList.push(e)
        setTimeout(() => {
          this.messageList = []
        }, 5000);
      })
      this.liveClient.on('localCameraChange', e => {
        this.addEventToList('localCameraChange')
        let message = ''
        this.localCameraShow = e
        if (e) {
          message = '摄像头已打开'
        } else {
          message = '摄像头已关闭'
        }
        this.$messageNew.success({
          message: message
        })
      })
      this.liveClient.on('localMicrophoneChange', e => {
        this.addEventToList('localMicrophoneChange')
        let message = ''
        this.localMicrophoneShow = e
        if (e) {
          message = '麦克风已打开'
        } else {
          message = '麦克风已关闭'
        }
        this.$messageNew.success({
          message: message
        })
      })
      this.liveClient.on('videoCallRender', e => {
        this.addEventToList('videoCallRender')
        if (this.liveClient) {
          this.localCameraShow = this.liveClient.isCameraEnable
          this.localMicrophoneShow = this.liveClient.isMicrophoneEnable
        }
        this.renderVideoItem(e)
      })
    },
    async updateParticipantName(arg) {
      // console.log('修改与会者名称', arg);
      let [name, identity] = arg
      await this.liveClient.updateParticipantName(identity, name)
    },
    renderVideoItem(videoItem) {
      console.log('视频与会者通话渲染, 用户identity：' + videoItem.identity, videoItem)
      // 验证用户姓名, 若用户姓名为'Phone 0'+手机号，则去掉前面的0
      let reg = /^(Phone 0)+?/
      videoItem.name = videoItem.name.replace(reg, '')
      // 会议室dom
      const container = document.getElementById('room-contain')
      if (!container) {
        return
      }
      let div = document.getElementById(`participant-${videoItem.identity}`)
      if (!div && !videoItem.remove) {
        if (this.participants.length >= 2) {
          return
        } else {
          // 更新与会者数组
          if (this.participants.length > 0) {
            let partIndex = this.participants.findIndex(item => {
              return item.identity === videoItem.identity
            })
            if (partIndex < 0) {
              this.participants.push(videoItem)
            } else {
              this.participants.splice(partIndex, 1, videoItem)
            }
          } else {
            this.participants.push(videoItem)
          }
          // 当会议中用户为2时，去除等待接通蒙层
          if (this.participants.length >= 2) {
            this.isMaskShow = false
          }
          // 成员渲染到页面
          div = document.createElement('div')
          div.id = `participant-${videoItem.identity}`
          div.className = videoItem.isLocal ? 'participant local' : 'participant'
          if (videoItem.isCameraEnabled || videoItem.isScreenShareEnabled) {
            div.innerHTML = `
                <video id="video-${videoItem.identity}" class="p-video" autoplay></video>
                <div id="describe-${videoItem.identity}" class="describe">
                  <div id="microphone-${videoItem.identity}" class="microphone"></div>
                  <div id="${videoItem.identity}" class="identity">${
              videoItem.isLocal ? videoItem.name + '(我)' : videoItem.name
            }</div>
                </div>
              `
          } else {
            div.innerHTML = `
              <video id="video-${videoItem.identity}" class="p-video" autoplay></video>
              <div id="board-${videoItem.identity}" class="board">
                <div class="board-img"></div>
                <div id="describe-${videoItem.identity}" class="describe">
                    <div id="microphone-${videoItem.identity}" class="microphone"></div>
                    <div id="${videoItem.identity}" class="identity">${
              videoItem.isLocal ? videoItem.name + '(我)' : videoItem.name
            }</div>
                </div>
              </div>
              `
          }
          let layoutParticipantEle = document.querySelector('#room-contain .layout-participant')
          let layoutLocalEle = document.querySelector('#room-contain .layout-local')
          if (div.className.includes('local')) {
            if (layoutLocalEle) {
              layoutLocalEle.appendChild(div)
            } else {
              layoutLocalEle = document.createElement('div')
              layoutLocalEle.className = 'layout-local'
              layoutLocalEle.appendChild(div)
              container.insertBefore(layoutLocalEle, container.firstChild)
            }
          } else {
            if (layoutParticipantEle) {
              layoutParticipantEle.appendChild(div)
            } else {
              layoutParticipantEle = document.createElement('div')
              layoutParticipantEle.className = 'layout-participant'
              layoutParticipantEle.appendChild(div)
              container.appendChild(layoutParticipantEle)
            }
          }
        }
      }
      // 摄像头关闭状态下展板dom元素
      let boardEle = document.getElementById(`board-${videoItem.identity}`)
      // 视频元素
      let videoElm = document.getElementById(`video-${videoItem.identity}`)
      // 静音用户
      const unableMicrophone = () => {
        this.liveClient.changeParticipantMicrophoneStatus(videoItem.identity, true)
      }
      // 解除静音用户
      const enableMicrophone = () => {
        this.liveClient.changeParticipantMicrophoneStatus(videoItem.identity, false)
      }
      // 设置指定用户左侧放大显示
      const setBlurVideoLeft = () => {
        let layoutLocalEle = document.querySelector('#room-contain .layout-local')
        let layoutParticipantEle = document.querySelector('#room-contain .layout-participant')
        let localChild = layoutLocalEle.firstChild
        let currentEle = document.getElementById(`participant-${videoItem.identity}`)
        if (localChild) {
          if (localChild.id === `participant-${videoItem.identity}`) {
            return
          } else {
            layoutParticipantEle.insertBefore(localChild, layoutParticipantEle.firstChild)
            layoutLocalEle.appendChild(currentEle)
          }
        } else {
          layoutLocalEle.appendChild(currentEle)
        }
      }
      // 删除与会者
      if (videoItem.remove) {
        if (videoElm) {
          videoElm.srcObject = null
          videoElm.src = ''
          videoElm?.remove()
        }
        if (div) {
          div?.remove()
        }
        if (this.participants.length > 0) {
          let index = this.participants.findIndex(item => {
            return item.identity === videoItem.identity
          })
          if (index > -1) {
            this.participants.splice(index, 1)
          }
        }
        return
      }
      // 以下为与会者状态变更渲染代码
      if (div) {
        if (videoItem.isCameraEnabled || videoItem.isScreenShareEnabled) {
          if (boardEle) {
            div.removeChild(boardEle)
            boardEle = null
          }
          let describeElm = document.getElementById(`describe-${videoItem.identity}`)
          if (!describeElm) {
            describeElm = document.createElement('div')
            describeElm.id = 'describe-' + videoItem.identity
            describeElm.className = 'describe'
            describeElm.innerHTML = `
              <div id="microphone-${videoItem.identity}" class="microphone"></div>
              <div id="${videoItem.identity}" class="identity">${
              videoItem.isLocal ? videoItem.name + '(我)' : videoItem.name
            }</div>
            `
            div.appendChild(describeElm)
          }
        } else {
          if (!boardEle) {
            let describeElm = document.getElementById(`describe-${videoItem.identity}`)
            if (describeElm) {
              describeElm.remove()
            }
            let boardEle = document.createElement('div')
            boardEle.id = `board-${videoItem.identity}`
            boardEle.className = 'board'
            boardEle.innerHTML = `
                <div class="board-img"></div>
                <div class="describe">
                  <div id="microphone-${videoItem.identity}" class="microphone"></div>
                  <div id="${videoItem.identity}" class="identity">${
              videoItem.isLocal ? videoItem.name + '(我)' : videoItem.name
            }</div>
                </div>
              `
            div.appendChild(boardEle)
          }
        }
        // 麦克风图标dom
        let microphoneElm = document.getElementById(`microphone-${videoItem.identity}`)
        div.onclick = setBlurVideoLeft
        if (!videoItem.isLocal) {
          this.isParticipantMicrophoneEnable = videoItem.isMicrophoneEnabled
        }
        if (microphoneElm && videoItem.isMicrophoneEnabled) {
          microphoneElm.className = 'microphone microphone-active'
          microphoneElm.onclick = unableMicrophone
        } else if (microphoneElm && !videoItem.isMicrophoneEnabled) {
          microphoneElm.className = 'microphone microphone-inactive'
          microphoneElm.onclick = enableMicrophone
        }
        // 视频轨道绑定
        if (videoItem.videoTrack) {
          videoItem.videoTrack.attach(videoElm)
          if (
            this.trackList.findIndex(item => {
              return item.sid === videoItem.videoTrack.sid
            }) < 0
          ) {
            this.trackList.push(videoItem.videoTrack)
          }
        }
        // 音频轨道绑定,消除回音
        if (!videoItem.isLocal) {
          if (videoItem.audioTrack) {
            videoItem.audioTrack.attach(videoElm)
            if (
              this.trackList.findIndex(item => {
                return item.sid === videoItem.audioTrack.sid
              }) < 0
            ) {
              this.trackList.push(videoItem.audioTrack)
            }
          }
        }
        // 更新与会者名称
        let nameDom = document.getElementById(videoItem.identity)
        if (nameDom) {
          nameDom.innerHTML = videoItem.isLocal ? videoItem.name + '(我)' : videoItem.name
        }
        // 更新与会者数组
        if (this.participants.length > 0) {
          let index = this.participants.findIndex(item => {
            return item.identity === videoItem.identity
          })
          if (index > -1) {
            this.participants.splice(index, 1, videoItem)
          }
        }
      }
    },
    // 清除组件中的定时器
    clearAllInterval() {
      if (meetingInterval) {
        clearInterval(meetingInterval)
        meetingInterval = null
      }
    },
    // 清除组件中绑定的事件监听
    dispatchLiveClientEvent() {
      console.log('当前组件中绑定的事件监听', this.pageEventList)
      if (this.liveClient) {
        if (this.pageEventList.length > 0) {
          this.pageEventList.forEach(eventName => {
            console.log('解除绑定事件:', eventName)
            this.liveClient.off(eventName)
          })
        }
      }
    },
    // 清除组件中绑定的音视频轨道
    clearTrack() {
      if (this.trackList.length > 0) {
        this.trackList.forEach(track => {
          track.detach()
          track.stop()
        })
      }
    },
    // 退出会议
    leaveRoom() {
      if (this.liveClient) {
        this.liveClient.leaveRoom()
        this.closeDialog()
      }
    },
    closeDialog() {
      this.clearAllInterval()
      this.clearTrack()
      this.dispatchLiveClientEvent()
    },
    // 用户点击挂断
    hangOff() {
      this.leaveRoom()
    },
    sendMessage(e) {
      this.liveClient.sendMessage(e, this.username);
    },
  }
}
</script>

<style lang="scss" scoped>
.point-dialog {
  width: 900px;
  height: 573px;
  position: absolute;
  z-index: 3000;
  left: 510px;
  top: 206px;
  // transform: translate(-50%, -50%);
  background: url('~@/assets/service/point-dialog-normal.png') no-repeat center / 100% 100%;

  .point-head {
    height: 68px;
    padding: 0 48px 0 38px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    &-left {
      display: flex;
      align-items: center;
      .invited {
        font-family: YouSheBiaoTiHei;
        font-size: 28px;
        color: #ffffff;
        line-height: 36px;
        margin-right: 40px;
      }
      .call-duration {
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #cbe5ff;
        line-height: 21px;
      }
    }
    .full-btn {
      width: 32px;
      height: 32px;
      cursor: pointer;
      background: url('~@/assets/service/full1.png') no-repeat center / 100% 100%;
    }
  }

  .point-contain {
    width: 100%;
    height: calc(100% - 68px);
    padding: 12px;
    position: relative;
    overflow: hidden;
    &-mask {
      width: calc(100% - 24px);
      height: calc(100% - 24px);
      position: absolute;
      left: 12px;
      top: 12px;
      background: rgb(0, 16, 33, 0.5);
      z-index: 2500;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #ffffff;
    }
    .point-foot {
      width: calc(100% - 24px);
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 40px;
      position: absolute;
      left: 12px;
      bottom: 12px;
      z-index: 3000;
      background: url('~@/assets/service/foot-mini.png') no-repeat center / 100% 100%;
      &-group {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        .foot-btn {
          display: flex;
          align-items: center;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 14px;
          color: #ffffff;
          cursor: pointer;
          &-icon {
            width: 28px;
            height: 28px;
          }
          &:not(:last-child) {
            margin-right: 65px;
          }
          .mic-on {
            background: url('~@/assets/service/mic-on.png') no-repeat center / 100% 100%;
          }
          .mic-off {
            background: url('~@/assets/service/mic-off.png') no-repeat center / 100% 100%;
          }
          .cam-on {
            background: url('~@/assets/service/cam-on.png') no-repeat center / 100% 100%;
          }
          .cam-off {
            background: url('~@/assets/service/cam-off.png') no-repeat center / 100% 100%;
          }
          .other-on {
            background: url('~@/assets/service/other-on.png') no-repeat center / 100% 100%;
          }
          .other-off {
            background: url('~@/assets/service/other-off.png') no-repeat center / 100% 100%;
          }
          .hang-off {
            background: url('~@/assets/service/hang-off.png') no-repeat center / 100% 100%;
          }
        }
      }
    }
  }

  &-full {
    width: 100% !important;
    height: 100% !important;
    left: 0px !important;
    top: 0px !important;
    background: url('~@/assets/service/large.png') no-repeat center / 100% 100% !important;
    .point-foot {
      background: url('~@/assets/service/foot-large.png') no-repeat center / 100% 100% !important;
    }
    .point-head {
      .full-btn {
        background: url('~@/assets/service/full2.png') no-repeat center / 100% 100%;
      }
    }
  }
}
</style>
<style lang="scss">
@import './style/pointMeetingStyle.scss';
</style>
