import {
  getQgdCodejjcd,
  getQgdCodesfgk,
  getQgdCodesqlx,
  getQgdCodesjly
} from '@/api/hs/hs.api.js'

export const myMixins = {
  data () {
    return {
      qgdCodejjcdOptions: [],
      qgdCodesfgkOptions: [],
      qgdCodesqlxOptions: [],
      qgdCodesjlyOptions: []
    }
  },
  created () {
    // this.getQgdCodejjcd() // 紧急程度数据字典
    // this.getQgdCodesfgk() // 是否公开数据字典
    // this.getQgdCodesqlx() // 诉求类型数据字典
    // this.getQgdCodesjly() // 事件来源数据字典
  },
  methods: {
    async getQgdCodejjcd () {
      let res = await getQgdCodejjcd()
      this.qgdCodejjcdOptions = res.result
    },
    async getQgdCodesfgk () {
      let res = await getQgdCodesfgk()
      this.qgdCodesfgkOptions = res.result
    },
    async getQgdCodesqlx () {
      let res = await getQgdCodesqlx()
      this.qgdCodesqlxOptions = res.result
    },
    async getQgdCodesjly () {
      let res = await getQgdCodesjly()
      this.qgdCodesjlyOptions = res.result
    },
  }
}
