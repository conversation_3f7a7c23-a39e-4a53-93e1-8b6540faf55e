<template>
  <div class="chart-icon">
    <el-popover placement="bottom" width="200" trigger="hover" popper-class="custom-popover">
      <div class="emotion-list">
        <a
          href="javascript:void(0);"
          @click="getEmo(index)"
          v-for="(item, index) in faceList"
          :key="index"
          class="emotion-item"
          >{{ item }}</a
        >
      </div>
      <img class="chart-icon-btn" slot="reference" src="@/assets/bjnj/emoji.png" alt="表情" />
    </el-popover>
  </div>
</template>

<script>
import emojiData from "@/assets/json/emoji.json";
  export default {
    name: 'chooseEmoji',
    data() {
      return {
        faceList: []
      }
    },
    mounted() {
      emojiData.forEach(emo => {
        this.faceList.push(emo.char)
      })
    },
    methods: {
      getEmo(index) {
        this.$emit('setEmo', this.faceList[index])
      }
    }
  }
</script>

<style lang="scss" scoped>
  .chart-icon {
    width: 16px;
    height: 16px;
    position: absolute;
    left: 20px;
    top: 12px;
    cursor: pointer;
    &-btn {
      width: 100%;
      height: 100%;
    }
    .emotion-list {
      display: flex;
      flex-wrap: wrap;
      padding: 10px;
      .emotion-item {
        width: 20px;
        font-size: 18px;
        text-align: center;
        line-height: 20px;
      }
    }
  }
</style>