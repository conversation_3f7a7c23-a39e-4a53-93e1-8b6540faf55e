<template>
	<div class="qxyj-detail-dialog" v-if="show">
		<div class="event">
			<div class="title">
				<span>预警详情</span>
				<i class="el-icon-close close_btn" @click="closeFn"></i>
			</div>
			<div class="content">
				<div class="contentTitle">{{ list[6] }}</div>
				<div class="contentTable">
					<div class="contentItem">
						<div class="contentLeft">预警类型：</div>
						<div class="contentRight">{{ list[2] }}预警</div>
					</div>
					<div class="contentItem">
						<div class="contentLeft">预警时间：</div>
						<div class="contentRight">{{ list[1] }}</div>
					</div>
					<div class="contentItem">
						<div class="contentLeft">预警区域：</div>
						<div class="contentRight">{{ list[3] }}</div>
					</div>
					<div class="contentItem">
						<div class="contentLeft">预警等级：</div>
						<div class="contentRight">{{ list[4] }}预警</div>
					</div>
				</div>
				<div class="contentCont">{{ list[5] }}</div>
			</div>
			<!-- <div class="btns">
      <div class="btn" v-for="(item,index) in btns" :key="index" @click="handle(index)">{{item}}</div>
    </div> -->
		</div>
	</div>
</template>

<script>
export default {
	name: 'EventDialog',
	props: {
		value: {
			type: Boolean,
			default: false,
		},
		list: {
			type: Array,
			default: () => {
				return []
			},
		},
		btns: {
			type: Array,
			default: () => ['取消', '确定'],
		},
	},
	computed: {
		show() {
			return this.value
		},
	},
	methods: {
		closeFn() {
			this.$emit('input', false)
		},
		handle(i) {
			this.$emit('handle', i)
		},
	},
}
</script>

<style lang="less" scoped>
.qxyj-detail-dialog {
	width: 1920px;
	height: 1080px;
	position: absolute;
	left: 0;
	top: 0;
	background-color: rgba(0, 0, 0, 0.3);
	z-index: 1105;
	display: flex;
	justify-content: center;
	align-items: center;
}

.event {
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	z-index: 1111;
	width: 683px;
	// height: 262px;
	margin: 0 auto;
	// background: url('~@/assets/map/dialog/bg9.png') no-repeat center / 100% 100%;
	background: #001c40;

	border: 1px solid #023164;

	box-sizing: border-box;

	.title {
		width: 100%;
		height: 48px;
		line-height: 48px;
		// background: url(~@/assets/map/dialog/title7.png) no-repeat;

		padding: 0 16px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background: #104993;
		span {
			font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
			font-weight: normal;
			font-size: 16px;
			color: #d0deee;
			line-height: 16px;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}
		.close_btn {
			font-size: 14px;
			color: #80caff;
			cursor: pointer;
		}
	}
	.content {
		padding: 29px 50px 58px 46px;
		.contentTitle {
			font-family: PingFangSC, PingFang SC;
			font-weight: 600;
			font-size: 20px;
			color: #ffffff;
			line-height: 28px;
			text-align: center;
			font-style: normal;
			background: linear-gradient(180deg, #ffffff 0%, #00d4ff 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}
		.contentTable {
			width: 400px;
			margin: 0 auto;
			margin-top: 13px;
			display: flex;
			flex-wrap: wrap;
			justify-content: space-evenly;
			align-content: space-between;
			.contentItem {
				width: 50%;
				margin-bottom: 7px;
				.contentLeft {
					float: left;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 12px;
					color: #ffffff;
					line-height: 17px;
					text-align: center;
					font-style: normal;
				}
				.contentRight {
					float: left;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 12px;
					color: #ffffff;
					line-height: 17px;
					text-align: center;
					font-style: normal;
				}
			}
		}
		.contentCont {
			margin-top: 16px;
			font-family: PingFangSC, PingFang SC;
			font-weight: 300;
			font-size: 14px;
			color: #ffffff;
			line-height: 26px;
			text-align: left;
			font-style: normal;
		}
	}
}
</style>
