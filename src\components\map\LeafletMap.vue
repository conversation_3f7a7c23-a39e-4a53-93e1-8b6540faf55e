<!-- <template>
  <div :style="mapStyle">
    <l-map :zoom="zoom" :center="center">
      <l-tile-layer :url="url" :attribution="attribution"></l-tile-layer>
      <l-marker
        v-for="marker in eventMarkers"
        :key="marker.id"
        :lat-lng="marker.latlng"
        :icon="eventIcon"
        @click="onMarkerClick(0, marker)"
      >
      </l-marker>
    </l-map>
  </div>
</template>

<script>
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'
import { LMap, LTileLayer, LMarker, LPopup } from 'vue2-leaflet'
import 'leaflet-providers/leaflet-providers.js'

const mapStyle = {
  height: '100%',
  width: '100%'
}

export default {
  name: 'LeafletMap',
  components: {
    LMap,
    LTileLayer,
    LMarker,
    LPopup
  },
  data() {
    return {
      zoom: 12,
      center: [31.97, 118.78],
      url:
        'http://t1.tianditu.com/vec_c/wmts?layer=vec&style=default&tilematrixset=c&Service=WMTS&Request=GetTile&Version=1.0.0&Format=tiles&TileMatrix={z}&TileCol={x}&TileRow={y}&tk=',
      attribution: '&copy; OpenStreetMap contributors',
      eventIcon: new L.Icon({
        iconUrl: require('@/assets/map/point/point1.png'),
        iconSize: [34, 84],
        iconAnchor: [17, 84]
      }),
      eventMarkers: []
    }
  },
  computed: {
    mapStyle() {
      return {
        ...mapStyle
      }
    }
  },
  methods: {
    showEvents() {
      // 清除之前的点位
      this.clearMarkers()
      // 添加事件点位
      this.eventMarkers = [
        {
          id: 1,
          latlng: [31.97 + Math.random() * 0.1, 118.78 + Math.random() * 0.1] // 随机生成经纬度坐标
        },
        {
          id: 2,
          latlng: [31.97 + Math.random() * 0.1, 118.78 + Math.random() * 0.1] // 随机生成经纬度坐标
        }
      ]
    },
    onMarkerClick(i, marker) {
      this.$emit('showDialog', i, marker.id)
    },
    clearMarkers(i) {
      // 清空点位数组
      if (i === 0) {
        this.eventMarkers = []
      }
    }
  }
}
</script>

<style lang="less" scoped>
::v-deep .leaflet-zoom-animated img {
  -webkit-filter: sepia(100%) invert(90%) !important;
  -ms-filter: sepia(100%) invert(90%) !important;
  -moz-filter: sepia(100%) invert(90%) !important;
  filter: sepia(100%) invert(90%) !important;
}
</style> -->
