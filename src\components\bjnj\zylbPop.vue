<template>
	<div class="zyqd-dialog" v-if="show">
		<div class="ai_waring">
			<div class="title">
				<span>资源清单</span>
				<i class="el-icon-close close_btn" @click="closeEmitai"></i>
			</div>
			<div class="content">
				<!-- <div class="list" v-for="(item, index) of list" :key="index">
          <div class="list1">
            <div class="list1Item1">{{item.title}}</div>
            <div class="list1Item2">
              <div class="list1Item3">联系人: {{item.name}}</div>
              <div class="list1Item4">联系方式: {{item.phone}}</div>
            </div>
          </div> -->
				<div class="list2">
					<SwiperTableMap
						:titles="['农机编码', '农机品目', '农机型号', '车牌号', '就位时间', '作业面积']"
						:widths="['15%', '15%', '15%', '15%', '20%', '20%']"
						:data="list"
						:contentHeight="'144px'"
						:settled="settled"
						@operate="operate"
					></SwiperTableMap>
				</div>
				<!-- </div> -->
			</div>
		</div>
	</div>
</template>

<script>
import SwiperTableMap from './table/RealTimeEventTable2.vue'
// api
import { getEventType, getUnDoneEventList, getDoneEventList, getQgdSssj } from '@/api/hs/hs.api.js'
import { myMixins } from '@/libs/myMixins.js'

export default {
	name: 'RealTimeEventDialogQgd',
	mixins: [myMixins],
	components: { SwiperTableMap },
	props: {
		value: {
			type: Boolean,
			default: false,
		},
		list: {
			type: Array,
			default: () => {
				return [
					{
						title: '周村应急救灾中心',
						name: '王建国',
						phone: '13808932467',
						lists: [
							['1', '234356', '履带式收割机', 'LD-1224', '苏A90233'],
							['2', '234356', '履带式收割机', 'LD-1224', '苏A90233'],
							['3', '234356', '履带式收割机', 'LD-1224', '苏A90233'],
							['4', '234356', '履带式收割机', 'LD-1224', '苏A90233'],
							['5', '234356', '履带式收割机', 'LD-1224', '苏A90233'],
						],
					},
					{
						title: '周村应急救灾中心',
						name: '王建国',
						phone: '13808932467',
						lists: [
							['1', '234356', '履带式收割机', 'LD-1224', '苏A90233'],
							['2', '234356', '履带式收割机', 'LD-1224', '苏A90233'],
							['3', '234356', '履带式收割机', 'LD-1224', '苏A90233'],
							['4', '234356', '履带式收割机', 'LD-1224', '苏A90233'],
							['5', '234356', '履带式收割机', 'LD-1224', '苏A90233'],
						],
					},
				]
			},
		},
	},
	data() {
		return {
			tabActive: 0,
			tabs: ['未办结', '办结'],
			btnsArr: [
				{
					name: '未办结',
					normalBg: require('@/assets/shzl/map/left_n.png'),
					activeBg: require('@/assets/shzl/map/left_a.png'),
				},
				{
					name: '已办结',
					normalBg: require('@/assets/shzl/map/right_n.png'),
					activeBg: require('@/assets/shzl/map/right_a.png'),
				},
			],
			urgentOptions: [
				{
					value: '0',
					label: '普通',
				},
				{
					value: '1',
					label: '突发',
				},
				{
					value: '2',
					label: '重要',
				},
				{
					value: '3',
					label: '紧急',
				},
			],
			eventTypeOptions: [
				{
					value: '0',
					label: '普通',
				},
			],
			timeOutOptions: [
				{
					value: '0',
					label: '是',
				},
				{
					value: '1',
					label: '否',
				},
			],
			proityValue: '',
			timeOutValue: '',
			settled: true,
			total: 0,
			pageNum: 1,
			pageSize: 10,
			finished: 1,
			tableList: [
				['1', '234356', '履带式收割机', 'LD-1224', '苏A90233'],
				['2', '234356', '履带式收割机', 'LD-1224', '苏A90233'],
				['3', '234356', '履带式收割机', 'LD-1224', '苏A90233'],
				['4', '234356', '履带式收割机', 'LD-1224', '苏A90233'],
				['5', '234356', '履带式收割机', 'LD-1224', '苏A90233'],
			],
			gdObj: {
				jjcdValue: '',
				sqlxValue: '',
			},
		}
	},
	watch: {
		qgdCodejjcdOptions(val) {
			if (this.qgdCodejjcdOptions.length > 0) {
				// this.getQgdSssj()
			}
		},
	},
	computed: {
		show() {
			return this.value
		},
	},
	methods: {
		operate(i, id, it) {
			this.$emit('operate', i, id, it)
		},
		closeEmitai() {
			this.$emit('input', false)
		},
		pageNumChange(val) {
			this.pageNum = val
			this.getQgdSssj()
		},
		async getQgdSssj() {
			this.tableList = []
			let res = await getQgdSssj({
				// street: '1649962979288023040',
				page: this.pageNum,
				size: this.pageSize,
				emeLevel: this.gdObj.jjcdValue,
				appealType: this.gdObj.sqlxValue,
			})
			console.log('实时事件弹窗', res)
			console.log(this.qgdCodejjcdOptions)
			if (res?.code == '200' && res.result.data.length > 0) {
				console.log('res.result.data', res.result.data)
				this.tableList = res.result.data.map((it, i) => [
					this.pageSize * (this.pageNum - 1) + i + 1,
					it.orderNum,
					it.emeLevel,
					it.appealContent,
					it.appealType,
					it.community,
					it.eventDate,
					it.formStatus,
					it.eventLocation,
					it.id,
					it,
				])
				this.total = res.result.recordsTotal
			}
			// console.log('this.tableList', this.tableList)
		},
		searchBtn() {
			this.getQgdSssj()
		},
		resetBtn() {
			this.gdObj = {}
			this.getQgdSssj()
		},
	},
	mounted() {},
}
</script>

<style lang="less" scoped>
.bg-header {
	position: absolute;
	width: 100%;
	height: 1080px;
	background: rgba(2, 14, 50, 0.75);
	z-index: 999999;
	top: 0;
	left: 0;
}
/deep/ .ivu-select {
	width: 168px;
}
/deep/ .ivu-select-selection {
	width: 168px;
	height: 34px;
	background: rgba(0, 74, 143, 0.4) linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
	border: 1px solid rgba(0, 162, 255, 0.6);
}
/deep/ .ivu-select-selection .ivu-select-selected-value {
	font-size: 13px;
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	color: #ccf4ff;
	line-height: 34px;
}

/deep/ .ivu-input {
	background: rgba(0, 74, 143, 0.4) linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
	border: 1px solid rgba(0, 162, 255, 0.6);
	font-size: 16px;
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	color: #ccf4ff;
	line-height: 22px;
}
/deep/ .ivu-select-placeholder {
	font-size: 13px !important;
	line-height: 34px !important;
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	color: #ccf4ff !important;
}
/deep/ .ivu-page-item {
	background: transparent;
	color: #fff;
}
/deep/ .ivu-page-item a {
	color: #fff;
}
/deep/ .ivu-page-prev,
/deep/ .ivu-page-next {
	background: transparent;
}

.zyqd-dialog {
	width: 1920px;
	height: 1080px;
	position: absolute;
	left: 0;
	top: 0;
	background-color: rgba(0, 0, 0, 0.3);
	z-index: 1003;
	display: flex;
	justify-content: center;
	align-items: center;
}
.ai_waring {
	width: 652px;
	height: 652px;
	background: #001c40;

	border: 1px solid #023164;
	z-index: 1100;
	.title {
		width: 100%;
		height: 48px;
		line-height: 48px;
		// background: url(~@/assets/map/dialog/title7.png) no-repeat;

		padding: 0 16px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background: #104993;
		span {
			font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
			font-weight: normal;
			font-size: 16px;
			color: #d0deee;
			line-height: 16px;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}
		.close_btn {
			font-size: 14px;
			color: #80caff;
			cursor: pointer;
		}
	}

	.content {
		padding: 26px 16px 24px 16px;
		.list {
			margin-bottom: 26px;
			.list1 {
				width: 661px;
				height: 74px;
				background: url(~@/assets/map/dialog/title6.png) no-repeat center / 100% 100%;
				padding: 12px 0 0 31px;
				.list1Item1 {
					font-family: PingFangSC, PingFang SC;
					font-size: 20px;
					color: #ffffff;
					line-height: 26px;
					text-align: left;
					font-style: normal;
				}
				.list1Item2 {
					font-family: SourceHanSansCN, SourceHanSansCN;
					font-weight: 400;
					font-size: 14px;
					color: #adddff;
					line-height: 21px;
					text-align: left;
					font-style: normal;
					margin-top: 3px;
					.list1Item3 {
						float: left;
						margin-right: 36px;
					}
					.list1Item4 {
						float: left;
					}
				}
			}
		}
		.list2 {
			width: 100%;
			height: 550px;
			margin: 0 auto;
		}
	}
}
// /deep/ .ivu-page-total {
//   font-size: 18px;
//   color: #fff;
// }
</style>
