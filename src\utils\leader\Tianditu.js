/*
 * @Author: kaix
 * @Date: 2021-04-06 16:37:02
 * @LastEditTime: 2021-04-06 17:09:34
 * @LastEditors: kaix
 * @Description:
 */
/* eslint-disable */
/**
 * @requires window.SuperMap/Layer/CanvasLayer.js
 * @requires window.SuperMap/Layer/Grid.js
 * @requires window.SuperMap/Tile/Image.js
 */

/**
 * Class: window.SuperMap.Layer.Tianditu
 * 天地图服务图层类。
 *     用于显示天地图的地图服务，使用<window.SuperMap.Layer.Tianditu>的
 *     构造函数可以创建天地图图层，更多信息查看：
 *
 * Inherits from:
 *  - <window.SuperMap.Layer.CanvasLayer>
 */
window.SuperMap.Layer.Tianditu = window.SuperMap.Class(window.SuperMap.CanvasLayer, {
  /**
   * APIProperty: layerType
   * {String} 图层类型.(vec:矢量图层，img:影像图层，ter:地形图层)
   */
  layerType: 'vec', //(vec:矢量图层，cva:矢量标签图层，img:影像图层,cia:影像标签图层，ter:地形,cta:地形标签图层)

  /**
   * APIProperty: isLabel
   * {Boolean} 是否是标签图层.
   */
  isLabel: false,

  /**
   * Property: attribution
   * {String} The layer attribution.
   */
  attribution:
    "Data by <a style='white-space: nowrap' target='_blank' href='http://www.tianditu.com'>Tianditu</a>",

  /**
   * Property: url
   * {String} 图片url.
   */
  url:
    'http://t${num}.tianditu.com/DataServer?T=${type}_${proj}&x=${x}&y=${y}&l=${z}&tk=8177363d1b5e8f65ab85876dd85ca41f',

  //cva_url:"http://t${num}.tianditu.com/DataServer?T=cva_${proj}&x=${x}&y=${y}&l=${z}",

  /**
   * Property: zOffset
   * {Number} 图片url中z值偏移量
   */
  zOffset: 1,

  /**
   * APIProperty: dpi
   * {Float} 屏幕上每英寸包含像素点的个数。
   * 该参数结合图层比例尺可以推算出该比例尺下图层的分辨率.默认为96。
   */
  dpi: 96,

  /**
   * Constructor: window.SuperMap.Layer.Tianditu
   * 创建天地图图层
   *
   * Example:
   * (code)
   * var tiandituLayer = new window.SuperMap.Layer.Tianditu();
   * (end)
   */
  initialize: function(options) {
    var me = this;
    me.name = '天地图';

    //        options = window.SuperMap.Util.extend({
    //            maxExtent: new window.SuperMap.Bounds(
    //                minX, minY, maxX, maxY
    //            ),
    //            tileOrigin:new window.SuperMap.LonLat(minX, maxY),
    //            //maxResolution:maxResolution,
    //            //minResolution:minResolution,
    //            resolutions:resolutions,
    //            units:me.units,
    //            projection:me.projection
    //        }, options);
    window.SuperMap.CanvasLayer.prototype.initialize.apply(me, [me.name, me.url, null, options]);
  },

  /**
   * APIMethod: clone
   * 创建当前图层的副本。
   *
   * Parameters:
   * obj - {Object}
   *
   * Returns:
   * {<window.SuperMap.Layer.Tianditu>}  新的图层。
   */
  clone: function(obj) {
    var me = this;
    if (obj == null) {
      obj = new window.SuperMap.Layer.Tianditu(me.name, me.url, me.params, me.getOptions());
    }

    obj = window.SuperMap.CanvasLayer.prototype.clone.apply(me, [obj]);
    obj._timeoutId = null;

    return obj;
  },

  /**
   * Method: getTileUrl
   * 获取每个tile的图片url
   *
   * Parameters:
   * xyz - {Object}
   */
  getTileUrl: function(xyz) {
    var me = this;

    var proj = this.projection;
    if (proj.getCode) {
      proj = proj.getCode();
    }

    if (proj == 'EPSG:4326') {
      var proj = 'c';
    } else {
      var proj = 'w';
    }

    var x = xyz.x;
    var y = xyz.y;

    var z = xyz.z + me.zOffset;
    var num = Math.abs((xyz.x + xyz.y) % 7);

    var lt = this.layerType;
    if (this.isLabel) {
      if (this.layerType == 'vec') lt = 'cva';
      if (this.layerType == 'img') lt = 'cia';
      if (this.layerType == 'ter') lt = 'cta';
    }

    var url = window.SuperMap.String.format(me.url, {
      num: num,
      x: x,
      y: y,
      z: z,
      proj: proj,
      type: lt
    });
    return url;
  },

  /**
   * Method: setMap
   * Set the map property for the layer. This is done through an accessor
   *     so that subclasses can override this and take special action once
   *     they have their map variable set.
   *
   *     Here we take care to bring over any of the necessary default
   *     properties from the map.
   *
   * Parameters:
   * map - {<window.SuperMap.Map>}
   */
  setMap: function(map) {
    window.SuperMap.CanvasLayer.prototype.setMap.apply(this, [map]);
    var proCode = null;
    var proj = this.projection || map.projection;
    if (proj) {
      if (proj.getCode) {
        proCode = proj.getCode();
      } else {
        proCode = proj;
      }
    }
    this.setTiandituParam(proCode);
  },

  /**
   * Method: setTiandituParam
   * 设置出图相关参数
   *
   * Parameters:
   * projection - {String} 投影坐标系
   */
  setTiandituParam: function(projection) {
    var lt = this.layerType;
    if (lt == 'vec') {
      var resLen = 18;
      var resStart = 0;
      this.zOffset = 1;
      this.numZoomLevels = 18;
    } else if (lt == 'img') {
      var resLen = 17;
      var resStart = 0;
      this.zOffset = 1;
      this.numZoomLevels = 17;
    } else if (lt == 'ter') {
      var resLen = 13;
      var resStart = 0;
      this.zOffset = 1;
      this.numZoomLevels = 13;
    }
    if (projection == 'EPSG:4326') {
      var minX = -180;
      var minY = -90;
      var maxX = 180;
      var maxY = 90;

      //var maxResolution = 156543.0339;
      //var minResolution = 0.5971642833709717;

      var resolutions = [];
      for (var i = resStart; i < resLen; i++) {
        resolutions.push(1.40625 / 2 / Math.pow(2, i));
      }

      this.units = 'degree';
      this.projection = new window.SuperMap.Projection('EPSG:4326');

      this.maxExtent = new window.SuperMap.Bounds(minX, minY, maxX, maxY);
      this.tileOrigin = new window.SuperMap.LonLat(minX, maxY);
      this.resolutions = resolutions;
    } else {
      var minX = -20037508.3392;
      var minY = -20037508.3392;
      var maxX = 20037508.3392;
      var maxY = 20037508.3392;

      //var maxResolution = 156543.0339;
      //var minResolution = 0.5971642833709717;

      var resolutions = [];
      for (var i = resStart; i < resLen; i++) {
        resolutions.push(156543.0339 / 2 / Math.pow(2, i));
      }
      //this.numZoomLevels = 18;

      this.units = 'm';
      this.projection = new window.SuperMap.Projection('EPSG:3857');

      this.maxExtent = new window.SuperMap.Bounds(minX, minY, maxX, maxY);
      this.tileOrigin = new window.SuperMap.LonLat(minX, maxY);
      this.resolutions = resolutions;
    }
  },

  CLASS_NAME: 'window.SuperMap.Layer.Tianditu'
});

export default window.SuperMap.Layer.Tianditu;
