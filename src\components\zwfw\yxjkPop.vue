<template>
  <div>
    <div class="zwfw_pop">
      <div class="title">
        <div class="title_text">运行监控</div>
        <img class="close_btn" src="@/assets/leader/img/zwfw/close.png" @click="close" />
      </div>
      <div class="pop_content">
        <BlockBox title="已认领事项统计" class="content_box" :isListBtns="false" :blockHeight="342">
          <div class="table_content">
            <swiper-table
              :data="sxtjData"
              :titles="[
                '部门',
                '已认领事项',
                '待确认事项',
                '已关联窗口事项',
                '已配置网上办理地址事项',
              ]"
              :widths="['88px', '86px', '119px', '135px', '90px']"
              content-height="220px"
            />
          </div>
        </BlockBox>
        <BlockBox title="窗口视频监控" class="content_box" :isListBtns="false" :blockHeight="342">
          <div class="center_right2">
            <div class="center_right2Item">
              <hlsVideo :src="testUrl1"></hlsVideo>
            </div>
            <div class="center_right2Item">
              <hlsVideo :src="testUrl2"></hlsVideo>
            </div>
            <div class="center_right2Item">
              <hlsVideo :src="testUrl3"></hlsVideo>
            </div>
            <div class="center_right2Item">
              <hlsVideo :src="testUrl4"></hlsVideo>
            </div>
          </div>
        </BlockBox>
        <BlockBox title="办理异常统计" class="content_box" :isListBtns="false" :blockHeight="342">
          <div class="yctj">
            <div v-for="(item, index) in bltjList" :key="index" class="yctj_item">
              <img :src="item.img" alt="" />
              <div class="num_box">
                <div class="num">
                  <countTo
                    ref="countTo"
                    :startVal="$countTo.startVal"
                    :decimals="$countTo.decimals(item.num)"
                    :endVal="item.num"
                    :duration="$countTo.duration"
                  />
                </div>
                <div class="item_text">{{ item.tit }}</div>
              </div>
            </div>
          </div>
        </BlockBox>
        <BlockBox title="即将超期办理事件" class="content_box" :isListBtns="false" :blockHeight="342">
          <div class="table_content">
            <swiper-table
              :data="blsjList"
              :titles="['窗口', '事项名称', '办结剩余时间']"
              :widths="['162px', '200px', '160px']"
              content-height="220px"
            />
          </div>
        </BlockBox>
      </div>
    </div>
      <div>
        <transition name="fade">
        <div
          class="bg-header"
        ></div>
      </transition>
      </div>
  </div>
</template>

<script>
import hlsVideo from '@/components/leader/hlsVideo'
import BlockBox from '@/components/leader/common/blockBox.vue'
import SwiperTable from '@/components/leader/leader_zwjs/SwiperTable.vue'
import 'swiper/css/swiper.css'

export default {
  name: 'ywnlPop',
  components: {
    BlockBox,
    SwiperTable,
    hlsVideo,
  },
  data() {
    return {
      // testUrl1: 'http://**********:8087/pulllive/pulllive_32010199001329000011/hls.m3u8',
      // testUrl2: 'http://**********:8087/pulllive/pulllive_32010199001329000020/hls.m3u8',
      // testUrl3: 'http://**********:8087/pulllive/pulllive_32010600041325000403/hls.m3u8',
      // testUrl4: 'http://**********:8087/pulllive/pulllive_32010600041325000402/hls.m3u8',
      testUrl1: 'http://**********:8088/firstfloor/stream1/hls.m3u8',
      testUrl2: 'http://**********:8088/sixthfloor/stream1_01/hls.m3u8',
      testUrl3: 'http://**********:8088/sixthfloor/stream1_01/hls.m3u8',
      testUrl4: 'http://**********:8088/firstfloor/stream2/hls.m3u8',
      qsfx: {
        data: [
          ['product', '受理数量', '办结数量'],
          ['7月', 150, 100],
          ['8月', 450, 310],
          ['9月', 390, 320],
          ['10月', 500, 350],
          ['11月', 419, 360],
          ['12月', 319, 260],
        ],
        options: {
          // 颜色数据
          color: ['#FAE699', '#00A3D7'],
          // 柱子的宽度
          barWidth: 20,
          // 柱子X轴位置
          symbolOffsetX: ['-25%', '25%', '25%'],
          // 顶部的大小
          symbolSize: [20, 11],
        },
      },
      fltj: {
        data: [
          ['product', ''],
          ['行政许可', 150],
          ['行政征收', 450],
          ['行政确认', 390],
          ['行政奖励', 500],
          ['行政裁决', 419],
          ['行政征用', 319],
          ['行政强制', 250],
          ['行政处罚', 380],
        ],
        options: {
          // 颜色数据
          color: ['#00A3D7'],
          // 柱子的宽度
          barWidth: 20,
          // 柱子X轴位置
          symbolOffsetX: ['', '25%', '25%'],
          // 顶部的大小
          symbolSize: [20, 11],
        },
      },
      sxtjData: [
        ['文化局', '874', '65', '614', '761'],
        ['市场监督局', '579', '28', '501', '449'],
        ['税务局', '858', '21', '789', '581'],
        ['城管局', '761', '39', '711', '452'],
        ['水利局', '382', '19', '763', '123'],
        ['统计局', '289', '16', '873', '273'],
      ],
      blsjList: [
        ['微型餐饮开发区网点', '公司注销登记', '21小时55分钟'],
        ['企业经营一件事', '药品经营许可证变更', '20小时40分钟'],
        ['企业经营一件事', '养老保险', '17小时21分钟'],
        ['微型餐饮开发区网点', '公司注销登记', '16小时46分钟'],
        ['企业经营一件事', '人才补贴', '13小时20分钟'],
        ['微型餐饮开发区网点', '公司注册登记', '23小时41分钟'],
      ],
      bltjList: [
        {
          num: 321,
          img: require('@/assets/leader/img/zwfw/yctj1.png'),
          tit: '临近时限预警',
        },
        {
          num: 415,
          img: require('@/assets/leader/img/zwfw/yctj2.png'),
          tit: '办理超期',
        },
        {
          num: 231,
          img: require('@/assets/leader/img/zwfw/yctj3.png'),
          tit: '评价不满意',
        },
        {
          num: 234,
          img: require('@/assets/leader/img/zwfw/yctj4.png'),
          tit: '不予受理',
        },
        {
          num: 345,
          img: require('@/assets/leader/img/zwfw/yctj5.png'),
          tit: '不予许可',
        },
        {
          num: 345,
          img: require('@/assets/leader/img/zwfw/yctj6.png'),
          tit: '延期申请',
        },
        {
          num: 115,
          img: require('@/assets/leader/img/zwfw/yctj7.png'),
          tit: '异常终止',
        },
      ],
    }
  },
  methods: {
    close() {
      this.$emit('closeEmit')
    },
  },
}
</script>
<style lang="less" scoped>
 .bg-header {
    position: absolute;
    width: 100%;
    height: 1080px;
    background: rgba(2, 14, 50, 0.75);
    z-index: 999999;
    top:0;
    left:0;
  }
.zwfw_pop {
  width: 1251px;
  height: 839px;
  position: absolute;
  z-index: 99999999;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border-radius: 11px;
  border: 1px solid;
  background: #001638;
  border-image: linear-gradient(360deg, rgba(57, 133, 255, 0.5), rgba(0, 205, 255, 0)) 1 1;
  .title {
    margin: 0 auto;
    width: 634px;
    height: 74px;
    background: url(~@/assets/csts/title1.png) no-repeat;
    text-align: center;
    margin-bottom: 37px;
    .title_text {
      display: inline-block;
      line-height: 74px;
      font-size: 40px;
      font-family: YouSheBiaoTiHei;
      color: #ffffff;
      background: linear-gradient(180deg, #ffffff 0%, #2aebff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .close_btn {
      position: absolute;
      top: 10px;
      right: 30px;
      width: 44px;
      height: 44px;
      cursor: pointer;
    }
  }
  .pop_content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    .content_box {
      width: 533px;
      .table_content {
        margin-top: 24px;
        margin-left: 5px;
        overflow: hidden;
        width: 522px;
        .item_cont1 {
          flex: 1;
          ::v-deep .row-item {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #37c1ff;
            line-height: 20px;
          }
          ::v-deep .swiper-item {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
          }
        }
      }
      .yctj {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        margin-top: 37px;
        margin-left: 9px;
        .yctj_item {
          display: flex;
          align-items: flex-end;
          margin-bottom: 17px;
          img {
            width: 55px;
            height: 58px;
          }
          .num_box {
            display: flex;
            flex-direction: column;
            margin-left: 15px;
            margin-bottom: 3px;
            align-items: flex-start;
            .num {
              font-size: 20px;
              font-family: DINCond-Black, DINCond;
              font-weight: 900;
              color: #ffffff;
              line-height: 24px;
              margin-bottom: 2px;
            }
            .item_text {
              background: url(~@/assets/leader/img/zwfw/bg11.png) no-repeat center / 100% 100%;
              width: 91px;
              height: 25px;
              font-size: 11px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 25px;
              text-align: left;
              padding-left: 10px;
            }
          }
        }
      }
      .center_right2 {
        display: flex;
        flex-wrap: wrap;
        margin-left: 37px;
        .center_right2Item {
          width: 220px;
          height: 124px;
          margin: 10px;
        }
      }
    }
  }
}
</style>