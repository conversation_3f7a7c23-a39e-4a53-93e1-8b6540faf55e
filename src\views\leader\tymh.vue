<template>
  <div class="tymh_box">
    <div class="content_tab">
      <ul>
        <li v-for="(item,index) in icons" :key="index" @click="jump(item)">
          <img :src="item.img" alt />
          <p>
            <span :title="item.sysdescribe">{{item.sysdescribe}}</span>
          </p>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { jumpJk, getJumpUrl } from '@/api/hs/hs.api.js'
export default {
  data () {
    return {
      icons: []
    }
  },
  mounted () {
    this.jumpJk()
  },
  methods: {
    async jumpJk () {
      let res = await jumpJk()
      if (res.code == '200') {
        this.icons = []
        this.icons = res.result
      }
    },
    // 调接口要是有值，就单点登录，没有值就直接取list里的地址调转
    async jump (it) {
      let res = await getJumpUrl({ modelId: it.id })
      if (res?.result) {
        window.open(res.result)
      } else {
        window.open(it.sysvalue)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.tymh_box {
  width: 1920px;
  height: 1080px;
  background: url('~@/assets/mhassets/bg.png') no-repeat 100% 100%;
  .content_tab {
    width: 100%;
    height: calc(100% - 248px);
    top: 248px;
    left: 50%;
    position: relative;
    transform: translateX(-50%);
    padding: 0 125px 0 125px;
    overflow-y: scroll;
    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 6px;
      background: transparent;
      // border: 1px solid #999;
      /*高宽分别对应横竖滚动条的尺寸*/
      // height: 1px;
    }

    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 2px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: rgb(45, 124, 228);
      height: 100px;
    }
    ul {
      width: 100%;
      display: flex;
      // justify-content: space-between;
      flex-wrap: wrap;
      list-style: none;
      padding: 0;
      li {
        display: flex;
        align-items: center;
        flex-direction: column;
        margin-bottom: 80px;
        margin-right: 100px;
        img {
          width: 450px;
          height: 250px;
        }
        p {
          margin-top: 40px;
          width: 400px;
          height: 62px;
          background: url('~@/assets/mhassets/tab_bg.png') no-repeat 100% 100%;
          background-size: 100% 100%;
          span {
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin: 0 auto;
            padding: 0 40px 0 40px;
            // text-align: left;
            display: inline-block;
            width: 100%;
            font-size: 30px;
            font-family: SourceHanSansCN-Bold, SourceHanSansCN;
            font-weight: bold;
            color: #ffffff;
            line-height: 62px;
            letter-spacing: 3px;
            background: linear-gradient(180deg, #ffffff 0%, #64c6ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
      }
    }
  }
}

</style>