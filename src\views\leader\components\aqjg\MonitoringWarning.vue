<template>
  <div class="wrap">
    <div class="info">
      <div class="item">
        <div class="icon">
          <img src="@/assets/aqjg/icon3.png" alt="" />
        </div>
        <div class="detail">
          <div class="line"></div>
          <div class="label">现有烟感设备数</div>
          <div class="count">
            <countTo
              ref="countTo"
              :startVal="$countTo.startVal"
              :decimals="$countTo.decimals(200)"
              :endVal="200"
              :duration="$countTo.duration"
            />
            /个
          </div>
          <div class="line"></div>
        </div>
      </div>
      <div class="item">
        <div class="icon">
          <img src="@/assets/aqjg/icon4.png" alt="" />
        </div>
        <div class="detail">
          <div class="line"></div>
          <div class="label">告警累计次数</div>
          <div class="count">
            <countTo
              ref="countTo"
              :startVal="$countTo.startVal"
              :decimals="$countTo.decimals(50)"
              :endVal="50"
              :duration="$countTo.duration"
            />
            /次
          </div>
          <div class="line"></div>
        </div>
      </div>
    </div>
    <div class="chart_wrap">
      <TrendLineChart :data="chartData" :options="options" :initOption="initOption" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'MonitoringWarning',
  data() {
    return {
      chartData: [
        ['product', '事件'],
        ['1月', 77],
        ['2月', 97],
        ['3月', 95],
        ['4月', 81],
        ['5月', 15],
        ['6月', 6],
        ['7月', 28],
        ['8月', 1],
        ['9月', 54],
        ['10月', 12],
        ['11月', 26],
        ['12月', 50]
      ],
      options: {
        legend: {
          show: false
        }
      },
      initOption: {
        yAxis: {
          name: '件'
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.wrap {
  width: 100%;
  height: 100%;
  padding: 11px 20px 22px;
  .info {
    width: 100%;
    height: 69px;
    padding: 0 7px;
    display: flex;
    justify-content: space-between;
    .item {
      display: flex;
      .icon {
        position: relative;
        width: 86px;
        height: 100%;
        background: url('~@/assets/aqjg/bg1.png') no-repeat;
        margin-right: 3px;
        img {
          position: absolute;
          left: 30px;
          top: 12px;
          animation: move infinite 3s ease-in-out;
          @keyframes move {
            0% {
              transform: rotateY(0);
            }
            50% {
              transform: rotateY(180deg);
            }
            100% {
              transform: rotateY(360deg);
            }
          }
        }
      }
      .detail {
        padding-top: 6px;
        padding-bottom: 2px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .line {
          width: 89px;
          height: 5px;
          background: url('~@/assets/aqjg/icon5.png') no-repeat;
        }
        .label {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #cbe5ff;
          line-height: 20px;
          letter-spacing: 1px;
          padding-left: 6px;
        }
        .count {
          display: flex;
          align-items: center;
          font-size: 13px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #cbe5ff;
          padding-left: 6px;
          span {
            font-size: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 24px;
          }
        }
      }
    }
  }
  .chart_wrap {
    width: 100%;
    height: calc(100% - 69px);
    padding-top: 10px;
  }
}
</style>
