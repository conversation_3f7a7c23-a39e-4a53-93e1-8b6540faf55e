<template>
	<div class="block" :style="{ height: blockHeight + 'px', width: this.blockWidth + 'px' }">
		<img :src="blockBackgroundImage" alt="" />
		<div class="content-box">
			<div class="block_title" @click="clickBulletFrame">
				<div class="left-box">
					<!-- <div class="nongji-icon-box"></div> -->
					<div class="left-title">{{ leftTitle }}</div>
				</div>

				<div v-if="showTotalNumber" class="total-number-box">
					{{ titleNumber + titleNUmberUnit }}
				</div>

				<ul class="btns" v-if="isListBtns">
					<li
						:class="['btnList', currentIndex == index ? 'active' : '']"
						v-for="(item, index) in textArr"
						:key="index"
						@click.stop="clickchange(index)"
					>
						{{ item }}
					</li>
				</ul>
				<div class="more" v-if="showMore" @click="showMoreFn">更多 <img src="@/assets/img/block-box/more-btn.png" alt="" /></div>
				<div class="more" v-if="showMore2" @click="showMoreFn2">返回</div>
				<div v-if="showMore3" class="popDropDown">
					<el-dropdown v-if="textArr2.length > 0" @command="clickchange">
						<span class="el-dropdown-link"> {{ textArr2[currentIndex][1] }}<i class="el-icon-arrow-down el-icon--right"></i> </span>
						<el-dropdown-menu slot="dropdown">
							<el-dropdown-item v-for="(item, index) in textArr2" :key="index" :command="index">
								{{ item[1] }}
							</el-dropdown-item>
						</el-dropdown-menu>
					</el-dropdown>
				</div>
			</div>
			<div class="content" :style="{ marginTop: marginTop + 'px' }">
				<slot></slot>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		leftTitle: {
			type: String,
			default: '',
		},
		rightTitle: {
			type: String,
			default: '',
		},

		subtitle: {
			type: String,
			default: '',
		},
		textArr: {
			type: Array,
			default: () => [],
		},
		textArr2: {
			type: Array,
			default: () => [['', '全部']],
		},
		isListBtns: {
			type: Boolean,
			default: true,
		},
		showMore: {
			type: Boolean,
			default: false,
		},
		showMore2: {
			type: Boolean,
			default: false,
		},
		showMore3: {
			type: Boolean,
			default: false,
		},
		blockHeight: {
			type: Number,
			default: 354,
		},
		blockWidth: {
			type: Number,
			default: 455,
		},

		blockBackgroundImage: {
			type: String,
			default: '',
		},
		marginTop: {
			type: Number,
			default: 0,
		},
		titleNumber: {
			type: Number,
			default: 0,
		},
		titleNUmberUnit: {
			type: String,
			default: '万',
		},
		showTotalNumber: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			currentIndex: 0,
		}
	},
	methods: {
		clickchange(index) {
			this.currentIndex = index
			this.$emit('updateChange', this.currentIndex)
		},
		clickBulletFrame() {
			// this.$emit('updateChange2', true)
		},
		showMoreFn() {
			this.$emit('handleShowMore')
		},
		showMoreFn2() {
			this.$emit('handleShowMore2')
		},
	},
}
</script>

<style lang="less" scoped>
.block {
	box-sizing: border-box;
	z-index: 101;
	position: relative;

	// background: linear-gradient(180deg, rgba(36, 129, 140, 0) 0%, rgba(126, 200, 209, 0.29) 73%, rgba(255, 255, 255, 0.91) 100%);

	// background: linear-gradient(180deg, rgba(36, 129, 140, 0) 0%, rgba(126, 200, 209, 0.29) 73%, rgba(255, 255, 255, 0.91) 100%);

	/* 背景不重复 */
	background-repeat: no-repeat;
	/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
	background-size: cover;
	/* 背景居中显示（可选） */
	background-position: center;

	img {
		width: 100%;
		height: 100%;
	}

	.content-box {
		width: 100%;
		height: 100%;
		position: absolute;
		top: 0;
		left: 0;
		padding-top: 46px;
		display: flex;
		justify-content: flex-end;
		align-items: flex-start;
	}

	.block_title {
		width: 100%;
		position: absolute;
		top: 4px;
		// left: 24px;
		height: 40px;
		/* 背景不重复 */
		background-repeat: no-repeat;
		/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
		background-size: cover;
		/* 背景居中显示（可选） */
		background-position: center;

		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-left: 24px;
		padding-right: 14px;

		.left-box {
			height: 100%;
			display: flex;
			justify-content: flex-start;
			align-items: center;

			.left-title {
				font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
				font-weight: 800;
				font-size: 22px;
				line-height: 22px;
				letter-spacing: 2px;
				text-shadow: 0px 0px 5px rgba(45, 205, 255, 0.3);
				font-style: normal;
				text-transform: none;
				text-align: left;
				background-image: linear-gradient(180deg, #2897ff 0%, #ffffff 100%);
				-webkit-background-clip: text; /* 让背景应用到文字 */
				color: transparent; /* 文字颜色透明，显示渐变 */
			}
		}

		.total-number-box {
			font-family: PangMenZhengDao-3, PangMenZhengDao-3;
			font-weight: 400;
			font-size: 20px;
			color: #dbf1ff;
			font-weight: bold;
			text-shadow: 0px 0px 10px #27a6ff;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}

		&.title_bg_Btn {
			background: url(~@/assets/leader/img/component/title_bg1.png) no-repeat;
			background-size: 100% 100%;
		}
		.title {
			font-family: YouSheBiaoTiHei;
			font-size: 22px;
			color: #ffffff;
			line-height: 36px;
			font-style: normal;
			letter-spacing: 2px;
			background: linear-gradient(180deg, #ffffff 0%, #7ed1ff 100%);
			background-clip: text;
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}
		.subtitle {
			font-size: 14px;
			font-family: DINCond-RegularAlternate;
			font-weight: normal;
			color: #b3daff;
		}

		.btns {
			display: flex;
			align-items: center;
			height: 16px;
			// margin-right: 22px;
			position: absolute;
			top: 1px;
			right: 15px;
			.btnList {
				// width: 50px;
				height: 26px;
				font-size: 16px;
				font-family: PangMenZhengDao;
				color: #caecff;
				text-shadow: 0px 0px 1px #00132e;
				background: url(~@/assets/leader/img/component/title_btn.png) no-repeat;
				background-size: 100% 100%;
				line-height: 26px;
				cursor: pointer;
				&.active {
					color: #45daff;
					text-shadow: 0px 0px 4px rgba(0, 175, 255, 0.5), 0px 0px 1px rgba(0, 30, 50, 0.5);
				}
				&:not(:last-of-type) {
					margin-right: 14px;
				}
			}
		}
		.more {
			height: 100%;
			text-shadow: 0px 0px 1px #00132e;
			font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
			font-size: 16px;
			color: #caecff;
			line-height: 21px;
			text-align: center;
			font-style: normal;

			display: flex;
			justify-content: flex-end;
			align-items: center;

			cursor: pointer;

			img {
				width: 18px;
				height: 18px;
				margin-left: 8px;
			}
		}
	}
	.content {
		position: relative;
		// width: 100%;
		width: calc(100% - 8px);
		height: 100%;
		backdrop-filter: blur(4px);
		background-color: rgba(0, 0, 0, 0.3);
		// height: calc(100% - 55px);
		// height: 100%;
		// background: rgba(0, 14, 25, 0.4);
		// border: 1px solid rgba(56, 99, 176, 0.75);
	}
}

.popDropDown {
	::v-deep .el-dropdown {
		color: #caecff;
		font-family: YouSheBiaoTiHei;
		font-size: 16px;
		line-height: 21px;
		text-align: right;
		font-style: normal;
		line-height: 40px;
		right: 0;
		position: absolute;
	}
	::v-deep .el-dropdown-menu {
		// background-color: #00173b !important;
		// border: 1px solid #2b7bbb !important;
		background-color: #c6e2e7 !important;
		border: 1px solid #d2f2ea !important;
	}
	.el-dropdown-menu__item {
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
		line-height: 40px;
		padding: 0 30px;
		&:hover {
			background: #178a7f;
			color: rgba(255, 255, 255, 0.8);
		}
	}
}
</style>
