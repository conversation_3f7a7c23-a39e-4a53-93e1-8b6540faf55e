<template>
  <div class="wrap">
    <div
      class="table-wrapper"
      :style="{
        height: `calc(${contentHeight})`
      }"
    >
      <div class="table-content" :style="{ height: contentHeight }">
        <swiper class="swiper" :options="swiperOption" ref="myBotSwiper">
          <swiper-slide v-for="(it, i) in dataList" :key="i">
            <div class="info">
              <img
                class="state-icon"
                src="@/assets/zhdd/state1.png"
                alt=""
                v-if="it.state === '未处理'"
              />
              <img
                class="state-icon"
                src="@/assets/zhdd/state2.png"
                alt=""
                v-if="it.state === '已完成'"
              />
              <img
                class="state-icon"
                src="@/assets/zhdd/state3.png"
                alt=""
                v-if="it.state === '处理中'"
              />
              <el-image
                class="pic"
                :src="
                  it.appendixList?.length > 0 && it.appendixList[0].url
                    ? it.appendixList[0].url
                    : defaultImg
                "
                :preview-src-list="
                  it.appendixList?.length > 0 && it.appendixList[0].url
                    ? [it.appendixList[0].url]
                    : [defaultImg]
                "
              ></el-image>
              <!-- <img
                class="pic"
                :src="
                  it.appendixList && it.appendixList[0].url ? it.appendixList[0].url : defaultImg
                "
                alt=""
              /> -->
              <div class="detail" @click="showDetail(it)">
                <div class="title" :title="it.title">{{ it.title }}</div>
                <div class="depart">
                  <div class="name">{{ it.type }}</div>
                  <div class="type" :title="it.eventCategoryName">{{ it.eventCategoryName }}</div>
                </div>
                <div class="status">
                  <div class="time">{{ it.insertTime?.slice(2) }}</div>
                  <!-- <div class="state" :class="{ unhandle: it.state === 0 }">
                    {{ it.state === 0 ? '未处理' : '处理中' }}
                  </div> -->
                </div>
              </div>
            </div>
          </swiper-slide>
        </swiper>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RealTimeEvent',
  props: {
    data: {
      type: Array,
      default: () => [
        {
          title: '江宁区湖熟街道雨润大家北京口交接处遇到一事件',
          name: '12345热线',
          type: '城市管理-市容热线',
          time: '23/03/12 14:39:45',
          state: 0
        },
        {
          title: '江宁区湖熟街道雨润大家北京口交接处遇到一事件',
          name: '12345热线',
          type: '城市管理-市容热线',
          time: '23/03/12 14:39:45',
          state: 1
        },
        {
          title: '江宁区湖熟街道雨润大家北京口交接处遇到一事件',
          name: '12345热线',
          type: '城市管理-市容热线',
          time: '23/03/12 14:39:45',
          state: 2
        }
      ]
    },
    titles: {
      type: Array,
      default: () => ['事件标题', '创建时间', '信息来源', '分类', '状态', '上报人']
    },
    widths: {
      type: Array,
      default: () => ['246px', '348px', '224px', '214px', '214px', '214px']
    },
    contentHeight: {
      type: String,
      default: '220px'
    },
    showHeader: {
      type: Boolean,
      default: true
    },
    tabelHeight: {
      type: String,
      default: '105px'
    },
    spanPadding: {
      type: String,
      default: '0 15px'
    },
    headerSpanPadding: {
      type: String,
      default: '0 15px'
    },
    headerHeight: {
      type: String,
      default: '44px'
    },
    rowFontSize: {
      type: String,
      default: '14px'
    }
  },
  computed: {
    // 将手机号的中间四位隐藏
    dataList() {
      return this.data.map(it => {
        // let [name, mobile, time, totals, bfb, qwer] = it;
        // const reg = /^(\d{3})\d{4}(\d{4})$/;
        // mobile = mobile.replace(reg, '$1****$2');

        // 不需要隐藏手机号码就隐去上面的代码
        let qwer = it
        return qwer
      })
    },
    myBotSwiper() {
      return this.$refs.myBotSwiper.$swiper
    }
  },
  data() {
    return {
      defaultImg: require('@/assets/zhdd/image.png'),
      currentActiveRow: 999,
      swiperOption: {
        autoHeight: true,
        direction: 'vertical',
        spaceBetween: 0,
        autoplay: {
          delay: 3500,
          disableOnInteraction: false,
          autoplayDisableOnInteraction: false
        },
        slidesPerView: 'auto',
        grabCursor: true,
        autoplayDisableOnInteraction: false,
        mousewheelControl: true
      }
    }
  },
  methods: {
    showDetail(it) {
      this.$emit('clickRealtimeEvent', it)
    }
  },
  watch: {
    data() {
      this.currentActiveRow = 0
    }
  }
}
</script>

<style lang="scss" scoped>
.wrap {
  width: 100%;
  height: 100%;
  padding: 9px 0 19px 33px;
  :deep(.swiper-slide) {
    height: 120px;
    border-bottom: 1px dashed #979797;
    box-sizing: border-box;
    padding-top: 14px;
    &:last-child {
      border-bottom: none;
    }
  }
  .info {
    position: relative;
    display: flex;
    .state-icon {
      position: absolute;
      right: 0;
      top: 0;
      width: 68px;
      height: 71px;
    }
    .pic {
      width: 140px;
      height: 90px;
      margin-right: 14px;
    }
    .detail {
      padding: 4px 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .title {
        width: 210px;
        height: 22px;
        font-size: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 22px;
        text-align: left;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .depart {
        width: 210px;
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
        display: flex;
        gap: 15px;
        .name {
          width: 100px;
          text-align: left;
        }
        .type {
          width: 156px;
          text-align: right;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .status {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .time {
          height: 20px;
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 20px;
        }
        .state {
          height: 17px;
          font-size: 14px;
          font-family: AppleSystemUIFont;
          line-height: 17px;
          background: linear-gradient(180deg, #ffffff 0%, #23cdf2 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .unhandle {
          background: linear-gradient(180deg, #ffffff 0%, #f2b923 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
  }
}
.table-wrapper {
  width: 100%;

  span {
    box-sizing: border-box;
  }

  .table-header {
    $height: 60px;
    width: 100%;
    height: $height;
    display: flex;
    background: rgba(3, 135, 255, 0.15);

    span {
      padding: 0px !important;
      // background: rgba(255, 255, 255, 0.12);
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      line-height: $height;
      font-weight: 400;
      color: #37c1ff;
      line-height: 20px;

      .row-item {
        position: absolute;
        padding: 0 15px;
        padding: 0px !important;
        top: 50%;
        transform: translateY(-50%);
        overflow: hidden;
        text-overflow: ellipsis; /* 文本溢出时显示省略号来代表被修剪的文本 */
        white-space: normal;
        display: -webkit-box; /* 必须结合的属性 ，将对象作为弹性伸缩盒子模型显示 */
        -webkit-box-orient: vertical; /* 必须结合的属性 ，设置或检索伸缩盒对象的子元素的排列方式 */
        -webkit-line-clamp: 2; /* 文本需要显示多少行 */
        word-break: break-all;
      }

      &:nth-child(2) {
        text-align: center;
      }
    }

    span + span {
      margin-left: 2px;
    }
  }

  .table-content {
    width: 100%;
    height: 284px;

    .swiper {
      width: 100%;
      height: 100%;

      .table-row {
        width: 100%;
        height: 44px;
        line-height: 44px;
        cursor: pointer;
        display: flex;

        &.active {
          span {
            color: rgba(131, 194, 238, 1);
            font-size: 14px;
            font-weight: bold;
          }
        }

        &.stripe span {
          background: rgba(255, 255, 255, 0.06);
        }

        span {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: rgba(255, 255, 255, 0.85);

          & > div {
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
          }
        }

        span + span {
          margin-left: 2px;
        }

        .swiper-item {
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  span {
    height: 100%;
    // line-height: 100%;
    // display: inline-block;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 15px;
    display: flex;
    justify-content: center;
    position: relative;

    .row-point {
      display: flex;
      align-items: center;
      justify-content: center;

      .red-point {
        color: #08bf8a;
      }

      .green-point {
        color: #ff9090;
      }
    }

    .row-item {
      position: absolute;
      padding: 0 15px;
      top: 50%;
      transform: translateY(-50%);
      overflow: hidden;
      text-overflow: ellipsis; /* 文本溢出时显示省略号来代表被修剪的文本 */
      white-space: normal;
      display: -webkit-box; /* 必须结合的属性 ，将对象作为弹性伸缩盒子模型显示 */
      -webkit-box-orient: vertical; /* 必须结合的属性 ，设置或检索伸缩盒对象的子元素的排列方式 */
      -webkit-line-clamp: 2; /* 文本需要显示多少行 */
      word-break: break-all;
    }
  }
  .icon_ {
    display: flex;
    margin: 0 0 0 12px;
    display: flex;
    align-items: center;
    img {
      width: 12px;
      height: 16px;
      margin-left: 2px;
    }
  }
}
</style>
