<template>
  <div class="block" :style="{ height: blockHeight + 'px' }">
    <div class="block_title" @click="clickBulletFrame">
      <img :src="iconSrc" v-if="showIcon" />
      <span
        :style="{ 
          fontSize: titleStyle.titlefontSize + 'px',
        fontWeight:titleStyle.fontWeight,
        lineHeight:titleStyle.lineHeight + 'px'
        }"
        :class="['title', showIcon ? 'isIcon' : '']"
      >
        {{ title }}
        <slot name="tab"></slot>
      </span>
      <span class="subtitle">{{ subtitle }}</span>
      <ul class="btns" v-if="isListBtns">
        <li
          :class="['btnList', currentIndex == index ? 'active' : '']"
          v-for="(item, index) in textArr"
          :key="index"
          @click.stop="clickchange(index)"
        >{{ item }}</li>
      </ul>
      <div :style="{ 
          fontSize: titleStyle.morefontSize + 'px',
        }" class="more" v-if="showMore" @click="showMoreFn">更多</div>
    </div>
    <div class="content">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    subtitle: {
      type: String,
      default: ''
    },
    textArr: {
      type: Array,
      default: () => ['社会保险', '住房保险']
    },
    isListBtns: {
      type: Boolean,
      default: true
    },
    showMore: {
      type: Boolean,
      default: false
    },
    blockHeight: {
      type: Number,
      default: 277
    },
    showIcon: {
      type: Boolean,
      default: false
    },
    iconSrc: {
      type: String,
      default: require('@/assets/leader/img/component/title_icon.png')
    },
    titleStyle: {
      type: Object,
      default: () => {
        return {
          titlefontSize: '18',
          fontWeight: 'normal',
          lineHeight: '33',
          morefontSize:'16'
        }
      }
    }
    // fontSize: {
    //   type: Number,
    //   default: 18
    // },
    // fontWeight: {
    //   type: [String , Number],
    //   default: 'normal'
    // },
    // lineHeight: {
    //   type:  Number,
    //   default: 33
    // },
  },
  data() {
    return {
      currentIndex: 0
    }
  },
  methods: {
    clickchange(index) {
      this.currentIndex = index
      this.$emit('updateChange', this.currentIndex)
    },
    clickBulletFrame() {
      this.$emit('updateChange2', true)
    },
    showMoreFn() {
      this.$emit('handleShowMore', true)
    }
  }
}
</script>

<style lang="scss" scoped>
.block {
  width: 460px;
  box-sizing: border-box;
  position: relative;
  .block_title {
    position: relative;
    width: 100%;
    height: 33px;
    background: url(~@/assets/img/title_bg1.png) no-repeat;
    display: flex;
    // justify-content: space-between;
    padding-left: 39px;
    position: relative;

    &.title_bg_Btn {
      background: url(~@/assets/leader/img/component/title_bg1.png) no-repeat;
      background-size: 100% 100%;
    }
    img {
      width: 33px;
      height: 35px;
    }
    .title {
      // font-size: 18px;
      font-family: FZSKJW--GB1-0, FZSKJW--GB1;
      // font-weight: normal;
      color: #ffffff;
      line-height: 33px;
      // margin-top: -8px;
      margin-right: 6px;
      margin-left: 15px;
      span {
        cursor: pointer;
        font-size: 14px;
        font-family: FZSKJW--GB1-0, FZSKJW--GB1;
        font-weight: normal;
        color: #9de2ff;
        line-height: 16px;
        &.active {
          font-size: 18px;
          color: #ffffff;
          line-height: 33px;
        }
      }
      span + span {
        margin-left: 10px;
      }
      &.isIcon {
        margin-left: 6px;
        font-size: 18px;
        font-family: FZSKJW--GB1-0, FZSKJW--GB1;
        font-weight: normal;
        color: #ffffff;
        line-height: 28px;
        background: linear-gradient(180deg, #ffffff 0%, #ffb900 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
    .subtitle {
      font-size: 14px;
      font-family: DINCond-RegularAlternate;
      font-weight: normal;
      color: #b3daff;
      line-height: 33px;
    }

    .btns {
      display: flex;
      align-items: center;
      height: 16px;
      // margin-right: 22px;
      position: absolute;
      top: 1px;
      right: 15px;
      .btnList {
        // width: 50px;
        height: 26px;
        font-size: 14px;
        font-family: FZSKJW--GB1-0, FZSKJW--GB1;
        font-weight: normal;
        color: #9de2ff;
        line-height: 33px;
        cursor: pointer;
        &.active {
          font-size: 16px;
          color: #ffffff;
          line-height: 33px;
          background: linear-gradient(180deg, #ffffff 0%, #09c8f0 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        &:not(:last-of-type) {
          margin-right: 14px;
        }
      }
    }
    .more {
      position: absolute;
      right: 22px;
      top: 1px;
      // height: 16px;
      // font-size: 16px;
      font-family: FZSKJW--GB1-0, FZSKJW--GB1;
      font-weight: normal;
      color: #ffffff;
      line-height: 33px;
      background: linear-gradient(180deg, #ffffff 0%, #09c8f0 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      cursor: pointer;
    }
  }
  .content {
    position: relative;
    height: calc(100% - 33px);
  }
}
</style>
