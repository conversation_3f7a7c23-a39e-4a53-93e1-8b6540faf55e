<template>
	<div class="header">
		<div class="title">
			<div class="nongji-icon-box"></div>
			<div class="app-title-box"></div>
			<div class="app-tab-box">
				<div
					class="app-tab-item"
					:style="{
						background:
							currentIndex == i
								? require('@/assets/leader/img/component/header/header-app-tag-active.png')
								: require('@/assets/leader/img/component/header/header-app-tag.png'),
					}"
					v-for="(it, i) in appBtns"
					:key="i"
					@click="changeBtn(index)"
				>
					{{ it }}
				</div>
			</div>
			<div class="time-info-box">
				<div class="time-info">
					<span class="time1">{{ time }}</span>
					<span class="time2">{{ time1 }}</span>
				</div>
				<div class="close-system-box" @click="tcdl">
					<div class="close-icon" :title="username"></div>
					<div class="close-tip">退出</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
// import { tianqiapi } from '@/api/common/common'
import { bjnjUrl } from '@/utils/leader/const'
export default {
	props: {
		icons: {
			type: Array,
			default() {
				return ['城市体征', '统一门户', '指挥调度', '监测预警', '事件处置', '考核研判']
			},
		},
	},
	data() {
		return {
			currentIndex: -1,
			leftBtns: ['首页'],
			rightBtns: ['指挥调度'],
			timer: null,
			time: '',
			time1: '',
			time2: '',
			tq: {},
			showChildMenu: false,
		}
	},
	methods: {
		changeBtn(index) {
			this.currentIndex = index

			switch (this.currentIndex) {
				case 0:
					// this.$router.push('/zt')
					window.open('https://portal.camtc.cn/home/<USER>')
					break
				case 1:
					this.$router.push('/zt')
					window.open(bjnjUrl + '/api/oauth/jumpSceenOauth')
					break
				case 2:
					this.$router.push('/zt')
					break
				case 3:
					this.$router.push('/zt')
					break
				default:
					break
			}
		},
		//  时间格式化
		getTime() {
			let myDate = new Date()
			let wk = myDate.getDay()
			const month = this.dayjs().month() + 1 // 月份从0开始，所以要加1
			const day = this.dayjs().date()
			let weeks = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']

			this.time2 = weeks[wk]

			this.time = `${month}月${day}日 ${this.time2}`

			this.time1 = this.dayjs().format('HH:mm:ss')
		},
		// 天气
		// gettq() {
		//   tianqiapi().then(res => {
		//     // console.log(999, res)
		//     this.tq = res.lives[0]
		//   })
		// },
		clickIcon(i) {
			console.log(i)
		},
	},
	watch: {
		$route(to, from) {
			console.log(to.path)
			switch (to.path) {
				case '/hszl':
					this.currentIndex = 0
					break
				case '/djyl':
					this.currentIndex = 1
					break
				case '/tsgz':
					this.currentIndex = 2
					break
				case '/zhny':
					this.currentIndex = 4
					break
				// case '/zhdd':
				//   this.currentIndex = 4
				//   break
				// case '/aqjg':
				//   this.currentIndex = 5
				//   break
				case '/fxyp':
					this.currentIndex = 5
					break
				case '/tymh':
					this.currentIndex = 6
					break
				default:
					break
			}
		},
		invokeRHtx(newValue, oldValue) {
			console.log('融合通信状态：', 'newValue' + newValue, 'oldValue' + oldValue)
		},
	},
	mounted() {
		this.getTime()
		this.timer = setInterval(this.getTime, 1000)
		// this.gettq()
		// this.tqtime = setInterval(this.gettq, 3600000)
	},
	beforeDestroy() {
		clearInterval(this.timer)
	},
	computed: {
		invokeRHtx() {
			return this.$store.state.rhtxState
		},
	},
}
</script>

<style lang="less" scoped>
.header {
	width: 100vw;
	height: 80px;
	position: relative;
	z-index: 1003;
	.title {
		position: absolute;
		left: 0px;
		top: 0px;
		width: 1920px;
		height: 80px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		z-index: 999;
		position: relative;
		padding: 0 34px 0 24px;
		background: url(~@/assets/leader/img/component/header/header-banner.png);
		/* 背景不重复 */
		background-repeat: no-repeat;

		/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
		background-size: cover;

		/* 背景居中显示（可选） */
		background-position: center;

		.nongji-icon-box {
			width: 45px;
			height: 45px;
			background: url(~@/assets/leader/img/component/header/header-nongji-icon.png);
			/* 背景不重复 */
			background-repeat: no-repeat;
			/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
			background-size: cover;
			/* 背景居中显示（可选） */
			background-position: center;
			margin-right: 64px;
		}

		.app-title-box {
			width: 520px;
			height: 100%;
			background: url(~@/assets/leader/img/component/header/header-title-bg.png);
			/* 背景不重复 */
			background-repeat: no-repeat;
			/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
			background-size: cover;
			/* 背景居中显示（可选） */
			background-position: center;
			margin-right: 34px;
		}

		.app-tab-box {
			width: 870px;
			height: 100%;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			.app-tab-item {
				width: 232px;
				height: 44px;
				/* 背景不重复 */
				background-repeat: no-repeat;
				/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
				background-size: cover;
				/* 背景居中显示（可选） */
				background-position: center;
				display: flex;
				justify-content: center;
				align-items: center;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 16px;
				color: #24818c;
				font-style: normal;
				margin-left: -3px;
				cursor: pointer;
				&:first-child {
					margin-left: 0 !important;
				}
			}
		}

		.time-info-box {
			width: 142px;
			height: 100%;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			margin-right: 20px;

			.time-info {
				width: 96px;
				height: 100%;
				display: flex;
				justify-content: center;
				align-items: flex-start;

				.time1 {
					font-family: AppleSystemUIFont;
					font-size: 10px;
					color: #24818c;
					line-height: 10px;
					font-style: normal;
				}
				.time2 {
					font-family: AppleSystemUIFont;
					font-size: 20px;
					color: #24818c;
					line-height: 20px;
					letter-spacing: 2px;
					font-style: normal;
				}
			}
		}

		.close-system-box {
			width: 26px;
			height: 100%;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			cursor: pointer;
			.close-icon {
				width: 11px;
				height: 11px;
				background: url(~@/assets/leader/img/component/header/header-close-icon.png);
				/* 背景不重复 */
				background-repeat: no-repeat;
				/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
				background-size: cover;
				/* 背景居中显示（可选） */
				background-position: center;
				margin-bottom: 5px;
				cursor: pointer;
			}

			.close-tip {
				width: 26px;
				height: 20px;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 13px;
				color: #24818c;
				line-height: 20px;
				font-style: normal;
			}
		}
	}
}
</style>
