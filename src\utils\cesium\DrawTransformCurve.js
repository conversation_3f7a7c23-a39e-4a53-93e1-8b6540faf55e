class DrawTransformCurve {
    constructor(viewer) {
        //设置唯一id 备用
        this.viewer = viewer;
        this._poly = {};
        this._positionsd = {};
        this._polyLine = {};
        this._entitys = {};
    }
    // 添加模型
    addEntity(params) {
        var entiti = this.viewer.entities.add({
            id: params.id,
            position: Cesium.Cartesian3.fromDegrees(params.position.lon, params.position.lat, params.position.het),
            orientation: Cesium.Transforms.headingPitchRollQuaternion(Cesium.Cartesian3.fromDegrees(params.position.lon, params.position.lat, params.position.het),
                new Cesium.HeadingPitchRoll(Cesium.Math.toRadians(params.rotates && params.rotates.heading || 0), Cesium.Math.toRadians(params.rotates && params.rotates.pitch || 0), Cesium.Math.toRadians(params.rotates && params.rotates.roll || 0))),
            model: {
                uri: params.url || "./Apps/SampleData/models/CesiumAir/Cesium_Air.glb",
                minimumPixelSize: params.minSize || 128,//最小的模型像素
                maximumScale: params.MaxSize || 10000,//最大的模型像素
            }
        })
        this._entitys[params.id] = entiti;
    }

    // 绘制移动线
    addtransPolyLine(params) {
        var _this = this
        var viewer = this.viewer
        var PolyLinePrimitive = (function () {
            function _(positions) {
                console.log('轨迹线颜色：',params.color)
                this.options = {
                    polyline: {
                        show: true,
                        positions: [],
                        material: params.color ? Cesium.Color.fromCssColorString(params.color) : Cesium.Color.RED,
                        width: params.width || 5
                    }
                };
                this.positions = positions;
                this._init();
            }

            _.prototype._init = function () {
                var _self = this;
                var _update = function () {
                    return _self.positions;
                };
                //实时更新polyline.positions
                this.options.polyline.positions = new Cesium.CallbackProperty(_update, false);
                var pLine = viewer.entities.add(this.options);
                _this._polyLine[params.id] = pLine;
            };
            return _;
        })();
        var fromDegposition = Cesium.Cartesian3.fromDegrees(params.position.lon, params.position.lat, params.position.alt)

        if (_this._positionsd[params.id] || _this._positionsd[params.id] instanceof Array) {
            if (_this._positionsd[params.id].length >= 2) {
                if (!Cesium.defined(_this._poly[params.id])) {
                    _this._poly[params.id] = new PolyLinePrimitive(_this._positionsd[params.id]);
                } else {
                    _this._positionsd[params.id].push(fromDegposition);
                }

            } else {
                _this._positionsd[params.id].push(fromDegposition);
            }
        } else {
            _this._positionsd[params.id] = [];
            _this._positionsd[params.id].push(fromDegposition);
        }

    }

    // 开始绘制移动模型轨迹
    startTransfromEnt(params) {
        if (this._entitys[params.id]) {
            var fromDegposition = Cesium.Cartesian3.fromDegrees(params.position.lon, params.position.lat, params.position.alt)
            this._entitys[params.id]._position._value = fromDegposition;
            if (params.ifDrawLine)
                this.addtransPolyLine(params)
            if (params.rotates) this.rotateEnt(params)
        } else {
            this.addEntity(params)
            if (params.ifDrawLine)
                this.addtransPolyLine(params)
        }
    }
    // 旋转模型
    rotateEnt(params) {
        if (this._entitys[params.id]) {
            var hpr = new Cesium.HeadingPitchRoll(Cesium.Math.toRadians(params.rotates && params.rotates.heading || 0), Cesium.Math.toRadians(params.rotates && params.rotates.pitch || 0), Cesium.Math.toRadians(params.rotates && params.rotates.roll || 0));
            var orientation = Cesium.Transforms.headingPitchRollQuaternion(this._entitys[params.id]._position._value, hpr);
            this._entitys[params.id]._orientation._value = orientation
        }
    }
    ///删/
    // 清空
    clear() {
        for (const key in this._entitys) {
            if (this._entitys.hasOwnProperty.call(this._entitys, key)) {
                const element = this._entitys[key];
                this.viewer.entities.remove(element)
                this.viewer.entities.remove(this._polyLine[key])
                this._poly[key] = undefined;
                this._positionsd[key] = undefined;
                this._polyLine[key] = undefined;
                this._entitys[key] = undefined;
            }
        }
    }
    // 销毁
    destroy() {

    }
}

export default DrawTransformCurve