<template>
  <div class='aird-digitalscroll' :style="{ height: fontSize + 'px', lineHeight: fontSize + 'px' }">
    <p
      v-for='(item, index) in computeNumber'
      :key='index'
      class='aird-digitalscroll__item'
      :style="{ width: (['.', ','].indexOf(item) >= 0) ? fontSize / 2 - 9.5 + 'px' : fontSize / 2 + 8.5 + 'px'
      , fontSize: fontSize + 'px' }">

      <span
        v-if='item === "."'
        ref='numberDom'
        :class='{ move: isMove }'
        :style="{ color: color, letterSpacing: fontSize + 'px' }"
      >..........</span>

      <span
        v-else-if='item === ","'
        ref='numberDom'
        :class='{ move: isMove }'
        :style="{ color: color, letterSpacing: fontSize + 'px' }"
      >,,,,,,,,,,</span>

      <span
        v-else
        ref='numberDom'
        :class='{ move: isMove }'
        :style="{ color: color, letterSpacing: fontSize + 'px' }"
      >0123456789</span>
    </p>
    <span class='unit' :style="{color: color}">{{unit}}</span>
  </div>
</template>

<script>
export default {
  //
  name: 'NumScroll',
  props: {
    number: {
      type: String,
      default: '0'
    },
    color: {
      type: String,
      default: '#fff'
    },
    fontSize: {
      type: [String, Number],
      default: 44
    },
    unit: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      maxLen: 10, //最大长度
      computeNumber: [], //数字补零后分割为数组，遍历
      timeTicket: null,
      isMove: false
    };
  },
  watch: {
    number() {
      this.refresh();
    }
  },
  mounted() {
    this.refresh();
    this.increaseNumber();
  },
  beforeDestroy() {
    window.clearInterval(this.timeTicket);
    this.timeTicket = null;
  },
  methods: {
    /**
     * @description: 数字补零操作，返回数组
     * @param {string} num 被操作数
     * @param {number} n 总长度
     * @return:
     */
    prefixZero(num, n) {
      return (Array(n).join('') + num).slice(-n).split('');
    },
    // 设置每一位数字的偏移
    setNumberTransform() {
      const numberItems = this.$refs.numberDom;
      if(numberItems){
      for (let index = 0; index < numberItems.length; index++) {
        const elem = numberItems[index];
        let single = Number(this.computeNumber[index]) * -10;
        if (this.computeNumber[index] === '.') {
          single = -80;
        } else if (this.computeNumber[index] === ',') {
          single = -90;
        }
        // console.log('Number', single);
        elem.style.transform = `translate(-50%,${single}%`;
      }
      }
      
    },
    setNumberToZero() {
      this.isMove = false;
      const numberItems = this.$refs.numberDom;
      for (let index = 0; index < numberItems.length; index++) {
        const elem = numberItems[index];
        elem.style.transform = `translate(-50%,-50%`;
      }
    },
    // 定时增长数字
    increaseNumber() {
      this.timeTicket = setInterval(() => {
        this.setNumberToZero();
        setTimeout(() => {
          this.refresh();
        }, 15);
      }, 10000);
    },
    // 定时刷新数据
    refresh() {
      this.isMove = true;
      this.computeNumber = this.prefixZero(this.number, this.maxLen);
      // console.log('this.computeNumber', this.computeNumber);
      this.$nextTick(() => {
        this.setNumberTransform();
      });
    }
  }
};
</script>

<style lang='scss' scoped>

.aird-digitalscroll {
  display: flex;
  justify-content: start;
  line-height: 100%;
  height: 100%;

  p {
    line-height: 100%;
    width: 12px;
    height: 100%;
    text-align: center;
    display: inline-block;
    position: relative;
    writing-mode: vertical-lr;
    text-orientation: upright;
    overflow: hidden;
    &:last-child {
      margin-right: 0;
    }

    span {
      width: 20px;
      font-size: 18px;
      position: absolute;
      top: 0;
      left: 40%;
      transform: translate(-50%, -50%);
      letter-spacing: 10px;
      font-family: DIN-BlackItalic, DIN;
    }
  }

  .unit {
    // font-size: 30px;
    // font-family: DIN-BlackItalic, DIN;
    // line-height: 104px;
    // text-align: end;
    // margin-left: 3px;
  }
}

.move {
  transition: transform 2s ease;
}
</style>
