<template>
  <div>
    <div class="spjkPop">
      <img src="@/assets/img/video_close.png" alt class="close" @click="close" />
      <div class="title">
        <div class="title_name">
          <slot name="title1">视频监控</slot>
        </div>
      </div>
      <div class="center">
        <div class="center_left">
          <div class="center_left1">
            <div class="center_item1">
              <slot name="title2">视频接入</slot>
            </div>
            <!-- <div class="center_item2">
              <el-input v-model="cameraValue" placeholder="类型" readonly=""></el-input>
              <img src="@/assets/shzl/map/select_icon.png" alt />
            </div>-->
            <div class="center_item3">
              <el-input v-model="cameraValue" placeholder="搜索"></el-input>
              <img src="@/assets/shzl/map/camera/ss.png" alt />
            </div>
            <div class="center_item4">
              <el-tree
                :data="leftTreeData"
                :check-on-click-node="true"
                show-checkbox
                node-key="id"
                :props="defaultProps"
                ref="videoTreeRef"
                default-expand-all
                :filter-node-method="filterNode"
                @check-change="handleCheck"
              ></el-tree>
            </div>
          </div>
        </div>
        <div class="center_right">
          <div class="center_right1">{{ dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss') }}</div>
          <div class="center_right2">
            <div class="center_right2Item">
              <hlsVideo class="video_" :src="testUrl1" :cameraCode="cameraCode1"></hlsVideo>
            </div>
            <div class="center_right2Item">
              <hlsVideo class="video_" :src="testUrl2" :cameraCode="cameraCode2"></hlsVideo>
            </div>
            <div class="center_right2Item">
              <hlsVideo class="video_" :src="testUrl3" :cameraCode="cameraCode3"></hlsVideo>
            </div>
            <div class="center_right2Item">
              <hlsVideo class="video_" :src="testUrl4" :cameraCode="cameraCode4"></hlsVideo>
            </div>
          </div>
        </div>
        <!-- 实时告警 -->
        <div class="ssgj">
          <div class="label">实时告警</div>
          <div class="table">
            <div class="table-head">
              <span v-for="(it, i) of tableHead" :key="i">{{ it }}</span>
            </div>
            <div class="table-body">
              <swiper class="swiper" :options="swiperOption" ref="myBotSwiper">
                <swiper-slide v-for="(it, i) in realTimeAlarmList" :key="i">
                  <div class="item">
                    <span v-for="(item, idx) of it" :key="idx">
                      <i v-if="idx < it.length - 1">{{ item }}</i>
                      <i class="operate" v-else>
                        <div @click="$emit('operate', it, 0)">详情</div>
                        <div @click="$emit('operate', it, 1)">任务下发</div>
                      </i>
                    </span>
                  </div>
                </swiper-slide>
              </swiper>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import hlsVideo from '@/components/leader/hlsVideo'
import LivePlayer from '@liveqing/liveplayer'
import rtc from '@/rhtx/core/index'
// 接口
import { getCameraMakers, getCameraXq, getWrjMarker } from '@/api/hs/hs.api.js'
export default {
  props: {
    leftTreeData: {
      type: Array,
      default: () => [
        {
          id: 1,
          label: '雪亮工程',
          children: [
            {
              id: 4,
              label: '苏庄水库摄像头南'
            },
            {
              id: 5,
              label: '苏庄水库摄像头西'
            }
          ]
        },
        {
          id: 2,
          label: '天网工程',
          children: [
            {
              id: 6,
              label: '灵顺南路21号'
            },
            {
              id: 7,
              label: '梁台街13号'
            }
          ]
        },
        {
          id: 3,
          label: '平安城市',
          children: [
            {
              id: 8,
              label: '小学东南门摄像头'
            },
            {
              id: 9,
              label: '小学西北门摄像头'
            }
          ]
        }
      ]
    }
  },
  components: {
    hlsVideo,
    LivePlayer
  },
  name: 'spjkPop',
  computed: {
    myBotSwiper() {
      return this.$refs.myBotSwiper.$swiper
    }
  },
  data() {
    return {
      title: 1,
      realTimeAlarmList: [
        [1, '摄像头01', '违禁捕鱼', '23/03/12 14:39', '详情 任务下发'],
        [2, '摄像头01', '违禁捕鱼', '23/03/12 14:39', '详情 任务下发'],
        [3, '摄像头01', '违禁捕鱼', '23/03/12 14:39', '详情 任务下发'],
        [4, '摄像头01', '违禁捕鱼', '23/03/12 14:39', '详情 任务下发'],
        [5, '摄像头01', '违禁捕鱼', '23/03/12 14:39', '详情 任务下发'],
        [6, '摄像头01', '违禁捕鱼', '23/03/12 14:39', '详情 任务下发'],
        [7, '摄像头01', '违禁捕鱼', '23/03/12 14:39', '详情 任务下发'],
        [8, '摄像头01', '违禁捕鱼', '23/03/12 14:39', '详情 任务下发'],
        [9, '摄像头01', '违禁捕鱼', '23/03/12 14:39', '详情 任务下发'],
        [10, '摄像头01', '违禁捕鱼', '23/03/12 14:39', '详情 任务下发']
      ],
      tableHead: ['序号', '点位名称', '告警类型', '预警时间', '操作'],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      company2: 2,
      optionsList1: [
        {
          value: 1,
          label: '标清'
        },
        {
          value: 2,
          label: '高清'
        },
        {
          value: 3,
          label: '超清'
        }
      ],
      cameraValue: '',
      // testUrl1: 'http://10.2.32.45:8088/firstfloor/stream1/hls.m3u8',
      // testUrl2: 'http://10.2.32.45:8088/sixthfloor/stream1_01/hls.m3u8',
      // testUrl3: 'http://10.2.32.45:8088/sixthfloor/stream1_01/hls.m3u8',
      // testUrl4: 'http://10.2.32.45:8088/firstfloor/stream2/hls.m3u8',
      testUrl1: '',
      testUrl2: '',
      testUrl3: '',
      testUrl4: '',
      cameraCode1: null,
      cameraCode2: null,
      cameraCode3: null,
      cameraCode4: null,
      rhtxBoxShow: false,
      tempVideoHy: null,
      video1: require('@/assets/leader/video/video1.mp4'),
      video2: require('@/assets/leader/video/video2.mp4'),
      video3: require('@/assets/leader/video/video4.mp4'),
      video4: require('@/assets/leader/video/video8.mp4'),
      swiperOption: {
        autoHeight: true,
        direction: 'vertical',
        spaceBetween: 0,
        autoplay: {
          delay: 2500,
          disableOnInteraction: false,
          autoplayDisableOnInteraction: false
        },
        slidesPerView: 'auto',
        grabCursor: true,
        autoplayDisableOnInteraction: false,
        mousewheelControl: true
      }
    }
  },
  mounted() {
    this.cameraMarker(4, 11, 9, 5)
  },
  methods: {
    handleCheck(data, checked, item) {
      console.log(data, checked, item)
      // console.log(item.checked, item.data.id)
      if (checked) {
        if (data.id === 1) {
          this.cameraMarker(1, 2, 3, 6)
        } else if (data.id === 2) {
          this.cameraMarker(7, 8, 10, 12)
        } else if (data.id === 3) {
          this.cameraMarker(21, 22, 23, 24)
        }
      } else {
        this.cameraMarker(4, 11, 9, 5)
      }
    },
    close() {
      this.$emit('closeEmit')
    },
    titleShow(index) {
      this.title = index
    },
    changeSelect1() {},
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    resetPeople() {
      this.$refs.videoTreeRef.setCheckedKeys([])
      window.sessionStorage.setItem('oldUers', [])
    },
    choosePeople() {
      // console.log(222)
      // console.log(this.$refs.videoTreeRef.getCheckedKeys(true))
      if (!this.tempVideoHy) {
        let oldUers = this.$refs.videoTreeRef.getCheckedKeys(true)
        window.sessionStorage.setItem('oldUers', JSON.parse(JSON.stringify(oldUers)))
        this.testBtnHySp(oldUers)
      } else if (this.tempVideoHy) {
        let newUser = this.filterArr(
          window.sessionStorage
            .getItem('oldUers')
            .split(',')
            .map(item => Number(item)),
          this.$refs.videoTreeRef.getCheckedKeys(true)
        )
        console.log('newUser', newUser)
        this.addUsers(newUser)
      }
    },
    async testBtnHySp(users) {
      this.rhtxBoxShow = true
      rtc.meet.mounted('rhtx_BoxId')
      this.tempVideoHy = await rtc.meet.callPopTempVideo({ users: users })
      this.tempVideoHy.on('meetOff', () => {
        console.log('会议关闭')
        this.rhtxBoxShow = false
        this.tempVideoHy = null
        this.resetPeople()
      })
    },
    addUsers(newUser) {
      console.log('addUsers')
      this.tempVideoHy.addUser(newUser)
    },
    // js找出两个数组中不同元素
    filterArr(arr1, arr2) {
      return arr1.concat(arr2).filter((t, i, arr) => {
        return arr.indexOf(t) === arr.lastIndexOf(t)
      })
    },
    async cameraMarker(a, b, c, d) {
      const res = await getCameraMakers()
      console.log('res111', res)
      if (res && res.length > 0) {
        let filerRes = res.filter(item => item.id)
        console.log('filerRes', filerRes)
        this.cameraXq1(filerRes[a].id)
        this.cameraXq2(filerRes[b].id)
        this.cameraXq3(filerRes[c].id)
        this.cameraXq4(filerRes[d].id)
      } else {
        this.$message({
          message: '未查询到事件信息',
          type: 'warning'
        })
      }
    },
    async cameraXq1(id) {
      const res = await getCameraXq(id)
      if (res && res.body && res.body.data && res.body.data.length > 0 && res.body.data[0].url) {
        this.testUrl1 = res.body.data[0].url
        this.cameraCode1 = id //传给hls组件，当视频播放失败时再次调用
      }
    },
    async cameraXq2(id) {
      const res = await getCameraXq(id)
      if (res && res.body && res.body.data && res.body.data.length > 0 && res.body.data[0].url) {
        this.testUrl2 = res.body.data[0].url
        this.cameraCode2 = id //传给hls组件，当视频播放失败时再次调用
      }
    },
    async cameraXq3(id) {
      const res = await getCameraXq(id)
      if (res && res.body && res.body.data && res.body.data.length > 0 && res.body.data[0].url) {
        this.testUrl3 = res.body.data[0].url
        this.cameraCode3 = id //传给hls组件，当视频播放失败时再次调用
      }
    },
    async cameraXq4(id) {
      const res = await getCameraXq(id)
      if (res && res.body && res.body.data && res.body.data.length > 0 && res.body.data[0].url) {
        this.testUrl4 = res.body.data[0].url
        this.cameraCode4 = id //传给hls组件，当视频播放失败时再次调用
      }
    }
  },
  watch: {
    cameraValue(val) {
      this.$refs.jiedaoTree.filter(val)
    }
  }
}
</script>

<style lang="scss" scoped>
.bg-header {
  position: absolute;
  width: 100%;
  height: 1080px;
  background: rgba(2, 14, 50, 0.75);
  z-index: 999999;
  top: 0;
  left: 0;
}
.spjkPop {
  width: 1846px;
  height: 749px;
  background: rgba(9, 19, 34, 0.9);
  box-shadow: 0px 0px 22px 0px rgba(11, 54, 138, 0.35), 0px 2px 8px 0px rgba(0, 0, 0, 0.7),
    inset 0px 0px 8px 0px rgba(17, 146, 214, 0.35);
  border-radius: 11px;
  border: 1px solid #015c8c;
  position: absolute;
  z-index: 101;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1003;
  .close {
    position: absolute;
    width: 23px;
    height: 23px;
    top: 24px;
    right: 24px;
    cursor: pointer;
  }
  .title {
    width: 634px;
    height: 76px;
    line-height: 32px;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
    background-image: url(~@/assets/img/video_title.png);
    .title_name {
      background-clip: text;
      font-size: 36px;
      font-family: PangMenZhengDao;
      color: #ffffff;
      line-height: 76px;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .center {
    width: 100%;
    height: calc(100% - 76px);
    padding: 28px 39px 33px 23px;
    display: flex;
    gap: 31px;
    .type_ {
      display: flex;
      align-items: center;
      margin: 17px 0 8px 14px;
      & > div {
        width: 76px;
        height: 30px;
        line-height: 30px;
        font-size: 13px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ccf4ff;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        background-position: center;
        background-image: url(~@/assets/shzl/map/camera/type_bg.png);
        &.active {
          background-image: url(~@/assets/shzl/map/camera/type_bg_active.png);
        }
        &:first-of-type {
          margin-right: 10px;
        }
      }
    }
    .center_left {
      width: 246px;
      .center_left1 {
        width: 246px;
        height: 595px;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        background-position: center;
        background-image: url(~@/assets/img/video_leftk.png);
        margin: 8px 0 0 0;
        padding: 12px 0 0 0;
        .center_item1 {
          margin: 0 0 13px 25px;
          font-size: 20px;
          font-family: YouSheBiaoTiHei;
          color: #ffffff;
          line-height: 26px;
          background: linear-gradient(180deg, #eeeeee 0%, #43f4ff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          text-align: left;
        }
        .center_item2 {
          width: 203px;
          height: 34px;
          line-height: 34px;
          color: #fff;
          text-align: left;
          background: rgba(0, 74, 143, 0.4)
            linear-gradient(
              360deg,
              rgba(181, 223, 248, 0) 0%,
              rgba(29, 172, 255, 0.29) 100%,
              #ffffff 100%
            );
          border-radius: 4px;
          border: 1px solid rgba(0, 162, 255, 0.6);
          position: relative;
          margin: 0 auto;
          img {
            position: absolute;
            right: 5px;
            top: 5px;
          }
        }
        .center_item3 {
          width: 203px;
          height: 34px;
          line-height: 34px;
          color: #fff;
          text-align: left;
          background: rgba(0, 74, 143, 0.4)
            linear-gradient(
              360deg,
              rgba(181, 223, 248, 0) 0%,
              rgba(29, 172, 255, 0.29) 100%,
              #ffffff 100%
            );
          border-radius: 4px;
          border: 1px solid rgba(0, 162, 255, 0.6);
          position: relative;
          margin: 16px auto 0;
          img {
            position: absolute;
            right: 9px;
            top: 10px;
          }
        }
        .center_item4 {
          margin: 20px auto 0;
          height: 387px;
          overflow-y: auto;
          width: 203px;
          &::-webkit-scrollbar {
            /*滚动条整体样式*/
            // width: 2px;
            background: transparent;
          }
          // &::-webkit-scrollbar-thumb {
          //   /*滚动条里面小方块*/
          //   border-radius: 2px;
          //   box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
          //   background: rgb(45, 124, 228);
          //   height: 20px;
          // }
        }
      }
    }
    .center_right {
      width: 1068px;
      height: 580px;
      .center_right1 {
        text-align: left;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 20px;
        letter-spacing: 1px;
      }
      .center_right2 {
        width: 1039px;
        height: 595px;
        padding: 3px 0;
        display: flex;
        justify-content: space-between;
        align-content: space-between;
        flex-wrap: wrap;
        .center_right2Item {
          width: 507px;
          height: 273px;
          margin: 5px;
          .video_ {
            width: 505px;
            height: 273px;
          }
        }
      }
    }
    .ssgj {
      text-align: left;
      width: 440px;
      height: 100%;
      .label {
        height: 18px;
        font-size: 18px;
        font-family: PangMenZhengDao;
        line-height: 21px;
        background: linear-gradient(180deg, #eeeeee 0%, #43b1ff 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 20px;
      }
      .table {
        width: 100%;
        height: calc(100% - 38px);
        .table-head {
          display: flex;
          align-items: center;
          height: 44px;
          line-height: 44px;
          background: rgba(0, 101, 183, 0.2);
          span {
            text-align: center;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #37c1ff;
            &:nth-child(1) {
              width: 45px;
            }
            &:nth-child(2) {
              width: 85px;
            }
            &:nth-child(3) {
              width: 70px;
            }
            &:nth-child(4) {
              width: 115px;
            }
            &:nth-child(5) {
              flex: 1;
            }
          }
        }
        .table-body {
          height: calc(100% - 44px);
          .swiper {
            width: 100%;
            height: 100%;
            .swiper-slide {
              height: 44px;
              line-height: 44px;
              &:nth-child(even) {
                background: rgba(0, 101, 183, 0.1);
              }
              .item {
                width: 100%;
                height: 100%;
                display: flex;
                text-align: center;
                span {
                  font-size: 14px;
                  font-family: PingFangSC-Regular, PingFang SC;
                  font-weight: 400;
                  color: #ffffff;
                  &:nth-child(1) {
                    width: 45px;
                  }
                  &:nth-child(2) {
                    width: 85px;
                  }
                  &:nth-child(3) {
                    width: 70px;
                  }
                  &:nth-child(4) {
                    width: 115px;
                  }
                  &:nth-child(5) {
                    flex: 1;
                    font-weight: 500;
                    color: #43b3ff;
                  }
                }
                .operate {
                  display: flex;
                  justify-content: space-evenly;
                }
              }
            }
          }
        }
      }
    }
  }
}
::v-deep .el-tree {
  background: transparent;
  color: #f6feff;
  .el-tree-node__content:hover {
    background: transparent !important;
  }
  .el-checkbox__inner {
    background: transparent !important;
    width: 17px;
    height: 17px;
  }
}
::v-deep .el-tree-node__label {
  font-size: 16px;
}
::v-deep .el-tree-node__content {
  margin-bottom: 10px;
}
::v-deep .el-tree-node:focus > .el-tree-node__content {
  background-color: transparent !important;
}

::v-deep .el-tree-node__content:hover {
  background: transparent !important;
}
::v-deep .el-select {
  width: 71px;
  height: 30px;
  .el-input__inner {
    font-size: 13px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ccf4ff;
    line-height: 18px;
    background: rgba(0, 74, 143, 0.4)
      linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(0, 162, 255, 0.08) 100%, #ffffff 100%);
    border-radius: 15px;
    border: 1px solid rgba(0, 162, 255, 0.6);
    padding: 0 20px 0 12px;
  }
}
::v-deep .el-input__inner {
  background-color: transparent !important;
  border: none;
  color: #ccf4ff;
  height: 34px;
}
</style>
