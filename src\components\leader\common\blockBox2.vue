<template>
  <div class="block" :style="{'height':blockHeight + 'px'}">
    <div :class="['block_title',isListBtns?'title_bg_Btn':'']" @click="clickBulletFrame">
      <span>{{ title }}</span>
      <ul class="btns" v-if="isListBtns">
        <li
          :class="['btnList', currentIndex == index ?'active':'']"
          v-for="(item, index) in textArr"
          :key="index"
          @click.stop="clickchange(index)"
        >
          {{ item }}
        </li>
      </ul>
    </div>
    <div class="content">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    textArr: {
      type: Array,
      default: () => ['社会保险', '住房保险']
    },
    isListBtns: {
      type: Boolean,
      default: true
    },
    blockHeight: {
      type: Number,
      default: 277
    },
    
  },
  data() {
    return {
      currentIndex: 0
    };
  },
  methods: {
    clickchange(index) {
      this.currentIndex = index;
      this.$emit('updateChange', this.currentIndex);
    },
    clickBulletFrame() {
      this.$emit('updateChange2', true);
    }
  }
};
</script>

<style lang="scss" scoped>
.block {
  // height: 277px;
  width: 100%;
  box-sizing: border-box;
  z-index: 101;
  position: relative;
  .block_title {
    width: 100%;
    height: 50px;
    font-size: 18px;
    background: url(~@/assets/leader/img/component/title_bgs.png) no-repeat;
    background-size: 100% 100%;
    font-size: 20px;
    font-family: PangMenZhengDao;
    color: #FFFFFF;
    line-height: 50px;
    letter-spacing: 2px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    &.title_bg_Btn{
      background: url(~@/assets/leader/img/component/title_bg1.png) no-repeat;
      background-size: 100% 100%;
    }
    &>span{
      margin-left: 64px;
    }

    .btns {
      display: flex;
      align-items: center;
      line-height: 50px;
      margin-right: 22px;
      .btnList{
        width: 80px;
        height: 26px;
        font-size: 16px;
        font-family: PangMenZhengDao;
        color: #CAECFF;
        text-shadow: 0px 0px 1px #00132E;
        background: url(~@/assets/leader/img/component/title_btn.png) no-repeat;
        background-size: 100% 100%;
        line-height: 26px;
        cursor: pointer;
        &.active{
          color: #45DAFF;
          text-shadow: 0px 0px 4px rgba(0,175,255,0.5), 0px 0px 1px rgba(0,30,50,0.5);
        }
      }
    }
  }
  .content {
    position: relative;
    height: calc(100% - 50px);
  }
}
</style>
