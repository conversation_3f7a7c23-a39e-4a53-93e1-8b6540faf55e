<template>
  <div class="sj_box">
    <div class="title">
      <p>事件信息</p>
      <div class="del" @click="close"></div>
    </div>
    <div class="content">
      <div class="info">
        <div class="line">
          <div>* 事件标题：</div>
          <div>路面塌陷</div>
        </div>
        <div class="line">
          <div>* 事件类型：</div>
          <div>突发事件</div>
        </div>
        <div class="line">
          <div>* 上报时间：</div>
          <div>2022-06-02 08:56:32</div>
        </div>
        <div class="line">
          <div>* 事发地点：</div>
          <div>南京市鼓楼区宁海路199号</div>
        </div>
        <div class="line">
          <div>* 详情描述：</div>
          <div>XX路路面塌陷</div>
        </div>
        <div class="line">
          <div>* 事件来源：</div>
          <div>视频AI预警</div>
        </div>
        <div class="line">
          <div>* 当前责任人：</div>
          <div>董小伟</div>
        </div>
        <div class="line">
          <div>* 手机号码：</div>
          <div>13098746183</div>
        </div>
        <div class="line">
          <div>* 事件流程：</div>
          <div></div>
        </div>
      </div>
      <div class="process">
        <div>开始</div>
        <div class="ing_">指挥中心</div>
        <div class="">成员单位</div>
        <div class="ing_">结束</div>
      </div>
    </div>
  </div>
</template>

<script type="text/ecmascript-6">
export default {
  name: 'mapSj',
  data() {
    return {}
  },
  created() {},
  mounted() {},
  methods: {
    close() {
      this.$emit('close')
    },
  },
}
</script>

<style lang="scss" scoped>
@font-face {
  font-family: FZSKJWGB1;
  src: url(~@/assets/leader/font/FZSKJWGB1.ttf);
}
.sj_box {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 101;
  width: 351px;
  height: 405px;
  background: url(~@/assets/leader/img/leader_ldzh/map/map_sj_bg.png) no-repeat;
  background-size: 100% 100%;
  padding: 0 0 28px 0;
  // border: 1px solid red;
  .title {
    margin: 20px 29px 25px 31px;
    background: url(~@/assets/leader/img/leader_ldzh/map/map_sj_title.png) no-repeat;
    background-size: 100% 100%;
    width: 291px;
    height: 33px;
    position: relative;
    p {
      font-size: 18px;
      font-family: FZSKJWGB1;
      font-weight: normal;
      color: #22fcff;
      height: 33px;
      letter-spacing: 1px;
      text-shadow: 0px 0px 4px #00353a;
      background: linear-gradient(180deg, #ffffff 0%, #01c3ff 100%);
      -webkit-background-clip: text;
      // -webkit-text-fill-color: transparent;
    }
    .del {
      background: url(~@/assets/leader/img/leader_ldzh/map/map_sj_del.png) no-repeat;
      background-size: 100% 100%;
      width: 29px;
      height: 36px;
      position: absolute;
      right: -12px;
      bottom: 0;
    }
  }
  .content {
    // border: 1px solid red;
    .info {
      padding: 0 34px 0 33px;
      .line {
        height: 14px;
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
        & > div:first-of-type {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 20px;
        }
        & > div:last-of-type {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 20px;
        }
      }
    }
    .process {
      padding: 0 22px 0 21px;
      display: flex;
      & > div {
        width: 80px;
        height: 30px;
        background: url(~@/assets/leader/img/leader_ldzh/map/map_sj_start.png) no-repeat;
        background-size: 100% 100%;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 30px;
        text-align: center;
        &:not(:first-of-type) {
          margin-left: -4px;
        }
        &.ing_ {
          background: url(~@/assets/leader/img/leader_ldzh/map/map_sj_ing.png) no-repeat;
        }
        &.done_ {
          background: url(~@/assets/leader/img/leader_ldzh/map/map_sj_done.png) no-repeat;
        }
      }
    }
  }
}
</style>
