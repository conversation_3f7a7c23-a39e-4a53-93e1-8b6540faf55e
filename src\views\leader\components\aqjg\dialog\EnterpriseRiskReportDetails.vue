<template>
  <div class="ai_waring" v-if="show">
    <div class="title">
      <span>企业风险报告详情</span>
    </div>
    <div class="close_btn" @click="closeEmitai"></div>
    <div class="tabs">
      <div
        class="tab"
        :class="{ active: tabActive === i }"
        v-for="(it, i) of tabs"
        :key="i"
        @click="changeTab(i)"
      >
        <span>{{ it }}</span>
      </div>
    </div>
    <div class="content" v-if="tabActive === 0">
      <twoColumnList :list="data1" />
    </div>
    <div class="content" v-if="tabActive === 1">
      <twoColumnList :list="data2" />
    </div>
    <div class="content" v-else-if="tabActive === 2">
      <div class="table_box">
        <SwiperTable
          :titles="[
            '风险源位置',
            '风险源名称',
            '管理类别',
            '风险代码',
            '主要事故类别',
            '风险点',
            '管控措施'
          ]"
          :widths="['10%', '15%', '15%', '10%', '15%', '20%', '15%']"
          :data="data3"
          :contentHeight="'510px'"
          :isNeedOperate="false"
        ></SwiperTable>
      </div>
    </div>
    <div class="content" v-else-if="tabActive === 3">
      <div class="table_box">
        <SwiperTable
          :titles="['领域类别', '领域详情']"
          :widths="['20%', '80%']"
          :data="data4"
          :contentHeight="'510px'"
          :isNeedOperate="false"
        ></SwiperTable>
      </div>
    </div>
    <div class="content" v-else-if="tabActive === 4">
      <div class="table_box">
        <SwiperTable
          :titles="['序号', '监控名称', '是否在校', '播放监控']"
          :widths="['25%', '25%', '25%', '25%']"
          :data="data5"
          :contentHeight="'510px'"
          :isNeedOperate="true"
          :operateBtn="['播放']"
          @operate="handleOperate"
        ></SwiperTable>
      </div>
    </div>
    <div class="content" v-else-if="tabActive === 5">
      <div class="table_box">
        <SwiperTable
          :titles="['单位名称', '所属区域', '所属街道', '巡查详情', '巡查时间', '巡查结果']"
          :widths="['20%', '15%', '15%', '15%', '20%', '15%']"
          :data="data6"
          :contentHeight="'510px'"
          :isNeedOperate="false"
        ></SwiperTable>
      </div>
    </div>
    <DroneVideo v-model="droneVideoShow" :video-url="videoUrl" />
  </div>
</template>

<script>
import SwiperTable from '../table/SwiperTable.vue'
import { twoColumnList } from '../list'
import { DroneVideo } from '../../zhdd/dialog'

export default {
  name: 'EnterpriseRiskReportDetails',
  components: { SwiperTable, twoColumnList, DroneVideo },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      inputValue: '',
      tabActive: 0,
      // 控制无人机监控显示
      droneVideoShow: false,
      videoUrl: '',
      tabs: ['基础信息', '安全生产信息', '较大及以上风险源', '重点环节', '企业监控', '自查自检'],

      btnsArr: [
        {
          name: '未办结',
          normalBg: require('@/assets/shzl/map/left_n.png'),
          activeBg: require('@/assets/shzl/map/left_a.png')
        },
        {
          name: '已办结',
          normalBg: require('@/assets/shzl/map/right_n.png'),
          activeBg: require('@/assets/shzl/map/right_a.png')
        }
      ],

      data3: [
        [
          '生产车间四楼',
          '可燃性粉尘爆炸',
          '（03）冶金等工贸行业通用',
          '0303',
          '火灾、其他爆炸、中毒和窒息',
          '涉及可燃性粉尘的场所或设备，如产尘车间、粉尘存放场所、集（除）尘系统、气力输送系统、料仓、提升机、粉碎机、散装集尘车等',
          '排风扇，防爆电器'
        ],
        [
          '燃气锅炉房',
          '燃气储存设施、使用设施燃气泄漏',
          '（03）冶金等工贸行业通用',
          '0305',
          '火灾、其他爆炸、中毒和窒息',
          '燃气使用设施泄漏',
          '排风扇，燃气报警器'
        ]
      ],
      data4: [
        [
          '粉尘涉爆企业',
          '粉尘种类：木粉，单班作业人数：10-29人，干式集中除尘系统数量：1套，湿式集中除尘系统数量：0套，抛光(喷砂机)数量：0台，木粉尘单机滤袋式吸除尘装置数量：0台，静电喷涂除尘设备数：0台，净月产生粉尘量：150kg'
        ]
      ],
      data5: [
        ['1', '仁盛木业_1', '是'],
        ['2', '仁盛木业_2', '是'],
        ['3', '仁盛木业_3', '是'],
        ['4', '仁盛木业_4', '是'],
        ['5', '仁盛木业_5', '是'],
        ['6', '仁盛木业_6', '是']
      ],
      // 基础信息
      data1: [
        {
          label: '企业名称: ',
          value: '南京甘汁园股份有限公司'
        },
        {
          label: '企业标签: ',
          value: ''
        },
        {
          label: '企业规模: ',
          value: '规上企业（中型）'
        },
        {
          label: '风险源数量: ',
          value: '2'
        },
        {
          label: '统一社会信用代码: ',
          value: '9232011578790583H'
        },
        {
          label: '法人代表: ',
          value: '蔡铁华'
        },
        {
          label: '法人证件号: ',
          value: '320XXXXXXXXXXX'
        },
        {
          label: '成立时间: ',
          value: '2000'
        },
        {
          label: '注册资金（万元）: ',
          value: '5000'
        },
        {
          label: '主营范围: ',
          value: '食糖、淀粉糖、淀粉及淀粉制品、方便食品（其他方便食品）'
        },
        {
          label: '注册地址: ',
          value: '南京市江宁滨江经济开发区地秀路766号'
        },
        {
          label: '企业建筑面积（万平方米）:',
          value: '2.4'
        },
        {
          label: '安全生产投入占比（%）: ',
          value: '1.04'
        }
      ],
      // 安全生产信息
      data2: [
        {
          label: '生产经营场地地址: ',
          value: '地秀路788号'
        },
        {
          label: '从业人员数量（人): ',
          value: '310'
        },
        {
          label: '外用工人员数量（人): ',
          value: '36'
        },
        {
          label: '企业主要负责人: ',
          value: '蔡智州'
        },
        {
          label: '企业主要负责人联系方式: ',
          value: '159xxxxxxxx'
        },
        {
          label: '是否拥有证书: ',
          value: '有'
        },
        {
          label: '企业安全负责人: ',
          value: '陈明'
        },
        {
          label: '企业安全负责人联系方式: ',
          value: '157xxxxxxxx'
        },
        {
          label: '是否拥有证书: ',
          value: '有'
        },
        {
          label: '安全总监: ',
          value: '有'
        },
        {
          label: '注册安全工程师人数: ',
          value: '0'
        },
        {
          label: '安全管理部门名称: ',
          value: '综保部'
        },
        {
          label: '安全管理人员数: ',
          value: '9人（专职人员数3人、持证0人；兼职人员数6人、持证0人）'
        },
        {
          label: '安全生产标准化建设情况: ',
          value: '三级安全生产标准化企业'
        },
        {
          label: '特种作业人员持证情况（本）: ',
          value: '6'
        },
        {
          label: '履行建设项目安全设置“三同时”手续情况: ',
          value: '否'
        }
      ],
      data6: [
        [
          '爱尔集新能源科技（南京）有限公司',
          '江宁区',
          '湖熟街道',
          '正常',
          '2023-02-20 09:32:47',
          '正常'
        ],
        [
          '爱尔集新能源科技（南京）有限公司',
          '江宁区',
          '湖熟街道',
          '正常',
          '2023-02-20 09:26:38',
          '正常'
        ],
        [
          '爱尔集新能源科技（南京）有限公司',
          '江宁区',
          '湖熟街道',
          '正常',
          '2023-02-20 09:19:44',
          '正常'
        ],
        [
          '爱尔集新能源科技（南京）有限公司',
          '江宁区',
          '湖熟街道',
          '正常',
          '2023-02-20 08:57:17',
          '正常'
        ],
        [
          '爱尔集新能源科技（南京）有限公司',
          '江宁区',
          '湖熟街道',
          '正常',
          '2023-02-20 08:40:37',
          '正常'
        ],
        [
          '爱尔集新能源科技（南京）有限公司',
          '江宁区',
          '湖熟街道',
          '正常',
          '2023-02-20 08:39:03',
          '正常'
        ]
      ]
    }
  },
  computed: {
    show() {
      return this.value
    }
  },
  methods: {
    closeEmitai() {
      this.$emit('input', false)
    },
    dealMethod() {
      console.log(1111)
      this.$parent.dealMethod()
    },
    dwMethod() {
      this.$parent.dwMethod()
    },
    changeTab(i) {
      this.tabActive = i
    },
    handleOperate(i, it) {
      console.log(i, it)
      if (this.tabActive === 4) {
        this.videoUrl = 'http://**********:8088/firstfloor/stream1/hls.m3u8'
        this.droneVideoShow = true
      }
    }
  }
}
</script>

<style lang="less" scoped>
.bg-header {
  position: absolute;
  width: 100%;
  height: 1080px;
  background: rgba(2, 14, 50, 0.75);
  z-index: 999999;
  top: 0;
  left: 0;
}
/deep/ .ivu-select {
  width: 168px;
}
/deep/ .ivu-select-selection {
  width: 168px;
  height: 34px;
  background: rgba(0, 74, 143, 0.4)
    linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
}
/deep/ .ivu-select-selection .ivu-select-selected-value {
  font-size: 13px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 34px;
}

/deep/ .ivu-input {
  background: rgba(0, 74, 143, 0.4)
    linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 22px;
}
/deep/ .ivu-select-placeholder {
  font-size: 13px !important;
  line-height: 34px !important;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff !important;
}
/deep/ .ivu-page-item {
  background: transparent;
  color: #fff;
}
/deep/ .ivu-page-item a {
  color: #fff;
}
/deep/ .ivu-page-prev,
/deep/ .ivu-page-next {
  background: transparent;
}
.ai_waring {
  width: 1435px;
  height: 754px;
  background: rgba(9, 19, 34, 0.95);
  box-shadow: 0px 0px 22px 0px rgba(11, 54, 138, 0.35), 0px 2px 8px 0px rgba(0, 0, 0, 0.7),
    inset 0px 0px 8px 0px rgba(17, 146, 214, 0.35);
  border-radius: 11px;
  border: 1px solid #015c8c;
  padding-bottom: 35px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1003;
  .title {
    margin: 0 auto 23px;
    width: 635px;
    height: 76px;
    line-height: 76px;
    background: url(~@/assets/map/dialog/title2.png) no-repeat;
    display: grid;
    place-items: center;
    span {
      font-size: 36px;
      font-family: PingFangSC, PingFang SC;
      line-height: 36px;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .tabs {
    display: flex;
    gap: 13px;
    margin: 28px 0 42px 28px;
    .tab {
      width: 191px;
      height: 41px;
      line-height: 41px;
      background: url('~@/assets/map/dialog/bg5.png') no-repeat center / 100% 100%;
      cursor: pointer;
      span {
        font-size: 24px;
        font-family: PingFangSC, PingFang SC;
        color: #ffffff;
      }
      &.active {
        background: url('~@/assets/map/dialog/bg4.png') no-repeat center / 100% 100%;
        span {
          font-family: PingFangSC, PingFang SC;
          background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
  }
  .close_btn {
    position: absolute;
    width: 23px;
    height: 23px;
    top: 25px;
    right: 40px;
    background: url(~@/assets/map/dialog/btn3.png) no-repeat;
    cursor: pointer;
  }
  .content {
    padding: 0 30px;
    .btns {
      // border: 1px solid red;
      margin: 0 auto;
      display: flex;
      justify-content: center;
      & > div {
        width: 154px;
        height: 53px;
        span {
          font-size: 24px;
          font-family: PingFangSC, PingFang SC;
          color: #ffffff;
          line-height: 53px;
          background: linear-gradient(180deg, #ffffff 0%, #ffeba0 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
    .check_types {
      text-align: left;
      display: flex;
      .jjcd {
        display: flex;
        align-items: center;
        margin-right: 24px;
        span {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
        }
      }
      .report_time {
        margin-left: 26px;
        display: flex;
        align-items: center;
        span {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
      }
      .istime_out {
        margin-left: 26px;
        align-items: center;
        display: flex;
        span {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
      }
      .type_btns {
        margin-left: 27px;
        display: flex;
        align-items: center;
        & div {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 43px;
          background: url(~@/assets/shzl/map/type_btn.png) no-repeat center / 100% 100%;
          background-size: 100% 100%;
          text-align: center;
          cursor: pointer;
          &:first-of-type {
            width: 98px;
            height: 43px;
          }
          &:nth-of-type(2) {
            margin-left: 20px;
            width: 98px;
            height: 43px;
          }
          &:nth-of-type(3) {
            margin-left: 20px;
            width: 98px;
            height: 43px;
          }
        }
      }
    }
    .table_box {
      margin-top: 31px;
      overflow: hidden;
    }
    .fy_page {
      margin-top: 20px;
    }
  }
}
/deep/ .el-input {
  width: 200px;
  height: 34px;
  .el-input__inner {
    height: 34px;
    background: rgba(0, 74, 143, 0.4)
      linear-gradient(
        360deg,
        rgba(181, 223, 248, 0) 0%,
        rgba(29, 172, 255, 0.29) 100%,
        #ffffff 100%
      );
    border: 1px solid rgba(0, 162, 255, 0.6);
    color: #fff;
    padding: 0 30px 0 15px;
  }
}
</style>
