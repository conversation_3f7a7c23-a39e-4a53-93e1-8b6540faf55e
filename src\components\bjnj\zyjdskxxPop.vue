<template>
  <div v-if="show">
    <div class="ai_waring">
      <div class="title">
        <span>作业进度实况信息</span>
      </div>
      <div class="close_btn" @click="closeEmitai"></div>
      <div class="content">
        <div>
          <div class="check_types" v-if="AreaId == 100000">
            <div class="jjcd">
              <span>省份：</span>
              <el-select v-model="gdObj.service" placeholder="请选择省份" @change="provinceSwitching(item.level)">
                <el-option v-for="item in jobTypeList" :key="item.areaId" :label="item.areaName"
                  :value="item.areaId"></el-option>
              </el-select>
            </div>
            <div class="jjcd">
              <span>发布日期：</span>
              <el-date-picker
                v-model="gdObj.createDatetime"
                type="date"
                placeholder="选择日期">
              </el-date-picker>
            </div>
            <div class="type_btns">
              <div @click="searchBtn">查询</div>
              <div @click="resetBtn">重置</div>
            </div>
          </div>
          <div class="table_box" v-if="AreaId == 100000">
            <SwiperTableMap :titles="[
              '序号',
              '标题',
              '省份',
              '期数',
              '发布日期',
              '操作',
            ]" :widths="['6%', '34%', '15%', '15%', '20%', '10%']" :data="tableList1" :contentHeight="'636px'"
              :settled="settled" @operate="operate" v-if="tableList1.length"></SwiperTableMap>
              <img class="zwsjImg" src="@/assets/bjnj/zwsj.png" v-else>
          </div>
          <div class="table_box" v-else>
            <SwiperTableMap :titles="[
              '序号',
              '标题',
              '期数',
              '发布日期',
              '操作',
            ]" :widths="['6%', '44%', '15%', '25%', '10%']" :data="tableList2" :contentHeight="'636px'"
              :settled="settled" @operate="operate" v-if="tableList2.length"></SwiperTableMap>
              <img class="zwsjImg" src="@/assets/bjnj/zwsj.png" v-else>
          </div>
        </div>
        <div class="fy_page">
          <Page :total="total" :page-size="pageSize" v-model="pageNum" @on-change="pageNumChange" show-total></Page>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SwiperTableMap from './table/RealTimeEventTable6.vue'
// api
import { getEventType, getUnDoneEventList, getDoneEventList, getQgdSssj } from '@/api/hs/hs.api.js'
import {
  queryAreaByLevel,
  workScheduleList
} from '@/api/njzl/zyzx.js'
import { myMixins } from '@/libs/myMixins.js'
import {
  queryPageTEmergencyCenterByCondition,
  areaTree,
  queryPageTDryingCenterByCondition,
  queryPageTAgmachInfoByCondition,
} from '@/api/bjnj/zhdd.js'
import { getCscpBasicHxItemCode } from '@/api/bjnj/zhdd.js'

export default {
  name: 'RealTimeEventDialogQgd',
  mixins: [myMixins],
  components: { SwiperTableMap },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default: () => {
        return []
      }
    },
    userAreaId: {
      type: String,
      default: ''
    },
    AreaId: {
      type: Number,
      default: 100000
    },
  },
  data() {
    return {
      options: [],
      qgdCodesqlxOption: [],
      qgdCodesqlxOptions: [],
      btnList: [
        {
          name: '区域农机社会化服务中心',
        },
        {
          name: '农机应急作业服务队',
        },
        {
          name: '区域农业应急救灾中心',
        },
        {
          name: '机具保有量',
        },
      ],
      tableShow: 0,
      tabs: ['未办结', '办结'],
      btnsArr: [
        {
          name: '未办结',
          normalBg: require('@/assets/shzl/map/left_n.png'),
          activeBg: require('@/assets/shzl/map/left_a.png')
        },
        {
          name: '已办结',
          normalBg: require('@/assets/shzl/map/right_n.png'),
          activeBg: require('@/assets/shzl/map/right_a.png')
        }
      ],
      urgentOptions: [
        {
          value: '0',
          label: '普通'
        },
        {
          value: '1',
          label: '突发'
        },
        {
          value: '2',
          label: '重要'
        },
        {
          value: '3',
          label: '紧急'
        }
      ],
      eventTypeOptions: [
        {
          value: '0',
          label: '普通'
        }
      ],
      timeOutOptions: [
        {
          value: '0',
          label: '是'
        },
        {
          value: '1',
          label: '否'
        }
      ],
      proityValue: '',
      timeOutValue: '',
      settled: true,
      total: 100,
      pageNum: 1,
      pageSize: 10,
      finished: 1,
      tabList1_all: [],
      tabList2_all: [],
      tabList3_all: [],
      tabList4_all: [],
      tableList1: [
      ],
      tableList2: [
      ],
      tableList3: [
      ],
      tableList4: [
      ],
      gdObj: {
        service: '100000'
      },
      jobTypeList: [],
      businessStateList: [],
      params: {},
      areaFlag: 0,
    }
  },
  watch: {
    qgdCodejjcdOptions(val) {
      if (this.qgdCodejjcdOptions.length > 0) {
        // this.getQgdSssj()
      }
    },
    // 防止出现获取不到annexNum
    userAreaId: {
      immediate: true,
      handler(annexNum) {
        // console.log('annexNum', annexNum)
        this.userAreaId = annexNum + ''
        this.areaTree()
        // if (annexNum) {
        //   this.queryAnnexByAnnexNum();
        // }
      }
    }
  },
  computed: {
    show() {
      return this.value
    }
  },
  mounted() {
    // this.areaTree()
    this.queryAreaByLevel()
    // this.workScheduleList()
    // Promise.all([this.getCscpBasicHxItemCode1('jobType'), this.getCscpBasicHxItemCode1('businessState'), this.getCscpBasicHxItemCode1('agmachTypeId')]).then(res => {
    //   this.queryPageTEmergencyCenterByCondition({
    //     area: this.gdObj.area,
    //     current: this.pageNum,
    //     name: this.gdObj.name,
    //     size: this.pageSize,
    //     type: this.tableShow + 1,
    //   })
    // })
  },
  methods: {
    async getCscpBasicHxItemCode1(data) {
      let res = await getCscpBasicHxItemCode(data)
      console.log('res', res)
      console.log('data', data)
      if (res?.code == '0') {
        if (data == 'jobType') {
          this.jobTypeList = res.data
        } else if (data == 'businessState') {
          this.businessStateList = res.data
        } else if (data == 'agmachTypeId') {
          this.qgdCodesqlxOptions = res.data
        }
      }
    },
    async areaTree(data) {
      let res = await areaTree(data)
      // this.options = res
      const fullAreaTree = [res];

      // 根据用户区域信息，在区域树中查找子树
      const getUserAreaTree = (areaList) => {
        if (!areaList) return [];
        const userArea = areaList.find(
          (area) => area.areaId === this.userAreaId
        );
        if (userArea) return [userArea];
        return areaList.flatMap((area) => getUserAreaTree(area.sonArea));
      };
      const userAreaTree = getUserAreaTree(fullAreaTree);

      // 处理区域树数据
      const parseAreaNode = (area) => {
        area.areaName = area.areaName.trim();
        if (area.sonArea) {
          area.sonArea.forEach(parseAreaNode);
        }
      };
      userAreaTree.forEach(parseAreaNode);
      // console.log('userAreaTree', userAreaTree)
      this.options = userAreaTree;
    },
    provinceSwitching(areaFlag) {
      this.areaFlag = areaFlag
    },
    async queryAreaByLevel() {
      let res = await queryAreaByLevel({
        areaId: this.AreaId
      })
      this.jobTypeList = res.data
    },
    async workScheduleList(params) {
      this.params = params
      this.params.areaId = this.gdObj.service
      this.params.day = this.gdObj.createDatetime
      this.params.page = this.pageNum
      this.params.size = this.pageSize
      let res = await workScheduleList(params)
      this.tableList1 = []
      this.tableList1 = [].concat(
        res.data.map((it, index) => [this.pageSize * (this.pageNum - 1) + index + 1, it.title, it.province_area_name, it.num, it.dt_day, it.id,])
      )
      this.total = res.data[0].num
    },
    async queryPageTEmergencyCenterByCondition(data) {
      this.tableList1 = []
      this.tableList2 = []
      let res = await queryPageTEmergencyCenterByCondition(data)
      if (this.tableShow == 0) {
        let records = res.data.records;
        records.forEach(element => {
          if (element.service) {
            element.service = element.service.split(',')
            let arr = []
            element.service.forEach(ele => {
              this.jobTypeList.forEach(item => {
                if (ele == item.itemCode) {
                  arr.push(item.itemValue)
                }
              });
            });
            element.service = arr
            element.service = element.service.join(',')
          }
        });
        this.tabList1_all = res.data.records;
        this.tableList1 = [].concat(
          records.map((it, index) => [this.pageSize * (this.pageNum - 1) + index + 1, it.name, it.contact, it.phone, it.address, it.service,])
        )
        this.total = res.data.total
      } else if (this.tableShow == 1) {
        let records = res.data.records;
        records.forEach(element => {
          if (element.service) {
            element.service = element.service.split(',')
            let arr = []
            element.service.forEach(ele => {
              this.jobTypeList.forEach(item => {
                if (ele == item.itemCode) {
                  arr.push(item.itemValue)
                }
              });
            });
            element.service = arr
            element.service = element.service.join(',')
          }
        });
        this.tabList2_all = res.data.records;
        this.tableList2 = [].concat(
          records.map((it, index) => [this.pageSize * (this.pageNum - 1) + index + 1, it.name, it.contact, it.phone, it.address, it.service,])
        )
        this.total = res.data.total
      }
    },
    async queryPageTEmergencyCenterByCondition2(data) {
      this.tableList3 = []
      let res = await queryPageTEmergencyCenterByCondition(data)
      let records = res.data.records;
      records.forEach(element => {
        if (element.service) {
          element.service = element.service.split(',')
          let arr = []
          element.service.forEach(ele => {
            this.jobTypeList.forEach(item => {
              if (ele == item.itemCode) {
                arr.push(item.itemValue)
              }
            });
          });
          element.service = arr
          element.service = element.service.join(',')
        }
      });
      this.tabList1_all = res.data.records;
      this.tableList3 = [].concat(
        records.map((it, index) => [this.pageSize * (this.pageNum - 1) + index + 1, it.name, it.contact, it.phone, it.address, it.service,])
      )
      this.total = res.data.total
    },
    async queryPageTDryingCenterByCondition(data) {
      this.tableList3 = []
      let res = await queryPageTDryingCenterByCondition(data)
      let records = res.data.records;
      records.forEach(element => {
        this.businessStateList.forEach(ele => {
          if (element.businessState == ele.itemCode) {
            element.businessState = ele.itemValue
          }
        });
      });
      this.tabList3_all = res.data.records;
      this.tableList3 = [].concat(
        records.map((it, index) => [this.pageSize * (this.pageNum - 1) + index + 1, it.name, it.talent, it.contact, it.phone, it.address, it.businessState])
      )
      this.total = res.data.total
    },
    async queryPageTAgmachInfoByCondition(data) {
      this.tableList4 = []
      let res = await queryPageTAgmachInfoByCondition(data)
      let records = res.data.records;
      this.tabList4_all = res.data.records;
      this.tableList4 = [].concat(
        records.map((it, index) => [this.pageSize * (this.pageNum - 1) + index + 1, it.agmachType, it.teamName, it.licensePlateCode, it.contact, it.phone,])
      )
      this.total = res.data.total
    },
    handleChange(value) {
      this.pageNum = 1;
    },
    tableswitch(index) {
      this.pageNum = 1
      this.total = 0;
      this.gdObj = {}
      this.tableShow = index
      // this.switchChangeMethods();
      this.params.areaFlag = this.areaFlag
      this.workScheduleList(this.params)
    },
    switchChangeMethods() {
      if (this.tableShow == 0 || this.tableShow == 1) {
        this.queryPageTEmergencyCenterByCondition({
          area: this.gdObj.area,
          current: this.pageNum,
          name: this.gdObj.name,
          size: this.pageSize,
          type: this.tableShow + 1,
          service: this.gdObj.service,
        })
      } else if (this.tableShow == 2) {
        // this.queryPageTDryingCenterByCondition({
        //   areaId: this.gdObj.area,
        //   current: this.pageNum,
        //   name: this.gdObj.name,
        //   size: this.pageSize,
        // })
        this.queryPageTEmergencyCenterByCondition2({
          area: this.gdObj.area,
          current: this.pageNum,
          name: this.gdObj.name,
          size: this.pageSize,
          service: this.gdObj.service,
          type: 1,
          level: 1,
        })
      } else if (this.tableShow == 3) {
        this.queryPageTAgmachInfoByCondition({
          areaId: this.gdObj.area,
          agmachTypeId: this.gdObj.agmachTypeId,
          licensePlateCode: this.gdObj.licensePlateCode,
          current: this.pageNum,
          name: this.gdObj.name,
          size: this.pageSize,
        })
      }
    },
    operate(it, e) {
      console.log("it", it)
      console.log("e", e)
      this.$emit('handle', e)
    },
    closeEmitai() {
      this.$emit('input', false)
    },
    pageNumChange(val) {
      this.pageNum = val
      this.params.areaFlag = this.areaFlag
      this.workScheduleList(this.params)
      // this.switchChangeMethods()
      // this.queryPageTEmergencyCenterByCondition({
      //   area: this.gdObj.area,
      //   current: this.pageNum,
      //   name: this.gdObj.name,
      //   size: this.pageSize,
      //   type: this.tableShow + 1,
      // })
    },
    async getQgdSssj() {
      this.tableList = []
      let res = await getQgdSssj({
        // street: '1649962979288023040',
        page: this.pageNum,
        size: this.pageSize,
        emeLevel: this.gdObj.jjcdValue,
        appealType: this.gdObj.sqlxValue
      })
      console.log('实时事件弹窗', res)
      console.log(this.qgdCodejjcdOptions)
      if (res?.code == '200' && res.result.data.length > 0) {
        console.log('res.result.data', res.result.data)
        this.tableList = res.result.data.map((it, i) => [
          this.pageSize * (this.pageNum - 1) + i + 1,
          it.orderNum,
          it.emeLevel,
          it.appealContent,
          it.appealType,
          it.community,
          it.eventDate,
          it.formStatus,
          it.eventLocation,
          it.id,
          it
        ])
        this.total = res.result.recordsTotal
      }
      // console.log('this.tableList', this.tableList)
    },
    searchBtn() {
      // this.switchChangeMethods();
      this.params.areaFlag = this.areaFlag
      this.workScheduleList(this.params)
    },
    resetBtn() {
      this.gdObj = {}
      this.pageNum = 1;
      // this.switchChangeMethods();
      this.params.areaFlag = this.areaFlag
      this.workScheduleList(this.params)
    }
  },
}
</script>

<style lang="less" scoped>
.bg-header {
  position: absolute;
  width: 100%;
  height: 1080px;
  background: rgba(2, 14, 50, 0.75);
  z-index: 999999;
  top: 0;
  left: 0;
}


.zwsjImg{
  width: 250px;
  margin: 20px 0;
}
/deep/ .ivu-select {
  width: 189px;
}

/deep/ .ivu-select-selection {
  width: 189px;
  height: 34px;
  background: rgba(0, 74, 143, 0.4) linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
}

/deep/ .ivu-select-selection .ivu-select-selected-value {
  font-size: 13px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 34px;
}

/deep/ .ivu-input {
  background: rgba(0, 74, 143, 0.4) linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 22px;
}

/deep/ .ivu-select-placeholder {
  height: 34px !important;
  font-size: 13px !important;
  line-height: 34px !important;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff !important;
}

/deep/ .ivu-page-item {
  background: transparent;
  color: #fff;
}

/deep/ .ivu-page-item a {
  color: #fff;
}

/deep/ .ivu-page-prev,
/deep/ .ivu-page-next {
  background: transparent;
}

/deep/ .el-input__inner {
  color: #CCF4FF;
}
/deep/ .el-picker-panel {
  color: #CCF4FF;
}

.ai_waring {
  width: 1525px;
  height: 850px;
  background: rgba(9, 19, 34, 0.95);
  box-shadow: 0px 0px 22px 0px rgba(11, 54, 138, 0.35), 0px 2px 8px 0px rgba(0, 0, 0, 0.7),
    inset 0px 0px 8px 0px rgba(17, 146, 214, 0.35);
  border-radius: 11px;
  border: 1px solid #015c8c;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1100;

  .title {
    margin: 0 auto 36px;
    width: 634px;
    height: 72px;
    line-height: 72px;
    background: url(~@/assets/map/dialog/title7.png) no-repeat center / 100% 100%;
    display: grid;
    place-items: center;
    font-family: YouSheBiaoTiHei;

    span {
      display: inline-block;
      font-size: 36px;
      font-family: YouSheBiaoTiHei;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .close_btn {
    position: absolute;
    width: 23px;
    height: 23px;
    top: 24px;
    right: 27px;
    background: url(~@/assets/map/dialog/btn3.png) no-repeat center / 100% 100%;
    cursor: pointer;
  }

  .content {
    padding: 0 30px;

    .btns {
      // border: 1px solid red;
      margin: 0 auto;
      display: flex;
      justify-content: center;

      &>div {
        width: 154px;
        height: 53px;

        span {
          font-size: 20px;
          font-family: YouSheBiaoTiHei;
          color: #ffffff;
          line-height: 53px;
          background: linear-gradient(180deg, #ffffff 0%, #ffeba0 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }

    .check_types {
      text-align: left;
      display: flex;

      .jjcd {
        display: flex;
        align-items: center;
        margin-right: 24px;

        span {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
        }

        /deep/ .el-input {
          width: 189px;
          height: 34px;

          .el-input__inner {
            width: 189px;
            height: 34px;
            background: linear-gradient(180deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #FFFFFF 100%), rgba(0, 74, 143, 0.4);
            border: 1px solid rgba(0, 162, 255, 0.6);
          }
        }
        /deep/ .el-input__inner {
          width: 378px;
          background: linear-gradient(180deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #FFFFFF 100%), rgba(0, 74, 143, 0.4);
          border: 1px solid rgba(0, 162, 255, 0.6);
        }
        /deep/ .el-range-input {
          background: none;
          color: #CCF4FF;
        }
        /deep/ .el-range-separator {
          color: #CCF4FF;
        }
      }

      .report_time {
        margin-left: 26px;
        display: flex;
        align-items: center;

        span {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
      }

      .istime_out {
        margin-left: 26px;
        align-items: center;
        display: flex;

        span {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
      }

      .type_btns {
        margin-left: 27px;
        display: flex;
        align-items: center;

        & div {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 43px;
          background: url(~@/assets/shzl/map/type_btn.png) no-repeat center / 100% 100%;
          background-size: 100% 100%;
          text-align: center;
          cursor: pointer;

          &:first-of-type {
            width: 98px;
            height: 43px;
          }

          &:nth-of-type(2) {
            margin-left: 20px;
            width: 98px;
            height: 43px;
          }

          &:nth-of-type(3) {
            margin-left: 20px;
            width: 98px;
            height: 43px;
          }
        }
      }
    }

    .table_box {
      margin-top: 12px;
      overflow: hidden;
    }

    .fy_page {
      margin-top: 8px;

      /deep/ .ivu-page-total {
        margin-top: 0;
      }
    }

    .tableBox {
      height: 41px;
      margin-bottom: 21px;

      .tableItem {
        float: left;
        width: 180px;
        height: 41px;
        background: url(~@/assets/map/dialog/btn5.png) no-repeat center / 100% 100%;
        margin-right: 10px;
        font-family: PingFangSC, PingFang SC;
        font-size: 22px;
        color: #ffffff;
        line-height: 41px;
        text-align: center;
        font-style: normal;
        text-transform: none;

        &:nth-child(1),
        &:nth-child(2),
        &:nth-child(3) {
          width: 250px;
        }

        span {
          background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          font-family: YouSheBiaoTiHei;
          cursor: pointer;
        }
      }

      .tableActive {
        background: url(~@/assets/map/dialog/btn4.png) no-repeat center / 100% 100%;

        span {
          font-size: 22px;
          color: #ffffff;
          // font-weight: 600;
          background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          font-family: YouSheBiaoTiHei;
        }
      }
    }
  }
}

// /deep/ .ivu-page-total {
//   font-size: 18px;
//   color: #fff;
// }</style>
