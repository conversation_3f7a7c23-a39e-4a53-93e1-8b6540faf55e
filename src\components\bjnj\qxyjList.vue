<template>
	<div class="qxyj-dialog" v-if="show">
		<div class="ai_waring">
			<div class="title">
				<span>气象预警</span>
				<i class="el-icon-close close_btn" @click="closeEmitai"></i>
			</div>
			<div class="content">
				<div class="check_types">
					<div class="jjcd">
						<span>预警时间：</span>
						<el-date-picker
							v-model="gdObj.releaseTime"
							style="border-color:#008aff;color: #008aff;"
							popper-class="elDatePicker"
							type="date"
							placeholder="选择预警时间"
						>
						</el-date-picker>
					</div>
					<div class="jjcd">
						<span>预警类型：</span>
						<!-- <Select v-model="gdObj.type">
              <Option
                v-for="item in qgdCodesqlxOptions"
                :value="item.itemValue"
                :key="item.itemValue"
              >
                {{
                item.itemValue
                }}
              </Option>
            </Select> -->
						<el-select v-model="gdObj.type" placeholder="请选择">
							<el-option
								v-for="item in qgdCodesqlxOptions"
								:key="item.itemValue"
								:label="item.itemValue"
								:value="item.itemValue"
							></el-option>
						</el-select>
					</div>
					<div class="type_btns">
						<div @click="searchBtn">查询</div>
						<div @click="resetBtn">重置</div>
					</div>
				</div>
				<div class="table_box">
					<SwiperTableMap
						:titles="['序号', '预警时间', '预警类型', '预警区域', '预警等级', '预警信息', '操作']"
						:widths="['6%', '14%', '13%', '13%', '14%', '30%', '10%']"
						:data="tableList"
						:contentHeight="'470px'"
						:settled="settled"
						@operate="operate"
					></SwiperTableMap>
				</div>
				<div class="fy_page">
					<!-- <Page :total="total" @on-change="pageNumChange" show-total></Page> -->

					<el-pagination
						@current-change="pageNumChange"
						:current-page="pageNum"
						:page-size="this.pageSize"
						layout="total, prev, pager, next, jumper"
						:total="this.total"
					>
					</el-pagination>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import dayjs from 'dayjs'
import SwiperTableMap from './table/RealTimeEventTable6.vue'
// api
import { getEventType, getUnDoneEventList, getDoneEventList, getQgdSssj } from '@/api/hs/hs.api.js'
import { myMixins } from '@/libs/myMixins.js'
import { getAlarm, getCscpBasicHxItemCode } from '@/api/bjnj/zhdd.js'

export default {
	name: 'RealTimeEventDialogQgd',
	mixins: [myMixins],
	components: { SwiperTableMap },
	props: {
		value: {
			type: Boolean,
			default: false,
		},
		list: {
			type: Array,
			default: () => {
				return []
			},
		},
		areaId: {
			type: String,
			default: '',
		},
		dtDay: {
			type: String,
			default: '',
		},
	},
	data() {
		return {
			qgdCodesqlxOptions: [],
			tabActive: 0,
			tabs: ['未办结', '办结'],
			btnsArr: [
				{
					name: '未办结',
					normalBg: require('@/assets/shzl/map/left_n.png'),
					activeBg: require('@/assets/shzl/map/left_a.png'),
				},
				{
					name: '已办结',
					normalBg: require('@/assets/shzl/map/right_n.png'),
					activeBg: require('@/assets/shzl/map/right_a.png'),
				},
			],
			urgentOptions: [
				{
					value: '0',
					label: '普通',
				},
				{
					value: '1',
					label: '突发',
				},
				{
					value: '2',
					label: '重要',
				},
				{
					value: '3',
					label: '紧急',
				},
			],
			eventTypeOptions: [
				{
					value: '0',
					label: '普通',
				},
			],
			timeOutOptions: [
				{
					value: '0',
					label: '是',
				},
				{
					value: '1',
					label: '否',
				},
			],
			proityValue: '',
			timeOutValue: '',
			settled: true,
			total: 0,
			pageNum: 1,
			pageSize: 10,
			finished: 1,
			tableList: [],
			gdObj: {
				jjcdValue: '',
				sqlxValue: '',
				releaseTime: '',
			},
			regionCode: '',
		}
	},
	watch: {
		qgdCodejjcdOptions(val) {
			if (this.qgdCodejjcdOptions.length > 0) {
				// this.getQgdSssj()
			}
		},
		// 防止出现获取不到annexNum
		// areaId: {
		//   immediate: true,
		//   handler(annexNum) {
		//     console.log('annexNum111', annexNum)
		//     this.getAlarm()
		//   }
		// },
		value: {
			immediate: true,
			handler(annexNum) {
				this.gdObj.releaseTime = this.dtDay
				this.getAlarm()
			},
		},
	},
	computed: {
		show() {
			return this.value
		},
	},
	mounted() {
		this.getCscpBasicHxItemCode1()
		// this.getAlarm()
	},
	methods: {
		async getCscpBasicHxItemCode1() {
			let res = await getCscpBasicHxItemCode('weatherAlarmType')
			console.log(res)
			if (res?.code == '0') {
				this.qgdCodesqlxOptions = res.data
			}
		},
		async getAlarm() {
			console.log(this.dtDay)
			console.log(this.gdObj.releaseTime)
			this.tableList = []
			let res = await getAlarm({
				regionCode: this.areaId,
				pageNo: this.pageNum,
				pageSize: this.pageSize,
				type: this.gdObj.type,
				releaseTime: this.gdObj.releaseTime ? dayjs(this.gdObj.releaseTime).format('YYYY-MM-DD') : '',
			})
			console.log('getAlarm', res)
			if (res?.code == '0') {
				this.tableList = res.data.alarm.map((it, i) => [
					this.pageSize * (this.pageNum - 1) + i + 1,
					it.time,
					it.type,
					it.area,
					it.level,
					it.detail,
					it.title,
				])
				this.total = res.data.total
			}
		},
		operate(i, it) {
			this.$emit('operate', i, it)
		},
		closeEmitai() {
			this.gdObj = {
				releaseTime: this.dtDay,
				type: '',
			}
			this.pageNum = 1
			this.pageSize = 10
			this.$emit('input', false)
		},
		pageNumChange(val) {
			this.pageNum = val
			this.getAlarm()
		},
		async getQgdSssj() {
			this.tableList = []
			let res = await getQgdSssj({
				// street: '1649962979288023040',
				page: this.pageNum,
				size: this.pageSize,
				emeLevel: this.gdObj.jjcdValue,
				appealType: this.gdObj.sqlxValue,
			})
			console.log('实时事件弹窗', res)
			console.log(this.qgdCodejjcdOptions)
			if (res?.code == '200' && res.result.data.length > 0) {
				console.log('res.result.data', res.result.data)
				this.tableList = res.result.data.map((it, i) => [
					this.pageSize * (this.pageNum - 1) + i + 1,
					it.orderNum,
					it.emeLevel,
					it.appealContent,
					it.appealType,
					it.community,
					it.eventDate,
					it.formStatus,
					it.eventLocation,
					it.id,
					it,
				])
				this.total = res.result.recordsTotal
			}
			// console.log('this.tableList', this.tableList)
		},
		searchBtn() {
			this.getAlarm()
		},
		resetBtn() {
			this.gdObj = {
				releaseTime: this.dtDay,
				type: '',
			}
			this.getAlarm()
		},
	},
}
</script>
<style lang="less">
.elDatePicker.el-picker-panel {
	color: #fff; //设置当前面板的月份的字体为白色，记为1
	background: #002450; //定义整体面板的颜色
	border: 1px solid #1384b4; //定义整体面板的轮廓
	.el-input__inner {
		background: #002450;
		color: #fff;
	}
	.el-picker-panel__icon-btn {
		//设置年份月份调节按钮颜色，记为2
		color: #ffffff;
	}
	.el-date-picker__header-label {
		//设置年月显示颜色，记为3
		color: #ffffff;
	}
	.el-date-table th {
		//设置星期颜色，记为4
		color: #ffffff;
	}
	.el-picker-panel__footer {
		background: #002450;
	}
}
</style>
<style lang="less" scoped>
.bg-header {
	position: absolute;
	width: 100%;
	height: 1080px;
	background: rgba(2, 14, 50, 0.75);
	z-index: 999999;
	top: 0;
	left: 0;
}
/deep/ .ivu-select {
	width: 189px;
}
/deep/ .ivu-select-selection {
	width: 189px;
	height: 34px;
	background: rgba(0, 74, 143, 0.4) linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
	border: 1px solid rgba(0, 162, 255, 0.6);
}
/deep/ .ivu-select-selection .ivu-select-selected-value {
	font-size: 13px;
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	color: #ccf4ff;
	line-height: 34px;
}

/deep/ .ivu-input {
	background: rgba(0, 74, 143, 0.4) linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
	border: 1px solid rgba(0, 162, 255, 0.6);
	font-size: 16px;
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	color: #ccf4ff;
	line-height: 22px;
}
/deep/ .ivu-select-placeholder {
	height: 34px !important;
	font-size: 13px !important;
	line-height: 34px !important;
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	color: #ccf4ff !important;
}
/deep/ .ivu-page-item {
	background: transparent;
	color: #fff;
}
/deep/ .ivu-page-item a {
	color: #fff;
}
/deep/ .ivu-page-prev,
/deep/ .ivu-page-next {
	background: transparent;
}
/deep/ .el-input__inner {
	color: #ccf4ff;
}

.qxyj-dialog {
	width: 1920px;
	height: 1080px;
	position: absolute;
	left: 0;
	top: 0;
	background-color: rgba(0, 0, 0, 0.3);
	z-index: 1104;
	display: flex;
	justify-content: center;
	align-items: center;
}

.ai_waring {
	width: 1367px;
	height: 690px;
	background: #001c40;

	border: 1px solid #023164;
	z-index: 1100;
	.title {
		width: 100%;
		height: 48px;
		line-height: 48px;
		// background: url(~@/assets/map/dialog/title7.png) no-repeat;

		padding: 0 16px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background: #104993;
		span {
			font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
			font-weight: normal;
			font-size: 16px;
			color: #d0deee;
			line-height: 16px;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}
		.close_btn {
			font-size: 14px;
			color: #80caff;
			cursor: pointer;
		}
	}

	.content {
		padding: 26px 16px 24px 16px;

		.btns {
			// border: 1px solid red;
			margin: 0 auto;
			display: flex;
			justify-content: center;
			& > div {
				width: 154px;
				height: 53px;
				span {
					font-size: 24px;
					font-family: PingFangSC, PingFang SC;
					color: #ffffff;
					line-height: 53px;
					background: linear-gradient(180deg, #ffffff 0%, #ffeba0 100%);
					background-clip: text;
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
				}
			}
		}
		.check_types {
			text-align: left;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			.jjcd {
				width: 256px;
				height: 32px;
				display: flex;
				align-items: center;
				margin-right: 24px;

				font-size: 14px;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				line-height: 14px;
				color: #ffffff;
				span {
					width: 70px;
					font-size: 14px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					line-height: 14px;
					color: #ffffff;
					margin-right: 5px;
				}
				/deep/ .el-input {
					width: 181px;
					height: 32px;
					.el-input__inner {
						width: 181px !important;
						height: 32px !important;
						// background: linear-gradient(180deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%),
						// 	rgba(0, 74, 143, 0.4);
						background-color: #001c40 !important;
						border: 1px solid #d9d9d9 !important;
						border-radius: 4px 4px 4px 4px !important;

						font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
						font-weight: normal;
						font-size: 14px !important;
						color: #6c8097 !important;
						line-height: 14px;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}
				}

				/deep/ .el-select {
					width: 181px !important;
					height: 32px !important;
				}
			}

			.type_btns {
				display: flex;
				align-items: center;

				& div {
					width: 60px;
					height: 32px;
					font-size: 16px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					color: #ffffff;
					line-height: 32px;
					border-radius: 2px 2px 2px 2px;
					border: 1px solid #d0deee;
					display: flex;
					justify-content: center;
					align-items: center;
					font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
					font-weight: normal;
					font-size: 14px;
					line-height: 14px;
					text-align: center;
					font-style: normal;
					text-transform: none;
					margin: 0 4px;
					cursor: pointer;
				}

				& div:first-child {
					background-color: #001c40;
					color: #d0deee;
				}

				& div:last-child {
					background-color: #159aff;
					color: #d0deee;
				}
			}
			.report_time {
				margin-left: 26px;
				display: flex;
				align-items: center;
				span {
					font-size: 16px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					color: #ffffff;
					line-height: 22px;
				}
			}
			.istime_out {
				margin-left: 26px;
				align-items: center;
				display: flex;
				span {
					font-size: 16px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					color: #ffffff;
					line-height: 22px;
				}
			}
		}
		.table_box {
			margin-top: 31px;
			height: 440px;
			overflow-y: hidden;

			&::-webkit-scrollbar {
				width: 4px; /* 滚动条宽度 */
			}

			//轨道
			&::-webkit-scrollbar-track {
				background: #04244e; /* 滚动条轨道背景 */
				border-radius: 10px;
				padding: 5px 0;
			}

			//滑块
			&::-webkit-scrollbar-thumb {
				background: #82aed0; /* 滑块背景颜色 */
				border-radius: 10px;
				height: 15px;
			}

			//悬停滑块颜色
			&::-webkit-scrollbar-thumb:hover {
				background: #ff823b; /* 鼠标悬停时滑块颜色 */
			}

			//箭头
			&::-webkit-scrollbar-button {
				display: none; /* 隐藏滚动条的箭头按钮 */
			}

			.czBtn {
				display: inline-block;
				button {
					margin: 0 8px !important;
				}
			}


			/deep/ .table-header {
				// position: sticky;
				// top: -1px; /* 根据布局调整 */
				// z-index: 10;
				// background-color: #001C40;
				background-color: #023164 !important;
				border: none !important;
				span{
					border-right: none !important;
				}
			}

			/deep/ .el-table th.el-table__cell {
				background-color: rgba(0, 0, 0, 0);
			}
			/deep/ .el-table tr {
				background-color: rgba(0, 0, 0, 0);
			}
			/deep/ .el-table td.el-table__cell,
			/deep/ .el-table th.el-table__cell.is-leaf {
				// border-bottom: none;

				border-bottom: 1px solid #263e5d !important;
			}
			/deep/ .el-table,
			/deep/ .el-table__expanded-cell {
				background-color: rgba(0, 0, 0, 0);
			}
			/deep/ .el-table__header-wrapper {
				background-color: #023164;
				// border-bottom: 1px solid rgba(255, 255, 255, 0.15);
			}
			/deep/ .el-table th.el-table__cell.is-leaf {
				// border-right: 1px solid #2968c7;
			}
			/deep/ .el-table th.el-table__cell > .cell {
				text-align: left;

				font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
				font-weight: normal;
				font-size: 14px;
				color: #159aff;
				line-height: 14px;
				font-style: normal;
				text-transform: none;
			}
			/deep/ .el-table .cell {
				text-align: left;
				-webkit-user-select: text;
				-moz-user-select: text;
				-ms-user-select: text;
				user-select: text;
			}

			/deep/ .el-table .el-checkbox__inner {
				border: 1px solid rgba(0, 162, 255, 0.6) !important;
				background-color: #001c40 !important;
			}

			/deep/ .el-table__row {
			}
			/deep/ .el-table__row:nth-child(odd) {
			}
			/deep/ .el-table__body tr.hover-row > td.el-table__cell {
				background-color: rgba(0, 0, 0, 0);
			}

			/deep/ .el-table__body-wrapper {
				height: calc(100% - 48px);

				overflow: auto;
				&::-webkit-scrollbar {
					width: 4px; /* 滚动条宽度 */
					height: 4px;
				}

				//轨道
				&::-webkit-scrollbar-track {
					background: #04244e; /* 滚动条轨道背景 */
					border-radius: 10px;
					padding: 5px 0;
				}

				//滑块
				&::-webkit-scrollbar-thumb {
					background: #82aed0; /* 滑块背景颜色 */
					border-radius: 10px;
					height: 15px;
				}

				//悬停滑块颜色
				&::-webkit-scrollbar-thumb:hover {
					background: #ff823b; /* 鼠标悬停时滑块颜色 */
				}

				//箭头
				&::-webkit-scrollbar-button {
					display: none; /* 隐藏滚动条的箭头按钮 */
				}
			}
		}
		.fy_page {
			margin-top: 24px;
			display: flex;
			justify-content: flex-end;
			align-items: center;

			/deep/ .el-pager {
				margin: 0 8px;

				background: #001c40;
				li {
					// width: 32px;
					height: 32px;
					border-radius: 2px;
					padding: 10px !important;
					line-height: 13px !important;
					&.active {
						color: #ffffff;
						background: #159aff;
						border-radius: 2px 2px 2px 2px;
					}
				}

				.number,
				.more {
					// width: 32px;
					height: 32px;
					color: #ffffff;
					padding: 10px 12px !important;
					border-radius: 2px 2px 2px 2px;
					border: 1px solid #dcdee2;
					background-color: #001c40;

					&:hover {
						color: #ffffff;
						background: #159aff;
					}
					&:before {
						line-height: 14px;
					}
				}
			}

			/deep/ .el-pagination {
				display: flex;
				justify-content: flex-start;
				align-items: center;
			}

			/deep/ .el-pagination span {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #d0deee;
				text-align: left;
				font-style: normal;
				text-transform: none;
				&:not {
				}
			}

			/deep/ .el-pagination button {
				width: 32px;
				height: 32px;
				font-family: Helvetica Neue, Helvetica Neue;
				font-weight: 400;
				font-size: 14px;
				background: #001c40;
				color: #d0deee;
				line-height: 22px;
				text-align: center;
				font-style: normal;
				text-transform: none;
				border: 1px solid #dcdee2;
				border-radius: 2px;
				padding: 10px 8px !important;
			}

			/deep/ .el-pagination__jump {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #d0deee;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}
			/deep/ .el-pagination__editor.el-input {
				width: 48px;
				margin: 0 8px;
			}

			/deep/ .el-input__inner {
				border-radius: 2px 2px 2px 2px;
				border: 1px solid #dcdee2;
				background-color: #001c40;
				color: #d0deee;
			}
		}
	}
}
// /deep/ .ivu-page-total {
//   font-size: 18px;
//   color: #fff;
// }
</style>
