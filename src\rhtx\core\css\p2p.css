.rtc-media {
  background: var(--p2p-bg-color);
}

.rtc-media[data-mouted='true'] {
  position: absolute;
  top: 0%;
  left: 0%;
  height: 350px;
  width: 302px;
}

.rtc-media[data-mouted='false'] {
  position: relative;
  height: 100%;
  width: 100%;
}

.rtc-media .receiver-report[data-show='info_show'] {
  display: block;
}

.rtc-media .receiver-report[data-show='info_hide'] {
  display: none;
}

.rtc-media .container {
  height: 100%;
  width: 100%;
}

.rtc-media .container .yuyin {
  color: #fff;
  font-size: 50px;
}

.rtc-media .loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--p2p-bg-text-color);
}

.rtc-media:hover .video-header {
  display: flex;
}

.rtc-media:hover .actions {
  display: flex;
}

.rtc-media video {
  background-color: #000;
  height: 100%;
  width: 100%;
}

.rtc-media .video-header {
  display: none;
  position: absolute;
  top: 0;
  width: 100%;
  color: var(--p2p-header-bg-txt-color);
  background-color: var(--p2p-header-bg-color);
  align-items: center;
  justify-content: space-between;
  padding: 4px;
}

.rtc-media .actions {
  display: none;
  position: absolute;
  bottom: 0px;
  width: 100%;
  height: 35px;
  align-items: center;
  justify-content: space-between;
}

.rtc-media .actions .action-btn {
  flex: 1;
  height: 100%;
  background-color: var(--p2p-action-bg-color);
  color: var(--p2p-action-text-color);
}

.rtc-media .actions .action-btn .iconfont {
  color: #fff;
  font-size: 16px;
  margin-right: 1px;
}
