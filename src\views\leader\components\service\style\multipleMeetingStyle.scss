.layout1 {
  display: flex;
  align-items: center;
  justify-content: center;
  .participant {
    width: 100%;
    height: 100%;
  }
}
.layout2 {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: center;
  .participant {
    width: 600px;
    height: 339px;
  }
}
.layout3 {
  display: flex;
  flex-wrap: wrap;
  align-content: center;
  justify-content: center;
  overflow-y: auto;
  .participant {
    width: 600px;
    height: 339px;
  }
}
.layout4 {
  display: flex;
  flex-wrap: wrap;
  align-content: center;
  justify-content: center;
  overflow-y: auto;
  .participant {
    width: 396px;
    height: 224px;
  }
}
.layout5 {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  overflow-y: auto;
  .participant {
    width: 396px;
    height: 224px;
  }
}
// participant公共样式
.participant {
  // margin: 5px;
  border-radius: 8px;
  position: relative;
  background-color: #000;
  .p-video {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 8px;
  }
  .board {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 0;
    top: 0;
    background: #F1F6FC;
    border-radius: 8px;
    border: 1px solid #316ABE;
    &-avatar {
      width: 22%;
      height: auto;
      border-radius: 50%;
      aspect-ratio: 1/1;
    }
    &-icon {
      width: 22%;
      height: auto;
      border-radius: 50%;
      aspect-ratio: 1/1;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #316abe;
      // width: 54px;
      // height: 54px;
      // margin-right: 20px;
      // border-radius: 50%;
      font-size: 24px;
      color: #FFFFFF;
    }
    &-img {
      width: 22%;
      height: auto;
      border-radius: 50%;
      aspect-ratio: 1/1;
      background: url('~@/assets/service/bohao.png') no-repeat center / 100% 100%;
    }
    .describe {
      background: rgba(0,0,0,0.4);
    }
  }
  .describe {
    z-index: 1;
    position: absolute;
    padding: 0 9px 0 10px;
    max-width: 70%;
    display: flex;
    align-items: center;
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #FFFFFF;
    left: 0px;
    bottom: 0px;
    background: rgba(0,0,0,0.4);
    border-radius: 8px;
    .microphone {
      width: 11px;
      height: 16px;
      margin-right: 6px;
      &-active {
        background: url('~@/assets/service/active-micro.png') no-repeat center / 100% 100%;
      }
      &-inactive {
        background: url('~@/assets/service/ban-micro.png') no-repeat center / 100% 100%;
      }
    }
    .identity {
      white-space: nowrap;
    }
  }
}