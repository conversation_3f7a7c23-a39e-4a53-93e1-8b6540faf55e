<style>
body {
  word-wrap: break-word;
  background: #f5f5f5 center top no-repeat;
  background-size: auto 680px;
}
pre {
  white-space: pre-wrap;
}
a {
  text-decoration: none;
  color: #06c;
}
a:hover {
  color: #f00;
}

.main {
  max-width: 700px;
  margin: 0 auto;
  padding-bottom: 80px;
}

.mainBox {
  margin-top: 12px;
  padding: 12px;
  border-radius: 6px;
  background: #fff;
  --border: 1px solid #0b1;
  box-shadow: 2px 2px 3px #aaa;
}
.ylLine_box {
  border-radius: 6px;
  /* background: #fff; */
  position: relative;
  bottom: 160px;
  left: 320px;
  background: url(~@/assets/leader/img/zwfw/bg12.png) no-repeat center / 100% 100%;
  background-size: 80% 80%;
  z-index: 999999;
}
.ylLine {
  height: 100px;
  width: 170px;
  box-sizing: border-box;
  display: inline-block;
  vertical-align: bottom;
  padding: 0 0px 0 2px;
  overflow: hidden;
}
.btns button {
  display: inline-block;
  cursor: pointer;
  border: none;
  border-radius: 3px;
  background: #2d8cf0;
  color: #fff;
  padding: 0 15px;
  margin: 3px 20px 3px 0;
  line-height: 36px;
  height: 36px;
  overflow: hidden;
  vertical-align: middle;
}
.btns button:active {
  background: #2d8cf0;
}
.pd {
  padding: 0 0 6px 0;
}
.lb {
  display: inline-block;
  vertical-align: middle;
  background: #2d8cf0;
  color: #fff;
  font-size: 14px;
  padding: 2px 8px;
  border-radius: 99px;
}
</style>


<template>
  <div class="main">
    <slot name="top"></slot>

    <div class="mainBox" style="height: 0; opacity: 0">
      <div class="btns">
        <div>
          <button ref="recordButton" @click="recOpen">开启录音连接</button>
          <button @click="recClose">关闭录音</button>
        </div>

        <button @click="recStart">开始录制</button>
        <!--        <button @click="recStop" style="margin-right:80px">停止</button>-->
        <!--			</span>-->
        <!--        <span style="display: inline-block;">-->
        <!--				<button @click="recPlayLast">播放</button>-->
        <!--			</span>-->
      </div>
    </div>

    <div class="ylLine_box" :style="{ opacity: powerLevel > 50 && !soundFlag  ? 1 : 0 }">
    <!-- <div class="ylLine_box" style="opacity: 1"> -->
      <div style="" class="ctrlProcessWave ylLine"></div>
      <!--      <div style="height:40px;width:300px;display:inline-block;background:#999;position:relative;vertical-align:bottom">-->
      <!--        <div class="ctrlProcessX" style="height:40px;background:#0B1;position:absolute;" :style="{width:powerLevel+'%'}"></div>-->
      <!--        <div class="ctrlProcessT" style="padding-left:50px; line-height:40px; position: relative;">{{ duration+"/"+powerLevel }}</div>-->
      <!--      </div>-->
    </div>

    <div class="mainBox" style="height: 0; opacity: 0">
      <audio ref="LogAudioPlayer" style="width: 100%"></audio>

      <div class="mainLog">
        <div v-for="obj in logs" :key="obj.idx">
          <div :style="{ color: obj.color == 1 ? 'red' : obj.color == 2 ? 'green' : obj.color }">
            <!-- <template v-once> 在v-for里存在的bug，参考：https://v2ex.com/t/625317 -->
            <span v-once>[{{ getTime() }}]</span><span v-html="obj.msg" />

            <template v-if="obj.res">
              {{ intp(obj.res.rec.set.bitRate, 3) }}kbps {{ intp(obj.res.rec.set.sampleRate, 5) }}hz
              编码{{ intp(obj.res.blob.size, 6) }}b [{{ obj.res.rec.set.type }}]{{
                intp(obj.res.duration, 6)
              }}ms

              <button @click="recdown(obj.idx)">下载</button>
              <button @click="recplay(obj.idx)">播放</button>

              <span v-html="obj.playMsg"></span>
              <span v-if="obj.down">
                <span style="color: red">{{ obj.down }}</span>

                没弹下载？试一下链接或复制文本<button @click="recdown64(obj.idx)">
                  生成Base64文本
                </button>

                <textarea v-if="obj.down64Val" v-model="obj.down64Val"></textarea>
              </span>
            </template>
          </div>
        </div>
      </div>
    </div>

    <div
      v-if="recOpenDialogShow"
      style="
        z-index: 99999;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        position: fixed;
        background: rgba(0, 0, 0, 0.3);
      "
    >
      <div style="display: flex; height: 100%; align-items: center">
        <div style="flex: 1"></div>
        <div style="width: 240px; background: #fff; padding: 15px 20px; border-radius: 10px">
          <div style="padding-bottom: 10px">
            录音功能需要麦克风权限，请允许；如果未看到任何请求，请点击忽略~
          </div>
          <div style="text-align: center">
            <a @click="waitDialogClick" style="color: #0b1">忽略</a>
          </div>
        </div>
        <div style="flex: 1"></div>
      </div>
    </div>

    <slot name="bottom"></slot>
  </div>
</template>
<script>
//加载必须要的core
import Recorder from 'recorder-core'
//需要使用到的音频格式编码引擎的js文件
import 'recorder-core/src/engine/wav'
import * as socketApi from '@/libs/socket'
//可选的扩展
import 'recorder-core/src/extensions/waveview'

export default {
  props: {
    lying: {
      type: Boolean,
      default: false,
    },
    soundFlag: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      Rec: Recorder,

      type: 'wav',
      bitRate: 16,
      sampleRate: 16000,

      rec: 0,
      duration: 0,
      powerLevel: 0,
      TalkChunk: null,
      TalkChunks: [],
      isTalkSend: true,
      recOpenDialogShow: 0,
      logs: [],
    }
  },
  created() {
    this.recClose()
  },
  methods: {
    recOpen: function () {
      var This = this
      This.TalkChunks = []
      This.TalkChunk = null
      This.isTalkSend = true
      var rec = (this.rec = Recorder({
        type: This.type,
        bitRate: This.bitRate,
        sampleRate: This.sampleRate,
        onProcess: function (buffers, powerLevel, duration, sampleRate) {
          This.duration = duration
          This.powerLevel = powerLevel
          console.log('powerLevel', powerLevel)
          if (powerLevel > 50 && !This.soundFlag) {
            This.$parent.lyingMethod()
          }
          This.wave.input(buffers[buffers.length - 1], powerLevel, sampleRate)

          if (socketApi.question && This.socketQuestion != socketApi.question) {
            This.socketQuestion = socketApi.question
            let answerJson = JSON.parse(This.socketQuestion)
            console.info(answerJson)
            This.$emit('msgResult', answerJson)
            This.TalkSend(buffers, sampleRate, false)
          } else {
            This.TalkSend(buffers, sampleRate, false)
          }
        },
      }))

      // This.dialogInt=setTimeout(function(){
      //   This.showDialog();
      // },800);

      rec.open(
        function () {
          This.dialogCancel()
          This.reclog(
            '已打开:' + This.type + ' ' + This.sampleRate + 'hz ' + This.bitRate + 'kbps',
            2
          )

          This.wave = Recorder.WaveView({ elem: '.ctrlProcessWave' })
          // 调用开始录音
          if (!This.rec || !Recorder.IsOpen()) {
            This.reclog('未打开录音', 1)
            This.$refs.recordButton.dispatchEvent(new MouseEvent('click'))
            return
          }
          This.rec.start()
          socketApi.initWebSocket()
        },
        function (msg, isUserNotAllow) {
          This.dialogCancel()
          This.reclog((isUserNotAllow ? 'UserNotAllow，' : '') + '打开失败：' + msg, 1)
        }
      )

      This.waitDialogClickFn = function () {
        This.dialogCancel()
        This.reclog('打开失败：权限请求被忽略，用户主动点击的弹窗', 1)
      }
    },
    TalkSend(buffers, bufferSampleRate, isClose) {
      // console.log(9999);
      if (!this.isTalkSend) {
        return
      }
      if (this.TalkChunks == []) {
        this.TalkChunk = null
      }
      var pcm = []
      var pcmSampleRate = 0
      if (buffers.length > 0) {
        //借用SampleData函数进行数据的连续处理，采样率转换是顺带的，得到新的pcm数据
        var chunk = Recorder.SampleData(buffers, bufferSampleRate, 16000, this.TalkChunk)
        //清理已处理完的缓冲数据，释放内存以支持长时间录音，最后完成录音时不能调用stop，因为数据已经被清掉了
        for (var i = this.TalkChunk ? this.TalkChunk.index : 0; i < chunk.index; i++) {
          buffers[i] = null
        }
        //此时的chunk.data就是原始的音频16位pcm数据（小端LE），直接保存即为16位pcm文件
        this.TalkChunk = chunk
        pcm = chunk.data
        pcmSampleRate = chunk.sampleRate
        if (pcmSampleRate != 16000) {
          throw new Error('error pcmSampleRate:' + pcmSampleRate + '!=16000')
        }
        // console.log("pcm.length="+pcm.length);
      }
      if (pcm && pcm.length > 0) {
        //将pcm数据丢进缓冲，凑够一帧发送，缓冲内的数据可能有多帧，循环切分发送
        this.TalkChunks.push({ pcm: pcm, pcmSampleRate: pcmSampleRate })
      }
      //从缓冲中切出一帧数据
      var chunkSize = 480 //8位时需要的采样数和帧大小一致，16位时采样数为帧大小的一半
      var pcm1 = new Int16Array(chunkSize)
      var pcmSampleRate1 = 0
      // console.log(77,new Int16Array(chunkSize));
      var pcmOK = false,
        pcmLen = 0
      for1: for (var i1 = 0; i1 < this.TalkChunks.length; i1++) {
        var chunk1 = this.TalkChunks[i1]
        // console.log('chunk1',chunk1);
        pcmSampleRate1 = chunk1.pcmSampleRate
        // console.log('pcmSampleRate1',pcmSampleRate1);
        for (var i2 = chunk1.offset || 0; i2 < chunk1.pcm.length; i2++) {
          pcm1[pcmLen] = chunk1.pcm[i2]
          pcmLen++
          //满一帧了，清除已消费掉的缓冲
          if (pcmLen == chunkSize) {
            pcmOK = true
            chunk1.offset = i2 + 1
            for (var i3 = 0; i3 < i1; i3++) {
              this.TalkChunks.splice(0, 1)
            }
            break for1
          }
        }
      }
      // console.log(88888,pcmOK);
      //缓冲的数据不够一帧时，不发送 或者 是结束了
      if (!pcmOK) {
        //结束发送，销毁websocket.
        return
      }
      // 发送数据
      var blob = new Blob([pcm1], { type: 'audio/pcm' })
      // console.log(1111, blob)
      // audio未播放时，推流socket
      if (!this.soundFlag) {
        socketApi.sendSock(blob, null)
      }

      //循环调用，继续切分缓冲中的数据帧，直到不够一帧
      this.TalkSend([], 0, isClose)
      // console.log(6666);
    },
    recClose: function () {
      this.recStop()
      var rec = this.rec
      this.rec = null
      if (rec) {
        rec.close()
        this.reclog('已关闭')
      } else {
        this.reclog('未打开录音', 1)
      }
    },
    recStart: function () {
      if (!this.rec || !Recorder.IsOpen()) {
        this.reclog('未打开录音', 1)
        this.$refs.recordButton.dispatchEvent(new MouseEvent('click'))
        return
      }
      this.rec.start()

      var set = this.rec.set
      socketApi.initWebSocket()
    },
    recStop: function () {
      this.isTalkSend = false
      if (!(this.rec && Recorder.IsOpen())) {
        return
      }
      this.TalkChunks = []
      this.TalkChunk = null
      socketApi.closeWebSocket()
      // var rec=This.rec;
      // rec.stop(function(blob,duration){
      //   This.reclog("已录制:","",{
      //     blob:blob
      //     ,duration:duration
      //     ,rec:rec
      //   });
      // },function(s){
      //   This.reclog("录音失败："+s,1);
      // });
    },
    recPlayLast: function () {
      if (!this.recLogLast) {
        this.reclog('请先录音，然后停止后再播放', 1)
        return
      }
      this.recplay(this.recLogLast.idx)
    },
    reclog: function (msg, color, res) {
      var obj = {
        idx: this.logs.length,
        msg: msg,
        color: color,
        res: res,

        playMsg: '',
        down: 0,
        down64Val: '',
      }
      if (res && res.blob) {
        this.recLogLast = obj
      }
      this.logs.splice(0, 0, obj)
    },
    recplay: function (idx) {
      var This = this
      var o = this.logs[this.logs.length - idx - 1]
      o.play = (o.play || 0) + 1
      var logmsg = function (msg) {
        o.playMsg =
          '<span style="color:#2d8cf0">' + o.play + '</span> ' + This.getTime() + ' ' + msg
      }
      logmsg('')

      var audio = this.$refs.LogAudioPlayer
      audio.controls = true
      if (!(audio.ended || audio.paused)) {
        audio.pause()
      }
      audio.onerror = function (e) {
        logmsg(
          '<span style="color:red">播放失败[' +
            audio.error.code +
            ']' +
            audio.error.message +
            '</span>'
        )
      }
      audio.src = (window.URL || webkitURL).createObjectURL(o.res.blob)
      audio.play()
    },
    recdown: function (idx) {
      var This = this
      var o = this.logs[this.logs.length - idx - 1]
      o.down = (o.down || 0) + 1

      o = o.res
      var name =
        'rec-' +
        o.duration +
        'ms-' +
        (o.rec.set.bitRate || '-') +
        'kbps-' +
        (o.rec.set.sampleRate || '-') +
        'hz.' +
        (o.rec.set.type || (/\w+$/.exec(o.blob.type) || [])[0] || 'unknown')
      var downA = document.createElement('A')
      downA.href = (window.URL || webkitURL).createObjectURL(o.blob)
      downA.download = name
      downA.click()
    },
    recdown64: function (idx) {
      var This = this
      var o = this.logs[this.logs.length - idx - 1]
      var reader = new FileReader()
      reader.onloadend = function () {
        o.down64Val = reader.result
      }
      reader.readAsDataURL(o.res.blob)
    },
    getTime: function () {
      var now = new Date()
      var t =
        ('0' + now.getHours()).substr(-2) +
        ':' +
        ('0' + now.getMinutes()).substr(-2) +
        ':' +
        ('0' + now.getSeconds()).substr(-2)
      return t
    },
    intp: function (s, len) {
      s = s == null ? '-' : s + ''
      if (s.length >= len) return s
      return ('_______' + s).substr(-len)
    },

    showDialog: function () {
      if (!/mobile/i.test(navigator.userAgent)) {
        return //只在移动端开启没有权限请求的检测
      }
      this.recOpenDialogShow = 1
    },
    dialogCancel: function () {
      clearTimeout(this.dialogInt)
      this.recOpenDialogShow = 0
    },
    waitDialogClick: function () {
      this.dialogCancel()
      this.waitDialogClickFn()
    },
  },
}
</script>
