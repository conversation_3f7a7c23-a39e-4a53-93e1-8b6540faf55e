import axios from 'axios'
import qs from 'qs'
const baseURL = 'http://10.1.44.94:8083/mcwg'
const baseUrlWg = `${window.location.origin}/wg`;
// const baseUrlWg = 'http://60.174.116.164:8091/wg'
const baseLdzhUrl = 'http://10.2.13.177:8090/ldzh'
let baseUrl = '' //添加请求拦截器
axios.interceptors.request.use(
  config => {
    return config
  },
  error => {
    return Promise.reject(error)
  }
)
//添加响应拦截器
axios.interceptors.response.use(
  response => {
    return response
  },
  error => {
    return Promise.resolve(error.response)
  }
)
// axios.defaults.baseURL = baseURL;
axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded; charset=utf-8'
axios.defaults.headers.post['X-Requested-With'] = 'XMLHttpRequest'
axios.defaults.timeout = 120000

function checkStatus(response) {
  return new Promise((resolve, reject) => {
    if (
      response &&
      (response.status === 200 || response.status === 304 || response.status === 400)
    ) {
      resolve(response.data)
    } else {
      reject({
        state: '0',
        message: '请求服务出现问题'
      })
    }
  })
}
export default {
  post(url, params, type) {
    baseUrl = baseUrlWg
    if (type == 2) {
      baseUrl = baseLdzhUrl
    }
    return axios({
      method: 'post',
      url: baseUrl + url,
      data: params
    }).then(response => {
      return checkStatus(response)
    })
  },
  get(url, params, type) {
    baseUrl = baseUrlWg
    if (type == 2) {
      baseUrl = baseLdzhUrl
    }
    return axios({
      method: 'get',
      url: baseUrl + url,
      params
    }).then(response => {
      return checkStatus(response)
    })
  },
  postJson(url, params) {
    baseUrl = baseUrlWg
    if (type == 2) {
      baseUrl = baseLdzhUrl
    }
    // axios.defaults.headers.post['Content-Type'] = 'x-www-form-urlencoded;charset=UTF-8';
    return axios({
      headers: {
        'Content-Type': 'application/json'
      },
      method: 'post',
      url: baseUrl + url,
      data: params
    }).then(response => {
      return checkStatus(response)
    })
  }
}
