<template>
	<div class="child-block" :style="{ height: blockHeight + 'px' }">
		<div class="content">
			<slot></slot>
			<div class="cont">
				<div class="cont1">
					<div class="cont1Item1" @click="typeSwitch3(1)">
						<img src="@/assets/bjnj/yjyl5.png" v-if="typeShow3 == 1" alt="" />
						<img src="@/assets/bjnj/yjyl1.png" v-else alt="" />
						<div class="cont1Name" :class="typeShow3 == 1 ? 'cont1Active' : ''">请求上报</div>
					</div>
					<img class="cont1Item2" src="@/assets/bjnj/ddqq7.png" alt="" />
					<div class="cont1Item3" @click="typeSwitch3(2)">
						<img src="@/assets/bjnj/yjyl6.png" v-if="typeShow3 == 2" alt="" />
						<img src="@/assets/bjnj/yjyl2.png" v-else alt="" />
						<div class="cont1Name" :class="typeShow3 == 2 ? 'cont1Active' : ''">调度记录</div>
					</div>
				</div>
				<div v-if="typeShow3 == 1">
					<div class="cont2">
						<div class="contList">
							<div class="contLeft"><span>*</span>上报部门</div>
							<div class="contRight">{{ data4[0].departName }}</div>
						</div>
						<div class="contList">
							<div class="contLeft"><span>*</span>上报人：</div>
							<div class="contRight">{{ data4[0].usernmae }}</div>
						</div>
						<div class="contList">
							<div class="contLeft"><span>*</span>受灾类型：</div>
							<div class="contRight">{{ dataObj.affectedType }}</div>
						</div>
						<div class="contList">
							<div class="contLeft"><span>*</span>受灾作物：</div>
							<div class="contRight">{{ dataObj.affectedCrop }}</div>
						</div>
						<div class="contList">
							<div class="contLeft"><span>*</span>受灾区域：</div>
							<div class="contRight">{{ dataObj.areaname }}</div>
						</div>
						<div class="contList">
							<div class="contLeft"><span>*</span>受灾面积：</div>
							<div class="contRight">{{ dataObj.affectedArea }}</div>
						</div>
						<div class="contList">
							<div class="contLeft"><span>*</span>所需农机：</div>
							<div class="contRight" :title="dataObj.itemsNew">{{ dataObj.itemsNew }}</div>
						</div>
						<div class="contList">
							<div class="contLeft"><span>*</span>任务时间：</div>
							<div class="contRight">{{ dataObj.requestDate }}</div>
						</div>
						<div class="contList">
							<div class="contLeft"><span>*</span>任务详情：</div>
						</div>
						<div class="contList1">
							<div class="contContent">{{ dataObj.comments }}</div>
						</div>
					</div>
				</div>
				<div v-if="typeShow3 == 2">
					<div class="cont8">
						<el-collapse v-model="activeNames" @change="handleChange">
							<el-collapse-item v-for="(item, index) of data14" :key="index">
								<template slot="title">
									<div class="cont5Left">
										<div class="cont5Left1">{{ item.name }}</div>
										<div class="cont5Left2">
											<div class="cont5Left3" :title="item.contact">联系人: {{ item.contact }}</div>
											<div class="cont5Left4">联系方式: {{ item.phone }}</div>
										</div>
									</div>
									<div class="cont5Right" @click="ckqd(item.tagmachInfos)"><img src="@/assets/bjnj/yjyl10.png" alt="" />查看清单</div>
								</template>
							</el-collapse-item>
						</el-collapse>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		contentNumberUnitOne: {
			type: String,
			default: '万',
		},

		contentDataList: {
			type: Array,
			default: () => [],
		},

		blockHeight: {
			type: Number,
			default: 350,
		},
		dataObj: {
			type: Object,
			default: () => {},
		},
		typeShow3: {
			type: Number,
			default: 1,
		},
		data4: {
			type: Array,
			default: () => [],
		},
		data14: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			currentIndex: 0,
			isActive: true,
			isOpenOtherData: false,
			otherDataList: [],
			currentTabIndex: 0,
			activeNames: ['1'],
		}
	},
	computed: {
		otherArgmachNumber() {
			let otherArgmachNumber = 0

			this.otherDataList.forEach((item) => {
				otherArgmachNumber += Number(item.number)
			})
			return otherArgmachNumber
		},
	},
	methods: {
		clickchange(index) {
			this.currentIndex = index
			this.$emit('updateChange', this.currentIndex)
		},
		clickBulletFrame() {
			this.$emit('updateChange2', true)
		},
		showMoreFn() {
			this.$emit('handleShowMore', true)
		},
		showMoreFn2() {
			this.$emit('handleShowMore2', true)
		},
		handleClickOtherData() {
			this.isOpenOtherData = !this.isOpenOtherData
		},
		handleClickCloseOtherData() {
			this.isOpenOtherData = false
		},
		handleChangeTab(item, index) {
			this.currentTabIndex = index
			this.$emit('handleChangeTab', item.value)
		},
		btnSwitch(item, index) {
			this.$emit('btnSwitch', item, index)
		},
		typeSwitch3(code) {
			this.$emit('typeSwitch3', code)
		},
		handleChange() {},
		ckqd(list){
			this.$emit('ckqd', list)

		}
	},
}
</script>

<style lang="less" scoped>
.child-block {
	width: 455px;
	box-sizing: border-box;
	z-index: 101;
	position: relative;
	padding: 16px 16px 0 22px;

	.content {
		position: relative;
		width: 100%;
		height: 100%;
		// height: calc(100% - 74px);
		display: flex;
		justify-content: flex-start;
		flex-wrap: wrap;
		align-items: center;
		overflow: auto;

		&::-webkit-scrollbar {
			width: 4px; /* 滚动条宽度 */
		}

		//轨道
		&::-webkit-scrollbar-track {
			background: #04244e; /* 滚动条轨道背景 */
			border-radius: 10px;
			padding: 5px 0;
		}

		//滑块
		&::-webkit-scrollbar-thumb {
			background: #82aed0; /* 滑块背景颜色 */
			border-radius: 10px;
			height: 15px;
		}

		//悬停滑块颜色
		&::-webkit-scrollbar-thumb:hover {
			background: #ff823b; /* 鼠标悬停时滑块颜色 */
		}

		//箭头
		&::-webkit-scrollbar-button {
			display: none; /* 隐藏滚动条的箭头按钮 */
		}

		&.no-active-content {
			height: 0;
			display: none;
		}

		.cont {
			width: 100%;
			height: 100%;
			padding-top: 14px;
			position: relative;

			.cont1 {
				width: 100%;

				display: flex;
				justify-content: center;
				align-items: center;
				.cont1Item1 {
					float: left;
					width: 64px;
					cursor: pointer;

					img {
						width: 39px;
						height: 40px;
					}

					.cont1Name {
						font-family: PingFangSC, PingFang SC;
						font-size: 16px;
						color: #45daff;
						line-height: 18px;
						text-shadow: 0px 0px 4px rgba(0, 175, 255, 0.5), 0px 0px 1px rgba(0, 30, 50, 0.5);
						text-align: center;
						font-style: normal;
						background: linear-gradient(180deg, #ffffff 0%, #00b8ff 100%);
						background-clip: text;
						-webkit-background-clip: text;
						-webkit-text-fill-color: transparent;
						margin-top: 12px;
						opacity: 0.56;
					}

					.cont1Active {
						background: linear-gradient(180deg, #ffffff 0%, #00b8ff 100%);
						background-clip: text;
						-webkit-background-clip: text;
						-webkit-text-fill-color: transparent;
						opacity: 1;
					}
				}

				.cont1Item2 {
					float: left;
					width: 106px;
					height: 10px;
					margin: 0 5px;
					margin-top: 17px;
				}

				.cont1Item3 {
					float: left;
					width: 64px;
					cursor: pointer;

					img {
						width: 39px;
						height: 40px;
					}

					.cont1Name {
						font-family: PingFangSC, PingFang SC;
						font-size: 16px;
						color: #45daff;
						line-height: 18px;
						text-shadow: 0px 0px 4px rgba(0, 175, 255, 0.5), 0px 0px 1px rgba(0, 30, 50, 0.5);
						text-align: center;
						font-style: normal;
						background: linear-gradient(180deg, #ffffff 0%, #00b8ff 100%);
						background-clip: text;
						-webkit-background-clip: text;
						-webkit-text-fill-color: transparent;
						margin-top: 12px;
						opacity: 0.56;
					}

					.cont1Active {
						background: linear-gradient(180deg, #ffffff 0%, #00b8ff 100%);
						background-clip: text;
						-webkit-background-clip: text;
						-webkit-text-fill-color: transparent;
						opacity: 1;
					}
				}
			}

			.cont2 {
				width: 419px;
				margin: 0 auto;
				margin-top: 17px;
				border: 1px solid;
				border-image: linear-gradient(270deg, rgba(5, 155, 238, 0.2), rgba(5, 155, 238, 0.5), rgba(5, 155, 238, 0.2)) 1 1;

				.contList {
					width: 100%;
					height: 56px;
					line-height: 56px;
					padding: 0 27px 0 23px;

					&:nth-child(odd) {
						background: rgba(0, 101, 183, 0.15);
					}

					.contLeft {
						height: 100%;
						float: left;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 14px;
						color: #abdcf4;
						text-align: left;
						font-style: normal;

						span {
							height: 100%;
							font-family: PingFangSC, PingFang SC;
							font-weight: 500;
							font-size: 16px;
							color: #8bc7ff;
							text-align: left;
							font-style: normal;
							margin-right: 11px;
						}
					}

					.contRight {
						width: 270px;
						height: 56px;
						float: right;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 14px;
						color: #ffffff;
						text-align: right;
						font-style: normal;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}

					.contContent {
						padding: 12px 0;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 14px;
						color: #ffffff;
						line-height: 20px;
						text-align: right;
						font-style: normal;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 14px;
						color: #ffffff;
						line-height: 20px;
						text-align: right;
						font-style: normal;
					}
				}

				.contList1 {
					width: 100%;
					height: 96px;
					padding: 0 16px;
					display: flex;
					justify-content: flex-start;
					flex-direction: column;
					align-items: flex-start;
					background: rgba(0, 101, 183, 0.15);


					span {
						font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
						font-weight: normal;
						font-size: 14px;
						color: #d0deee;
						text-align: left;
						font-style: normal;
						text-transform: none;
						margin-bottom: 8px;
					}

					.contContent {
						width: 100%;
						font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
						font-weight: normal;
						font-size: 14px;
						color: #ffffff;
						line-height: 20px;
						padding-left: 8px;

						text-align: left;
						font-style: normal;
						text-transform: none;
					}
				}
			}

			.cont4 {
				margin-top: 30px;
				width: 100%;
				height: 528px;
				overflow-y: auto;
				&::-webkit-scrollbar {
					/*滚动条整体样式*/
					width: 6px;
					background: transparent;
					// border: 1px solid #999;
					/*高宽分别对应横竖滚动条的尺寸*/
					// height: 1px;
				}
				&::-webkit-scrollbar-thumb {
					/*滚动条里面小方块*/
					border-radius: 2px;
					box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
					background: rgb(45, 124, 228);
					height: 100px;
				}

				.cont4List {
					width: 438px;
					height: 156px;
					margin: 0 auto;
					margin-bottom: 20px;

					.cont4List1 {
						width: 438px;
						height: 78px;
						background: url(~@/assets/bjnj/rwxfBg.png) no-repeat center / 100% 100%;

						.cont4List1Left {
							float: left;
							padding: 16px 0 0 27px;

							.cont4List1Left1 {
								font-family: PingFangSC, PingFang SC;
								font-size: 20px;
								color: #ffffff;
								line-height: 26px;
								text-align: left;
								font-style: normal;
							}

							.cont4List1Left2 {
								font-family: PingFangSC, PingFang SC;
								font-weight: 400;
								font-size: 14px;
								color: #adddff;
								line-height: 21px;
								text-align: left;
								font-style: normal;
								margin-top: 6px;

								.cont4List1Left3 {
									float: left;
									margin-right: 20px;
								}

								.cont4List1Left4 {
									float: left;
								}
							}
						}

						.cont4List1Right1 {
							float: right;
							width: 55px;
							height: 21px;
							background: linear-gradient(180deg, rgba(140, 2, 0, 0.09) 0%, rgba(255, 0, 0, 0.62) 100%);
							box-shadow: inset 0px 0px 11px 0px rgba(140, 70, 70, 0.5);
							border-radius: 2px;
							border: 1px solid #ffa6a6;
							margin: 28px 14px 0 0;

							span {
								font-family: PingFangSC, PingFang SC;
								font-weight: 400;
								font-size: 12px;
								color: #ffffff;
								line-height: 21px;
								text-shadow: 0px 0px 6px #e64d00;
								text-align: center;
								font-style: normal;
								background: linear-gradient(180deg, #ffffff 0%, #b32424 100%);
								background-clip: text;
								-webkit-background-clip: text;
								-webkit-text-fill-color: transparent;
							}
						}

						.cont4List1Right2 {
							float: right;
							width: 55px;
							height: 21px;
							background: linear-gradient(180deg, rgba(0, 82, 140, 0.3) 0%, #0e4d8c 100%);
							box-shadow: inset 0px 0px 15px 0px rgba(70, 111, 140, 0.5);
							border-radius: 2px;
							border: 1px solid #a6daff;
							margin: 28px 14px 0 0;

							span {
								font-family: PingFangSC, PingFang SC;
								font-weight: 400;
								font-size: 12px;
								color: #ffffff;
								line-height: 17px;
								text-shadow: 0px 0px 8px #00ace6;
								text-align: center;
								font-style: normal;
								background: linear-gradient(90deg, #e6fdff 0%, #248fb3 100%);
								background-clip: text;
								-webkit-background-clip: text;
								-webkit-text-fill-color: transparent;
							}
						}
					}

					.cont4List2 {
						width: 438px;
						height: 36px;
						background: rgba(77, 159, 252, 0.3);

						.cont4Left {
							float: left;
							width: 266px;
							height: 36px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 14px;
							color: rgba(255, 255, 255, 0.9);
							line-height: 36px;
							text-align: left;
							font-style: normal;
							padding-left: 70px;
						}

						.cont4Right {
							float: left;
							width: 104px;
							height: 36px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 14px;
							color: rgba(255, 255, 255, 0.9);
							line-height: 36px;
							text-align: left;
							font-style: normal;
						}
					}

					.cont4List3 {
						width: 438px;
						height: 36px;
						background: rgba(77, 149, 252, 0.1);
						margin-top: 6px;

						.cont4Left {
							float: left;
							width: 266px;
							height: 36px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 14px;
							color: #ffffff;
							line-height: 36px;
							text-align: left;
							font-style: normal;
							padding-left: 70px;
						}

						.cont4Right {
							float: left;
							width: 104px;
							height: 36px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 14px;
							color: rgba(255, 255, 255, 0.9);
							line-height: 36px;
							text-align: left;
							font-style: normal;
						}
					}
				}
			}

			.cont3 {
				margin-top: 17px;

				::v-deep .el-select {
					width: 124px;
					height: 34px;
					margin-right: 6px;

					.el-input {
						width: 124px;
						height: 34px;
						margin-left: 0;
					}
				}

				::v-deep .el-input {
					width: 295px;
					height: 34px;
					margin-left: 8px;
				}

				::v-deep .el-input__inner {
					height: 34px;
					line-height: 34px;
					background: linear-gradient(180deg, rgba(0, 162, 255, 0) 0%, rgba(0, 162, 255, 0.08) 100%, rgba(0, 162, 255, 0.3) 200%),
						rgba(0, 74, 143, 0.4);
					border: 1px solid rgba(0, 162, 255, 0.6);
				}
			}

			.cont5 {
				width: 100%;
				height: 594px;
				overflow-y: auto;
				&::-webkit-scrollbar {
					/*滚动条整体样式*/
					width: 6px;
					background: transparent;
					// border: 1px solid #999;
					/*高宽分别对应横竖滚动条的尺寸*/
					// height: 1px;
				}
				&::-webkit-scrollbar-thumb {
					/*滚动条里面小方块*/
					border-radius: 2px;
					box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
					background: rgb(45, 124, 228);
					height: 100px;
				}
				margin-top: 18px;

				.cont5List {
					margin-bottom: 20px;

					.cont5Title {
						width: 100%;
						height: 25px;
						padding-left: 18px;
						margin-bottom: 20px;

						.cont5Img {
							float: left;
							width: 16px;
							height: 16px;
							margin-right: 6px;
							margin-top: 5px;
						}

						.cont5Title2 {
							float: left;
							font-family: PingFangSC, PingFang SC;
							font-weight: 500;
							font-size: 18px;
							color: #b3daff;
							line-height: 25px;
							text-shadow: 0px 2px 0px rgba(0, 15, 34, 0.5);
							text-align: left;
							font-style: normal;
							background: linear-gradient(180deg, #ffffff 0%, #7ed1ff 100%);
							background-clip: text;
							-webkit-background-clip: text;
							-webkit-text-fill-color: transparent;
						}

						.cont5Title3 {
							float: left;
							font-family: PingFangSC, PingFang SC;
							font-weight: 500;
							font-size: 18px;
							color: #b3daff;
							line-height: 25px;
							text-shadow: 0px 2px 0px rgba(0, 15, 34, 0.5);
							text-align: left;
							font-style: normal;
							background: linear-gradient(180deg, #ffffff 0%, #b32424 100%);
							background-clip: text;
							-webkit-background-clip: text;
							-webkit-text-fill-color: transparent;
						}
					}

					.cont5Item {
						width: 100%;
						height: 74px;
						margin: 0 auto;
						margin-bottom: 10px;
						background: url(~@/assets/bjnj/yjyl12.png) no-repeat center / 100% 100%;

						.cont5Left {
							float: left;
							padding-left: 23px;
							padding-top: 12px;

							.cont5Left1 {
								font-family: PingFangSC, PingFang SC;
								font-size: 20px;
								color: #ffffff;
								line-height: 26px;
								text-align: left;
								font-style: normal;
							}

							.cont5Left2 {
								margin-top: 3px;
								font-family: PingFangSC, PingFang SC;
								font-weight: 400;
								font-size: 14px;
								color: #adddff;
								line-height: 20px;
								text-align: left;
								font-style: normal;

								.cont5Left3 {
									float: left;
								}

								.cont5Left4 {
									float: left;
									margin-left: 40px;
								}
							}
						}

						.cont5Right {
							float: right;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 14px;
							color: #adddff;
							line-height: 20px;
							text-align: left;
							font-style: normal;
							margin: 27px 12px 0 0;

							img {
								float: left;
								width: 18px;
								height: 18px;
								margin-right: 3px;
								margin-top: 1px;
							}
						}

						.cont5Right2 {
							float: right;
							width: 65px;
							height: 24px;
							border-radius: 3px;
							border: 1px solid #adddff;
							margin: 25px 12px 0 0;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 14px;
							color: #adddff;
							line-height: 24px;
							text-align: center;
							font-style: normal;
						}
					}
				}
			}

			.cont6 {
				position: absolute;
				width: 100%;
				height: 33px;
				bottom: 19px;

				.cont6Btn {
					width: 150px;
					height: 33px;
					background: url(~@/assets/bjnj/btnBg.png) no-repeat center / 100% 100%;
					margin: 0 auto;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 16px;
					color: #ffffff;
					line-height: 33px;
					text-align: center;
					font-style: normal;
				}
			}

			.cont7 {
				margin-top: 16px;

				::v-deep .el-input {
					width: 416px;
					height: 34px;
				}

				::v-deep .el-input__inner {
					height: 34px;
					line-height: 34px;
					background: linear-gradient(180deg, rgba(0, 162, 255, 0) 0%, rgba(0, 162, 255, 0.08) 100%, rgba(0, 162, 255, 0.3) 200%),
						rgba(0, 74, 143, 0.4);
					border: 1px solid rgba(0, 162, 255, 0.6);
				}
			}

			.cont8 {
				width: 100%;
				height: 739px;
				overflow-y: auto;
				&::-webkit-scrollbar {
					/*滚动条整体样式*/
					width: 6px;
					background: transparent;
					// border: 1px solid #999;
					/*高宽分别对应横竖滚动条的尺寸*/
					// height: 1px;
				}
				&::-webkit-scrollbar-thumb {
					/*滚动条里面小方块*/
					border-radius: 2px;
					box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
					background: rgb(45, 124, 228);
					height: 100px;
				}
				margin-top: 16px;

				::v-deep .el-collapse-item__arrow {
					display: none;
				}

				::v-deep .el-collapse-item__header {
					width: 100%;
					height:auto;
					margin: 0 auto;
					padding: 10px 16px;
					background: url(~@/assets/bjnj/yjyl12.png) no-repeat center / 100% 100%;
					border-bottom: none;
					display:flex;
					justify-content: space-between;
					align-items: center;
				}

				::v-deep .el-collapse {
					border-top: none;
					border-bottom: none;
				}

				::v-deep .el-collapse-item {
					margin-bottom: 10px;
				}

				::v-deep .el-collapse-item__content {
					padding-bottom: 0;
				}

				::v-deep .el-collapse-item__wrap {
					background-color: unset;
					border-bottom: none;
				}

				.cont5Left {
					width: 345px;
					height: 74px;
					float: left;

					.cont5Left1 {
						font-family: PingFangSC, PingFang SC;
						font-size: 20px;
						color: #ffffff;
						line-height: 26px;
						text-align: left;
						font-style: normal;
					}

					.cont5Left2 {
						margin-top: 3px;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 14px;
						color: #adddff;
						line-height: 20px;
						text-align: left;
						font-style: normal;

						.cont5Left3 {
							width: 128px;
							height: 20px;
							float: left;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
						}

						.cont5Left4 {
							width: 194px;
							height: 20px;
							float: left;
						}
					}
				}

				.cont5Right {
					width: 90px;
					float: right;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 14px;
					color: #adddff;
					line-height: 20px;
					text-align: left;
					font-style: normal;

					img {
						float: left;
						width: 18px;
						height: 18px;
						margin-right: 3px;
						margin-top: 1px;
					}
				}

				.cont5Right2 {
					float: right;
					width: 65px;
					height: 24px;
					border-radius: 3px;
					border: 1px solid #adddff;
					margin: 25px 12px 0 0;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 14px;
					color: #adddff;
					line-height: 24px;
					text-align: center;
					font-style: normal;
				}
			}

			.cont9 {
				padding: 10px 16px 0 16px;

				.cont2 {
					width: 413px;
					height: 232px;
					margin: 12px 0 0 3px;
					padding-top: 35px;
					position: relative;

					.cont2Img {
						width: 413px;
						height: 197px;
						background: url(~@/assets/bjnj/zyjd1.png) no-repeat center / 100% 100%;
					}

					.cont2Left1 {
						width: 50%;
						height: 48px;
						position: absolute;
						top: 0;
						left: 0;
						text-align: left;
					}

					.cont2Left2 {
						width: 50%;
						height: 48px;
						position: absolute;
						top: 136px;
						left: 0;
						text-align: left;
					}

					.cont2Right1 {
						width: 50%;
						height: 48px;
						position: absolute;
						top: 0;
						right: 0;
						text-align: right;
					}

					.cont2Right2 {
						width: 50%;
						height: 48px;
						position: absolute;
						top: 136px;
						right: 0;
						text-align: right;
					}

					.cont2List1 {
						font-family: PingFangSC, PingFang SC;
						font-weight: bold;
						font-size: 24px;
						color: #ffffff;
						line-height: 28px;
						letter-spacing: 2px;
						font-style: normal;

						span {
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 14px;
							color: #cbe5ff;
							line-height: 20px;
							letter-spacing: 1px;
							font-style: normal;
							margin-left: 4px;
						}
					}

					.cont2List2 {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 14px;
						color: #cbe5ff;
						line-height: 20px;
						letter-spacing: 1px;
						font-style: normal;
					}
				}

				.cont4 {
					margin-top: 14px;
					text-align: left;

					img {
						float: left;
						margin-right: 6px;
					}

					span {
						font-family: PingFangSC, PingFang SC;
						font-size: 18px;
						color: #b3daff;
						line-height: 19px;
						letter-spacing: 1px;
						text-shadow: 0px 2px 0px rgba(0, 15, 34, 0);
						text-align: left;
						font-style: normal;
						background: linear-gradient(180deg, #ffffff 0%, #7ed1ff 100%);
						background-clip: text;
						-webkit-background-clip: text;
						-webkit-text-fill-color: transparent;
					}
				}

				.cont5 {
					width: 100%;
					height: 378px;
					margin-top: 20px;

					.cont5List {
						width: 100%;
						height: 116px;
						background: url(~@/assets/bjnj/zyjd2.png) no-repeat center / 100% 100%;
						margin-bottom: 10px;
						margin-left: -8px;

						.cont5Left {
							float: left;
							width: 345px;
							height: 116px;
							padding: 14px 0 0 27px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 18px;
							color: #ffffff;
							line-height: 25px;
							text-align: left;
							font-style: normal;

							.cont5Left2 {
								margin: 6px 0;
							}
						}

						.cont5Right {
							float: left;
							width: 90px;
							height: 116px;
							padding: 14px 13px 0 0;

							.cont5Right1 {
								font-family: PingFangSC, PingFang SC;
								font-weight: 400;
								font-size: 14px;
								color: #adddff;
								line-height: 20px;
								text-align: right;
								font-style: normal;
							}
						}
					}
				}
			}
		}
	}
}
</style>
