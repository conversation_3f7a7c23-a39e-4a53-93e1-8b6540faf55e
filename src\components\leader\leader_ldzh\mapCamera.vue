<template>
  <div class="camera_box">
    <div class="title">
      <p>视频监控</p>
      <div class="del" @click="close"></div>
    </div>
    <div class="content">
    <hlsVideo :src="testUrl"></hlsVideo>
    </div>
  </div>
</template>

<script type="text/ecmascript-6">
import hlsVideo from '@/components/leader/hlsVideo'

export default {
  name: 'mapCamera',
  components: {
    hlsVideo,
  },
  data() {
    return {
       testUrl:
        'http://1257120875.vod2.myqcloud.com/0ef121cdvodtransgzp1257120875/3055695e5285890780828799271/v.f230.m3u8',
    }
  },
  created() {},
  mounted() {},
  methods: {
    close() {
      this.$emit('close')
    },
  },
}
</script>

<style lang="scss" scoped>
@font-face {
  font-family: FZSKJWGB1;
  src: url(~@/assets/leader/font/FZSKJWGB1.ttf);
}
.camera_box {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 101;
  width: 351px;
  height: 255px;
  background: url(~@/assets/leader/img/leader_ldzh/map/map_ry_bg.png) no-repeat;
  background-size: 100% 100%;
  padding: 0 0 28px 0;
  // border: 1px solid red;
  .title {
    margin: 20px 29px 0px 31px;
    background: url(~@/assets/leader/img/leader_ldzh/map/map_sj_title.png) no-repeat;
    background-size: 100% 100%;
    width: 291px;
    height: 33px;
    position: relative;
    p {
      font-size: 18px;
      font-family: FZSKJWGB1;
      font-weight: normal;
      color: #22fcff;
      height: 33px;
      letter-spacing: 1px;
      text-shadow: 0px 0px 4px #00353a;
      background: linear-gradient(180deg, #ffffff 0%, #01c3ff 100%);
      -webkit-background-clip: text;
      // -webkit-text-fill-color: transparent;
    }
    .del {
      background: url(~@/assets/leader/img/leader_ldzh/map/map_sj_del.png) no-repeat;
      background-size: 100% 100%;
      width: 29px;
      height: 36px;
      position: absolute;
      right: -12px;
      bottom: 0;
    }
  }
  .content {
    width: 310px;
    height: 174px;
    margin: 7px 0 0 21px;
  }
}
</style>
