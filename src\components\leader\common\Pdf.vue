<template>
  <div class="pdf-container">
    <div class="arrow">
     <!-- <button @click="print">打印</button> -->
     <button @click="down">下载</button>
     <button @click="hidePdf">X</button>
    </div>
    <pdf ref="pdf" v-for="index in numPages" :key="index" :src="src" :page="index" />
  </div>
</template>
<script>
import pdf from 'vue-pdf' 
export default {
  name: 'Pdf',
  components: {
    pdf
  },
  props: {
    url: {
      type: String,
      default: ''
    },
    name:{
       type: String,
      default: ''
    }
  },
  data () {
    return {
      src: '',
      // numPages: 0,
      filePreviewUrl:'',
      numPages: null, // pdf 总页数
      pageNum: 1,
      pageTotalNum: 1, //总页数
      loadedRatio: 0, // 当
    }
  },
  mounted () {
    // this.pdfTask(this.url)
  },
  methods: {
    pdfTask (pdfUrl) {
      console.log(pdfUrl);
      let loadingTask = pdf.createLoadingTask(pdfUrl)

      loadingTask.promise.then(pdf => {
        this.pageTotalNum = pdf.numPages
        this.src = loadingTask
        this.numPages = pdf.numPages
      })
    },
    print() {
      this.$refs.pdf[0].print()
    },
    down(){
      var aEle = document.createElement("a");
      aEle.click=this.downFile(this.name,this.url)
      
      
      // console.log(this.name)
      // console.log(this.url)
      // console.log(aEle)
      // aEle.download = this.name;
      // aEle.href = this.url;
      // aEle.click();
    },
    downFile(name,url){
      console.log("下载名称"+name)
       var x = new XMLHttpRequest();
      x.open("GET", url, true);
      x.responseType = 'blob';
      x.onload=function(e) {
        // e;
        var path = window.URL.createObjectURL(x.response)
        var a = document.createElement('a');
        a.href = path
        a.download = name;
        a.click()
      }
      x.send();
    },
    hidePdf(){
      this.$emit('hidePdf')
    }
  },
  watch: {
    url (val) {
      this.pdfTask(val)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .annotationLayer{
  transform: scale(1)!important;
}
.pdf-container {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  background: #fff;
   &::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 16px;
    background: transparent;
    // border: 1px solid #999;
    /*高宽分别对应横竖滚动条的尺寸*/
    // height: 1px;
  }
  &::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 2px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgb(45, 124, 228);
    height: 100px;
  }
  .arrow{
    background: #fff;
    font-size: 32px;
    display: flex;
    align-items: center;
    justify-content: end;
    padding: 10px 0 0 0;
    box-sizing: border-box;
    position: absolute;
    top: 0px;
    right: 20px;
    z-index: 10;
    button:first-of-type{
      width: 100px;
      height: 60px;
      font-size: 28px;
      margin-right: 10px;
      cursor: pointer;
      border: none;
      outline: none;
      padding: 10px;
      background-color: #ccc;
      color: #000;
    }
    button:last-of-type{
      width: 60px;
      height: 60px;
      font-size: 28px;
      margin-right: 10px;
      cursor: pointer;
      border: none;
      outline: none;
      padding: 10px;
      background-color: #ccc;
      color: #000;
    }

  }
}

</style>
