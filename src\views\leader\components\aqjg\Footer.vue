<template>
  <div class="wrap">
    <ul class="menu">
      <li v-for="(it, i) of list" :key="i" @click="marker(i)">
        <div class="icon" :style="{ backgroundImage: `url(${it.icon})` }"></div>
        <div class="base"></div>
        <div class="effect"></div>
        <div class="cont">
          <div class="label">{{ it.label }}</div>
          <div class="count" v-if="it.count">{{ it.count }}</div>
        </div>
      </li>
    </ul>
    <SurroundingResources v-model="showYjzyList" />
    <!-- <div class="checkbox" v-if="showYjzyList">
      <ul class="check_wrap">
        <li v-for="(it, i) of yjzyList" :key="i" @click="handleCheck(it)">
          <div class="check" :class="{ checked: it.checked }">
            <img src="@/assets/foot/checked.png" alt="" v-if="it.checked" />
          </div>
          <div class="icon" :style="{ background: `url(${it.iconNormal}) no-repeat` }"></div>
          <div class="label" :class="{ checked: it.checked }">{{ it.label }}</div>
          <div class="count" :class="{ checked: it.checked }">{{ it.count }}</div>
        </li>
      </ul>
    </div> -->
  </div>
</template>

<script>
import { SurroundingResources } from '../map'
export default {
  name: 'ZhddFooter',
  components: {
    SurroundingResources
  },
  data() {
    return {
      list: [
        {
          icon: require('@/assets/foot/icon5.png'),
          label: '重点场所',
          count: 0
        },
        {
          icon: require('@/assets/foot/icon6.png'),
          label: '应急预案',
          count: 0
        },
        {
          icon: require('@/assets/foot/icon7.png'),
          label: '应急资源',
          count: 0
        },
        {
          icon: require('@/assets/foot/icon8.png'),
          label: '视频监控',
          count: 0
        },
        {
          icon: require('@/assets/foot/icon9.png'),
          label: '视频会商',
          count: 0
        }
      ],
      showYjzyList: false,
      yjzyList: [
        {
          iconNormal: require('@/assets/foot/icon10.png'),
          label: '企业应急队伍',
          count: 3,
          checked: true
        },
        {
          iconNormal: require('@/assets/foot/icon11.png'),
          label: '加油站',
          count: 2,
          checked: false
        },
        {
          iconNormal: require('@/assets/foot/icon12.png'),
          label: '企业',
          count: 162,
          checked: false
        },
        {
          iconNormal: require('@/assets/foot/icon13.png'),
          label: '防护目标',
          count: 1,
          checked: false
        },
        {
          iconNormal: require('@/assets/foot/icon14.png'),
          label: '体外除颤仪',
          count: 7,
          checked: false
        },
        {
          iconNormal: require('@/assets/foot/icon15.png'),
          label: '医疗机构',
          count: 1,
          checked: false
        },
        {
          iconNormal: require('@/assets/foot/icon16.png'),
          label: '变电站',
          count: 4,
          checked: false
        },
        {
          iconNormal: require('@/assets/foot/icon17.png'),
          label: '二级消防单位',
          count: 13,
          checked: false
        },
        {
          iconNormal: require('@/assets/foot/icon18.png'),
          label: '视频监控',
          count: 72,
          checked: false
        },
        {
          iconNormal: require('@/assets/foot/icon19.png'),
          label: '公安队伍',
          count: 1,
          checked: false
        },
        {
          iconNormal: require('@/assets/foot/icon20.png'),
          label: '学校',
          count: 4,
          checked: false
        }
      ]
    }
  },
  methods: {
    marker(i) {
      if (i === 2) {
        this.showYjzyList = !this.showYjzyList
      }
      this.$emit('marker', i)
    },
    handleCheck(it) {
      it.checked = !it.checked
    }
  }
}
</script>

<style lang="less" scoped>
.wrap {
  position: absolute;
  bottom: 10px;
  left: 0;
  width: 1920px;
  height: 141px;
  display: grid;
  place-items: center;
  z-index: 1000;
  .menu {
    display: flex;
    gap: 29px;
    height: 100%;
    li {
      position: relative;
      width: 133px;
      cursor: pointer;
      .icon {
        position: absolute;
        width: 38px;
        height: 39px;
        left: 47px;
        top: 13px;
        background-repeat: no-repeat;
      }
      .base {
        position: absolute;
        width: 104px;
        height: 97px;
        left: 16px;
        bottom: 0;
        background: url('~@/assets/foot/bg1.png') no-repeat;
      }
      .effect {
        position: absolute;
        width: 57px;
        height: 80px;
        left: 39px;
        top: 0;
        background: url('~@/assets/foot/bg2.png') no-repeat;
      }
      .cont {
        position: absolute;
        width: 133px;
        height: 31px;
        left: 0;
        bottom: 8px;
        background: url('~@/assets/foot/bg3.png') no-repeat;
        display: flex;
        align-items: center;
        justify-content: space-evenly;
        .label,
        .count {
          height: 18px;
          font-size: 18px;
          font-family: PingFangSC, PingFang SC;
          line-height: 21px;
          background: linear-gradient(180deg, #daefff 0%, #b3daff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
      &:hover {
        .icon {
          animation: move infinite 3s ease-in-out;
          @keyframes move {
            0% {
              transform: rotateY(0);
            }
            50% {
              transform: rotateY(180deg);
            }
            100% {
              transform: rotateY(360deg);
            }
          }
        }
      }
    }
  }
}
</style>
