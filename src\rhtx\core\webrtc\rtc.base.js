import { USER_MEDIA, DISPLAY_MEDIA } from '../dict/index';
import {
  DEFAULT_VIDEO_CONSTRAINT,
  DEFAULT_AUDIO_CONSTRAINT,
  DEFAULT_DISPLAY_CONSTRAINT,
  HTTP_URL
} from '../dict/index';

('use strict');

class RTCPublisher {
  constructor() {
    this.warterMarkerObj = null;
    this.pc = new RTCPeerConnection(null);
    this.onaddstream = null;
  }

  async publish({ url, type, mediaConstraints, waterMarker }) {
    mediaConstraints.audio &&
      this.pc.addTransceiver('audio', { direction: 'sendonly' });
    mediaConstraints.video &&
      this.pc.addTransceiver('video', { direction: 'sendonly' });
    let stream = await createStream(type, mediaConstraints);
    if (waterMarker.show) {
      this.warterMarkerObj = warterMarkers();
      stream = await this.warterMarkerObj.add({ stream, waterMarker });
    }
    stream.getTracks().forEach((track) => {
      this.pc.addTrack(track);
    });
    // sdp协商
    const offer = await this.pc.createOffer();
    await this.pc.setLocalDescription(offer);
    try {
      const session = await sendXHR(url, offer.sdp);
      await this.pc.setRemoteDescription(
        new RTCSessionDescription({ type: 'answer', sdp: session.sdp })
      );
      this.onaddstream && this.onaddstream({ stream });
    } catch (error) {
      console.error(error);
      stream.getTracks().forEach((item) => {
        item.stop();
      });
    }
  }

  close() {
    this.warterMarkerObj && this.warterMarkerObj.remove();
    this.helpObj && this.helpObj.close();
    this.pc && this.pc.close();
    this.pc = null;
  }
}

class RTCPlayer {
  constructor() {
    this.pc = new RTCPeerConnection(null);
    this.onaddstream = null;
    this.init();
  }

  init() {
    this.pc.onaddstream = (event) => {
      this.onaddstream && this.onaddstream(event);
    };
  }

  async play({ url, audio, video }) {
    audio && this.pc.addTransceiver('audio', { direction: 'recvonly' });
    video && this.pc.addTransceiver('video', { direction: 'recvonly' });
    const offer = await this.pc.createOffer();
    await this.pc.setLocalDescription(offer);
    const session = await sendXHR(url, offer.sdp);
    await this.pc.setRemoteDescription(
      new RTCSessionDescription({ type: 'answer', sdp: session.sdp })
    );
    return session;
  }

  close() {
    this.pc && this.pc.close();
    this.pc = null;
  }
}

function createStream(type = USER_MEDIA, mediaConstraints) {
  return new Promise(async (resolve, reject) => {
    try {
      let stream;
      if (type === USER_MEDIA) {
        stream = await navigator.mediaDevices.getUserMedia({
          audio: mediaConstraints.audio && DEFAULT_AUDIO_CONSTRAINT,
          video: mediaConstraints.video && DEFAULT_VIDEO_CONSTRAINT
        });
      } else if (type === DISPLAY_MEDIA) {
        stream = await navigator.mediaDevices.getDisplayMedia(
          DEFAULT_DISPLAY_CONSTRAINT
        );
      }
      resolve(stream);
    } catch (error) {
      reject(error);
    }
  });
}

function sendXHR(url, data) {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    xhr.responseType = 'json';
    xhr.open('post', `${HTTP_URL}${url}`, true);
    xhr.setRequestHeader('Content-Type', 'application/json');
    xhr.onload = function () {
      if (xhr.status === 200) {
        resolve(xhr.response);
      }
    };
    xhr.onerror = function () {
      reject(xhr.response);
    };
    xhr.send(data);
  });
}

export { RTCPublisher, RTCPlayer };
