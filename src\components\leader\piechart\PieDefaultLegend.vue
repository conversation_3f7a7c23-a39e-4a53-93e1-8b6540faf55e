<template>
  <div class="chart" ref="chart"></div>
</template>

<script>
import echarts from 'echarts';
import ResizeListener from 'element-resize-detector';
export default {
  name: 'PieDefaultLegend',
  props: {
    sourceData: {
      type: Array,
      default: () => []
    },
    chartOption: {
      type: Object,
      default: () => ({
        titlefontSize: 18,
        inRadius: ['50%', '70%'],
        inCenter: ['30%', '50%'],
        labelfontSize: 20,
        outRadius: ['43%', '50%'],
        outCeter: ['30%', '50%'],
        legendTop: 'center'
      })
    },
    isOpenTimer: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      option: null,
      chart: null,
      currentActiveIndex: 0,
      timer: null
    };
  },
  mounted() {
    // let that = this;
    this.chart = echarts.init(this.$el);
    this.option = {
      title: {
        text: this.chartOption.title,
        top: 'center',
        left: '28%',
        textAlign: 'center',
        textStyle: {
          color: '#fff',
          fontSize: this.chartOption.titlefontSize || 18,
          fontWeight: '400'
        }
      },
      color: this.chartOption.legendColor,
      legend: {
        type: 'scroll',
        itemHeight: 12,
        itemWidth: 12,
        orient: 'vertical', // 图例列表的布局朝向：纵向布局
        data: this.sourceData.map(item => item.name),
        formatter: this.chartOption.legendFormatter,

        right: this.chartOption.legendRight || '15%',
        top: this.chartOption.legendTop,
        // bottom: '4%',
        // left: '-4%',
        align: 'auto',
        itemGap: 12,
        textStyle: {
          color: '#fff',
          fontSize: 16
        }
      },
      series: [
        {
          name: '',
          type: 'pie',
          radius: this.chartOption.inRadius || ['50%', '70%'],
          center: this.chartOption.inCenter || ['30%', '50%'],
          avoidLabelOverlap: false,
          startAngle: this.chartOption.startAngle,
          // itemStyle: {
          //   borderWidth: 10, //边框的宽度
          //   borderColor: 'rgba(5,21,42,.9)' //边框的颜色
          // },
          label: {
            show: false,
            position: this.chartOption.labelPosition || 'outside', // 鼠标悬浮在环形图上时出现的label的位置：'center' | 'outside'
            color: this.chartOption.labelColor,
            fontSize: this.chartOption.labelfontSize || 20,
            fontWeight: 'bold',
            formatter: this.chartOption.labelFormatter
          },
          emphasis: {
            label: {
              show: true
            }
          },
          labelLine: {
            show: false
          },
          data: this.sourceData
        },
        {
          type: 'pie',
          radius: this.chartOption.outRadius || ['43%', '50%'],
          center: this.chartOption.outCenter || ['30%', '50%'],
          z: 1,
          itemStyle: {
            normal: {
              color: 'rgba(255, 255, 255, 0.2)'
            }
          },
          hoverAnimation: false,
          data: [100],
          labelLine: {
            show: false
          }
        }
      ]
    };
    this.chart = echarts.init(this.$refs.chart);
    this.chart.setOption(this.option, true);

    let showHover = this.chartOption.showHover;
    // 是否选中
    if (showHover != false) {
      this.chart.on('mouseover', params => {
        this.handleItemMouseover(params.dataIndex);
      });
      this.handleItemMouseover(this.currentActiveIndex);
    }

    this.addChartResizeListener();
  },
  methods: {
    addChartResizeListener() {
      const instance = ResizeListener({
        strategy: 'scroll',
        callOnAdd: true
      });

      instance.listenTo(this.$el, () => {
        if (!this.chart) return;
        this.chart.resize();
      });
    },
    handleItemMouseover(i) {
      this.chart.dispatchAction({
        type: 'downplay',
        seriesIndex: 0,
        dataIndex: this.currentActiveIndex
      });
      this.currentActiveIndex = i;
      this.chart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: i
      });
    },
    openTimer() {
      let _count = 1;
      setTimeout(() => {
        this.timer = setInterval(() => {
          if (_count > 3) {
            _count = 0;
          }
          this.handleItemMouseover(_count);
          _count++;
        }, 1000);
      }, 1000);
    }
  },
  watch: {
    isOpenTimer: {
      immediate: true,
      handler(newValue) {
        if (newValue) {
          this.openTimer();
        } else {
          if (this.timer) {
            clearInterval(this.timer);
          }
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>
