import http from '@/utils/leader/request'
const url = 'http://110.41.148.195:18808'

// 获取token
export const aqjgGetToken = () => {
  return http.post(url + '/prod-api/auth/login', {
    device: '123',
    username: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    password: 'Mi<PERSON>a_123'
  })
}

// 企业信息
export const aqjgGetQyxx = token => {
  return http.get(
    url + '/prod-api/sisp/business/page',
    {},
    {
      Authorization: token
    }
  )
}

// 社会面小场所
export const aqjgGetXcs = token => {
  return http.get(
    url + '/prod-api/resources/ninePlace/statistics',
    {},
    {
      Authorization: token
    }
  )
}

// 应急资源
export const aqjgGetYjzy = (data, token) => {
  return http.get(url + '/prod-api/resources/resources/page', data, {
    Authorization: token
  })
}
