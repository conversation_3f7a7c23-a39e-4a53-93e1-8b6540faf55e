<template>
	<div class="condition">
		<div class="conditionLeft">
			<img src="@/assets/img/block-box/more-left-btn.png" alt="" @click="dateMinus" />
			<el-date-picker
				v-model="dtDay_"
				type="date"
				value-format="yyyy-MM-dd"
				placeholder="选择时间"
				@change="dateSwitch"
			></el-date-picker>
			<img src="@/assets/img/block-box/more-btn.png" alt="" @click="datePlus" />
		</div>

		<div class="conditionRight">
			<el-select v-model="curSeason_" placeholder="请选择" @change="seasonSwitch">
				<el-option v-for="item in options" :key="item.type" :label="item.name" :value="item.type"> </el-option>
			</el-select>
		</div>
	</div>
</template>

<script>
import dayjs from 'dayjs'

export default {
	props: {
		dtDay: {
			type: String,
			default: dayjs()
				.subtract(1, 'day')
				.format('YYYY-MM-DD'),
		},
		curSeason: {
			type: String,
			default: () => {
				return 'sanx'
			},
		},
	},
	data() {
		return {
			dtDay_: dayjs()
				.subtract(1, 'day')
				.format('YYYY-MM-DD'),
			curSeason_: 'sanx',
			options: [
				{
					name: '春耕',
					type: 'cg',
				},
				{
					name: '三夏',
					type: 'sanx',
				},
				{
					name: '三秋',
					type: 'sq',
				},
				{
					name: '双抢',
					type: 'shuangqiang',
				},
			],
		}
	},

	watch: {
		dtDay(val) {
			this.dtDay_ = val
		},
		curSeason(val) {
			this.curSeason_ = val
		},
	},

	methods: {
		dateSwitch() {
			this.$emit('dateSwitch', this.dtDay_)
		},
		seasonSwitch() {
			this.$emit('seasonSwitch', this.curSeason_)
		},
		datePlus() {
			this.dtDay = dayjs(this.dtDay)
				.add(1, 'day')
				.format('YYYY-MM-DD')
			console.log('this.dtDay--==', this.dtDay)
		},
		dateMinus() {
			this.dtDay = dayjs(this.dtDay)
				.subtract(1, 'day')
				.format('YYYY-MM-DD')
			console.log('this.dtDay--==', this.dtDay)
		},
	},
}
</script>

<style lang="less" scoped>
.condition {
	width: 450px;
	height: 36px;
	.conditionLeft {
		width: 294px;
		height: 36px;
		float: left;
		display: flex;
		justify-content: space-between;
		align-items: center;
		/deep/ .el-input {
			width: 294px;
			height: 36px;
		}
		img {
			width: 18px;
			height: 18px;
			cursor: pointer;

			&:first-child {
				margin-right: 8px;
			}
			&:last-child {
				margin-left: 8px;
			}
		}
	}
	.conditionRight {
		width: 144px;
		height: 36px;
		float: right;
		/deep/ .el-select {
			width: 144px !important;
			height: 36px !important;
			.el-input {
				width: 144px !important;
				height: 36px !important;
			}
			.el-input__inner {
				width: 100% !important;
				height: 100% !important;
			}
		}
	}
}

/deep/ .el-input__inner {
	background: #002450;
	color: #fff;
	border: 1px solid #1359c2;
	height: 36px !important;
}
</style>
