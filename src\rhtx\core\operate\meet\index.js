import RTCBase from '../base/index';
import MeetSession from './MeetSession';
import {
  getStorage,
  setStorage,
  getCallDeviceType,
  removeStorage
} from '../../util/index';
import { MEET_NUMBER_KEY } from '../../dict/index';
import { useMeetNotice } from './notice';
import { MEET_TYPE } from '../../dict';

export default class Meet extends RTCBase {
  constructor({ userInfo, ws }) {
    super({ userInfo, ws });
    this.init();
  }

  init() {
    this.initMeetNotice();
  }

  initMeetNotice() {
    const meetNotice = useMeetNotice.call(this);
    this.ws.on('*', meetNotice);
  }

  // 呼叫正式会议
  async call(
    { meet, waterMarker = { show: false, text: null }, users = [] },
    event = {}
  ) {
    try {
      if (!meet) {
        console.error('请传入会议');
        return;
      }
      const target = meet.meetnum;
      const mediaConstraints = getCallDeviceType(meet.type);
      const publishPcInfo = await this.publish({
        target,
        mediaConstraints,
        waterMarker
      });
      setStorage(MEET_NUMBER_KEY, target);
      const meetSession = new MeetSession(
        {
          publishPcInfo,
          isCompere: true
        },
        event,
        this.userInfo
      );
      meetSession.addUser(users);
      this.meetSession = meetSession;
      return meetSession;
    } catch (error) {
      removeStorage(MEET_NUMBER_KEY);
      console.error(error);
      throw error;
    }
  }

  callTempVideo({ users = [], meet }, event = {}) {
    return new Promise(async (resolve, reject) => {
      try {
        meet =
          meet ||
          (await this.api.tempMeet({
            type: MEET_TYPE.MEET_VIDEO,
            user_id: this.userInfo.user.id
          }));
        const callSession = await this.call(
          {
            meet,
            users
          },
          event
        );
        // console.log('callSession1',callSession);
        window.sessionStorage.setItem('tempMeetVideoNum',meet.meetnum)
        resolve(callSession);
      } catch (error) {
        reject(error);
      }
    });
  }

  callTempVoice({ users = [], meet }, event = {}) {
    return new Promise(async (resolve, reject) => {
      try {
        meet =
          meet ||
          (await this.api.tempMeet({
            type: MEET_TYPE.MEET_VOICE,
            user_id: this.userInfo.user.id
          }));
        const callSession = await this.call(
          {
            meet,
            users
          },
          event
        );
        // console.log('callSession2',callSession);
        window.sessionStorage.setItem('tempMeetVoiceNum',meet.meetnum)
        resolve(callSession);
      } catch (error) {
        reject(error);
      }
    });
  }

  async answer(meet, event) {
    try {
      const target = meet.meetnum;
      const mediaConstraints = getCallDeviceType(meet.type);
      const publishPcInfo = await this.publish({
        target,
        mediaConstraints
      });
      setStorage(MEET_NUMBER_KEY, target);
      Object.assign(this.meetSession, {
        publishPcInfo,
        event
      });
      return this.meetSession;
    } catch (error) {
      removeStorage(MEET_NUMBER_KEY);
      console.error(error);
    }
  }

  // 播放
  play({ meet, meetInUser }) {
    return new Promise(async (resolve, reject) => {
      if (meet.type === 'meet_video') {
        const playVideo = this.player({
          target: meet.meetnum,
          call_type: 'meet_video_only',
          stream: meet.stream_url
        });
        const playAudio = this.player({
          target: meet.meetnum,
          call_type: 'meet_voice',
          stream: meetInUser.stream_url
        });
        Promise.all([playVideo, playAudio]).then(
          ([videoStream, audioStream]) => {
            videoStream.stream.addTrack(audioStream.stream.getAudioTracks()[0]);
            resolve(videoStream);
          }
        );
      } else {
        const stream = await this.player({
          target: meet.meetnum,
          call_type: meet.type,
          stream: meetInUser.stream_url
        });
        resolve(stream);
      }
    });
  }
}
