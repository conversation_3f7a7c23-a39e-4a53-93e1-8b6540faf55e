.ivu-select-selection {
  width: 171px !important;
  height: 30px !important;
  background: rgba(2, 52, 114, 0.8)
    linear-gradient(360deg, rgba(17, 77, 168, 0) 0%, rgba(36, 132, 212, 0.5) 100%) !important;
  box-shadow: inset 0px 0px 2px 0px #57afff !important;
  border: 1px solid #0a5eaa !important;
}
.ivu-select-dropdown {
  width: 172px !important;
  height: 200px !important;
  background: rgba(2, 52, 114, 0.8)
    linear-gradient(360deg, rgba(17, 77, 168, 0) 0%, rgba(36, 132, 212, 0.5) 100%) !important;
  box-shadow: inset 0px 0px 2px 0px #57afff !important;
  border-radius: 2px !important;
  border: 1px solid #0a5eaa !important;
}

.ivu-select-item-selected,
.ivu-select-item-selected:hover {
  background: transparent !important;
}

.ivu-select-item-selected {
  color: #fff !important;
}
.ivu-select-item {
  color: #fff !important;
}
.ivu-select-item:hover {
  background: url('~@/assets/mskx/bg_slect.png') no-repeat center / 100% 100% !important;
}
.ivu-select-single .ivu-select-selection .ivu-select-placeholder {
  font-size: 14px !important;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400 !important;
  color: #f8f8f8 !important;
  line-height: 26px !important;
}

.ivu-select .ivu-select-dropdown {
  margin: 20px 0 13px;

  &::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 4px;
    background: transparent;
    // border: 1px solid #999;
    /*高宽分别对应横竖滚动条的尺寸*/
    // height: 1px;
  }

  &::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 2px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgb(45, 124, 228);
    height: 100px;
    width: 4px;
  }
}
.ivu-select {
  color: #ccf4ff !important;
}


.ivu-page-item-jump-next, .ivu-page-item-jump-prev, .ivu-page-next, .ivu-page-prev{
  border-color: #2d8cf0 !important;
  color: rgba(255, 255, 255, 0.8) !important;
}

.ivu-page-item{
  border-color: #2d8cf0 !important;
  color: rgba(255, 255, 255, 0.8) !important;
}

.ivu-page-item > a {
  color: rgba(255, 255, 255, 0.8) !important;
  font-size: 14px;
}

.ivu-page-item-active{
  border-color: #7AC6FF !important;
  color: #47EBFF !important;
}

.ivu-page-item-active > a {
  color: #47EBFF !important;
}

.ivu-page-total{
  font-size: 16px;
}
