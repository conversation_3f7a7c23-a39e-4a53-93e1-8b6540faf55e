<template>
  <div v-if="show">
    <div class="ai_waring">
      <div class="title">
        <span>演练任务</span>
      </div>
      <div class="close_btn" @click="closeEmitai"></div>
      <div class="content">
        <div class="check_types">
          <div class="jjcd">
            <span>任务编号：</span>
            <el-input v-model="gdObj.id" placeholder="请输入任务编号"></el-input>
          </div>
          <!-- <div class="jjcd">
            <span>任务类型：</span>
            <Select v-model="gdObj.requestType">
              <Option
                v-for="item in qgdCodejjcdOptions"
                :value="Number(item.itemCode)"
                :key="Number(item.itemCode)"
              >
                {{ item.itemValue }}
              </Option>
            </Select>
          </div> -->
          <div class="jjcd">
            <span>任务状态：</span>
            <!-- <Select v-model="gdObj.state">
              <Option
                v-for="item in qgdCodesqlxOptions"
                :value="item.itemCode"
                :key="Number(item.itemCode)"
              >
                {{ item.itemValue }}
              </Option>
            </Select> -->
            <el-select v-model="gdObj.state" placeholder="请选择任务状态">
              <el-option v-for="item in qgdCodesqlxOptions" :key="Number(item.itemCode)" :label="item.itemValue"
                :value="Number(item.itemCode)"></el-option>
            </el-select>
          </div>
          <div class="jjcd">
            <span>发布时间：</span>
            <el-date-picker
              v-model="gdObj.createDatetime"
              type="date"
              placeholder="选择发布时间">
            </el-date-picker>
          </div>
          <div class="type_btns">
            <div @click="searchBtn">查询</div>
            <div @click="resetBtn">重置</div>
          </div>
        </div>
        <div class="table_box">
          <SwiperTableMap
            :titles="[
              '序号',
              '演练编号',
              '受灾区域',
              '受灾面积',
              '受灾类型',
              '受灾作物',
              '所需农机',
              '发布时间',
              '演练时间',
              '状态',
              '操作',
            ]"
            :widths="['4%','10%','10%','9%','9%','9%','10%','11%','9%', '9%', '10%',]"
            :data="tableList"
            :contentHeight="'630px'"
            :settled="settled"
            @operate="operate"
          ></SwiperTableMap>
        </div>
        <div class="fy_page">
          <Page :total="total" @on-change="pageNumChange" show-total></Page>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import SwiperTableMap from './table/RealTimeEventTable5.vue'
// api
import { getEventType, getUnDoneEventList, getDoneEventList, getQgdSssj } from '@/api/hs/hs.api.js'
import { myMixins } from '@/libs/myMixins.js'
import {
  getDrillTaskList,
  getCscpBasicHxItemCode,
} from '@/api/bjnj/zhdd.js'

export default {
  name: 'RealTimeEventDialogQgd',
  mixins: [myMixins],
  components: { SwiperTableMap },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      qgdCodesqlxOptions: [
        {
          dicValue: '',
          dicLabel: '未完成',
        },
        {
          dicValue: '2',
          dicLabel: '已完成',
        },
      ],
      qgdCodejjcdOptions: [],
      tabActive: 0,
      tabs: ['未办结', '办结'],
      btnsArr: [
        {
          name: '未办结',
          normalBg: require('@/assets/shzl/map/left_n.png'),
          activeBg: require('@/assets/shzl/map/left_a.png')
        },
        {
          name: '已办结',
          normalBg: require('@/assets/shzl/map/right_n.png'),
          activeBg: require('@/assets/shzl/map/right_a.png')
        }
      ],
      urgentOptions: [
        {
          value: '0',
          label: '普通'
        },
        {
          value: '1',
          label: '突发'
        },
        {
          value: '2',
          label: '重要'
        },
        {
          value: '3',
          label: '紧急'
        }
      ],
      eventTypeOptions: [
        {
          value: '0',
          label: '普通'
        }
      ],
      timeOutOptions: [
        {
          value: '0',
          label: '是'
        },
        {
          value: '1',
          label: '否'
        }
      ],
      proityValue: '',
      timeOutValue: '',
      settled: true,
      total: 0,
      pageNum: 1,
      pageSize: 10,
      finished: 1,
      tableList: [
        [
          '1',
          'fz202312201224',
          '防灾请求',
          '江苏省南京市江宁区',
          '500亩',
          '暴雨',
          '小麦',
          '履带式收割机10台',
          '2023-12-20 12:24:09',
          '2023-12-27',
          '待处置',
          '操作',
        ],
        [
          '2',
          'fz202312201224',
          '防灾请求',
          '江苏省南京市江宁区',
          '500亩',
          '暴雨',
          '小麦',
          '履带式收割机10台',
          '2023-12-20 12:24:09',
          '2023-12-27',
          '进行中',
          '操作',
        ],
        [
          '3',
          'fz202312201224',
          '防灾请求',
          '江苏省南京市江宁区',
          '500亩',
          '暴雨',
          '小麦',
          '履带式收割机10台',
          '2023-12-20 12:24:09',
          '2023-12-27',
          '已处置',
          '操作',
        ],
      ],
      gdObj: {
        id: '',
        requestType: '',
        state: '',
        createDatetime: '',
      }
    }
  },
  watch: {
    qgdCodejjcdOptions(val) {
      if (this.qgdCodejjcdOptions.length > 0) {
        // this.getQgdSssj()
      }
    }
  },
  computed: {
    show() {
      return this.value
    }
  },
  mounted() {
    this.getCscpBasicHxItemCode1()
    this.getCscpBasicHxItemCode2()
    this.getDrillTaskList({
      current: this.pageNum, 
      size: this.pageSize,
    })
  },
  methods: {
    async getDrillTaskList(data) {
      data={
        ...data,
        state:data.state||''
      }
      let res = await getDrillTaskList(data)
      console.log(res)
      this.tableList = res.data.map((it, i) => [
        this.pageSize * (this.pageNum - 1) + i + 1,
        it.id,
        it.areaname,
        it.affectedArea,
        it.affectedType,
        it.affectedCrop,
        it.itemsNew,
        it.createDatetime,
        it.requestDate,
        it.statusName,
        it.actionsForCurrentNode,
        it.zstatus,
        it.updateTime,
        it,
      ])
      this.total = res.recordsTotal
    },
    async getCscpBasicHxItemCode1() {
      let res = await getCscpBasicHxItemCode('requestType')
      console.log(res)
      if (res?.code == '0') {
        this.qgdCodejjcdOptions = res.data
      }
    },
    async getCscpBasicHxItemCode2() {
      let res = await getCscpBasicHxItemCode('ddStatus')
      console.log(res)
      if (res?.code == '0') {
        this.qgdCodesqlxOptions = res.data
      }
    },
    operate(i, it) {
      this.$emit('operate', i, it)
    },
    closeEmitai() {
      this.$emit('input', false)
    },
    pageNumChange(val) {
      this.pageNum = val
      let data = {
        id: this.gdObj.id,
        // requestType: this.gdObj.requestType,
        state: this.gdObj.state,
        createDatetime: this.gdObj.createDatetime ? dayjs(this.gdObj.createDatetime).format('YYYYMMDD') : '',
        current: this.pageNum, 
        size: this.pageSize,
      }
      this.removeEmptyValues(data)
      console.log(data)
      this.getDrillTaskList(data)
    },
    async getQgdSssj() {
      this.tableList = []
      let res = await getQgdSssj({
        // street: '1649962979288023040',
        page: this.pageNum,
        size: this.pageSize,
        emeLevel: this.gdObj.jjcdValue,
        appealType: this.gdObj.sqlxValue
      })
      console.log('实时事件弹窗', res)
      console.log(this.qgdCodejjcdOptions)
      if (res?.code == '200' && res.result.data.length > 0) {
        console.log('res.result.data', res.result.data)
        this.tableList = res.result.data.map((it, i) => [
          this.pageSize * (this.pageNum - 1) + i + 1,
          it.orderNum,
          it.emeLevel,
          it.appealContent,
          it.appealType,
          it.community,
          it.eventDate,
          it.formStatus,
          it.eventLocation,
          it.id,
          it
        ])
        this.total = res.result.recordsTotal
      }
      // console.log('this.tableList', this.tableList)
    },
    removeEmptyValues(obj) {
      for (const key in obj) {
        if (typeof obj[key] === 'object') {
          this.removeEmptyValues(obj[key]);
        }
        if (!obj[key] || (typeof obj[key] === 'object' && !Reflect.ownKeys(obj[key]).length)) {
          delete obj[key];
        }
      }
    },
    searchBtn() {
      let data = {
        id: this.gdObj.id,
        // requestType: this.gdObj.requestType,
        state: this.gdObj.state,
        createDatetime: this.gdObj.createDatetime ? dayjs(this.gdObj.createDatetime).format('YYYYMMDD') : '',
        current: this.pageNum, 
        size: this.pageSize,
      }
      this.removeEmptyValues(data)
      console.log(data)
      this.getDrillTaskList(data)
    },
    resetBtn() {
      this.gdObj = {}
      this.getDrillTaskList({
        current: this.pageNum, 
        size: this.pageSize,
      })
    }
  },
}
</script>

<style lang="less" scoped>
.bg-header {
  position: absolute;
  width: 100%;
  height: 1080px;
  background: rgba(2, 14, 50, 0.75);
  z-index: 999999;
  top: 0;
  left: 0;
}
/deep/ .ivu-select {
  width: 189px;
}
/deep/ .ivu-select-selection {
  width: 189px;
  height: 34px;
  background: rgba(0, 74, 143, 0.4)
    linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
}
/deep/ .ivu-select-selection .ivu-select-selected-value {
  font-size: 13px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 34px;
}

/deep/ .ivu-input {
  background: rgba(0, 74, 143, 0.4)
    linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 22px;
}
/deep/ .ivu-select-placeholder {
  height: 34px !important;
  font-size: 13px !important;
  line-height: 34px !important;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff !important;
}
/deep/ .ivu-page-item {
  background: transparent;
  color: #fff;
}
/deep/ .ivu-page-item a {
  color: #fff;
}
/deep/ .ivu-page-prev,
/deep/ .ivu-page-next {
  background: transparent;
}
/deep/ .el-input__inner {
  color: #CCF4FF;
}
.ai_waring {
  width: 1535px;
  height: 900px;
  background: rgba(9, 19, 34, 0.95);
  box-shadow: 0px 0px 22px 0px rgba(11, 54, 138, 0.35), 0px 2px 8px 0px rgba(0, 0, 0, 0.7),
    inset 0px 0px 8px 0px rgba(17, 146, 214, 0.35);
  border-radius: 11px;
  border: 1px solid #015c8c;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1100;
  .title {
    margin: 0 auto 23px;
    width: 635px;
    height: 76px;
    line-height: 76px;
    background: url(~@/assets/map/dialog/title2.png) no-repeat;
    display: grid;
    place-items: center;
    span {
      display: inline-block;
      font-size: 36px;
      font-family: PingFangSC, PingFang SC;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .close_btn {
    position: absolute;
    width: 23px;
    height: 23px;
    top: 25px;
    right: 40px;
    background: url(~@/assets/map/dialog/btn3.png) no-repeat;
    cursor: pointer;
  }
  .content {
    padding: 0 30px;
    .btns {
      // border: 1px solid red;
      margin: 0 auto;
      display: flex;
      justify-content: center;
      & > div {
        width: 154px;
        height: 53px;
        span {
          font-size: 24px;
          font-family: PingFangSC, PingFang SC;
          color: #ffffff;
          line-height: 53px;
          background: linear-gradient(180deg, #ffffff 0%, #ffeba0 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
    .check_types {
      text-align: left;
      display: flex;
      .jjcd {
        display: flex;
        align-items: center;
        margin-right: 24px;
        span {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
        }
        /deep/ .el-input {
          width: 189px;
          height: 34px;
          .el-input__inner {
            width: 189px;
            height: 34px;
            background: linear-gradient(180deg , rgba(181,223,248,0) 0%, rgba(29,172,255,0.29) 100%, #FFFFFF 100%), rgba(0,74,143,0.4);
            border: 1px solid rgba(0,162,255,0.6);
          }
        }
      }
      .report_time {
        margin-left: 26px;
        display: flex;
        align-items: center;
        span {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
      }
      .istime_out {
        margin-left: 26px;
        align-items: center;
        display: flex;
        span {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
      }
      .type_btns {
        margin-left: 27px;
        display: flex;
        align-items: center;
        & div {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 43px;
          background: url(~@/assets/shzl/map/type_btn.png) no-repeat center / 100% 100%;
          background-size: 100% 100%;
          text-align: center;
          cursor: pointer;
          &:first-of-type {
            width: 98px;
            height: 43px;
          }
          &:nth-of-type(2) {
            margin-left: 20px;
            width: 98px;
            height: 43px;
          }
          &:nth-of-type(3) {
            margin-left: 20px;
            width: 98px;
            height: 43px;
          }
        }
      }
    }
    .table_box {
      margin-top: 31px;
      overflow: hidden;
    }
    .fy_page {
      margin-top: 20px;
    }
  }
}
// /deep/ .ivu-page-total {
//   font-size: 18px;
//   color: #fff;
// }
</style>
