<template>
  <div class="dialer_box">
    <div class="title">
      <img class="tel_btn" src="@/assets/dialer/tel_btn.png" alt />
      <span>拨号盘</span>
      <img @click="closeBtn" class="del" src="@/assets/dialer/close.png" alt />
    </div>
    <div class="input_box">
      <!-- <div>{{ inputValue }}</div> -->
      <input class="input_value" type="text" v-model="inputValue" />
      <img @click="clearBtn" class="clear_btn" src="@/assets/dialer/clear.png" alt />
    </div>
    <div class="numeric-keypad">
      <button v-for="item in digits" :key="item" @click="addDigit(item)">{{ item }}</button>
      <button @click="addDigit('*')">*</button>
      <button @click="addDigit('#')">#</button>
    </div>
    <div class="additional-buttons">
      <div @click="videoBtn" class="video_btn">
        <span>视频</span>
      </div>
      <div @click="voiceBtn" class="voice_btn">
        <span>语音</span>
      </div>
      <div @click="addSphsBtn" class="addSphs_btn">
        <span>入会</span>
      </div>
    </div>
    <rhtxVideo
      v-if="mapVideoShow"
      @closeVideoEmit="mapVideoShow=false"
      :phone="phone"
      :callType="callType"
    />
  </div>
</template>

<script>
import rhtxVideo from '@/components/rhtx/rhtxVideo.vue'
export default {
  components: { rhtxVideo },
  //注册局部组件指令
  directives: {
    drag: function(el) {
      let dragBox = el //获取当前元素
      dragBox.onmousedown = e => {
        //算出鼠标相对元素的位置
        let disX = e.clientX - dragBox.offsetLeft
        let disY = e.clientY - dragBox.offsetTop
        document.onmousemove = e => {
          e.stopPropagation()
          //用鼠标的位置减去鼠标相对元素的位置，得到元素的位置
          let left = e.clientX - disX
          let top = e.clientY - disY
          //移动当前元素
          dragBox.style.left = left + 'px'
          dragBox.style.top = top + 'px'
        }
        document.onmouseup = e => {
          //鼠标弹起来的时候不再移动
          document.onmousemove = null
          //预防鼠标弹起来后还会循环（即预防鼠标放上去的时候还会移动）
          document.onmouseup = null
        }
      }
    }
  },
  data() {
    return {
      digits: [1, 2, 3, 4, 5, 6, 7, 8, 9, 0],
      inputValue: '',
      mapVideoShow: false,
      phone: ''
    }
  },
  methods: {
    addDigit(item) {
      if (this.inputValue.length >= '11') {
        return
      }
      this.inputValue += item
    },
    clearBtn() {
      if (this.inputValue.length > 0) {
        this.inputValue = this.inputValue.slice(0, -1)
      }
    },
    closeBtn() {
      this.$emit('closeBtnDialer')
    },
    videoBtn() {
      const regex = /^1[3456789]\d{9}$/
      if (regex.test(this.inputValue)) {
        this.mapVideoShow = true
        this.phone = this.inputValue
        this.callType = 'video'
      } else {
        this.$message({
          message: '请输入正确的手机号',
          type: 'warning'
        })
      }
    },
    voiceBtn() {
      const regex = /^1[3456789]\d{9}$/
      if (regex.test(this.inputValue)) {
        this.mapVideoShow = true
        this.phone = this.inputValue
        this.callType = 'voice'
      } else {
        this.$message({
          message: '请输入正确的手机号',
          type: 'warning'
        })
      }
    },
    addSphsBtn() {
      const regex = /^1[3456789]\d{9}$/
      if (regex.test(this.inputValue)) {
        this.$emit('addSphsBtn', this.inputValue)
      } else {
        this.$message({
          message: '请输入正确的手机号',
          type: 'warning'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dialer_box {
  width: 342px;
  height: 434px;
  background: url(~@/assets/dialer/bg.png) no-repeat 100% 100%;
  background-size: 100% 100%;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
  .title {
    width: 305px;
    height: 47px;
    background: url(~@/assets/dialer/title.png) no-repeat 100% 100%;
    background-size: 100% 100%;
    margin: 20px auto;
    display: flex;
    align-items: center;
    .tel_btn {
      width: 24px;
      height: 23px;
      display: inline-block;
      margin-left: 22px;
      margin-top: 2px;
    }
    span {
      margin-top: 2px;
      margin-left: 2px;
      font-size: 22px;
      font-family: PangMenZhengDao;
      color: #ffffff;
      line-height: 26px;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .del {
      margin: 0 0 0 165px;
      cursor: pointer;
    }
  }
  .input_box {
    width: 264px;
    height: 35px;
    margin: 0 auto;
    position: relative;
    box-shadow: inset 2px 2px 6px 0px rgba(19, 61, 83, 0.5),
      inset -2px -2px 6px 0px rgba(19, 61, 83, 0.5), inset 0px 1px 3px 0px rgba(0, 0, 0, 0.5);
    // & > div {
    //   font-size: 18px;
    //   font-family: YouSheBiaoTiHei;
    //   color: #a6d2ff;
    //   line-height: 23px;
    //   text-align: left;
    // }
    .input_value {
      font-size: 18px;
      font-family: YouSheBiaoTiHei;
      color: #a6d2ff;
      background-color: transparent;
      border: none;
      width: 100%;
      height: 30px;
    }
    .clear_btn {
      width: 28px;
      height: 23px;
      position: absolute;
      top: 8px;
      right: 0;
      cursor: pointer;
    }
  }
  .numeric-keypad {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 10px;
    width: 264px;
    height: 222px;
    box-shadow: inset 2px 2px 6px 0px rgba(19, 61, 83, 0.5),
      inset -2px -2px 6px 0px rgba(19, 61, 83, 0.5), inset 0px 1px 3px 0px rgba(0, 0, 0, 0.5);
    margin: 0 auto;
    padding: 17px 28px 15px;
    button {
      width: 35px;
      height: 37px;
      font-size: 18px;
      font-family: YouSheBiaoTiHei;
      color: #a6d2ff;
      line-height: 23px;
      background: url(~@/assets/dialer/circle.png) no-repeat 100% 100%;
      background-size: 100% 100%;
      border: none;
      margin: 0 auto;
      cursor: pointer;
    }
  }
  .additional-buttons {
    grid-column: span 3;
    display: flex;
    justify-content: space-between;
    margin-top: 19px;
    width: 100%;
    padding: 0 43px;
    & > div {
      // width: 221px;
      // height: 36px;
      font-size: 16px;
      font-family: MicrosoftYaHei;
      color: #ffffff;
      line-height: 36px;
      flex-shrink: 0;
      width: 76px;
      height: 34px;
      span {
        margin-left: 24px;
      }
    }
    & .video_btn {
      background: url(~@/assets/dialer/video_bg.png) no-repeat 100% 100%;
      background-size: 100% 100%;
    }
    & .voice_btn {
      background: url(~@/assets/dialer/voice_bg.png) no-repeat 100% 100%;
      background-size: 100% 100%;
    }
    & .addSphs_btn {
      background: url(~@/assets/dialer/addsphs_bg.png) no-repeat 100% 100%;
      background-size: 100% 100%;
    }
  }
}
</style>
