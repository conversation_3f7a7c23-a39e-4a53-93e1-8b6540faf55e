<template>
  <div class="event" v-if="show">
    <div class="close" @click="closeFn"></div>
    <div class="title">
      <span>应急救灾中心详情</span>
    </div>
    <ul class="content">
      <li>
        <div class="label">请求类型：</div>
        <div class="value" :title="list.title">{{ list.title }}</div>
      </li>
      <li>
        <div class="label">受灾类型：</div>
        <div class="value" :title="list.type">{{ list.type }}</div>
      </li>
      <li>
        <div class="label">受灾作物：</div>
        <div class="value" :title="list.time">{{ list.time }}</div>
      </li>
      <li>
        <div class="label">受灾面积：</div>
        <div class="value" :title="list.address">{{ list.address }}</div>
      </li>
      <li>
        <div class="label">受灾区域：</div>
        <div class="value" :title="list.describe">{{ list.describe }}</div>
      </li>
      <li>
        <div class="label">请求调度时间：</div>
        <div class="value" :title="list.describe2">{{ list.describe2 }}</div>
      </li>
      <li>
        <div class="label">所需农机：</div>
        <div class="value" :title="list.describe3">{{ list.describe3 }}</div>
      </li>
      <li>
        <div class="label">状态：</div>
        <div class="value" :title="list.describe4">{{ list.describe4 }}</div>
      </li>
    </ul>
    <div class="btns">
      <!-- <div class="btn" @click="handle(0)">事件详情</div>
      <div class="btn" @click="handle(1)">一般处置</div>
      <div class="btn" @click="handle(2)">应急处置</div> -->
      <div class="btn" v-for="(item,index) in btns" :key="index" @click="handle(index)">{{item}}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EventDialog',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    list: {
      type: Object,
      default: () => {
        return {
          title: '救灾请求',
          type: '暴雨',
          time: '小麦',
          address: '100亩',
          describe: '江苏省南京市浦口区',
          describe2: '2024-02-20',
          describe3: '联合收割机10台',
          describe4: '进行中',
        }
      }
    },
    btns:{
      type:Array,
      default:()=>[
        '查看详情'
      ]
    }
  },
  computed: {
    show() {
      return this.value
    }
  },
  methods: {
    closeFn() {
      this.$emit('input', false)
    },
    handle(i) {
      this.$emit('handle', i)
    }
  }
}
</script>

<style lang="less" scoped>
.event {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  width: 413px;
  height: 442px;
  padding: 20px 0 0 0;
  margin: 0 auto;
  background: url('~@/assets/map/dialog/bg7.png') no-repeat center / 100% 100%;
  box-sizing: border-box;
  .close {
    position: absolute;
    top: 31px;
    right: 43px;
    width: 24px;
    height: 24px;
    background: url('~@/assets/map/dialog/btn2.png') no-repeat;
    box-sizing: border-box;
    cursor: pointer;
  }
  .title {
    width: 375px;
    height: 47px;
    line-height: 47px;
    padding-left: 27px;
    margin: 0 auto;
    text-align: left;
    background: url('~@/assets/map/dialog/title1.png') no-repeat;
    box-sizing: border-box;
    span {
      font-size: 18px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      background: linear-gradient(180deg, #ffffff 0%, #2aa6ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  ul {
    display: flex;
    flex-direction: column;
    gap: 14px;
    margin-top: 20px;
    padding: 0 50px 0 48px;
    box-sizing: border-box;
    li {
      display: flex;
      justify-content: space-between;
      box-sizing: border-box;
      .label {
        // width: 70px;
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
        flex-shrink: 0;
      }
      .value {
        width: 225px;
        text-align: right;
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
 .btns {
    margin-top: 23px;
    display: flex;
    justify-content: center;
    gap: 10px;
    .btn {
      width: 150px;
      height: 33px;
      line-height: 33px;
      font-size: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #ffffff;
      background: url('~@/assets/map/dialog/btn1.png') no-repeat center / 100% 100%;
      cursor: pointer;
    }
  }
}
</style>
