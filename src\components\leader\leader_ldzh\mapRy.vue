<template>
  <div class="ry_box">
    <div class="title">
      <p>人员信息</p>
      <div class="del" @click="close"></div>
    </div>
    <div class="content">
      <div class="info">
        <div class="line">
          <div>* 姓名:</div>
          <div>张海</div>
        </div>
        <div class="line">
          <div>* 所属部门:</div>
          <div>XX县XX局</div>
        </div>
        <div class="line">
          <div>* 职位:</div>
          <div>巡查员</div>
        </div>
        <div class="line">
          <div>* 所在地点:</div>
          <div>南京市鼓楼区宁海路199号</div>
        </div>
        <div class="line">
          <div>* 联系方式:</div>
          <div>13544778563</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script type="text/ecmascript-6">
export default {
  name: 'mapRy',
  data() {
    return {}
  },
  created() {},
  mounted() {},
  methods: {
    close() {
      this.$emit('close')
    },
  },
}
</script>

<style lang="scss" scoped>
@font-face {
  font-family: FZSKJWGB1;
  src: url(~@/assets/leader/font/FZSKJWGB1.ttf);
}
.ry_box {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 101;
  width: 351px;
  height: 255px;
  background: url(~@/assets/leader/img/leader_ldzh/map/map_ry_bg.png) no-repeat;
  background-size: 100% 100%;
  padding: 0 0 28px 0;
  // border: 1px solid red;
  .title {
    margin: 20px 29px 25px 31px;
    background: url(~@/assets/leader/img/leader_ldzh/map/map_sj_title.png) no-repeat;
    background-size: 100% 100%;
    width: 291px;
    height: 33px;
    position: relative;
    p {
      font-size: 18px;
      font-family: FZSKJWGB1;
      font-weight: normal;
      color: #22fcff;
      height: 33px;
      letter-spacing: 1px;
      text-shadow: 0px 0px 4px #00353a;
      background: linear-gradient(180deg, #ffffff 0%, #01c3ff 100%);
      -webkit-background-clip: text;
      // -webkit-text-fill-color: transparent;
    }
    .del {
      background: url(~@/assets/leader/img/leader_ldzh/map/map_sj_del.png) no-repeat;
      background-size: 100% 100%;
      width: 29px;
      height: 36px;
      position: absolute;
      right: -12px;
      bottom: 0;
    }
  }
  .content {
    // border: 1px solid red;
    .info {
      padding: 0 34px 0 33px;
      .line {
        height: 14px;
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
        & > div:first-of-type {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 20px;
        }
        & > div:last-of-type {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 20px;
        }
      }
    }
  }
}
</style>
