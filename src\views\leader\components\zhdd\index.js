import DutySystem from './DutySystem.vue'
import WorkOrderNotCompl from './WorkOrderNotCompl.vue'
import RealTimeEvent from './RealTimeEvent.vue'
import RealTimeEventQgd from './RealTimeEventQgd.vue'
import EmergencyResources from './EmergencyResources.vue'
import GridMemberLoginStatus from './GridMemberLoginStatus.vue'
import VideoSurveillance from './VideoSurveillance.vue'
import ZhddFooter from './Footer.vue'
import ZhddAside from './Aside.vue'
import GeneralDisposal from './GeneralDisposal.vue'
import GeneralDisposalQgd from './GeneralDisposalQgd.vue'
import SurroundingResources from './SurroundingResources.vue'
import AIEventAnalysis from './AIEventAnalysis.vue'

export {
  DutySystem,
  WorkOrderNotCompl,
  RealTimeEvent,
  RealTimeEventQgd,
  EmergencyResources,
  GridMemberLoginStatus,
  VideoSurveillance,
  ZhddFooter,
  ZhddAside,
  GeneralDisposal,
  GeneralDisposalQgd,
  SurroundingResources,
  AIEventAnalysis
}
