import axios from 'axios';
import qs from "qs";

//添加请求拦截器
axios.interceptors.request.use(
  config => {
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);
//添加响应拦截器
axios.interceptors.response.use(
  response => {
    return response;
  },
  error => {
    return Promise.resolve(error.response);
  }
);
// axios.defaults.baseURL = process.env.VUE_APP_BASE_URL;
axios.defaults.headers.post['Content-Type'] = 'application/json';
axios.defaults.headers.post['X-Requested-With'] = 'XMLHttpRequest';
axios.defaults.timeout = 120000;

function checkStatus(response) {
  return new Promise((resolve, reject) => {
    if (
      response &&
      (response.status === 200 || response.status === 304 || response.status === 400)
    ) {
      resolve(response.data);
    } else {
      reject({
        state: '0',
        message: '请求服务出现问题'
      });
    }
  });
}
export default {
  post(url, params, headers) {
    if (headers) {
      for (const key in headers) {
        console.log('headers key', key);
        console.log('headers value', headers[key]);
        axios.defaults.headers.post[key] = headers[key];
      }
    }
    return axios({
      method: 'post',
      url,
      data: params
    }).then(response => {
      return checkStatus(response);
    });
  },
  get(url, params) {
    // params = qs.stringify(params);
    return axios({
      method: 'get',
      url,
      params
    }).then(response => {
      return checkStatus(response);
    });
  },
  postForm(url, params, headers) {
    // axios.defaults.headers.post['Content-Type'] = 'x-www-form-urlencoded;charset=UTF-8';
    return axios({
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      method: 'post',
      url,
      data: qs.stringify(params)
    }).then(response => {
      return checkStatus(response);
    });
  },
};
