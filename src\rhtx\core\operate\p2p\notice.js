/*
 * @Author: fanjialong <EMAIL>
 * @Date: 2023-04-15 10:29:09
 * @LastEditors: fanjialong <EMAIL>
 * @LastEditTime: 2023-06-16 11:21:14
 * @FilePath: \hs_dp\src\rhtx\core\operate\p2p\notice.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { CONNECT_STATE } from '../../dict';
import {
  MINE_P2P_RING,
  MINE_P2P_ONCALL,
  MINE_P2P_AVAILABLE,
  TARGET_P2P_RING,
  TARGET_P2P_ONCALL,
  TARGET_P2P_AVAILABLE,
  TARGET_P2P_ANSWER
} from '../../websocket/message/p2p';
import CallSession from './CallSession';
import P2P_EVENT from './event';

export function useP2PNotice() {
  const _this = this;

  function deleteCallItem(call) {
    const target = _this.getTarget(call, _this.userInfo);
    const callItem = _this.getTargetCallItem(
      call,
      _this.userInfo,
      _this.callList
    );
    callItem && callItem.stopPublish && callItem.stopPublish();
    delete _this.callList[target];
  }

  return {
    [MINE_P2P_RING]: ({ call }) => {
      const target = _this.getTarget(call, _this.userInfo);
      const callItem = new CallSession({ call, userInfo: _this.userInfo });
      _this.callList[target] = callItem;
    },
    [MINE_P2P_ONCALL]: ({ call }) => {
      const callItem = _this.getTargetCallItem(
        call,
        _this.userInfo,
        _this.callList
      );
      if (callItem.isMineCaller) {
        Object.assign(callItem, { call });
      } else {
        Object.assign(callItem, { call, connectState: CONNECT_STATE.CONNECT });
      }
    },
    [TARGET_P2P_ANSWER]: async ({ user, call }) => {
      // 对方应答  拉流
      const playerPcInfo = await _this.play({ call });
      const callItem = _this.getTargetCallItem(
        call,
        _this.userInfo,
        _this.callList
      );
      if (callItem) {
        Object.assign(callItem, {
          call,
          playerPcInfo,
          connectState: CONNECT_STATE.CONNECT
        });

        callItem.fire(P2P_EVENT.ANSWER, callItem);
      } else {
        throw new Error(`${TARGET_P2P_ANSWER}:not find answer user`);
      }
    },
    [TARGET_P2P_AVAILABLE]: ({ call }) => {
      const callItem = _this.getTargetCallItem(
        call,
        _this.userInfo,
        _this.callList
      );
      callItem && callItem.fire(P2P_EVENT.HANGUP, callItem);
      deleteCallItem(call);
    },
    [MINE_P2P_AVAILABLE]: ({ call }) => {
      const callItem = _this.getTargetCallItem(
        call,
        _this.userInfo,
        _this.callList
      );
      callItem && callItem.fire(P2P_EVENT.HANGUP, callItem);
      deleteCallItem(call);
    }
  };
}
