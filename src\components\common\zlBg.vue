<template>
  <div class="zlBg_box">
    <div class="video_box">
      <video muted autoplay loop="loop" src="./img/bg.mp4"></video>
    </div>
    <div class="items">
      <div class="item1" @click="emitMenu(1)">
        <img src="./img/hswh.png" alt />
        <div>湖熟文化</div>
      </div>
      <div class="item2" @click="emitMenu(2)">
        <img src="./img/jjfz.png" alt />
        <div>经济发展</div>
      </div>
      <div class="item3" @click="emitMenu(3)">
        <img src="./img/wgzl.png" alt />
        <div>网格治理</div>
      </div>
      <div class="item4" @click="emitMenu(4)">
        <img src="./img/stjs.png" alt />
        <div>生态建设</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'zlBg',
  data() {
    return {}
  },
  mounted() {},
  methods: {
    emitMenu(val) {
      console.log(1111, val)
      this.$emit('emitMenu', val)
    }
  }
}
</script>
<style scoped lang="scss">
.zlBg_box {
  width: 100%;
  height: 100%;
  // position: absolute;
  // z-index: 99;
  .video_box {
    width: 100%;
    height: 100%;
  }
  .items {
    // position: absolute;
    // left: 485px;
    // top: 500px;
    .item1 {
      position: absolute;
      left: 485px;
      top: 550px;
      z-index: 999;
      img {
        transform: scale(0.8);
      }
      & div {
        font-size: 28px;
        color: #fff;
        font-family: PangMenZhengDao;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 32px;
      }
    }
    .item2 {
      position: absolute;
      left: 714px;
      top: 648px;
      z-index: 999;
      img {
        transform: scale(0.8);
      }
      & div {
        font-size: 28px;
        color: #fff;
        font-family: PangMenZhengDao;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 32px;
      }
    }
    .item3 {
      position: absolute;
      left: 984px;
      top: 632px;
      z-index: 999;
      img {
        transform: scale(0.8);
      }
      & div {
        font-size: 28px;
        color: #fff;
        font-family: PangMenZhengDao;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 32px;
      }
    }
    .item4 {
      position: absolute;
      left: 1182px;
      top: 550px;
      z-index: 999;
      img {
        transform: scale(0.8);
      }
      & div {
        font-size: 28px;
        color: #fff;
        font-family: PangMenZhengDao;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 32px;
      }
    }
  }
}
</style>
