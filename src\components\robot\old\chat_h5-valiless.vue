<style lang="less" scoped>
  @media screen and (max-width: 1366px) {
    .qa-container {
      height:100%;
      .qa-box {
        &.wrapper {
          // height: 375px;
          height: calc(100% - 70px);
        }
      }
    }
  }
  @media screen and (min-width: 1367px) {
    .qa-container {
      height:100%;
      .qa-box {
        &.wrapper {
          height: calc(100% - 70px);
          // height: 500px;
        }
      }
    }
  }
  .qa-container {
    .qa-box {
      margin-top: 5px;
      &.wrapper {
        width: 100%;
        overflow: hidden;
        position: relative;
      }
      .item {
        margin: 8px 0;
        padding: 0 8px;
        >div {
          background: #F1F1FA;
          border-radius: 4px;
          padding: 5px 8px;
          margin: 0 8px;
          word-break: break-all;
          &.answer-bg {
            background: #F9F7ED;
            max-width: 87%;
            position: relative;
            .height {
              max-height: 272px;
              overflow: hidden;
            }
            .dotting {
              color: #007bff;
              display: inline-block; min-width: 2px; min-height: 2px;
              box-shadow: 2px 0 currentColor, 6px 0 currentColor, 10px 0 currentColor; /* for IE9+, ..., 3个点 */
              animation: dot 1s infinite step-start both; /* for IE10+, ... */
            }
            .dotting:before { content: '...'; } /* for IE8 */
            .dotting::before { content: ''; } /* for IE9+ 覆盖 IE8 */
            :root .dotting { margin-right: 8px; } /* for IE9+,FF,CH,OP,SF 占据空间*/
            @keyframes dot {
              25% { box-shadow: none; }                                  /* 0个点 */
              50% { box-shadow: 2px 0 currentColor; }                    /* 1个点 */
              75% { box-shadow: 2px 0 currentColor, 6px 0 currentColor;  /* 2个点 */ }
            }
          }
          &.related-btn{
            background-image: linear-gradient(to right, #009A98, #005773);
          }
          &.related-btn.active{
            background: #fff;
            color: #005773;
          }
        }

      }
      .look-more {
        height: 24px;
        background: #F9F7ED;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }
      img {
        width: 44px;
        height: 44px;
        /*border-radius: 16px;*/
      }
      .related-btn {
        font-size: 12px;
        margin: 0 0 8px 50px;
        width: 85px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        background-image: linear-gradient(to right, #009A98, #005773);
        color: #fff;
        border-radius: 4px;
        box-shadow: 1px 1px 5px 0px #bfbfbf;
        cursor: pointer;
        &.active {
          background: #fff;
          color: #005773;
        }
        &.more {
          margin-left: 10px;
        }

      }
      .related-detail {
        font-size: 12px;
        width: 85%;
        margin: 12px 0 8px 50px;
        padding: 10px 20px;
        box-shadow: 1px 1px 5px 0px #bfbfbf;
        position: relative;
        .cursor-active {
          overflow: hidden;
          height: 20px;
          cursor: pointer;
          display: flex;
          &.active {
            color: #009A98;
          }
          &:hover {
            color: #009A98;
          }
          .index {
            color: #E7865A;
            margin: 7px 5px 0 0;
            width: 3px;
            height: 3px;
            border-radius: 3px;
            background-image: linear-gradient(to right, #009A98, #005773);
          }
          .detail {
            width: 98%;
          }
        }
        &.more-answer {
          .cursor-active {
            align-items: unset;
            .index {
              margin-top: 7px;
            }
          }
          .triangle {
            left: 95px;
            &.shadow {
              left: 100px;
            }
          }
        }

        .triangle {
          position: absolute;
          top: 0;
          left: 18px;
          width: 24px;
          height: 10px;
          background: #fff;
          z-index: 2;
          &.shadow {
            width: 10px;
            top: -5px;
            left: 25px;
            box-shadow: 1px 1px 5px 0px #bfbfbf;
            z-index: 1;
            transform: rotate(45deg);
          }
        }
        .icon {
          position: absolute;
          top: 10px;
          right: 10px;
        }
      }
    }
    .question-box {
      position: fixed;
      bottom: 0;
      width: 95%;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      padding: 8px 0;
      .question-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 16px;
        border-radius: 8px 0 0 8px;
        background: #EAEAEA;
      }
      .send-btn {
        border: 0;
        border-radius: 16px;
        box-shadow: 2px 3px 5px 1px #EBEBEB;
        padding: 0 15px;
        margin: 8px 20px;
        &:hover {
          background: #2D8CF0;
          color: #fff;
        }
      }
    }
  }
  .wenwen-footer {
       width:100%;
       height:3.5rem;
       position:absolute;
       bottom:1px;
       // top: calc((100% - 55px));
       left:0;
       background:#e5e5e5;
       padding:10px;
       border-top:solid 1px #ddd;
       box-sizing:border-box;
       display:flex;
       justify-content:space-around;
       align-items:center;
       padding-top:1%;
     }
  .dropdown {
    width:16%;
    height:20%;
    position:absolute;
    bottom:1px;
    // top: calc((100% - 55px));
    left:0;
    background:#e5e5e5;
    padding:10%;
    padding-bottom:10%;
    border-top:solid 1px #ddd;
    box-sizing:border-box;
    display:flex;
    justify-content:space-around;
    align-items:center;
    padding-top:1%;
  }
  .wenwen_btn,.wenwen_help {
    width:15%;
    text-align:center;
  }
  .wenwen_btn img,.wenwen_help img {
    height:30px;
  }
  .wenwen_text {
    /*height:90px;*/
    /*border-bottom:solid 1px #C3BFBF;*/
    box-sizing:border-box;
    width:70%;
    text-align:center;
    overflow:hidden;
    margin-left:2%;
  }
  .additionalQuestions {
    width: 55%;
    position: absolute;
    bottom: 8%;
    margin-left: 16%;
    left: 0;
    background: #fff;
    border-top: solid 1px #ddd;
    box-sizing: border-box;
    display: flex;
    justify-content: space-around;
    border-radius: 5px;
    align-items: center;
    padding-top: 0%;
  }
  .questionsDiv{
    ::v-deep .ivu-select-item  {
      padding: 0px;
      background:#ffffff;
    }
  }
  .circle-button {
    padding:0 5px;
  }
  .wenwen_text .circle-button {
    font-size:14px;
    color:#666;
    line-height:38px;
  }
  .write_box {
    width:100%;
    /*height:90px;*/
    line-height:40px;
  }
  .write_box input {
    height:2.3rem;
    padding:0 6px;
    line-height:40px;
    width:100%;
    box-sizing:border-box;
    border:solid 1px #ddd;
    font-family: PingFangSC-Regular, sans-serif;
    font-size:14px;
    border-radius: 5px;
  }
  .wenwen_help button {
    width:98%;
    background:#5076b7;
    color:#fff;
    border-radius:5px;
    border:0;
    height:2rem;
    display:block;
    font-family: PingFangSC-Regular, sans-serif;
  }
  #wenwen {
    height:100%;
  }
  .speak_window {
    overflow-y:scroll;
    height:100%;
    width:100%;
    position:fixed;
    top:0;
    left:0;
  }
  .speak_box {
    // margin-bottom:5px;
    padding:0px 10px 10px;
  }
  .speak_box_top {
    // margin-bottom:5px;
    padding:10px 10px 0px;
  }
  .question{
    margin-bottom:5px;
    padding-top:10px;
    padding-bottom:0px;
  }
  .answer {
    margin-bottom:5px;
    // padding-bottom: 10px;
  }
  .answer_top {
    margin-bottom:0px;
  }
  .question {
    text-align:right;
  }
  .question>div {
    display:inline-block;
  }
  .left {
    float:left;
  }
  .right {
    float:right;
  }
  .clear {
    clear:both;
  }
  .heard_img {
    height:44px;
    width:44px;
    // display: inline-block;
    padding-right: 10px;
    border-radius:3px;
    overflow:hidden;
    background:#ddd;
  }
  .heard_img img {
    width:100%;
    height:100%
  }
  .question_text,.answer_text {
    box-sizing:border-box;
    position:relative;
    display:table-cell;
    min-height:45px;
  }
  .question_text {
    padding-right:12px;
  }
  .answer_text {
    padding-left:12px;
    position: relative;
    max-width: 65vw;
    min-width: 65vw;
    display: inline-block;
  }
  .question_text p {
    border-radius:5px;
    padding:.5rem;
    margin:0;
    font-size:14px;
    line-height:25px;
    box-sizing:border-box;
    vertical-align:middle;
    display:table-cell;
    word-wrap:break-word;
    max-width:65vw;
  }
  .answer_text p, .answer p {
    border-radius:5px;
    padding:.5rem;
    margin:0;
    font-size:14px;
    line-height:25px;
    box-sizing:border-box;
    vertical-align:middle;
    display:table-cell;
    word-wrap:break-word;
    /*max-width:65vw;*/
    /*min-width:10vw;*/
  }
  .answer_text p {
    background:#fff;
    text-align: justify!important;
  }
  .question_text p {
    background:#8CCC49;
    color:#000;
    text-align:left;
  }
  .question_text i,.answer_text i {
    width:0;
    height:0;
    border-top:5px solid transparent;
    border-bottom:5px solid transparent;
    position:absolute;
    top:14px;
  }
  .answer_text i {
    border-right:6px solid #fff;
    left:6px;
  }
  .question_text i {
    border-left:6px solid #8CCC49;
    right:6px;
  }
  .answer_text p a {
    color:#42929d;
    display:inline-block;
  }
  audio {
    display:none;
  }
  .saying {
    position:fixed;
    bottom:30%;
    left:50%;
    width:120px;
    margin-left:-60px;
    display:none;
  }
  .saying img {
    width:100%;
  }
  .tab { white-space:nowrap; overflow-x:auto; }		/*注释1*/
  /*.tab::-webkit-scrollbar { width:0; height:0; display: none; }     !*注释2*!*/
  .tab div {
    list-style: none;
    line-height: 30px;
    margin-right: 10px;
  }
  #queTab p {
    padding: 0 .5rem 0 .5rem;
  }
  #botTab {
    white-space: nowrap;
    overflow-x: auto;
    width: 100%;
    position: fixed;
    bottom: -5px;
    margin-bottom: 3.5rem;
    background: #eeeeee;
  }
  #botTab div {
    list-style: none;
    line-height: 50px;
    margin-right: 10px;
  }
  #botTab botton {
    border-radius: 500px;
    padding: 8px 15px 8px 15px;
    background: white;
    margin-left: 10px;
    margin-right: 10px;
  }
  .up_img {
    height:22px;
    width:22px;
    border-radius:500px;
    overflow:hidden;
    background:rgba(36 142 36 0);
    position: absolute;
    bottom: 60px;
  }
  .down_img {
    height:22px;
    width:22px;
    border-radius:500px;
    overflow:hidden;
    background:rgba(36 142 36 0);
    position: absolute;
    bottom: 20px;
  }
  .up_img img, .down_img img {
    width:100%;
    height:100%
  }
  input, p, img, botton, a {
    font-size: 14px;
  }
  .mainBox{
    text-align: center;
    background-color: #fff;
    border-radius: 20px;
    position:fixed;
    top:50%;
    left:50%;
    transform:translateX(-50%) translateY(-50%);

  }
</style>

<template>
  <div class="qa-container" style="height:100%">
    <div class="wrapper qa-box">
      <vue-scroll :ops="options" ref="divScroll">
        <div class="speak_box_top" style="padding-top:5px">
          <div class="answer" >
            <div class="heard_img left">
              <img src="../../images/smart_qa.png"/>
            </div>
            <div class="answer_text">
              <div id="sugTab_0" style="border-radius: 5px;">
                <p style="padding: .5rem .5rem 0 .5rem;height:44px;text-algin:center">{{greeting}}</p>
              </div>
              <i></i>
            </div>
          </div>
          <div class="answer" style="padding-top:5px" v-if="favorAll.length>0">
            <div class="heard_img left">
              <img src="../../images/smart_qa.png"/>
            </div>
            <div class="answer_text">
              <div id="sugTab_0" style="background: white;border-radius: 5px;">
                <p style="padding: .5rem .5rem 0 .5rem;">猜您关心：</p>
                <p style="cursor: pointer;color: dodgerblue;position: absolute; top: 0;right: 0;display: flex;padding-bottom: 0px;" @click="change(typeIndex,favorIndex,'favor')">
                  <img src="../../images/refresh.png" style="width: 22px;height: 22px;" >换一换</p>
                <vue-scroll :ops="options2" ref="divScroll2">
                  <Tabs  size="small"  @on-click="changeType">
                    <TabPane v-for="data,index in favorAll" :label="data.label" >
                    </TabPane>
                  </Tabs>
                </vue-scroll>
                <div  @click="quesOther(item.value)" style="padding:2px 10px 10px" v-for="item,itemIndex in favorAll[typeIndex].question.slice(favorIndex,favorIndex+3) ">
                  {{item.value}}
                </div>
              </div>
              <i></i>
            </div>
          </div>
        </div>
        <div class="mainBox" v-if="isShowDialog" style = "height:150px;width:350px;border:1px;z-index:9999">
          <div  style="height:100px;width:300px;box-sizing: border-box;display:inline-block;"  class="ctrlProcessWave"></div>
          <div style="position: absolute;left: 30%;">
            <Icon type="md-close" size = "40" @click="cancelRecorder()" />
          </div>
          <div style="position: absolute;left: 55%;">
            <Icon type="md-checkmark" size = "40" @click="recStop()" />
          </div>
        </div>

        <div class="answer">
          <audio ref="LogAudioPlayer" style="width:100%"></audio>
          <div class="speak_box" v-for="(item,index) in qaList" ref="speak_box_ref">
            <div v-if="item.question!=null && item.msgType != 'audio'"  class="question">
              <div class="heard_img right">
                <img src="../../images/user_img.png"/>
              </div>
              <div v-if="item.msgType != 'image'" class="question_text clear"><p>{{item.question}}</p><i></i>

              </div>
              <div v-if="item.msgType == 'image'" >
                <viewer :image="item.question" v-if="item.question != ''">
                  <img :src="item.question" :key="item.question"  style="width:auto;height:auto;max-height:180px;max-width:210px;cursor:pointer;padding-right:8px;"><i></i>
                </viewer>
              </div>
            </div>

            <div v-if ="item.obj !=null && item.botType == null " class="question">
              <div class="heard_img right">
                <img src="../../images/user_img.png"/>
              </div>
              <div :style="{color:item.obj.color==1?'red':item.obj.color==2?'green':item.obj.color}">
                <template v-if="item.obj.res">
                  <div class="question_text clear"><p @click="recplay(item.obj)">{{(item.obj.res.duration/1000).toFixed(1)}}''<i></i>
                    <img v-if ="item.obj.res.isPlaying == true"   style="width: 14px;height: 14px; " src="../../images/voiceplaying.gif">
                    <img v-if = "item.obj.res.isPlaying == false" style="width: 14px;height: 14px; " src="../../images/voiceplayback.png">
                  </p> <i></i>
                  </div>
                </template>
              </div>
            </div>

            <div class="answer" v-if="((index < qaList.length-1)||(flag==true)) && item.botType != 'flow_selectedReplyOp' && item.botType !=  'flow_definedReply'" >
              <div class="answer" v-if="item.question == null">
                <div v-if="item.answerFrom == null && item.botType != null">
                  <div v-if="item.botType != 'flow_selectedReplyOp'" class="heard_img left">
                    <img src="../../images/smart_qa.png"/>
                  </div>
                  <div class="answer_text" v-if="(item.msgType == 'text' || item.msgType == 'audio')&& item.botType != 'flow_selectedReplyOp'">
                    <div v-if="item.msgType == 'text'" style="display:inline-block;padding-right:5px">
                      <p style="height:44px;"  v-html="item.msg">{{item.msg}}</p>
                    </div>
                    <div v-if="item.msgType == 'audio'" style="display:inline-block;padding-right:5px">
                      <div ><p @click="recplay(item.obj)">{{(item.obj.res.duration).toFixed(1)}}''<i></i>
                        <img v-if ="item.obj.isPlaying == true"   style="width: 14px;height: 14px; " src="../../images/voiceplaying.gif">
                        <img v-if = "item.obj.isPlaying == false" style="width: 14px;height: 14px; " src="../../images/voiceplayback.png">
                        </p> <i></i>
                      </div>
                    </div>
                    <div v-if="item.botType == 'kb'" style="display:inline-block;">
                      <div v-if="qaList[index].comType == 0" @click="up(index)" style="position: relative;display: inline-block;">
                        <img style="height:22px;width:22px;border-radius:500px;overflow:hidden;background:rgba(36 142 36 0);position: absolute;bottom: 28px;" src="../../images/up.png" >
                      </div>
                      <div v-if="qaList[index].comType == 1" style="position: relative;display: inline-block;">
                        <img style="height:22px;width:22px;border-radius:500px;overflow:hidden;background:rgba(36 142 36 0);position: absolute;bottom: 28px;" src="../../images/up_1.png" >
                      </div>
                      <div v-if="qaList[index].comType == 0" @click="down(index)" style="position: relative;display: inline-block;">
                        <img style="height:22px;width:22px;border-radius:500px;overflow:hidden;background:rgba(36 142 36 0);position: absolute;bottom: 0px;" src="../../images/down.png">
                      </div>
                      <div v-if="qaList[index].comType == 2" style="position: relative;display: inline-block;">
                        <img style="height:22px;width:22px;border-radius:500px;overflow:hidden;background:rgba(36 142 36 0);position: absolute;bottom: 0px;" src="../../images/down_1.png">
                      </div>
                    </div>
                  </div>
                  <div class="answer_text" v-if="item.msgType == 'img'">
                    <viewer>
                      <img  :src="item.msg" :alt="item.msg" style="width:auto;height:auto;max-height:180px;max-width:210px;cursor:pointer;padding-left:8px;padding-top:8px">
                    </viewer>
                  </div>
                </div>
                <div  v-if="item.answerFrom != null" style="display:inline-block">
                  <div class="heard_img left">
                    <img src="../../images/smart_qa.png"/>
                  </div>
                  <div class="answer_text" ref="answer_width"  v-if="item.answerFrom != null">
                    <div style="border-radius:5px;background-color: white;padding:5px">
                      <div v-for="file in item.answerFrom ">
                        <a style = "color: dodgerblue; textDecoration: underline;background: #f3f3f3;" @click="download(file.fileUrl)">{{file.fileName}}</a>
                      </div>
                      <br>
                      <i></i>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="index > 0 && qaList[index].comType == 3" style="padding-bottom:10px;padding-top:5px">
                <div v-if="index > 0 && qaList[index].comType == 3" style="background: white; border-radius: 5px" class="answer" >
                  <p style="font-weight: bold;">意见反馈</p>
                  <div style="text-align: center">
                    <Button v-if="qaList[index].feedbackType == 0"  type="primary" style="height: 25px;border-radius: 500px;border: solid 1px #ccc;padding: 2px 10px 0 10px;margin-left: 20px; float: left;">
                      答非所问
                    </Button>
                    <Button v-if="qaList[index].feedbackType != 0" @click="changeComment(0,index)" style="height: 25px;border-radius: 500px;border: solid 1px #ccc;padding: 2px 10px 0 10px;margin-left: 20px; float: left;">
                      答非所问
                    </Button>
                    <Button v-if="qaList[index].feedbackType == 1"  type="primary" style="height: 25px;display: inline-block;border-radius: 500px;border: solid 1px #ccc;padding: 2px 10px 0 10px;">
                      内容看不懂
                    </Button>
                    <Button v-if="qaList[index].feedbackType != 1 " @click="changeComment(1,index)" style="height: 25px;display: inline-block;border-radius: 500px;border: solid 1px #ccc;padding: 2px 10px 0 10px;">
                      内容看不懂
                    </Button>
                    <Button v-if="qaList[index].feedbackType == 2 "  type="primary" style="height: 25px;border-radius: 500px;border: solid 1px #ccc;padding: 2px 10px 0 10px;margin-right: 20px;float: right;" >
                      其他
                    </Button>
                    <Button v-if="qaList[index].feedbackType != 2 " @click="changeComment(2,index)" style="height: 25px;border-radius: 500px;border: solid 1px #ccc;padding: 2px 10px 0 10px;margin-right: 20px;float: right;" >
                      其他
                    </Button>
                  </div>
                  <div v-if="qaList[qaList[index].index].feedback == false" style="padding: 10px 20px 10px 20px;">
                    <input disabled style="height: 25px;width: 100%;border-radius: 5px;background: #e5e5e5;border: none;margin-right: 20px;" :placeholder="getCommentValue(index)"/>
                  </div>
                  <div v-if="qaList[qaList[index].index].feedback == null" style="padding: 10px 20px 10px 20px;">
                    <input v-model="commentValue" style="height: 25px;width: 100%;border-radius: 5px;background: #e5e5e5;border: none;margin-right: 20px;" placeholder="请在此输入您的建议"/>
                  </div>
                  <div style="text-align: -webkit-right; padding-bottom: 10px;" v-if="qaList[qaList[index].index].feedback == null ">
                    <Button type="primary" @click="submitComment(qaList[index].index)" style="height: 25px;display: inline-block;background: #1296db;border-radius: 500px;color: white;padding: 2px 10px 0 10px;margin-right: 20px;">
                      提交反馈
                    </Button>
                  </div>
                  <div  style="text-align: -webkit-right; padding-bottom: 10px;" v-if="qaList[qaList[index].index].feedback == false ">
                    <Button  disabled style="height: 25px;display: inline-block;border-radius: 500px;padding: 2px 10px 0 10px;margin-right: 20px;">
                      提交反馈
                    </Button>
                  </div>
                </div>
              </div>
              <div class="answer" v-if="item.botType=='other' && item.otherQues.length>0" >
                <div class="answer_text">
                  <div id="sugTab_0" style="background: white;border-radius: 5px;">
                    <p style="padding: .5rem .5rem 0 .5rem;">您可能还想问：</p>
                    <p v-if="otherQues.length>3" style="cursor: pointer;color: dodgerblue;position: absolute; top: 0;right: 0;display: flex;padding-bottom: 0px;"  @click="change(index,item.changeIndex,'other')">
                      <img src="../../images/refresh.png" style="width: 22px;height: 22px;">换一换</p>
                    <div v-for="(ques,otherIndex) in item.otherQues" @click="quesOther(ques.question)" style="padding:5px 10px 5px;color:#007bff">
                      [{{otherIndex+1}}]{{ques.question}}
                    </div>
                  </div>
                  <i></i>
                </div>
              </div>
              <div class="answer"  v-if="item.botType=='guest' && item.otherQues.length>0" >
                <div class="answer_text">
                  <div id="sugTab_0" style="background: white;border-radius:10px;">
                    <p style="padding: .5rem .5rem 0 .5rem;">猜您想问的是：</p>
                    <p v-if="otherQues.length>3" style="cursor: pointer;color: dodgerblue;position: absolute; top: 0;right: 0;display: flex;padding-bottom: 0px;"  @click="change(index,item.changeIndex,'like')">
                      <img src="../../images/refresh.png" style="width: 22px;height: 22px;">换一换</p>
                    <div v-for="(ques,likeIndex) in item.otherQues" @click="quesOther(ques.question)" style="padding:5px 10px 5px;color:#007bff">
                      [{{likeIndex+1}}]{{ques.question}}
                    </div>
                  </div>
                  <i></i>
                </div>
              </div>
            </div>
            <div class="answer" v-if="item.botType=='flow_selectedReplyOp' && item.selectedQuestionList.length>0" >
              <div class="heard_img left">
                <img src="../../images/smart_qa.png"/>
              </div>
              <div class="answer_text">
                <div id="sugTab_0" style="background: white;border-radius: 5px;">
                  <p style="padding: .5rem .5rem 0 .5rem;">{{item.msg}}</p>
                  <p :ref="'selectedChangeP'+index" style="cursor: pointer; color: dodgerblue;position: absolute; top: 0;right: 0;display: flex;padding-bottom: 0px;"  @click="changeQuesionts(index,item.selectedIndex)">
                    <img src="../../images/refresh.png" style="width: 22px;height: 22px;"  >换一换</p>
                  <div :ref="'selectedDiv'+index+'.'+otherIndex" v-for="(ques,otherIndex) in item.selectedQuestionList.slice(qaList[index].selectedIndex,qaList[index].selectedIndex+3)" @click="quesSelectedOther(ques,index,otherIndex,item.selectedIndex)" style="padding:5px 10px 5px;color:#007bff;cursor:pointer;" disabled="false">
                    {{ques.index}}. {{ques.msg}}
                  </div>
                </div>
                <i></i>
              </div>
            </div>
            <div class="answer" v-if="item.botType=='flow_definedReply'" >
              <div class="heard_img left">
                <img src="../../images/smart_qa.png"/>
              </div>
              <div class="answer_text">
                <div v-if="item.msgType == 'text'" style="display:inline-block;padding-right:5px">
                  <p style="height:44px;"  v-html="item.msg">{{item.msg}}</p>
                  <i></i>
                </div>
                <i></i>
              </div>
            </div>
          </div>
        </div>
      </vue-scroll>
    </div>
    <div  class="additionalQuestions" :style="{'height':adapteHeight}" v-if="question.length>0 && possibleQuestions.length > 0 && JSON.stringify(question)!='' && isText ">
      <div class="questionsDiv" style=" float:left;width:100%;">
        <Option v-for="(item,varIndex) in possibleQuestions" :value="item.sendMessage" :key="item.sendMessage">
          <div ref="possibleQuestion" style="width:100%;height:100%;z-index:2 ;" @mouseenter="changeColor('enter',varIndex)" @mouseleave="changeColor('leave',varIndex)" @click="quesOther(item.sendMessage)">
            <div style="padding-left: 10px;height: 35px;line-height:35px;overflow: hidden;text-overflow: ellipsis;" >
              {{item.sendMessage}}
            </div>
          </div>
        </Option>
      </div>
    </div>
    <div class="dropdown" v-show = "menuIsShow" style="padding-right: 5px;padding-left: 5px; padding-bottom: 4%;" >
<!--      <img v-if ="audio == 'false' || audio == null" src="../../images/user_img.png" style="width: 48px;margin-left: -10px;margin-top: -10px;">-->
<!--      <img v-if ="audio == 'true'" src="../../images/voice.png"  style="width: 30px;height: 30px; "   @click="recOpenBefore()"/>-->
<!--      <img v-if ="audio == 'true'" src="../../images/keyboard.png"  style="width: 30px;height: 30px; float: left;"   @click="recClose()"/>-->

      <Tooltip v-if ="audio == 'true'" placement="top" content="录入语音">
        <img  src="../../images/voice.png" style="width: 30px;height: 30px; " @click="recOpenBefore()"  >
      </Tooltip>

      <Tooltip v-if ="audio == 'true'" placement="top" content="输入文字">
        <img  src="../../images/keyboard.png" style="width: 30px;height: 30px; " @click="recClose()"  >
      </Tooltip>

      <Tooltip v-if ="audio == 'true'" placement="top" content="上传图片">
        <img  @click="choiceImg" src="../../images/uploadImage.png" style="width: 28px;height: 28px; " >
        <input  v-show="false" ref="filElem" type="file"  @change="getFile($event)">
      </Tooltip>

      <Tooltip  v-if ="audio != 'true'" placement="top" style="padding-right: 5px;padding-left: 5px;" content="上传图片">
        <img  @click="choiceImg" src="../../images/uploadImage.png" style="width: 28px;height: 28px; " >
        <input  v-show="false" ref="filElem" type="file"  @change="getFile($event)">
      </Tooltip>
    </div>
    <!-- <div class="wenwen-footer" :style="{'top':getTop,'buttom':getButtom}"> -->
    <div class="wenwen-footer" :style="{'top':getTop,'buttom':getButtom}">
      <div  style="width: 30px;height: 30px;margin-left: -10px;  float: left;">
        <div class="menu" style="padding-left: 30px;">

<!--          <div class="main" >-->
<!--            <img @click="menuIsShow = !menuIsShow"  src="../../images/add_new.png" style="width: 30px;height: 30px; ">-->
<!--          </div>-->

      <img v-if ="audio == 'false' || audio == null" src="../../images/user_img.png" style="width: 48px;margin-left: -35px;margin-top: -10px;">
      <img v-if ="audio == 'true' && isText" src="../../images/voice.png"  style="width: 30px;height: 30px;margin-left: -30px;"   @click="recOpenBefore()"/>
      <img v-if ="audio == 'true' && isRecorder" src="../../images/keyboard.png"  style="width: 30px;height: 30px;margin-left: -30px; float: left;"   @click="recClose()"/>

        </div>

      </div>
      <!--      <img v-if =isText src="../../images/user_img.png" style="width: 48px;margin-left: -10px;">-->
      <div  class="wenwen_text left" style=" width: 80%; margin-left: -10px;">
        <div  class="write_box">
          <button v-if ="audio == 'true' && isRecorder" style="width:100%;height:32px ; line-height: 16px; background-color:white;border: none;border-radius: 5px" class="left" @click="recOpen(); " >
            <p style="color: #757575">开始录音</p></button>
          <Input v-if ="audio == 'false' || audio == null " @on-change = "queryQuestions()" v-model="question" type="text" style=" width: 70%;" class="left" placeholder="请在这里输入您想咨询的问题"/>
          <Input v-if ="audio == 'true' && isText" @on-change = "queryQuestions()" v-model="question" type="text" style=" width: 70%;" class="left" placeholder="请在这里输入您想咨询的问题"/>
        </div>
        <div v-if="question.length>0 && JSON.stringify(question)!='' && (audio == 'false' || audio == null) && isText" style="width: 20% ;height: 2.3rem;" class="wenwen_help right" >
          <button @click="sendQuestion(true);"  class="right">发送</button>
        </div>

        <div v-if="(audio == 'false' || audio == null) && question.length==0 || JSON.stringify(question)=='' && isText" style="width: 20%;height: 2.3rem;" class="wenwen_help right">
          <Tooltip v-if ="audio == 'true'" placement="top" content="上传图片">
            <img  @click="choiceImg" src="../../images/uploadImage.png" style="width: 25px;height: 25px; margin-top: 5px" >
            <input  v-show="false" ref="filElem" type="file"  @change="getFile($event)">
          </Tooltip>

          <Tooltip  v-if ="audio != 'true'" placement="top" style="padding-right: 5px;padding-left: 5px;" content="上传图片">
            <img  @click="choiceImg" src="../../images/uploadImage.png" style="width: 28px;height: 28px; " >
            <input  v-show="false" ref="filElem" type="file"  @change="getFile($event)">
          </Tooltip>
        </div>

        <div v-if="audio == 'true' && question.length>0 && JSON.stringify(question)!='' && isText" style="width: 20% ;height: 2.3rem;" class="wenwen_help right" >
          <button @click="sendQuestion(true);"  class="right">发送</button>
        </div>
<!--        <div v-if="audio == 'true' && isText && question.length==0 || JSON.stringify(question)=='' " style="width: 20%;height: 2.3rem;" class="wenwen_help right">-->
<!--          <button class="right">发送</button>-->
<!--        </div>-->
        <div v-if="audio == 'true' && isText && question.length==0 || JSON.stringify(question)=='' " style="width: 20%;height: 2.3rem;" class="wenwen_help right">
          <Tooltip v-if ="audio == 'true'" placement="top" content="上传图片">
            <img  @click="choiceImg" src="../../images/uploadImage.png" style="width: 25px;height: 25px; margin-top: 5px" >
            <input  v-show="false" ref="filElem" type="file"  @change="getFile($event)">
          </Tooltip>

          <Tooltip  v-if ="audio != 'true'" placement="top" style="padding-right: 5px;padding-left: 5px;" content="上传图片">
            <img  @click="choiceImg" src="../../images/uploadImage.png" style="width: 28px;height: 28px; " >
            <input  v-show="false" ref="filElem" type="file"  @change="getFile($event)">
          </Tooltip>
        </div>

        <div>
        </div>
      </div>


    </div>
    <Modal v-model="showPic" :closable="false" :fullscreen="true" :footer-hide="true" >
      <div style="width:100%;height:100%" @click="closePic()">
        <img style="max-width:960px;top:50%;left:50%" :src="showPicUrl" alt="">
      </div>
    </Modal>
    <div v-if="recOpenDialogShow" style="z-index:99999;width:100%;height:100%;top:0;left:0;position:fixed;background:rgba(0,0,0,0.3);">
      <div style="display:flex;height:100%;align-items:center;">
        <div style="flex:1;"></div>
        <div style="width:240px;background:#fff;padding:15px 20px;border-radius: 10px;">
          <div style="padding-bottom:10px;">录音功能需要麦克风权限，请允许；如果未看到任何请求，请点击忽略~</div>
          <div style="text-align:center;"><a @click="waitDialogClick" style="color:#0B1">忽略</a></div>
        </div>
        <div style="flex:1;"></div>
      </div>
    </div>
  </div>
</template>
<script>
  import Util from '@/libs/util.js'
  import $ from 'jquery'
  import blobDuration from 'get-blob-duration'
  import vueScroll from 'vuescroll';
  import Recorder from 'recorder-core'
  import 'recorder-core/src/engine/wav'
  //可选的扩展
  import 'recorder-core/src/extensions/waveview'
  import { constants } from 'os';
  import { log } from 'util';
  import { v4 } from 'uuid'
  export default {
    component: {
      vueScroll
    },
    data () {
      return {
        audio:'true',
        appKey:'19ea4ca6480f4912bf15',
        // appSecret:this.$route.query.token.substring(20),
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE',
          'Authorization': localStorage.token,
        },
        options: {
          vuescroll: {
            mode: 'native',
            sizeStrategy: 'percent',
            detectResize: true,
          },
        },
        options2: {
          vuescroll: {
            mode: 'native',
            sizeStrategy: 'percent',
            detectResize: true,
            locking: true,
          },
          rail: {
            size: '0px',
          },
          bar: {
            size: '0px',
          }
        },
        options3: {
          vuescroll: {
            mode: 'native',
            sizeStrategy: 'percent',
            detectResize: true,
            locking: true,
          },
          rail: {
            size: '0px',
          },
          bar: {
            size: '0px',
          }
        }, Rec:Recorder
        ,type:"wav"
        ,bitRate:16
        ,sampleRate:16000

        ,rec:0
        ,duration:0
        ,powerLevel:0

        ,recOpenDialogShow:0
        ,logs:[],
        // answerList:[],
        // otherAnswerList:[],
        // answerFromList:[],
        question:"",
        isShowDialog:false,
        isText:true,
        isRecorder:false,
        isPhoto:false,
        recorderData:"",
        showPic:false,
        showPicUrl:'',
        robotName:"",
        robotDesc:"",
        greeting:"",
        robotId:"",
        sessid:"",
        userId:"",
        list:"",
        flag: false,
        feedbackType:'',
        commentValue:'',
        ans_length:0,
        sugCount:0,
        indexSug:0,
        blankMsg:"",
        otherQues:[],
        favorQues:[],//用于展示猜你关心
        favorAll:[],//全部猜您关心数据
        favorIndex:0,//分类下问题index
        typeIndex:0,//分类index
        czresponse: {},
        favorTypes:[],
        showLoading: false,
        loading: false,
        isSending: false,
        qaList:[],
        screenHeight: document.body.offsetHeight,
        imageWidth: document.body.offsetWidth*0.69+"px",
        imageHeight: "",
        fileObj : {},
        base64File:"",
        menuIsShow:false,
        possibleQuestions:[],
        selectedIndex:0,
      }
    },
    watch: {
      screenHeight (val) {
        // 为了避免频繁触发resize函数导致页面卡顿，使用定时器
        if (!this.timer) {
          // 一旦监听到的screenWidth值改变，就将其重新赋给data里的screenWidth
          this.screenHeight = val
          this.timer = true
          let that = this
          setTimeout(function () {
            // 打印screenWidth变化的值
            that.timer = false
          }, 400)
        }
      }
    },
    computed: {
      getTop(){
        let ua = navigator.userAgent.toLowerCase();
        let isiOS = !!(ua.indexOf('iphone') > -1 || ua.indexOf('mac') > -1);
        if(!isiOS){
          return null;
        }else{
          let clientHeight = document.documentElement.clientHeight-55 + "px"
          //窗口可视区域发生变化的时候执行
          window.onresize = () => {
            clientHeight = document.documentElement.clientHeight-55 + "px"
            return clientHeight
          }
          return clientHeight
        }
      },
      getButtom(){
        let ua = navigator.userAgent.toLowerCase();
        let isiOS = !!(ua.indexOf('iphone') > -1 || ua.indexOf('mac') > -1);
        if(isiOS){
          return null;
        }else{
          return 1+"px";
        }
      },
      // getAnswerWidth(){
      //   this.imageWidth = document.body.offsetWidth*0.646+"px";
      //   this.imageWidth
      //   return this.$refs.speak_box_ref[0].clientWidth*0.65+50+"px"
      // },
      bottom(){
        return '-20px';
      },
      adapteHeight(){
        // if(this.possibleQuestions && this.possibleQuestions.length == 1){
        //   return 6+"%";
        // }else if(this.possibleQuestions && this.possibleQuestions.length == 2){
        //   return 12+"%";
        // } else if(this.possibleQuestions && this.possibleQuestions.length >= 3){
        //   return 18+"%";
        // }
        return this.possibleQuestions.length*35+"px";
      }
    },
    created() {
      var lett = this;
      document.onkeydown = function(e) {
        var key = window.event.keyCode;
        if (key == 13) {
          // lett.question = this.question.trim();
          lett.question = lett.question.trim();
          if(JSON.stringify(lett.question).length>2){
            lett.sendQuestion(true);
          }
        }
      }
    },
    methods: {
      sendMsg(){
        this.qaList.push({'question':'测试','answer':'回答'})
        this.$refs["divScroll"].scrollIntoView("#d3", 600);
      },
      openPic(arg){
        this.showPic=true
        this.showPicUrl=arg
      },
      closePic(){
        this.showPic=false
      },
      up(index){
        //点赞
        var qaListParam = this.qaList[index];
        this.qaList[index].comType = '1';
        //点赞进行回复
        this.pushQaList({
          answer: [{"msg":'感谢您的点赞,么么哒',"msgType":"text",'botType':"comment",}],
          botType:"comment",
        })
        console.log(qaListParam)
        this.comment({
          'userId':this.$route.query.userId,
          'index':null,
          'robotId': this.$route.query.robotId,
          'sessionId':this.sessid,
          'message':qaListParam.ques,
          'answer':qaListParam.msg,
          'evalType':'1'
        })
        this.$refs["divScroll"].scrollIntoView("#d3", 500);
        this.randomScroll();
      },
      down(index){
        //点踩
        this.qaList[index].comType = '2';
        //点踩进行回复
        var qaListParam = this.qaList[index];
        this.comment({
          'userId':this.$route.query.userId,
          'index':index,
          'robotId': this.$route.query.robotId,
          'sessionId':this.sessid,
          'message':qaListParam.question,
          'answer':qaListParam.msg,
          'evalType':'2',
          'feedbackType':qaListParam.feedbackType,
          'feedbackContent':this.commentValue
        })
        this.qaList[index].feedback = null;
        this.pushQaList({
          answer: [{"msg":'可以告诉我不满的原因吗？我会努力改进的哦~',"msgType":"text",'botType':"comment",}],
          botType:"comment",
          comType:3,
          index:index,
        })
        this.$refs["divScroll"].scrollIntoView("#d3", 500);
        this.randomScroll();
      },
      getCommentValue(index){
        return this.qaList[this.qaList[index].index].commentValue;
      },
      changeComment(num,index){
        if(this.qaList[this.qaList[index].index].feedbackFlag == null){
          this.feedbackType = num;
          this.qaList[index].feedbackType = num;
          this.qaList[this.qaList[index].index].feedbackType = num;
        }
      },
      submitComment(index){
        this.qaList[index].commentValue = this.commentValue;
        this.qaList[index].feedbackFlag = true;
        var qaListParam = this.qaList[index];
        if(qaListParam.feedbackType == null ){
          qaListParam.feedbackType = 0;
        }
        this.comment({
          'userId':this.$route.query.userId,
          'id':qaListParam.id,
          'index':null,
          'robotId': this.$route.query.robotId,
          'sessionId':this.sessid,
          'message':qaListParam.question,
          'answer':qaListParam.answer,
          'evalType':'2',
          'feedbackType':qaListParam.feedbackType,
          'feedbackContent':this.commentValue
        })
        this.qaList[index].feedback = false;
        this.feedbackType = '';
        this.commentValue = '';
        //点踩进行回复
        this.pushQaList({
          answer: [{"msg":'感谢您的反馈，我会努力学习哒~',"msgType":"text",'botType':"comment",}],
          botType:"comment",
          index:index,
        })
        this.$refs["divScroll"].scrollIntoView("#d3", 500);
        this.randomScroll();
      },
      comment(param){
        let self = this;
        var uuid = v4();
        self.$http.post('api/v2/robot/h5/msgEvaluate', {
          'param' : param
        },{
          headers: {
            appkey: self.appKey,
            // appSecret: self.appSecret,
            timeStamp : new Date().getTime(),
            nonce : uuid
          }
        }).then(function (response) {
          var id = response.data.data.id;
          if(param.index!=null){
            self.qaList[param.index].id = id;
          }
        })
          .catch(function (error) {
            console.log(error);
          });
      },
      getRobotById(){
        let self = this;
        var uuid = v4()
        self.$http.post('api/v2/robot/h5/getConfig',
          {
          },
          {
            headers: {
              appkey: self.appKey,
              // appSecret: self.appSecret,
              timeStamp : new Date().getTime(),
              nonce : uuid
            }
          }
        ).then(function (response) {
          self.greeting = response.data.data.greeting;
          self.robotName = response.data.data.robotName;
          document.title = self.robotName;
        })
          .catch(function (error) {
            console.log(error);
          });
      },
      getFavorList(){
        let self = this;
        var uuid = v4()
        self.$http.post('api/v2/qa/h5/recommendKnowledge',
          {
          },
          {
            headers: {
              appkey: self.appKey,
              // appSecret: self.appSecret,
              timeStamp : new Date().getTime(),
              nonce : uuid
            }
          }
        ).then(function (response) {
          var code = response.data.code
          if(code == 0){
            self.favorAll = response.data.data;
            self.favorIndex = 0;
          }
        })
          .catch(function (error) {
            self.favorQues = [];
            self.favorAll = [];
            self.favorIndex = 0;
            console.log(error);
          });
      },
      sendQuestion(type) {
        this.flag = false;
        this.question = this.question.trim()
        if(this.question) {
          if(this.question.length <= 200) {
            this.loading = true;
            this.showLoading = true;
            let list = this.question.split(' ');
            this.question = ""
            this.copyQuestion = list.join('');
            this.qaList.push({
              question: this.copyQuestion,
              hasAnswer: false,
              openAnswer: false
            });
            this.question = '';
            this.$refs["divScroll"].scrollIntoView("#d3", 500);
            this.msgType = '';
            this.handleWord(type);
          } else {
            this.blankMsg = '您输入的问题不能超过200字，请重新输入！';
            this.disabled = false;
            this.$emit('handleCompeted', true);
            this.isSending = false;
          }
        } else {
          this.blankMsg = '不能发送空白信息！';
          this.disabled = false;
          this.isSending = false;
        }
      },
      changeType(index){
        this.typeIndex = index;
        this.favorIndex = 0;
      },
      change(index,changeIndex,type){
        if(type=="other"){
          if(changeIndex+3<this.otherQues.length){
            changeIndex += 3;
          }else{
            changeIndex = 0;
          }
          var paramOther1 = [];
          var j = 0;
          for(var i=changeIndex;i<this.otherQues.length;i++){
            paramOther1.push(this.otherQues[i])
            j++;
            if(j==3){
              j=0;
              break;
            }
          }
          this.qaList[index].changeIndex = changeIndex;
          this.qaList[index].otherQues= paramOther;
        }
        if(type=="like"){
          if(changeIndex+3<this.otherQues.length){
            changeIndex += 3;
          }else{
            changeIndex = 0;
          }
          var paramOther = [];
          var j1 = 0;
          for(var i1=changeIndex;i1<this.otherQues.length;i1++){
            paramOther.push(this.otherQues[i1])
            j1++;
            if(j1==3){
              j1=0;
              break;
            }
          }
          this.qaList[index].changeIndex = changeIndex;
          this.qaList[index].otherQues= paramOther;
        }
        if(type=="favor"){
          var length = this.favorAll[index].question.length;
          if(changeIndex+3<length){
            this.favorIndex+=3;
          }else{
            this.favorIndex=0;
          }
        }
      },
      changeQuesionts(index,changeIndex){
        this.$nextTick(() => {
          if (this.$refs['selectedChangeP' + index][0].style.color == 'rgb(81, 90, 110)') {
            return;
          }
          this.qaList[index].selectedQuestionList;
          var length = this.qaList[index].selectedQuestionList.length;
          if (changeIndex + 3 < length) {
            this.qaList[index].selectedIndex += 3;
          } else {
            this.qaList[index].selectedIndex = 0;
          }
        })
      },
      handleWord(type) {
        var uuid = v4();
        Util.http
          .post(`${Util.baseUrl}/api/v2/robot/h5/sendMessage`, {
            message: this.copyQuestion,
            msgType:this.msgType,
            sessionId: this.sessid,
            userId:this.userId,
            base64File:this.base64File,
            // 'robotId': this.$route.query.robotId,
          },{
            headers: {
              appkey: this.appKey,
              // appSecret: this.appSecret,
              timeStamp : new Date().getTime(),
              nonce : uuid
            }
          })
          .then(res => {
            if(res.data.code == 0){
              if(res.data.data.botType == 'flow' ){
                this.$nextTick(()=>{
                  this.handleFlowTalk(res.data.data);
                })
              }else {
                this.pushQaList(res.data.data)
              }
            }
            if(res.data.code == 9){
              let data =  {
                "kbId" : null,
                "answerFrom" : [ ],
                "topkKnowledge" : [ ],
                "answer" : [ {
                  "msg" : "未识别录入语音，请重新录音",
                  "msgType" : "text",
                  "botType" : "reply"
                } ],
                "chatId" : null,
                "botType" : "reply"
              }
              this.pushQaList(data)
            }
            // this.addOtherQues();
            this.showLoading = false;
            this.loading = false;
            this.isSending = false;
          })
          .catch(error => {
            console.log("error"+error);
            this.handleResult({
              answer: '系统异常，请重试！',
              qaList: [],
              hasMore: false
            }, true);
            this.showLoading = false;
            this.loading = false;
            this.isSending = false;
          })
      },
      handleFlowTalk(data){
        for(var i = 0;i < data.answer.length;i++){
          if(data.answer[i].botType == 'flow_definedReply'){
            this.handleFlowDefinedReply(data.answer[i]);
          }else if(data.answer[i].botType == 'flow_selectedReplyOp'){
            let selectedQuestionList = [];
            this.handleSelectedReplyOp(selectedQuestionList,data,i)
          }else if(data.answer[i].botType == 'flow_selectedReply'){
            continue;
          }
        }
      },
      handleFlowDefinedReply(item){
        this.qaList.push({
          msg: item.msg,
          botType: item.botType,
          msgType: item.msgType,
        })
      },
      handleSelectedReplyOp(selectedQuestionList,data,i){
        let temp = 0;
        for(var j = i+1;j < data.answer.length;j++){
          if(data.answer[j].botType == 'flow_selectedReply'){
            selectedQuestionList.push(data.answer[j])
          }else {
            for(var index = 0;index < selectedQuestionList.length;index++){
              this.$set(selectedQuestionList[index], "index", index + 1);
            }
            this.qaList.push({
              msg: data.answer[i].msg,
              botType: data.answer[i].botType,
              msgType: data.answer[i].msgType,
              selectedQuestionList : selectedQuestionList,
              selectedIndex: 0,
            });
            break;
          }
          temp = j;
        }
        if(temp == data.answer.length -1 ){
          for(var index1 = 0;index1< selectedQuestionList.length;index1++){
            this.$set(selectedQuestionList[index1], "index", index1 + 1);
          }
          this.qaList.push({
            msg: data.answer[i].msg,
            botType: data.answer[i].botType,
            msgType: data.answer[i].msgType,
            selectedQuestionList : selectedQuestionList,
            selectedIndex: 0,
          });
        }
      },
      addOtherQues(){
        this.otherQues = JSON.parse(JSON.stringify(this.qaList))[0].qaList.splice(1);
        this.flag=true;
      },
      base64Toblob(tts){
        var arr = tts.split(','),
        mime = arr[0].match(/:(.*?);/)[1],
        bstr = atob(arr[1]), n = bstr.length, u8arr = new Uint8Array(n);
        while(n--){
          u8arr[n] = bstr.charCodeAt(n);
        }
        return new Blob([u8arr], {type:mime})
      },
      async getDuration(blob){
        var url = window.URL.createObjectURL(blob);
        var video = document.createElement('video');
        video.setAttribute('src',url);
        video.oncanplay = ()=>{
          console.log(video.duration)
          return video.duration;
        }
      },
      pushQaList(arg){
        // this.qaList.length && this.qaList.splice(-1);
        var t
        var answerList = arg.answer;
        var otherAnswerList =arg.topkKnowledge;
        var answerFromList = arg.answerFrom;
        var comType = 0;
        if(arg.comType!=null){
          comType = arg.comType
        }
        var fromList = []
        var time = 0;
        if(answerList!=null && answerList.length>0){
          answerList.forEach((item,index) => {
            setTimeout(() => {
              this.$refs["divScroll"].scrollIntoView("#d3", 500);
              this.randomScroll();
              var blob = null;
              if(item.msgType == "audio"){
                var tts = item.tts;
                blob = this.base64Toblob(tts);
                console.log(blob.size)
                this.qaList.push({
                  msg:item.msg,
                  ques:this.copyQuestion,
                  obj:{'res':{'duration':blob.size/33822,'blob':blob,'isPlaying':false,'play':1}},
                  msgType:item.msgType,
                  botType:item.botType,
                  comType:comType,
                  feedbackType:null,
                  index:arg.index,
                })
              }else{
                this.qaList.push({
                  msg:item.msg,
                  ques:this.copyQuestion,
                  msgType:item.msgType,
                  botType:item.botType,
                  comType:comType,
                  feedbackType:null,
                  index:arg.index,
                })
              }
            },time);
            time = time+500;
          })
          setTimeout(() => {
            this.$refs["divScroll"].scrollIntoView("#d3", 500);
            this.randomScroll();
            if(answerFromList!=null && answerFromList.length>0){
              this.qaList.push({
                botType:'kb',
                answerFrom:answerFromList,
                comType:comType,
                feedbackType:null,
                index:arg.index,
              })
            }
          },time);
          time = time+500;
        }
        setTimeout(() => {
          this.$refs["divScroll"].scrollIntoView("#d3", 500);
          this.randomScroll();
          if(otherAnswerList!=null && otherAnswerList.length>0){
            this.otherQues = otherAnswerList;
            if(otherAnswerList.length > 3){
              otherAnswerList = otherAnswerList.slice(0,3)
            }
            if(answerList!=null && answerList.length>0){
              //返回answer不为空 猜你还想问
              this.qaList.push({
                otherQues:otherAnswerList,
                botType:'other',
                changeIndex:0
              })
            }else{
              //返回answer为空,猜你想问
              this.qaList.push({
                otherQues:otherAnswerList,
                botType:'guest',
                changeIndex:0
              })
            }
          }
        },time);
        this.flag=true;
        this.$refs["divScroll"].scrollIntoView("#d3", 500);
        this.randomScroll();
      },
      quesLike(index,otherIndex){
        var param = this.qaList[index].likeQues;
        param = param[otherIndex];
        var paramOther =[];
        if(this.quesLike.length > 1){
          for(var i=1;i<this.likeQues.length;i++){
            paramOther.push(this.likeQues[i])
          }
        }
        if(param.answerFrom!=null){
          var answerFromList = param.answerFrom.split('/');
          var num = answerFromList.length
          var answerFromUrl = answerFromList[num-1];
        }
        this.qaList.push({
          question: param.question,
          answer: param.answer,
          answerFrom: param.answerFrom,
          answerType: param.answerType,
          answerFromUrl:answerFromUrl,
          type:0,
          botType:"qa",
          otherQues: [],
          likeQues: []
        })
        this.flag=true;
        this.$refs["divScroll"].scrollIntoView("#d3", 500);
        this.randomScroll();
      },
      randomScroll() {
        const vs = this.$refs['divScroll'];
        const panel = vs.scrollPanelElm;
        const x =  panel.scrollWidth;
        const y =  panel.scrollHeight;
        vs.scrollTo({
          x,
          y
        });
      },
      quesOther(ques){
        this.question = ques;
        this.sendQuestion(false);
      },
      quesSelectedOther(ques,index,otherIndex,selectedIndex){
        this.$nextTick(()=>{
          for(var i=0;i<this.qaList[index].selectedQuestionList.slice(selectedIndex,selectedIndex+3).length;i++){
            if(this.$refs['selectedDiv' + index+'.'+i][0].style.color == 'rgb(81, 90, 110)'){
              return;
            }
          }
          this.question = ques.msg;
          for(var i1=0;i1<this.qaList[index].selectedQuestionList.slice(selectedIndex,selectedIndex+3).length;i1++){
            if(otherIndex != i1){
              this.$refs['selectedDiv' + index+'.'+i1][0].style.color = '#515a6e';
            }
            this.$refs['selectedDiv' + index+'.'+i1][0].style.cursor = 'text;';
            this.$refs['selectedChangeP' + index][0].style.color = '#515a6e';
          }
          this.sendQuestion(false);
        })
        this.$refs["divScroll"].scrollIntoView("#d3", 500);
        this.randomScroll();
        this.$forceUpdate();
      },
      changeColor(operate,index){
        this.$nextTick(()=>{
          if("leave"==operate){
            this.$refs.possibleQuestion[index].style.background = '#ffffff';
          }else {
            this.$refs.possibleQuestion[index].style.background = 'rgb(231, 243, 255)';
          }
        })
      },
      download(url) {
        location.href = url;
      },
      toBottom(){
      },
      init(){
        this.sessid = new Date().getTime();
        this.getFavorList();
        this.getRobotById();
      },
      recOpenBefore:function(){
        this.isText = false;
        this.isPhoto = false;
        this.isRecorder = true;
        this.menuIsShow = false;
      },
      recOpen:function(){
        var This=this;
        this.isText = false;
        this.isRecorder = true;

        var rec=this.rec=Recorder({
          type:This.type
          ,bitRate:This.bitRate
          ,sampleRate:This.sampleRate
          ,onProcess:function(buffers,powerLevel,duration,sampleRate){
            This.duration=duration;
            This.powerLevel=powerLevel;

            This.wave.input(buffers[buffers.length-1],powerLevel,sampleRate);
          }
        });

        This.dialogInt=setTimeout(function(){
          This.showDialog();
        },4000);
        this.isShowDialog = true;
        rec.open(function(){
          This.dialogCancel();
          This.wave=Recorder.WaveView({elem:".ctrlProcessWave"});

        },function(msg,isUserNotAllow){
          This.dialogCancel();
        });

        This.waitDialogClickFn=function(){
          This.dialogCancel();
        };

        This.recStartInt=setTimeout(function(){
          This.recStart();
        },400);
      }
      ,recClose:function(){
        this.isRecorder = false;
        this.isShowDialog = false;
        this.menuIsShow = false;
        this.isPhoto = false;
        this.isText = true;
        var rec=this.rec;
        this.rec=null;
        if(rec){
          rec.close();
        }else{
        };
      },photoSwitch:function(){
        this.isPhoto = true;
        this.isText = false;
        this.isRecorder = false;
        this.menuIsShow = false;
      }
      ,recStart:function(){
        this.isShowDialog = true;
        if(!this.rec||!Recorder.IsOpen()){
          this.recOpen()
          return;
        }
        this.rec.start();

        var set=this.rec.set;
      }
      ,recPause:function(){
        if(this.rec&&Recorder.IsOpen()){
          this.rec.pause();
        }else{
        };
      }
      ,recResume:function(){
        if(this.rec&&Recorder.IsOpen()){
          this.rec.resume();
        }else{
        };
      }
      ,recStop:function(){
        if(!(this.rec&&Recorder.IsOpen())){
          return;
        }

        var This=this;
        var rec=This.rec;
        rec.stop(function(blob,duration){
          This.reclog("","",{
            blob:blob
            ,duration:duration
            ,rec:rec
          });

        },function(s){
        });
        this.isShowDialog = false;

        This.recCloseInt=setTimeout(function(){
          // 关闭资源
          this.rec=null;
          if(rec){
            rec.close();
          }else{
          };
        },200);

        setTimeout(function(){
          This.recUploadLast();
        },200);
      },recUploadLast:function(){
        if(!this.recLogLast){
          return;
        };
        var This=this;
        var blob=this.recLogLast.res.blob;

        //本例子假设使用原始XMLHttpRequest请求方式，实际使用中自行调整为自己的请求方式
        //录音结束时拿到了blob文件对象，可以用FileReader读取出内容，或者用FormData上传

        var onreadystatechange=function(title){
          
        };
        /***方式一：将blob文件转成base64纯文本编码，调用sendmessage接口***/
        const file = new File([blob], 'recoder.wav', {type: 'audio/wav'});
        this.blobToBase64(file)

        /***方式二：使用FormData用multipart/form-data表单上传文件***/
        //  var form=new FormData();
        // form.append("file",blob,"recorder.wav"); //和普通form表单并无二致，后端接收到upfile参数的文件，文件名为recorder.mp3
        // form.append("fileCatalogue","/123");
        // form.append("fileType", "-1");
        //
        //  var uploadUrl = Util.baseUrl + "/api/file/uploadFile";
        //  var xhr=new XMLHttpRequest();
        //  xhr.open("POST", uploadUrl);
        //  xhr.onreadystatechange=onreadystatechange("上传方式二【FormData】");
        //  xhr.send(form);
      },cancelRecorder:function(){
        this.isShowDialog = false;
        var rec=this.rec;
        this.rec=null;
        if(rec){
          rec.close();
        }else{
        };
      },
      blobToBase64(file) {
        // return new Promise((resolve, reject) => {
        //   const fileReader = new FileReader();
        //   fileReader.onload = (e) => {
        //     this.copyQuestion =e.target.result;
        //     this.msgType = "audio";
        //     this.handleWord(true);
        //     resolve(e.target.result);
        //   };
        //   // readAsDataURL
        //   fileReader.readAsDataURL(blob);
        //   fileReader.onerror = () => {
        //     reject(new Error('文件流异常'));
        //   };
        // });
        const a = new FileReader();
        a.readAsDataURL(file);
        a.onload = (e) => {
          const base64 = e.target.result;
          this.copyQuestion =base64;
          this.msgType = "audio";
          this.handleWord(true);
        }
      },
      reclog:function(msg,color,res){
        var obj={
          idx:this.logs.length
          ,msg:msg
          ,color:color
          ,res:res
          ,isPlaying:false
          ,playMsg:""
          ,down:0
          ,down64Val:""
        };
        if(res&&res.blob){
          this.recLogLast=obj;
        };
        this.logs.splice(0,0,obj);
        this.qaList.push({"obj":obj,"type":"audio"});
      }
      ,recplay:function(obj){
        var This=this;

        var audio=this.$refs.LogAudioPlayer;

        audio.controls=true;
        if(!(audio.ended || audio.paused)){
          audio.pause();
        };
        audio.onerror=function(e){
          logmsg('<span style="color:red">播放失败['+audio.error.code+']'+audio.error.message+'</span>');
        };
        audio.src=(window.URL||webkitURL).createObjectURL(obj.res.blob);
        obj.isPlaying = true;
        setTimeout(function(){
          This.closeVoicePlaying(obj);
        },obj.res.duration);
        audio.play();
      } ,closeVoicePlaying:function(obj){
        obj.isPlaying = false;
      }
      ,getTime:function(){
        var now=new Date();
        var t=("0"+now.getHours()).substr(-2)
          +":"+("0"+now.getMinutes()).substr(-2)
          +":"+("0"+now.getSeconds()).substr(-2);
        return t;
      },showDialog:function(){
        if(!/mobile/i.test(navigator.userAgent)){
          return;//只在移动端开启没有权限请求的检测
        };
        this.recOpenDialogShow=1;
      }
      ,dialogCancel:function(){
        clearTimeout(this.dialogInt);
        this.recOpenDialogShow=0;
      },
      choiceImg(){
        // console.info("fileObj",this.fileObj)
        // console.info("this.$refs.filElem.files[0]",this.$refs.filElem.files)
        this.$refs.filElem.dispatchEvent(new MouseEvent('click'))
        this.fileObj = null;
        this.menuIsShow = false;
      },
      getFile(event){
        var that = this;
        const inputFile = this.$refs.filElem.files[0];
        if(inputFile){
          if(inputFile.type !== 'image/jpeg' && inputFile.type !== 'image/png' && inputFile.type !== 'image/jpg'){
            this.$Message.error('不是有效的图片文件！');
            return;
          }
          if(inputFile.size >  1 * 2048 * 1024){
            this.$Message.error('文件过大！');
            return;
          }
          this.imgInfo = Object.assign({}, this.imgInfo, {
            name: inputFile.name,
            size: inputFile.size,
            lastModifiedDate: inputFile.lastModifiedDate.toLocaleString()
          })
          const reader = new FileReader();
          reader.readAsDataURL(inputFile);
          reader.onload = function (e) {
            that.imgSrc = this.result;
            that.base64FileParam = this.result;
            that.uploadBase64File(inputFile.name,this.result)
            // that.copyQuestion = fileUrl;
            // that.msgType = "image";
            that.base64File = this.result;
            that.qaList.push({"question":that.base64File,"inputFile":inputFile,"msgType":"image"});
            // that.handleWord(true);
            event.target.value=[]
          }
        } else {
          return;
        }
      },
      uploadBase64File(fileName,base64File){
        let self = this;
        // 上传图片文件
        self.$http.post('/api/file/uploadBase64File', {
          'fileName':fileName,
          'base64File': base64File,
        }).then(function (response) {
          if(response.data){
            // this.fileUrl = response.data['fileUrl']
            self.copyQuestion = response.data['fileUrl'];
            self.msgType = "image";
            self.handleWord(true);

            return response.data['fileUrl'];
          }
        }).catch(function (error) {
          console.log(error);
        });
      },
      handleClose(){
        this.menuIsShow = false;
      },
      queryQuestions(){
        let self = this;
        if(!self.question || self.question == ''){
          self.possibleQuestions = [];
          return;
        }
        // 获取补全的推荐问题
        self.$http.post('/api/qa/fuzzyQueryQuestion', {
          'robotId':localStorage.robotId,
          'content': self.question,
          "robotType":"1",
          "size":5
        }).then(function (response) {
          if(response.data){
            self.possibleQuestions = response.data
          }
        }).catch(function (error) {
          console.log(error);
        });
      },
    },
    mounted() {
      let self = this;
      self.init();
      self.$nextTick(()=>{
        window.addEventListener('resize', () => {
            this.imageWidth = document.body.offsetWidth*0.69+"px";
          }
        );
      });
      document.onkeydown = function(e) {
        var key = window.event.keyCode;
        if (key == 13) {
          // lett.question = this.question.trim();
          self.question = self.question.trim();
          if(JSON.stringify(self.question).length>2){
            self.sendQuestion(true);
          }
        }
      }
    }
  }

</script>

