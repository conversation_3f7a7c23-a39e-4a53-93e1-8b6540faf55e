<template>
  <div>
    <div id="svgmapWg" ref="myEchartWg" class="mapContainer"></div>
  </div>
</template>

<script>
import * as echarts from 'shzl-datav/node_modules/echarts'
import { svg } from './hswg.js'
export default {
  name:'glWgMap',
  data() {
    return {
      myChart: null,
      option: null,
      selectName: '',
      jdData: [
        { name: '荆刘社区', peoNum: '2107', areaNum: '5.4' },
        { name: '柏树社区', peoNum: '3269', areaNum: '6.8' },
        { name: '双塘社区', peoNum: '3583', areaNum: '11.89' },
        { name: '亲见社区', peoNum: '2521', areaNum: '9.6' },
        { name: '石坝社区', peoNum: '3115', areaNum: '6.9' },
        { name: '周村社区', peoNum: '3269', areaNum: '7.6' },
        { name: '向阳社区', peoNum: '5955', areaNum: '6.5' },
        { name: '谷里社区', peoNum: '6672', areaNum: '11.5' },
        { name: '箭塘社区', peoNum: '3798', areaNum: '9.7' },
        { name: '公塘社区', peoNum: '3473', areaNum: '12.34' },
        { name: '张溪社区', peoNum: '3479', areaNum: '9.6' }
      ],
      filterObj: [],
      peoNum: '',
      areaNum: ''
    }
  },
  mounted() {
    let that = this
    this.myChart = echarts.init(document.getElementById('svgmapWg'))
    this.init()
    console.log('this.myChart', this.myChart)
    this.myChart.on('click', function(event) {
      // console.log(111);
      // console.log(event)
      that.$emit('wgInfoItem',event.name)
      if (that.selectName == event.name) {
        that.selectName = ''
        that.myChart.clear()
        that.init()
        return
      }

      that.filterObj = that.jdData.filter(item => item.name == event.name)
      that.peoNum = that.filterObj.length > 0 ? that.filterObj[0].peoNum : ''
      that.areaNum = that.filterObj.length > 0 ? that.filterObj[0].areaNum : ''

      that.selectName = event.name
      that.myChart.clear()
      that.init()

      // that.myChart.setOption(that.option);
      // that.myChart.dispatchAction({
      //   type: 'highlight',
      //   geoIndex: 0,
      //   name: event.name
      // });
    })
  },
  methods: {
    init() {
      var that = this
      echarts.registerMap('organ_diagram', { svg: svg })

      this.option = {
        // backgroundColor: '#040b1c',
        geo: {
          left: 10,
          right: 10,
          map: 'organ_diagram',
          itemStyle: {
            borderWidth: 0
          },
          emphasis: {
            focus: 'none',
            itemStyle: {
              areaColor: '#54E9EF'
            },
            label: {
              show: false,
              position: [10, 10],
              backgroundColor: {
                image: require('@/assets/img/map/tooltip_bg.png')
              },
              width: 273,
              height: 114,
              formatter: `{a|{a}}\n {b|区域面积：}{c|${that.areaNum}}{d|平方公里}\n{b| 人口总数：}{c|${that.peoNum}}{d|人}`,
              rich: {
                a: {
                  color: '#fff',
                  fontSize: 20,
                  align: 'left',
                  padding: [6, 0, 0, 20]
                },
                b: {
                  color: '#fff',
                  fontSize: 18,
                  fontWeight: 'bold',
                  padding: [14, 0, 0, 26]
                },
                c: {
                  color: '#9fff6b',
                  fontSize: 22,
                  padding: [14, 0, 0, 6]
                },
                d: {
                  color: '#fff',
                  padding: [14, 0, 0, 6],
                  fontSize: 18
                }
              }
            }
          },
          regions: [
            {
              name: that.selectName,
              itemStyle: {
                areaColor: '#54E9EF'
              },
              label: {
                show: false,
                position: [10, 10],
                backgroundColor: {
                  image: require('@/assets/img/map/tooltip_bg.png')
                },
                width: 273,
                height: 114,
                // padding: [0, 0, 0, 60],
                formatter: `{a|{a}}\n {b|区域面积：}{c|${that.areaNum}}{d|平方公里}\n{b| 人口总数：}{c|${that.peoNum}}{d|人}`,
                // backgroundColor: 'RGBA(12, 33, 75, 0.9)',
                // borderColor: '#8C8D8E',
                // borderWidth: 1,
                // borderRadius: 4,
                rich: {
                  a: {
                    color: '#fff',
                    fontSize: 20,
                    align: 'left',
                    padding: [6, 0, 0, 20]
                  },
                  b: {
                    color: '#fff',
                    fontSize: 18,
                    fontWeight: 'bold',
                    padding: [14, 0, 0, 26]
                  },
                  c: {
                    color: '#9fff6b',
                    fontSize: 22,
                    padding: [14, 0, 0, 6]
                  },
                  d: {
                    color: '#fff',
                    padding: [14, 0, 0, 6],
                    fontSize: 18
                  }
                }
              }
            }
          ]
        }
      }
      this.myChart.setOption(this.option)
    }
  }
}
</script>
<style scoped lang="scss">
.mapContainer {
  width: 100%;
  height: 730px;
}
</style>
