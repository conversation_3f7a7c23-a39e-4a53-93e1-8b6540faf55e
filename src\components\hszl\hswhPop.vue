<template>
  <div class="stlyJq">
    <div class="tkTitle">
      <span>文物详情</span>
    </div>
    <img @click="closeEmitai" src="@/assets/zhny/map/tkGb.png" class="tkGb" />
    <div class="tkContent">
      <div class="wrapper wrapper4">
        <swiper class="swiper content" :options="swiperOption">
          <swiper-slide v-for="(it, i) in hswhData.tbAppendices" class="width: 100%" :key="i">
            <img :src="it.url" alt />
            <!-- <div class="cont">{{ it.originalFileName.slice(0, it.originalFileName.indexOf('.')) }}</div> -->
          </swiper-slide>
          <div class="swiper-pagination" slot="pagination"></div>
        </swiper>
      </div>
      <div class="info">
        <div class="title">{{ hswhData.name }}</div>
        <ul>
          <div>
            <li>
              <span>年代:</span><span>{{ hswhData.years }}</span>
            </li>
            <li>
              <span>类别：</span><span>{{ hswhData.type }}</span>
            </li>
          </div>
          <div>
            <li>
              <span>保护级别:</span><span>{{ hswhData.protection }}</span>
            </li>
            <li>
              <span>地址：</span><span>{{ hswhData.address }}</span>
            </li>
          </div>
        </ul>
      </div>
      <div class="introduce">
        <p>
          {{ hswhData.synopsis }}
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import BlockBox from '@/components/leader/common/blockBox.vue'
export default {
  components: { BlockBox },
  name: 'hswhPop',
  props: {
    hswhData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      data6: [],
      swiperOption: {
        observer: true,
        observeParents: true,
        autoHeight: true,
        spaceBetween: 0,
        autoplay: {
          delay: 3500,
          disableOnInteraction: false,
        },
        slidesPerView: 'auto',
        grabCursor: true,
        autoplayDisableOnInteraction: false,
        mousewheelControl: true,
        pagination: {
          el: '.swiper-pagination',
        },
      },
      timer: null,
    }
  },
  mounted() {
    // 获取滚动容器和文本元素
    const scrollingText = document.querySelector('.introduce')
    const text = document.querySelector('.introduce p')
    // 获取文本元素的高度
    const scrollHeight = scrollingText.clientHeight
    const textHeight = text.clientHeight
    // console.log(textHeight - scrollHeight)
    // 定义滚动函数
    function scroll() {
      // console.log('scrollingText.scrollTop', scrollingText.scrollTop)
      // 如果文本元素已经完全滚动出去，则将其重置到滚动容器的顶部
      if (scrollingText.scrollTop >= textHeight - scrollHeight - 1) {
        scrollingText.scrollTop = 0
      }
      // 否则，将滚动容器向上滚动一个像素的距离
      else {
        scrollingText.scrollTop += 1
      }
    }

    // 每隔20毫秒调用一次滚动函数
    this.timer = window.setInterval(scroll, 100)
  },
  beforeDestroy() {
    window.clearInterval(this.timer)
  },
  methods: {
    closeEmitai() {
      this.$emit('close')
    },
  },
}
</script>

<style lang="less" scoped>
.stlyJq {
  width: 700px;
  height: 853px;
  // background: url(~@/assets/zhny/map/tkBg.png) no-repeat center / 100% 100%;
  background: url(~@/assets/hszl/tkBg.png) no-repeat center / 100% 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10001;
  padding: 0 43px;
  .tkTitle {
    // width: 1054px;
    height: 74px;
    background: url(~@/assets/zhny/map/tkTitle.png) no-repeat center / 100% 100%;
    margin: 0 auto;
    margin-top: 22px;
    line-height: 74px;
    span {
      font-size: 36px;
      font-family: PangMenZhengDao;
      color: #ffffff;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .tkGb {
    position: absolute;
    top: 38px;
    right: 43px;
    cursor: pointer;
  }
  .tkContent {
    width: 100%;
    height: calc(100% - 74px - 44px);
    .wrapper4 {
      width: 580px;
      height: 229px;
      margin: 41px auto 0;
      .swiper-slide {
        height: 100%;
      }
      .swiper-pagination {
        text-align: right;
        bottom: 3px;
      }
      /deep/.swiper-pagination-bullet-active {
        background-color: rgba(255, 255, 255, 0.8);
      }
      .content {
        width: 100%;
        height: 100%;
        position: relative;
        img {
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
        }
        .cont {
          width: 100%;
          height: 30px;
          padding: 5px 10px;
          position: absolute;
          bottom: 0;
          font-size: 14px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
          text-align: left;
          background-color: rgba(0, 0, 0, 0.35);
        }
      }
    }
    .introduce {
      margin: 20px auto 0;
      width: 100%;
      height: 300px;
      background-size: 100% 100%;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #4fddff;
      line-height: 22px;
      letter-spacing: 2px;
      text-align: left;
      /* 样式用于控制滚动容器的高度、宽度、边框和滚动效果等 */
      overflow: hidden;
      position: relative;

      & > p {
        position: absolute;
        top: 0;
        left: 0;
        margin: 0;
        padding: 0px 16px 18px;
        font-size: 18px;
      }
    }
    .info {
      margin-top: 16px;
      padding: 0 10px;
      .title {
        font-size: 28px;
        font-family: YouSheBiaoTiHei;
        color: #4ceeff;
        line-height: 33px;
        text-shadow: 0px 0px 3px rgba(255, 255, 255, 0.5);
        background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        text-align: left;
      }
      ul {
        display: flex;
        & div:last-of-type {
          margin-left: 20px;
        }
        li {
          display: flex;
          margin-top: 10px;

          & span:first-of-type {
            text-align: left;
            flex-shrink: 0;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 20px;
          }
          & span:last-of-type {
            margin-left: 18px;
            text-align: left;
            font-size: 14px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #ffffff;
            line-height: 20px;
          }
        }
      }
    }
  }
}
</style>
