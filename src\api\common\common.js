import http from '@/utils/leader/request';
import { location,baseUrl,bjnjUrl } from '@/utils/leader/const';

// 天气接口
export const tianqiapi = params => {
  return http.get(
    // `https://www.tianqiapi.com/free/day?appid=84366981&appsecret=IDX8UrRO&unescape=1&city=${location}`,
    `https://restapi.amap.com/v3/weather/weatherInfo?key=580ed7420de1568307c3bf398e9eaee9&city=${location}&extensions=base`,
    params
  );
};



//图形接口
//   //土壤水分监测转tif
//   "4094": "0-10cm土壤相对湿度",
//   "4095": "10-20cm土壤相对湿度",
//   "4096": "20-30cm土壤相对湿度",
//   "4097": "30-40cm土壤相对湿度",
//   "4098": "40-50cm土壤相对湿度",
//   //农业气象灾害预报转tif
//   "4091": "冬小麦晚霜冻害预报",
//   "4092": "农业于旱预报",
//   "4093": "暴雨预报",
//   //未来2小时降雨预报雷达反射率图
//   "1402": "未来24小时的降雨预报 ",
//   //未来24小时的降雨预报
//   "1318": "雷达预报蔚来两小时 ",
// }
export const picTif = params => {
  return http.get(
    `http://api.weatherdt.com/pic/?area=101&type=${params.type}&date=${params.date}&key=e503e4a74a7d3a3063827ef324303bc6`
  );
};

//查询土壤水分监测图层信息 4094,4095,4096,4097,4098
export const walarmPic = params => {
  return http.get(
    `${baseUrl}/daas/api/walarm/v1/soil/moisture/pic`,
    params
  );
};

//查询农业气象灾害预报信息 4091,4092,4093
export const meteorologicalPic = params => {
  return http.get(
    `${baseUrl}/daas/api/walarm/v1/meteorological/disaster/prediction/pic`,
    params
  );
};
//查询未来降雨预报图层信息 1318,1402
export const rainfallPic = params => {
  return http.get(
    `${baseUrl}/daas/api/agmach/v1/rainfall/forecast/pic`,
    params
  );
};

//查询全国风场实况图层信息
export const windPic = params => {
  return http.get(
    `${baseUrl}/daas/api/walarm/v1/wind/farm/pic`,
    params
  );
};


//风场解压
export const extractFile = data => {
  return http.post(
    `${bjnjUrl}/api/common/extractTarJson`,
    data
  );
};