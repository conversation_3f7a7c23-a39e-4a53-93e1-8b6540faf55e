<template>
	<div class="popDropDown">
		<!-- <el-dropdown v-if="textArr2.length > 0" trigger="click" @command="clickchange">
			<span class="el-dropdown-link"> {{ textArr2[currentIndex][1] }}<i class="el-icon-caret-bottom el-icon--right"></i> </span>
			<el-dropdown-menu slot="dropdown">
				<el-dropdown-item v-for="(item, index) in textArr2" :key="index" :command="index">
					{{ item[1] }}
				</el-dropdown-item>
			</el-dropdown-menu>
		</el-dropdown> -->

		<el-select v-if="textArr2.length > 0" v-model="seleds" collapse-tags placeholder="请选择" @change="handleChangeArgmach">
			<el-option v-for="item in textArr2" :key="item.value" :label="item.label" :value="item.value"> </el-option>
		</el-select>
	</div>
</template>

<script>
export default {
	name: 'i-argmach-select',
	props: {
		textArr2: {
			type: Array,
			default: () => [],
		},
		selected: {
			type: String,
			default: () => '',
		},
		argmachType: {
			type: String,
			default: '',
		},
	},
	data() {
		return {
			seleds: '',
		}
	},
	watch: {
		textArr2(val) {},

		selected: {
			handler: function(val) {
				this.seleds = val
			},
			immediate: true,
		},
	},
	methods: {
		handleChangeArgmach(ev) {
			this.$emit('change', ev)
		},
	},
}
</script>

<style lang="less" scoped>
.popDropDown {
	min-width: 176px;
	min-height: 22px;

	display: flex;
	justify-content: center;
	align-items: center;

	/deep/ .el-select {
		.el-input .el-select__caret  {
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}

	/deep/.el-dropdown {
		height: 22px;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 12px;
		color: #24818c;
		line-height: 24px;
		text-align: left;
		font-style: normal;
		position: absolute;

		width: 100%;
	}

	.el-dropdown-menu {
		border: 1px solid #d2f2ea !important;
	}
	.el-dropdown-menu__item {
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #cbeaed;
		text-align: center;
		line-height: 40px;
		padding: 0 30px;
		&:hover {
			background: #1e8e82;
			color: #cbeaed;
		}
	}

	.el-dropdown-link {
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-left: 22px;
		padding-right: 10px;
	}
}
</style>
