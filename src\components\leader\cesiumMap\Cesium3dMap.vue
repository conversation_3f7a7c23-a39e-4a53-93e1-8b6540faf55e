<template>
  <div class="map-warrper">
    <div ref="map" class="container" />
  </div>
</template>

<script>
import testPoint from '@/assets/json/testPoint'
import Spriteline1MaterialProperty from '@/utils/cesium/Spriteline1MaterialProperty'
import ellipsoidElectricMaterialProperty from '@/utils/cesium/ellipsoidElectricMaterialProperty'
import CircleWave from '@/utils/cesium/CircleWaveMaterialProperty'
import dynamicWallMaterialProperty from '@/utils/cesium/dynamicWallMaterialProperty'
import WallDiffuseMaterialProperty from '@/utils/cesium/WallDiffuseMaterialProperty'
import AroundView from '@/utils/cesium/AroundView'
import Track from '@/utils/cesium/track'
import CircleDiffusion from '@/utils/cesium/CircleDiffusion'
import Scanline from '@/utils/cesium/Scanline'
import lineFlowMaterialProperty from '@/utils/cesium/lineFlowMaterialProperty'
import Bubble from './bubble/index.js'
import DragEntity from './entity/dragentity.js'
import zhzxMapPs from '@/assets/json/zhzxMapPs'
import tracklineRoming from '@/assets/map/tracklineRoming'
import DrawTransformCurve from '@/utils/cesium/DrawTransformCurve'

export default {
  name: 'Cesium',
  props: {},
  data() {
    return {
      // 'http://sanzhigou.cn:8088/3dtile/tileset.json'  './3dtile/gulou/tileset.json'
      TILESET_URL: './3dtile/gulou/tileset.json',
      viewer: null,
      scene: null,
      layerList: [],
      geoqUrl:
        'https://map.geoq.cn/arcgis/rest/services/ChinaOnlineStreetPurplishBlue/MapServer/tile/{z}/{y}/{x}',
      poinEntity: {},
      bubbles: null,
      trackLayer: null,
      timer: null,
      clickPont: null
    }
  },
  mounted() {
    this.initMap()
  },
  beforeDestroy() {
    this.timer && clearInterval(this.timer)
    this.viewer && this.viewer.destroy()
    this.viewer = null
  },
  methods: {
    /**
     * 初始化地图
     */
    initMap() {
      Cesium.Ion.defaultAccessToken =
        // 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************.jvX6FPlrswlurh8Lan24qhVROJ4a_GQ5_T6geUAbCKQ'
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.GnXyBbMj8o8TZ2LeCxFB_SrdUiQodGmjT1AbNCvmezA';
      this.viewer = new Cesium.Viewer(this.$refs.map, {
        // imageryProvider: tdtImage, // 地图底图服务
        geocoder: false,
        homeButton: false,
        sceneModePicker: false,
        baseLayerPicker: false,
        navigationHelpButton: false,
        animation: false,
        timeline: false,
        fullscreenButton: false,
        vrButton: false,
        shadows: false,
        infoBox: false,
        CreditsDisplay: false
      })
      let geoqLayer = new Cesium.UrlTemplateImageryProvider({
        url: this.geoqUrl
      })
      this.scene = this.viewer.scene
      this.viewer.imageryLayers.addImageryProvider(geoqLayer)
      this.viewer._cesiumWidget._creditContainer.style.display = 'none'
      /* 抗锯齿 */
      this.viewer.scene.postProcessStages.fxaa.enabled = true

      // this.viewer.zoomTo(tilesets)
      this.viewer.camera.flyTo({
        // destination: Cesium.Cartesian3.fromDegrees(118.7723837, 32.0672973, 356),
        // destination: Cesium.Cartesian3.fromDegrees(118.747152,31.949696, 7000),
        destination: Cesium.Cartesian3.fromDegrees(118.75211,32.00879,3016),
        orientation: {
          heading: Cesium.Math.toRadians(6.0),
          pitch: Cesium.Math.toRadians(-28.0)
        }
      })
      // const close = new URL("../../assets/img/close.png", import.meta.url).href;
      // this.loadPoints(testPoint.point1, close, 'point1')

      this.loadAllData()
      // this.loadWater();
    },
    loadAllData() {
      this.load3dtiles()
      this.loadWater()
      this.loadRoad1()
      

      // this.loadEllipsoidElectric();
      // this.loadCircleWave();
      // this.loadDynamicWall();
      // this.loadScanline();
      // this.loadLineFlow([118.76297950744629, 32.077701993640964], 50);

      this.leftDownAction()
      this.rightDownAction()
      this.addPoiPoints()
      
      // setTimeout(() => {
      //   this.loadTrack()
      // }, 5000);  
      // setTimeout(() => {
      //   this.removeTrack()
      // }, 20000);    

      // this.loadCircleDiffusion();
      // this.dragEntity();
      // this.loadGeojsonBillboardLayer('people', "./json/poi/people.geojson", "./imgs/poi/people.png")
    },
    addPoiPoints() {
      this.addPoi(
        '江苏省人民医院',
        [118.76856830391444, 32.05155420304949, 100],
        './imgs/poi/医院.png'
      )
      this.addPoi(
        '南京中药科大学',
        [118.76580812946537, 32.08306247427144, 100],
        './imgs/poi/大学.png'
      )
      this.addPoi(
        '江苏省人民政府',
        [118.75979153006, 32.071862758196175, 100],
        './imgs/poi/政府.png'
      )
    },
    /**
     * data  点数组
     * img 图片地址 require('@/assets/imgs/map/point-jk.png')
     * layerid  图层id
     */
    // loadPoints(data, img, layerid) {
    //   this.removeLayer(layerid);
    //   const layer = new Cesium.CustomDataSource(layerid);
    //   data.forEach((element) => {
    //     layer.entities.add(
    //       this.addBillboard(element.position, element.properties, img)
    //     );
    //   });
    //   this.viewer.dataSources.add(layer);
    //   this.layerList.push({
    //     layerId: layerid,
    //     source: layer,
    //   });
    // },
    addBillboard(position, properties, img) {
      return {
        // id: 1,
        // name: 'billboard',
        position: Cesium.Cartesian3.fromDegrees(position[0], position[1], position[2]),
        billboard: {
          image: img,
          show: true, // 是否显示
          sacle: 1, // 放大倍数
          // pixelOffset: new Cesium.Cartesian2(100,200),//偏移像素
          // eyeOffset:new Cesium.Cartesian3(0.0,1000000.0,0.0),//视野偏移
          // horizontalOrigin:Cesium.HorizontalOrigin.LEFT,//相对于原点的水平位置
          // verticalOrigin: Cesium.VerticalOrigin.BOTTOM//相对于原点的垂直位置
          // heightReference:Cesium.HeightReference.CLAMP_TO_GROUND,//标识相对于地形的位置，CLAMP_TO_GROUND	位置固定在地形上。RELATIVE_TO_GROUND	位置高度是指地形上方的高度
          //  color:Cesium.Color.RED.withAlpha(0.8),//图像的颜色
          //  width:96,//图像宽度
          // heigth:96,//图像高度
          // scaleByDistance:new Cesium.NearFarScalar(1.5e2, 2.5, 8.0e6,0.0),//设置广告牌的近距离和远距离缩放属性
          // translucencyByDistance:new Cesium.NearFarScalar(1.5e2, 1.0, 8.0e6,0.0),//根据距摄像机的距离来指定广告牌的透明度
          // pixelOffsetScaleByDistance:new Cesium.NearFarScalar(1.5e2, 20, 8.0e6,0.0),//根据距照相机的距离指定广告牌的像素偏移
          // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(100000.0, 2000000.0),//根据与相机的与广告牌远近确定可见性
          disableDepthTestDistance: 0 // 获取或设置与相机的距离，在深度处禁用深度测试,Number.POSITIVE_INFINITY无穷大，不会应用深度测试，0始终应用深度测试，应用深度测试避免地形的遮挡
        },
        monitoItems: {
          data: properties
        }
        // label: {
        //   text: this.des,
        //   font: '25px',
        //   color: Cesium.Color.RED,
        //   style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        //   outlineWidth: 1,
        //   //垂直位置
        //   verticalOrigin: Cesium.VerticalOrigin.BUTTON,
        //   //中心位置
        //   pixelOffset: new Cesium.Cartesian2(0, 20),
        //   eyeOffset: new Cesium.Cartesian3(0, 0, -10)
        // }
      }
    },
    /**
     * 移除图层
     */
    removeLayer(layerId) {
      const layer = this.layerList.find(e => e.layerId == layerId)
      if (layer) {
        this.viewer.dataSources.remove(layer.source)
        this.layerList.filter(e => e.layerId !== layerId)
      }
    },
    /**
     * 加载三维白膜
     */
    load3dtiles() {
      console.log('加载白膜')
      // debugger
      // Cesium.TILE_FS_BODY = `
      //       // 可以修改的参数
      //       // 注意shader中写浮点数是，一定要带小数点，否则会报错，比如0需要写成0.0，1要写成1.0
      //       float _baseHeight = -30.0; // 物体的基础高度，需要修改成一个合适的建筑基础高度
      //       float _heightRange = 60.0; // 高亮的范围(_baseHeight ~ _baseHeight + _      heightRange) 默认是 0-60米
      //       float _glowRange = 0.0; // 光环的移动范围(高度)
      //       // 建筑基础色
      //       float vtxf_height = v_stcVertex.y - _baseHeight;
      //       float vtxf_a11 = fract(czm_frameNumber / 120.0) * 3.14159265 * 2.0;
      //       float vtxf_a12 = vtxf_height / _heightRange + sin(vtxf_a11) * 0.005;
      //       gl_FragColor *= vec4(vtxf_a12, vtxf_a12, vtxf_a12, 1.0);
      //       `;

      const textureUniformShader = new Cesium.CustomShader({
        mode: Cesium.CustomShaderMode.MODIFY_MATERIAL,
        lightingModel: Cesium.LightingModel.RGB,
        // translucencyMode:CustomShaderTranslucencyMode.TRANSLUCENT, //这是版本1.97以上才有的属性
        isTranslucent: false, //我是1.96版本，用它来表示模型的透明度

        varyings: {
          v_selectedColor: Cesium.VaryingType.VEC3
        },
        vertexShaderText: `
          void vertexMain(VertexInput vsInput, inout czm_modelVertexOutput vsOutput) {
            v_selectedColor=vec3(0,0.552941,0.996078);
          }
      `,
        fragmentShaderText: `
          void fragmentMain(FragmentInput fsInput, inout czm_modelMaterial material) {
            material.diffuse=v_selectedColor * fsInput.attributes.positionMC.y*0.02;
          }
        `
      })
      var tilesets = new Cesium.Cesium3DTileset({
        url: this.TILESET_URL,
        maximumScreenSpaceError: 100,
        enableModelExperimental: true,
        customShader: textureUniformShader
      })
      this.viewer.scene.primitives.add(tilesets)
      tilesets.readyPromise.then(argument => {
        let heading = 0.0
        let center = Cesium.Cartographic.fromCartesian(argument.boundingSphere.center)
        var wgs84togcj02 = coordtransform.wgs84togcj02(
          center.longitude * (180 / Math.PI),
          center.latitude * (180 / Math.PI)
        ) //将wgs84转成gcj02火星坐标系
        var position = Cesium.Cartesian3.fromDegrees(wgs84togcj02[0], wgs84togcj02[1], -5) //高度设成-5 让模型贴地
        var mat = Cesium.Transforms.eastNorthUpToFixedFrame(position)
        var rotationX = Cesium.Matrix4.fromRotationTranslation(
          Cesium.Matrix3.fromRotationZ(Cesium.Math.toRadians(heading))
        )
        Cesium.Matrix4.multiply(mat, rotationX, mat)
        tilesets._root.transform = mat

        // argument.style = new Cesium.Cesium3DTileStyle({
        //   color: {
        //     conditions: [
        //       ["true", "color('rgb(0,141,254)')"]
        //     ]
        //   }
        // });
      })
    },
    /**
     * geojson 批量添加广告牌
     */
    loadGeojsonBillboardLayer(id, data, image) {
      let promise = Cesium.GeoJsonDataSource.load(data)
      //数据加载后渲染
      promise.then(ds => {
        let entitys = ds.entities.values
        entitys.forEach(e => {
          e.billboard = new Cesium.BillboardGraphics({
            image: image,
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
          })
        })
      })
      this.viewer.dataSources.add(promise)
    },
    /**
     * 道路穿梭线
     */
    loadRoad1() {
      var that = this
      Cesium.GeoJsonDataSource.load('./json/mainRoad.geojson').then(dataSource => {
        that.viewer.dataSources.add(dataSource)
        const entities = dataSource.entities.values
        for (let i = 0; i < entities.length; i++) {
          let entity = entities[i]
          entity.polyline.width = 1.7
          entity.polyline.material = new Cesium.Spriteline1MaterialProperty(
            2000,
            './imgs/road1.png'
          )
        }
      })
    },
    /**
     * 动态水面
     */
    loadWater() {
      var that = this
      Cesium.GeoJsonDataSource.load('./json/water.geojson').then(ds => {
        let instances = []
        let entitys = ds.entities.values
        entitys.forEach(e => {
          let geometry = new Cesium.GeometryInstance({
            geometry: new Cesium.PolygonGeometry({
              polygonHierarchy: new Cesium.PolygonHierarchy(
                e.polygon.hierarchy.getValue().positions
              ),
              extrudedHeight: 0,
              height: 0,
              vertexFormat: Cesium.EllipsoidSurfaceAppearance.VERTEX_FORMAT
            }),
            attributes: {
              color: Cesium.ColorGeometryInstanceAttribute.fromColor(
                Cesium.Color.fromRandom({
                  alpha: 0.5
                })
              )
            }
          })
          instances.push(geometry)
        })
        let primitive = new Cesium.GroundPrimitive({
          geometryInstances: instances, //合并
          //某些外观允许每个几何图形实例分别指定某个属性
          appearance: new Cesium.EllipsoidSurfaceAppearance({
            aboveGround: true, //在椭球面上
            material: new Cesium.Material({
              fabric: {
                type: 'Water',
                uniforms: {
                  baseWaterColor: new Cesium.Color(64 / 255.0, 157 / 255.0, 253 / 255.0, 0.5),
                  normalMap: './imgs/waterNormals.png',
                  frequency: 1000.0, // 控制波数的数字。
                  animationSpeed: 0.3, // 控制水的动画速度的数字。
                  amplitude: 10.0, // 控制水波振幅的数字。
                  specularIntensity: 20 // 控制镜面反射强度的数字。
                }
              }
            })
          })
        })
        that.viewer.scene.primitives.add(primitive)
      })
    },
    /**
     * 电弧
     */
    loadEllipsoidElectric() {
      this.viewer.entities.add({
        position: Cesium.Cartesian3.fromDegrees(118.7614345550537, 32.051371100689686),
        name: '电弧球体',
        ellipsoid: {
          radii: new Cesium.Cartesian3(400.0, 400.0, 400.0),
          material: new Cesium.EllipsoidElectricMaterialProperty({
            color: new Cesium.Color.fromCssColorString('rgba(0, 255, 204, 1)'),
            speed: 10.0
          })
        }
      })
    },
    /**
     * 水波纹扩散
     */
    loadCircleWave() {
      // 水波纹扩散
      let circleWave = new CircleWave(this.viewer, 'cirCleWave1')
      circleWave.add([118.76182079315186, 32.07362920197811, 0], '#1FA8E3', 500, 3000)
    },
    /**
     * 动态立体墙
     */
    loadDynamicWall() {
      var that = this
      Cesium.GeoJsonDataSource.load('./json/gulouborder_gcj02.geojson').then(ds => {
        let entitys = ds.entities.values
        entitys.forEach(e => {
          let glPositions = e.polygon.hierarchy.getValue().positions
          that.viewer.entities.add({
            name: '鼓楼区立体墙',
            wall: {
              positions: glPositions,
              // 设置高度
              maximumHeights: new Array(glPositions.length).fill(200),
              minimunHeights: new Array(glPositions.length).fill(0),
              // material: new Cesium.DynamicWallMaterialProperty({
              //   color: Cesium.Color.fromBytes(55, 96, 56).withAlpha(0.7),
              //   duration: 3000,
              //   viewer: this.viewer
              // }),
              material: new Cesium.WallDiffuseMaterialProperty({
                color: new Cesium.Color.fromCssColorString('rgba(60,132,132, 1)')
              })
            }
          })
        })
      })
    },
    /**
     * 绕地旋转
     */
    aroundView() {
      let aroundViewer = new AroundView(this.viewer, 1)
      aroundViewer.start()
    },
    /**
     * 圆扩散
     */
    loadCircleDiffusion() {
      // 圆扩散
      const circleDiffusion = new CircleDiffusion(this.viewer)
      circleDiffusion.add([118.75980377197266, 32.074720146107076, 20], '#F7EB08', 800, 9500)
    },
    /**
     * 扫描圆
     */
    loadScanline() {
      // 线圈发光扩散
      let scanLine1 = new Scanline(this.viewer, 'scanLine1')
      scanLine1.add([118.77199172973631, 32.087010550368674, 0], '#CE1374', 1200, 15)
    },
    /**
     * 随机竖直飞线
     * _center  中心点
     * ——num 个数
     */
    loadLineFlow(_center, _num) {
      let _positions = this.generateRandomPosition(_center, _num)
      _positions.forEach(item => {
        // 经纬度
        let start_lon = item[0]
        let start_lat = item[1]

        let startPoint = new Cesium.Cartesian3.fromDegrees(start_lon, start_lat, 0)

        // 随机高度
        let height = 5000 * Math.random()
        let endPoint = new Cesium.Cartesian3.fromDegrees(start_lon, start_lat, height)
        let linePositions = []
        linePositions.push(startPoint)
        linePositions.push(endPoint)
        this.viewer.entities.add({
          polyline: {
            positions: linePositions,
            material: new Cesium.LineFlowMaterialProperty({
              color: new Cesium.Color(1.0, 1.0, 0.0, 0.8),
              speed: 15 * Math.random(),
              percent: 0.1,
              gradient: 0.01
            })
          }
        })
      })
    },
    /**
     * 生成随机点
     */
    generateRandomPosition(position, num) {
      let list = []
      for (let i = 0; i < num; i++) {
        // random产生的随机数范围是0-1，需要加上正负模拟
        let lon = position[0] + Math.random() * 0.04 * (i % 2 == 0 ? 1 : -1)
        let lat = position[1] + Math.random() * 0.04 * (i % 2 == 0 ? 1 : -1)
        list.push([lon, lat])
      }
      return list
    },
    leftDownAction() {
      var handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas)
      handler.setInputAction(movement => {
        var picked = this.viewer.scene.pick(movement.position)
        if (Cesium.defined(picked) && picked.id._monitoItems) {
          let pickData = picked.id._monitoItems.data
          console.log('点数据', pickData)
          this.$emit('bubble', pickData)
        } else {
          return
        }
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
    },
    // leftDownAction() {
    //   let handler = new Cesium.ScreenSpaceEventHandler(
    //     this.viewer.scene.canvas
    //   );
    //   handler.setInputAction( movement => {
    //     const pick = this.viewer.scene.pick(movement.position);
    //     if (Cesium.defined(pick) && pick.id.id) {
    //       // _this.leftDownFlag = true;
    //       // id = pick.id.id;
    //       console.log(pick.id.monitoItems)
    //       // _this.bubble(id);
    //     } else {
    //       // console.log(_this.bubbles)
    //       if (this.bubbles) {
    //         this.bubbles.windowClose();
    //       }
    //     }
    //   }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    // },
    bubble(id) {
      if (this.bubbles) {
        this.bubbles.windowClose()
      }
      console.log(id)
      console.log(this.poinEntity[id])
      this.bubbles = new Bubble(
        Object.assign(this.poinEntity[id], {
          viewer: this.viewer
        })
      )
    },
    // 加载点
    dragEntity() {
      const drag = new DragEntity({
        viewer: this.viewer
      })
      const _this = this
      // this.poin = [{id:234,position:[122.8,39.9],text:"L"},{id:432,position:[122,39],text:"C"}]
      testPoint.cl.forEach(item => {
        item['img'] = './imgs/poi/people.png'
        const entity = drag.addEntity(item)
        _this.poinEntity[item.id] = entity
      })
    },
    // 加载传入的点位---
    dragEntityOut(data) {
      const drag = new DragEntity({
        viewer: this.viewer
      })
      const _this = this
      // this.poin = [{id:234,position:[122.8,39.9],text:"L"},{id:432,position:[122,39],text:"C"}]
      data.cl.forEach(item => {
        // console.log(item.id);
        if (item.id == 'zhzxPs') {
          console.log(1111)
          // item["img"] = "./imgs/poi/zhzx.png"
          item['img'] = './imgs/poi/people.png'
        } else {
          item['img'] = './imgs/poi/people.png'
        }
        const entity = drag.addEntity(item)
        _this.poinEntity[item.id] = entity
      })
    },
    addPoi(poiName, poiPosition, poiImg) {
      // 添加广告牌实体
      var enetity = this.viewer.entities.add({
        name: 'poi',
        position: Cesium.Cartesian3.fromDegrees(poiPosition[0], poiPosition[1], poiPosition[2]),
        label: {
          //文字标签
          text: poiName,
          font: '500 14px Helvetica', // 15pt monospace
          scale: 1,
          style: Cesium.LabelStyle.FILL,
          fillColor: Cesium.Color.WHITE,
          pixelOffset: new Cesium.Cartesian2(15, -20), //偏移量
          eyeOffset: new Cesium.Cartesian3(0, 0, -100),
          showBackground: false
        },
        billboard: {
          image: poiImg,
          horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          scale: 1
        }
      })
    },
    /**
     * 移除所有点图层
     */
    removeAllLayer() {
      console.log('移除点位')
      this.viewer.dataSources.removeAll()
      this.layerList = []
    },
    loadPoints1(data, layerid) {
      console.log('data=======++++', data)
      this.removeLayer(layerid)
      const layer = new Cesium.CustomDataSource(layerid)
      data.forEach(element => {
        // console.log(element)
        // let img = new URL(element.img,import.meta.url).href
        layer.entities.add(this.addBillboard(element.position, element.properties, element.img))
      })
      this.viewer.dataSources.add(layer)
      this.layerList.push({
        layerId: layerid,
        source: layer
      })
    },
    /**
     * 鼠标右击事件
     */
     rightDownAction() {
      var handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas)
      handler.setInputAction(movement => {
        const cartesian = this.viewer.scene.pickPosition(movement.position)
        const { x, y, z } = cartesian
        const wordCoords = {
          x: Number(x.toFixed(1)),
          y: Number(y.toFixed(1)),
          z: Number(z.toFixed(1))
        }
        console.log('此处世界坐标：', wordCoords)
        const cartographic = Cesium.Cartographic.fromCartesian(cartesian)
        const lng = Cesium.Math.toDegrees(cartographic.longitude) // 经度
        const lat = Cesium.Math.toDegrees(cartographic.latitude) // 纬度
        const alt = cartographic.height // 高度
        const coordinate = {
          longitude: Number(lng.toFixed(6)),
          latitude: Number(lat.toFixed(6)),
          altitude: Number(alt.toFixed(2))
        }
        console.log('此处经纬度坐标：', coordinate)

        const heading = Cesium.Math.toDegrees(this.viewer.camera.heading)
        const pitch = Cesium.Math.toDegrees(this.viewer.camera.pitch)
        const roll = Cesium.Math.toDegrees(this.viewer.camera.roll)
        const cartographic2 = this.viewer.camera.positionCartographic
        const { height, longitude, latitude } = cartographic2

        const camera = {
          height,
          longitude: Cesium.Math.toDegrees(longitude),
          latitude: Cesium.Math.toDegrees(latitude),
          heading,
          pitch,
          roll }

        console.log('当前视角：', camera)
      }, Cesium.ScreenSpaceEventType.RIGHT_CLICK)
    },
    /**
     * 加载轨迹
     */
    loadTrack(){
      // 加载轨迹线
      var that = this
      // Cesium.GeoJsonDataSource.load('./json/trackline.geojson').then(dataSource => {
      //   that.trackLayer = dataSource
      //   that.viewer.dataSources.add(that.trackLayer)
      //   const entities = dataSource.entities.values
      //   for (let i = 0; i < entities.length; i++) {
      //     let entity = entities[i]
      //     entity.polyline.width = 10;
      //     (entity.polyline.material = new Cesium.PolylineGlowMaterialProperty({
      //         color: new Cesium.Color(0, 0, 119, 1)
      //     }), 10)
      //   }
      // })

      let linePoints = tracklineRoming.features[0].geometry.coordinates
      let startPosition = linePoints[0]
      let endPosition = linePoints[linePoints.length - 1]
      // 起点和终点
      let points = [
         {
            position: startPosition.concat(100),
            properties: "",
            img: './imgs/trackStart.png'
          },
          {
            position: endPosition.concat(100),
            properties: "",
            img: './imgs/trackEnd.png'
          }]
      that.loadPoints1(points, 'trackPoints')
      // 起点和终点弹窗
      that.showTrackPop(startPosition, 'startDiv', true)
      that.showTrackPop(endPosition, 'endDiv', false)
      // 跳转到起点
      // that.flyToPoint(startPosition[0], startPosition[1], 1000)
      that.dynamicTrack()
    },
    /**
     * 动态轨迹
     */
    dynamicTrack(){
      let linePoints = tracklineRoming.features[0].geometry.coordinates
      let index = 0
      this.clickPont = new DrawTransformCurve(this.viewer)
      this.timer && clearInterval(this.timer)
      var position = {
                       lon: linePoints[0][0], lat: linePoints[0][1], alt: 10,
                   }
      var rotates = {
          heading: 0,
          pitch: 0,
          roll: 0
      }
      var _this = this
      this.timer = setInterval(() => {
          if(linePoints.length <= index){
            clearInterval(_this.timer)
            // _this.showTrackPop(linePoints[linePoints.length - 1], 'endDiv', false)
          }
          _this.clickPont.startTransfromEnt({
              id: 'a3-60',
              position,
              rotates,
              ifDrawLine: true,
              color: '#FFAF00'
          })
          ++index
          // console.log(index)
          // console.log(linePoints)
          if(linePoints.length > index){
          position.lon = linePoints[index][0];
          position.lat = linePoints[index][1];
          
          // position.alt += Math.random() * 1;
          // rotates.heading += Math.random() * 10;
          // rotates.pitch += Math.random() * 10;
          // rotates.roll += Math.random() * 10;
        }
      }, 1000)
    },
    /**
     * 轨迹弹窗
     * position 显示位置
     * divId 容器id
     * isStart 是否为起点
     */
    showTrackPop(position, divId, isStart) {
      var that = this
      const div = document.createElement('div');
      div.id = divId
      if(isStart){
        div.innerHTML = '' +
        '<div class="track">' +
        '<div class="startCircle">始</div>' +
        '<div class="trackInfo"><div class="trackTime">2022/05/09 09:00:00</div><div class="trackAddress">南京市鼓楼区汉中路268号</div></div></div>';
      }else {
        div.innerHTML = '' +
        '<div class="track">' +
        '<div class="endCircle">终</div>' +
        '<div class="trackInfo"><div class="trackTime">2022/05/09 09:00:00</div><div class="trackAddress">南京市鼓楼区汉中路268号</div></div></div>';
      }     
      this.viewer.container.append(div);
      this.scene.postRender.addEventListener(() => {
        let windowCoord = Cesium.SceneTransforms.wgs84ToWindowCoordinates(
          that.scene,
          Cesium.Cartesian3.fromDegrees(...position, 0)
        );
        let x = windowCoord.x - div.offsetWidth / 2 + 100;
        let y = windowCoord.y - div.offsetHeight - 50;
        div.style.cssText = `
          position:absolute;
          left:0;
          top:0;
          height:90px;
          width:300px;
          transform:translate3d(${Math.round(x)}px,${Math.round(y)}px, 0);
      `;
      });
    },
    /**
     * 移除轨迹
     */
    removeTrack(){
      // if(this.trackLayer){
      //   this.viewer.dataSources.remove(this.trackLayer)
      // }
      this.removeLayer('trackPoints')
      this.removeDiv('startDiv')
      this.removeDiv('endDiv')
      this.timer && clearInterval(this.timer)
      if(this.clickPont){
        this.clickPont.clear()
      }
    },
    /**
     * div销毁
     */
    removeDiv(id) {
      var div = document.getElementById(id)
      console.log('div', div)
      if(div){
        div.remove()
        // div.parentNode.removeChild(div)
      }
      
      
    },
    /**
     * 定位
     * lon 经度
     * lat 纬度
     * alt 高度
     * heading 指向
     * pitch 视角
     */
    flyToPoint(lon, lat, alt, heading = 10, pitch = 1){
        this.viewer.camera.flyTo({
          destination: Cesium.Cartesian3.fromDegrees(lon, lat, alt),
          // orientation: {
          //     heading: Cesium.Math.toRadians(heading),
          //     pitch: Cesium.Math.toRadians(pitch),
          //     roll: 0
          // }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.map-warrper {
  position: relative;
  width: 100%;
  height: 100%;
  .container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    // z-index: 1;
  }
}
</style>
