<template>
  <ul class="wrap">
    <li v-for="(it, i) of data" :key="i">
      <div class="icon" :style="{ backgroundImage: `url(${it.icon})` }"></div>
      <div class="desc">
        <div class="tit">{{ it.tit }}</div>
        <countTo
          ref="countTo"
          :startVal="$countTo.startVal"
          :decimals="$countTo.decimals(it.num)"
          :endVal="it.num"
          :duration="$countTo.duration"
        />
      </div>
    </li>
  </ul>
</template>

<script>
export default {
  props: {
    data: {
      type: Array,
      default: () => {
        return [
          {
            num: 499,
            tit: '本年工单',
            // img: require('@/assets/csts/icon.png'),
            // url: require('@/assets/zhdd/icon5.png'),
            icon: require('@/assets/fxyp/icon_gh1.png'),
            name: '亿元'
          },
          {
            num: 34,
            tit: '上月工单',
            // img: require('@/assets/csts/icon.png'),
            // url: require('@/assets/zhdd/icon6.png'),
            icon: require('@/assets/fxyp/icon_gh1.png'),
            name: '亿元'
          },
        ]
      }
    }
  },
  name: 'EmergencyResources',
  data () {
    return {
      list: [
        {
          num: 499,
          tit: '严重精神障碍患者(人)',
          img: require('@/assets/csts/icon.png'),
          url: require('@/assets/zhdd/icon5.png'),
          icon: require('@/assets/fxyp/icon_gh1.png'),
          name: '亿元'
        },
        {
          num: 34,
          tit: '社区矫正(人)',
          img: require('@/assets/csts/icon.png'),
          url: require('@/assets/zhdd/icon6.png'),
          icon: require('@/assets/fxyp/icon_gh2.png'),
          name: '亿元'
        },
      ]
    }
  }
}
</script>

<style lang="less" scoped>
.wrap {
  width: 100%;
  height: 100%;
  padding: 20px 0;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 30px;
  align-content: space-between;
  li {
    width: 28%;
    position: relative;
    display: flex;
    // gap: 12px;
    .icon {
      width: 47px;
      height: 47px;
      flex-shrink: 0;
      margin-right: 10px;
    }
    .desc {
      display: flex;
      flex-direction: column;
      // padding-top: 4px;
      gap: 3px;
      text-align: left;
      span {
        height: 20px;
        font-size: 26px;
        font-family: DINOT-Black, DINOT;
        font-weight: 900;
        color: #ffffff;
        line-height: 14px;
      }
      .tit {
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
        margin-bottom: 4px;
        // white-space: nowrap;
      }
    }
  }
}
</style>
