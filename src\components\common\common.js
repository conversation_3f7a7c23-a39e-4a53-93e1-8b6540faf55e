import aiWaringPop from '../shzl/aiWaringPop.vue'
import cameraPop from '../shzl/cameraPop.vue'
import tipsPop from '../shzl/tipsPop.vue'
import szyyPop from '../shzl/szyyPop.vue'
import jcyjPop from '../shzl/jcyjPop.vue'
import wgyPop from '../csts/wgyPop.vue'
import csglgdPop from '../csts/csglgdPop.vue'
import shaqPop from '../csts/shaqPop.vue'
import sthbPop from '../csts/sthbPop.vue'
import whlyPop from '../csts/whlyPop.vue'
import yjfkPop from '../csts/yjfkPop.vue'
import ggjtPop from '../csts/ggjtPop.vue'
import zdqyPop from '../csts/zdqyPop.vue'
import xxPop from '../csts/xxPop.vue'
import wbPop from '../csts/wbPop.vue'
import ylcsPop from '../csts/ylcsPop.vue'
import hczPop from '../csts/hczPop.vue'
import dyyPop from '../csts/dyyPop.vue'
// import dzzPop from '../csts/dzzPop.vue'
import xzqyPop from '../csts/xzqyPop.vue'
import zwzxPop from '../csts/zwzxPop.vue'
import zhzxPop from '../csts/zhzxPop.vue'
// import spjkPop from '../csts/spjkPop.vue'
import sjtjPop from '../shzl/sjtjPop.vue'
import sjtjPopAi from '../shzl/sjtjPopAi.vue'
import wlsbPop from '../jcyj/wlsbPop.vue'
import yjfxPop from '../jcyj/yjfxPop.vue'
import rwpfPop from '../shzl/rwpfPop.vue'
import djylPop from '../csts/djylPop.vue'
import bjxqPop from '../csts/bjxqPop.vue'
import pfSuccessPop from '../shzl/pfSuccessPop.vue'
import wgyPopShzl from '../csts/wgyPopShzl.vue'
import sphsPop from '../shzl/sphsPop.vue' //视频会商
import spjkPop from '../shzl/spjkPop.vue' //视频监控
import txddPop from '../shzl/txddPop.vue' //通讯调度

import jcdwPop from '@/components/djyl/jcdwPop.vue' //基层党委弹窗
import dzzPop from '@/components/djyl/dzzPop.vue' //党总支弹窗
import dzbPop from '@/components/djyl/dzbPop.vue' //党支部弹窗
import dyPop from '@/components/djyl/dyPop.vue' //党员弹窗

// import ddqqPop from '@/components/bjnj/ddqqPop.vue' //调度请求
// import yjjzzxxqPop from '@/components/bjnj/yjjzzxxqPop.vue' //应急救灾中心详情
// import sbqqPop from '@/components/bjnj/sbqqPop.vue' //上报请求
// import hlqqPop from '@/components/bjnj/hlqqPop.vue' //忽略请求


const NowTimeComponent = {
  install: Vue => {
    Vue.component('aiWaringPop', aiWaringPop)
    Vue.component('cameraPop', cameraPop)
    Vue.component('tipsPop', tipsPop)
    Vue.component('szyyPop', szyyPop)
    Vue.component('jcyjPop', jcyjPop)
    Vue.component('wgyPop', wgyPop)
    Vue.component('csglgdPop', csglgdPop)
    Vue.component('shaqPop', shaqPop)
    Vue.component('sthbPop', sthbPop)
    Vue.component('whlyPop', whlyPop)
    Vue.component('yjfkPop', yjfkPop)
    Vue.component('ggjtPop', ggjtPop)
    // Vue.component('dzzPop', dzzPop)
    Vue.component('xzqyPop', xzqyPop)
    Vue.component('zwzxPop', zwzxPop)
    Vue.component('zhzxPop', zhzxPop)
    Vue.component('zdqyPop', zdqyPop)
    Vue.component('xxPop', xxPop)
    Vue.component('wbPop', wbPop)
    Vue.component('ylcsPop', ylcsPop)
    Vue.component('hczPop', hczPop)
    Vue.component('dyyPop', dyyPop)
    // Vue.component('spjkPop', spjkPop)
    Vue.component('sjtjPop', sjtjPop)
    Vue.component('sjtjPopAi', sjtjPopAi)
    Vue.component('wlsbPop', wlsbPop)
    Vue.component('yjfxPop', yjfxPop)
    Vue.component('rwpfPop', rwpfPop)
    Vue.component('djylPop', djylPop)
    Vue.component('bjxqPop', bjxqPop)
    Vue.component('pfSuccessPop', pfSuccessPop)
    Vue.component('wgyPopShzl', wgyPopShzl)
    Vue.component('sphsPop', sphsPop)
    Vue.component('spjkPop', spjkPop)
    Vue.component('txddPop', txddPop)
    Vue.component('jcdwPop', jcdwPop)
    Vue.component('dzzPop', dzzPop)
    Vue.component('dzbPop', dzbPop)
    Vue.component('dyPop', dyPop)
    // Vue.component('ddqqPop', ddqqPop)
    // Vue.component('yjjzzxxqPop', yjjzzxxqPop)
    // Vue.component('sbqqPop', sbqqPop)
    // Vue.component('hlqqPop', hlqqPop)
  }
}
export default NowTimeComponent