.el-dropdown {
	color: #caecff;
	font-family: YouSheBiaoTiHei;
	font-size: 16px;
	line-height: 21px;
	text-align: right;
	font-style: normal;
	line-height: 40px;
	right: 0;
	position: absolute;
}
.el-dropdown-menu {
	background-color: #c6e2e7 !important;
	border: 1px solid #d2f2ea !important;
}
.el-dropdown-menu.select_ {
	transform-origin: center top !important;
	width: 92px;
	background: rgba(15, 43, 87, 0.9);
	border: 1px solid #2b7bbb;
	border-radius: 3px;
	position: absolute;
	top: 731px !important;
	left: 314px !important;

	.el-dropdown-menu__item {
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
		line-height: 40px;

		&:focus,
		&:not(.is-disabled):hover {
			// background: #185493;
			// color: rgba(255, 255, 255, 0.8);

			background: #1e8e82 !important;
			color: #cbeaed !important;
		}
	}
}

.select_sb {
	transform-origin: center top !important;
	width: 120px;
	background-color: rgba(15, 43, 87, 0.9) !important;
	border: 1px solid #2b7bbb !important;
	border-radius: 3px !important;
	.el-dropdown-menu__item {
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
		line-height: 40px;

		&:focus,
		&:not(.is-disabled):hover {
			// background: #185493;
			// color: rgba(255, 255, 255, 0.8);

			background: #1e8e82 !important;
			color: #cbeaed !important;
		}
	}
	::v-deep .el-icon-arrow-down:before {
		// content: '\e790' !important;
		color: #caecff !important;
	}

	::v-deep .el-popper[x-placement^='bottom'] .popper__arrow:after {
		display: none;
	}

	::v-deep .el-popper .popper__arrow:after {
		border-bottom-color: #185493 !important;
	}
}
.el-dropdown-menu.select_month {
	transform-origin: center top !important;
	width: 87px;
	background: rgba(15, 43, 87, 0.9);
	border: 1px solid #2b7bbb;
	border-radius: 3px;
	position: absolute;
	top: 731px !important;
	left: 418px !important;
	// height: 300px;
	// overflow: auto;

	.el-dropdown-menu__item {
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
		line-height: 40px;

		&:focus,
		&:not(.is-disabled):hover {
			// background: #185493;
			// color: rgba(255, 255, 255, 0.8);
			background: #1e8e82 !important;
			color: #cbeaed !important;
		}
	}
}

.el-dropdown-menu.select_Fy {
	transform-origin: center top !important;
	width: 92px;
	background: rgba(15, 43, 87, 0.9);
	border: 1px solid #2b7bbb;
	border-radius: 3px;
	position: absolute;
	top: 456px !important;
	left: 1665px !important;
	.el-dropdown-menu__item {
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
		line-height: 40px;

		&:focus,
		&:not(.is-disabled):hover {
			// background: #185493;
			// color: rgba(255, 255, 255, 0.8);
			background: #1e8e82 !important;
			color: #cbeaed !important;
		}
	}
}
.el-dropdown-menu.select_monthFy {
	transform-origin: center top !important;
	width: 92px;
	background: rgba(15, 43, 87, 0.9);
	border: 1px solid #2b7bbb;
	border-radius: 3px;
	position: absolute;
	top: 456px !important;
	left: 1766px !important;

	.el-dropdown-menu__item {
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
		line-height: 40px;

		&:focus,
		&:not(.is-disabled):hover {
			// background: #185493;
			// color: rgba(255, 255, 255, 0.8);
			background: #1e8e82 !important;
			color: #cbeaed !important;
		}
	}
}

.ivu-page-total {
	margin-top: 10px;
	font-size: 18px;
	color: #fff;
}

.el-message--warning {
	z-index: 1000000 !important;
}
.el-dropdown-menu.select_s {
	transform-origin: center top !important;
	width: 140px;
	height: 150px;
	background: rgba(15, 43, 87, 0.9);
	border: 1px solid #2b7bbb;
	border-radius: 3px;
	position: absolute;
	overflow-y: scroll;
	// top: 710px !important;
	// left: 385px !important;
	top: 62% !important;
	// left: 16% !important;
	&::-webkit-scrollbar {
		/*滚动条整体样式*/
		width: 0px;
		background: transparent;
		// border: 1px solid #999;
		/*高宽分别对应横竖滚动条的尺寸*/
		// height: 1px;
	}

	&::-webkit-scrollbar-thumb {
		/*滚动条里面小方块*/
		border-radius: 2px;
		box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
		background: rgb(45, 124, 228);
		height: 100px;
	}
	.el-dropdown-menu__item {
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
		line-height: 40px;
		padding: 0 5px;
		&:focus,
		&:not(.is-disabled):hover {
			// background: #185493;
			// color: rgba(255, 255, 255, 0.8);

			background: #1e8e82 !important;
			color: #cbeaed !important;
		}
	}
	.el-popper[x-placement^='bottom'] .popper__arrow:after {
		border-bottom-color: #185493 !important;
	}
	.popper__arrow:after {
		display: none !important;
	}
}
.el-dropdown-menu.select_m {
	transform-origin: center top !important;
	width: 150px;
	height: 150px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	background: rgba(15, 43, 87, 0.9);
	border: 1px solid #2b7bbb;
	border-radius: 3px;
	position: absolute;
	overflow-y: scroll;
	// top: 720px !important;
	// right: 150px !important;
	top: 43% !important;
	right: 0% !important;
	&::-webkit-scrollbar {
		/*滚动条整体样式*/
		width: 0px;
		background: transparent;
		// border: 1px solid #999;
		/*高宽分别对应横竖滚动条的尺寸*/
		// height: 1px;
	}

	&::-webkit-scrollbar-thumb {
		/*滚动条里面小方块*/
		border-radius: 2px;
		box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
		background: rgb(45, 124, 228);
		height: 100px;
	}
	.el-dropdown-menu__item {
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
		line-height: 40px;
		padding: 0 5px;
		&:focus,
		&:not(.is-disabled):hover {
			// background: #185493;
			// color: rgba(255, 255, 255, 0.8);
			background: #1e8e82 !important;
			color: #cbeaed !important;
		}
	}
	.el-popper[x-placement^='bottom'] .popper__arrow:after {
		border-bottom-color: #185493 !important;
	}
	.popper__arrow:after {
		display: none !important;
	}
}

.el-dropdown-menu.select_t {
	transform-origin: center top !important;
	width: 140px;
	height: 150px;
	overflow-y: scroll;
	background: rgba(15, 43, 87, 0.9);
	border: 1px solid #2b7bbb;
	border-radius: 3px;
	position: absolute;
	// top: 370px !important;
	// left: 350px !important;
	top: 36% !important;
	// left:16%!important;
	&::-webkit-scrollbar {
		/*滚动条整体样式*/
		width: 0px;
		background: transparent;
		// border: 1px solid #999;
		/*高宽分别对应横竖滚动条的尺寸*/
		// height: 1px;
	}

	&::-webkit-scrollbar-thumb {
		/*滚动条里面小方块*/
		border-radius: 2px;
		box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
		background: rgb(45, 124, 228);
		height: 100px;
	}
	.el-dropdown-menu__item {
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
		line-height: 40px;
		padding: 0 5px;
		&:focus,
		&:not(.is-disabled):hover {
			// background: #185493;
			// color: rgba(255, 255, 255, 0.8);
			background: #1e8e82 !important;
			color: #cbeaed !important;
		}
	}
	.el-popper[x-placement^='bottom'] .popper__arrow:after {
		border-bottom-color: #185493 !important;
	}
	.popper__arrow:after {
		display: none !important;
	}
}
.el-dropdown-menu.select_trend {
	transform-origin: center top !important;
	width: 140px;
	height: 150px;
	overflow-y: scroll;
	background: rgba(15, 43, 87, 0.9);
	border: 1px solid #2b7bbb;
	border-radius: 3px;
	position: absolute;
	// top: 140px !important;
	// left: 378px !important;
	top: 11% !important;
	// left:16%!important;
	&::-webkit-scrollbar {
		/*滚动条整体样式*/
		width: 0px;
		background: transparent;
		// border: 1px solid #999;
		/*高宽分别对应横竖滚动条的尺寸*/
		// height: 1px;
	}

	&::-webkit-scrollbar-thumb {
		/*滚动条里面小方块*/
		border-radius: 2px;
		box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
		background: rgb(45, 124, 228);
		height: 100px;
	}
	.el-dropdown-menu__item {
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
		line-height: 40px;
		padding: 0 5px;
		&:focus,
		&:not(.is-disabled):hover {
			// background: #185493;
			// color: rgba(255, 255, 255, 0.8);
			background: #1e8e82 !important;
			color: #cbeaed !important;
		}
	}
	.el-popper[x-placement^='bottom'] .popper__arrow:after {
		border-bottom-color: #185493 !important;
	}
	.popper__arrow:after {
		display: none !important;
	}
}

.el-dropdown-menu.select_head {
	transform-origin: center top !important;
	width: 180px;
	height: 170px;
	overflow-y: scroll;
	background: rgba(15, 43, 87, 0.9);
	border: 1px solid #2b7bbb;
	border-radius: 3px;
	position: absolute;
	// top: 200px !important;
	// left: 600px !important;
	top: 21% !important;
	left: 31% !important;
	&::-webkit-scrollbar {
		/*滚动条整体样式*/
		width: 0px;
		background: transparent;
		// border: 1px solid #999;
		/*高宽分别对应横竖滚动条的尺寸*/
		// height: 1px;
	}

	&::-webkit-scrollbar-thumb {
		/*滚动条里面小方块*/
		border-radius: 2px;
		box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
		background: rgb(45, 124, 228);
		height: 100px;
	}
	.el-dropdown-menu__item {
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
		line-height: 40px;
		padding: 0 5px;
		&:focus,
		&:not(.is-disabled):hover {
			// background: #185493;
			// color: rgba(255, 255, 255, 0.8);
			background: #1e8e82 !important;
			color: #cbeaed !important;
		}
	}
	.el-popper[x-placement^='bottom'] .popper__arrow:after {
		border-bottom-color: #185493 !important;
	}
	.popper__arrow:after {
		display: none !important;
	}
}
.el-dropdown-menu.select_head2 {
	transform-origin: center top !important;
	width: 180px;
	height: 170px;
	overflow-y: scroll;
	background: rgba(15, 43, 87, 0.9);
	border: 1px solid #2b7bbb;
	border-radius: 3px;
	position: absolute;
	// top: 200px !important;
	// left: 600px !important;
	top: 25.5% !important;
	left: 31% !important;
	&::-webkit-scrollbar {
		/*滚动条整体样式*/
		width: 0px;
		background: transparent;
		// border: 1px solid #999;
		/*高宽分别对应横竖滚动条的尺寸*/
		// height: 1px;
	}

	&::-webkit-scrollbar-thumb {
		/*滚动条里面小方块*/
		border-radius: 2px;
		box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
		background: rgb(45, 124, 228);
		height: 100px;
	}
	.el-dropdown-menu__item {
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
		line-height: 40px;
		padding: 0 5px;
		&:focus,
		&:not(.is-disabled):hover {
			// background: #185493;
			// color: rgba(255, 255, 255, 0.8);
			background: #1e8e82 !important;
			color: #cbeaed !important;
		}
	}
	.el-popper[x-placement^='bottom'] .popper__arrow:after {
		border-bottom-color: #185493 !important;
	}
	.popper__arrow:after {
		display: none !important;
	}
}

.el-dropdown-menu.select_r1 {
	transform-origin: center top !important;
	width: 140px;
	height: 150px;
	overflow-y: scroll;
	background: rgba(15, 43, 87, 0.9);
	border: 1px solid #2b7bbb;
	border-radius: 3px;
	position: absolute;
	top: 160px !important;
	right: 150px !important;
	&::-webkit-scrollbar {
		/*滚动条整体样式*/
		width: 0px;
		background: transparent;
		// border: 1px solid #999;
		/*高宽分别对应横竖滚动条的尺寸*/
		// height: 1px;
	}

	&::-webkit-scrollbar-thumb {
		/*滚动条里面小方块*/
		border-radius: 2px;
		box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
		background: rgb(45, 124, 228);
		height: 100px;
	}
	.el-dropdown-menu__item {
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
		line-height: 40px;
		padding: 0 5px;
		&:focus,
		&:not(.is-disabled):hover {
			// background: #185493;
			// color: rgba(255, 255, 255, 0.8);
			background: #1e8e82 !important;
			color: #cbeaed !important;
		}
	}
	.el-popper[x-placement^='bottom'] .popper__arrow:after {
		border-bottom-color: #185493 !important;
	}
	.popper__arrow:after {
		display: none !important;
	}
}
.el-time-panel {
	background: rgba(15, 43, 87, 0.9) !important;
	border: 1px solid #2b7bbb !important;
	border-radius: 3px !important;
	font-family: PingFangSC-Regular, PingFang SC !important;
	font-weight: 400 !important;
	color: #409eff !important;
	.el-time-panel__content {
		&::before {
			content: '';
			top: 50%;
			position: absolute;
			margin-top: -15px;
			height: 32px;
			z-index: -1;
			left: 0;
			right: 0;
			box-sizing: border-box;
			padding-top: 6px;
			text-align: left;
			border-top: 1px solid #2b7bbb;
			border-bottom: 1px solid #2b7bbb;
		}
		&::after {
			content: '';
			top: 50%;
			position: absolute;
			margin-top: -15px;
			height: 32px;
			z-index: -1;
			left: 0;
			right: 0;
			box-sizing: border-box;
			padding-top: 6px;
			text-align: left;
			border-top: 1px solid #2b7bbb;
			border-bottom: 1px solid #2b7bbb;
		}
		.el-time-spinner__item {
			font-family: PingFangSC-Regular, PingFang SC !important;
			font-weight: 400 !important;
			color: #409eff !important;
			font-size: 12px;
			&:hover {
				background: rgba(255, 255, 255, 0.2) !important;
			}
		}
	}
	.el-time-panel__footer {
		.cancel {
			color: #409eff !important;
		}
		border-top: 1px solid #2b7bbb !important;
	}
}
.el-cascader__dropdown {
	background: none !important;
	border: 1px solid #2b7bbb !important;
}
.el-cascader-menu__list {
	transform-origin: center top !important;
	// width: 140px;
	// height: 150px;
	background: rgba(15, 43, 87, 0.9);
	border: 1px solid #2b7bbb;
	border-radius: 3px;
}
.el-cascader-menu {
	border: none !important;
}
.el-popper .popper__arrow:after {
	border-bottom-color: rgba(15, 43, 87, 0.9);
}
.el-cascader-node:not(.is-disabled):focus,
.el-cascader-node:not(.is-disabled):hover {
	background: #185493 !important;
	color: rgba(255, 255, 255, 0.8) !important;
}
.el-cascader-node {
	font-size: 14px;
	font-family: PingFangSC-Regular, PingFang SC;
	font-weight: 400;
	color: rgba(255, 255, 255, 0.8);
	text-align: center;
	line-height: 40px;
	padding: 0 5px;
}
.el-cascader-node.is-selectable.in-active-path {
	color: rgba(255, 255, 255, 0.8) !important;
}
.el-select {
	width: 176px !important;
	height: 22px;
}
.el-select .el-input__inner {
	width: 176px !important;
	height: 100% !important;
	border-radius: 20px !important;
	// background: rgba(255, 255, 255, 0.46);
	// border: 1px solid #e5e6eb;
	// color: #24818c !important;
	background: #04244e;
	border: 1px solid rgba(0, 163, 255, 0.5);
	color: #b0e0ff;
}

.el-select-dropdown {
	transform-origin: center top !important;
	// width: 140px;
	// height: 150px;

	// background: rgba(23, 138, 126, 0.9) !important;
	// background: url(~@/assets/img/block-box/other-argmach-box-bg.png);
	/* 背景不重复 */
	background-repeat: no-repeat;
	/* 背景拉伸 使其完全填充*/
	background-size: 100% 100%;
	/* 背景居中显示（可选） */
	background-position: center;

	background: #04244e !important;
	border: 1px solid rgba(0, 163, 255, 0.5) !important;
	border-radius: 3px !important;
}
.el-select-dropdown .el-select-dropdown__wrap .el-select-dropdown__list {
	padding-top: 0 !important ;
	padding-bottom: 0 !important ;
}

.el-picker-panel {
	background-color: rgba(15, 43, 87, 0.9) !important;
	background: rgba(15, 43, 87, 0.9) !important;
	border: 1px solid #2b7bbb !important;
	border-radius: 3px !important;
	color: rgba(194, 221, 252, 1) !important;
}
.el-select-dropdown__item {
	height: 30px !important;
	font-size: 12px !important;
	font-family: PingFangSC-Regular, PingFang SC !important;
	font-weight: 400 !important;
	// color: rgba(36, 129, 140, 0.8) !important;
	color: rgb(176, 224, 255) !important;
	text-align: center !important;
	line-height: 30px !important;
	padding: 0 5px !important;

	&.selected {
		// background: #1b8c80 !important;
		// color: rgba(255, 255, 255, 0.8) !important;

		background: #178dfa !important;
		color: #fff !important;
	}
}
.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
	// background: #1b8c80 !important;
	// color: rgba(255, 255, 255, 0.8) !important;
	background: #178dfa !important;
	color: #fff !important;
}
.el-popper[x-placement^='bottom'] .popper__arrow:after {
	// border-bottom-color: #0e2952 !important;
	// border-top-color: #0e2952 !important;

	border-bottom-color: #24818c !important;
	border-top-color: #24818c !important;
}

.el-popper[x-placement^='top'] .popper__arrow:after {
	border-bottom-color: #24818c !important;
	border-top-color: #24818c !important;
}

.el-popper[x-placement^='bottom'] .popper__arrow {
	border-bottom-color: #24818c !important;
	border-top-color: #24818c !important;
}

.el-popper[x-placement^='top'] .popper__arrow {
	border-bottom-color: #24818c !important;
	border-top-color: #24818c !important;
}

.el-radio__inner {
	border: 1px solid rgba(0, 162, 255, 0.6) !important;
	border-radius: 100%;
	width: 14px;
	height: 14px;
	background-color: rgb(16, 99, 184) !important;
	cursor: pointer;
	box-sizing: border-box;
}

.el-checkbox__inner {
	border: 1px solid rgba(0, 162, 255, 0.6) !important;
	background-color: rgb(16, 99, 184) !important;
}

.el-table th.el-table__cell > .cell {
	color: #37c1ff;
}

.el-table--border:after,
.el-table--group:after,
.el-table:before {
	background-color: #091423 !important ;
}

.el-icon-date:before {
	position: relative !important;
	bottom: 3px !important;
}

.el-select .el-input .el-select__caret {
	display: flex;
	align-items: center;
}

.el-cascader .el-input .el-icon-arrow-down.is-reverse {
	display: flex;
	align-items: center;
}

.el-date-table th {
	border-bottom: 1px solid rgba(235, 238, 245, 0.6) !important;
}
.el-date-picker__header-label {
	color: #fff !important;
}
.el-year-table td .cell {
	color: #fff !important;
}
