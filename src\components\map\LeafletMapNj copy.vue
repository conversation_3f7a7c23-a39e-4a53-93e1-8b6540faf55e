<template>
	<div>
		<div ref="smLeaMap" class="mapContainer" />
		<div class="clear2" @click="clearLayers()" v-if="clear2"></div>

		<div class="back2" @click="backLayers()" v-if="back2"></div>

		<!-- <div class="weather" @click="showLayers">图形</div> -->
	</div>
</template>

<script>
var clusterIndex
var clusterMarkers
const DataVUrl = 'https://geo.datav.aliyun.com/areas_v3/bound/'
// const DataVUrl = 'https://dpzs.camtc.cn/areas_v3/bound/'
import { fromArrayBuffer } from 'geotiff'
import * as plotty from 'plotty'
import testData from './testData.json'
// import clusterData from './clusterData.json'
import gcoord from 'gcoord'
import RadarScanner from './utils/RadarScanner'
import { picTif, walarmPic, meteorologicalPic, rainfallPic, windPic, extractFile } from '@/api/common/common'
import iconNjBUrl from '@/assets/mskx/icon_nj_b.png'
import simplify from 'simplify-js'
// import turf from '@turf/turf'

export default {
	components: {},
	props: {
		clear2: {
			type: Boolean,
			default: true,
		},
		back2: {
			type: Boolean,
			default: true,
		},
		xzpd: {
			type: Boolean,
			default: true,
		},
		haveRight: {
			type: Boolean,
			default: true,
		},
	},
	data() {
		return {
			marker1: [],
			polygon: null,
			gridLayers: [],
			map: null,
			mapOptions: {
				// 中心点
				center: this.haveRight ? [35.595703125, 105.029296875] : [39, 98.029296875],
				// 当前显示层级
				zoom: 4,
				// 最小显示层级
				minZoom: 3,
				maxZoom: 17,
			},
			rootLayerGroup: null,
			layerList: [],
			tdtKey: 'd9e005e98452c5eb2ad792489d2a369b',
			bounds: [100.063476, 23.805449, 119.0215, 40.713955],
			dPolyline: null,
			dPolygon: null,
			maskLayer: null,
			imageMaskLayer: null,
			geojsonLayer: null,
			dynamicCicle: null,
			dynamicCicleInterval: null,
			// lastAreaCode: [100000],
			lastAreaCode: [],
			circleList: [],
			radarScanner: null,
			wmsTemwLayer: null,
			canvasIconLayer_: null,
			sstsMarkers: {},
			markerSourceData: {},
			currentLayerId: '',
			currentMapZoom: 4,
			timer001: null,
			timerLoadGeoJsonLayer: null,

			newZoomCluster: 0,
			maxZoomCluster: 9,
			oldZoomCluster: 0,
			isClickGrid: false,
			currentMapLevel: '',
		}
	},
	mounted() {
		this.initMap()
	},
	methods: {
		/**
		 * 初始化地图
		 */
		initMap() {
			this.map = L.map(this.$refs.smLeaMap, {
				crs: L.CRS.EPSG4326,
				center: this.mapOptions.center,
				zoomDelta: 0.5,
				zoom: this.mapOptions.zoom,
				minZoom: this.mapOptions.minZoom,
				maxZoom: this.mapOptions.maxZoom,
				// 不添加属性说明控件
				attributionControl: false,
				zoomControl: false,
				editable: true,
				doubleClickZoom: false,
			})

			// 天地图
			// L.tileLayer(
			//   'http://t1.tianditu.com/img_c/wmts?layer=img&style=default&tilematrixset=c&Service=WMTS&Request=GetTile&Version=1.0.0&Format=tiles&TileMatrix={z}&TileCol={x}&TileRow={y}&tk=' +
			//     this.tdtKey,
			//   {
			//     tileSize: 256,
			//     zoomOffset: 1
			//   }
			// ).addTo(this.map)
			// L.tileLayer(
			//   'http://t1.tianditu.com/cia_c/wmts?layer=cia&style=default&tilematrixset=c&Service=WMTS&Request=GetTile&Version=1.0.0&Format=tiles&TileMatrix={z}&TileCol={x}&TileRow={y}&tk=' +
			//     this.tdtKey,
			//   {
			//     tileSize: 256,
			//     zoomOffset: 1
			//   }
			// ).addTo(this.map)
			this.loadTdtXYZ()
			// this.InformationMarker()

			// 添加根图层，方便管理其他图层
			this.rootLayerGroup = this.group ? this.group : L.featureGroup().addTo(this.map)

			this.onMapClick()
			// this.onMapZoomend();

			this.map.on('moveend', this.updateCluster)

			this.$nextTick(() => {
				// 加载行政区域
				this.loadGeoJsonLayerFromUrl(DataVUrl + '100000_full.json', 'geojson', true)
				// 随机生成1000个点

				// 轨迹
				const trackData = testData.track
				// this.loadTrackLayer('trcak', trackData)
				// 热力
				// const heatData = testData.heat
				// this.loadHeatLayer('heat', heatData)
				// 数字点位（模仿聚合点）
				const numData = [
					{
						latlng: [33.5517, 119.6803],
						props: {
							name: '江苏省',
							num: 17777,
						},
					},
					{
						latlng: [32.2233, 117.0875],
						props: {
							name: '安徽省',
							num: 28888,
						},
					},
				]
				const that = this
				that.map.createPane('gcoordCustomPane')
				that.map.getPane('gcoordCustomPane').style.zIndex = 500
				that.map.createPane('markerCustomPane')
				that.map.getPane('markerCustomPane').style.zIndex = 600
				that.canvasIconLayer_ = L.canvasIconLayer({ moveReset: false, collisionFlg: true, isImg: true }).addTo(that.map)
				//为标记点创建点击事件
				that.canvasIconLayer_.addOnClickListener(function(event, data) {
					if (that.map.getZoom() >= 10) {
						const { info: data_ } = data[0].data.options.data.props
						const { agmachId } = data_
						that.$emit('poiClick', that.currentLayerId, [agmachId])
					}
				})
				that.map.on('zoomstart', () => {
					console.log('aaaaaaaaaaaaaaaa')

					// that.removeLayer(that.currentLayerId)

					if (this.map.getZoom() >= 10) {
						// this.removeLayer(this.currentLayerId)
					} else if (this.map.getZoom() <= 9) {
						if (this.currentMapLevel !== '') {
							// this.clearCluster()
						}
					}
				})
				that.map.on(
					'zoomend',
					that.onWheelMap,
					// () => {
					// 	console.log('bbbbbbbbbbbbbbb', that.map.getZoom())
					// 	that.drawSstsPoiMarker(that.markerSourceData[that.currentLayerId] || [], that.currentLayerId) //调用4marker标点方法
					// }
				)
			})
		},

		async onWheelMap(ev) {
			this.currentMapZoom = this.map.getZoom()
			if (!this.isClickGrid) {
				// //给鼠标滚动添加防抖
				clearTimeout(this.timer001)
				this.timer001 = setTimeout(async () => {
					this.newZoomCluster = this.map.getZoom()
					console.log('this.map.getZoom()--==', this.map.getZoom())

					if (
						(this.newZoomCluster > this.maxZoomCluster && this.oldZoomCluster < this.maxZoomCluster) ||
						(this.newZoomCluster < this.maxZoomCluster && this.oldZoomCluster > this.maxZoomCluster)
					) {
						this.oldZoomCluster = this.newZoomCluster

						// if (this.newZoomCluster > this.maxZoomCluster && this.oldZoomCluster < this.maxZoomCluster) {
						// 	this.oldZoomCluster = this.newZoomCluster
						// 	//保留之前数据绘制
						// 	clearMarkers(markerArrayObj.value)
						// 		.then(() => {
						// 			if (markerArrayObj.value.length > 0) {
						// 				markerArrayObj.value.forEach((marker) => {
						// 					myMap.addOverLay(marker)
						// 				})
						// 			} else {
						// 				getMarkers()
						// 			}
						// 		})
						// 		.finally(() => {
						// 		})
						// } else if (this.newZoomCluster < this.maxZoomCluster && this.oldZoomCluster > this.maxZoomCluster) {
						// 	this.oldZoomCluster = this.newZoomCluster
						// 	//重新获取数据绘制
						// 	// clearMarkers(markerArrayObj.value).then(() => {
						// 	// 	getMarkers()
						// 	// })
						// }
						// this.currentMapLevel == 'province' || this.currentMapLevel == 'city' || this.currentMapLevel == 'district'
						// if (this.currentMapLevel !== '') {
						// 	await this.removeLayer(this.currentLayerId)
						// 	this.drawSstsPoiMarker(this.markerSourceData[this.currentLayerId] || [], this.currentLayerId) //调用4marker标点方法
						// }else if(this.currentMapLevel !== 'country'){

						// }
						if (this.map.getZoom() >= 10) {
							await this.removeLayer(this.currentLayerId)
							this.drawSstsPoiMarker(this.markerSourceData[this.currentLayerId] || [], this.currentLayerId) //调用4marker标点方法
						} else if (this.map.getZoom() <= 9) {
							if (this.currentMapLevel !== '') {
								this.clearCluster()
								this.drawSstsBigDataClusterPoint(
									this.markerSourceData[this.currentLayerId] || [],
									require('@/assets/mskx/icon_nj_b.png'),
									[48, 62],
									{
										largeIconUrl: './imgs/cluster/nj1.png',
										mediumIconUrl: './imgs/cluster/nj2.png',
										smallIconUrl: './imgs/cluster/nj3.png',
									},
									this.currentLayerId,
								)
							}
						}
					}
				}, 500)
			}
		},
		/**
		 * 根据经纬度数组画区域
		 */
		// serefresh(point){
		//   console.log('point',point)
		//   let pointList = []
		//   let arr = []
		//   arr[0] = point[0].map(item => [item[1], item[0]])
		//   pointList.push(arr)
		//   const lineLayer = L.layerGroup()
		//   this.rootLayerGroup.addLayer(lineLayer)
		//   let color = 'red';
		//   for(let i=0; i< pointList.length; i++){
		//     console.log('pointList[i]',pointList[i])
		//     this.gridLayers[i] = L.polygon(pointList[i],{fillColor: color,fillOpacity: 0.5 ,fill: true, opacity: 1, radius: 6,  weight:0.1});
		//     console.log('this.gridLayers',this.gridLayers)
		//     lineLayer.addLayer(this.gridLayers[i]);
		//     // this.gridLayers[i].bindPopup("Direction: " + val[i][0] +"<br/>"+ "Value: " + val[i][1]);
		//   }
		// },
		serefresh(point) {
			console.log('point', point)
			this.polygon = L.polygon(point, { color: 'yellow', fillColor: 'yellow', fillOpacity: 1 }).addTo(this.map)
			// zoom the map to the polygon
			this.map.fitBounds(this.polygon.getBounds())
		},
		/**
		 * 天地图XYZ方式加载
		 */
		loadTdtXYZ() {
			const tdt_url_bottom = 'https://t{s}.tianditu.gov.cn/DataServer?T=img_c&x={x}&y={y}&l={z}&tk='
			const tdt_url_label = 'https://t{s}.tianditu.gov.cn/DataServer?T=cia_c&x={x}&y={y}&l={z}&tk='
			const layer_bottom = L.tileLayer(tdt_url_bottom + this.tdtKey, {
				zoomOffset: 1,
				tileSize: 256,
				minZoom: 3,
				maxZoom: 17,
				subdomains: [0, 1, 2, 3, 4, 5, 6, 7],
			})
			const layer_label = L.tileLayer(tdt_url_label + this.tdtKey, {
				zoomOffset: 1,
				tileSize: 256,
				minZoom: 3,
				maxZoom: 17,
				subdomains: [0, 1, 2, 3, 4, 5, 6, 7],
			})
			layer_bottom.addTo(this.map)
			layer_label.addTo(this.map)
		},
		/**
		 * 地图点击
		 */
		onMapClick() {
			this.map.on('click', (e) => {
				console.log('onMapClickE---===', e)
				console.log('overlay---===', e.overlay)

				console.log(this.map.getZoom())
				console.log('点击经纬度：', [e.latlng.lng, e.latlng.lat])
			})
		},
		drawCircle(data, r = 2, color = '#c53114') {
			this.map.setView([data[0].lat, data[0].lon], 12)
			this.removeCicleList()
			this.circleList = []
			if (data.length) {
				data.forEach((item, index) => {
					// 创建圆形对象
					let circle = L.circle([item.lat, item.lon], {
						color: color,
						fillColor: color,
						fill: true,
						radius: r, // 单位：米
						weight: 0,
						fillOpacity: '1',
					})
					this.circleList.push(circle)
					// 将圆形添加到地图上
					circle.addTo(this.map)
				})
			}
		},
		removeCicleList() {
			if (this.circleList.length) {
				this.circleList.map((item) => {
					this.map.removeLayer(item)
				})
			}
		},
		/**
		 * 加载动态光圈
		 * @param {*} center 中心点[纬度，经度]
		 * @param {*} radius 半径（单位米）
		 * @param {*} color 颜色
		 */
		loadDaynamicCicle(center, radius, color = '#FF9900') {
			this.dynamicCicle = L.circle(center, {
				radius: 5, // 初始半径
				color: color,
				fillColor: color,
				fillOpacity: 0.2,
				weight: 2,
			}).addTo(this.map)
			const radiuschange = Math.round(radius / 25)
			var that = this
			this.dynamicCicleInterval = setInterval(() => {
				that.dynamicCicle.setRadius(that.dynamicCicle.getRadius() + radiuschange)
				if (that.dynamicCicle.getRadius() > radius) {
					that.dynamicCicle.setRadius(5)
				}
			}, 200)
		},
		/**
		 * 移除动态光圈
		 */
		removeDaynamicCicle() {
			this.dynamicCicleInterval && clearInterval(this.dynamicCicleInterval)
			this.dynamicCicle && this.map.removeLayer(this.dynamicCicle)
		},
		/**
		 * 加载雷达扫描效果
		 * @param {*} coord 扫描中心点[118.7644, 32.0977]
		 * @param {*} radius 半径
		 */
		loadRadarScan(coord, radius) {
			this.removeRadarScan()
			this.radarScanner = new RadarScanner(this.map, gcoord)
			this.radarScanner.initRadarLayer(coord, radius)
		},
		removeRadarScan() {
			this.radarScanner && this.radarScanner.removeRadarLayer()
		},
		/**
		 * 加载wms气象图层
		 * @param {*} layerId 图层id
		 * @param {*} layers 查询图层名
		 * @param {*} category 智能格点
		 * @param {*} element 要素名
		 * @param {*} height 高度
		 * @param {*} publish_time 发布时间
		 * @param {*} valid_time 时间区间
		 * @param {*} opacity 透明度 范围0-1
		 */
		loadWmsLayer(layerId, layers, category, element, height, publish_time, valid_time, opacity) {
			this.removeLayer(layerId)
			const wmsLayer = L.tileLayer.wms('http://101.201.56.243/geoserver/gtweb/wms', {
				layers: layers,
				format: 'image/png',
				transparent: true,
				// 过滤查询参数
				// cql_filter: `product_category='WF-NWFD' and product_element='TMA' and geo_height='L88' and coverage_area='CHN' and publish_time='20240416080000' and valid_time='00000-02400'`,
				// cql_filter: `product_category='CLDAS2' and product_element='TMA' and geo_height='L2M' and coverage_area='CHN' and publish_time='20240416080000' and valid_time='02400-00000'`,
				cql_filter: `product_category='${category}' and product_element='${element}' and geo_height='${height}' and coverage_area='CHN' and publish_time='${publish_time}' and valid_time='${valid_time}'`,
				opacity: opacity,
			})
			this.rootLayerGroup.addLayer(wmsLayer)
			this.layerList.push({
				id: layerId,
				layer: wmsLayer,
			})
		},
		/**
		 * 加载GeoTiff图层
		 * @param {String} layerId 图层id
		 * @param {String} dataUrl 数据地址
		 * @return {*}
		 */
		async loadGeoTiffLayer(layerId, dataUrl) {
			this.removeLayer(layerId)
			const geotiffLayer = await this.renderTIFF(dataUrl)
			this.rootLayerGroup.addLayer(geotiffLayer)
			this.layerList.push({
				id: layerId,
				layer: geotiffLayer,
			})
		},
		/**
		 * @description: 渲染tif
		 * @param {*} url
		 * @return {*}
		 */
		async renderTIFF(url) {
			const response = await fetch(url)
			const arrayBuffer = await response.arrayBuffer()
			// console.log(arrayBuffer)
			const tiff = await fromArrayBuffer(arrayBuffer)
			const img = await tiff.getImage()
			const data = await img.readRasters()
			const bounds = img.getBoundingBox()
			const canvas = document.createElement('canvas')
			canvas.width = img.getWidth()
			canvas.height = img.getHeight()
			const arr = data[0]
			let max = arr[0],
				min = arr[0]
			arr.forEach((item) => (max = item > max ? item : max))
			arr.forEach((item) => (min = item < min ? item : min))
			console.log('🚀 ~ renderTIFF ~ max:', max)
			console.log('🚀 ~ renderTIFF ~ min:', min)
			const plot = new plotty.plot({
				canvas,
				data: data[0],
				width: img.getWidth(),
				height: img.getHeight(),
				domain: [min, max - 10],
				colorScale: 'diverging_2',
				applyDisplayRange: false,
				clampLow: false,
				clampHigh: false,
				// noDataValue: max
			})
			plot.render({
				// noDataValueColor: 'transparent' // 设置透明色
			})
			let b64 = canvas.toDataURL('image/png')
			return L.imageOverlay(
				b64,
				[
					[bounds[1], bounds[0]],
					[bounds[3], bounds[2]],
				],
				{
					opacity: 0.5,
				},
			)
		},
		/**
		 * @description: 加载风场图层
		 * @param {*} layerId 图层id
		 * @param {*} dataUrl 数据地址
		 * @return {*}
		 */
		async loadWindLayer(layerId, dataUrl) {
			const response = await fetch(dataUrl)
			// console.log(response)
			if (response.status == 200) {
				const data = await response.json()
				this.removeLayer(layerId)
				const windLayer = L.velocityLayer({
					// displayValues: true,
					// displayOptions: {
					//   velocityType: "Global Wind",
					//   position: "bottomleft",
					//   emptyString: "No wind data",
					// },
					data: data,
					// maxVelocity: 15,
				})
				this.rootLayerGroup.addLayer(windLayer)
				this.layerList.push({
					id: layerId,
					layer: windLayer,
				})
			} else {
				console.error('无风场数据！！！')
			}
		},
		/**
		 * @description: 叠加图片图层
		 * @param {*} layerId 图层id
		 * @param {*} imageUrl 图片地址
		 * @param {*} imageBounds 图片经纬度范围 [[10, 70],[54, 140]]
		 * @return {*}
		 */
		loadImageLayer(layerId, imageUrl, imageBounds) {
			this.removeLayer(layerId)
			const imgLayer = L.imageOverlay(imageUrl, imageBounds)
			this.rootLayerGroup.addLayer(imgLayer)
			this.layerList.push({
				id: layerId,
				layer: imgLayer,
			})
		},
		/**
		 * @description: 加载作业中心功能图层（日期地点弹出层、行军路线层）
		 * @param {*} layerId 图层id
		 * @param {*} data [{name: '保定', date: '2024-01-01', latlon: [38.8780, 115.4905]}, ...]
		 * @return {*}
		 */
		loadZyzxLayer(layerId, data) {
			this.removeLayer(layerId)
			let layer = new L.featureGroup()
			this.rootLayerGroup.addLayer(layer)
			this.layerList.push({
				id: layerId,
				layer: layer,
			})

			for (let i = 0; i < data.length; i++) {
				let marker = L.marker(data[i].latlon, {
					icon: L.divIcon({
						className: 'custom-div-icon',
						html: `
                    <div class="marker-zyzx" style="background-image: url('./imgs/poi/zyzx.png')"><div>${data[i].name}</div><div>${data[i].date}</div></div>
                  `,
						iconSize: [155, 145], // 设置图标尺寸
						iconAnchor: [30, 140], // 设置图标的锚点，使得图标正好在点的位置上
					}),
				}).on('click', () => {
					console.log('i', data[i])
					if (data[i]) {
						this.$emit('poiClick', layerId, data[i])
					}
				})
				layer.addLayer(marker)
				if (i < data.length - 1) {
					// let line = L.polyline([data[i].latlon, data[i + 1].latlon], {
					//   color: '#f00',
					//   draggable: 'true',
					//   weight: 8,
					//   opacity: 0.5
					// })
					// let arrow = L.polylineDecorator(line, {
					//   patterns: [
					//     {
					//       offset: '100%',
					//       repeat: 10,
					//       symbol: L.Symbol.arrowHead({
					//         pixelSize: 25,
					//         pathOptions: { color: '#f00', fillOpacity: 1, weight: 2 }
					//       })
					//     }
					//   ]
					// })
					// layer.addLayer(line)
					// layer.addLayer(arrow)

					// 新样式
					let line = L.Plot.fineArrow([data[i].latlon, data[i + 1].latlon], {
						color: '#f00',
						tailWidthFactor: 0.02,
						neckWidthFactor: 0.05,
						headWidthFactor: 0.08,
					})
					layer.addLayer(line)
				}
			}
			// const lineArr = data.map(item => {
			//   return item.latlon
			// })
		},
		/**
		 * 地图缩放
		 */
		onMapZoomend() {
			this.map.on('zoomend', () => {
				var zoomLevel = this.map.getZoom()
				// console.log('zoomLevel', zoomLevel)
				// console.log('Bounds', this.map.getBounds())
				let bounds = this.map.getBounds()

				console.log('aaaaaaaaaaaaaaaa')
				this.removeLayer(this.currentLayerId)
				this.drawSstsPoiMarker(bounds, this.currentLayerId) //调用4marker标点方法
			})
		},
		/**
		 * 绘制大数据量聚合点
		 * @param {聚合点数据 数组类型[{lon: '', lat: '', props: {相关属性值}}, ...]} data
		 * @param {打点图标} iconUrl
		 * @param {打点图标大小} iconSize
		 * @param {聚合样式} clusterStyle
		 */
		drawBigDataClusterPoint(data, iconUrl, iconSize, clusterStyle, layerId, multiIcon = false) {
			this.currentLayerId = layerId
			console.log('layerId', layerId)
			this.removeLayer(layerId)
			let defaultIcon = L.icon({
				iconUrl: iconUrl,
				iconSize: iconSize,
			})

			let clusterComponents = []
			for (let i = 0; i < data.length; i++) {
				clusterComponents.push({
					type: 'Feature',
					geometry: {
						type: 'Point',
						coordinates: [Number(data[i][3]), Number(data[i][4])],
					},
					properties: {
						...data[i],
					},
				})
			}
			// console.log(clusterComponents)

			clusterMarkers = L.geoJson(null, {
				pointToLayer: createClusterIcon,
			}).addTo(this.map)

			clusterIndex = new Supercluster({
				radius: 60,
				extent: 256,
				minZoom: 3,
				// maxZoom: 18
			}).load(clusterComponents)

			var that = this
			clusterMarkers.on('click', function(e) {
				var clusterId = e.layer.feature.properties.cluster_id
				var center = e.latlng
				var expansionZoom
				if (clusterId) {
					expansionZoom = clusterIndex.getClusterExpansionZoom(clusterId)
					that.map.flyTo(center, expansionZoom)
				}
			})

			this.updateCluster()

			function createClusterIcon(feature, latlng) {
				if (!feature.properties.cluster) {
					if (multiIcon) {
						console.log('feature.properties', feature.properties[1] == 0)
						console.log('feature.properties', feature.properties[2] == 0)
						defaultIcon = L.icon({
							iconUrl:
								feature.properties[1] == 0
									? require('@/assets/mskx/icon_nj_b3.png')
									: feature.properties[2] == 0
									? require('@/assets/mskx/icon_nj_b2.png')
									: require('@/assets/mskx/icon_nj_b.png'), //根据feature.properties的里面的字段写判断逻辑，传不同的图标
							iconSize: iconSize,
						})
					}
					return L.marker(latlng, {
						icon: defaultIcon,
					}).on('click', () => {
						console.log('聚合点击', layerId)
						console.log('聚合点击', feature.properties)
						that.$emit('poiClick', layerId, feature.properties)
					})
				}

				const count = feature.properties.point_count
				// const size = count < 100 ? 'small' : count < 1000 ? 'medium' : 'large'
				// const icon = L.divIcon({
				//   html: `<div><span>${feature.properties.point_count_abbreviated}</span></div>`,
				//   className: `marker-cluster marker-cluster-${size}`,
				//   iconSize: L.point(40, 40)
				// })
				let icon
				if (count >= 100) {
					icon = L.divIcon({
						html:
							`<div style="background-image: url(${clusterStyle.largeIconUrl}) "><span>` +
							feature.properties.point_count_abbreviated +
							'</span></div>',
						className: 'custom-cluster custom-cluster-large',
						iconSize: new L.Point(50, 50),
					})
				} else if (count >= 10 && count < 100) {
					icon = L.divIcon({
						html:
							`<div style="background-image: url(${clusterStyle.mediumIconUrl}) "><span>` +
							feature.properties.point_count_abbreviated +
							'</span></div>',
						className: 'custom-cluster custom-cluster-medium',
						iconSize: new L.Point(40, 40),
					})
				} else {
					icon = L.divIcon({
						html:
							`<div style="background-image: url(${clusterStyle.smallIconUrl}) "><span>` +
							feature.properties.point_count_abbreviated +
							'</span></div>',
						className: 'custom-cluster custom-cluster-small',
						iconSize: new L.Point(30, 30),
					})
				}

				return L.marker(latlng, { icon })
			}
		},

		/**
		 * 绘制大数据量聚合点
		 * @param {聚合点数据 数组类型[{lon: '', lat: '', props: {相关属性值}}, ...]} data
		 * @param {打点图标} iconUrl
		 * @param {打点图标大小} iconSize
		 * @param {聚合样式} clusterStyle
		 */
		drawSstsBigDataClusterPoint(data, iconUrl, iconSize, clusterStyle, layerId, multiIcon = false) {
			this.currentLayerId = layerId
			console.log('layerId', layerId)
			this.removeLayer(layerId)
			// let defaultIcon = L.icon({
			// 	iconUrl: iconUrl,
			// 	iconSize: iconSize,
			// })
			console.log('this.markerSourceData[layerId]--==', data)
			this.markerSourceData[layerId] = data
			this.isClickGrid = false
			if (data.length == 0) {
				return
			}
			let defaultIcon = L.icon({
				iconUrl: clusterStyle.smallIconUrl,
				iconSize: [12, 12],
				iconAnchor: [6, 6],
			})

			let clusterComponents = []
			console.log('data--=-=', data)
			for (let i = 0; i < data.length; i++) {
				clusterComponents.push({
					type: 'Feature',
					geometry: {
						type: 'Point',
						coordinates: [Number(data[i].latlng[1]), Number(data[i].latlng[0])],
					},
					properties: {
						...data[i],
					},
				})
			}
			// console.log(clusterComponents)

			clusterMarkers = L.geoJson(null, {
				pointToLayer: createClusterIcon,
			}).addTo(this.map)

			// clusterIndex = new Supercluster({
			// 	radius: 60,
			// 	extent: 256,
			// 	minZoom: 3,
			// 	// maxZoom: 18
			// }).load(clusterComponents)

			clusterIndex = new Supercluster({
				radius: 40,
				extent: 2048,
				minZoom: 3,
				// maxZoom: 18
			}).load(clusterComponents)

			var that = this
			clusterMarkers.on('click', function(e) {
				var clusterId = e.layer.feature.properties.cluster_id
				var center = e.latlng
				var expansionZoom
				if (clusterId) {
					expansionZoom = clusterIndex.getClusterExpansionZoom(clusterId)
					that.map.flyTo(center, expansionZoom)
				}
			})

			this.updateCluster()

			function createClusterIcon(feature, latlng) {
				console.log('feature.properties.cluster--==', feature.properties.cluster)
				if (!feature.properties.cluster) {
					if (multiIcon) {
						console.log('feature.properties', feature.properties[1] == 0)
						console.log('feature.properties', feature.properties[2] == 0)
						// defaultIcon = L.icon({
						// 	iconUrl:
						// 		feature.properties[1] == 0
						// 			? require('@/assets/mskx/icon_nj_b3.png')
						// 			: feature.properties[2] == 0
						// 			? require('@/assets/mskx/icon_nj_b2.png')
						// 			: require('@/assets/mskx/icon_nj_b.png'), //根据feature.properties的里面的字段写判断逻辑，传不同的图标
						// 	iconSize: iconSize,
						// })
					}
					return L.marker(latlng, {
						icon: defaultIcon,
					}).on('click', () => {
						console.log('聚合点击', layerId)
						console.log('聚合点击', feature.properties)
						that.$emit('poiClick', layerId, feature.properties)
					})
				}

				const count = feature.properties.point_count
				// const size = count < 100 ? 'small' : count < 1000 ? 'medium' : 'large'
				// const icon = L.divIcon({
				//   html: `<div><span>${feature.properties.point_count_abbreviated}</span></div>`,
				//   className: `marker-cluster marker-cluster-${size}`,
				//   iconSize: L.point(40, 40)
				// })
				let icon
				// if (count >= 100) {
				// 	icon = L.divIcon({
				// 		html:
				// 			`<div style="background-image: url(${clusterStyle.largeIconUrl}) "><span>` +
				// 			feature.properties.point_count_abbreviated +
				// 			'</span></div>',
				// 		className: 'custom-cluster custom-cluster-large',
				// 		iconSize: new L.Point(50, 50),
				// 	})
				// } else if (count >= 10 && count < 100) {
				// 	icon = L.divIcon({
				// 		html:
				// 			`<div style="background-image: url(${clusterStyle.mediumIconUrl}) "><span>` +
				// 			feature.properties.point_count_abbreviated +
				// 			'</span></div>',
				// 		className: 'custom-cluster custom-cluster-medium',
				// 		iconSize: new L.Point(40, 40),
				// 	})
				// } else {
				// 	icon = L.divIcon({
				// 		html:
				// 			`<div style="background-image: url(${clusterStyle.smallIconUrl}) "><span>` +
				// 			feature.properties.point_count_abbreviated +
				// 			'</span></div>',
				// 		className: 'custom-cluster custom-cluster-small',
				// 		iconSize: new L.Point(30, 30),
				// 	})
				// }

				// icon = L.divIcon({
				// 	html: `<div style="background-image: url(${clusterStyle.smallIconUrl}) "></div>`,
				// 	className: 'custom-cluster custom-cluster-small',
				// 	iconSize: new L.Point(30, 30),
				// })
				// iconSize: [16, 16],
				// iconAnchor: [8, 8],
				icon = L.divIcon({
					html: `<div class="custom-icon-box" style="background-image: url(${clusterStyle.smallIconUrl}) ;width:12px;height: 12px"></div>`,
					className: 'custom-cluster custom-cluster-small',
					// iconSize: new L.Point(30, 30),
					iconSize: new L.Point(12, 12),
				})

				return L.marker(latlng, { icon })
			}
		},
		/**
		 * 更新聚合点
		 */
		updateCluster() {
			// console.log('update')
			const bounds = this.map.getBounds()
			const bbox = [bounds.getWest(), bounds.getSouth(), bounds.getEast(), bounds.getNorth()]
			let zoom = this.map.getZoom()
			// console.log('bbox', bbox)
			// console.log('zoom', zoom)
			// if (zoom === 5) zoom += 0.5
			let clusters = null
			if (clusterIndex) {
				clusters = clusterIndex.getClusters(bbox, zoom)
			}
			if (clusterMarkers) {
				clusterMarkers.clearLayers()
				clusterMarkers.addData(clusters)
			}
		},
		/**
		 * 清除聚合点
		 */
		clearCluster() {
			if (clusterMarkers) {
				clusterMarkers.clearLayers()
				clusterMarkers = null
			}
		},
		/**
		 * 绘制poi点
		 * @param {poi点数据 数组类型} data
		 * @param {图层id} layerId
		 * @param {是否聚合} cluster
		 * @param {是否跳转第一个点} flyFirstPoint
		 */
		drawPoiMarker(data, layerId, cluster = false, flyFirstPoint = false, iconShow = false) {
			this.removeLayer(layerId)
			// 设置poi点图层
			let markerLayer
			if (cluster) {
				markerLayer = L.markerClusterGroup({
					showCoverageOnHover: false,
				})
			} else {
				markerLayer = L.featureGroup()
			}

			// 保存图层方便根据id删除
			this.layerList.push({
				id: layerId,
				layer: markerLayer,
			})
			// 处理点数据
			const markers = data
				.filter((it) => {
					if (it == null) {
						return false
					}
					return true
				})
				.map((it) => {
					// 初始化点 坐标、图标
					if (!iconShow) {
						const marker = L.marker(it.latlng, {
							icon: L.icon(it.icon),
						})
						// 绑定点击事件
						marker.on('click', () => {
							if (it.props) {
								// 可根据传入的props属性去判断点击的是哪类点
								this.$emit('poiClick', layerId, it)
							}
						})
						return marker
					} else {
						// 创建divIcon来作为标记图标，同时添加图片和文字标签
						const divIcon = L.divIcon({
							className: 'custom-div-icon',
							html: `
                    <div class="marker-icon" style="background-image: url('${it.icon.iconUrl}')"><div>${it.props.name}</div></div>
                  `,
							iconSize: [178, 69], // 设置图标尺寸
							iconAnchor: [89, 89], // 设置图标的锚点，使得图标正好在点的位置上
						})

						// 创建标记
						const marker = L.marker(it.latlng, { icon: divIcon })

						// 绑定点击事件
						marker.on('click', () => {
							if (it.props) {
								// 可根据传入的props属性去判断点击的是哪类点
								this.$emit('poiClick', layerId, it)
							}
						})
						return marker
					}
				})
			// 点图层添加点
			markers.forEach((p) => {
				markerLayer.addLayer(p)
			})
			// 将markerLayer添加到根图层
			this.rootLayerGroup.addLayer(markerLayer)
			// 视角跳转第一个点
			if (flyFirstPoint) {
				this.flyToPoint(markers[0].getLatLng(), 16)
			}
		},
		/**
		 * 绘制poi散点/农机位置点
		 * @param {poi点数据 数组类型} data
		 * @param {图层id} layerId
		 * @param {是否聚合} cluster
		 * @param {是否跳转第一个点} flyFirstPoint
		 */
		drawSstsPoiMarker(data, layerId, cluster = false, flyFirstPoint = false, iconShow = false) {
			if (!layerId) {
				return
			}
			this.currentLayerId = layerId
			this.removeLayer(layerId)
			this.clearCluster()
			const that = this
			if (data.length == 0) {
				return
			} else {
			}
			this.isClickGrid = false
			that.markerSourceData[layerId] = data
			that.sstsMarkers[layerId] = [] // 存放海量点的数组
			if (that.map.getZoom() >= 10) {
				data.map((point) => {
					var myIcon1 =
						// L.divIcon({
						// 	html: `<div style="background: green;border-radius: 50%;" ><span>` + '' + '</span></div>',
						// 	className: 'custom-div-icon',
						// 	iconSize: [178, 69], // 设置图标尺寸
						// 	iconAnchor: [89, 89], // 设置图标的锚点，使得图标正好在点的位置上
						// })

						L.icon({
							iconUrl: require('@/assets/mskx/icon_nj_b.png'),
							iconSize: [50, 60],
							iconAnchor: [25, 30],
							// iconAnchor: [25,60],
						})
					let market = L.marker(point.latlng, { icon: myIcon1, data: point, zIndex: 999 })

					that.sstsMarkers[layerId].push(market)
				})
			} else if (that.map.getZoom() <= 9) {
				// data.map((point) => {
				// 	var myIcon1 = L.icon({
				// 		iconUrl: './imgs/cluster/nj3.png',
				// 		iconSize: [8, 8],
				// 		iconAnchor: [4, 4],
				// 	})
				// 	let market = L.marker(point.latlng, { icon: myIcon1, title: '', alt: '', zIndex: 999 })
				// 	market.on({ click: (e) => {} })
				// 	that.sstsMarkers[layerId].push(market)
				// })
			}
			that.canvasIconLayer_.addLayers(that.sstsMarkers[layerId]) //海量点标点
			that.layerList.push({
				id: layerId,
				type: 'canvasIconLayer',
				layer: that.sstsMarkers[layerId],
			})

			// 将markerLayer添加到根图层
			// this.rootLayerGroup.addLayer(that.canvasIconLayer_)
		},
		/**
		 * 绘制信息框
		 */
		InformationMarker(list) {
			for (var i = 0; i < list.length; i++) {
				let shjd = list[i].shjd + '%'
				let marker1 = L.marker([list[i].lat, list[i].lng], {
					opacity: 0,
				})
					.addTo(this.map)
					.bindPopup(shjd, {
						autoClose: false,
						closeOnClick: false,
						offset: [0, 30],
					})
					.openPopup()
				marker1.off('click')
				this.marker1.push(marker1)
			}
		},
		/**
		 * 关闭信息框
		 */
		CloseMarker() {
			for (var i = 0; i < this.marker1.length; i++) {
				this.marker1[i].closePopup()
			}
		},
		/**
		 * 绘制poi点(自定义聚合样式)
		 * @param {poi点数据 数组类型} data
		 * @param {图层id} layerId
		 * @param {是否聚合} cluster
		 * @param {是否跳转第一个点} flyFirstPoint
		 * @param {自定义聚合样式} clusterStyle
		 */
		drawPoiMarkerCustomCluster(data, layerId, cluster = false, flyFirstPoint = false, iconShow = false, clusterStyle) {
			this.removeLayer(layerId)
			// 设置poi点图层
			let markerLayer
			if (cluster) {
				markerLayer = L.markerClusterGroup({
					iconCreateFunction: function(cluster) {
						var cnt = cluster.getChildCount()
						if (cnt >= 100) {
							return L.divIcon({
								html:
									`<div style="background-image: url(${clusterStyle.largeIconUrl}) "><span>` +
									cluster.getChildCount() +
									'</span></div>',
								className: 'custom-cluster custom-cluster-large',
								iconSize: new L.Point(50, 50),
							})
						} else if (cnt >= 10 && cnt < 100) {
							return L.divIcon({
								html:
									`<div style="background-image: url(${clusterStyle.mediumIconUrl}) "><span>` +
									cluster.getChildCount() +
									'</span></div>',
								className: 'custom-cluster custom-cluster-medium',
								iconSize: new L.Point(40, 40),
							})
						} else {
							return L.divIcon({
								html:
									`<div style="background-image: url(${clusterStyle.smallIconUrl}) "><span>` +
									cluster.getChildCount() +
									'</span></div>',
								className: 'custom-cluster custom-cluster-small',
								iconSize: new L.Point(30, 30),
							})
						}
					},
					showCoverageOnHover: false,
				})
			} else {
				markerLayer = L.featureGroup()
			}

			// 保存图层方便根据id删除
			this.layerList.push({
				id: layerId,
				layer: markerLayer,
			})
			// 处理点数据
			const markers = data
				.filter((it) => {
					if (it == null) {
						return false
					}
					return true
				})
				.map((it) => {
					// 初始化点 坐标、图标
					if (!iconShow) {
						const marker = L.marker(it.latlng, {
							icon: L.icon(it.icon),
						})
						// 绑定点击事件
						marker.on('click', () => {
							if (it.props) {
								// 可根据传入的props属性去判断点击的是哪类点
								this.$emit('poiClick', layerId, it)
							}
						})
						return marker
					} else {
						// 创建divIcon来作为标记图标，同时添加图片和文字标签
						const divIcon = L.divIcon({
							className: 'custom-div-icon',
							html: `
                    <div class="marker-icon" style="background-image: url('${it.icon.iconUrl}')"><div>${it.props.name}</div></div>
                  `,
							iconSize: [178, 69], // 设置图标尺寸
							iconAnchor: [89, 89], // 设置图标的锚点，使得图标正好在点的位置上
						})

						// 创建标记
						const marker = L.marker(it.latlng, { icon: divIcon })

						// 绑定点击事件
						marker.on('click', () => {
							if (it.props) {
								// 可根据传入的props属性去判断点击的是哪类点
								this.$emit('poiClick', layerId, it)
							}
						})
						return marker
					}
				})
			// 点图层添加点
			markers.forEach((p) => {
				markerLayer.addLayer(p)
			})
			// 将markerLayer添加到根图层
			this.rootLayerGroup.addLayer(markerLayer)
			// 视角跳转第一个点
			if (flyFirstPoint) {
				this.flyToPoint(markers[0].getLatLng(), 16)
			}
		},
		/**
		 * 绘制数字点位
		 * @param {*} data
		 * @param {*} layerId
		 */
		drawNumberMarker(data, layerId) {
			this.removeLayer(layerId)
			let numMarkerLayer = L.featureGroup()
			this.layerList.push({
				id: layerId,
				layer: numMarkerLayer,
			})
			const numMarkers = data.map((it) => {
				var customIcon = L.divIcon({
					className: 'custom-icon',
					html: `<div class="number-marker-out"><div class="number-marker-in">${it.props.num}</div></div>`,
					iconSize: [50, 50],
				})
				const marker = L.marker(it.latlng, { icon: customIcon })
				marker.on('click', () => {
					console.log('it--==', it)
					if (it.props) {
						// 可根据传入的props属性去判断点击的是哪类点
						console.log('数字点位', it.props)
						// this.$emit('numMarkerClick', layerId, it)
						this.$emit('poiClick', layerId, it)
					}
				})
				return marker
			})
			numMarkers.forEach((p) => {
				numMarkerLayer.addLayer(p)
			})
			this.rootLayerGroup.addLayer(numMarkerLayer)
		},

		/**
		 * 使用各省份数字数据绘制数字点位
		 * @param {*} data
		 * @param {*} layerId
		 */
		drawSstsNumberMarker(data, layerId) {
			if (!layerId) {
				return
			}
			this.currentLayerId = layerId
			const that = this
			that.removeLayer(layerId)
			this.clearCluster()
			that.markerSourceData[layerId] = data
			that.sstsMarkers[layerId] = [] // 存放海量点的数组
			if (data.length == 0) {
				return
			} else {
			}
			that.isClickGrid = false
			let numMarkerLayer = L.featureGroup()

			// data.map((point) => {
			// 	var myIcon1 = L.divIcon({
			// 		className: 'custom-icon',
			// 		iconUrl: `<div class="number-marker-out"><div class="number-marker-in">${point.props.num}</div></div>`,
			// 		// html: `<div class="number-marker-out"><div class="number-marker-in">${point.props.num}</div></div>`,
			// 		iconSize: [50, 50],
			// 	})
			// 	let market = L.marker(point.latlng, { icon: myIcon1, data: point })

			// 	that.sstsMarkers[layerId].push(market)
			// })
			// console.log('that.sstsMarkers[layerId]--==', that.sstsMarkers[layerId])
			// that.canvasIconLayer_.addLayers(that.sstsMarkers[layerId]) //海量点标点
			// that.layerList.push({
			// 	id: layerId,
			// 	type: 'canvasIconLayer',
			// 	layer: that.sstsMarkers[layerId],
			// })
			const numMarkers = data.map((it) => {
				var customIcon = L.divIcon({
					className: 'custom-icon',
					html: `<div class="number-marker-out"><div class="number-marker-in">${it.props.num}</div></div>`,
					iconSize: [50, 50],
				})
				const marker = L.marker(it.latlng, { icon: customIcon })
				marker.on('click', () => {
					if (it.props) {
						// 可根据传入的props属性去判断点击的是哪类点
						//点击了数字点位
						console.log('it--==', it)
						that.gridClick(it.props)
						
					}
				})
				return marker
			})
			numMarkers.forEach((p) => {
				numMarkerLayer.addLayer(p)
			})
			that.rootLayerGroup.addLayer(numMarkerLayer)
			that.layerList.push({
				id: layerId,
				layer: numMarkerLayer,
			})
		},

		/**
		 * 根据图层id移除图层
		 * @param {*} layerId
		 */
		removeLayer(layerId) {
			const layer = this.layerList.find((e) => e.id == layerId)
			if (layer) {
				console.log('removeLayer layer', layer)
				if (!layer.type) {
					this.rootLayerGroup.removeLayer(layer.layer)
					this.layerList = this.layerList.filter((e) => e.id != layerId)
					console.log('removeLayer执行了')
					console.log('removeLayer this.layerList', this.layerList)
				} else if (layer.type == 'canvasIconLayer') {
					this.layerList = this.layerList.filter((e) => e.id != layerId)
					if (!this.sstsMarkers[layerId]) {
					} else {
						//在标点前，先清空已有标点。
						if (this.sstsMarkers[layerId].length > 0) {
							this.canvasIconLayer_.removeLayers(this.sstsMarkers[layerId])
						}
					}
					// this.rootLayerGroup.clearLayers()
				}
				console.log('removeResult layer', this.layerList)
			}
		},
		/**
		 * 移除所有图层
		 */
		clearLayers() {
			this.rootLayerGroup.clearLayers()
			this.layerList = []
			if (this.trackLayer) {
				this.trackLayer.clearLayers()
			}

			this.clearCluster()
			this.removeDaynamicCicle()
			this.removeLayer('trcak')
			//清除散点
			if (this.sstsMarkers[this.currentLayerId].length > 0) {
				this.canvasIconLayer_.removeLayers(this.sstsMarkers[this.currentLayerId])
			}
			this.sstsMarkers[this.currentLayerId] = []
			this.markerSourceData[this.currentLayerId] = []
			this.removeCicleList()
			this.$emit('qctc', false)
		},
		/**
		 * @description: 生成geojson
		 * @param {*} coordinates
		 * @param {*} properties
		 * @return {*}
		 */
		setGeojson(coordinates, properties) {
			// coordinates = coordinates.map(e => {
			//   console.log('saas', e);
			// });
			var geojson = {
				type: 'FeatureCollection',
				features: [
					{
						type: 'Feature',
						geometry: {
							type: 'MultiPolygon',
							coordinates: coordinates,
						},
						properties: properties,
					},
				],
			}
			// console.log('geojson', geojson);
			return geojson
		},
		/**
		 * @description: 加载geojson数据
		 * @param {*} data
		 * @param {*} layerId
		 * @return {*}
		 */
		loadGeoJsonLayer(data, layerId) {
			this.removeLayer(layerId)
			const geojsonLayer = L.geoJSON(data, {
				style: (feature) => {
					return { color: '#007FFF' }
				},
				pane: 'overlayPane',
				onEachFeature: (feature, layer) => {
					layer.on({
						click: () => {
							// alert(feature.properties.name)
							this.$emit('gridClick', feature.properties)
						},
					})

					layer.bindTooltip(`${feature.properties.name}`, { permanent: true, direction: 'center' }).openTooltip()
				},
			}).addTo(this.map)
			this.map.fitBounds(geojsonLayer.getBounds())
			this.bounds = this.getBBox(geojsonLayer.getBounds())
			this.layerList.push({
				id: layerId,
				layer: geojsonLayer,
			})
			this.rootLayerGroup.addLayer(geojsonLayer)
		},
		backLayers() {
			console.log('backLayers this.lastAreaCode', this.lastAreaCode)
			if (this.lastAreaCode.length > 2) {
				this.$emit('operate', this.lastAreaCode[this.lastAreaCode.length - 2], this.lastAreaCode[this.lastAreaCode.length - 1])

				if (this.lastAreaCode[3] && this.lastAreaCode[2] !== this.lastAreaCode[3]) {
					this.lastAreaCode.splice(3)
				} else {
					this.lastAreaCode.splice(2)
				}
				this.loadGeoJsonLayerFromUrl(DataVUrl + this.lastAreaCode[this.lastAreaCode.length - 1] + '_full.json', 'geojson', false)

				if (this.lastAreaCode.length == 2) {
					this.currentMapLevel = 'province'
				}
				if (this.lastAreaCode.length == 3) {
					this.currentMapLevel = 'city'
				}
			} else if (this.lastAreaCode.length == 2) {
				this.loadGeoJsonLayerFromUrl(DataVUrl + this.lastAreaCode[this.lastAreaCode.length - 2] + '_full.json', 'geojson', false)
				this.map.setView(this.mapOptions.center, this.mapOptions.zoom)
				this.$nextTick(() => {
					this.$emit('operate', this.lastAreaCode[this.lastAreaCode.length - 2], this.lastAreaCode[this.lastAreaCode.length - 1])
				})
				this.lastAreaCode.splice(this.lastAreaCode.length - 1, 1)
				console.log('backLayers this.lastAreaCode--==', this.lastAreaCode)
				if (this.lastAreaCode.length == 1 && this.lastAreaCode[0] == 100000) {
					this.currentMapLevel = ''
				}
			} else {
				this.removeLayer('trcak')
				this.$message.warning('当前已经是最高一级啦！')
				this.currentMapLevel = ''
			}
		},
		async loadGeoJsonLayerFromUrl(dataUrl, layerId, isChina = false) {
			const response = await fetch(dataUrl)
			if (response.status == 200) {
				let data = await response.json()
				// console.log('🚀 ~ loadGeoJsonLayerFromUrl ~ data1:', data)
				gcoord.transform(data, gcoord.GCJ02, gcoord.WGS84)
				// console.log('🚀 ~ loadGeoJsonLayerFromUrl ~ data2:', data)
				// this.removeLayer(layerId)
				if (this.geojsonLayer) this.map.removeLayer(this.geojsonLayer)
				// this.loadMaskLayer(data)
				this.loadImageMaskLayer(data, './imgs/map-bg1.png')

				this.geojsonLayer = L.geoJSON(data, {
					style: (feature) => {
						return {
							color: 'transparent', // 隐藏边框颜色
							weight: 0, // 边框宽度为0
							fillOpacity: 0, // 填充色透明度为0
							zIndexOffset: 500,
						}
					},
					// pane: 'gcoordCustomPane', //控制层级顺序
					pane: 'overlayPane', //控制层级顺序

					onEachFeature: (feature, layer) => {
						layer.on({
							click: () => {
								// alert('行政编码：' + feature.properties.adcode)
								if (this.xzpd) {
									this.gridClick(feature.properties)
								}
							},
							mouseover: () => {
								layer.setStyle({
									color: '#007FFF',
									fillOpacity: 0.1,
									weight: 2,
								})

								if (!L.Browser.ie && !L.Browser.opera && !L.Browser.edge) {
									layer.bringToFront()
								}
							},
							mouseout: () => {
								this.geojsonLayer.resetStyle(layer)
							},
						})

						// layer
						//   .bindTooltip(`${feature.properties.name}`, { permanent: true, direction: 'center' })
						//   .openTooltip()
					},
				}).addTo(this.map)

				this.bounds = this.getBBox(this.geojsonLayer.getBounds())
				if (!isChina) {
					// console.log('当前层级', this.map.getZoom())
					this.map.fitBounds(this.geojsonLayer.getBounds())
				}
				// this.layerList.push({
				//   id: layerId,
				//   layer: geojsonLayer
				// })
				// this.rootLayerGroup.addLayer(geojsonLayer)
			} else {
				console.error('无geojson数据！！！')
			}
			// console.log('🚀 ~ loadGeoJsonLayerFromUrl ~ data:', data)
		},
		gridClick(data) {
			if (this.lastAreaCode.indexOf(data.adcode) == -1) {
				this.lastAreaCode.push(data.adcode)
			}
			if (this.map.getZoom() >= 10) {
			} else {
				clearTimeout(this.timerLoadGeoJsonLayer)
				this.timerLoadGeoJsonLayer = setTimeout(async () => {
					this.currentMapLevel = data.level
					console.log('gridClick this.currentMapLevel', this.currentMapLevel)
					if (data.level === 'district') {
						this.loadGeoJsonLayerFromUrl(DataVUrl + data.adcode + '.json', 'geojson')
					} else {
						this.loadGeoJsonLayerFromUrl(DataVUrl + data.adcode + '_full.json', 'geojson')
					}
					this.isClickGrid = true
					this.$nextTick(() => {
						this.$emit('gridClick', data)
					})
				}, 500)
			}
		},

		loadMaskLayer(data) {
			if (this.maskLayer) this.map.removeLayer(this.maskLayer)
			this.maskLayer = L.mask(data, {
				map: this.map,
				// color: '#f10909',
				// weight: 8,
				fillColor: '#fff',
				fillOpacity: 1,
			}).addTo(this.map)
		},
		loadImageMaskLayer(data, imgUrl) {
			if (this.imageMaskLayer) this.map.removeLayer(this.imageMaskLayer)

			const geojson = L.geoJSON(data)
			this.imageMaskLayer = L.imageMask(imgUrl, {
				padding: 1,
				polygons: geojson.getLayers(),
				mode: 'clip', //clip:指定区域不显示背景图,show:指定区域显示背景图
			}).addTo(this.map)
		},
		/**
		 * 定位点
		 * @param {坐标点} position
		 * @param {层级} zoom
		 */
		flyToPoint(position, zoom) {
			this.map.flyTo(position, zoom)
		},
		/**
		 * @description: 获取边界坐标
		 * @return {*}
		 */
		getBBox(bounds) {
			return [bounds._southWest.lng, bounds._southWest.lat, bounds._northEast.lng, bounds._northEast.lat]
		},
		/**
		 * @description: 生成随机点
		 * @param {*} layerId 图层id
		 * @param {*} number 返回随机点数
		 * @param {*} img 点图标 require('@/assets/img/map/jz/jz.png')
		 * @return {*}
		 */
		randomPoint(layerId, number, img, size) {
			if (this.bounds.length === 0) {
				// alert('请选择区域范围');
				return false
			}
			var points = turf.randomPoint(number, { bbox: this.bounds })
			var features = points.features.map((e, index) => {
				let coordinates = e.geometry.coordinates.reverse()
				console.log(coordinates)
				// var latLng = L.CRS.EPSG3857.unproject(L.point(coordinates));
				let pointInfo = {
					latlng: coordinates,
					icon: {
						iconUrl: img,
						iconSize: size,
					},
					props: {
						name: '某某poi点',
						type: layerId,
						id: index,
					},
				}
				return pointInfo
			})
			// console.log('随机点：', features)
			this.drawPoiMarkerCustomCluster(features, layerId, true, false, false, {
				largeIconUrl: './imgs/cluster/large-icon.png',
				mediumIconUrl: './imgs/cluster/medium-icon.png',
				smallIconUrl: './imgs/cluster/small-icon.png',
			})

			// 单个点位
			// let features = {
			//   icon: {
			//     iconUrl: 'img/people.7eff8f7f.png',
			//     iconSize: [47, 90]
			//   },
			//   latlng: [31.80757463704106, 119.21717254828414],
			//   props: {
			//     name: '某某poi点',
			//     type: 'people',
			//     id: 1
			//   }
			// }
			// this.drawPoiMarker([features], layerId, true)
		},
		/**
		 * @description: 加载轨迹图层
		 * @param {*} layerId
		 * @param {*} data
		 * @return {*}
		 */
		async loadTrackLayer(layerId, data) {
			this.removeLayer(layerId)
			this.map.setView([data[0][1], data[0][0]], 16)
			const lineLayer = L.layerGroup()
			this.rootLayerGroup.addLayer(lineLayer)
			this.layerList.push({
				id: layerId,
				layer: lineLayer,
			})
			const latents = data.map((e) => {
				if (isNaN(e[0]) || isNaN(e[1])) {
				} else {
					return [e[1], e[0]]
				}
			})

			//修改轨迹线
			const routeLine = L.polyline(latents, {
				weight: 20,
				color: 'rgb(6,229,255)',
				opacity: 0.6,
			})
			lineLayer.addLayer(routeLine)
			const realRouteLine = L.polyline([], {
				weight: 5,
				color: '#C9F928',
			})
			lineLayer.addLayer(realRouteLine)

			const carIcon = L.icon({
				// iconSize: [37, 26],
				iconSize: [24, 31],
				iconAnchor: [0, 0],
				iconUrl: './imgs/car.png',
				// iconUrl:require('@/assets/mskx/icon_nj_b.png')
			})
			// 动态marker
			let animatedMarker = L.animatedMarker(routeLine.getLatLngs(), {
				interval: 700,
				icon: carIcon,
				playCall: updateRealLine,
			})
			lineLayer.addLayer(animatedMarker)
			// const { lat, lng } = routeLine.getLatLngs()[0]
			// const newLatlngs = [
			// 	{
			// 		lat,
			// 		lng,
			// 	},
			// ]
			const newLatlngs = [routeLine.getLatLngs()[0]]

			// 绘制已行走轨迹线（橙色那条）
			function updateRealLine(latlng) {
				newLatlngs.push(latlng)
				realRouteLine.setLatLngs(newLatlngs)
			}

			setTimeout(() => {
				animatedMarker.start()
			}, 1000)
			/**
			 * 通过多边形覆盖 无用
			 */
			// 过滤无效的点，确保经纬度是有效的
			// if (latents.length < 3) {
			// 	alert('至少需要3个有效的点来计算凸包')
			// 	return
			// }
			// // 使用 Turf.js 计算凸包
			// const polygon_ = turf.convex(turf.featureCollection(latents.map((point) => turf.point(point))))
			// const { geometry } = polygon_
			// const { coordinates } = geometry
			// console.log('coordinates--==', coordinates)
			// L.polygon(coordinates, {stroke: false, color:'red' ,fillOpacity:0.7 }).addTo(this.map)
		},
		/**
		 * @description: 加载热力图
		 * @return {*}
		 */
		loadHeatLayer(layerId, heatDatas) {
			let heatData = heatDatas.filter((item) => !isNaN(Number(item.latitude)))
			this.removeLayer(layerId)
			let heatDataList = []
			if (heatData && heatData.length > 0) {
				heatDataList = heatData.map((e) => {
					return [e.latitude, e.longitude].concat(1)
				})
				console.log('热力', heatDataList)
				const heatLayer = L.heatLayer(heatDataList, {
					radius: 24,
					minOpacity: 0.2,
					gradient: {
						'0.2': '#0ff',
						'0.3': '#0f0',
						'0.5': '#ff0',
						'0.7': '#f63b05',
					},
				})
				this.rootLayerGroup.addLayer(heatLayer)
				this.layerList.push({
					id: layerId,
					layer: heatLayer,
				})
				// console.log('热力', heatLayer)
			}
		},

		// getPicType(type, date) {
		//   picTif({
		//     type: type,
		//     date: date
		//   }).then(res => {
		//     let picurl = res.content[0].picurl + '?key=e503e4a74a7d3a3063827ef324303bc6';
		//     console.log("picurl", picurl)
		//     if (type == 4091 || type == 4092 || type == 4093 || type == 4094 || type == 4095 || type == 4096 || type == 4097 || type == 4098) {
		//       this.loadGeoTiffLayer(
		//         'geotiff',
		//         picurl
		//       )
		//     } else if (type == 1318) {
		//       this.loadImageLayer('jy', picurl, [
		//         [0, 70],
		//         [60, 140]
		//       ])
		//     } else if (type == 1402) {
		//       this.loadImageLayer('jy2', picurl, [
		//         [10.1556, 73.4413],
		//         [53.5656, 135.0913]
		//       ])
		//     }
		//   })
		// },

		getPicType(type, date) {
			console.log('type', type)
			let params = {
				picType: type,
				startTime: this.dayjs(date)
					.subtract(1, 'days')
					.format('YYYYMMDD'),
				endTime: date,
				page_no: '1',
				page_size: '20',
			}
			if (type == '4094' || type == '4095' || type == '4096' || type == '4097') {
				walarmPic(params).then((res) => {
					if (res.code == 0) {
						this.loadGeoTiffLayer('geotiff', res.data[0].rows[0].picurl + '?key=e503e4a74a7d3a3063827ef324303bc6')
					}
				})
			} else if (type == '4091' || type == '4092' || type == '4093') {
				meteorologicalPic(params).then((res) => {
					if (res.code == 0) {
						this.loadGeoTiffLayer('geotiff', res.data[0].rows[0].picurl + '?key=e503e4a74a7d3a3063827ef324303bc6')
					}
				})
			} else if (type == 1318) {
				rainfallPic(params).then((res) => {
					if (res.code == 0) {
						this.loadImageLayer('jy', res.data[0].rows[0].picurl + '?key=e503e4a74a7d3a3063827ef324303bc6', [
							[0, 70],
							[60, 140],
						])
					}
				})
			} else if (type == 1402) {
				rainfallPic(params).then((res) => {
					if (res.code == 0) {
						this.loadImageLayer('jy2', res.data[0].rows[0].picurl + '?key=e503e4a74a7d3a3063827ef324303bc6', [
							[10.1556, 73.4413],
							[53.5656, 135.0913],
						])
					}
				})
			} else if (type == 'gz_windflowobs1h') {
				// this.loadWindLayer('wind', './json/gfvu-world-202403010800.json')
				params = {
					startTime: this.dayjs(date)
						.subtract(1, 'days')
						.format('YYYYMMDDhhmm'),
					endTime: this.dayjs(date).format('YYYYMMDDhhmm'),
					page_no: '1',
					page_size: '20',
				}
				windPic(params).then((res) => {
					if (res.code == 0) {
						extractFile({
							fileUrl: res.data[0].rows[0].picurl,
						}).then((res) => {
							console.log('res', res)
							this.removeLayer('wind')
							const windLayer = L.velocityLayer({
								data: res,
							})
							this.rootLayerGroup.addLayer(windLayer)
							this.layerList.push({
								id: 'wind',
								layer: windLayer,
							})
						})
						//this.loadWindLayer('wind', res.data[0].rows[0].picurl + '?key=e503e4a74a7d3a3063827ef324303bc6')
					}
				})
			}
		},

		showLayers(areaId, picType, date) {
			this.removeLayer('geotiff')
			this.removeLayer('jy')
			this.removeLayer('jy2')
			this.removeLayer('wind')
			this.$nextTick(() => {
				this.loadGeoJsonLayerFromUrl(DataVUrl + `${areaId || 100000}_full.json`, 'geojson', true)
				if (picType) {
					this.getPicType(picType, this.dayjs(date).format('YYYYMMDD'))
				}
			})
		},
	},
}
</script>

<style lang="less" scoped>
/deep/ .custom-cluster-small {
	// line-height: 12px !important;
}

/deep/ .custom-icon-box {
	background-size: cover;
	background-position: center center;
	background-repeat: no-repeat;
}

.custom-marker {
	z-index: 999;
	.custom-marker-icon {
		width: 50px;
		height: 60px;
		background-size: 100% 100%;
	}
}

.mapContainer {
	// width: 100%;
	// height: 100%;
	z-index: 0;
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0px;
	left: 0px;
}

.clear {
	z-index: 1;
	position: absolute;
	width: 44px;
	height: 44px;
	top: 151px;
	left: 1337px;
	background: url(~@/assets/map/icon3.png) no-repeat;
	cursor: pointer;
}
.clear2 {
	z-index: 1;
	position: absolute;
	width: 44px;
	height: 44px;
	top: 201px;
	left: 1337px;
	background: url(~@/assets/map/icon3.png) no-repeat;
	cursor: pointer;
}

.back {
	z-index: 1;
	position: absolute;
	width: 44px;
	height: 44px;
	top: 201px;
	left: 1337px;
	background: url(~@/assets/map/back_default.png) no-repeat;
	background-size: 100% 100%;
	cursor: pointer;
}
.back2 {
	z-index: 1;
	position: absolute;
	width: 44px;
	height: 44px;
	top: 251px;
	left: 1337px;
	background: url(~@/assets/map/back_default.png) no-repeat;
	background-size: 100% 100%;
	cursor: pointer;
}

// .back:hover {
//   background: url(~@/assets/map/back_active.png) no-repeat;
// }

.weather {
	z-index: 1;
	position: absolute;
	width: 44px;
	height: 44px;
	top: 801px;
	left: 1337px;
	background: url(~@/assets/map/icon3.png) no-repeat;
}

// 修改地图滤镜
// /deep/ .leaflet-zoom-animated img {
//   -webkit-filter: sepia(100%) invert(90%) !important;
//   -ms-filter: sepia(100%) invert(90%) !important;
//   -moz-filter: sepia(100%) invert(90%) !important;
//   filter: sepia(100%) invert(90%) !important;
// }

/deep/ .leaflet-tooltip {
	background-color: unset;
	color: #fff;
	text-shadow: -1px 0 #333, 0 1px #333, 1px 0 #333, 0 -1px #333;
	border: unset;
	box-shadow: unset;
	font-size: 16px;
	// font-weight: 800;
}

/deep/ .my-popup {
	.leaflet-popup-content-wrapper {
		box-shadow: unset;
		background: unset;
		color: unset;

		.leaflet-popup-content {
			width: unset !important;
			margin: 0px;
		}
	}

	.leaflet-popup-close-button {
		top: 16px;
		right: 1rem;
	}
}

/deep/ .iclient-leaflet-logo {
	display: none;
}

/deep/ .leaflet-measure-path-measurement {
	position: absolute;
	font-size: 10px;
	color: black;
	text-shadow: -1px 0 0 white, -1px -1px 0 white, 0 -1px 0 white, 1px -1px 0 white, 1px 0 0 white, 1px 1px 0 white, 0 1px 0 white,
		-1px 1px 0 white;
	white-space: nowrap;
	transform-origin: 0;
	pointer-events: none;
}

/deep/ .leaflet-measure-path-measurement > div {
	position: relative;
	margin-top: -25%;
	left: -50%;
}

/deep/ .zxnj-div-icon {
	display: flex;
	.zxnj-marker-icon {
		width: 12px;
		height: 12px;
		border-radius: 50%;
	}
}

/deep/ .custom-div-icon {
	display: flex;

	.marker-icon {
		color: #fff;
		width: 138px;
		height: 69px;
		background-size: 100% 100%;

		div {
			white-space: nowrap;
			margin: 18px 0 0 60px;
			font-size: 18px;
			font-family: FZSKJW--GB1-0, FZSKJW--GB1;
			font-weight: normal;
			color: #ffffff;
		}
	}
}
</style>
