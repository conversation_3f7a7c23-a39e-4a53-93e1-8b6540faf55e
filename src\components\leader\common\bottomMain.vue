<template>
  <div class='bottomMain'>
    <div class='bottomContent'></div>
  </div>
</template>

<script>
export default {
  props: {},
  name: 'bottom-main',
  data() {
    return {};
  },
  mounted() {
  },
  beforeDestroy() {
  },
  methods: {}
};
</script>

<style lang='scss' scoped>
.bottomMain {
  width: 100%;
  height: 18.6875rem;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 1;
  // background: url(~@/assets/img/header/bottom_bg_2.png) no-repeat;
  background-size: 100% 100%;

  .bottomContent {
    width: 100%;
    height: 4.0625rem;
    position: absolute;
    bottom: 0;
    left: 0;
    // background: url(~@/assets/img/header/bottom.png);
  }
}
</style>
