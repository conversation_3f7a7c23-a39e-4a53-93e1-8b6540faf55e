<template>
  <div class="middle_box">
    <div class="line">
      <div
        class="line_box"
        :style="{ marginRight: lineMarginLeft + 'px' }"
        v-for="(item, index) in middleList"
        :key="index"
      >
        <div class="num_">
          <!-- {{item.num}} -->
          <countTo
            ref="countTo"
            :startVal="$countTo.startVal"
            :decimals="$countTo.decimals(item.num)"
            :endVal="item.num"
            :duration="$countTo.duration"
          />
          <!-- <num-scroll :number='formatNumber(item.num)'></num-scroll> -->
          <span>{{ item.numUnit }}</span>
        </div>
        <div class="name_unit">
          <span class="name_">{{ item.name }}</span
          ><span class="unit_" v-if="item.nameUnit">({{ item.nameUnit }})</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import NumScroll from '@/components/leader/num_scroll';
export default {
  props: {
    middleList: {
      type: Array,
      default: () => [
        {
          name: 'GDP',
          numUnit: '',
          num: 430.5,
          nameUnit: '亿元',
        },
        {
          name: '户籍人口',
          numUnit: '',
          num: 32.6,
          nameUnit: '万人',
        },
        {
          name: '部件总数',
          numUnit: '',
          num: 97558,
          nameUnit: '件',
        },
        {
          name: '企业总数',
          numUnit: '',
          num: 2766,
          nameUnit: '个',
        },
        {
          name: '空气质量优良率',
          num: 99.5,
          numUnit: '%',
          nameUnit: '',
        },
      ],
    },
    lineMarginLeft: {
      type: Number,
      default: 13,
    },
  },
  components: {
    NumScroll,
  },
  data() {
    return {
      aaaa:'6000'
    }
  },
   computed: {
    formatNumber() {
      return function (param) {
        console.log(param)
        if (this.isEmpty(param) || param.length < 4 || isNaN(param)) {
          return param
        }

        let small = ''
        let isSmall = false
        if (param.split('.').length === 2) {
          // 有小数
          isSmall = true
          small = param.split('.')[1]
          param = param.split('.')[0]
        }

        let result = ''

        while (param.length > 3) {
          result = ',' + param.slice(-3) + result
          param = param.slice(0, param.length - 3)
        }

        if (param) {
          result = param + result
        }

        if (isSmall) {
          result = result + '.' + small
        }
        console.log(result)
        return result
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.middle_box {
  position: absolute;
  top: 164px;
  left: 532px;
  z-index: 999;
  .line {
    display: flex;
    justify-content: space-between;
    .line_box {
      width: 152px;
      height: 35px;
      background-size: 100% 100%;
      // &:not(:last-of-type) {
      //   margin-right: 59px;
      // }
      background: url(~@/assets/leader/img/component/middle_header/bg.png) no-repeat;
      background-size: 100% 100%;
      position: relative;
      .num_ {
        position: absolute;
        // bottom: 6px;
        left: 26px;
        font-size: 36px;
        font-family: DIN-BlackItalic, DIN;
        font-weight: normal;
        color: #ffffff;
        display: flex;
        top: -22px;
        // text-shadow: 0px 0px 1px rgba(0, 43, 133, 0.84);
        // background-clip: text;
        // -webkit-background-clip: text;
        // -webkit-text-fill-color: transparent;
        & span:last-of-type {
          font-size: 17px;
          line-height: 60px;
        }
      }
      .name_unit {
        position: absolute;
        top: 51px;
        left: 18px;
        white-space: nowrap;
        .name_ {
          font-size: 18px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #ffffff;
        }
        .unit_ {
          margin-left: 2px;
          font-size: 14px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
      }
    }
  }
}
</style>