<template>
  <div>
    <!-- <div class="meng_video" v-show="isUrlShow"></div> -->
    <video
      ref="myVideo"
      class="video-js vjs-default-skin vjs-big-play-centered vjs-fluid vjs-16-9"
      controls
      :playsinline="true"
      preload="auto"
    ></video>
  </div>
</template>

<script>
import Video from 'video.js'
import 'video.js/dist/video-js.css'
import { getCameraMakers, getCameraXq } from '@/api/hs/hs.api.js'

export default {
  name: 'hls-video',
  props: {
    // 请求路径
    src: {
      type: String,
      default: ''
    },
    // 视频再次请求id
    cameraCode: {
      type: String,
      default: ''
    },
    // 摄像流来源
    isSource: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isLoading: false,
      times: null,
      timespace: 15000,
      isUrlShow: true
    }
  },
  beforeDestroy() {
    if (this.player) {
      this.player.off('waiting')
      this.player.off('playing')
      this.player.off('error')
      // this.player.stop();
      this.player.dispose()
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initVideo()
    })
  },
  watch: {
    src(newSrc) {
      console.log('newSrc', newSrc)
      this.playFromSrc(newSrc)
      setTimeout(() => {
        this.isUrlShow = false
      }, 200)
    }
  },
  methods: {
    initVideo() {
      //初始化视频方法
      this.player = Video(this.$refs.myVideo, {
        //确定播放器是否具有用户可以与之交互的控件。没有控件，启动视频播放的唯一方法是使用autoplay属性或通过Player API。
        controls: true,
        //自动播放属性,muted:静音播放
        autoplay: 'muted',
        //建议浏览器是否应在<video>加载元素后立即开始下载视频数据。
        preload: 'auto',
        // disablePictureInPicture: true,  // 画中画
        loop: true //循环播放
      })
      this.player.on('error', this.onError)
      this.player.on('waiting', this.onWaitting)
      this.player.on('playing', this.onOpen)
      console.log('this.src', this.src)
      if (this.src) {
        this.playFromSrc(this.src)
        setTimeout(() => {
          this.isUrlShow = false
        }, 200)
      }
      // this.player.duration();
    },
    // 播放
    playFromSrc(src) {
      console.log('src', src)
      this.player.src({
        src,
        type: 'application/x-mpegURL'
      })
    },

    async onError() {
      setTimeout(() => {
        getCameraXq(this.cameraCode).then(res => {
          console.log('res', res)
          if (
            res &&
            res.body &&
            res.body.data &&
            res.body.data.length > 0 &&
            res.body.data[0].url
          ) {
            this.playFromSrc(res.body.data[0].url)
          }
        })
      }, 3000)
    },
    onWaitting() {
      // console.log("等待数据");
      if (!this.isLoading) {
        this.isLoading = true
        this.times = setTimeout(() => {
          this.onError()
        }, this.timespace)
      }
    },
    onOpen() {
      // console.log('视频播放中');
      // console.log('this.times', this.times);
      this.times && clearTimeout(this.times)
      this.isLoading = false
    }
  }
}
</script>

<style lang="less" scoped>
video {
  width: 100%;
  height: 100%;
  object-fit: fill;
}
.video-js.vjs-16-9:not(.vjs-audio-only-mode) {
  padding-top: 54.25%;
}

.meng_video {
  width: 98%;
  height: 80%;
  // background: #000;
  position: absolute;
  top: 16%;
  left: 1%;
  z-index: 999999;
}
/deep/ .video-js .vjs-control {
  width: 1.5rem !important;
}
/deep/ .video-js .vjs-time-control {
  display: none;
}
/deep/ .video-js .vjs-modal-dialog {
  overflow: hidden;
}
</style>
