<template>
  <chart class='charts' :autoresize='true' :option='options' :loading="loading"></chart>
</template>

<script setup>
import {ref, defineProps} from "vue"
// import 'echarts-wordcloud/dist/echarts-wordcloud'
import 'echarts-wordcloud/dist/echarts-wordcloud.min'
const loading = ref(true)
const props = defineProps({
    data:{
        type:Array,
        default:()=>[
            { name: "交通拥堵", value: 12 },
            { name: "疫苗", value: 14 },
            { name: "大学生活", value: 8 },
            { name: "社保查询", value: 20 },
            { name: "企业服务", value: 22 },
            { name: "疫情", value: 21 },
            { name: "就业服务", value: 20 },
            { name: "社区服务", value: 9 },
            { name: "城市环境", value: 18 },
            { name: "情感咨询", value: 27 },
            { name: "志愿者", value: 26 },
            { name: "大白", value: 2 },
            { name: "打雷", value: 24 },
            { name: "小雨", value: 3 }
        ]
    }
})
const options = ref({
  series: [
    {
      type: "wordCloud",
      // 网格大小，各项之间间距
      gridSize: 25,
      // 形状 circle 圆，cardioid  心， diamond 菱形，
      // triangle-forward 、triangle 三角，star五角星
      shape: "circle",
      // 字体大小范围
      sizeRange: [14, 30],
      // 文字旋转角度范围
      rotationRange: [0, 90],
      // 旋转步值
      rotationStep: 90,
      // 自定义图形
      // maskImage: maskImage,
      left: "center",
      top: "center",
      right: null,
      bottom: null,
      // 画布宽
      width: "80%",
      // 画布高
      height: "100%",
      // 是否渲染超出画布的文字
      drawOutOfBound: false,
      textStyle: {
        color: function () {
            let r = 55 + ~~(Math.random() * 200);
            let g = 55 + ~~(Math.random() * 200);
            let b = 55 + ~~(Math.random() * 200);
            return `rgb(${r}, ${g}, ${b})`;
        },
        emphasis: {
          shadowBlur: 10,
          shadowColor: "#2ac",
        },
      },
      data: props.data,
    },
  ],
})
setTimeout(() => {
  loading.value = false
}, 300);
</script>

<style lang="scss" scoped>
.charts {
  width: 100%;
  height: 100%;
}
</style>