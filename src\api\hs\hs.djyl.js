/*
 * @Author: fanjialong <EMAIL>
 * @Date: 2023-04-17 15:06:09
 * @LastEditors: fanjialong 
 * @LastEditTime: 2023-09-11 22:35:01
 * @FilePath: \hs_dp\src\api\hs\hs.api.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import http from '@/utils/leader/request'
import {
  xtyyUrl
} from '@/utils/leader/const'

//地图打点
export const getDgwMap = (type) => {
  return http.get(xtyyUrl + `/api/tDjylDzzxx/queryAll?type=${type}`)
}

//党组织信息
export const getDzzInfo = (type) => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryByModule?module=党组织信息`)
}

//党员画像-党龄
export const getDyhxDl = (type) => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryByModule?module=党员画像-党龄`)
}

//党员画像-学历
export const getDyhxXl = (type) => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryByModule?module=党员画像-学历`)
}

//党员画像-党龄
export const getDyhxLx = (type) => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryByModule?module=党员画像-类型`)
}

// 非公党支部信息
export const getFgdzbPage = (name, page, size) => {
  return http.get(xtyyUrl + `/api/tDjylDzzxx/queryNonPublicByPage?name=${name}&page=${page}&size=${size}`)
}

// 三会一课
export const getShyk = () => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryByModule?module=三会一课`)
}

// 党组织信息
export const getDzzxx = () => {
  return http.get(xtyyUrl + `/api/tDjylDzzxx/queryByPage`)
}

// 文化湖熟
export const getWhhs = () => {
  return http.get(xtyyUrl + `/api/tArticleInfo/queryByPage`)
}
