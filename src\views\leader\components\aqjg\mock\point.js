function generateId() {
  const randomNum = Math.random()
    .toString(36)
    .substring(2, 11) // 生成9位随机字符串
  return randomNum
}

// 企业 - 单个
export const enterpriseSinglePoint = [
  {
    latlng: [31.838550567626953, 118.94554138183594],
    iconUrl: require('@/assets/map/point/point1.png'),
    id: generateId()
  }
]

// 企业应急队伍
export const enterpriseEmergencyTeamPoint = [
  {
    latlng: [31.830125758537203, 118.94251609917319],
    iconUrl: require('@/assets/map/point/point9.png'),
    id: generateId()
  },
  {
    latlng: [31.866861293205172, 118.9596822368685],
    iconUrl: require('@/assets/map/point/point9.png'),
    id: generateId()
  }
]

// 加油站
export const gasStationPoint = [
  {
    latlng: [31.833902308830172, 118.92397667046225],
    iconUrl: require('@/assets/map/point/point10.png'),
    id: generateId()
  },
  {
    latlng: [31.79922671068564, 118.97135521050132],
    iconUrl: require('@/assets/map/point/point10.png'),
    id: generateId()
  }
]

// 企业
export const enterprisePoint = [
  {
    latlng: [31.839395472892672, 118.954189072806],
    iconUrl: require('@/assets/map/point/point11.png'),
    id: generateId()
  },
  {
    latlng: [31.83973879564658, 118.92397667046225],
    iconUrl: require('@/assets/map/point/point11.png'),
    id: generateId()
  }
]

// 防护目标
export const protectiveTargetPoint = [
  {
    latlng: [31.80025667894736, 118.92603660698569],
    iconUrl: require('@/assets/map/point/point12.png'),
    id: generateId()
  },
  {
    latlng: [31.84042544115439, 118.88655449028647],
    iconUrl: require('@/assets/map/point/point12.png'),
    id: generateId()
  }
]

// 体外除颤仪
export const externalDefibrillatorPoint = [
  {
    latlng: [31.836305568107516, 118.94251609917319],
    iconUrl: require('@/assets/map/point/point13.png'),
    id: generateId()
  },
  {
    latlng: [31.815032958984375, 118.92494201660156],
    iconUrl: require('@/assets/map/point/point13.png'),
    id: generateId()
  }
]

// 医疗机构
export const medicalInstitutionPoint = [
  {
    latlng: [31.834730407649815, 118.92981315727866],
    iconUrl: require('@/assets/map/point/point14.png'),
    id: generateId()
  },
  {
    latlng: [31.874071071037203, 118.98234153862632],
    iconUrl: require('@/assets/map/point/point14.png'),
    id: generateId()
  }
]

// 变电站
export const substationPoint = [
  {
    latlng: [31.83218569506064, 118.94045616264975],
    iconUrl: require('@/assets/map/point/point15.png'),
    id: generateId()
  },
  {
    latlng: [31.860338160880953, 118.87488151665366],
    iconUrl: require('@/assets/map/point/point15.png'),
    id: generateId()
  }
]

// 二级消防单位
export const fireFightingUnitPoint = [
  {
    latlng: [31.795450160392672, 118.94457603569663],
    iconUrl: require('@/assets/map/point/point16.png'),
    id: generateId()
  },
  {
    latlng: [31.795450160392672, 118.98886467095053],
    iconUrl: require('@/assets/map/point/point16.png'),
    id: generateId()
  }
]

// 视频监控
export const videoSurveillancePoint = [
  {
    latlng: [31.821542689689547, 118.90097404595053],
    iconUrl: require('@/assets/map/point/point19.png'),
    id: generateId()
  },
  {
    latlng: [31.86171145189658, 118.96208549614585],
    iconUrl: require('@/assets/map/point/point19.png'),
    id: generateId()
  }
]

// 公安队伍
export const publicecurityTeamPoint = [
  {
    latlng: [31.846605250724703, 118.90028740044272],
    iconUrl: require('@/assets/map/point/point17.png'),
    id: generateId()
  },
  {
    latlng: [31.836648890861422, 118.88346458550132],
    iconUrl: require('@/assets/map/point/point17.png'),
    id: generateId()
  }
]

// 学校
export const schoolPoint = [
  {
    latlng: [31.830997467041016, 118.92305374145508],
    iconUrl: require('@/assets/map/point/point18.png'),
    id: generateId()
  },
  {
    latlng: [31.85708999633789, 119.00390625],
    iconUrl: require('@/assets/map/point/point18.png'),
    id: generateId()
  }
]

// 网格员
export const gridOperatorPoint = [
  {
    latlng: [31.836305568107516, 118.94251609917319],
    iconUrl: require('@/assets/map/point/point2.png'),
    id: generateId()
  },
  {
    latlng: [31.815032958984375, 118.92494201660156],
    iconUrl: require('@/assets/map/point/point2.png'),
    id: generateId()
  }
]
