// 时长处理
export const calculateTime = (time) => {
  let hour, minute, second;
  let rest = Math.floor(time % 3600)
  if(Math.floor(time / 3600) > 0) {
    hour = Math.floor(time / 3600) <= 9
            ? "0" + Math.floor(time / 3600)
            : Math.floor(time / 3600);
  } else {
    hour = "00"
  }
  if (Math.floor(rest / 60) > 0) {
    minute =
      Math.floor(rest / 60) <= 9
        ? "0" + Math.floor(rest / 60)
        : Math.floor(rest / 60);
  } else {
    minute = "00";
  }
  if (Math.floor(rest % 60) > 0) {
    second =
      Math.floor(rest % 60) <= 9
        ? "0" + Math.floor(rest % 60)
        : Math.floor(rest % 60);
  } else {
    second = "00";
  }
  return `${hour}:${minute}:${second}`
}

export const timeToMinute = (time) => {
  let minute, second;
  if (Math.floor(time / 60) > 0) {
    minute =
      Math.floor(time / 60) <= 9
        ? "0" + Math.floor(time / 60)
        : Math.floor(time / 60);
  } else {
    minute = "00";
  }
  if (Math.floor(time % 60) > 0) {
    second =
      Math.floor(time % 60) <= 9
        ? "0" + Math.floor(time % 60)
        : Math.floor(time % 60);
  } else {
    second = "0";
  }
  if(time >= 60) {
    return `${minute}分${second}秒`;
  } else {
    return `${second}秒`;
  }
}

export const timeToHtml = (time) => {
  let minute, second;
  if (Math.floor(time / 60) > 0) {
    minute =
      Math.floor(time / 60) <= 9
        ? "0" + Math.floor(time / 60)
        : Math.floor(time / 60);
  } else {
    minute = "00";
  }
  if (Math.floor(time % 60) > 0) {
    second =
      Math.floor(time % 60) <= 9
        ? "0" + Math.floor(time % 60)
        : Math.floor(time % 60);
  } else {
    second = "00";
  }
  if(time >= 60) {
    return `${minute}<span class="unit">分</span>${second}<span class="unit">秒</span>`;
  } else {
    return `${second}<span class="unit">秒</span>`;
  }
}
// 隐藏手机号中间4位
export const hideMobile = (mobile) => {
  return mobile.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')
}
// 校验手机号
export const validateCallNum = (identity) => {
  // let reg = /^Phone\s[0-9]+/
  let reg = /^1[345789]\d{9}$/
  return reg.test(identity)
}
// 验证用户来源
export const validateCallName = (identity) => {
  let reg = /^01[345789]\d{9}$/
  return reg.test(identity)
}

let timer = null
export function debounce(fun, wait = 200) {
  return function () {
    const argu = arguments
    if(timer) {
      clearTimeout(timer)
      timer = null
    }
    timer = setTimeout(function () {
      fun.apply(this, argu)
    }, wait)
  }
}
