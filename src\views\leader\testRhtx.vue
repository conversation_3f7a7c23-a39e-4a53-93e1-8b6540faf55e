<!--
 * @Author: fanjialong <EMAIL>
 * @Date: 2023-04-17 10:43:52
 * @LastEditors: fanjialong <EMAIL>
 * @LastEditTime: 2023-04-17 12:11:09
 * @FilePath: \hs_dp\src\views\leader\testRhtx.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="box">
    <div class="btn">
      <div @click="testBtnSp">单人视频</div>
      <div @click="testBtnYy">单人语音</div>
      <div @click="testBtnHySp">多人视频</div>
      <div @click="testBtnHyyy">多人语音</div>
      <div @click="testBtnAdd">添加人员</div>
    </div>

    <div id="rhtx_BoxId" ref="video" v-if="rhtxBoxShow"></div>
  </div>
</template>

<script>
import rtc from '@/rhtx/core/index'
export default {
  data() {
    return {
      tempVideoHy: null,
      rhtxBoxShow: false,
      dryyth: null
    }
  },
  methods: {
    testBtnSp() {
      console.log(1111)
      // 视频通话
      this.rhtxBoxShow = true
      this.$nextTick(async () => {
        this.dryyth = await rtc.p2p.callPop({
          el: this.$refs.video, //页面挂载元素
          target: '4002', //手机端的账号
          callType: 'voice_video'
        })
        console.log('this.dryyth', this.dryyth)
        this.dryyth.session.on('hangup', () => {
          console.log('视频通话关闭')
          this.rhtxBoxShow = false
        })
      })
    },
    testBtnYy() {
      console.log(222)
      // 语音通话
      this.rhtxBoxShow = true
      this.$nextTick(async () => {
        this.dryyth = await rtc.p2p.callPop({
          el: this.$refs.video, //页面挂载元素
          target: '4002', //手机端的账号
          callType: 'voice'
        })
        console.log('this.dryyth', this.dryyth)
        this.dryyth.session.on('hangup', () => {
          console.log('语音通话关闭')
          this.rhtxBoxShow = false
        })
      })
    },
    async testBtnHySp() {
      console.log(this.$refs.video)
      this.rhtxBoxShow = true
      rtc.meet.mounted('rhtx_BoxId')
      this.tempVideoHy = await rtc.meet.callPopTempVideo({ users: [4002] })
      this.tempVideoHy.on('meetOff', () => {
        console.log('会议关闭')
        this.rhtxBoxShow = false
      })
      console.log(this.tempVideoHy)
    },
    async testBtnHyyy() {
      this.rhtxBoxShow = true
      rtc.meet.mounted('rhtx_BoxId')
      this.tempVideoHy = await rtc.meet.callPopTempVoice({ users: [4002] })
      this.tempVideoHy.on('meetOff', () => {
        console.log('会议关闭')
        this.rhtxBoxShow = false
      })
    },
    testBtnAdd() {
      console.log(555)
      this.tempVideoHy.addUser([4001])
    }
  }
}
</script>

<style lang="less" scoped>
.box {
  width: 1920px;
  height: 1080px;
  position: relative;
  z-index: 99999;
}
.btn {
  position: absolute;
  top: 200px;
  color: #fff;
  left: 560px;
  z-index: 99999;
}
#rhtx_BoxId {
  position: absolute;
  left: 600px;
  width: 600px;
  height: 600px;
  top: 300px;
  border: 1px solid red;
  z-index: 999;
}
</style>