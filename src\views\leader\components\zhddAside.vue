<template>
  <div class="container">
    <ul>
      <li v-for="(item,index) in envirData" :key="index" @click="menuClick(item,index)">
        <div class="pic">
          <img :src="activeIndex == index?item.icon_a:item.icon" />
        </div>
        <div :class="['name',activeIndex == index?'active':'']">{{ item.name }}</div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    envirData: {
      type: Array,
      default: () => [
        {
          icon: require("@/assets/mhassets/icon_yc.png"),
          icon_a: require("@/assets/mhassets/icon_yc_a.png"),
          name: '云场',
          type: ''
        },
        {
          icon: require("@/assets/mhassets/icon_fc.png"),
          icon_a: require("@/assets/mhassets/icon_fc_a.png"),
          name: '风场',
          type: 'gz_windflowobs1h'
        },
        {
          icon: require("@/assets/mhassets/icon_jy.png"),
          icon_a: require("@/assets/mhassets/icon_jy_a.png"),
          name: '降雨',
          type: 1318
        },
        {
          icon: require("@/assets/mhassets/icon_tr.png"),
          icon_a: require("@/assets/mhassets/icon_tr_a.png"),
          name: '土壤湿度',
          type: 4094
        },
      ]
    }
  },
  data () {
    return {
      activeIndex: -1
    }
  },
  created () { },
  mounted () { },
  watch: {},
  computed: {},
  methods: {
    menuClick (item, index) {
      if (this.activeIndex === index) {
        this.activeIndex = -1
        this.$emit('menuClick', false)
      } else {
        this.activeIndex = index
        this.$emit('menuClick', item.type)
      }
    }
  },
};
</script>
<style lang="scss" scoped>
.container {
  position: absolute;
  bottom: 100px;
  left: 510px;
  z-index: 1001;
  cursor: pointer;
  ul {
    display: flex;
    flex-direction: column;
    gap: 22px;
    li {
      display: flex;
      .pic {
        margin-right: 15px;
        img {
          width: 30px;
          height: 30px;
        }
      }
      .name {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 14px;
        color: #4db5ff;
        line-height: 20px;
        text-align: center;
        height: 30px;
        line-height: 30px;
        &.active {
          background: linear-gradient(
            180deg,
            rgba(255, 255, 255, 0.9) 0%,
            rgba(255, 208, 0, 0.9) 100%
          );
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        background: linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, #0093ff 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }
}
</style>