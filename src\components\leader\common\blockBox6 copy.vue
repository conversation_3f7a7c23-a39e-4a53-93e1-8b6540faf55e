<template>
  <div class="block" :style="{ height: blockHeight + 'px' }">
    <div class="block_title" @click="clickBulletFrame">
      <span class="title">{{ title }}</span>
      <!-- <span class="subtitle">{{ subtitle }}</span> -->
      <ul class="btns" v-if="isListBtns">
        <li
          :class="['btnList', currentIndex == index ? 'active' : '']"
          v-for="(item, index) in textArr"
          :key="index"
          @click.stop="clickchange(index)"
        >
          {{ item }}
        </li>
      </ul>
      <div class="more" v-if="showMore" @click="showMoreFn">更多</div>
      <div class="more" v-if="showMore2" @click="showMoreFn2">返回</div>
      <div  v-if="showMore3" class="popDropDown">
        <el-dropdown v-if="textArr2.length > 0" @command="clickchange">
          <span class="el-dropdown-link">
            {{ textArr2[currentIndex][1] }}<i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="(item, index) in textArr2" :key="index" :command="index">
              {{ item[1] }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <div class="content">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    subtitle: {
      type: String,
      default: ''
    },
    textArr: {
      type: Array,
      default: () => ['社会保险', '住房保险']
    },
    textArr2:{
      type: Array,
      default: () => [["","全部"]]
    },
    isListBtns: {
      type: Boolean,
      default: true
    },
    showMore: {
      type: Boolean,
      default: false
    },
    showMore2: {
      type: Boolean,
      default: false
    },
    showMore3:{
      type: Boolean,
      default: false
    },
    blockHeight: {
      type: Number,
      default: 277
    }
  },
  data() {
    return {
      currentIndex: 0
    }
  },
  methods: {
    clickchange(index) {
      this.currentIndex = index
      this.$emit('updateChange', this.currentIndex)
    },
    clickBulletFrame() {
      this.$emit('updateChange2', true)
    },
    showMoreFn() {
      this.$emit('handleShowMore', true)
    },
    showMoreFn2() {
      this.$emit('handleShowMore2', true)
    }
  }
}
</script>

<style lang="less" scoped>
.block {
  width: 450px;
  box-sizing: border-box;
  z-index: 101;
  position: relative;
  .block_title {
    position: relative;
    width: 100%;
    height: 48px;
    background: url(~@/assets/img/title_bg2.png) no-repeat;
    background-size: 100% 100%;
    display: flex;
    // justify-content: space-between;
    padding-left: 30px;
    position: relative;
    &.title_bg_Btn {
      background: url(~@/assets/leader/img/component/title_bg1.png) no-repeat;
      background-size: 100% 100%;
    }
    .title {
      font-family: YouSheBiaoTiHei;
      font-size: 22px;
      color: #FFFFFF;
      line-height: 36px;
      font-style: normal;
      letter-spacing: 2px;
      background: linear-gradient(180deg, #ffffff 0%, #7ed1ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .subtitle {
      font-size: 14px;
      font-family:DINCond-RegularAlternate;
      font-weight: normal;
      color: #b3daff;
    }

    .btns {
      display: flex;
      align-items: center;
      height: 16px;
      // margin-right: 22px;
      position: absolute;
      top: 1px;
      right: 15px;
      .btnList {
        // width: 50px;
        height: 26px;
        font-size: 16px;
        font-family: PangMenZhengDao;
        color: #caecff;
        text-shadow: 0px 0px 1px #00132e;
        background: url(~@/assets/leader/img/component/title_btn.png) no-repeat;
        background-size: 100% 100%;
        line-height: 26px;
        cursor: pointer;
        &.active {
          color: #45daff;
          text-shadow: 0px 0px 4px rgba(0, 175, 255, 0.5), 0px 0px 1px rgba(0, 30, 50, 0.5);
        }
        &:not(:last-of-type) {
          margin-right: 14px;
        }
      }
    }
    .more {
      position: absolute;
      right: 8px;
      top: 14px;
      height: 21px;
      text-shadow: 0px 0px 1px #00132e;
      cursor: pointer;
      font-family: YouSheBiaoTiHei;
      font-size: 16px;
      color: #CAECFF;
      line-height: 21px;
      text-align: center;
      font-style: normal;
    }
  }
  .content {
    position: relative;
    width: 100%;
    height: calc(100% - 54px);
    background: rgba(0,14,25,0.4);
    border: 1px solid rgba(56,99,176,0.75);
    margin-top: 6px;
  }
}

.popDropDown{
  ::v-deep .el-dropdown {
    color: #CAECFF;
    font-family: YouSheBiaoTiHei;
    font-size: 16px;
    line-height: 21px;
    text-align: right;
    font-style: normal;
    line-height:40px;
    right: 0;
    position: absolute;
  }
  ::v-deep .el-dropdown-menu {
    // background-color: #00173b !important;
    // border:1px solid #2b7bbb !important;

    background-color: #c6e2e7 !important;
	border: 1px solid #d2f2ea !important;
  }
  .el-dropdown-menu__item {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.8);
    text-align: center;
    line-height: 40px;
    padding: 0 30px;
    &:hover {
      background: #185493;
      color: rgba(255, 255, 255, 0.8);
    }
  }
}
</style>
