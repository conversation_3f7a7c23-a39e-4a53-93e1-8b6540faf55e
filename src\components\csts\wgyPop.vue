<template>
  <div class="ai_waring">
    <div class="title">
      <span>网格员信息</span>
      <img @click="closeEmit" class="close_btn" src="@/assets/shzl/map/close_btn.png" alt="" />
    </div>
    <div class="content">
      <div class="pic">
        <img src="@/assets/csts/wgy_tx.png" alt="" />
      </div>
      <div><span>姓名：</span><span>张东海</span></div>
      <div><span>所属部门：</span><span>鼓楼区-中央门街道-第三网格</span></div>
      <div><span>职位：</span><span>网格员</span></div>
      <div><span>联系方式：</span><span>189516022221</span></div>
      <!-- <div class="btns">
        <div @click="splxMethod">视频连线</div>
        <div @click="gjMethod">轨迹巡查</div>
      </div> -->
    </div>
  </div>
</template>

<script>
export default {
  name: 'wgyPop',
  methods: {
    closeEmit() {
      this.$emit('closeEmit')
    },
    splxMethod() {
      console.log(1111)
      this.$parent.splxMethod()
    },
    gjMethod() {
      console.log(222)
      this.$parent.gjMethod()
    },
  },
}
</script>

<style lang='less' scoped>
.ai_waring {
  width: 413px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  // border: 1px solid red;
  background: url(~@/assets/shzl/map/ai_popBg.png) no-repeat center / 100% 100%;
  background-size: 100% 100%;
  z-index: 999;
  .title {
    background: url(~@/assets/shzl/map/title_bg.png) no-repeat center / 100% 100%;
    background-size: 100% 100%;
    width: 375px;
    height: 47px;
    margin: 20px auto 0;
    line-height: 47px;
    text-align: left;
    text-indent: 27px;
    font-size: 18px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #ffffff;
    position: relative;
    span {
      background: linear-gradient(180deg, #ffffff 0%, #2aa6ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .close_btn {
      position: absolute;
      top: 11px;
      right: 25px;
      cursor: pointer;
    }
  }
  .content {
    padding: 9px 50px 38px 48px;
    & > div {
      display: flex;
      justify-content: space-between;
      &:not(:last-of-type) {
        margin-bottom: 14px;
      }
      & span:first-of-type {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
      }
      & span:last-of-type {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
      }
    }
    .pic {
      width: 96px;
      height: 115px;
      margin: 0 auto;
    }
    .btns {
      padding: 0 40px 0;
      justify-content: space-between;
      & > div {
        background: url(~@/assets/shzl/map/tip_btn1.png) no-repeat center / 100% 100%;
        background-size: 100% 100%;
        width: 96px;
        height: 38px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 38px;
      }
    }
  }
}
</style>