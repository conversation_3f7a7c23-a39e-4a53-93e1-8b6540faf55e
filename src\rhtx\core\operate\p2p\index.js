import CallSession from './CallSession';
import { useP2PNotice } from './notice';
import RTCBase from '../base/index';
import { USER_SCOPE } from '../../dict/index';
import { getCallDeviceType } from '../../util/index';

export default class P2P extends RTCBase {
  constructor({ userInfo, ws }) {
    super({ userInfo, ws });
    this.callList = {};
    this.init();
  }

  init() {
    this.initP2PNotice();
  }

  initP2PNotice() {
    const p2pNotice = useP2PNotice.call(this);
    this.ws.on('*', p2pNotice);
  }

  getTarget(call) {
    return call.caller === this.userInfo.user.phone ? call.callee : call.caller;
  }

  getTargetCallItem(call) {
    const target = this.getTarget(call, this.userInfo);
    const callItem = this.callList[target];
    return callItem;
  }

  // 呼叫
  async call(
    {
      target,
      mediaConstraints = {
        audio: false,
        video: false
      }
    },
    event = {}
  ) {
    if (target === this.userInfo.user.phone) {
      throw 'prohibit calling yourself';
    }
    let user = await this.api.usersearch({ phone: target });
    let scope = user.length ? user[0].scope : USER_SCOPE.OUTNUMBER;
    mediaConstraints.type =
      this.userInfo.config.sys_sip_use_state &&
      [
        USER_SCOPE.TELEPHONE,
        USER_SCOPE.OUTNUMBER,
        USER_SCOPE.ONLINEAPP
      ].includes(Number(scope))
        ? 'sip'
        : 'p2p';
    const publishPcInfo = await this.publish({ target, mediaConstraints });
    let callItem = new CallSession(
      {
        publishPcInfo,
        userInfo: this.userInfo,
        isMineCaller: true
      },
      event
    );
    this.callList[target] = callItem;
    return callItem;
  }

  //挂断
  hangup(call) {
    const target = this.getTarget(call);
    if (
      this.callList[target] &&
      this.callList[target].publishPcInfo &&
      !this.config.isMediaSource
    ) {
      this.callList[target] && this.callList[target].terminate();
    } else {
      this.api.sysCallHangup({
        user_id: this.userInfo.user.id,
        uuid: call.uuid
      });
    }
  }

  // 接听
  answer(call, event) {
    // 先推流 在拉流
    return new Promise(async (resolve, reject) => {
      try {
        if (!call) {
          console.error('必填参数call不能为空');
          return;
        }
        let publishPcInfo = {};
        //其他设备源头
        if (this.userInfo.config.media_sources) {
          this.api.sysCallAnswer({
            caller: call.caller,
            callee: this.userInfo.user.phone
          });
        } else {
          //自身设备推流
          publishPcInfo = await this.publish({
            target: call.caller,
            mediaConstraints: getCallDeviceType(call.call_type)
          });
        }
        let playerPcInfo = await this.player({
          url: call.caller_stream,
          target: call.caller,
          call_type: call.call_type,
          stream: call.caller_stream
        });

        const callItem = this.getTargetCallItem(
          call,
          this.userInfo,
          this.callList
        );
        // callItem.setEvent(event);
        Object.assign(callItem, {
          publishPcInfo,
          playerPcInfo,
          event
        });
        resolve(callItem);
      } catch (error) {
        console.log(error);
        reject(error);
      }
    });
  }

  //播放
  play({ call }) {
    return this.player({
      url:
        this.userInfo.user.phone === call.caller
          ? call.callee_stream
          : call.caller_stream,
      target:
        this.userInfo.user.phone === call.caller ? call.callee : call.caller,
      call_type: call.call_type,
      stream: call.callee_stream
    });
  }
}
