<template>
  <div class="block" :style="{ height: blockHeight + 'px', width: blockWidth + 'px' }">
    <div
      class="block_title"
      @click="clickBulletFrame"
      :style="{backgroundImage: 'url('+blockBg+')'}"
    >
      <span class="title">{{ title }}</span>
      <span class="subtitle">{{ subtitle }}</span>
      <ul class="btns" v-if="isListBtns">
        <li
          :class="['btnList', currentIndex == index ? 'active' : '']"
          v-for="(item, index) in textArr"
          :key="index"
          @click.stop="clickchange(index)"
        >{{ item }}</li>
      </ul>
      <div class="more" v-if="showMore" @click="showMoreFn">更多</div>
    </div>
    <div class="content">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    subtitle: {
      type: String,
      default: ''
    },
    textArr: {
      type: Array,
      default: () => ['社会保险', '住房保险']
    },
    isListBtns: {
      type: Boolean,
      default: true
    },
    showMore: {
      type: Boolean,
      default: false
    },
    blockHeight: {
      type: Number,
      default: 277
    },
    blockWidth: {
      type: Number,
      default: 277
    },
    blockBg: {
      type: String,
      default: require('@/assets/fxyp/title_long_bg.png')
    }
  },
  data() {
    return {
      currentIndex: 0
    }
  },
  methods: {
    clickchange(index) {
      this.currentIndex = index
      this.$emit('updateChange', this.currentIndex)
    },
    clickBulletFrame() {
      this.$emit('updateChange2', true)
    },
    showMoreFn() {
      this.$emit('handleShowMore', true)
    }
  }
}
</script>

<style lang="scss" scoped>
.block {
  width: 460px;
  box-sizing: border-box;
  z-index: 101;
  position: relative;
  .block_title {
    // border: 1px solid red;
    position: relative;
    width: 100%;
    height: 33px;
    // background: url(~@/assets/fxyp/title_long_bg.png) no-repeat;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;
    // justify-content: space-between;
    padding-left: 34px;
    position: relative;
    // &.title_bg_Btn {
    //   background: url(~@/assets/leader/img/component/title_bg1.png) no-repeat;
    //   background-size: 100% 100%;
    // }
    .title_long_left {
      width: 100%;
      height: 33px;
      background: url(~@/assets/fxyp/title_long_left.png) no-repeat;
      background-size: 100% 100%;
      position: absolute;
      z-index: 10;
    }
    .title {
      font-size: 22px;
      font-family: PangMenZhengDao;
      letter-spacing: 2px;
      background: linear-gradient(180deg, #ffffff 0%, #7ed1ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-top: -6px;
      margin-right: 6px;
      margin-left: 6px;
    }
    .subtitle {
      font-size: 14px;
      font-family:DINCond-RegularAlternate;
      font-weight: normal;
      color: #b3daff;
    }

    .btns {
      display: flex;
      align-items: center;
      line-height: 50px;
      // margin-right: 22px;
      position: absolute;
      right: 22px;
      bottom: 14px;
      .btnList {
        font-size: 18px;
        font-family: PangMenZhengDao;
        color: #caecff;
        line-height: 18px;
        text-shadow: 0px 0px 1px #00132e;
        cursor: pointer;
        &.active {
          // color: #45daff;
          // text-shadow: 0px 0px 4px rgba(0, 175, 255, 0.5), 0px 0px 1px rgba(0, 30, 50, 0.5);
        }
        &:not(:last-of-type) {
          margin-right: 14px;
        }
      }
    }
    .more {
      position: absolute;
      right: 22px;
      top: 1px;
      height: 16px;
      font-size: 16px;
      font-family: PangMenZhengDao;
      color: #caecff;
      line-height: 18px;
      text-shadow: 0px 0px 1px #00132e;
      cursor: pointer;
    }
  }
  .content {
    position: relative;
    height: calc(100% - 33px);
  }
}
</style>
