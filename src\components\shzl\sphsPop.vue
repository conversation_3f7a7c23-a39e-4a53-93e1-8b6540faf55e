<!--
 * @Author: fanjialong <EMAIL>
 * @Date: 2023-04-18 16:04:46
 * @LastEditors: fanjialong 
 * @LastEditTime: 2023-10-18 12:13:30
 * @FilePath: \hs_dp\src\components\shzl\sphsPop.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <div class="szyyPop">
      <img src="@/assets/img/video_close.png" alt class="close" @click="close" />
      <div class="title">
        <div class="title_name">
          <slot name="title1">视频会商</slot>
        </div>
      </div>
      <div class="center">
        <div class="center_left">
          <div class="center_left1">
            <div class="center_item1">
              <slot name="title2">会议接入</slot>
            </div>
            <div class="center_item2">
              <el-input v-model="cameraValue" placeholder="搜索"></el-input>
              <img src="@/assets/shzl/map/camera/ss.png" alt />
            </div>
            <div class="center_item3">
              <!-- <el-collapse v-model="activeNames"> -->
                <!-- <el-collapse-item title="网格员" name="wgy"> -->
                  <el-tree
                    v-loading="loading"
                    element-loading-text="加载中"
                    :data="leftTreeData"
                    :check-on-click-node="true"
                    show-checkbox
                    node-key="id"
                    :props="defaultProps"
                    ref="videoTreeRef"
                    :filter-node-method="filterNode"
                    :default-checked-keys="checkedArr"
                  >
                    <span slot-scope="{ data }" :title="data.name">{{ data.name }}</span>
                  </el-tree>
                <!-- </el-collapse-item> -->
                <!-- <el-collapse-item title="城管人员" name="cgry">
                  <el-tree
                    :data="leftTreeDataCgry"
                    :check-on-click-node="true"
                    show-checkbox
                    node-key="id"
                    :props="defaultProps"
                    ref="videoTreeCgryRef"
                    :filter-node-method="filterNode"
                  >
                    <span slot-scope="{ data }" :title="data.label">{{ data.label }}</span>
                  </el-tree>
                </el-collapse-item> -->
              <!-- </el-collapse> -->
            </div>
            <div class="center_item4">
              <div @click="resetPeople">
                <slot name="title3">重置</slot>
              </div>
              <div @click="choosePeople">
                <slot name="title4">加入会议</slot>
              </div>
              <div @click="dialerShow=true">拨号</div>
            </div>
          </div>
        </div>
        <div class="center_right">
          <div class="center_right1">{{ dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss') }}</div>
          <div class="center_right2" id="rhtx_BoxId" v-if="rhtxBoxShow"></div>
        </div>
      </div>
      <!-- 拨号盘 -->
      <dialer v-if="dialerShow" @closeBtnDialer="dialerShow=false" @addSphsBtn="addSphsBtn" />
    </div>
    <transition name="fade">
      <div class="bg-header"></div>
    </transition>
  </div>
</template>

<script>
// import hlsVideo from '@/components/leader/hlsVideo'
import dialer from '@/components/common/dialer.vue'
import rtc from '@/rhtx/core/index'
// import { closeMeet } from '@/api/hs/hs.api.js'
export default {
  props: {
    leftTreeData: {
      type: Array,
      default: () => []
    },
    leftTreeDataCgry: {
      type: Array,
      default: () => []
    },
    initCheckedPeo: {
      type: Array,
      default: () => []
    }
  },
  components: {
    // hlsVideo,
    dialer
  },
  name: 'sphsPop',
  data() {
    return {
      title: 1,

      defaultProps: {
        children: 'showList',
        label: 'label'
      },
      company2: 2,
      optionsList1: [
        {
          value: 1,
          label: '标清'
        },
        {
          value: 2,
          label: '高清'
        },
        {
          value: 3,
          label: '超清'
        }
      ],
      cameraValue: '',
      testUrl1: 'http://**********:8088/firstfloor/stream1/hls.m3u8',
      testUrl2: 'http://**********:8088/sixthfloor/stream1_01/hls.m3u8',
      testUrl3: 'http://**********:8088/sixthfloor/stream1_01/hls.m3u8',
      testUrl4: 'http://**********:8088/firstfloor/stream2/hls.m3u8',
      rhtxBoxShow: false,
      tempVideoHy: null,
      checkedArr: [],
      mobiles: [],
      mobiles1: [],
      newUser: [],
      dialerShow: false,
      loading: true
      // activeNames: []
    }
  },
  created() {
    window.sessionStorage.setItem('oldUers', [])
  },
  mounted() {
    // console.log('this.$store.state.queryOrgStreetTreeData',this.$store.state.queryOrgStreetTreeData);
    if(this.$store.state.queryOrgStreetTreeData.length>0){
      this.loading = false
      this.leftTreeData = this.$store.state.queryOrgStreetTreeData
    }
    console.log('leftTreeData', this.leftTreeData)
    console.log('initCheckedPeo', this.initCheckedPeo)
    this.$nextTick(() => {
      // 默认有选中的人进行视频通话
      if (this.initCheckedPeo.length > 0) {
        this.checkedArr = this.initCheckedPeo //左侧tree默认选中传入的人员
        // 默认有选中的人保存到sessionStorage，再次点击tree加入会议，与sessionStorage的人员筛选下
        let oldUers = this.initCheckedPeo
        window.sessionStorage.setItem('oldUers', JSON.parse(JSON.stringify(oldUers)))
        this.testBtnHySp(oldUers)
      }
    })
  },
  methods: {
    async close() {
      try {
        if (this.tempVideoHy && window.sessionStorage.getItem('tempMeetVideoNum')) {
          // 关闭之前调用挂断
          this.tempVideoHy.terminate()
          window.sessionStorage.setItem('tempMeetVideoNum', '')
          window.sessionStorage.setItem('oldUers', [])
          this.$emit('closeEmit')
          // const res = await closeMeet(window.sessionStorage.getItem('tempMeetVideoNum'))
          // if (res.res_code == '0') {
          //   window.sessionStorage.setItem('tempMeetVideoNum', '')
          //   window.sessionStorage.setItem('oldUers', [])

          //   this.$emit('closeEmit')
          // } else {
          //   window.sessionStorage.setItem('tempMeetVideoNum', '')
          //   window.sessionStorage.setItem('oldUers', [])
          //   this.$emit('closeEmit')
          // }
        } else {
          window.sessionStorage.setItem('tempMeetVideoNum', '')
          window.sessionStorage.setItem('oldUers', [])
          this.$emit('closeEmit')
        }
      } catch (error) {
        window.sessionStorage.setItem('tempMeetVideoNum', '')
        window.sessionStorage.setItem('oldUers', [])
        this.$emit('closeEmit')
      }
    },
    titleShow(index) {
      this.title = index
    },
    changeSelect1() {},
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    resetPeople() {
      this.$refs.videoTreeRef.setCheckedKeys([])
      // this.$refs.videoTreeCgryRef.setCheckedKeys([])
      window.sessionStorage.setItem('oldUers', [])
      // this.$emit('sphsDataSearch', '')
    },
    choosePeople() {
      // console.log(222, this.$refs.videoTreeRef.getCheckedKeys(true))
      // 初始化选择人员开启会议，再次点击选择人员，调用addUsers方法
      if (!this.tempVideoHy) {
        // 筛选出mobiles
        this.mobiles = []
        this.mobiles = this.$refs.videoTreeRef.getCheckedKeys(true).reduce((result, id) => {
          const phone = this.findMobileById(id, this.leftTreeData)
          if (phone) {
            result.push(phone)
          }
          return result
        }, [])

        // this.mobiles1 = []
        // this.mobiles1 = this.$refs.videoTreeCgryRef.getCheckedKeys(true).reduce((result, id) => {
        //   const phone = this.findMobileById(id, this.leftTreeDataCgry)
        //   if (phone) {
        //     result.push(phone)
        //   }
        //   return result
        // }, [])

        // console.log(this.mobiles)
        let oldUers = this.mobiles
        window.sessionStorage.setItem('oldUers', JSON.parse(JSON.stringify(oldUers)))
        this.testBtnHySp(oldUers)
      } else if (this.tempVideoHy) {
        // 筛选出mobiles
        this.mobiles = []
        this.mobiles = this.$refs.videoTreeRef.getCheckedKeys(true).reduce((result, id) => {
          const phone = this.findMobileById(id, this.leftTreeData)
          if (phone) {
            result.push(phone)
          }
          return result
        }, [])
        // this.mobiles1 = []
        // this.mobiles1 = this.$refs.videoTreeCgryRef.getCheckedKeys(true).reduce((result, id) => {
        //   const phone = this.findMobileById(id, this.leftTreeDataCgry)
        //   if (phone) {
        //     result.push(phone)
        //   }
        //   return result
        // }, [])

        this.newUser = []
        if (window.sessionStorage.getItem('oldUers').length > 0) {
          this.newUser = this.filterArr(window.sessionStorage.getItem('oldUers').split(','), this.mobiles)
        } else {
          this.newUser = this.mobiles
        }
        this.addUsers(this.newUser)
      }
    },
    async testBtnHySp(users) {
      console.log('initUsers', users)
      this.rhtxBoxShow = true
      rtc.meet.mounted('rhtx_BoxId')
      this.tempVideoHy = await rtc.meet.callPopTempVideo({ users: users })
      this.tempVideoHy.on('meetOff', () => {
        console.log('会议关闭')
        this.rhtxBoxShow = false
        this.tempVideoHy = null
        this.resetPeople() // 清空已选人员
      })
    },
    // 添加人员
    addUsers(newUser) {
      console.log('addUsers', newUser)
      this.tempVideoHy.addUser(newUser)
      let allUsers = [...window.sessionStorage.getItem('oldUers').split(','), ...newUser]
      window.sessionStorage.setItem('oldUers', JSON.parse(JSON.stringify(allUsers)))
    },
    findMobileById(id, items) {
      for (let i = 0; i < items.length; i++) {
        const item = items[i]
        if (item.id === id && item.mobile) {
          return item.mobile
        } else if (item.showList && item.showList.length > 0) {
          const phone = this.findMobileById(id, item.showList)
          if (phone) {
            return phone
          }
        }
      }
      return ''
    },
    // js找出两个数组中不同元素
    filterArr(arr1, arr2) {
      return arr1.concat(arr2).filter((t, i, arr) => {
        return arr.indexOf(t) === arr.lastIndexOf(t)
      })
    },
    addSphsBtn(val) {
      this.dialerShow = false
      console.log(val)
      if (!this.tempVideoHy) {
        this.testBtnHySp([val])
      } else {
        this.addUsers([val])
      }
    },
    // searchName() {
    //   this.$emit('sphsDataSearch', this.cameraValue)
    // }
  },
  watch: {
    cameraValue(val) {
      this.$refs.videoTreeRef.filter(val)
      // this.$refs.videoTreeCgryRef.filter(val)
      // if (val == '') {
      //   this.$emit('sphsDataSearch', this.cameraValue)
      // }
    },
    queryOrgStreetTreeData(newValue, oldValue) {
      // console.log('视频会商成员：','newValue'+ newValue, 'oldValue' + oldValue);
      if(newValue){
        this.loading = false
        this.leftTreeData = this.$store.state.queryOrgStreetTreeData
      }
    }
  },
  computed: {
    queryOrgStreetTreeData() {
      return this.$store.state.queryOrgStreetTreeData; 
    }
  },
}
</script>

<style lang="scss" scoped>
@import url('~@/assets/rhtx/sphs.less');
.bg-header {
  position: absolute;
  width: 100%;
  height: 1080px;
  background: rgba(2, 14, 50, 0.75);
  z-index: 999999;
  top: 0;
  left: 0;
}
.szyyPop {
  width: 1197px;
  height: 749px;
  background: rgba(9, 19, 34, 0.9);
  box-shadow: 0px 0px 22px 0px rgba(11, 54, 138, 0.35), 0px 2px 8px 0px rgba(0, 0, 0, 0.7),
    inset 0px 0px 8px 0px rgba(17, 146, 214, 0.35);
  border-radius: 11px;
  border: 1px solid #015c8c;
  position: absolute;
  z-index: 101;
  left: 50%;
  top: 38%;
  transform: translate(-50%, -40%);
  z-index: 99999999;
  .close {
    position: absolute;
    width: 23px;
    height: 23px;
    top: 24px;
    right: 24px;
    cursor: pointer;
  }
  .title {
    width: 634px;
    height: 76px;
    line-height: 32px;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
    background-image: url(~@/assets/img/video_title.png);
    .title_name {
      background-clip: text;
      font-size: 36px;
      font-family: PangMenZhengDao;
      color: #ffffff;
      line-height: 76px;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .center {
    width: 1200px;
    height: 600px;
    padding: 28px 0 0 0;
    .type_ {
      display: flex;
      align-items: center;
      margin: 17px 0 8px 14px;
      & > div {
        width: 76px;
        height: 30px;
        line-height: 30px;
        font-size: 13px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ccf4ff;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        background-position: center;
        background-image: url(~@/assets/shzl/map/camera/type_bg.png);
        &.active {
          background-image: url(~@/assets/shzl/map/camera/type_bg_active.png);
        }
        &:first-of-type {
          margin-right: 10px;
        }
      }
    }
    .center_left {
      float: left;
      margin-left: 15px;
      .center_left1 {
        width: 310px;
        height: 595px;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        background-position: center;
        background-image: url(~@/assets/img/video_leftk.png);
        margin: 8px 0 0 0;
        padding: 12px 0 0 0;
        .center_item1 {
          margin: 0 0 13px 25px;
          font-size: 20px;
          font-family: YouSheBiaoTiHei;
          color: #ffffff;
          line-height: 26px;
          // text-shadow: 0px 0px 4px #00575d;
          background: linear-gradient(180deg, #eeeeee 0%, #43f4ff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          text-align: left;
        }
        .center_item2 {
          width: 256px;
          height: 34px;
          line-height: 34px;
          color: #fff;
          text-align: left;
          background: rgba(0, 74, 143, 0.4)
            linear-gradient(
              360deg,
              rgba(181, 223, 248, 0) 0%,
              rgba(29, 172, 255, 0.29) 100%,
              #ffffff 100%
            );
          border-radius: 4px;
          border: 1px solid rgba(0, 162, 255, 0.6);
          position: relative;
          margin: 0 auto;
          img {
            position: absolute;
            right: 9px;
            top: 10px;
            z-index: 99;
          }
        }
        .center_item3 {
          margin: 20px auto 0;
          height: 387px;
          width: 290px;
          overflow-y: scroll;
          &::-webkit-scrollbar {
            /*滚动条整体样式*/
            width: 6px;
            background: transparent;
            // border: 1px solid #999;
            /*高宽分别对应横竖滚动条的尺寸*/
            // height: 1px;
          }

          &::-webkit-scrollbar-thumb {
            /*滚动条里面小方块*/
            border-radius: 2px;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
            background: rgb(45, 124, 228);
            height: 100px;
          }
        }
        .center_item4 {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 266px;
          font-size: 16px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 33px;
          margin: 42px auto 0;
          // position: relative;
          // z-index: 999;
          cursor: pointer;
          & div:first-of-type {
            width: 69px;
            height: 33px;
            background: url(~@/assets/img/video_reset.png) no-repeat;
          }
          & div:nth-of-type(2) {
            width: 99px;
            height: 33px;
            background: url(~@/assets/img/video_confirm.png) no-repeat;
          }
          & div:last-of-type {
            width: 69px;
            height: 33px;
            background: url(~@/assets/img/video_reset.png) no-repeat;
          }
        }
      }
    }
    .center_right {
      width: 860px;
      height: 580px;
      float: left;
      margin-left: 15px;
      .center_right1 {
        text-align: left;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 20px;
        letter-spacing: 1px;
      }
      .center_right2 {
        width: 860px;
        height: 595px;
        padding: 3px 0;
        // border: 1px solid #eee;
      }
    }
  }
}
::v-deep .el-tree {
  background: transparent;
  color: #f6feff;
  .el-tree-node__content:hover {
    background: transparent !important;
  }
  .el-checkbox__inner {
    background: transparent !important;
    width: 17px;
    height: 17px;
  }
}
::v-deep .el-tree-node__label {
  font-size: 16px;
}
::v-deep .el-tree-node__content {
  margin-bottom: 10px;
  font-size: 16px;
}
::v-deep .el-tree-node:focus > .el-tree-node__content {
  background-color: transparent !important;
}

::v-deep .el-tree-node__content:hover {
  background: transparent !important;
}
::v-deep .el-select {
  width: 71px;
  height: 30px;
  .el-input__inner {
    font-size: 13px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ccf4ff;
    line-height: 18px;
    background: rgba(0, 74, 143, 0.4)
      linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(0, 162, 255, 0.08) 100%, #ffffff 100%);
    border-radius: 15px;
    border: 1px solid rgba(0, 162, 255, 0.6);
    padding: 0 20px 0 12px;
  }
}
::v-deep .el-input__inner {
  background-color: transparent !important;
  border: none;
  color: #ccf4ff;
  height: 34px;
}
::v-deep .el-loading-mask {
  background: transparent;
}
</style>
