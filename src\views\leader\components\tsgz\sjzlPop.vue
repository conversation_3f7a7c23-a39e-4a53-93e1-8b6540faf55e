<template>
  <div class="tkBox">
    <div class="tkTitle">
      <span>事件态势分析</span>
    </div>
    <img @click="closeEmitai" src="@/assets/hszl/tkGb.png" class="tkGb" />
    <BlockBox title="事件来源" subtitle class="tkList" :isListBtns="false" :blockHeight="296">
      <div class="ybsscont">
        <PieChart3D type="1" :data="chartData" />
      </div>
    </BlockBox>
    <BlockBox title="高发事项" subtitle class="tkList" :isListBtns="false" :blockHeight="296">
      <div class="wrapper wrapper2">
        <pieChartTsgz />
      </div>
    </BlockBox>
    <BlockBox title="事件趋势" subtitle class="tkList" :isListBtns="false" :blockHeight="296">
      <div class="wrapper">
        <TrendLineChart :options="zczOptions" :data="zczData" :init-option="zczinitOptions" />
      </div>
    </BlockBox>
    <BlockBox title="高发区域" subtitle class="tkList" :isListBtns="false" :blockHeight="296">
      <div class="wrapper wrapper4">
        <div class="wrapperList">
          <div class="wrapperList1">
            <div class="wrapperLeft">湖熟社区</div>
            <div class="wrapperRight">
              500
              <span>件</span>
            </div>
          </div>
          <div class="wrapperList2">
            <div class="wrapperSelected"></div>
          </div>
        </div>
        <div class="wrapperList">
          <div class="wrapperList1">
            <div class="wrapperLeft">河南社区</div>
            <div class="wrapperRight">
              400
              <span>件</span>
            </div>
          </div>
          <div class="wrapperList2">
            <div class="wrapperSelected"></div>
          </div>
        </div>
        <div class="wrapperList">
          <div class="wrapperList1">
            <div class="wrapperLeft">河北社区</div>
            <div class="wrapperRight">
              300
              <span>件</span>
            </div>
          </div>
          <div class="wrapperList2">
            <div class="wrapperSelected"></div>
          </div>
        </div>
        <div class="wrapperList">
          <div class="wrapperList1">
            <div class="wrapperLeft">金桥社区</div>
            <div class="wrapperRight">
              200
              <span>件</span>
            </div>
          </div>
          <div class="wrapperList2">
            <div class="wrapperSelected"></div>
          </div>
        </div>
        <div class="wrapperList">
          <div class="wrapperList1">
            <div class="wrapperLeft">东阳社区</div>
            <div class="wrapperRight">
              100
              <span>件</span>
            </div>
          </div>
          <div class="wrapperList2">
            <div class="wrapperSelected"></div>
          </div>
        </div>
      </div>
    </BlockBox>
  </div>
</template>

<script>
import BlockBox from '@/components/leader/common/blockBox.vue'
import SwiperTable from '@/components/leader/leader_zwjs/SwiperTable.vue'
import pieChartTsgz from '@/components/tsgz/pieChartTsgz.vue'

export default {
  name: 'wgzltk',
  components: {
    BlockBox,
    SwiperTable,
    pieChartTsgz
  },
  data() {
    return {
      wgcurrentYear: this.dayjs().year(),
      wgcurrentMonth: '1',
      wgsjmonthList: [1, 2, 3],
      wgyearList: [],
      fyData: [
        {
          num: 42,
          name: '网格长/人'
        },
        {
          num: 56,
          name: '专属网格员/人'
        },
        {
          num: 51,
          name: '网格员/人'
        },
        {
          num: 65,
          name: '兼职网格员'
        }
      ],
      chartData: [
        ['product', '社会面小场所'],
        ['综治网格 10%', 1234],
        ['12345热线 15%', 134],
        ['数字城管 20%', 234],
        ['监测预警 25%', 34],
        ['其他 30%', 334]
      ],
      ybssData: [
        {
          num: 100,
          tit: '标准地址(个)',
          img: require('@/assets/hszl/wgzlImg1.png')
        },
        {
          num: 18072,
          tit: '实有人口(人)',
          img: require('@/assets/hszl/wgzlImg2.png')
        },
        {
          num: 624,
          tit: '实有房屋(个)',
          img: require('@/assets/hszl/wgzlImg3.png')
        },
        {
          num: 142,
          tit: '实有单位(个)',
          img: require('@/assets/hszl/wgzlImg4.png')
        }
      ],
      options: {
        yAxisMax: 1000,
        barColor: '#02C4DD',
        showBarLabel: true
      },
      data: [
        ['produce', '系列名'],
        ['2018', '830'],
        ['2019', '460'],
        ['2020', '700'],
        ['2021', '800'],
        ['2022', '580']
      ],
      box4BottomData: {
        data: [
          ['product', '事件总数'],
          ['燃气监测', 861],
          ['水质监测', 1026],
          ['烟感监测', 334],
          ['环境监测', 107]
        ],
        options: {
          colors: ['#80CF83', '#CDCF72', '#0280DF', '#13A4C0'],
          legend: {
            top: 2,
            // left: 100,
            // orient: 'vertical',
            itemWidth: 10,
            itemHeight: 10,
            itemRadius: 2,
            itemPadding: 5,
            itemDistance: 12,
            itemMarginTop: 12,
            textColor: 'rgba(255, 255, 255, 0.85)',
            fontWeight: '400',
            fontSize: '12px',
            fontFamily: 'DINPro-Medium',
            letterSpacing: '3px'
          },
          position: ['50%', '10%'],
          bgImg: {
            width: '60%',
            height: '60%',
            top: '42%',
            left: '50%'
          },
          unit: '件',
          title: {
            fontSize: '16px',
            top: 80
          },
          subtitle: {
            fontSize: '14px',
            top: 100
          }
        }
      },
      box1BottomData: {
        data: [
          ['product', '总量：吨', '系列名2', '总量：立方'],
          ['02', 50, 100, 99],
          ['04', 55, 100, 80],
          ['06', 25, 100, 70],
          ['08', 55, 100, 60],
          ['10', 55, 100, 77],
          ['12', 55, 100, 66]
        ],
        options: {}
      },
      zczOptions: {
        smooth: true,
        colors: [['#009BFF', '#0077FF']],
      },
      zczData: [
        ['product', ''],
        ['1月', 75],
        ['2月', 100],
        ['3月', 125],
        ['4月', 100],
        ['5月', 200],
        ['6月', 200],
        ['7月', 75],
        ['8月', 100],
        ['9月', 125],
        ['10月', 100],
        ['11月', 200],
        ['12月', 200]
      ],
    
    }
  },
  computed:{
      zczinitOptions() {
        return {
          yAxis: {
            name: '事件数量'
          },
          tooltip: {
            formatter: params => {
              var relVal = params[0].name
              for (var i = 0, l = params.length; i < l; i++) {
                var unit = '件'
                relVal += '<br/>' + params[i].marker + params[i].value + unit
              }
              return relVal
            }
          }
        }
      }
  },
  created() {
    let oldYear1 = 2020 //网格
    // let oldYear3 = 2018 //刑事案件
    let years1 = this.dayjs().year() - oldYear1
    // let years2 = this.dayjs().year() - oldYear2
    // let years3 = this.dayjs().year() - oldYear3
    console.log(years1)
    for (let i = 0; i < years1 + 1; i++) {
      // console.log(1111, oldYear1 + i)
      this.wgyearList.push(oldYear1 + i)
    }
  },
  methods: {
    closeEmitai() {
      this.$emit('closeEmitai')
    },
    wgsjYearMethod(item) {
      this.wgcurrentYear = item
    },
    wgsjMonthMethod(item) {
      this.wgcurrentMonth = item
    }
  }
}
</script>

<style lang="less" scoped>
@keyframes rotateS {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
.tkBox {
  width: 1135px;
  height: 843px;
  background: url(~@/assets/hszl/tkBg.png) no-repeat center / 100% 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10000;
  padding: 0 43px;
  .tkTitle {
    width: 634px;
    height: 74px;
    background: url(~@/assets/hszl/tkTitle.png) no-repeat center / 100% 100%;
    margin: 0 auto;
    margin-top: 22px;
    line-height: 74px;
    span {
      font-size: 36px;
      font-family: PingFangSC, PingFang SC;
      color: #ffffff;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .tkGb {
    position: absolute;
    top: 38px;
    right: 43px;
  }
  .tkList {
    float: left;
    margin: 0 32px;
    margin-top: 26px;
    position: relative;
    .wrapper {
      width: 460px;
      height: 296px;
      .select_ {
        position: absolute;
        right: 36px;
        top: -28px;
        ::v-deep .el-dropdown button {
          width: 88px;
          height: 24px;
          border-radius: 12px;
          background: url(~@/assets/shzl/select_bg.png) no-repeat center / 100% 100%;
          border: none;
          padding: 0;
          // background: linear-gradient(180deg, #0e63ac 0%, #253c69 100%);
          // border-radius: 2px;
          // border: 2px solid;
          // border-image: linear-gradient(180deg, rgba(63, 148, 224, 0.2), rgba(218, 250, 255, 0.3))
          //   2 2;
        }
      }
      .select_month {
        position: absolute;
        right: 52px;
        top: -27px;
        ::v-deep .el-dropdown button {
          width: 56px;
          height: 24px;
          right: -46px;
          top: -28px;
          border-radius: 12px;
          background: url(~@/assets/shzl/select_bg.png) no-repeat center / 100% 100%;
          border: none;
          padding: 0;
          // background: linear-gradient(180deg, #0e63ac 0%, #253c69 100%);
          // border-radius: 2px;
          // border: 2px solid;
          // border-image: linear-gradient(180deg, rgba(63, 148, 224, 0.2), rgba(218, 250, 255, 0.3))
          //   2 2;
        }
      }
    }
    .ybsscont {
      height: 100%;
      // padding: 14px 44px 24px 37px;
    }
    .wrapper2 {
      height: 100%;
    }
    .wrapper4 {
      padding-top: 10px;
      .wrapperList {
        width: 423px;
        height: 50px;
        margin: 0 auto;
        .wrapperList1 {
          overflow: hidden;
          margin-top: 6px;
          .wrapperLeft {
            float: left;
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 20px;
          }
          .wrapperRight {
            float: right;
            font-size: 20px;
            font-family: DINCond-Black, DINCond;
            font-weight: 900;
            color: #ffffff;
            line-height: 24px;
            span {
              font-size: 14px;
            }
          }
        }
        .wrapperList2 {
          width: 423px;
          height: 6px;
          background: rgba(66, 104, 146, 0.6);
          border-radius: 4px;
          opacity: 0.56;
          margin-top: 7px;
          .wrapperSelected {
          }
        }
        &:nth-child(1) .wrapperList2 {
          .wrapperSelected {
            width: 85%;
            height: 6px;
            background: linear-gradient(270deg, #fc1405 0%, #ff6f4c 100%);
            border-radius: 4px;
          }
        }
        &:nth-child(2) .wrapperList2 {
          .wrapperSelected {
            width: 70%;
            height: 6px;
            background: linear-gradient(270deg, #ffb819 0%, #ffde94 100%);
            border-radius: 4px;
          }
        }
        &:nth-child(3) .wrapperList2 {
          .wrapperSelected {
            width: 65%;
            height: 6px;
            background: linear-gradient(270deg, #1afff8 0%, #bdfffe 100%);
            border-radius: 4px;
          }
        }
        &:nth-child(4) .wrapperList2 {
          .wrapperSelected {
            width: 54%;
            height: 6px;
            background: linear-gradient(270deg, #12acff 0%, #9bdbff 100%);
            border-radius: 4px;
          }
        }
        &:nth-child(5) .wrapperList2 {
          .wrapperSelected {
            width: 54%;
            height: 6px;
            background: linear-gradient(270deg, #12acff 0%, #9bdbff 100%);
            border-radius: 4px;
          }
        }
      }
    }
  }
}
</style>
