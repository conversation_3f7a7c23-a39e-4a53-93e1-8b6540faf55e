<!--
 * @Author: srd18862913192 <EMAIL>
 * @Date: 2023-01-10 12:08:02
 * @LastEditors: srd18862913192 <EMAIL>
 * @LastEditTime: 2023-01-12 10:08:22
 * @FilePath: \ywtgdp\src\components\shzl\cameraPop.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="ai_waring">
    <div class="title">
      <span>视频连线</span>
      <img @click="closeEmitCamera" class="close_btn" src="@/assets/shzl/map/close_btn.png" alt="" /> 
    </div>
    <div class="content">
      <div>
        <img class="camera1" src="@/assets/shzl/map/camera1.png" alt="" />
        <img class="camera2" src="@/assets/shzl/map/camer_top.png" alt="" />
        <img class="close_camera" src="@/assets/shzl/map/close_camera.png" alt="" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  methods: {
    closeEmitCamera() {
      this.$emit('closeEmitCamera')
    },
  },
}
</script>

<style lang='less' scoped>
.ai_waring {
  width: 413px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -40%);
  // border: 1px solid red;
  background: url(~@/assets/shzl/map/ai_popBg.png) no-repeat center / 100% 100%;
  background-size: 100% 100%;
   z-index: 999;
  .title {
    background: url(~@/assets/shzl/map/title_bg.png) no-repeat center / 100% 100%;
    background-size: 100% 100%;
    width: 375px;
    height: 47px;
    margin: 20px auto 0;
    line-height: 47px;
    text-align: left;
    text-indent: 27px;
    font-size: 18px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #ffffff;
    position: relative;
    span {
      background: linear-gradient(180deg, #ffffff 0%, #2aa6ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .close_btn {
      position: absolute;
      top: 11px;
      right: 25px;
      cursor: pointer;
    }
  }
  .content {
    padding: 6px 38px 40px 35px;
    & > div {
      position: relative;
      .camera1 {
        width: 339px;
        height: 498px;
      }
      .camera2 {
        width: 106px;
        height: 139px;
        position: absolute;
        top: 0;
        right: 0;
      }
      .close_camera {
        width: 53px;
        height: 53px;
        position: absolute;
        top: 392px;
        right: 143px;
        cursor: pointer;
      }
    }
  }
}
</style>