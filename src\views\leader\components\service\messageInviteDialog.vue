<template>
  <div class="message-invite" @click.self="$emit('messageInviteClose')">
    <el-input v-model="callingMobile" placeholder="请输入手机号码"></el-input>
    <div class="message-invite-btn" @click="callByBoard">短信邀请</div>
  </div>
</template>

<script>
  import { validateCallNum } from "./tool/index"
  export default {
    name: 'messageInviteDialog',
    data() {
      return {
        callingMobile: ""
      }
    },
    methods: {
      callByBoard() {
        if(validateCallNum(this.callingMobile)) {
          this.$emit('sendMessageInvite', this.callingMobile)
        } else {
          this.$message({
            type: 'error',
            message: '手机号不合法，请重新输入'
          })
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .message-invite {
    width: 341px;
    height: 79px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background: url('~@/assets/service/board-bg.png') no-repeat center / 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    &-btn {
      width: 88px;
      line-height: 36px;
      text-align: center;
      background: url('~@/assets/service/board-btn-bg.png') no-repeat center / 100% 100%;
      cursor: pointer;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #FFFFFF;
    }
    ::v-deep {
      .el-input {
        width: 200px !important;
        margin-right: 10px;
        .el-input__inner {
          height: 32px;
          line-height: 32px;
          border: 1px solid #CBE5FF !important;
          background-color: transparent !important;
          font-size: 16px !important;
          color: #FFFFFF !important;
        }
      }
    }
  }
</style>