<!--
 * @Author: fanjialong 
 * @Date: 2023-12-12 17:28:16
 * @LastEditors: fanjialong 
 * @LastEditTime: 2023-12-21 11:25:21
 * @FilePath: /sjcz_cq_qd/src/views/admin/system-page/login/singleLogin.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<style lang="less">
// @import "./login.less";
</style>

<template>
	<div class="login-con"></div>
</template>

<script>
// import SecurityCode from '../../components/SecurityCode'
// import { format } from 'url'
// import { getDefaultUploadUrl } from "@/api/admin/sjCenter/createOrder.js";
export default {
	data() {
		return {
			userName: '',
			token: '',
			password: '',
			value: '',
			biyiCaptchaKey: '',
		}
	},
	components: {
		// SecurityCode
	},
	methods: {
		init() {
			console.log(777)
			let url = window.location.href
			// 获取参数部分
			let params = url.split('?')[1]
			// 将参数部分转换为对象
			let paramsObj = {}
			if (params) {
				let paramsArr = params.split('&')
				for (let i = 0; i < paramsArr.length; i++) {
					let param = paramsArr[i].split('=')
					paramsObj[param[0]] = param[1]
				}
			}
			// 获取指定参数的值
			let paramValue = paramsObj['token']
			if (paramValue) {
				localStorage.setItem('token', decodeURI(paramValue))
			}
			console.log('token1111', localStorage.token)

			// this.$Cookies.set('token', this.token) // 存储
			// this.$router.replace('/zl')
			this.$router.replace('/jxhfz')

			// this.getDefaultUploadUrlFn() // 获取上传默认地址，表单设计那块使用
		},
		// getDefaultUploadUrlFn(){
		//   getDefaultUploadUrl().then(res=>{
		//     if(res.data.code==0){
		//       sessionStorage.setItem('defaultUploadUrl',this.$util.baseUrl + res.data.data)
		//     }
		//   })
		// }
	},
	mounted() {
		this.init()
	},
	// created() {
	//   this.handleSubmit();
	// }
}
</script>

<style lang="less">
.replacement {
	position: absolute;
	opacity: 0;
}

.demo-spin-icon-load {
	animation: ani-demo-spin 1s linear infinite;
}

@keyframes ani-demo-spin {
	from {
		transform: rotate(0deg);
	}

	50% {
		transform: rotate(180deg);
	}

	to {
		transform: rotate(360deg);
	}
}
</style>
