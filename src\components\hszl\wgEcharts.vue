<!--
 * @Author: fanjialong <EMAIL>
 * @Date: 2023-06-01 16:39:54
 * @LastEditors: fanjialong 
 * @LastEditTime: 2023-09-09 09:52:03
 * @FilePath: \hs_dp\src\views\leader\testEcharts.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div
    id="echartsId"
    ref="echartsRef"
    style="
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 999;
      cursor: move;
    "
  ></div>
</template>

<script>
import * as echarts from 'echarts'
import chinaJson from '@/components/common/echarts/hswg.json'

export default {
  data() {
    return {
      chart: null,
      isDragging: false,
      startX: 0,
      startY: 0,
      lastX: 0,
      lastY: 0,
    }
  },
  mounted() {
    this.loadMap()
    window.addEventListener('resize', this.resizeHandler)
    this.chart.on('click', this.handleMapClick)
    this.chart.getZr().on('mousedown', this.handleMouseDown)
    this.chart.getZr().on('mousemove', this.handleMouseMove)
    this.chart.getZr().on('mouseup', this.handleMouseUp)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeHandler)
    this.chart.getZr().off('mousedown', this.handleMouseDown)
    this.chart.getZr().off('mousemove', this.handleMouseMove)
    this.chart.getZr().off('mouseup', this.handleMouseUp)
  },
  methods: {
    loadMap() {
      this.chart = echarts.init(this.$refs.echartsRef)
      echarts.registerMap('chinaJson', chinaJson)

      this.chart.setOption({
        series: [
          {
            type: 'map',
            roam: true,
            map: 'chinaJson',
            label: {
              show: false,
            },
            data: [],
            itemStyle: {
              areaColor: '#54E9EF',
              emphasis: {
                areaColor: '#2462BD',
                label: {
                  show: true,
                  color: '#fff',
                  fontSize: 20,
                },
              },
            },
          },
        ],
      })
    },
    resizeHandler() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    handleMapClick(params) {
      console.log('sqName', params)
      this.$emit('wgInfoItem', params.name)
    },
    handleMouseDown(event) {
      this.isDragging = true
      this.startX = event.offsetX
      this.startY = event.offsetY
      this.lastX = event.offsetX
      this.lastY = event.offsetY
      this.chart.getZr().setCursorStyle('move')
    },
    handleMouseMove(event) {
      if (this.isDragging) {
        const offsetX = event.offsetX
        const offsetY = event.offsetY
        const deltaX = offsetX - this.lastX
        const deltaY = offsetY - this.lastY

        this.lastX = offsetX
        this.lastY = offsetY

        this.chart.dispatchAction({
          type: 'geoMove',
          seriesId: 'seriesId', // Replace 'seriesId' with the actual series ID
          deltaX: -deltaX,
          deltaY: -deltaY,
        })
      }
    },
    handleMouseUp() {
      this.isDragging = false
      this.chart.getZr().setCursorStyle('default')
    },
  },
}
</script>

<style>
</style>
