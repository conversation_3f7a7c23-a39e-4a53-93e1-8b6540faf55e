<template>
  <div class="chart" ref="chart"></div>
</template>

<script>
import HighCharts from 'highcharts'

import { cloneDeep } from 'lodash'

export default {
  name: '<PERSON><PERSON><PERSON><PERSON>',
  props: {
    unit: {
      type: String,
      default: '人',
    },
    data: {
      type: Array,
      default: () => [
        {
          name: '城市管理',
          y: 24,
          depth: 35,
        },
        {
          name: '公共安全',
          y: 12,
          depth: 20,
        },
        {
          name: '社会治安',
          y: 10,
          depth: 20,
        },
        {
          name: '民生服务',
          y: 6,
          depth: 20,
        },
      ],
    },
    colors: {
      type: Array,
      default: () => [
        'rgba(90, 243, 184, 1)',
        'rgba(77, 116, 255, 1)',
        'rgba(147, 219, 255, 1)',
        'rgba(255, 217, 130, 1)',
        '#ef8036',
        '#be30ff',
      ],
    },
  },
  data() {
    return {
      option: null,
      chartInstance: null,
      timer: null,
      optionData: null,
      currentIndex: 0,
    }
  },
  computed: {
    total() {
      return this.optionData.reduce((pre, cur) => {
        return (pre += cur.y)
      }, 0)
    },
  },
  watch: {
    data(newVal) {
      this.chartInstance.series[0].update({
        data: newVal,
      })
    },
  },
  beforeDestroy() {
    this.timer && clearInterval(this.timer)
    // 销毁图表
    this.chartInstance && this.chartInstance.destroy()
  },
  created() {
    this.optionData = cloneDeep(this.data)
  },
  mounted() {
    this.init()
    this.timer = setInterval(() => {
      this.optionData[this.currentIndex]['depth'] = 20
      this.currentIndex++
      if (this.currentIndex === this.optionData.length) {
        this.currentIndex = 0
      }
      this.optionData[this.currentIndex]['depth'] = 35

      this.chartInstance.series[0].update({
        data: this.optionData,
      })
      const { name, y } = this.optionData[this.currentIndex]
      // const percent = ((y / this.total) * 100).toFixed(1) + '%';
      // 更新标题 第一个参数 标题 2. 副标题 3. 是否重绘
      this.chartInstance.setTitle(
        {
          text: y + this.unit +  '\n' + ((y / this.total) * 100).toFixed(2) + '%',
        },
        {
          text: name,
        },
        false
      )
    }, 5000)
  },
  methods: {
    init() {
      //日总存煤量
      this.chartInstance = HighCharts.chart(this.$refs.chart, {
        chart: {
          animation: true,
          type: 'pie',
          backgroundColor: 'transparent',
          events: {
            load: function () {
              let each = HighCharts.each,
                points = this.series[0].points
              each(points, function (p) {
                p.graphic.attr({
                  translateY: -p.shapeArgs.ran,
                })
                p.graphic.side1.attr({
                  translateY: -p.shapeArgs.ran,
                })
                p.graphic.side2.attr({
                  translateY: -p.shapeArgs.ran,
                })
              })
            },
          },
          options3d: {
            enabled: true,
            // 延y轴向内的倾斜角度
            alpha: 55,
            // 外旋转角度
            beta: 0,
          },
        },
        title: {
          text:
            this.optionData[this.currentIndex].y +
            this.unit + '\n'
            +((this.optionData[this.currentIndex].y / this.total) * 100).toFixed(2) +
            '%',
          align: 'center',
          y: 70,
          style: {
            color: 'rgba(255, 255, 255, 1)',
            fontSize: '16px',
            fontWeight: 'bold',
          },
        },
        subtitle: {
          text: this.optionData[this.currentIndex].name,
          align: 'center',
          y: 90,
          style: {
            color: 'rgba(255, 255, 255, 1)',
            fontSize: '16px',
          },
        },
        legend: {
          //图例
          layout: 'horizontal',
          verticalAlign: 'bottom',
          align: 'center',
          y: 10,
          x: 0,
          useHTML: true,
          symbolRadius: 2,
          symbolWidth: 10,
          symbolHeight: 10,
          symbolPadding: 6,
          itemDistance: 4,
          itemMarginTop: 6,
          labelFormatter: function () {
            return this.name
          },
          itemStyle: {
            color: 'rgba(255, 255, 255, 0.85)',
            fontWeight: '400',
            fontSize: '14px',
            fontFamily: 'DINPro-Medium',
            letterSpacing: '3px',
          },
          // hover 效果
          itemHoverStyle: {
            color: 'rgba(255, 255, 255, 0.55)',
          },
        },
        tooltip: {
          enabled: false,
        },
        credits: {
          enabled: false,
        },
        plotOptions: {
          series: {
            animation: true,
            events: {
              //控制图标的图例legend不允许切换
              legendItemClick: function () {
                return false //return  true 则表示允许切换
              },
            },
            states: {
              // 设置 hover 不可用
              inactive: {
                opacity: 1,
              },
              hover: {
                enabled: false,
              },
            },
          },
          pie: {
            allowPointSelect: true,
            center: ['50%', '5%'],
            size: 200,
            innerSize: 140,
            colors: this.colors,
            // 控制 每个区域的指示
            dataLabels: {
              enabled: false,
            },
          },
        },
        series: [
          {
            name: '青磁窑矿',
            data: this.data,
            showInLegend: true,
            dataLabels: {
              padding: -10,
              shadow: true,
              position: 'center',
              style: {
                fontSize: '14px',
                color: '#fff',
                textOutline: '1px 1px transparent',
              },
              formatter: function () {
                return (
                  this.key + '<br/>' + '<p >' + ((this.y / this.total) * 100).toFixed(2) + '%</p>'
                )
              },
            },
          },
        ],
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 100%;
  position: relative;
  &::after {
    content: '';
    position: absolute;
    top: -14px;
    left: 42px;
    display: block;
    width: 80%;
    height: 96%;
    z-index: -1;
    // background: url(~@/assets/leader/img/leader_ldzh/chart-bg.png) no-repeat center / 100% 100%;
  }
}
::v-deep .highcharts-container {
  z-index: 1 !important;
}
</style>
