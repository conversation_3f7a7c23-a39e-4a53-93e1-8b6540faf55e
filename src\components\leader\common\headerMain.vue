<template>
	<div class="header">
		<div class="title">
			<div class="header-left-box">
				<div class="nongji-icon-box"></div>
				<div class="app-title-box"></div>
				<div class="app-tab-box">
					<div
						class="app-tab-item"
						:class="currentIndex == i ? 'app-active' : 'no-active'"
						v-for="(it, i) in appBtns"
						:key="i"
						@click="changeBtn(i)"
					>
						{{ it }}
					</div>
				</div>
			</div>
			<div class="header-right-box">
				<div class="time-info-box">
					<div class="time-info">
						<span class="time1">{{ time }}</span>
						<span class="time2">{{ time1 }}</span>
					</div>
					<div class="devider"></div>
					<div class="close-system-box" @click="tcdl">
						<div class="close-icon" :title="username"></div>
						<div class="close-tip">退出</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
// import { tianqiapi } from '@/api/common/common'
// import { tymhUrl } from '@/utils/leader/const'
import { screenLogout, cscpCurrentUserDetails } from '@/api/bjnj/zhdd.js'
export default {
	props: {
		icons: {
			type: Array,
			default() {
				return ['城市体征', '统一门户', '指挥调度', '监测预警', '事件处置', '考核研判']
			},
		},
	},
	data() {
		return {
			areaName: '',
			roleNames: '',
			username: '',
			currentIndex: 0,
			// appBtns: ['实时态势', '指挥调度', '机械化发展', '机械化生产'],
			appBtns: ['机械化发展', '实时态势', '指挥调度', '作业进度'],
			// appBtns: ['机械化发展', '实时态势', '指挥调度'],


			timer: null,
			time: '',
			time1: '',
			time2: '',
			title1: '',
			title2: '',
			title3: '',
			title4: '',
			tq: {},
			showChildMenu: false,
			bgShow2: 0,
			bgShow3: 0,
			bgShow5: 0,
		}
	},
	methods: {
		async cscpCurrentUserDetails(data) {
			console.log(987654321)
			let res = await cscpCurrentUserDetails(data)
			this.title1 = ''
			this.title2 = ''
			this.title3 = ''
			this.title4 = ''
			console.log(res)
			if (res?.code == '0') {
				this.roleNames = res.data.roleNames
				this.username = res.data.username
				if (res.data.areaLevel == 0) {
					this.title1 = '全国'
				} else if (res.data.areaLevel == 1) {
					this.title2 = res.data.areaName
				} else if (res.data.areaLevel == 2) {
					this.title3 = res.data.areaName
				} else if (res.data.areaLevel == 3) {
					this.title4 = res.data.areaName
				}
				if (res.data.areaName) {
					if (res.data.areaLevel == 0) {
						this.areaName = '全国'
					} else {
						this.areaName = res.data.areaName
					}
				}
			}
		},

		tcdl() {
			let cookies = document.cookie.split(';')
			for (let i = 0; i < cookies.length; i++) {
				let cookie = cookies[i]
				let eqPos = cookie.indexOf('=')
				let name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie
				document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/'
			}
			if (cookies.length > 0) {
				for (let i = 0; i < cookies.length; i++) {
					let cookie = cookies[i]
					let eqPos = cookie.indexOf('=')
					let name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie
					let domain = location.host.substr(location.host.indexOf('.'))
					document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; domain=' + domain
				}
			}
			this.screenLogout()
		},
		async screenLogout(data) {
			let res = await screenLogout(data)
			console.log(res)
			if (res?.code == '0') {
				localStorage.setItem('token', '')
				window.location.href = res.data
			}
		},
		changeBtn(index) {
			this.currentIndex = index
			this.$store.commit('setCurrentIndex', this.currentIndex)

			console.log('index12346789', index)
			this.cscpCurrentUserDetails()
			this.$store.commit('invokerightShow4', false)
			switch (this.currentIndex) {
				case 0:
					this.$router.push('/jxhfz')
					break
				case 1:
					this.$router.push('/zl')
					break
				case 2:
					// this.$router.push('/mskx')
					this.$router.push('/zhdd2')
					break
				case 3:
					// this.$router.push('/zyzx')
					this.$router.push('/zyjd')
					break
				default:
					break
			}
		},
		//  时间格式化
		getTime() {
			let myDate = new Date()
			let wk = myDate.getDay()
			const month = this.dayjs().month() + 1 // 月份从0开始，所以要加1
			const day = this.dayjs().date()
			let weeks = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']

			this.time2 = weeks[wk]

			this.time = `${month}月${day}日 ${this.time2}`

			this.time1 = this.dayjs().format('HH:mm:ss')
		},
		// 天气
		// gettq () {
		//   tianqiapi().then(res => {
		//     // console.log(999, res)
		//     this.tq = res.lives[0]
		//   })
		// },
		clickIcon(i) {
			console.log(i)
		},
	},
	watch: {
		$route(to, from) {
			console.log('to.path--==', to.path)
			switch (to.path) {
				case '/jxhfz':
					this.currentIndex = 0
					break
				case '/zl':
					this.currentIndex = 1
					break
				case '/zhdd2':
					this.currentIndex = 2
					break

				// case '/zyzx':
				case '/zyjd':

					this.currentIndex = 3
					break
				// case '/zhdd':
				//   this.currentIndex = 4
				//   break
				// case '/aqjg':
				//   this.currentIndex = 5
				//   break
				// case '/fxyp':
				//   this.currentIndex = 5
				//   break
				// case '/tymh':
				//   this.currentIndex = 6
				//   break
				default:
					break
			}
		},
		invokeRHtx(newValue, oldValue) {
			console.log('融合通信状态：', 'newValue' + newValue, 'oldValue' + oldValue)
		},
		invokerightShow2(newValue) {
			this.bgShow2 = newValue
			console.log('this.bgShow2', this.bgShow2)
			if (this.bgShow2.level == 'province') {
				this.title1 = ''
				this.title2 = this.bgShow2.name
			}
			if (this.bgShow2.level == 'city') {
				this.title1 = ''
				this.title3 = '●' + this.bgShow2.name
			}
			if (this.bgShow2.level == 'district') {
				this.title1 = ''
				this.title4 = '●' + this.bgShow2.name
			}
		},
		invokerightShow3(newValue) {
			console.log('newValue123', newValue)
			if (newValue == '100000' || newValue == '0') {
				this.title1 = '全国'
			}
			console.log('this.title4', this.title4)
			console.log('this.title3', this.title3)
			console.log('this.title2', this.title2)
			console.log('this.title1', this.title1)
			if (this.title4) {
				this.title4 = ''
			} else if (this.title3) {
				this.title3 = ''
			} else if (this.title2) {
				this.title2 = ''
				this.title1 = '全国'
			}
		},
		invokerightShow5(newValue) {
			this.bgShow5 = newValue
			console.log('this.bgShow5', this.bgShow5)
			if (this.bgShow5.level == '1') {
				if (this.title1) {
					this.title1 = ''
					this.title2 = this.bgShow5.areaName
				}
			}
			if (this.bgShow5.level == '2') {
				if (this.title1) {
					this.title1 = ''
					this.title3 = this.bgShow5.areaName
				} else {
					this.title3 = '●' + this.bgShow5.areaName
				}
			}
			if (this.bgShow5.level == '3') {
				if (this.title1) {
					this.title1 = ''
					this.title4 = this.bgShow5.areaName
				} else {
					this.title4 = '●' + this.bgShow5.areaName
				}
			}
		},
		headerMainCurrentIndex(newValue) {
			if (newValue == -1) {
			} else {
				this.changeBtn(newValue)
			}
		},
	},
	mounted() {
		this.cscpCurrentUserDetails()
		this.getTime()
		this.timer = setInterval(this.getTime, 1000)
		this.$store.commit('setCurrentIndex', this.currentIndex)
		// this.gettq()
		// this.tqtime = setInterval(this.gettq, 3600000)
	},
	beforeDestroy() {
		clearInterval(this.timer)
	},
	computed: {
		invokeRHtx() {
			return this.$store.state.rhtxState
		},
		invokerightShow2() {
			return this.$store.state.rightShow2
		},
		invokerightShow3() {
			return this.$store.state.rightShow3
		},
		invokerightShow5() {
			return this.$store.state.rightShow5
		},
		headerMainCurrentIndex() {
			return this.$store.state.headerMainCurrentIndex
		},
	},
}
</script>

<style lang="less" scoped>
.header {
	// width: 1920px;
	// height: 80px;
	position: relative;
	z-index: 1003;
	.title {
		position: absolute;
		top: 0;
		left: 0;
		width: 1920px;
		height: 80px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		z-index: 999;
		padding: 0 34px 0 24px;
		background: url(~@/assets/leader/img/component/header/header-banner.png);
		/* 背景不重复 */
		background-repeat: no-repeat;

		/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
		background-size: cover;

		/* 背景居中显示（可选） */
		background-position: center;
		.header-left-box {
			height: 80px;
			display: flex;
			justify-content: flex-start;
			align-items: center;

			.nongji-icon-box {
				width: 45px;
				height: 45px;
				background: url(~@/assets/leader/img/component/header/header-nongji-icon.png);
				/* 背景不重复 */
				background-repeat: no-repeat;
				/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
				background-size: cover;
				/* 背景居中显示（可选） */
				background-position: center;
				margin-right: 64px;
			}

			.app-title-box {
				width: 520px;
				height: 52px;
				background: url(~@/assets/leader/img/component/header/header-title-bg.png);
				/* 背景不重复 */
				background-repeat: no-repeat;
				/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
				background-size: cover;
				/* 背景居中显示（可选） */
				background-position: center;
				margin-right: 34px;
			}
			.app-tab-box {
				width: 870px;
				height: 100%;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				.app-tab-item {
					width: 228px;
					height: 38px;
					display: flex;
					justify-content: center;
					align-items: center;
					font-family: PangMenZhengDao-3, PangMenZhengDao-3;
					font-weight: bold;
					font-size: 20px;
					color: #64b4ff;
					text-align: center;
					font-style: normal;
					text-transform: none;

					cursor: pointer;

					&.app-active {
						font-family: PangMenZhengDao-3, PangMenZhengDao-3;
						font-weight: 500;
						font-size: 20px;
						color: #ffffff;
						text-align: center;
						font-style: normal;
						text-transform: none;
						background: linear-gradient(90.00000000003779deg, #ffffff 56%, #cde4ff 78%, #95ccff 100%);

						background: url(~@/assets/leader/img/component/header/header-app-tag-active.png);
						/* 背景不重复 */
						background-repeat: no-repeat;
						/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
						background-size: cover;
						/* 背景居中显示（可选） */
						background-position: center;
					}

					&.no-active {
						background: url(~@/assets/leader/img/component/header/header-app-tag.png);
						/* 背景不重复 */
						background-repeat: no-repeat;
						/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
						background-size: cover;
						/* 背景居中显示（可选） */
						background-position: center;
					}
				}
			}
		}

		.header-right-box {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			height: 80px;
			.time-info-box {
				height: 100%;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				margin-right: 20px;

				.time-info {
					height: 100%;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: flex-start;
					.time1 {
						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						font-size: 10px;
						color: #b0e0ff;
						line-height: 10px;
						text-align: left;
						font-style: normal;
						text-transform: none;
						margin-bottom: 4px;
					}
					.time2 {
						font-family: PingFang SC, PingFang SC;
						font-weight: 600;
						font-size: 20px;
						color: #b0e0ff;
						line-height: 20px;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}
				}

				.devider {
					width: 1px;
					height: 22px;
					background-color: #b0e0ff;
					margin: 0 24px;
				}
			}
			.close-system-box {
				width: 26px;
				height: 100%;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				cursor: pointer;
				.close-icon {
					width: 11px;
					height: 11px;
					background: url(~@/assets/leader/img/component/header/header-close-icon.png);
					/* 背景不重复 */
					background-repeat: no-repeat;
					/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
					background-size: cover;
					/* 背景居中显示（可选） */
					background-position: center;
					margin-bottom: 5px;
					cursor: pointer;
				}

				.close-tip {
					width: 26px;
					height: 20px;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 13px;
					color: #b0e0ff;
					line-height: 20px;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}
			}
		}
	}
}
</style>
