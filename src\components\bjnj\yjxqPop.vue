<template>
  <div class="event" v-if="show">
    <div class="close" @click="closeFn"></div>
    <div class="title">
      <span>预警详情</span>
    </div>
    <div class="content">
      <div class="contentTitle">{{list[6]}}</div>
      <div class="contentTable">
        <div class="contentItem">
          <div class="contentLeft">预警类型：</div>
          <div class="contentRight">{{list[2]}}预警</div>
        </div>
        <div class="contentItem">
          <div class="contentLeft">预警时间：</div>
          <div class="contentRight">{{list[1]}}</div>
        </div>
        <div class="contentItem">
          <div class="contentLeft">预警区域：</div>
          <div class="contentRight">{{list[3]}}</div>
        </div>
        <div class="contentItem">
          <div class="contentLeft">预警等级：</div>
          <div class="contentRight">{{list[4]}}预警</div>
        </div>
      </div>
      <div class="contentCont">{{list[5]}}</div>
    </div>
    <!-- <div class="btns">
      <div class="btn" v-for="(item,index) in btns" :key="index" @click="handle(index)">{{item}}</div>
    </div> -->
  </div>
</template>

<script>
export default {
  name: 'EventDialog',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default: () => {
        return []
      }
    },
    btns:{
      type:Array,
      default:()=>[
        '取消','确定'
      ]
    }
  },
  computed: {
    show() {
      return this.value
    }
  },
  methods: {
    closeFn() {
      this.$emit('input', false)
    },
    handle(i) {
      this.$emit('handle', i)
    }
  }
}
</script>

<style lang="less" scoped>
.event {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1111;
  width: 683px;
  // height: 262px;
  padding: 20px 0 0 0;
  margin: 0 auto;
  background: url('~@/assets/map/dialog/bg9.png') no-repeat center / 100% 100%;
  box-sizing: border-box;
  .close {
    position: absolute;
    top: 31px;
    right: 43px;
    width: 24px;
    height: 24px;
    background: url('~@/assets/map/dialog/btn2.png') no-repeat;
    box-sizing: border-box;
    cursor: pointer;
  }
  .title {
    width: 643px;
    height: 47px;
    line-height: 47px;
    padding-left: 27px;
    margin: 0 auto;
    text-align: left;
    background: url('~@/assets/map/dialog/title5.png') no-repeat;
    box-sizing: border-box;
    span {
      font-size: 18px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      background: linear-gradient(180deg, #ffffff 0%, #2aa6ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .content {
    padding: 29px 50px 58px 46px;
    .contentTitle {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 20px;
      color: #FFFFFF;
      line-height: 28px;
      text-align: center;
      font-style: normal;
      background: linear-gradient(180deg, #FFFFFF 0%, #00D4FF 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .contentTable {
      width: 400px;
      margin: 0 auto;
      margin-top: 13px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-evenly;
      align-content: space-between;
      .contentItem {
        width: 50%;
        margin-bottom: 7px;
        .contentLeft {
          float: left;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #ffffff;
          line-height: 17px;
          text-align: center;
          font-style: normal;
        }
        .contentRight {
          float: left;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #FFFFFF;
          line-height: 17px;
          text-align: center;
          font-style: normal;
        }
      }
    }
    .contentCont {
      margin-top: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 300;
      font-size: 14px;
      color: #FFFFFF;
      line-height: 26px;
      text-align: left;
      font-style: normal;
    }
  }
}
</style>
