<template>
	<div>
		<div class="event_detail">
			<div class="title">
				<span>农机信息</span>
			</div>
			<div class="close" @click="close"></div>
			<div class="content">
				<BlockBox title="基本信息" :isListBtns="false" class="box1" :blockHeight="233">
					<ul>
						<li v-for="(item, index) in infoData" :key="index">
							<span class="name">{{ item.name }}：</span>
							<span class="value">{{ item.value }}</span>
						</li>
					</ul>
				</BlockBox>
				<!-- <BlockBox title="工况信息" :isListBtns="false" :blockHeight="237">
          <div class="box2">
            <div class="box2_item" v-for="(item, index) in workData" :class="workData.length > 0 ? '' : 'box2_items'"
              :key="index">
              <div class="name" :title="item.ocParamName">{{ item.ocParamName }}</div>
              <div class="value" :title="item.value">{{ item.value }}</div>
              <div class="unit" :title="item.ocParamDesc">{{ item.ocParamDesc }}</div>
            </div>
          </div>
        </BlockBox> -->
				<div class="button-group">
					<div class="button" @click="handleClick('history')">
						<span>历史作业</span>
					</div>
					<div v-if="realTimeButtonShow" class="button" @click="handleClick('realTime')">
						<span>实时轨迹</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import BlockBox from '@/components/leader/common/blockBox7.vue'
// import { getAgmachWorkConditionNew } from '@/api/hs/hs.api.js'
import { getAgmachWorkConditionNew } from '@/api/bjnj/zhdd.js'
import { agmachToTermId } from '@/api/njzl/hs.api.js'
export default {
	name: 'njInfo',
	components: {
		BlockBox,
	},
	props: {
		agmachInfo: {
			required: true,
			type: Object,
		},
    realTimeButtonShow: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			infoData: [
				{
					name: '农机编号',
					value: '-',
				},
				// {
				//   name: '农机品牌',
				//   value: '-'
				// },
				{
					name: '农机品目',
					value: '-',
				},
				// {
				//   name: '累计作业量',
				//   value: '-'
				// },
				{
					name: '联系人',
					value: '-',
				},
				{
					name: '车牌号',
					value: '-',
				},
				{
					name: '联系方式',
					value: '-',
				},
				// {
				//   name: '农机状态',
				//   value: '-'
				// },
				// {
				//   name: '所属单位',
				//   value: '-'
				// },
				{
					name: '所在区域',
					value: '-',
				},
				{
					name: '联系人地址',
					value: '-',
				},
				{
					name: '最近在线时间',
					value: '-',
				},
			],
			workData: [
				{
					name: '每小时油耗',
					value1: '15',
					unit: 'L/h',
				},
				{
					name: '机油压力',
					value1: '15',
					unit: 'Kpa',
				},
				{
					name: '总里程',
					value1: '15',
					unit: 'km',
				},
				{
					name: '发动机工作时间',
					value1: '15',
					unit: 'h',
				},
				{
					name: '实际扭矩百分比',
					value1: '15',
					unit: '%',
				},
				{
					name: '发动机转速',
					value1: '15',
					unit: 'RPM',
				},
				{
					name: '冷却水温度',
					value1: '15',
					unit: '℃',
				},
				{
					name: '油门踏板位置',
					value1: '15',
					unit: '%',
				},
			],
		}
	},
	mounted() {
		this.getAgmachDetail()
	},
	methods: {
		close() {
			this.$emit('closeEmitai')
		},
		handleClick(type) {
			if (type == 'history') {
				this.$emit('handleClick')
			} else if (type == 'realTime') {
				this.$emit('showRealTimeTrack')
			}
		},
		getAgmachDetail() {
			let {
				motorCode,
				mdlName,
				agmachId,
				agmachTypeName,
				userName,
				proEntName,
				licensePlateCode,
				agmach_status,
				phoneNo,
				brand,
				address,
				areaName,
				latestLocTime,
			} = this.agmachInfo
			this.infoData[0].value = agmachId && agmachId != 'null' ? agmachId : '-'
			// this.infoData[1].value = brand && brand != 'null' ? brand : '-'
			this.infoData[1].value = agmachTypeName && agmachTypeName != 'null' ? agmachTypeName : '-'
			// this.infoData[3].value = '-'
			this.infoData[2].value = userName && userName != 'null' ? userName : '-'
			this.infoData[3].value = licensePlateCode && licensePlateCode != 'null' ? licensePlateCode : '-'
			this.infoData[4].value = phoneNo && phoneNo != 'null' ? phoneNo : '-'
			// this.infoData[7].value = agmach_status && agmach_status != 'null' ? agmach_status : '-'
			// this.infoData[8].value = '-'
			this.infoData[5].value = areaName && areaName != 'null' ? areaName : '-'
			this.infoData[6].value = address && address != 'null' ? address : '-'
			this.infoData[7].value = latestLocTime && latestLocTime != 'null' ? latestLocTime : '-'
			// 高德api逆编码经纬度来获取区域和位置
			// this.$http.get(`https://restapi.amap.com/v3/geocode/regeo?key=429f9459392d3d35c5b7274e7f50c129&location=${this.agmachInfo.lon},${this.agmachInfo.lat}`)
			//   .then(address => {
			//     let addressComponent = address.regeocode.addressComponent
			//     this.infoData = [
			//       { name: '农机编号', value: this.agmachInfo.agmachId || '-' },
			//       { name: '农机型号', value: this.agmachInfo.mdlName || '-' },
			//       { name: '农机品目', value: this.agmachInfo.agmachTypeName || '-' },
			//       { name: '联系人', value: this.agmachInfo.contact || '-' },
			//       { name: '所属单位', value: this.agmachInfo.areaName || '-' },
			//       { name: '车牌号', value: this.agmachInfo.licensePlateCode || '-' },
			//       { name: '所在区域', value: address.infocode == '10000' ? addressComponent.province + addressComponent.city : '-' },
			//       { name: '农机状态', value: this.agmachInfo.state || '-' },
			//       { name: '具体位置', value: address.infocode == '10000' ? address.regeocode.formatted_address : '-' },
			//       { name: '联系方式', value: this.agmachInfo.phone || '-' },
			//       { name: '农机品牌', value: this.agmachInfo.brand || '-' }
			//     ]
			//   })

			agmachToTermId({
				agmachIds: this.agmachInfo.agmachId,
			}).then((res) => {
				this.getGkInfo(res.data[0].term_agmach_id)
			})
		},
		getGkInfo(term_agmach_id) {
			// 获取工况信息
			getAgmachWorkConditionNew({
				agmachId: term_agmach_id,
				agmachTypeId: this.agmachInfo.agmachTypeCode,
			}).then((response) => {
				console.log('response', response)
				if (response.data.length > 0) {
					this.workData = response.data[0].ocData
				} else {
					this.workData = []
				}
				// let workCondition = response.data[0]?.ocData ? JSON.parse(response.data[0].ocData) : {}
				// this.workData = [
				//   { name: '每小时油耗', value: workCondition.oilConsumption || '-', unit: 'L/h' },
				//   { name: '机油压力', value: workCondition.oilPressure || '-', unit: 'Kpa' },
				//   { name: '总里程', value: workCondition.totalMileage || '-', unit: 'km' },
				//   { name: '发动机工作时间', value: workCondition.engineOperating || '-', unit: 'h' },
				//   { name: '实际扭矩百分比', value: workCondition.actualTorque || '-', unit: '%' },
				//   { name: '发动机转速', value: workCondition.engineSpeed || '-', unit: 'RPM' },
				//   { name: '冷却水温度', value: workCondition.waterTemperature || '-', unit: '℃' },
				//   { name: '油门踏板位置', value: workCondition.gasPedal || '-', unit: '%' },
				// ]
			})
		},
	},
}
</script>

<style lang="less" scoped>
.bg-header {
	position: absolute;
	width: 100%;
	height: 1080px;
	background: rgba(2, 14, 50, 0.75);
	z-index: 999999;
	top: 0;
	left: 0;
}

.event_detail {
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	width: 868px;
	height: 409px;
	background: rgba(0, 23, 59, 1);
	box-shadow: 0px 0px 43px 0px rgba(11, 54, 138, 0.35), 0px 3px 16px 0px rgba(0, 0, 0, 0.7),
		inset 0px 0px 16px 0px rgba(17, 146, 214, 0.35);
	border-radius: 11px;
	border: 1px solid #015c8c;
  z-index: 1400;


	.title {
		margin: 0 auto;
		width: 834px;
		height: 73px;
		background: url(~@/assets/csts/title1.png) no-repeat center/100% 100%;
		text-align: center;

		span {
			display: inline-block;
			font-family: PingFangSC, PingFang SC;
			font-size: 36px;
			color: #ffffff;
			line-height: 73px;
			text-align: right;
			font-style: normal;
			text-transform: none;
			background: linear-gradient(180deg, #ffffff 30%, #0079ff 70%);
			background-clip: text;
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}
	}

	.close {
		position: absolute;
		width: 23px;
		height: 23px;
		top: 25px;
		right: 18px;
		background: url(~@/assets/csts/close2.png) no-repeat center/100% 100%;
		cursor: pointer;
	}

	.content {
		width: 100%;
		height: 336px;
		padding: 42px 28px 0 29px;

		.box1 {
			width: 811px !important;
			height: 200px !important;

			ul {
				width: 100%;
				margin: 0 auto;
				display: flex;
				justify-content: space-between;
				flex-wrap: wrap;
				padding-top: 20px;

				li {
					text-align: left;
					width: 50%;
					margin-bottom: 10px;
					padding-left: 33px;
					&:nth-child(7) {
						width: 100%;
					}
					&:last-child {
						width: 100%;
					}
					.name {
						height: 17px;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 14px;
						color: rgba(106, 146, 187, 1);
						line-height: 17px;
						text-align: center;
						font-style: normal;
					}

					.value {
						height: 17px;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 14px;
						color: #ffffff;
						line-height: 17px;
						text-align: center;
						font-style: normal;
					}
				}
			}
		}

		.box2 {
			width: 611px;
			height: 204px;
			background: radial-gradient(253% 128% at 92% -1%, rgb(20, 68, 102, 0.5) 0%, rgba(3, 22, 36, 0.25) 100%), #0d141a;
			border: 1px solid #435059;
			//opacity: 0.5;
			// display: flex;
			// justify-content: space-around;
			// flex-wrap: wrap;
			// align-items: center;
			overflow-y: auto;

			&::-webkit-scrollbar {
				/*滚动条整体样式*/
				width: 6px;
				background: transparent;
				// border: 1px solid #999;
				/*高宽分别对应横竖滚动条的尺寸*/
				// height: 1px;
			}

			&::-webkit-scrollbar-thumb {
				/*滚动条里面小方块*/
				border-radius: 2px;
				box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
				background: rgb(45, 124, 228);
				height: 100px;
			}

			.box2_item {
				width: 25%;
				height: 50%;
				float: left;
				text-align: left;
				padding: 10px;

				.name {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 16px;
					color: #ffffff;
					line-height: 24px;
					text-align: left;
					font-style: normal;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}

				.value {
					font-family: PingFangSC, PingFang SC;
					font-weight: bold;
					font-size: 22px;
					color: rgba(45, 173, 232, 0.8);
					line-height: 26px;
					// text-shadow: 0px 0px 9px #006aff, 0px 5px 3px rgba(0, 0, 0, 0.7);
					text-align: left;
					font-style: normal;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					// background: linear-gradient(180deg, #2d8bea 0%, #5bf6f6 100%);
					// background-clip: text;
					// -webkit-background-clip: text;
					// -webkit-text-fill-color: transparent;
				}

				.unit {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 12px;
					color: #9da1a4;
					line-height: 24px;
					text-align: left;
					font-style: normal;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
			}

			.box2_items {
				display: none;
			}
		}

		.button-group {
			width: 100%;
			height: 40px;
			margin: 20px auto 0;
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.button {
			background: url(~@/assets/cyjj/bg_history.png) no-repeat center/100% 100%;
			width: 120px;
			height: 33px;
			margin: 0 10px;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 16px;
			color: #ffffff;
			line-height: 33px;
			text-align: center;
			font-style: normal;
			cursor: pointer;
		}
	}
}
</style>
