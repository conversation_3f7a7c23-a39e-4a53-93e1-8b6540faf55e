/*
 * @Author: fanjialong <EMAIL>
 * @Date: 2023-04-17 15:06:09
 * @LastEditors: luyu
 * @LastEditTime: 2023-10-27 15:00:44
 * @FilePath: /hs_dp/src/api/hs/hs.zhny.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import http from '@/utils/leader/request'
import {
  xtyyUrl
} from '@/utils/leader/const'

// 获取社区数据
export const getFarmInfo = () => {
  return http.get(xtyyUrl + '/api/tFarmInfo/queryAll')
}

// 24小时雨情
export const getRainfall = () => {
  return http.get(xtyyUrl + '/api/stPptnR/queryHourData')
}

// 农业合作社
export const getNyInfo = () => {
  return http.get(xtyyUrl + '/api/tRuralCooperative/queryAll')
}

// 龙头企业
export const getLtqyInfo = () => {
  return http.get(xtyyUrl + '/api/tLeadingEnterprise/queryAll')
}

// 特色产业园
export const getTscyyInfo = () => {
  return http.get(xtyyUrl + '/api/tIndustrialPark/queryAll')
}

// 近5年产值分析
export const getCzfx = () => {
  return http.get(xtyyUrl + '/api/tDpConfig/queryByModule?module=产值分析')
}

export const getJhzVideo = () => {
  return http.post('http://hushu.u-zhong.com:18090/api/ticket/main/ywtg')
}

