<template>
  <div>
    <div class="ai_waring">
      <div class="title">
        <div><span>预警分析</span></div>
        <img @click="closeEmitai" class="close_btn" src="@/assets/shzl/map/close_btn.png" alt="" />
      </div>
      <div class="tabs">
        <ul>
          <li
            :class="{ active: aTabIdx === i }"
            v-for="(it, i) of tabs"
            :key="i"
            @click="aTabIdx = i"
          >
            {{ it }}
          </li>
        </ul>
        <el-input v-model="sbmc" placeholder="预警类型"></el-input>
        <div class="search"></div>
      </div>
      <div class="content">
        <div class="table_box">
          <SwiperTableYjfx
            :titles="[
              '预警类型编号',
              '预警分类',
              '预警类型',
              '预警等级',
              '事件描述',
              '状态',
              '时间',
              '操作'
            ]"
            :widths="['15%', '10%', '10%', '8%', '25%', '7%', '15%', '10%']"
            :data="sjtjData"
            :contentHeight="'580px'"
            :tabelHeight="'58px'"
          ></SwiperTableYjfx>
        </div>
        <div class="fy_page">
          <Page :total="100"></Page>
        </div>
      </div>
    </div>
    <transition name="fade">
      <div class="bg-header"></div>
    </transition>
  </div>
</template>

<script>
import SwiperTableYjfx from '@/components/shzl/SwiperTableYjfx.vue'

export default {
  name: 'sjtjPop',
  components: { SwiperTableYjfx },
  data() {
    return {
      sbmc: '',
      tabs: ['AI视频分析', '数据分析', '物联网设备'],
      aTabIdx: 0,
      btnsArr: [
        {
          name: '未处理',
          normalBg: require('@/assets/shzl/map/left_n.png'),
          activeBg: require('@/assets/shzl/map/left_a.png')
        },
        {
          name: '已处理',
          normalBg: require('@/assets/shzl/map/right_n.png'),
          activeBg: require('@/assets/shzl/map/right_a.png')
        }
      ],
      activaIdx: 0,
      sjtjData: [
        [
          'GGAQ-AI-A001-A002',
          '公共安全',
          '机动车违停监测',
          '红',
          '汉中路123号路口监控拍到有机动车违停占道',
          '未处置',
          '2022-08-08 07:57:49'
        ],
        [
          'GGAQ-AI-A001-A002',
          '公共安全',
          '机动车违停监测',
          '红',
          '汉中路123号路口监控拍到有机动车违停占道',
          '未处置',
          '2022-08-08 07:57:49'
        ],
        [
          'GGAQ-AI-A001-A002',
          '公共安全',
          '机动车违停监测',
          '红',
          '汉中路123号路口监控拍到有机动车违停占道',
          '未处置',
          '2022-08-08 07:57:49'
        ],
        [
          'GGAQ-AI-A001-A002',
          '公共安全',
          '机动车违停监测',
          '红',
          '汉中路123号路口监控拍到有机动车违停占道',
          '未处置',
          '2022-08-08 07:57:49'
        ],
        [
          'GGAQ-AI-A001-A002',
          '公共安全',
          '机动车违停监测',
          '红',
          '汉中路123号路口监控拍到有机动车违停占道',
          '未处置',
          '2022-08-08 07:57:49'
        ],
        [
          'GGAQ-AI-A001-A002',
          '公共安全',
          '机动车违停监测',
          '红',
          '汉中路123号路口监控拍到有机动车违停占道',
          '未处置',
          '2022-08-08 07:57:49'
        ],
        [
          'GGAQ-AI-A001-A002',
          '公共安全',
          '机动车违停监测',
          '红',
          '汉中路123号路口监控拍到有机动车违停占道',
          '未处置',
          '2022-08-08 07:57:49'
        ],
        [
          'GGAQ-AI-A001-A002',
          '公共安全',
          '机动车违停监测',
          '红',
          '汉中路123号路口监控拍到有机动车违停占道',
          '未处置',
          '2022-08-08 07:57:49'
        ],
        [
          'GGAQ-AI-A001-A002',
          '公共安全',
          '机动车违停监测',
          '红',
          '汉中路123号路口监控拍到有机动车违停占道',
          '未处置',
          '2022-08-08 07:57:49'
        ]
      ],
      proityOptions: [
        {
          value: '0',
          label: '一般'
        },
        {
          value: '1',
          label: '紧急'
        }
      ],
      timeOutOptions: [
        {
          value: '0',
          label: '是'
        },
        {
          value: '1',
          label: '否'
        }
      ],
      proityValue: '',
      timeOutValue: ''
    }
  },
  methods: {
    closeEmitai() {
      this.$emit('closeEmitai')
    },
    AidealMethod() {
      this.$parent.AidealMethod()
    },
    AispthMethod() {
      this.$parent.AispthMethod()
    },
    AirwzpMethod() {
      this.$parent.AirwzpMethod()
    }
  }
}
</script>

<style lang="less" scoped>
.bg-header {
  position: absolute;
  width: 100%;
  height: 1080px;
  background: rgba(2, 14, 50, 0.75);
  z-index: 999999;
  top: 0;
  left: 0;
}
/deep/ .ivu-select-selection {
  background: rgba(0, 74, 143, 0.4)
    linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
}
/deep/ .ivu-select-selection .ivu-select-selected-value {
  font-size: 16px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 22px;
}

/deep/ .ivu-input {
  background: rgba(0, 74, 143, 0.4)
    linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
  font-size: 16px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 22px;
}
/deep/ .ivu-select-placeholder {
  font-size: 16px !important;
}
/deep/ .ivu-page-item {
  background: transparent;
  color: #fff;
}
/deep/ .ivu-page-item a {
  color: #fff;
}
/deep/ .ivu-page-prev,
/deep/ .ivu-page-next {
  background: transparent;
}
.ai_waring {
  width: 1408px;
  height: 900px;
  background: rgba(0, 23, 59, 1);
  box-shadow: 0px 0px 43px 0px rgba(11, 54, 138, 0.35), 0px 3px 16px 0px rgba(0, 0, 0, 0.7),
    inset 0px 0px 16px 0px rgba(17, 146, 214, 0.35);
  border-radius: 11px;
  border: 1px solid #015c8c;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  // border: 1px solid red;
  z-index: 99999999;
  .title {
    & > div {
      margin-left: 387px;
      width: 634px;
      height: 74px;
      background: url(~@/assets/shzl/sjtj_titlebg.png) no-repeat center / 100% 100%;
      background-size: 100% 100%;
    }
    span {
      font-size: 40px;
      font-family: YouSheBiaoTiHei;
      color: #ffffff;
      line-height: 74px;
      background: linear-gradient(180deg, #ffffff 0%, #2aebff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .close_btn {
      width: 44px;
      height: 44px;
      position: absolute;
      top: 16px;
      right: 25px;
      cursor: pointer;
    }
  }
  .content {
    // border: 1px solid red;
    padding: 27px 51px 36px 63px;
    .btns {
      // border: 1px solid red;
      margin: 0 auto;
      display: flex;
      justify-content: center;
      & > div {
        width: 154px;
        height: 53px;
        span {
          font-size: 24px;
          font-family: YouSheBiaoTiHei;
          color: #ffffff;
          line-height: 53px;
          background: linear-gradient(180deg, #ffffff 0%, #ffeba0 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
    .check_types {
      margin-top: 32px;
      text-align: left;
      display: flex;
      .jjcd {
        display: flex;
        align-items: center;
        span {
          font-size: 16px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
      }
      .report_time {
        margin-left: 26px;
        display: flex;
        align-items: center;
        span {
          font-size: 16px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
      }
      .istime_out {
        margin-left: 26px;
        align-items: center;
        display: flex;
        span {
          font-size: 16px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
      }
      .type_btns {
        margin-left: 46px;
        display: flex;
        align-items: center;
        & div {
          font-size: 16px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 43px;
          background: url(~@/assets/shzl/map/type_btn.png) no-repeat center / 100% 100%;
          background-size: 100% 100%;
          text-align: center;
          &:first-of-type {
            width: 98px;
            height: 43px;
          }
          &:nth-of-type(2) {
            margin-left: 20px;
            width: 98px;
            height: 43px;
          }
          &:nth-of-type(3) {
            margin-left: 20px;
            width: 98px;
            height: 43px;
          }
        }
      }
    }
    .table_box {
      overflow: hidden;
    }
    .fy_page {
      margin-top: 30px;
    }
  }
  .tabs {
    position: relative;
    width: 100%;
    padding: 0 51px 0 63px;
    height: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 40px;
    ul {
      width: 987px;
      height: 50px;
      background: url('~@/assets/jcyj/bg2.png') no-repeat;
      display: flex;
      align-items: center;
      padding-left: 64px;
      li {
        font-size: 20px;
        font-family: PangMenZhengDao;
        color: rgba(255, 255, 255, 0.2);
        line-height: 23px;
        letter-spacing: 2px;
        margin-right: 20px;
        cursor: pointer;
        &.active {
          color: #fff;
        }
      }
    }
    .search {
      position: absolute;
      right: 65px;
      top: 20px;
      width: 14px;
      height: 14px;
      background: url('~@/assets/jcyj/search.png') no-repeat;
      cursor: pointer;
    }
  }
}
/deep/ .el-input {
  width: 234px;
  height: 34px;
  .el-input__inner {
    background: rgba(0, 74, 143, 0.4)
      linear-gradient(
        360deg,
        rgba(181, 223, 248, 0) 0%,
        rgba(29, 172, 255, 0.29) 100%,
        #ffffff 100%
      );
    border: 1px solid rgba(0, 162, 255, 0.6);
    color: #fff;
    padding: 0 30px 0 15px;
  }
}
</style>
