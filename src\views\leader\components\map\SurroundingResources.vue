<template>
  <div class="checkbox" :style="{ left, bottom }" v-if="show">
    <ul class="check_wrap">
      <li v-for="(it, i) of list" :key="i" @click="handleCheck(it, i)">
        <div class="check" :class="{ checked: it.checked }">
          <img src="@/assets/foot/checked.png" alt v-if="it.checked" />
        </div>
        <div
          class="icon"
          :style="{
            background: it.checked
              ? `url(${it.iconActive}) no-repeat`
              : `url(${it.iconNormal}) no-repeat`
          }"
        ></div>
        <div class="label" :class="{ checked: it.checked }">{{ it.label }}</div>
        <div class="count" :class="{ checked: it.checked }">{{ it.count }}</div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'SurroundingResources',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    left: {
      type: String,
      default: '50%'
    },
    bottom: {
      type: String,
      default: '135px'
    },
    list: {
      type: Array,
      default: () => {
        return [
          {
            iconNormal: require('@/assets/foot/icon10.png'),
            iconActive: require('@/assets/foot/icon21.png'),
            label: '企业应急队伍',
            count: 3,
            checked: false
          },
          {
            iconNormal: require('@/assets/foot/icon11.png'),
            iconActive: require('@/assets/foot/icon22.png'),
            label: '加油站',
            count: 2,
            checked: false
          },
          {
            iconNormal: require('@/assets/foot/icon12.png'),
            iconActive: require('@/assets/foot/icon23.png'),
            label: '企业',
            count: 162,
            checked: false
          },
          {
            iconNormal: require('@/assets/foot/icon13.png'),
            iconActive: require('@/assets/foot/icon24.png'),
            label: '防护目标',
            count: 1,
            checked: false
          },
          {
            iconNormal: require('@/assets/foot/icon14.png'),
            iconActive: require('@/assets/foot/icon25.png'),
            label: '体外除颤仪',
            count: 7,
            checked: false
          },
          {
            iconNormal: require('@/assets/foot/icon15.png'),
            iconActive: require('@/assets/foot/icon26.png'),
            label: '医疗机构',
            count: 1,
            checked: false
          },
          {
            iconNormal: require('@/assets/foot/icon16.png'),
            iconActive: require('@/assets/foot/icon27.png'),
            label: '变电站',
            count: 4,
            checked: false
          },
          {
            iconNormal: require('@/assets/foot/icon17.png'),
            iconActive: require('@/assets/foot/icon28.png'),
            label: '二级消防单位',
            count: 13,
            checked: false
          },
          {
            iconNormal: require('@/assets/foot/icon18.png'),
            iconActive: require('@/assets/foot/icon29.png'),
            label: '视频监控',
            count: 72,
            checked: false
          },
          {
            iconNormal: require('@/assets/foot/icon19.png'),
            iconActive: require('@/assets/foot/icon30.png'),
            label: '公安队伍',
            count: 1,
            checked: false
          },
          {
            iconNormal: require('@/assets/foot/icon20.png'),
            iconActive: require('@/assets/foot/icon31.png'),
            label: '学校',
            count: 4,
            checked: false
          },
          {
            iconNormal: require('@/assets/foot/icon32.png'),
            iconActive: require('@/assets/foot/icon33.png'),
            label: '无人机',
            count: 1,
            checked: false
          }
        ]
      }
    }
  },
  computed: {
    show() {
      return this.value
    }
  },
  data() {
    return {}
  },
  methods: {
    handleCheck(it, i) {
      it.checked = !it.checked
      this.$emit('surEmit', it.checked, i, it)
    }
  }
}
</script>

<style lang="less" scoped>
.checkbox {
  position: absolute;
  width: 213px;
  // height: 368px;
  padding: 14px 16px 14px 27px;
  transform: translateX(-50%);
  background: url('~@/assets/foot/bg4.png') no-repeat center / 100% 100%;
  z-index: 999;
  .check_wrap {
    display: flex;
    flex-direction: column;
    gap: 11px;
    li {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      cursor: pointer;
      .check {
        width: 16px;
        height: 16px;
        border-radius: 2px;
        border: 1px solid #979797;
        margin-right: 6px;
        display: grid;
        place-items: center;
        &.checked {
          border: 1px solid #25ddff;
        }
      }

      .icon {
        width: 20px;
        height: 20px;
        margin-right: 4px;
      }
      .label {
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        &.checked {
          color: #25ddff;
        }
      }
      .count {
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        margin-left: auto;
        &.checked {
          color: #25ddff;
        }
      }
    }
  }
}
</style>
