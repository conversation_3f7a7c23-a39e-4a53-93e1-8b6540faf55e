<template>
	<div class="t-box" :style="{ height: `${minHeight}px`, paddingBottom: `${tabIndex == 1 ? '40px' : '10px'}` }">
		<div class="t-title-box" v-if="textArr2.length > 0">
			<div class="t-select-box" :style="{ marginTop: `${filterMarginTop}px` }">
				<IArgmachSelect
					v-show="argmachSelectShow"
					:textArr2="textArr2"
					:selected="argmachType"
					@change="handleChangeArgmach"
				></IArgmachSelect>
				<div class="t-month-type-box" v-if="showMonthTypeSelect">
					<div
						class="item-month-box"
						:class="currentMonthType == item.value ? 'item-month-active' : ''"
						v-for="(item, index) in monthTypeData"
						:key="index"
						@click="handleChangeMonthType(item.value)"
					>
						{{ item.label }}
					</div>
				</div>
			</div>
		</div>
		<div class="t-content-box" :style="{ marginTop: `${marginTop}px` }">
			<!-- :id="eChartsId" -->
			<IBarChart
				v-if="xData.length > 0 && Object.keys(yDatas).length > 0"
				:xData="xData"
				:yDatas="yDatas"
				:minHeight="charMinHeight"
				:formatterType="formatterType"
				:dataType="dataType"
				:top="top"
				:tooltipTitleName="tooltipTitleName"
				:bottom="bottom"
				:yUnitName="yUnitName"
				:xDataType="xDataType"
				:isTooltipPositionDiy="true"
				:tabIndex="tabIndex"
				xUnit=""
				:showBarBg="showBarBg"
				:yUnit="yUnit"
				:itemColor="itemColor"
				:chartsLegend="chartsLegend"
			></IBarChart>
			<div class="t-empty-content-box" v-else>
				暂无数据
				<!-- <el-empty :image-size="120" description="暂无数据"></el-empty> -->
			</div>
		</div>
		<!-- <div class="t-content-box" v-else>
			<el-empty :image-size="100" description="暂无数据"></el-empty>
		</div> -->
	</div>
</template>

<script>
import IBarChart from '@/components/common/i-bar-chart.vue'
import IArgmachSelect from '@/components/common/i-argmach-select.vue'
export default {
	name: 'draw-data-bar',
	components: {
		IBarChart,
		IArgmachSelect,
	},
	props: {
		argmachSelectShow: {
			type: Boolean,
			default() {
				return true
			},
		},
		argmachType: {
			type: String,
			default() {
				return ''
			},
		},
		xData: {
			type: Array,
			default() {
				return []
			},
		},
		yDatas: {
			type: Object,
			default() {
				return {}
			},
		},
		selected: {
			type: String,
			default() {
				return ''
			},
		},
		textArr2: {
			type: Array,
			default() {
				return []
			},
		},
		minHeight: {
			type: Number,
			default() {
				return 289
			},
		},
		minWidth: {
			type: String,
			default() {
				return '425px'
			},
		},
		top: {
			type: String,
			default() {
				return '26'
			},
		},
		bottom: {
			type: String,
			default() {
				return '0'
			},
		},
		chartsLegend: {
			type: Array,
			default() {
				return []
			},
		},
		yUnitName: {
			type: String,
			default() {
				return ''
			},
		},
		yUnit: {
			type: String,
			default() {
				return '万台'
			},
		},
		formatterType: {
			type: String,
			default() {
				return ''
			},
		},
		dataType: {
			type: String,
			default() {
				return ''
			},
		},
		paddingTop: {
			type: Number,
			default() {
				return 48
			},
		},
		showBarBg: {
			type: Boolean,
			default() {
				return false
			},
		},
		itemColor: {
			type: String,
			default() {
				return '24,144,255'
			},
		},
		showMonthTypeSelect: {
			type: Boolean,
			default() {
				return false
			},
		},
		xDataType: {
			type: String,
			default() {
				return ''
			},
		},
		tooltipTitleName: {
			type: String,
			default() {
				return '农机数据'
			},
		},
		tabIndex: {
			type: Number,
			default() {
				return 0
			},
		},
		marginTop: {
			type: Number,
			default() {
				return 0
			},
		},
		filterMarginTop: {
			type: Number,
			default() {
				return 0
			},
		},
	},
	data() {
		return {
			seleds: '',
			chartsLegends: [],
			monthTypeData: [
				{
					value: '1',
					label: '近七日',
				},
				{
					value: '2',
					label: '近一月',
				},
				{
					value: '3',
					label: '近一年',
				},
			],
			currentMonthType: '1',
		}
	},

	computed: {
		charMinHeight() {
			if (this.tabIndex == 1) {
				return 160
			} else {
				if (this.textArr2.length > 0 || this.showMonthTypeSelect) {
					return this.minHeight - 40
				} else {
					return this.minHeight
				}
			}
		},
	},
	watch: {
		selected: {
			handler: function(val) {
				this.seleds = val
			},
			// immediate: true,
		},
		textArr2(val) {
			console.log('textArr2Val--==', val)
		},
		chartsLegend(val) {
			this.chartsLegends = val
		},
	},
	methods: {
		handleChangeArgmach(val) {
			this.seleds = val
			//在此处根据选择的日期调用数据获取接口
			this.$emit('handleSearch', { argmachType: val, monthType: this.currentMonthType })
		},
		handleChangeMonthType(value) {
			this.currentMonthType = value
			this.$emit('handleSearch', { argmachType: this.argmachType, monthType: value })
		},
	},
}

// let myCharts: ECharts;

// const xData = ref<string[]>(['05-16', '05-17', '05-18', '05-19', '05-20', '05-21', '05-22']);
// const yData = ref<number[]>([10000, 20000, 28000, 50000, 70000, 120000, 130000]);
// const selected = ref(getFormattedDate(new Date()));
</script>

<style lang="less" scoped>
.t-box {
	position: relative;
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	align-items: flex-end;
	padding-left: 15px;
	padding-bottom: 10px;
	// margin-top: 18px;

	.t-title-box {
		position: absolute;
		right: 0;
		top: -5px;

		// min-width: 176px;
		min-width: calc(100% - 8px);

		min-height: 22px;
		// background: rgba(255, 255, 255, 0.46);
		border-radius: 12px;
		// border: 1px solid #e5e6eb;

		z-index: 999;
		padding: 0 15px;

		.t-select-box {
			min-width: 100%;
			min-height: 22px;
			position: relative;

			display: flex;
			justify-content: space-between;
			align-items: center;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 12px;
			color: #24818c;
			line-height: 24px;
			font-style: normal;

			.t-month-type-box {
				height: 100%;
				display: flex;
				justify-content: flex-end;
				align-items: center;

				.item-month-box {
					height: 100%;
					padding: 0 8px;
					border-radius: 2px 2px 2px 2px;
					margin: 0 2px;
					border: 1px solid #aac2dc;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 14px;
					color: #d0deee;
					text-align: left;
					font-style: normal;
					text-transform: none;
					cursor: pointer;

					&:hover {
						color: #159aff;
						border: 1px solid #159aff;
					}
				}

				.item-month-active {
					color: #159aff;
					border: 1px solid #159aff;
				}
			}
		}
	}
	.t-content-box {
		width: 100%;
		// height: calc(100% - 30px);
		position: relative;
		display: flex;
		flex-direction: column;
		justify-content: flex-end;
		align-items: center;
		margin-bottom: 0px;
		::v-deep(.echarts-box) {
			color: rgb(0, 140, 160);
		}
		z-index: 1000;
		// margin-top: 30px;
	}

	.t-empty-content-box {
		// height: 100%;
		// display: flex;
		// justify-content: center;
		// align-items: center;

		width: calc(100% - 20px) !important;
		height: 185px;
		display: flex;
		justify-content: center !important;
		align-items: center !important;
		font-family: PangMenZhengDao-3, PangMenZhengDao-3;
		font-weight: 500;
		font-size: 20px;
		color: #dbf1ff;
		text-shadow: 0px 0px 10px #bddcf1;
		text-align: center;
		font-style: normal;
		text-transform: none;
	}
}
</style>
