<template>
  <div ref="container" class="container" />
</template>

<script>
// import AMap from 'AMap';

import nanjing from '@/assets/map/nanjing';
import nanjing_point from '@/assets/map/nanjing_point';
import testData from '@/assets/map/testData';
export default {
  name: 'Amap',
  props: {
    mapHeight: {
      type: String,
      default: '8.35rem'
    }
  },
  data() {
    return {
      map: null,
      center: [118.856781, 31.557387],
      pitch: 48.37325,
      zoom: 10.06,
      rotation:-11.60818,
      skyColor: 'rgb(0, 0, 0, 0)',
      rotateEnable: true, // 地图是否可旋转，3D视图默认为true，2D视图默认false
      pitchEnable: true, // 是否允许设置俯仰角度，3D视图下为true，2D视图下无效
      showBuildingBlock: true, // 显示3d楼栋块
      opts: {
        subdistrict: 0,
        extensions: 'all',
        level: 'city'
      },
      object3Dlayer: null,
      districtName: '南京市',
      districtJson: null,
      upName: '南京市',
      textMarkers: [],
      textJson: null,
      currentCenter: null,
      layerList: [],
      intervalId: null,
      intervalIndex: 0,
      mapStyle: 'amap://styles/e45b0f429ea2c90f43944db07302b2c9',
    };
  },
  mounted() {
    var this_ = this;
    this.initMap(() => {
      this_.$emit('ready');
    });
  },
  beforeDestroy() {
    this.intervalId && clearInterval(this.intervalId);
  },
  methods: {
    // 初始化地图
    initMap(callback) {
      var this_ = this;
      this.searchDistrict(this.districtName).then(bounds => {
        var mask = [];
        for (var i = 0; i < bounds.length; i += 1) {
          mask.push([bounds[i]]);
        }
        this_.map = new AMap.Map(this_.$refs.container, {
          mask: mask,
          center: this_.center,
          disableSocket: true,
          viewMode: '3D',
          pitch: this_.pitch,
          zoom: this_.zoom,
          rotation: this_.rotation,
          expandZoomRange: true,
          rotateEnable: this_.rotateEnable,
          pitchEnable: this_.pitchEnable,
          mapStyle: this_.mapStyle,
          skyColor: this_.skyColor,
          showBuildingBlock: this_.showBuildingBlock,
          layers: [
            // new AMap.TileLayer.Satellite()
          ]
        });
        this_.add3DWall(bounds);

        this_.districtJson = nanjing;
        this_.loadGeoJSON();
        this_.textJson = nanjing_point;
        this_.addDistinctMarker();

        this_.map.on('complete', () => {
          // console.log('地图加载完成！')
          callback();
        });

        // this_.map.on('moveend', this.getMapStatus);
      });
    },
    /**
     * 获取当前状态
     */
    getMapStatus(){
      console.log('中心点：', this.map.getCenter())
      console.log('层级：', this.map.getZoom())
      console.log('俯仰角：', this.map.getPitch())
      console.log('旋转角：', this.map.getRotation())
      
    },
    /**
     * 区域查询
     */
    searchDistrict(name) {
      return new Promise(resolve => {
        // 高德接口查询
        var district = new AMap.DistrictSearch(this.opts);
        district.search(name, (status, result) => {
          // console.log(result);
          var bounds = result.districtList[0].boundaries;
          // console.log(bounds);
          resolve(bounds);
        });

        // 现有数据查询
        // var bounds = []
        // console.log(nanjing_full)
        // if (name === '南京市') {
        //   bounds = nanjing_full.features[0].geometry.coordinates
        // } else {
        //   var feature = nanjing.features.find(e => e.properties.name === name)
        //   bounds = feature.geometry.coordinates
        // }
        // console.log(bounds)
        // debugger
        // resolve(bounds)
      });
    },
    /**
     * 添加高度面
     */
    add3DWall(bounds) {
      this.object3Dlayer = new AMap.Object3DLayer({ zIndex: 1 });
      this.map.add(this.object3Dlayer);
      var height = -30000;
      // var color = 'rgba(7,53,84,0.8)'// rgba
      var color = '#073554'
      var color2 = '#0370aa'
      var color3 = '#003366'
      var wall = new AMap.Object3D.Wall({
        path: bounds,
        height: height,
        color: color
      });
      var wall2 = new AMap.Object3D.Wall({
        path: bounds,
        height: height + 5000,
        color: color2
      });
      var wall3 = new AMap.Object3D.Wall({
        path: bounds,
        height: height + 10000,
        color: color3
      });
      wall.transparent = true;
      this.object3Dlayer.add(wall);
      wall2.transparent = true;
      this.object3Dlayer.add(wall2);
      wall3.transparent = true;
      this.object3Dlayer.add(wall3);
      // console.log(wall)
      // console.log(this.object3Dlayer)
      // 添加描边
      for (var j = 0; j < bounds.length; j += 1) {
        new AMap.Polyline({
          path: bounds[j],
          // isOutline: true,
          // outlineColor: '#5eabef',
          // borderWeight: 3,
          strokeColor: '#00FFFF',
          strokeWeight: 5,
          strokeOpacity: 0.8,
          lineJoin: 'round',
          lineCap: 'round',
          map: this.map
        });
      }
    },
    /**
     * 加载geojson数据
     */
    loadGeoJSON() {
      var this_ = this;
      var geojson = new AMap.GeoJSON({
        geoJSON: this_.districtJson,
        // 还可以自定义getMarker和getPolyline
        getPolygon: (geojson, lnglats) => {
          var polygon = new AMap.Polygon({
            path: lnglats,
            fillOpacity: 0.1,
            strokeColor: '#00FFFF',
            strokeWeight: 2.5,
            fillColor: '#101C38'
          });
          polygon.on('click', e => {
            console.log(e);
            this.$emit('mapclick')
            // this_.currentCenter = [e.lnglat.lng, e.lnglat.lat];
            // this_.districtName = geojson.properties._parentProperities.name;
            // this_.initMap(() => {
            //   this_.map.setZoomAndCenter(11, this_.currentCenter);
            // });
          });
          return polygon;
        }
      });

      geojson.setMap(this.map);
    },
    // 销毁地图
    destroyMap() {
      this.map && this.map.destroy();
    },
    /**
     * 坐标转换
     *  lnglat  [116.46179996,39.99241446]
     * type  gps、baidu
     */
    convertFrom(lnglat, type) {
      AMap.convertFrom(lnglat, type, function(status, result) {
        if (result.info === 'ok') {
          var resLnglat = result.locations[0];
          console.log(resLnglat);
        }
      });
    },
    // 返回上一级
    returnUp() {
      this.districtName = this.upName;
      this.initMap(() => {});
    },
    // 添加行政区点标记 点击下钻当前行政区
    addDistinctMarker() {
      this.textMarkers = [];

      var feature = this.textJson.features;
      feature.forEach(e => {
        var textName = e.properties.name;
        // 点标记显示内容，HTML要素字符串
        var markerContent =
          '' +
          '<div class="textMarker">' +
          '   <div class="textName">' +
          textName +
          '</div>' +
          '</div>';
        var marker = new AMap.Marker({
          position: e.geometry.coordinates,
          // 将 html 传给 content
          content: markerContent,
          // 以 icon 的 [center bottom] 为原点
          offset: new AMap.Pixel(-30, -50)
        });
        marker.setExtData(textName);
        marker.on('click', e => {
          // this.currentCenter = e.geometry.coordinates
          console.log(e);
          this.$emit('mapclick')
          // this.currentCenter = [e.lnglat.lng, e.lnglat.lat];
          // this.districtName = e.target.getExtData();
          // this.initMap(() => {
          //   this.map.setZoomAndCenter(11, this.currentCenter);
          // });
        });
        this.textMarkers.push(marker);
      });
      this.map.add(this.textMarkers);
    },
    /**
     * 加载点通用方法
     * points 点数据集  可以自定义  目前设计为数组 对象包含坐标信息和属性信息
     * icon 点的图标
     * imageSize 图标大小
     * layerId 图层id 方便移除
     */
    addMarkers(points, icon, imageSize = [25, 34], layerId) {
      var markers = [];
      var icon_new = new AMap.Icon({
        // size: new AMap.Size(25, 34),
        image: icon,
        imageSize: new AMap.Size(imageSize[0], imageSize[1])
        // imageOffset: new AMap.Pixel(-95, -3)
      });
      points.forEach(it => {
        var marker = new AMap.Marker({
          position: it.position,
          // offset: new AMap.Pixel(-10, -10),
          icon: icon_new,
          // title: e.properties.name
          extData: it.properties
        });
        // 点的点击事件
        marker.on('click', e => {
          // console.log(e.target.getExtData());
          // this.changeZoomAndCenter(13, [e.lnglat.lng, e.lnglat.lat])
          if (it.popup) {
            it.onclick();
            // this.openInfoWindow(it.popup, e.lnglat);
            console.log(e.lnglat)
            this.openInfoWindow(it.popup, [this.center[0], this.center[1] - 0.2])
            // this.mapPanTo(e.lnglat);
          }
        });
        markers.push(marker);
      });
      this.map.add(markers);
      this.layerList.push({ layerId: layerId, geometry: markers });
    },
    // 改变底图层级和中心点
    changeZoomAndCenter(zoom, center) {
      this.map.setZoomAndCenter(zoom, center);
    },
    // 移除图层
    removeLayer(layerId) {
      // console.log(this.layerList);
      var layer = this.layerList.find(e => e.layerId === layerId);
      if (layer) {
        this.map.remove(layer.geometry);
        this.layerList = this.layerList.filter(obj => obj.layerId != layerId);
        this.intervalId && clearInterval(this.intervalId);
        this.closeInfoWindow();
      }
    },
    // 加载监控图层
    loadCameraLayer() {
      // this.addMarkers(
      //   testData.markersCamera,
      //   require('@/assets/img/map/camera.png'),
      //   [33, 55],
      //   'camera'
      // );
    },
    openInfoWindow(infoContent, position) {
      let infoWindow = new AMap.InfoWindow({
        isCustom: true,
        content: infoContent,
        offset: new AMap.Pixel(0, -25),
        anchor: 'bottom-center'
      });
      // infoWindow.open(this.map, [position.lng, position.lat]);
      infoWindow.open(this.map, position);
    },
    openInfoWindow2(infoContent, position) {
      let infoWindow = new AMap.InfoWindow({
        isCustom: true,
        content: infoContent,
        offset: new AMap.Pixel(0, -25),
        anchor: 'bottom-center'
      });
      infoWindow.open(this.map, [position.lng, position.lat]);
      this.map.setCenter(this.center[0],this.center[1])
    },
    mapPanTo(position) {
      this.map.panTo([position.lng, position.lat]);
    },
    closeInfoWindow() {
      this.map.clearInfoWindow();
    },
    autoShowInfoWindow(points) {
      // points.forEach(it => {
      //   it.onclick();
      //   this.openInfoWindow(it.popup, { lng: it.position[0], lat: it.position[1] });
      // });
      this.intervalId = setInterval(() => {
        if (this.intervalIndex == points.length - 1) {
          this.intervalIndex = 0;
        } else {
          this.intervalIndex++;
        }
        let it = points[this.intervalIndex];
        it.onclick();
        this.openInfoWindow2(it.popup, {
          lng: it.position[0],
          lat: it.position[1]
        });
      }, 3000);
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100%;
  background: url('~@/assets/map/map-bg.png') no-repeat !important;
  background-size: 100% 100% !important;
  position: relative;
}


</style>
