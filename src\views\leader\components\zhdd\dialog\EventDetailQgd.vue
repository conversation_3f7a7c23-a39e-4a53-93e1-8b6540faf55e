<template>
  <div class="event_detail" v-if="show">
    <div class="title">
      <span>事件详情</span>
    </div>
    <div class="close" @click="close"></div>
    <div class="sub_tit">
      <span class="tit">基本信息</span>
      <span class="tit_en">Essential Information</span>
    </div>
    <div class="cont1">
      <ul>
        <li>
          <div class="label">姓名：</div>
          <div class="value">{{ detail.name }}</div>
        </li>
        <li>
          <div class="label">事件地址：</div>
          <div class="value">{{ detail.address }}</div>
        </li>
        <li>
          <div class="label">归属地：</div>
          <div class="value">{{ detail.gsd }}</div>
        </li>
        <li>
          <div class="label">上报时间：</div>
          <div class="value">{{ detail.reportTime }}</div>
        </li>
        <li>
          <div class="label">事件类型：</div>
          <div class="value">{{ detail.type }}</div>
        </li>
        <li>
          <div class="label">事件来源：</div>
          <div class="value">{{ detail.origin }}</div>
        </li>
        <li>
          <div class="label">事件内容：</div>
          <div class="value" :title="detail.content">{{ detail.content }}</div>
        </li>
        <li>
          <div class="label">事件编号：</div>
          <div class="value">{{ detail.eventNo }}</div>
        </li>
        <li>
          <div class="label">事件状态：</div>
          <div class="value">{{ detail.state }}</div>
        </li>
        <li>
          <div class="label">事发时间：</div>
          <div class="value">{{ detail.eventTime }}</div>
        </li>
        <li>
          <div class="label">公开信息：</div>
          <div class="value">{{ detail.info }}</div>
        </li>
        <!-- <li>
          <div class="label">附件：</div>
          <div class="value attachment">
            <swiper class="swiper" :options="swiperOption">
              <swiper-slide v-for="(item, i) in imgList" :key="i">
                <el-image :src="item" :preview-src-list="imgList"></el-image>
              </swiper-slide>
            </swiper>
          </div>
        </li>-->
        <li>
          <div class="label">紧急程度：</div>
          <div
            class="value urgent"
            :class="{
              urgent1: detail.urgent === '紧急',
              urgent2: detail.urgent === '重要',
              urgent3: detail.urgent === '突发',
              urgent4: detail.urgent === '普通'
            }"
          >{{ detail.urgent }}</div>
        </li>
      </ul>
    </div>
    <div class="sub_tit">
      <span class="tit">处理流程</span>
      <span class="tit_en">Process flow</span>
    </div>
    <div class="cont2">
      <div class="info">
        <ul class="time_line">
          <li class="time_line_item" v-for="(it, i) of processList" :key="i">
            <div class="basic_info">
              <div class="name">{{ it.actionName }}</div>
              <div class="time" v-if="it.updateDate">{{ it.updateDate }}</div>
            </div>
            <div
              class="detail_info"
              v-if="!(i === processList.length - 1 && it.nodeName === '事件结案')"
            >
              <ul>
                <li>
                  <div class="lab">操作动作：</div>
                  <div class="val">{{ it.actionName }}</div>
                </li>
                <li>
                  <div class="lab">经办人：</div>
                  <div class="val">{{ it.updateByName }}</div>
                </li>
                <li>
                  <div class="lab">计划签收时间：</div>
                  <div class="val">{{ it.planSignTime }}</div>
                </li>
                <li>
                  <div class="lab">计划完成时间：</div>
                  <div class="val">{{ it.planFinishTime }}</div>
                </li>
                <li>
                  <div class="lab">处理意见：</div>
                  <div class="val">{{ it.advice }}</div>
                </li>
                <!-- <li>
                  <div class="lab">附件：</div>
                  <div class="val" v-if="it.torderAttachments?.length === 0">无</div>
                  <div class="val" v-else>
                    <div v-for="(item, i) of it.torderAttachments">
                      <span @click="downFile(item)" :title="item.attachName">{{item.attachName}}</span>
                    </div>
                  </div>
                </li>-->
                <li>
                  <div class="lab">附件：</div>
                  <div class="val" v-if="it.torderAttachments?.length === 0">无</div>
                  <div class="val file_" v-else>
                    <div v-for="(option, i) in it.torderAttachments" :key="i">
                      <div
                        @click="downloadSub(option)"
                        v-if="!picTypeArr.includes(option.attachType)"
                        class="file"
                        :title="option.attachName"
                      >
                        <img src="@/assets/img/file_icon.png" alt />
                        <span>{{option.attachName}}</span>
                      </div>
                      <div v-else :title="option.attachName">
                        <span @click.self="clickImage(it.torderAttachments,i)">{{option.attachName}}</span>
                      </div>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <div class="btn_wrap" v-if="isDone">
      <div class="btns">
        <div class="btn" @click="$emit('handle', 0)">处置</div>
        <div class="btn" @click="$emit('handle', 1)">定位</div>
      </div>
    </div>
    <!-- 图片放大 -->
    <showBigImg v-if="showViewer" :url-list="jcxxElImageList" @close-viewer="closeViewer"></showBigImg>
  </div>
</template>

<script>
import { getQgdFileXq } from '@/api/hs/hs.api.js'
import { xtyyUrl } from '@/utils/leader/const'
import showBigImg from '@/components/common/showBigImg.vue'

export default {
  name: 'EventDetailQgd',
  components: { showBigImg },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    detail: {
      type: Object,
      default: () => {
        return {
          name: '小明（15163628596）',
          address: '',
          gsd: '',
          reportTime: '',
          type: '',
          origin: '',
          content: '',
          eventNo: '',
          state: '',
          eventTime: '',
          info: '',
          urgent: '',
        }
      }
    },
    imgList: {
      type: Array,
      default: () => {
        return []
      }
    },
    processList: {
      type: Array,
      default: () => {
        return []
      }
    },
    isDone: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    show() {
      return this.value
    }
  },
  data() {
    return {
      btns1: ['基本信息', '工单备注历史', '下一步处理人信息'],
      activeBtn1: 0,
      label2: [
        {
          lab: '创建人',
          width: 83
        },
        {
          lab: '创建时间',
          width: 133
        },
        {
          lab: '备注类型',
          width: 163
        },
        {
          lab: '备注内容',
          width: 528
        }
      ],
      info2: [
        '张三丰',
        '2022-05-23',
        '社区网格上报',
        ''
      ],
      label3: [
        {
          lab: '姓名',
          width: 273
        },
        {
          lab: 'ID',
          width: 253
        },
        {
          lab: '岗位',
          width: 163
        },
        {
          lab: '联系电话',
          width: 319
        }
      ],
      info3: ['朱中村管理员', '32128002202admin', '社区指挥中心', '15172638464'],
      picArr: [require('@/assets/csts/photo1.png'), require('@/assets/shzl/map/ai_bg.png')],
      url: require('@/assets/csts/photo1.png'),
      srcList: [require('@/assets/csts/photo1.png'), require('@/assets/shzl/map/ai_bg.png')],
      swiperOption: {
        slidesPerView: '3',
        autoplay: {
          // 这个参数很重要,当外力干扰轮播图时，它能够继续实现自我滚动
          disableOnInteraction: false,
          delay: 2500 //5秒切换一次
        },
        pagination: {
          el: '.swiper-pagination',
          clickable: true,
          clickableClass: 'my-pagination-clickable'
        }
      },
      showViewer: false,
      jcxxElImageList: [],
      picTypeArr:['jpg','jpeg','png','image/jpeg']
      // picTypeArr:['jpg','jpeg','png']
    }
  },
  methods: {
    close() {
      this.$emit('input', false)
    },
    downloadSub(item) {
      console.log('item.url',item.url);
      this.$downloadFile(
        item.url,
        item.attachName || '附件'
      )
    },
    closeViewer() {
      this.showViewer = false
    },
    clickImage(list, index) {
      // console.log(list,index);
      const clickedArr = list[index]
      const saveList = JSON.parse(JSON.stringify(list))
      saveList.unshift(saveList[index])
      saveList.splice(index + 1, 1)

      this.showViewer = true
      this.jcxxElImageList = saveList
        .filter(option => {
          return  option.attachType == 'jpg' || option.attachType == 'jpeg' || option.attachType == 'png' || option.attachType == 'image/jpeg'
        })
        .map(item => item.url)
    }
  }
}
</script>

<style lang="less" scoped>
.bg-header {
  position: absolute;
  width: 100%;
  height: 1080px;
  background: rgba(2, 14, 50, 0.75);
  z-index: 1999;
  top: 0;
  left: 0;
}

.event_detail {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 960px;
  height: 854px;
  background: rgba(9, 19, 34, 0.95);
  box-shadow: 0px 0px 22px 0px rgba(11, 54, 138, 0.35), 0px 2px 8px 0px rgba(0, 0, 0, 0.7),
    inset 0px 0px 8px 0px rgba(17, 146, 214, 0.35);
  border-radius: 11px;
  border: 1px solid #015c8c;
  z-index: 2000;

  .title {
    margin: 0 auto;
    width: 634px;
    height: 73px;
    line-height: 73px;
    background: url(~@/assets/map/dialog/title2.png) no-repeat;
    text-align: center;
    margin-bottom: 42px;

    span {
      display: inline-block;
      font-size: 36px;
      font-family: PingFangSC, PingFang SC;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .close {
    position: absolute;
    width: 23px;
    height: 23px;
    top: 25px;
    right: 40px;
    background: url(~@/assets/map/dialog/btn3.png) no-repeat;
    cursor: pointer;
  }

  .sub_tit {
    position: relative;
    width: 901px;
    height: 33px;
    margin: 0 auto;
    padding-left: 34px;
    background: url(~@/assets/map/dialog/title3.png) no-repeat;
    display: flex;
    gap: 4px;
    .tit {
      position: relative;
      top: -8px;
      font-size: 22px;
      font-family: PingFangSC, PingFang SC;
      letter-spacing: 2px;
      background: linear-gradient(180deg, #ffffff 0%, #7ed1ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .tit_en {
      font-size: 14px;
      font-family: DINCond-RegularAlternate;
      font-weight: normal;
      color: #b3daff;
      line-height: 18px;
    }
  }

  .cont1 {
    width: 901px;
    height: 255px;
    margin: 0 auto;
    padding: 9px 24px 0;
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    line-height: 30px;

    ul {
      width: 100%;
      height: 100%;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: flex-start;
      li {
        width: 400px;
        height: 30px;
        line-height: 30px;
        display: flex;
        text-align: left;
        .label {
          // width: 70px;
          color: #6a92bb;
          flex-shrink: 0;
        }
        .value {
          width: 330px;
          color: #fff;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .urgent {
          align-self: center;
          width: 49px;
          height: 21px;
          line-height: 21px;
        }
        .urgent1 {
          text-overflow: inherit;
          color: #ff6600;
        }
        .urgent2 {
          text-overflow: inherit;
          color: #ffa061;
        }
        .urgent3 {
          text-overflow: inherit;
          color: #d8c32f;
        }
        .urgent4 {
          text-overflow: inherit;
          color: #30a1f0;
        }
        .attachment {
          height: 50px;
          margin-top: 10px;
        }
      }
      &::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 6px;
        background: transparent;
        // border: 1px solid #999;
        /*高宽分别对应横竖滚动条的尺寸*/
        // height: 1px;
      }

      &::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius: 2px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        background: rgb(45, 124, 228);
        height: 100px;
      }
    }
  }

  .cont2 {
    width: 901px;
    margin: 0 auto;
    height: 306px;
    padding: 16px 0 0 24px;
    margin-bottom: 39px;

    .info {
      width: 100%;
      height: 100%;
      overflow-y: scroll;
      overflow-x: hidden;

      &::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 6px;
        background: transparent;
        // border: 1px solid #999;
        /*高宽分别对应横竖滚动条的尺寸*/
        // height: 1px;
      }

      &::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius: 2px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        background: rgb(45, 124, 228);
        height: 100px;
      }

      .time_line {
        .time_line_item {
          position: relative;
          width: 100%;
          height: auto;
          padding-left: 15px;
          padding-bottom: 20px;

          &::before {
            position: absolute;
            content: '';
            width: 10px;
            height: 10px;
            background-color: #00eaff;
            border-radius: 50%;
            left: 0;
            top: 5px;
          }

          &::after {
            position: absolute;
            content: '';
            width: 1px;
            height: calc(100% - 5px);
            left: 4.5px;
            top: 11px;
            background-color: #add3ff;
          }
          &:last-child {
            &::after {
              position: absolute;
              content: '';
              width: 1px;
              height: 0;
              left: 4.5px;
              top: 11px;
              background-color: #add3ff;
            }
          }

          .basic_info {
            display: flex;
            height: 21px;
            line-height: 21px;
            padding-left: 4px;
            align-items: center;
            margin-bottom: 13px;

            .time {
              width: 155px;
              height: 21px;
              background: url('~@/assets/map/dialog/bg2.png') no-repeat center / 100% 100%;
              font-size: 13px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #00eaff;
              margin-right: 22px;
            }

            .name,
            .grid,
            .state {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #00eaff;
              margin-right: 22px;
            }
            .line {
              width: 1px;
              height: 12px;
              background: #375289;
              margin: 0 17px;
            }
          }

          .detail_info {
            width: 837px;
            padding: 16px 19px;
            background: rgba(12, 128, 196, 0)
              linear-gradient(180deg, #172639 0%, rgba(28, 44, 67, 0) 100%);
            border: 1px solid;
            border-image: linear-gradient(180deg, rgba(50, 75, 126, 1), rgba(50, 75, 126, 0)) 1 1;

            ul {
              display: flex;
              flex-wrap: wrap;
              justify-content: space-between;
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #6a92bb;
              line-height: 24px;

              li {
                display: flex;
                gap: 5px;
                width: 357px;
                text-align: left;

                .lab {
                  min-width: 100px;
                  color: #9de2ff;
                }

                .val {
                  color: #ffffff;
                  // overflow: hidden;
                  // text-overflow: ellipsis;
                  // white-space: nowrap;
                  // &.imgs {
                  //   overflow: inherit;
                  //   cursor: pointer;
                  // }
                  &.file_{
                    width: 60%;
                  }
                  span {
                    width: 100%;
                    display: inline-block;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                  }
                  .file {
                    display: flex;
                    align-items: center;
                    img {
                      margin: 0 4px 0 0;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  .btn_wrap {
    display: grid;
    place-items: center;
    .btns {
      display: flex;
      gap: 17px;
      .btn {
        width: 150px;
        height: 33px;
        line-height: 33px;
        font-size: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        background: url('~@/assets/map/dialog/btn1.png') no-repeat;
        cursor: pointer;
      }
    }
  }
}
/* 附件 */

.swiper-slide {
  width: 110px !important;
  height: 50px;
}
:deep(.el-image) {
  width: 85px;
  height: 50px;
}
</style>
