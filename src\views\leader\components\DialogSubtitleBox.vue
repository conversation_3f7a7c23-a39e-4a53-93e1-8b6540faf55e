<template>
  <div class="sub-box" :style="{ height }">
    <div class="title">
      <span class="tit">{{ title }}</span>
      <span class="tit_en">{{ enTitle }}</span>
    </div>
    <div class="cont">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DialogSubtitleBox',
  props: {
    height: {
      type: String,
      default: '200px'
    },
    title: {
      type: String,
      default: ''
    },
    enTitle: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="less" scoped>
.sub-box {
  .title {
    position: relative;
    width: 901px;
    height: 33px;
    margin: 0 auto;
    padding-left: 34px;
    background: url(~@/assets/map/dialog/title3.png) no-repeat;
    display: flex;
    gap: 4px;
    .tit {
      position: relative;
      top: -8px;
      font-size: 22px;
      font-family: PingFangSC, PingFang SC;
      letter-spacing: 2px;
      background: linear-gradient(180deg, #ffffff 0%, #7ed1ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .tit_en {
      font-size: 14px;
      font-family: DINCond-RegularAlternate;
      font-weight: normal;
      color: #b3daff;
      line-height: 18px;
    }
  }
  .cont {
    height: calc(100% - 33px);
  }
}
</style>
