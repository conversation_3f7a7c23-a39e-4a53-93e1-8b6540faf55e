const env = window['$env'] ? window['$env'] : process.env.NODE_ENV

// const HTTP_URL =
//   env === 'development' ? 'https://58.213.148.112:8000' : `http://172.17.60.179:8099`
// const WS_URL =
//   env === 'development' ? 'wss://58.213.148.112:8000/wss' : `ws://172.17.60.179:8099/ws`

const HTTP_URL = window.location.origin.includes('http://172') ? `http://172.17.60.179:8099` : 'https://58.213.148.112:8000'

const WS_URL = window.location.origin.includes('http://172') ? `ws://172.17.60.179:8099/ws` : 'wss://58.213.148.112:8000/wss'

export {
  HTTP_URL,
  WS_URL
};