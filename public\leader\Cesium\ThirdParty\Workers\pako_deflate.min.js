/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.96
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

(function(D){typeof exports=="object"&&typeof module<"u"?module.exports=D():typeof define=="function"&&define.amd?define([],D):(typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:this).pako=D()})(function(){return function D(H,v,E){function w(p,A){if(!v[p]){if(!H[p]){var u=typeof require=="function"&&require;if(!A&&u)return u(p,!0);if(x)return x(p,!0);var f=new Error("Cannot find module '"+p+"'");throw f.code="MODULE_NOT_FOUND",f}var _=v[p]={exports:{}};H[p][0].call(_.exports,function(y){return w(H[p][1][y]||y)},_,_.exports,D,H,v,E)}return v[p].exports}for(var x=typeof require=="function"&&require,g=0;g<E.length;g++)w(E[g]);return w}({1:[function(D,H,v){"use strict";var E=typeof Uint8Array<"u"&&typeof Uint16Array<"u"&&typeof Int32Array<"u";v.assign=function(g){for(var p,A,u=Array.prototype.slice.call(arguments,1);u.length;){var f=u.shift();if(f){if(typeof f!="object")throw new TypeError(f+"must be non-object");for(var _ in f)p=f,A=_,Object.prototype.hasOwnProperty.call(p,A)&&(g[_]=f[_])}}return g},v.shrinkBuf=function(g,p){return g.length===p?g:g.subarray?g.subarray(0,p):(g.length=p,g)};var w={arraySet:function(g,p,A,u,f){if(p.subarray&&g.subarray)g.set(p.subarray(A,A+u),f);else for(var _=0;_<u;_++)g[f+_]=p[A+_]},flattenChunks:function(g){var p,A,u,f,_,y;for(p=u=0,A=g.length;p<A;p++)u+=g[p].length;for(y=new Uint8Array(u),p=f=0,A=g.length;p<A;p++)_=g[p],y.set(_,f),f+=_.length;return y}},x={arraySet:function(g,p,A,u,f){for(var _=0;_<u;_++)g[f+_]=p[A+_]},flattenChunks:function(g){return[].concat.apply([],g)}};v.setTyped=function(g){g?(v.Buf8=Uint8Array,v.Buf16=Uint16Array,v.Buf32=Int32Array,v.assign(v,w)):(v.Buf8=Array,v.Buf16=Array,v.Buf32=Array,v.assign(v,x))},v.setTyped(E)},{}],2:[function(D,H,v){"use strict";var E=D("./common"),w=!0,x=!0;try{String.fromCharCode.apply(null,[0])}catch{w=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch{x=!1}for(var g=new E.Buf8(256),p=0;p<256;p++)g[p]=252<=p?6:248<=p?5:240<=p?4:224<=p?3:192<=p?2:1;function A(u,f){if(f<65534&&(u.subarray&&x||!u.subarray&&w))return String.fromCharCode.apply(null,E.shrinkBuf(u,f));for(var _="",y=0;y<f;y++)_+=String.fromCharCode(u[y]);return _}g[254]=g[254]=1,v.string2buf=function(u){var f,_,y,k,j,S=u.length,m=0;for(k=0;k<S;k++)(64512&(_=u.charCodeAt(k)))==55296&&k+1<S&&(64512&(y=u.charCodeAt(k+1)))==56320&&(_=65536+(_-55296<<10)+(y-56320),k++),m+=_<128?1:_<2048?2:_<65536?3:4;for(f=new E.Buf8(m),k=j=0;j<m;k++)(64512&(_=u.charCodeAt(k)))==55296&&k+1<S&&(64512&(y=u.charCodeAt(k+1)))==56320&&(_=65536+(_-55296<<10)+(y-56320),k++),_<128?f[j++]=_:(_<2048?f[j++]=192|_>>>6:(_<65536?f[j++]=224|_>>>12:(f[j++]=240|_>>>18,f[j++]=128|_>>>12&63),f[j++]=128|_>>>6&63),f[j++]=128|63&_);return f},v.buf2binstring=function(u){return A(u,u.length)},v.binstring2buf=function(u){for(var f=new E.Buf8(u.length),_=0,y=f.length;_<y;_++)f[_]=u.charCodeAt(_);return f},v.buf2string=function(u,f){var _,y,k,j,S=f||u.length,m=new Array(2*S);for(_=y=0;_<S;)if((k=u[_++])<128)m[y++]=k;else if(4<(j=g[k]))m[y++]=65533,_+=j-1;else{for(k&=j===2?31:j===3?15:7;1<j&&_<S;)k=k<<6|63&u[_++],j--;1<j?m[y++]=65533:k<65536?m[y++]=k:(k-=65536,m[y++]=55296|k>>10&1023,m[y++]=56320|1023&k)}return A(m,y)},v.utf8border=function(u,f){var _;for((f=f||u.length)>u.length&&(f=u.length),_=f-1;0<=_&&(192&u[_])==128;)_--;return _<0||_===0?f:_+g[u[_]]>f?_:f}},{"./common":1}],3:[function(D,H,v){"use strict";H.exports=function(E,w,x,g){for(var p=65535&E|0,A=E>>>16&65535|0,u=0;x!==0;){for(x-=u=2e3<x?2e3:x;A=A+(p=p+w[g++]|0)|0,--u;);p%=65521,A%=65521}return p|A<<16|0}},{}],4:[function(D,H,v){"use strict";var E=function(){for(var w,x=[],g=0;g<256;g++){w=g;for(var p=0;p<8;p++)w=1&w?3988292384^w>>>1:w>>>1;x[g]=w}return x}();H.exports=function(w,x,g,p){var A=E,u=p+g;w^=-1;for(var f=p;f<u;f++)w=w>>>8^A[255&(w^x[f])];return-1^w}},{}],5:[function(D,H,v){"use strict";var E,w=D("../utils/common"),x=D("./trees"),g=D("./adler32"),p=D("./crc32"),A=D("./messages"),u=0,f=4,_=0,y=-2,k=-1,j=4,S=2,m=8,O=9,F=286,I=30,ie=19,_e=2*F+1,te=15,B=3,V=258,T=V+B+1,X=42,K=113,q=1,ee=2,Y=3,ae=4;function W(e,s){return e.msg=A[s],s}function se(e){return(e<<1)-(4<e?9:0)}function G(e){for(var s=e.length;0<=--s;)e[s]=0}function J(e){var s=e.state,t=s.pending;t>e.avail_out&&(t=e.avail_out),t!==0&&(w.arraySet(e.output,s.pending_buf,s.pending_out,t,e.next_out),e.next_out+=t,s.pending_out+=t,e.total_out+=t,e.avail_out-=t,s.pending-=t,s.pending===0&&(s.pending_out=0))}function N(e,s){x._tr_flush_block(e,0<=e.block_start?e.block_start:-1,e.strstart-e.block_start,s),e.block_start=e.strstart,J(e.strm)}function z(e,s){e.pending_buf[e.pending++]=s}function R(e,s){e.pending_buf[e.pending++]=s>>>8&255,e.pending_buf[e.pending++]=255&s}function M(e,s){var t,r,a=e.max_chain_length,n=e.strstart,h=e.prev_length,l=e.nice_match,i=e.strstart>e.w_size-T?e.strstart-(e.w_size-T):0,o=e.window,c=e.w_mask,d=e.prev,b=e.strstart+V,U=o[n+h-1],C=o[n+h];e.prev_length>=e.good_match&&(a>>=2),l>e.lookahead&&(l=e.lookahead);do if(o[(t=s)+h]===C&&o[t+h-1]===U&&o[t]===o[n]&&o[++t]===o[n+1]){n+=2,t++;do;while(o[++n]===o[++t]&&o[++n]===o[++t]&&o[++n]===o[++t]&&o[++n]===o[++t]&&o[++n]===o[++t]&&o[++n]===o[++t]&&o[++n]===o[++t]&&o[++n]===o[++t]&&n<b);if(r=V-(b-n),n=b-V,h<r){if(e.match_start=s,l<=(h=r))break;U=o[n+h-1],C=o[n+h]}}while((s=d[s&c])>i&&--a!=0);return h<=e.lookahead?h:e.lookahead}function Z(e){var s,t,r,a,n,h,l,i,o,c,d=e.w_size;do{if(a=e.window_size-e.lookahead-e.strstart,e.strstart>=d+(d-T)){for(w.arraySet(e.window,e.window,d,d,0),e.match_start-=d,e.strstart-=d,e.block_start-=d,s=t=e.hash_size;r=e.head[--s],e.head[s]=d<=r?r-d:0,--t;);for(s=t=d;r=e.prev[--s],e.prev[s]=d<=r?r-d:0,--t;);a+=d}if(e.strm.avail_in===0)break;if(h=e.strm,l=e.window,i=e.strstart+e.lookahead,o=a,c=void 0,c=h.avail_in,o<c&&(c=o),t=c===0?0:(h.avail_in-=c,w.arraySet(l,h.input,h.next_in,c,i),h.state.wrap===1?h.adler=g(h.adler,l,c,i):h.state.wrap===2&&(h.adler=p(h.adler,l,c,i)),h.next_in+=c,h.total_in+=c,c),e.lookahead+=t,e.lookahead+e.insert>=B)for(n=e.strstart-e.insert,e.ins_h=e.window[n],e.ins_h=(e.ins_h<<e.hash_shift^e.window[n+1])&e.hash_mask;e.insert&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[n+B-1])&e.hash_mask,e.prev[n&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=n,n++,e.insert--,!(e.lookahead+e.insert<B)););}while(e.lookahead<T&&e.strm.avail_in!==0)}function he(e,s){for(var t,r;;){if(e.lookahead<T){if(Z(e),e.lookahead<T&&s===u)return q;if(e.lookahead===0)break}if(t=0,e.lookahead>=B&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+B-1])&e.hash_mask,t=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),t!==0&&e.strstart-t<=e.w_size-T&&(e.match_length=M(e,t)),e.match_length>=B)if(r=x._tr_tally(e,e.strstart-e.match_start,e.match_length-B),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=B){for(e.match_length--;e.strstart++,e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+B-1])&e.hash_mask,t=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart,--e.match_length!=0;);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+1])&e.hash_mask;else r=x._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(r&&(N(e,!1),e.strm.avail_out===0))return q}return e.insert=e.strstart<B-1?e.strstart:B-1,s===f?(N(e,!0),e.strm.avail_out===0?Y:ae):e.last_lit&&(N(e,!1),e.strm.avail_out===0)?q:ee}function re(e,s){for(var t,r,a;;){if(e.lookahead<T){if(Z(e),e.lookahead<T&&s===u)return q;if(e.lookahead===0)break}if(t=0,e.lookahead>=B&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+B-1])&e.hash_mask,t=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=B-1,t!==0&&e.prev_length<e.max_lazy_match&&e.strstart-t<=e.w_size-T&&(e.match_length=M(e,t),e.match_length<=5&&(e.strategy===1||e.match_length===B&&4096<e.strstart-e.match_start)&&(e.match_length=B-1)),e.prev_length>=B&&e.match_length<=e.prev_length){for(a=e.strstart+e.lookahead-B,r=x._tr_tally(e,e.strstart-1-e.prev_match,e.prev_length-B),e.lookahead-=e.prev_length-1,e.prev_length-=2;++e.strstart<=a&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+B-1])&e.hash_mask,t=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),--e.prev_length!=0;);if(e.match_available=0,e.match_length=B-1,e.strstart++,r&&(N(e,!1),e.strm.avail_out===0))return q}else if(e.match_available){if((r=x._tr_tally(e,0,e.window[e.strstart-1]))&&N(e,!1),e.strstart++,e.lookahead--,e.strm.avail_out===0)return q}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(r=x._tr_tally(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<B-1?e.strstart:B-1,s===f?(N(e,!0),e.strm.avail_out===0?Y:ae):e.last_lit&&(N(e,!1),e.strm.avail_out===0)?q:ee}function P(e,s,t,r,a){this.good_length=e,this.max_lazy=s,this.nice_length=t,this.max_chain=r,this.func=a}function pe(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=m,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new w.Buf16(2*_e),this.dyn_dtree=new w.Buf16(2*(2*I+1)),this.bl_tree=new w.Buf16(2*(2*ie+1)),G(this.dyn_ltree),G(this.dyn_dtree),G(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new w.Buf16(te+1),this.heap=new w.Buf16(2*F+1),G(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new w.Buf16(2*F+1),G(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function le(e){var s;return e&&e.state?(e.total_in=e.total_out=0,e.data_type=S,(s=e.state).pending=0,s.pending_out=0,s.wrap<0&&(s.wrap=-s.wrap),s.status=s.wrap?X:K,e.adler=s.wrap===2?0:1,s.last_flush=u,x._tr_init(s),_):W(e,y)}function de(e){var s,t=le(e);return t===_&&((s=e.state).window_size=2*s.w_size,G(s.head),s.max_lazy_match=E[s.level].max_lazy,s.good_match=E[s.level].good_length,s.nice_match=E[s.level].nice_length,s.max_chain_length=E[s.level].max_chain,s.strstart=0,s.block_start=0,s.lookahead=0,s.insert=0,s.match_length=s.prev_length=B-1,s.match_available=0,s.ins_h=0),t}function oe(e,s,t,r,a,n){if(!e)return y;var h=1;if(s===k&&(s=6),r<0?(h=0,r=-r):15<r&&(h=2,r-=16),a<1||O<a||t!==m||r<8||15<r||s<0||9<s||n<0||j<n)return W(e,y);r===8&&(r=9);var l=new pe;return(e.state=l).strm=e,l.wrap=h,l.gzhead=null,l.w_bits=r,l.w_size=1<<l.w_bits,l.w_mask=l.w_size-1,l.hash_bits=a+7,l.hash_size=1<<l.hash_bits,l.hash_mask=l.hash_size-1,l.hash_shift=~~((l.hash_bits+B-1)/B),l.window=new w.Buf8(2*l.w_size),l.head=new w.Buf16(l.hash_size),l.prev=new w.Buf16(l.w_size),l.lit_bufsize=1<<a+6,l.pending_buf_size=4*l.lit_bufsize,l.pending_buf=new w.Buf8(l.pending_buf_size),l.d_buf=1*l.lit_bufsize,l.l_buf=3*l.lit_bufsize,l.level=s,l.strategy=n,l.method=t,de(e)}E=[new P(0,0,0,0,function(e,s){var t=65535;for(t>e.pending_buf_size-5&&(t=e.pending_buf_size-5);;){if(e.lookahead<=1){if(Z(e),e.lookahead===0&&s===u)return q;if(e.lookahead===0)break}e.strstart+=e.lookahead,e.lookahead=0;var r=e.block_start+t;if((e.strstart===0||e.strstart>=r)&&(e.lookahead=e.strstart-r,e.strstart=r,N(e,!1),e.strm.avail_out===0)||e.strstart-e.block_start>=e.w_size-T&&(N(e,!1),e.strm.avail_out===0))return q}return e.insert=0,s===f?(N(e,!0),e.strm.avail_out===0?Y:ae):(e.strstart>e.block_start&&(N(e,!1),e.strm.avail_out),q)}),new P(4,4,8,4,he),new P(4,5,16,8,he),new P(4,6,32,32,he),new P(4,4,16,16,re),new P(8,16,32,32,re),new P(8,16,128,128,re),new P(8,32,128,256,re),new P(32,128,258,1024,re),new P(32,258,258,4096,re)],v.deflateInit=function(e,s){return oe(e,s,m,15,8,0)},v.deflateInit2=oe,v.deflateReset=de,v.deflateResetKeep=le,v.deflateSetHeader=function(e,s){return e&&e.state?e.state.wrap!==2?y:(e.state.gzhead=s,_):y},v.deflate=function(e,s){var t,r,a,n;if(!e||!e.state||5<s||s<0)return e?W(e,y):y;if(r=e.state,!e.output||!e.input&&e.avail_in!==0||r.status===666&&s!==f)return W(e,e.avail_out===0?-5:y);if(r.strm=e,t=r.last_flush,r.last_flush=s,r.status===X)if(r.wrap===2)e.adler=0,z(r,31),z(r,139),z(r,8),r.gzhead?(z(r,(r.gzhead.text?1:0)+(r.gzhead.hcrc?2:0)+(r.gzhead.extra?4:0)+(r.gzhead.name?8:0)+(r.gzhead.comment?16:0)),z(r,255&r.gzhead.time),z(r,r.gzhead.time>>8&255),z(r,r.gzhead.time>>16&255),z(r,r.gzhead.time>>24&255),z(r,r.level===9?2:2<=r.strategy||r.level<2?4:0),z(r,255&r.gzhead.os),r.gzhead.extra&&r.gzhead.extra.length&&(z(r,255&r.gzhead.extra.length),z(r,r.gzhead.extra.length>>8&255)),r.gzhead.hcrc&&(e.adler=p(e.adler,r.pending_buf,r.pending,0)),r.gzindex=0,r.status=69):(z(r,0),z(r,0),z(r,0),z(r,0),z(r,0),z(r,r.level===9?2:2<=r.strategy||r.level<2?4:0),z(r,3),r.status=K);else{var h=m+(r.w_bits-8<<4)<<8;h|=(2<=r.strategy||r.level<2?0:r.level<6?1:r.level===6?2:3)<<6,r.strstart!==0&&(h|=32),h+=31-h%31,r.status=K,R(r,h),r.strstart!==0&&(R(r,e.adler>>>16),R(r,65535&e.adler)),e.adler=1}if(r.status===69)if(r.gzhead.extra){for(a=r.pending;r.gzindex<(65535&r.gzhead.extra.length)&&(r.pending!==r.pending_buf_size||(r.gzhead.hcrc&&r.pending>a&&(e.adler=p(e.adler,r.pending_buf,r.pending-a,a)),J(e),a=r.pending,r.pending!==r.pending_buf_size));)z(r,255&r.gzhead.extra[r.gzindex]),r.gzindex++;r.gzhead.hcrc&&r.pending>a&&(e.adler=p(e.adler,r.pending_buf,r.pending-a,a)),r.gzindex===r.gzhead.extra.length&&(r.gzindex=0,r.status=73)}else r.status=73;if(r.status===73)if(r.gzhead.name){a=r.pending;do{if(r.pending===r.pending_buf_size&&(r.gzhead.hcrc&&r.pending>a&&(e.adler=p(e.adler,r.pending_buf,r.pending-a,a)),J(e),a=r.pending,r.pending===r.pending_buf_size)){n=1;break}z(r,n=r.gzindex<r.gzhead.name.length?255&r.gzhead.name.charCodeAt(r.gzindex++):0)}while(n!==0);r.gzhead.hcrc&&r.pending>a&&(e.adler=p(e.adler,r.pending_buf,r.pending-a,a)),n===0&&(r.gzindex=0,r.status=91)}else r.status=91;if(r.status===91)if(r.gzhead.comment){a=r.pending;do{if(r.pending===r.pending_buf_size&&(r.gzhead.hcrc&&r.pending>a&&(e.adler=p(e.adler,r.pending_buf,r.pending-a,a)),J(e),a=r.pending,r.pending===r.pending_buf_size)){n=1;break}z(r,n=r.gzindex<r.gzhead.comment.length?255&r.gzhead.comment.charCodeAt(r.gzindex++):0)}while(n!==0);r.gzhead.hcrc&&r.pending>a&&(e.adler=p(e.adler,r.pending_buf,r.pending-a,a)),n===0&&(r.status=103)}else r.status=103;if(r.status===103&&(r.gzhead.hcrc?(r.pending+2>r.pending_buf_size&&J(e),r.pending+2<=r.pending_buf_size&&(z(r,255&e.adler),z(r,e.adler>>8&255),e.adler=0,r.status=K)):r.status=K),r.pending!==0){if(J(e),e.avail_out===0)return r.last_flush=-1,_}else if(e.avail_in===0&&se(s)<=se(t)&&s!==f)return W(e,-5);if(r.status===666&&e.avail_in!==0)return W(e,-5);if(e.avail_in!==0||r.lookahead!==0||s!==u&&r.status!==666){var l=r.strategy===2?function(i,o){for(var c;;){if(i.lookahead===0&&(Z(i),i.lookahead===0)){if(o===u)return q;break}if(i.match_length=0,c=x._tr_tally(i,0,i.window[i.strstart]),i.lookahead--,i.strstart++,c&&(N(i,!1),i.strm.avail_out===0))return q}return i.insert=0,o===f?(N(i,!0),i.strm.avail_out===0?Y:ae):i.last_lit&&(N(i,!1),i.strm.avail_out===0)?q:ee}(r,s):r.strategy===3?function(i,o){for(var c,d,b,U,C=i.window;;){if(i.lookahead<=V){if(Z(i),i.lookahead<=V&&o===u)return q;if(i.lookahead===0)break}if(i.match_length=0,i.lookahead>=B&&0<i.strstart&&(d=C[b=i.strstart-1])===C[++b]&&d===C[++b]&&d===C[++b]){U=i.strstart+V;do;while(d===C[++b]&&d===C[++b]&&d===C[++b]&&d===C[++b]&&d===C[++b]&&d===C[++b]&&d===C[++b]&&d===C[++b]&&b<U);i.match_length=V-(U-b),i.match_length>i.lookahead&&(i.match_length=i.lookahead)}if(i.match_length>=B?(c=x._tr_tally(i,1,i.match_length-B),i.lookahead-=i.match_length,i.strstart+=i.match_length,i.match_length=0):(c=x._tr_tally(i,0,i.window[i.strstart]),i.lookahead--,i.strstart++),c&&(N(i,!1),i.strm.avail_out===0))return q}return i.insert=0,o===f?(N(i,!0),i.strm.avail_out===0?Y:ae):i.last_lit&&(N(i,!1),i.strm.avail_out===0)?q:ee}(r,s):E[r.level].func(r,s);if(l!==Y&&l!==ae||(r.status=666),l===q||l===Y)return e.avail_out===0&&(r.last_flush=-1),_;if(l===ee&&(s===1?x._tr_align(r):s!==5&&(x._tr_stored_block(r,0,0,!1),s===3&&(G(r.head),r.lookahead===0&&(r.strstart=0,r.block_start=0,r.insert=0))),J(e),e.avail_out===0))return r.last_flush=-1,_}return s!==f?_:r.wrap<=0?1:(r.wrap===2?(z(r,255&e.adler),z(r,e.adler>>8&255),z(r,e.adler>>16&255),z(r,e.adler>>24&255),z(r,255&e.total_in),z(r,e.total_in>>8&255),z(r,e.total_in>>16&255),z(r,e.total_in>>24&255)):(R(r,e.adler>>>16),R(r,65535&e.adler)),J(e),0<r.wrap&&(r.wrap=-r.wrap),r.pending!==0?_:1)},v.deflateEnd=function(e){var s;return e&&e.state?(s=e.state.status)!==X&&s!==69&&s!==73&&s!==91&&s!==103&&s!==K&&s!==666?W(e,y):(e.state=null,s===K?W(e,-3):_):y},v.deflateSetDictionary=function(e,s){var t,r,a,n,h,l,i,o,c=s.length;if(!e||!e.state||(n=(t=e.state).wrap)===2||n===1&&t.status!==X||t.lookahead)return y;for(n===1&&(e.adler=g(e.adler,s,c,0)),t.wrap=0,c>=t.w_size&&(n===0&&(G(t.head),t.strstart=0,t.block_start=0,t.insert=0),o=new w.Buf8(t.w_size),w.arraySet(o,s,c-t.w_size,t.w_size,0),s=o,c=t.w_size),h=e.avail_in,l=e.next_in,i=e.input,e.avail_in=c,e.next_in=0,e.input=s,Z(t);t.lookahead>=B;){for(r=t.strstart,a=t.lookahead-(B-1);t.ins_h=(t.ins_h<<t.hash_shift^t.window[r+B-1])&t.hash_mask,t.prev[r&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=r,r++,--a;);t.strstart=r,t.lookahead=B-1,Z(t)}return t.strstart+=t.lookahead,t.block_start=t.strstart,t.insert=t.lookahead,t.lookahead=0,t.match_length=t.prev_length=B-1,t.match_available=0,e.next_in=l,e.input=i,e.avail_in=h,t.wrap=n,_},v.deflateInfo="pako deflate (from Nodeca project)"},{"../utils/common":1,"./adler32":3,"./crc32":4,"./messages":6,"./trees":7}],6:[function(D,H,v){"use strict";H.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},{}],7:[function(D,H,v){"use strict";var E=D("../utils/common"),w=0,x=1;function g(a){for(var n=a.length;0<=--n;)a[n]=0}var p=0,A=29,u=256,f=u+1+A,_=30,y=19,k=2*f+1,j=15,S=16,m=7,O=256,F=16,I=17,ie=18,_e=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],te=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],B=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],V=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],T=new Array(2*(f+2));g(T);var X=new Array(2*_);g(X);var K=new Array(512);g(K);var q=new Array(256);g(q);var ee=new Array(A);g(ee);var Y,ae,W,se=new Array(_);function G(a,n,h,l,i){this.static_tree=a,this.extra_bits=n,this.extra_base=h,this.elems=l,this.max_length=i,this.has_stree=a&&a.length}function J(a,n){this.dyn_tree=a,this.max_code=0,this.stat_desc=n}function N(a){return a<256?K[a]:K[256+(a>>>7)]}function z(a,n){a.pending_buf[a.pending++]=255&n,a.pending_buf[a.pending++]=n>>>8&255}function R(a,n,h){a.bi_valid>S-h?(a.bi_buf|=n<<a.bi_valid&65535,z(a,a.bi_buf),a.bi_buf=n>>S-a.bi_valid,a.bi_valid+=h-S):(a.bi_buf|=n<<a.bi_valid&65535,a.bi_valid+=h)}function M(a,n,h){R(a,h[2*n],h[2*n+1])}function Z(a,n){for(var h=0;h|=1&a,a>>>=1,h<<=1,0<--n;);return h>>>1}function he(a,n,h){var l,i,o=new Array(j+1),c=0;for(l=1;l<=j;l++)o[l]=c=c+h[l-1]<<1;for(i=0;i<=n;i++){var d=a[2*i+1];d!==0&&(a[2*i]=Z(o[d]++,d))}}function re(a){var n;for(n=0;n<f;n++)a.dyn_ltree[2*n]=0;for(n=0;n<_;n++)a.dyn_dtree[2*n]=0;for(n=0;n<y;n++)a.bl_tree[2*n]=0;a.dyn_ltree[2*O]=1,a.opt_len=a.static_len=0,a.last_lit=a.matches=0}function P(a){8<a.bi_valid?z(a,a.bi_buf):0<a.bi_valid&&(a.pending_buf[a.pending++]=a.bi_buf),a.bi_buf=0,a.bi_valid=0}function pe(a,n,h,l){var i=2*n,o=2*h;return a[i]<a[o]||a[i]===a[o]&&l[n]<=l[h]}function le(a,n,h){for(var l=a.heap[h],i=h<<1;i<=a.heap_len&&(i<a.heap_len&&pe(n,a.heap[i+1],a.heap[i],a.depth)&&i++,!pe(n,l,a.heap[i],a.depth));)a.heap[h]=a.heap[i],h=i,i<<=1;a.heap[h]=l}function de(a,n,h){var l,i,o,c,d=0;if(a.last_lit!==0)for(;l=a.pending_buf[a.d_buf+2*d]<<8|a.pending_buf[a.d_buf+2*d+1],i=a.pending_buf[a.l_buf+d],d++,l===0?M(a,i,n):(M(a,(o=q[i])+u+1,n),(c=_e[o])!==0&&R(a,i-=ee[o],c),M(a,o=N(--l),h),(c=te[o])!==0&&R(a,l-=se[o],c)),d<a.last_lit;);M(a,O,n)}function oe(a,n){var h,l,i,o=n.dyn_tree,c=n.stat_desc.static_tree,d=n.stat_desc.has_stree,b=n.stat_desc.elems,U=-1;for(a.heap_len=0,a.heap_max=k,h=0;h<b;h++)o[2*h]!==0?(a.heap[++a.heap_len]=U=h,a.depth[h]=0):o[2*h+1]=0;for(;a.heap_len<2;)o[2*(i=a.heap[++a.heap_len]=U<2?++U:0)]=1,a.depth[i]=0,a.opt_len--,d&&(a.static_len-=c[2*i+1]);for(n.max_code=U,h=a.heap_len>>1;1<=h;h--)le(a,o,h);for(i=b;h=a.heap[1],a.heap[1]=a.heap[a.heap_len--],le(a,o,1),l=a.heap[1],a.heap[--a.heap_max]=h,a.heap[--a.heap_max]=l,o[2*i]=o[2*h]+o[2*l],a.depth[i]=(a.depth[h]>=a.depth[l]?a.depth[h]:a.depth[l])+1,o[2*h+1]=o[2*l+1]=i,a.heap[1]=i++,le(a,o,1),2<=a.heap_len;);a.heap[--a.heap_max]=a.heap[1],function(C,Q){var ue,$,fe,L,ge,be,ne=Q.dyn_tree,ve=Q.max_code,ye=Q.stat_desc.static_tree,ke=Q.stat_desc.has_stree,ze=Q.stat_desc.extra_bits,we=Q.stat_desc.extra_base,ce=Q.stat_desc.max_length,me=0;for(L=0;L<=j;L++)C.bl_count[L]=0;for(ne[2*C.heap[C.heap_max]+1]=0,ue=C.heap_max+1;ue<k;ue++)ce<(L=ne[2*ne[2*($=C.heap[ue])+1]+1]+1)&&(L=ce,me++),ne[2*$+1]=L,ve<$||(C.bl_count[L]++,ge=0,we<=$&&(ge=ze[$-we]),be=ne[2*$],C.opt_len+=be*(L+ge),ke&&(C.static_len+=be*(ye[2*$+1]+ge)));if(me!==0){do{for(L=ce-1;C.bl_count[L]===0;)L--;C.bl_count[L]--,C.bl_count[L+1]+=2,C.bl_count[ce]--,me-=2}while(0<me);for(L=ce;L!==0;L--)for($=C.bl_count[L];$!==0;)ve<(fe=C.heap[--ue])||(ne[2*fe+1]!==L&&(C.opt_len+=(L-ne[2*fe+1])*ne[2*fe],ne[2*fe+1]=L),$--)}}(a,n),he(o,U,a.bl_count)}function e(a,n,h){var l,i,o=-1,c=n[1],d=0,b=7,U=4;for(c===0&&(b=138,U=3),n[2*(h+1)+1]=65535,l=0;l<=h;l++)i=c,c=n[2*(l+1)+1],++d<b&&i===c||(d<U?a.bl_tree[2*i]+=d:i!==0?(i!==o&&a.bl_tree[2*i]++,a.bl_tree[2*F]++):d<=10?a.bl_tree[2*I]++:a.bl_tree[2*ie]++,o=i,(d=0)===c?(b=138,U=3):i===c?(b=6,U=3):(b=7,U=4))}function s(a,n,h){var l,i,o=-1,c=n[1],d=0,b=7,U=4;for(c===0&&(b=138,U=3),l=0;l<=h;l++)if(i=c,c=n[2*(l+1)+1],!(++d<b&&i===c)){if(d<U)for(;M(a,i,a.bl_tree),--d!=0;);else i!==0?(i!==o&&(M(a,i,a.bl_tree),d--),M(a,F,a.bl_tree),R(a,d-3,2)):d<=10?(M(a,I,a.bl_tree),R(a,d-3,3)):(M(a,ie,a.bl_tree),R(a,d-11,7));o=i,(d=0)===c?(b=138,U=3):i===c?(b=6,U=3):(b=7,U=4)}}g(se);var t=!1;function r(a,n,h,l){var i,o,c,d;R(a,(p<<1)+(l?1:0),3),o=n,c=h,d=!0,P(i=a),d&&(z(i,c),z(i,~c)),E.arraySet(i.pending_buf,i.window,o,c,i.pending),i.pending+=c}v._tr_init=function(a){t||(function(){var n,h,l,i,o,c=new Array(j+1);for(i=l=0;i<A-1;i++)for(ee[i]=l,n=0;n<1<<_e[i];n++)q[l++]=i;for(q[l-1]=i,i=o=0;i<16;i++)for(se[i]=o,n=0;n<1<<te[i];n++)K[o++]=i;for(o>>=7;i<_;i++)for(se[i]=o<<7,n=0;n<1<<te[i]-7;n++)K[256+o++]=i;for(h=0;h<=j;h++)c[h]=0;for(n=0;n<=143;)T[2*n+1]=8,n++,c[8]++;for(;n<=255;)T[2*n+1]=9,n++,c[9]++;for(;n<=279;)T[2*n+1]=7,n++,c[7]++;for(;n<=287;)T[2*n+1]=8,n++,c[8]++;for(he(T,f+1,c),n=0;n<_;n++)X[2*n+1]=5,X[2*n]=Z(n,5);Y=new G(T,_e,u+1,f,j),ae=new G(X,te,0,_,j),W=new G(new Array(0),B,0,y,m)}(),t=!0),a.l_desc=new J(a.dyn_ltree,Y),a.d_desc=new J(a.dyn_dtree,ae),a.bl_desc=new J(a.bl_tree,W),a.bi_buf=0,a.bi_valid=0,re(a)},v._tr_stored_block=r,v._tr_flush_block=function(a,n,h,l){var i,o,c=0;0<a.level?(a.strm.data_type===2&&(a.strm.data_type=function(d){var b,U=4093624447;for(b=0;b<=31;b++,U>>>=1)if(1&U&&d.dyn_ltree[2*b]!==0)return w;if(d.dyn_ltree[18]!==0||d.dyn_ltree[20]!==0||d.dyn_ltree[26]!==0)return x;for(b=32;b<u;b++)if(d.dyn_ltree[2*b]!==0)return x;return w}(a)),oe(a,a.l_desc),oe(a,a.d_desc),c=function(d){var b;for(e(d,d.dyn_ltree,d.l_desc.max_code),e(d,d.dyn_dtree,d.d_desc.max_code),oe(d,d.bl_desc),b=y-1;3<=b&&d.bl_tree[2*V[b]+1]===0;b--);return d.opt_len+=3*(b+1)+5+5+4,b}(a),i=a.opt_len+3+7>>>3,(o=a.static_len+3+7>>>3)<=i&&(i=o)):i=o=h+5,h+4<=i&&n!==-1?r(a,n,h,l):a.strategy===4||o===i?(R(a,2+(l?1:0),3),de(a,T,X)):(R(a,4+(l?1:0),3),function(d,b,U,C){var Q;for(R(d,b-257,5),R(d,U-1,5),R(d,C-4,4),Q=0;Q<C;Q++)R(d,d.bl_tree[2*V[Q]+1],3);s(d,d.dyn_ltree,b-1),s(d,d.dyn_dtree,U-1)}(a,a.l_desc.max_code+1,a.d_desc.max_code+1,c+1),de(a,a.dyn_ltree,a.dyn_dtree)),re(a),l&&P(a)},v._tr_tally=function(a,n,h){return a.pending_buf[a.d_buf+2*a.last_lit]=n>>>8&255,a.pending_buf[a.d_buf+2*a.last_lit+1]=255&n,a.pending_buf[a.l_buf+a.last_lit]=255&h,a.last_lit++,n===0?a.dyn_ltree[2*h]++:(a.matches++,n--,a.dyn_ltree[2*(q[h]+u+1)]++,a.dyn_dtree[2*N(n)]++),a.last_lit===a.lit_bufsize-1},v._tr_align=function(a){var n;R(a,2,3),M(a,O,T),(n=a).bi_valid===16?(z(n,n.bi_buf),n.bi_buf=0,n.bi_valid=0):8<=n.bi_valid&&(n.pending_buf[n.pending++]=255&n.bi_buf,n.bi_buf>>=8,n.bi_valid-=8)}},{"../utils/common":1}],8:[function(D,H,v){"use strict";H.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}},{}],"/lib/deflate.js":[function(D,H,v){"use strict";var E=D("./zlib/deflate"),w=D("./utils/common"),x=D("./utils/strings"),g=D("./zlib/messages"),p=D("./zlib/zstream"),A=Object.prototype.toString,u=0,f=-1,_=0,y=8;function k(S){if(!(this instanceof k))return new k(S);this.options=w.assign({level:f,method:y,chunkSize:16384,windowBits:15,memLevel:8,strategy:_,to:""},S||{});var m=this.options;m.raw&&0<m.windowBits?m.windowBits=-m.windowBits:m.gzip&&0<m.windowBits&&m.windowBits<16&&(m.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new p,this.strm.avail_out=0;var O=E.deflateInit2(this.strm,m.level,m.method,m.windowBits,m.memLevel,m.strategy);if(O!==u)throw new Error(g[O]);if(m.header&&E.deflateSetHeader(this.strm,m.header),m.dictionary){var F;if(F=typeof m.dictionary=="string"?x.string2buf(m.dictionary):A.call(m.dictionary)==="[object ArrayBuffer]"?new Uint8Array(m.dictionary):m.dictionary,(O=E.deflateSetDictionary(this.strm,F))!==u)throw new Error(g[O]);this._dict_set=!0}}function j(S,m){var O=new k(m);if(O.push(S,!0),O.err)throw O.msg||g[O.err];return O.result}k.prototype.push=function(S,m){var O,F,I=this.strm,ie=this.options.chunkSize;if(this.ended)return!1;F=m===~~m?m:m===!0?4:0,typeof S=="string"?I.input=x.string2buf(S):A.call(S)==="[object ArrayBuffer]"?I.input=new Uint8Array(S):I.input=S,I.next_in=0,I.avail_in=I.input.length;do{if(I.avail_out===0&&(I.output=new w.Buf8(ie),I.next_out=0,I.avail_out=ie),(O=E.deflate(I,F))!==1&&O!==u)return this.onEnd(O),!(this.ended=!0);I.avail_out!==0&&(I.avail_in!==0||F!==4&&F!==2)||(this.options.to==="string"?this.onData(x.buf2binstring(w.shrinkBuf(I.output,I.next_out))):this.onData(w.shrinkBuf(I.output,I.next_out)))}while((0<I.avail_in||I.avail_out===0)&&O!==1);return F===4?(O=E.deflateEnd(this.strm),this.onEnd(O),this.ended=!0,O===u):F!==2||(this.onEnd(u),!(I.avail_out=0))},k.prototype.onData=function(S){this.chunks.push(S)},k.prototype.onEnd=function(S){S===u&&(this.options.to==="string"?this.result=this.chunks.join(""):this.result=w.flattenChunks(this.chunks)),this.chunks=[],this.err=S,this.msg=this.strm.msg},v.Deflate=k,v.deflate=j,v.deflateRaw=function(S,m){return(m=m||{}).raw=!0,j(S,m)},v.gzip=function(S,m){return(m=m||{}).gzip=!0,j(S,m)}},{"./utils/common":1,"./utils/strings":2,"./zlib/deflate":5,"./zlib/messages":6,"./zlib/zstream":8}]},{},[])("/lib/deflate.js")});
