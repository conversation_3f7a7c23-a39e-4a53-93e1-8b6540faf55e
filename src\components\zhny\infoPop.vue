<template>
  <div class="tkBox">
    <div class="tkTitle">
      <span>产业园信息</span>
    </div>
    <img @click="closeEmitai" src="@/assets/zhny/map/tkGb.png" class="tkGb" />
    <div class="info">
      <img class="logo_" src="@/assets/zhny/map/info1.png" alt="" />
      <ul>
        <li>
          <div>
            <p>总面积:</p>
            <p>{{ infoData.area }}<span>亩</span></p>
          </div>
        </li>
        <li>
          <div>
            <p>稻米产业:</p>
            <p>{{ infoData.area1 }}<span>亩</span></p>
          </div>
        </li>
        <li>
          <div>
            <p>水产产业:</p>
            <p>{{ infoData.area2 }}<span>亩</span></p>
          </div>
        </li>
        <li>
          <div>
            <p>园艺产业:</p>
            <p>{{ infoData.area3 }}<span>亩</span></p>
          </div>
        </li>
        <li>
          <div>
            <p>育秧中心:</p>
            <p>{{ infoData.area4 }}<span>亩</span></p>
          </div>
        </li>
        <!-- <li>
          <div>
            <p>高标准农田:</p>
            <p>{{ infoData.area5 }}<span>亩</span></p>
          </div>
        </li> -->
      </ul>
      <div v-if="false">
        <div class="title">{{ infoData.name }}</div>
        <ul>
          <li>
            <span class="left">企业类型：</span>
            <span class="right">{{ infoData.type }}</span>
          </li>
          <li>
            <span class="left">联系人：</span>
            <span class="right">{{ infoData.peopelName }}</span>
          </li>
          <li>
            <span class="left">联系电话：</span>
            <span class="right">{{ infoData.phone }}</span>
          </li>
          <li>
            <span class="left">企业地址：</span>
            <span class="right">{{ infoData.address }}</span>
          </li>
        </ul>
      </div>
    </div>
    <div class="introduce">
      <div class="title">
        <span>{{ infoData.name }}</span>
      </div>

      <div class="introduce_box">
        <!-- <div v-html="gljjValue"></div> -->
        <p>{{ infoData.sysnopsis }}</p>
      </div>
    </div>
    <div class="pics">
      <div>
        <img @click="showPic(0)" class="main_pic" :src="firstPic" alt="" />
      </div>
      <ul>
        <li
          @click="showPic(index + 1)"
          v-for="(item, index) in eImageList.slice(1, 5)"
          :key="index"
        >
          <img :src="item" alt="" />
        </li>
      </ul>
    </div>
    <!-- 图片放大 -->
    <showBigImg v-if="showViewer" :url-list="eImageList" @close-viewer="closeViewer"></showBigImg>
  </div>
</template>

<script>
import showBigImg from '@/components/common/showBigImg.vue'
export default {
  components: { showBigImg },
  name: 'infoPop',
  props: {
    infoData: {
      type: Object,
      default: () => ({
        name: '湖熟现代农业示范区',
        type: '农业服务',
        peopelName: '李晓飞',
        phone: '13890234897',
        address: '南京市江宁区湖熟街道湖熟现代农业示范园',
        synopsis:
          '湖熟现代农业示范区位于南京市主城区东南部，自古就是“鱼米之乡”，地势平坦、水网密集、光照充足、兩量充沛，自然环境优越，适宜各种优质果蔬生长，素有“小南京"的美誉。湖熟街道距南京市主城区20公里，距南京禄口国际机场13公里，距新生圩港37公里，宁杭高速、省道337穿境而过。即将开通的龙眠大道南延线全长6公里，极大地拉近了湖熟与主城区的距离，10分钟内即可到达地铁一号线南延线的“中国药科大学”站，“五分钟上高速、十五分钟到机场、二十分钟到市中心”已成为湖熟的对外交通新格局。湖熟菊花展分为露天菊花和温室大棚展览，露天的菊花品种已经开放了60%-70%，温室种植的菊花则开放了30%-40%。菊花品种,在湖熟菊花园，400亩菊花形成壮观花海，争奇斗艳。园内共有3300多个品种的菊花，不仅有各种传统秋菊，更有绿色大药、草莓药等少见品种，双色、绿色、间色等市场稀有品种菊花，在这里都可以找到。',
      }),
    },
  },
  data() {
    return {
      gljjValue: '',
      firstPic: '',
      picArr: [],
      showViewer: false,
      eImageList: [],
    }
  },
  mounted() {
    if (this.infoData.tbAppendixList && this.infoData.tbAppendixList.length > 0) {
      this.firstPic = this.infoData.tbAppendixList[0].url
      this.eImageList = this.infoData.tbAppendixList.map((it) => it.url)
    }

    // 获取滚动容器和文本元素
    const scrollingText = document.querySelector('.introduce_box')
    const text = document.querySelector('.introduce_box p')
    // 获取文本元素的高度
    const textHeight = text.offsetHeight
    // console.log('text.offsetHeight',text.offsetHeight);
    // console.log('scrollingText.scrollingText',scrollingText.offsetHeight);
    // 定义滚动函数
    function scroll() {
      // console.log('scrollingText.scrollTop', scrollingText.scrollTop)
      // 如果文本元素已经完全滚动出去，则将其重置到滚动容器的顶部
      if (scrollingText.scrollTop >= text.offsetHeight - scrollingText.offsetHeight - 1) {
        scrollingText.scrollTop = 0
      }
      // 否则，将滚动容器向上滚动一个像素的距离
      else {
        scrollingText.scrollTop += 1
      }
    }

    // 每隔毫秒调用一次滚动函数
    this.timer = window.setInterval(scroll, 100)
  },
  methods: {
    closeEmitai() {
      this.$emit('close')
    },
    async glglPopgljj() {
      let res = await glglPopgljj()
      if (res.code == '200') {
        this.gljjValue = '<p>' + res.result[0].sysvalue + '</p>'
        console.log(this.gljjValue, 'this.gljjValue')
      }
    },
    showPic(index) {
      const saveList = JSON.parse(JSON.stringify(this.eImageList))
      saveList.unshift(saveList[index])
      saveList.splice(index + 1, 1)
      this.eImageList = saveList
      this.showViewer = true
    },
    closeViewer() {
      this.showViewer = false
    },
  },
  computed: {
    box3Initoptions() {
      return {
        yAxis: {
          name: '人',
          nameTextStyle: {
            fontSize: 18,
          },
          axisLabel: {
            textStyle: {
              fontSize: 18,
            },
          },
        },
        xAxis: {
          axisLabel: {
            show: true,
            textStyle: {
              fontSize: 18,
            },
          },
        },
        dataZoom: [
          {
            type: 'inside', // 内置型式的 dataZoom 组件
            xAxisIndex: 0, // 对应 x 轴的索引，默认为 0
            start: 0, // 起始位置的百分比
            end: 30, // 结束位置的百分比
            // realtime: true // 启用实时滚动
          },
        ],
      }
    },
  },
}
</script>

<style lang="less" scoped>
.tkBox {
  width: 1266px;
  height: 787px;
  background: url(~@/assets/zhny/map/tkBg.png) no-repeat center / 100% 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10000;
  padding: 0 43px;

  .tkTitle {
    width: 1054px;
    height: 74px;
    background: url(~@/assets/zhny/map/tkTitle.png) no-repeat center / 100% 100%;
    margin: 0 auto;
    margin-top: 22px;
    line-height: 74px;
    span {
      font-size: 36px;
      font-family: SourceHanSansCN-Bold;
      color: #ffffff;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .tkGb {
    position: absolute;
    top: 38px;
    right: 43px;
    cursor: pointer;
  }
  .info {
    position: absolute;
    top: 138px;
    left: 73px;
    width: 578px;
    height: 194px;
    border-radius: 10px;
    border: 1px solid #0f51a1;
    display: flex;
    justify-content: space-between;
    .logo_ {
      width: 123px;
      height: 131px;
      margin: 33px 0 0 19px;
    }
    .title {
      margin-top: 18px;
      font-size: 28px;
      font-family: YouSheBiaoTiHei;
      color: #4ceeff;
      line-height: 33px;
      text-shadow: 0px 0px 3px rgba(255, 255, 255, 0.5);
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-align: left;
    }
    ul {
      padding: 20px 20px 20px 60px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: space-between;
      li {
        width: 50%;
        display: flex;
        align-items: center;
        // &:nth-of-type(1) {
        //   margin-bottom: 56px;
        // }
        & > div {
          display: flex;
          align-items: center;
          & p:first-of-type {
            font-size: 16px;
            font-weight: normal;
            color: #ffffff;
            line-height: 26px;
          }
          & p:last-of-type {
            margin-left: 10px;
            text-align: left;
            font-size: 18px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 20px;
            span {
              margin-left: 6px;
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
            }
          }
        }
      }
    }
    // ul {
    //   li {
    //     margin-top: 12px;
    //     display: flex;
    //     justify-content: space-between;
    //     .left {
    //       font-size: 14px;
    //       font-family: PingFangSC-Regular, PingFang SC;
    //       font-weight: 400;
    //       color: #ffffff;
    //       line-height: 20px;
    //     }
    //     .right {
    //       font-size: 14px;
    //       font-family: PingFangSC-Medium, PingFang SC;
    //       font-weight: 500;
    //       color: #ffffff;
    //       line-height: 20px;
    //       width: 70%;
    //       white-space: nowrap;
    //       text-overflow: ellipsis;
    //       overflow: hidden;
    //     }
    //   }
    // }
  }
  .introduce {
    position: absolute;
    top: 345px;
    left: 63px;
    width: 598px;
    height: 397px;
    margin: 23px auto 0;
    background: url(~@/assets/zhny/map/introWord.png) no-repeat;
    background-size: 100% 100%;
    .title {
      background: url(~@/assets/zhny/map/introTitle.png) no-repeat;
      background-size: 100% 100%;
      width: 575px;
      height: 42px;
      margin: 6px auto 0;
      span {
        font-size: 28px;
        font-family: SourceHanSansCN-Bold;
        color: #4ceeff;
        line-height: 42px;
        // text-shadow: 0px 0px 3px rgba(255, 255, 255, 0.5);
        background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
    .introduce_box {
      padding: 16px 37px 0px;
      height: calc(100% - 42px - 28px);
      overflow: hidden;
      position: relative;
      & > p {
        text-align: left;
        font-size: 16px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 28px;
      }
    }
  }
  .pics {
    width: 515px;
    position: absolute;
    right: 73px;
    top: 135px;
    .main_pic {
      width: 515px;
      height: 290px;
    }
    ul {
      width: 100%;
      height: 292px;
      margin-top: 11px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: space-between;
      li {
        img {
          width: 249px;
          height: 140px;
        }
      }
    }
  }
}
</style>
