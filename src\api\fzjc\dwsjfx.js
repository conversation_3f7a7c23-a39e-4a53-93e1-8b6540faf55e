import http from '@/utils/fzjc/request';
import { finaUrl } from '@/utils/fzjc/const';

// 归口
export const areaAnalysis = () => {
  return http.get(
    finaUrl + '/api/assistantDecision/areaAnalysis'
  );
};

// 归口
export const classifyAnalysis = () => {
  return http.get(
    finaUrl + '/api/assistantDecision/classifyAnalysis'
  );
};

// 归口
export const township = (obj) => {
  return http.get(
    finaUrl + '/api/assistantDecision/classifyAnalysisDetail/township',obj
  );
};

// 归口
export const village = (obj) => {
  return http.get(
    finaUrl + '/api/assistantDecision/classifyAnalysisDetail/village',obj
  );
};

// 归口
export const yearOnYearEventAnalysis = (obj) => {
  return http.get(
    finaUrl + '/api/assistantDecision/yearOnYearEventAnalysis',obj
  );
};

// 归口
export const deptAnalysisDetail = (obj) => {
  return http.get(
    finaUrl + '/api/assistantDecision/deptAnalysisDetail',obj
  );
};

// 归口
export const completeTimeAnalysis = (obj) => {
  return http.get(
    finaUrl + '/api/assistantDecision/completeTimeAnalysis',obj
  );
};

// 归口
export const outTimeEventAnalysis = () => {
  return http.get(
    finaUrl + '/api/assistantDecision/outTimeEventAnalysis'
  );
};

// 事件来源
export const sjly = () => {
  return http.get(
    finaUrl + '/api/eventSource/getEventSourceList?flag=3'
  );
};



// 工作分析 
export const gzfxJk = (dateType,startDateValue,endDateValue,dimension,analysisType,page,size) => {
  return http.get(
    '/api/assistantDecision/workAnalysis?dateType='+ dateType 
    +'&startDateValue=' + startDateValue 
    +'&endDateValue='+ endDateValue
    +'&dimension=' + dimension
    +'&analysisType=' + analysisType
    +'&page=' + page
    +'&size=' + size
  );
};

// 事件趋势分析 上报数量 办结率
export const sjqsfx = () => {
  return http.get(
    finaUrl + '/api/eventTrend/getTrend?type=m'
  );
};
// 热力图
export const pointList = () => {
  return http.get(
    finaUrl + '/api/aqyh/orderList?level=' + 2 + '&type=' + 2
  );
};
// 事件来源占比
export const sjlyzb = () => {
  return http.get(
    finaUrl + '/api/eventSource/getEventSourceList?flag=2'
  );
};

// 归口
export const getOrderClassifyTree = () => {
  return http.get(
    finaUrl + '/api/orderClassify/getOrderClassifyTree'
  );
};

// 归口
export const overview = () => {
  return http.get(
    finaUrl + '/api/assistantDecision/overview'
  );
};

// 地图打点
export const getAllOrderInfo = () => {
  return http.get(
    finaUrl + '/api/orderInfo/getAllOrderInfo'
  );
};

export const getOrderByType = (data) => {
  return http.get(
    finaUrl +  '/api/eventTrend/getOrderByType',data
  );
};