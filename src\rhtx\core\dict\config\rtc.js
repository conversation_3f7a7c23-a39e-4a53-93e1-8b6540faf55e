/*
 * @Author: fanjialong <EMAIL>
 * @Date: 2023-04-15 10:29:09
 * @LastEditors: fanjialong <EMAIL>
 * @LastEditTime: 2023-06-12 09:28:28
 * @FilePath: \hs_dp\src\rhtx\core\dict\config\rtc.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
//视频约束
const DEFAULT_VIDEO_CONSTRAINT = {
  width: 320,
  height: 240,
  frameRate: { ideal: 30, max: 30, min: 30 }
};

//语音约束
const DEFAULT_AUDIO_CONSTRAINT = {
  autoGainControl: true
};

//共享屏幕约束
const DEFAULT_DISPLAY_CONSTRAINT = {
  audio: true,
  video: { width: 640, height: 360 }
};

// 视频码率
const DEFALT_BIT_RANGE = 2048;

export {
  DEFAULT_VIDEO_CONSTRAINT,
  DEFAULT_AUDIO_CONSTRAINT,
  DEFAULT_DISPLAY_CONSTRAINT,
  DEFALT_BIT_RANGE
};
