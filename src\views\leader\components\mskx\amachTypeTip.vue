<template>
    <div class="container">
        <div class="box">
            <p>{{ name }}：</p>
            <div class="select">
                <Select v-model="value1" clearable filterable @on-change="handleOption">
                    <Option v-for="item in typeOptions" :value="item.agmachTypeCode" :key="item.agmachTypeName">{{item.agmachTypeName}}</Option>
                </Select>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    components: {},
    props: {
        name: {
            type: String,
            default: '农机类型'
        },
        value: {
            type: String,
            default: ''
        },
        typeOptions: {
            type: Array,
            default: () => [
            ]
        },
    },
    data() {
        return {
            value1:this.value
        }
    },
    created() { },
    mounted() { },
    watch: {},
    methods: {
        handleOption(e) {
            this.$emit('change', e)
        },
    },
};
</script>
<style lang="scss" scoped>
.container {
    position: absolute;
    top: 197px;
    left: 829px;
    z-index: 1001;
    width: 255px;
    height: 120px;
    background: url(~@/assets/mskx/bg_tooltip.png) no-repeat;
    background-size: 100% 100%;
    padding: 30px 41px;
    display: flex;
    justify-content: space-between;
    flex-direction: column;

    :deep(.ivu-select-input) {
        color: #fff !important;
    }

    p {
        // padding-left: 20px;
        height: 20px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #ffffff;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        margin-bottom: 5px;
    }

    .box {
        width: 100%;

        .select {
            // padding-left: 20px;
        }
    }
}
</style>