<template>
  <div class="tkBox">
    <div class="tkTitle"><span>市场主体</span></div>
    <img @click="closeEmitai" src="@/assets/hszl/tkGb.png" class="tkGb" />
    <div class="tabs">
      <div
        class="tab"
        :class="{ active: tabActive1 === it.value }"
        v-for="(it, i) of tabs"
        :key="i"
        @click="changeTab(it, i)"
      >
        <div class="item_">
          <span>{{ it.num }}</span>
          <span>{{ it.name }}</span>
        </div>
      </div>
    </div>
    <div class="table" v-if="tabActive1 == '0'">
      <SwiperTable
        :titles="['序号', '企业名称', '所属社区', '企业类型', '联系人', '联系电话']"
        :widths="['15%', '20%', '15%', '15%', '20%', '15%']"
        :data="tableData1"
        :contentHeight="'440px'"
        :isNeedOperate="true"
      ></SwiperTable>
    </div>
    <div class="table" v-else>
      <SwiperTable
        :titles="['序号', '企业名称']"
        :widths="['10%', '80%']"
        :data="tableData"
        :contentHeight="'440px'"
        :isNeedOperate="true"
      ></SwiperTable>
    </div>
    <div class="fy_page">
      <Page :current="pageNum" :total="total" @on-change="pageNumChange" show-total></Page>
    </div>
  </div>
</template>

<script>
import SwiperTable from '@/components/leader/leader_zwjs/SwiperTable.vue'
// import { getRkfb, getSczt, getScztPage,getScztJxcsPage } from '@/api/hs/hs.api.js'
import { getSczt, getScztPage } from '@/api/hs/hs.hszl.js'

export default {
  name: 'sczttk',
  components: {
    SwiperTable,
  },
  props: {
    tabActive: {
      type: [Number, String],
      default: 0,
    },
  },
  data() {
    return {
      tabs: [
        { name: '高企', value: '57' },
        { name: '规上', value: '98' },
        { name: '独角兽', value: '1' },
        { name: '瞪羚', value: '5' },
        { name: '专精特新', value: '9' },
        { name: '科技型', value: '105' },
        { name: '九小场所', value: '9030' },
        { name: '生成型企业', value: '414' },
        { name: '办公型企业', value: '605' },
        { name: '仓储企业', value: '150' },
      ],
      // tabActive1: 0,
      tableData1: [],
      tableData2: [],
      tableData3: [],
      tableData4: [],
      tableData5: [],
      tableData6: [],
      tableData: [],
      tableData10: [],
      currentType: 0,
      total: 0,
      pageNum: 1,
      pageSize: 10,
      tabActive1:this.tabActive
    }
  },
  methods: {
    closeEmitai() {
      this.$emit('closeEmitai')
    },
    async getRkfbFn() {
      let res = await getRkfb()
      console.log(1111, res)
      this.box2data = [['produce', ''], ...res.result.map((it) => [it.name, it.sysvalue])]
    },
    changeTab(it, i) {
      this.pageNum = 1
      this.tabActive1 = it.value
      this.currentType = it.value
      // console.log('this.currentType', this.currentType)
      switch (this.currentType) {
        case '0':
          this.getTable1()
          break
        default:
          this.getTable()
          break
      }
    },
    async getScztFn() {
      this.tabs = []
      let res = await getSczt()
      if (res?.code == '200') {
        this.tabs = res.data
        this.currentType = this.tabActive1
        switch (this.currentType) {
          case '0':
            this.getTable1(this.currentType)
            break
          default:
            this.getTable(this.currentType)
            break
        }
      }
    },
    async getTable1() {
      let res = await getScztPage(this.currentType, this.pageNum, this.pageSize)
      if (res?.code == '200') {
        this.tableData1 = res.data.data.map((it, i) => [
          this.pageSize * (this.pageNum - 1) + i + 1,
          it.enterpriseName,
          it.range,
          it.industry,
          it.linkPerson,
          it.linkPhone,
        ])
        this.total = res.data.recordsTotal
      }
    },
    async getTable() {
      let res = await getScztPage(this.currentType, this.pageNum, this.pageSize)
      if (res?.code == '200') {
        this.tableData = res.data.data.map((it, i) => [
          this.pageSize * (this.pageNum - 1) + i + 1,
          it.enterpriseName,
        ])
        this.total = res.data.recordsTotal
      }
    },

    pageNumChange(val) {
      this.pageNum = val
      switch (this.currentType) {
        case '0':
          this.getTable1()
          break
        default:
          this.getTable()
          break
      }
    },
  },
  mounted() {
    this.getScztFn()
  },
  computed: {
    box2Initoptions() {
      return {
        yAxis: {
          name: '人',
          nameTextStyle: {},
          axisLabel: {
            textStyle: {},
          },
        },
        xAxis: {
          axisLabel: {
            show: true,
            textStyle: {
              fontSize: 14,
            },
          },
        },
        dataZoom: [
          {
            type: 'inside', // 内置型式的 dataZoom 组件
            xAxisIndex: 0, // 对应 x 轴的索引，默认为 0
            start: 0, // 起始位置的百分比
            end: 10, // 结束位置的百分比
            // realtime: true // 启用实时滚动
          },
        ],
      }
    },
  },
}
</script>

<style lang="less" scoped>
.tkBox {
  width: 1700px;
  height: 843px;
  background: url(~@/assets/hszl/tkBg.png) no-repeat center / 100% 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10000;
  padding: 0 40px;
  .tkTitle {
    width: 634px;
    height: 74px;
    background: url(~@/assets/hszl/tkTitle.png) no-repeat center / 100% 100%;
    margin: 0 auto;
    margin-top: 22px;
    line-height: 74px;
    span {
      font-size: 36px;
      font-family: PangMenZhengDao;
      color: #ffffff;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .tkGb {
    position: absolute;
    top: 38px;
    right: 43px;
  }
  .tabs {
    display: flex;
    gap: 13px;
    margin: 28px 20px 28px 20px;
    .tab {
      width: 191px;
      line-height: 41px;
      background: url('~@/assets/map/dialog/bg5.png') no-repeat center / 100% 100%;
      cursor: pointer;
      .item_ {
        display: flex;
        flex-direction: column;
      }
      span {
        font-size: 24px;
        font-family: PangMenZhengDao;
        color: #ffffff;
      }
      &.active {
        background: url('~@/assets/map/dialog/bg4.png') no-repeat center / 100% 100%;
        span {
          font-family: PangMenZhengDao;
          background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
  }
  .table {
    padding: 0 20px;
  }
  /deep/ .ivu-page-item {
    background: transparent;
    color: #fff;
  }
  /deep/ .ivu-page-item a {
    color: #fff;
  }
  /deep/ .ivu-page-prev,
  /deep/ .ivu-page-next {
    background: transparent;
  }
  .fy_page {
    margin-top: 70px;
  }
}
</style>
