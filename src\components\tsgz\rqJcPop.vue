<template>
  <div class="ai_waring">
    <div class="title">
      <span>滨河实验小学</span>
    </div>
    <img @click="closeEmitai" class="close_btn" src="@/assets/jcyj/jc_close.png" alt />
    <div class="content">
      <div class="top">
        <div class="icon"></div>
        <ul>
          <li>
            <div class="label">信号强度：</div>
            <div class="value">15</div>
          </li>
          <li>
            <div class="label">电量：</div>
            <div class="value">正常</div>
          </li>
          <li>
            <div class="label">更新时间：</div>
            <div class="value">2023-3-23 09:09:00</div>
          </li>
          <li>
            <div class="label">电量：</div>
            <div class="value">正常</div>
          </li>
          <li>
            <div class="label">设备类型：</div>
            <div class="value">燃气报警器</div>
          </li>
          <li>
            <div class="label">设备地址：</div>
            <div class="value">中桥市场烟雾报警器23号点位</div>
          </li>
        </ul>
      </div>
      <!-- <div class="mid">
        <div class="item">
          <div class="label">设备地址：</div>
          <div class="value">沁扬市场</div>
        </div>
        <div class="item">
          <div class="label">设备类型：</div>
          <div class="value">燃气报警器</div>
        </div>
      </div>-->
      <div class="foot">
        <div class="cont">
          <div class="table_box">
            <SwiperTableWlsb
              :titles="['时间', '燃气监测指标', '阈值', '异常值']"
              :widths="['40%', '20%', '20%', '20%']"
              :data="sjtjData"
              :contentHeight="'90px'"
              :tabelHeight="'30px'"
            ></SwiperTableWlsb>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SwiperTableWlsb from '@/components/shzl/SwiperTableAnomaly.vue'
export default {
  components: { SwiperTableWlsb },
  props: {
    popInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      sjtjData: [
        ['2023-3-23 09:09:00', '烟雾度', '0.5', '0.6'],
        ['2023-3-23 08:09:00', '烟雾度', '0.5', '0.55']
      ],
      aTabIdx: 0
    }
  },
  methods: {
    closeEmitai() {
      this.$emit('closeEmitai')
    }
  }
}
</script>

<style lang="less" scoped>
.ai_waring {
  position: relative;
  width: 586px;
  background: rgba(9, 19, 34, 0.9);
  box-shadow: 0px 0px 22px 0px rgba(11, 54, 138, 0.35), 0px 2px 8px 0px rgba(0, 0, 0, 0.7),
    inset 0px 0px 8px 0px rgba(17, 146, 214, 0.35);
  border-radius: 11px;
  border: 1px solid #015c8c;
  z-index: 1000;
  padding-bottom: 40px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  .close_btn {
    position: absolute;
    top: 26px;
    right: 31px;
    cursor: pointer;
  }
  .warn {
    position: absolute;
    font-size: 14px;
    font-family: SourceHanSansCN-Normal, SourceHanSansCN;
    font-weight: 400;
    color: #df0404;
    line-height: 21px;
    top: 85px;
    right: 40px;
  }

  .title {
    position: absolute;
    background: url(~@/assets/jcyj/jc_title.png) no-repeat;
    width: 445px;
    height: 88px;
    left: 50%;
    top: -16px;
    transform: translateX(-50%);
    background-size: 100% 100%;

    span {
      background-clip: text;
      font-size: 36px;
      font-family: PangMenZhengDao;
      color: #ffffff;
      line-height: 110px;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .content {
    width: 100%;
    margin-top: 100px;
    padding: 0 52px;
    .top {
      width: 100%;
      display: flex;
      gap: 47px;
      .icon {
        width: 165px;
        height: 217px;
        background: url(~@/assets/jcyj/icon18.png) no-repeat;
        flex-shrink: 0;
      }
      ul {
        padding: 10px 0;
        display: flex;
        flex-direction: column;
        gap: 22px;
        li {
          display: flex;
          gap: 40px;
          text-align: left;
          .label {
            width: 73px;
            font-size: 14px;
            font-family: SourceHanSansCN-Normal, SourceHanSansCN;
            font-weight: 400;
            color: #6a92bb;
            line-height: 21px;
            flex-shrink: 0;
          }
          .value {
            font-size: 14px;
            font-family: SourceHanSansCN-Normal, SourceHanSansCN;
            font-weight: 400;
            color: #00eaff;
            line-height: 21px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .active {
            color: #df0404;
          }
        }
      }
    }
    .mid {
      display: flex;
      flex-direction: column;
      padding: 0 40px;
      margin-top: 10px;
      gap: 18px;
      .item {
        display: flex;
        gap: 60px;
        .label {
          width: 73px;
          display: flex;
          font-size: 14px;
          font-family: SourceHanSansCN-Normal, SourceHanSansCN;
          font-weight: 400;
          color: #6a92bb;
          line-height: 21px;
        }
        .value {
          font-size: 14px;
          font-family: SourceHanSansCN-Normal, SourceHanSansCN;
          font-weight: 400;
          color: #ffffff;
          line-height: 21px;
        }
      }
    }
    .foot {
      margin-top: 20px;
      .label {
        text-align: left;
        font-size: 18px;
        font-family: SourceHanSansCN-Normal, SourceHanSansCN;
        font-weight: 400;
        color: #ffffff;
        line-height: 21px;
        margin-bottom: 15px;
      }
    }
  }
}
</style>
