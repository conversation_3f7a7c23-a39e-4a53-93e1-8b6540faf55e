<template>
	<div class="child-block" :style="{ height: blockHeight + 'px' }">
		<div class="content">
			<slot></slot>
			<div class="cont">
				<el-form ref="form" :model="form" label-width="90px">
					<div class="rwxfTitle">
						<img class="rwxfImg" src="@/assets/bjnj/rwxf1.png" alt="" />
						<div class="rwxfTitle2">任务下发</div>
						<div class="rwxfBtn" @click="tjrw"><span>添加任务</span></div>
					</div>
					<div class="rwxfList" v-for="(item, index) of form.departments" :key="index">
						<img class="rwxfImg2" src="@/assets/bjnj/rwxf5.png" v-if="index != 0" @click="scrw(index)" alt="" />
						<el-form-item label="下级单位：">
							<el-select v-model="item.deptId" placeholder="请选择下级单位" @change="deptChange($event, index)">
								<el-option :label="its.deptName" :value="its.deptId" v-for="(its, ind) in departments_all" :key="ind"></el-option>
							</el-select>
							<el-input v-model="item.username" placeholder="联系人"></el-input>
							<el-input v-model="item.phone" placeholder="联系方式"></el-input>
						</el-form-item>
						<el-form-item label="所需农机：" class="ssnj">
							<div class="sxnjList" v-for="(it, i) of item.resourceList" :key="i">
								<el-select v-model="it.type" placeholder="请选择所需农机" @change="resourceChange($event, i, index)">
									<el-option :label="itn.name" :value="itn.type" v-for="(itn, inx) in resources_all" :key="inx"></el-option>
								</el-select>
								<div class="njsl-box">
									<el-input
										type="number"
										:max="it.max"
										:min="1"
										v-model="it.num"
										:placeholder="placeholder"
										@blur="blurText($event)"
									></el-input>
									<img
										class="plus-icon"
										src="@/assets/bjnj/rwxf3.png"
										@click="tjsxnj(index)"
										v-if="item.resourceList.length == i + 1"
										alt=""
									/>
									<img class="minus-icon" src="@/assets/bjnj/rwxf4.png" @click="scsxnj(index, i)" v-if="i != 0" alt="" />
								</div>
							</div>
						</el-form-item>
					</div>
				</el-form>
				<div class="contBtnbox">
					<div class="contBtn" @click="qxgd">取消</div>
					<div class="contBtn" @click="rwxfgd">任务下发</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		contentNumberUnitOne: {
			type: String,
			default: '万',
		},

		contentDataList: {
			type: Array,
			default: () => [],
		},

		blockHeight: {
			type: Number,
			default: 350,
		},
		dataObj: {
			type: Object,
			default: () => {},
		},
		typeShow3: {
			type: Number,
			default: 1,
		},
		data4: {
			type: Array,
			default: () => [],
		},
		data14: {
			type: Array,
			default: () => [],
		},
		form: {
			type: Object,
			default: () => {},
		},
		departments_all: {
			type: Array,
			default: () => [],
		},
		resources_all: {
			type: Array,
			default: () => [],
		},
		placeholder: {
			type: String,
			default: '农机数量',
		},
	},
	data() {
		return {
			currentIndex: 0,
			isActive: true,
			isOpenOtherData: false,
			otherDataList: [],
			currentTabIndex: 0,
			activeNames: ['1'],
		}
	},
	computed: {
		otherArgmachNumber() {
			let otherArgmachNumber = 0

			this.otherDataList.forEach((item) => {
				otherArgmachNumber += Number(item.number)
			})
			return otherArgmachNumber
		},
	},
	methods: {
		clickchange(index) {
			this.currentIndex = index
			this.$emit('updateChange', this.currentIndex)
		},
		clickBulletFrame() {
			this.$emit('updateChange2', true)
		},
		showMoreFn() {
			this.$emit('handleShowMore', true)
		},
		showMoreFn2() {
			this.$emit('handleShowMore2', true)
		},
		handleClickOtherData() {
			this.isOpenOtherData = !this.isOpenOtherData
		},
		handleClickCloseOtherData() {
			this.isOpenOtherData = false
		},
		handleChangeTab(item, index) {
			this.currentTabIndex = index
			this.$emit('handleChangeTab', item.value)
		},
		btnSwitch(item, index) {
			this.$emit('btnSwitch', item, index)
		},
		typeSwitch3(code) {
			this.$emit('typeSwitch3', code)
		},
		handleChange() {},
		tjrw() {
			this.$emit('tjrw')
		},
		scrw(index) {
			this.$emit('scrw', index)
		},
		deptChange(val, index) {
			this.$emit('deptChange', val, index)
		},

		resourceChange(cur, idx, inx) {
			this.$emit('resourceChange', cur, idx, inx)
		},
		blurText(e) {
			this.$emit('blurText', e)
		},
		tjsxnj(index) {
			this.$emit('tjsxnj', index)
		},
		scsxnj(index, i) {
			this.$emit('scsxnj', index, i)
		},
		qxgd() {
			this.$emit('qxgd')
		},
		rwxfgd() {
			this.$emit('rwxfgd')
		},
	},
}
</script>

<style lang="less" scoped>
.child-block {
	width: 455px;
	box-sizing: border-box;
	z-index: 101;
	position: relative;

	.content {
		position: relative;
		width: 100%;
		// height: calc(100% - 74px);
		display: flex;
		justify-content: flex-start;
		flex-wrap: wrap;
		align-items: flex-start;
		overflow: auto;

		&::-webkit-scrollbar {
			width: 4px; /* 滚动条宽度 */
		}

		//轨道
		&::-webkit-scrollbar-track {
			background: #04244e; /* 滚动条轨道背景 */
			border-radius: 10px;
			padding: 5px 0;
		}

		//滑块
		&::-webkit-scrollbar-thumb {
			background: #82aed0; /* 滑块背景颜色 */
			border-radius: 10px;
			height: 15px;
		}

		//悬停滑块颜色
		&::-webkit-scrollbar-thumb:hover {
			background: #ff823b; /* 鼠标悬停时滑块颜色 */
		}

		//箭头
		&::-webkit-scrollbar-button {
			display: none; /* 隐藏滚动条的箭头按钮 */
		}

		&.no-active-content {
			height: 0;
			display: none;
		}

		.cont {
			width: 100%;
			height: 100%;
			display: flex;
			justify-content: flex-end;
			align-items: flex-start;
			flex-direction: column;
			::v-deep .el-form {
				width: 100%;
				height: 820px;
				overflow: auto;

				padding: 0 16px 0 22px;

				&::-webkit-scrollbar {
					width: 4px; /* 滚动条宽度 */
					height: 4px;
				}

				//轨道
				&::-webkit-scrollbar-track {
					background: #04244e; /* 滚动条轨道背景 */
					border-radius: 10px;
					padding: 5px 0;
				}

				//滑块
				&::-webkit-scrollbar-thumb {
					background: #82aed0; /* 滑块背景颜色 */
					border-radius: 10px;
					height: 15px;
				}

				//悬停滑块颜色
				&::-webkit-scrollbar-thumb:hover {
					background: #ff823b; /* 鼠标悬停时滑块颜色 */
				}

				//箭头
				&::-webkit-scrollbar-button {
					display: none; /* 隐藏滚动条的箭头按钮 */
				}

				&.no-active-content {
					height: 0;
					display: none;
				}
			}

			::v-deep .el-form-item {
				margin-bottom: 0;
			}

			.ssnj {
				margin-top: 30px;

				.sxnjList {
					margin-bottom: 11px;
					display: flex;
					justify-content: flex-start;
					align-items: flex-start;
					flex-direction: column;

					.plus-icon,
					.minus-icon {
						cursor: pointer;
					}
				}

				.njsl-box {
					width: 100%;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					/deep/ .el-input {
						margin-bottom: 0;
					}
					img {
						width: 28px;
						height: 28px;
						margin-left: 10px;
					}
				}

				img {
					vertical-align: middle;
					margin-left: 8px;
					margin-top: -4px;
				}
			}

			::v-deep .el-form-item__label {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #ffffff;
				line-height: 21px;
				text-align: right;
				font-style: normal;
				text-transform: none;
			}

			::v-deep .el-form-item__content {
				line-height: 20px;
			}

			.rwxfTitle {
				overflow: hidden;
				margin-top: 5px;

				.rwxfImg {
					width: 16px;
					height: 16px;
					float: left;
					margin: 5px 6px 0 0;
				}

				.rwxfTitle2 {
					float: left;
					font-family: PingFangSC, PingFang SC;
					font-size: 18px;
					color: #b3daff;
					line-height: 19px;
					letter-spacing: 1px;
					text-shadow: 0px 2px 0px rgba(0, 15, 34, 0);
					text-align: left;
					font-style: normal;
					background: linear-gradient(180deg, #ffffff 0%, #7ed1ff 100%);
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
					margin: 4px 14px 0 0;
				}

				.rwxfBtn {
					float: left;
					width: 79px;
					height: 25px;
					line-height: 25px;
					text-align: center;
					background: url(~@/assets/bjnj/rwxf2.png) no-repeat center / 100% 100%;

					span {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 12px;
						color: #ffffff;
						text-shadow: 0px 0px 8px #00ace6;
						text-align: center;
						font-style: normal;
						background: linear-gradient(180deg, #e6fdff 0%, #248fb3 100%);
						-webkit-background-clip: text;
						-webkit-text-fill-color: transparent;
					}
				}
			}

			.rwxfList {
				width: 100%;
				border-radius: 5px;
				border: 1px solid;
				border-image: linear-gradient(270deg, rgba(5, 155, 238, 0.2), rgba(5, 155, 238, 0.5), rgba(5, 155, 238, 0.2)) 1 1;
				margin-top: 24px;
				padding: 12px 14px 14px 14px;
				position: relative;

				.rwxfImg2 {
					width: 22px;
					height: 22px;
					position: absolute;
					top: 6px;
					right: 14px;
					z-index: 1;
				}

				::v-deep .el-select {
					width: 124px;
					height: 34px;
					margin-bottom: 10px;

					.el-input {
						margin-left: 0;
					}
				}

				::v-deep .el-input__inner {
					height: 34px;
					line-height: 34px;
					background: linear-gradient(180deg, rgba(0, 162, 255, 0) 0%, rgba(0, 162, 255, 0.08) 100%, rgba(0, 162, 255, 0.3) 200%),
						rgba(0, 74, 143, 0.4);
					border: 1px solid rgba(0, 162, 255, 0.6);
				}
				/deep/ .el-select {
				}
				/deep/ .el-input {
					width: 181px;
					height: 34px;
					margin-bottom: 10px;

					.el-input__inner {
						width: 181px !important;
						height: 34px !important;
						// background: linear-gradient(180deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%),
						// 	rgba(0, 74, 143, 0.4);
						background-color: #001c40 !important;
						border: 1px solid #d9d9d9 !important;
						border-radius: 4px 4px 4px 4px !important;

						font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
						font-weight: normal;
						font-size: 14px !important;
						color: #fff !important;
						line-height: 14px;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}
				}

				::v-deep .el-form-item__label {
					width: 100% !important;
					text-align: left;
					line-height: 14px;
				}

				::v-deep .el-form-item__content {
					margin-left: 0 !important;
					margin-top: 29px;
					padding-left: 20px !important;
					display: flex !important;
					flex-direction: column !important;
					justify-content: flex-start !important;
					align-items: flex-start !important;
				}
			}

			.contBtnbox {
				width: 100%;
				display: flex;
				justify-content: flex-end;
				align-items: center;

				padding: 15px 16px;

				.contBtn {
					border-radius: 4px 4px 4px 4px;
					border: 1px solid #d0deee;
					padding: 9px 16px;
					margin: 0 4px;

					display: flex;
					justify-content: center;
					align-items: center;

					cursor: pointer;

					font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
					font-weight: normal;
					font-size: 14px;
					color: #d0deee;
					line-height: 14px;
					text-align: center;
					font-style: normal;
					text-transform: none;

					&:last-child {
						background: #159aff;
						color: #fff;
						border: 1px solid #159aff;
					}

					&:first-child:hover {
						background: #159aff;
						border: 1px solid #159aff;
						color: #fff;
					}
				}
			}
		}
	}
}
</style>
