<template>
  <div class="middle_box">
    <div class="line">
      <div
        class="line_box"
        v-for="(item, index) in middleList"
        :key="index"
        :style="{ backgroundImage: 'url(' + bgArr[index] + ')' }"
      >
        <!-- <span class="num_" :style="{ color: colorArr[index] }">{{ item.num }}</span>
        <div class="info_">
          <div>
            <img :src="iconArr[index]" alt="" />
            <p>
              {{ item.name }}<span>({{ item.unit }})</span>
            </p>
          </div>
        </div> -->
        <div class="item" v-for="(it, inx) in item" :key="inx">
          <div class="num" :style="{ color: colorArr[index][inx] }">{{ it.num }}<span>{{ it.unit }}</span></div>
          <div class="name">{{ it.name }}</div>
        </div>
        <div class="fgx" v-if="item.length > 1"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    colorArr: {
      type: Array,
      default: () => [
        ['#00A0FF','#00FF62'],
        ['#00DEFF','#00FF62'],
        ['#FF6600','#FF2F00'],
        ['#00C726'],
      ],
    },
    iconArr: {
      type: Array,
      default: () => [
        require('@/assets/leader/img/component/middle/leader_middle_icon1.png'),
        require('@/assets/leader/img/component/middle/leader_middle_icon2.png'),
        require('@/assets/leader/img/component/middle/leader_middle_icon3.png'),
        require('@/assets/leader/img/component/middle/leader_middle_icon4.png'),
      ],
    },
    middleList: {
      type: Array,
      default: () => [
        [
          {
            name: '申办数量',
            unit: '',
            num: 3238,
          },
          {
            name: '同比',
            unit: '%',
            num: '5.1',
          },
        ],
        [
          {
            name: '受理量',
            unit: '',
            num: 2975,
          },
          {
            name: '同比',
            unit: '%',
            num: '3.7',
          },
        ],
        [
          {
            name: '办结量',
            unit: '',
            num: 2599,
          },
          {
            name: '同比',
            unit: '%',
            num: '-1.3',
          },
        ],
        [
          {
            name: '满意度',
            unit: '%',
            num: '97.4',
          },
        ],
      ],
    },
    bgArr: {
      type: Array,
      default: () => [
        require('@/assets/leader/img/component/middle/middle_item_bg1.png'),
        require('@/assets/leader/img/component/middle/middle_item_bg2.png'),
        require('@/assets/leader/img/component/middle/middle_item_bg3.png'),
        require('@/assets/leader/img/component/middle/middle_item_bg4.png'),
      ],
    },
  },
  data() {
    return {}
  },
}
</script>

<style lang="scss" scoped>
.middle_box {
  position: absolute;
  top: 116px;
  // left: 533px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
  .line {
    // width: 858px;
    display: flex;
    // border: 1px solid red;
    justify-content: space-between;
    .line_box {
      width: 207px;
      height: 99px;
      background-size: 100% 100%;
      margin: 0 5px;
      display: flex;
      justify-content: space-around;
      position: relative;
      .fgx {
        width: 1px;
        height: 39px;
        background: rgba(255,255,255,0.4);
        border-radius: 2px;
        position: absolute;
        top: 30px;
        left: 50%;
      }
      .item {
        width: 90px;
        height: 99px;
        padding-top: 18px;
        .num {
          font-size: 36px;
          font-family: DINCond-Black, DINCond;
          font-weight: 900;
          color: #00A0FF;
          line-height: 42px;
          text-shadow: 0px 2px 4px rgba(0,0,0,0.5);
          span {
            font-size: 16px;
          }
        }
        .name {
          font-size: 16px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #FFFFFF;
          line-height: 22px;
        }
      }
      // &:not(:last-of-type) {
      //   margin-right: 10px;
      // }
      // .num_ {
      //   font-size: 36px;
      //   font-family: DINCond-Black, DINCond;
      //   font-weight: 900;
      //   color: #00a0ff;
      //   text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
      //   line-height: 62px;
      // }
      // .info_ {
      //   display: flex;
      //   // padding: 0 0 0 28px;
      //   align-items: center;
      //   justify-content: center;
      //   & > div {
      //     display: flex;
      //     align-items: center;
      //     img {
      //       width: 22px;
      //       height: 22px;
      //     }
      //     p {
      //       font-size: 16px;
      //       font-family: PingFangSC-Regular, PingFang SC;
      //       font-weight: 400;
      //       color: #ffffff;
      //       margin-left: 14px;
      //       & span {
      //         font-size: 10px;
      //       }
      //     }
      //   }
      // }
    }
  }
}
</style>