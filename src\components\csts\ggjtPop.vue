<template>
  <div class="ai_waring">
    <div class="title">
      <span>公共交通更多</span>
    </div>
    <img @click="closeEmit" class="close_btn" src="@/assets/shzl/map/close_btn2.png" alt="" />
    <div class="content">
      <div class="left">
        <BlockBox
          title="机动车分类"
          class="box box1"
          :isListBtns="false"
          :blockHeight="379"
        >
          <div class="box1Content">
            <PieChart3D type="2" :data="data10_2.data" :options="data10_2.options" />
          </div>
        </BlockBox>
        <BlockBox
          title="拥堵路段排行"
          class="box box2"
          :isListBtns="false"
          :blockHeight="310"
        >
          <div class="leader_zt_contain" style="margin-top: 12px">
            <RankBarChartE02 :data="data" :options="options" />
          </div>
        </BlockBox>
      </div>
      <div class="middle">
        <BlockBox
          title="流量分析"
          class="box box3"
          :isListBtns="false"
          :blockHeight="379"
        >
          <GroupColChart :data="qyeqData" :options="qyeqOptions"></GroupColChart>
        </BlockBox>
        <BlockBox
          title="高发违法类型"
          class="box box4"
          :isListBtns="false"
          :blockHeight="310"
        >
          <div class="box4Content">
            <pieCharts :data="data1" />
          </div>
        </BlockBox>
      </div>
      <div class="right">
        <BlockBox
          title="交通服务基本设施"
          class="box box5"
          :isListBtns="false"
          :blockHeight="689"
        >
          <div class="contentTitle">共享单车</div>
          <div class="contentBox">
            <div class="contentItem">
              <div class="contentName">投放量（亿辆）</div>
              <div class="contentNum">50</div>
            </div>
            <div class="contentItem">
              <div class="contentName">公交站点（万个）</div>
              <div class="contentNum">50</div>
            </div>
          </div>
          <div class="contentTitle">公共服务</div>
          <div class="contentBox">
            <div class="contentItem">
              <div class="contentName">公交线路（条）</div>
              <div class="contentNum">50</div>
            </div>
            <div class="contentItem">
              <div class="contentName">服务区（个）</div>
              <div class="contentNum">50</div>
            </div>
          </div>
          <div class="contentTitle">高速服务</div>
          <div class="contentBox">
            <div class="contentItem">
              <div class="contentName">高速里程（KM）</div>
              <div class="contentNum">50</div>
            </div>
            <div class="contentItem">
              <div class="contentName">投放量（亿辆）</div>
              <div class="contentNum">50</div>
            </div>
          </div>
          <div class="contentTitle">停车服务</div>
          <div class="contentBox">
            <div class="contentItem">
              <div class="contentName">站点数量（个）</div>
              <div class="contentNum">50</div>
            </div>
            <div class="contentItem">
              <div class="contentName">停车位（万个）</div>
              <div class="contentNum">50</div>
            </div>
          </div>
        </BlockBox>
      </div>
    </div>
  </div>
</template>

<script>
import BlockBox from '@/components/leader/common/blockBox2.vue'
import pieCharts from '@/components/csts/pieChart-zwfw.vue'
export default {
  name:'ggjtPop',
  data() {
    return {
      data1: [
        ['product', '违法总数'],
        ['机动车超速', 1234],
        ['人数超载', 1234],
        ['货车超载', 1234],
        ['闯红灯', 1234],
        ['其他', 1234],
      ],
      data10_2: {
        data: [
          ['product', '事件总数'],
          ['汽车', 1245],
          ['客车', 1245],
          ['新能源汽车', 1245],
          ['卡车', 1245],
          ['摩托车', 1245],
        ],
        options: {
          colors: ['#2EF6FF', '#5AE29D', '#E6F973', '#E6F973', '#6D5AE2'],
          legend: {
            top: 15
          },
          bgImg: {
            width: '70%',
            height: '70%',
            top: '51%',
            left: '50%'
          },
          unit: '件',
          title: {
            fontSize: '16px',
            top: 70,
          },
          subtitle: {
            fontSize: '14px',
            top: 90,
          },
        }
      },
      data: [
        ['name', 'value'],
        ['鼓楼区', '15'],
        ['玄武区', '13'],
        ['秦淮区', '11'],
        ['建邺区', '9'],
        ['江宁区', '8'],
      ],
      options: {
        color: ['#F8753A', '#FFD84E', '#3AF8DF', '#00A5FF', '#00A5FF'],
        barWidth: 7.88,
        fontSize: 18,
        labelSize: 14,
        top: '-100%',
        width: '95%',
      },
      qyeqData: [
        ['product', '总流量', '轿车', '客车', '火车'],
        ['1月', 200, 160, 140, 120],
        ['2月', 140, 90, 180, 140],
        ['3月', 102, 105, 120, 150],
        ['4月', 200, 90, 130, 110],
        ['5月', 150, 200, 150, 170],
        ['6月', 150, 200, 150, 180]
      ],
      qyeqOptions: {
        color: ['#01FF6C', '#8300D7', '#FF9201'],
        stack: true,
        isGradient: true,
        barMaxWidth: 10,
        gradientColors: [
          ['#01FF6C', '#00ED8B'],
          ['#8300D7', '#7800ED'],
          ['#FF9201', '#EDB000']
        ],
        isHor: false,
        barGap: '30%',
        tooltip: {
          color: '#fff',
          show: true,
          type: 'shadow'
        }
      },
    }
  },
  components: {
    BlockBox,
    pieCharts,
  },
  methods: {
    closeEmit() {
      this.$emit('closeEmit')
    },
  },
}
</script>

<style lang='less' scoped>
@keyframes rotateS {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes rotateN {
  0% {
    transform: rotate(360deg);
  }
  100% {
    transform: rotate(0);
  }
}
@keyframes rotateY {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotateY(360deg);
  }
}
.ai_waring {
  width: 1885px;
  height: 937px;
  padding: 22px;
  // background: rgba(0,23,59,0.95);
  background: url(~@/assets/csts/xztk.png) no-repeat;
  background-size: 100% 100%;
  box-shadow: 0px 0px 22px 0px rgba(11,54,138,0.35), 0px 2px 8px 0px rgba(0,0,0,0.7), inset 0px 0px 8px 0px rgba(17,146,214,0.35);
  border-radius: 11px;
  border: 1px solid;
  border-image: linear-gradient(360deg, rgba(57, 133, 255, 0.5), rgba(0, 205, 255, 0)) 1 1;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
  .title {
    background: url(~@/assets/shzl/map/title_bg2.png) no-repeat center / 100% 100%;
    background-size: 100% 100%;
    width: 914px;
    height: 74px;
    margin: 0 auto;
    line-height: 74px;
    text-align: center;
    position: relative;
    span {
      font-size: 40px;
      font-family: YouSheBiaoTiHei;
      color: #FFFFFF;
      line-height: 52px;
      background: linear-gradient(180deg, #FFFFFF 0%, #2AEBFF 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      display: inline-block;
      margin-top: 11px;
    }
  }
  .close_btn {
    position: absolute;
    top: 38px;
    right: 43px;
  }
  .content {
    width: 1841px;
    height: 765px;
    .contentTitle {
      width: 502px;
      height: 56px;
      font-size: 20px;
      font-family: PangMenZhengDao;
      color: #FFFFFF;
      line-height: 70px;
      letter-spacing: 2px;
    }
    .left {
      width: 533px;
      float: left;
      margin-left: 67px;
      padding-top: 43px;
      .box1 {
        .box1Content {
          height: 249px;
        }
      }
      .box2 {
        .leader_zt_contain {
          height: 260px;
        }
      }
    }
    .middle {
      width: 533px;
      float: left;
      margin-left: 54px;
      padding-top: 43px;
      .box3 {
      }
      .box4 {
        .box4Content {
          height: 260px;
        }
      }
    }
    .right {
      width: 533px;
      float: left;
      margin-left: 70px;
      padding-top: 43px;
      .box5 {
        .contentBox {
          width: 502px;
          height: 104px;
          .contentItem {
            width: 251px;
            height: 104px;
            float: left;
            .contentName {
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
              text-shadow: 0px 2px 4px rgba(0,0,0,0.5);
              text-align: left;
              margin: 28px 0 0 116px;
            }
            .contentNum {
              font-size: 20px;
              font-family: DINAlternate-Bold, DINAlternate;
              font-weight: bold;
              color: #FFFFFF;
              line-height: 24px;
              text-shadow: 0px 2px 4px rgba(0,0,0,0.5);
              text-align: left;
              margin: 6px 0 0 134px;
            }
          }
          &:nth-of-type(2) {
            .contentItem {
              &:nth-of-type(1) {
                background: url(~@/assets/csts/jtfwjbss1.png) no-repeat;
                background-size: 100% 100%;
              }
              &:nth-of-type(2) {
                background: url(~@/assets/csts/jtfwjbss2.png) no-repeat;
                background-size: 100% 100%;
              }
            }
          }
          &:nth-of-type(4) {
            .contentItem {
              &:nth-of-type(1) {
                background: url(~@/assets/csts/jtfwjbss3.png) no-repeat;
                background-size: 100% 100%;
              }
              &:nth-of-type(2) {
                background: url(~@/assets/csts/jtfwjbss4.png) no-repeat;
                background-size: 100% 100%;
              }
            }
          }
          &:nth-of-type(6) {
            .contentItem {
              &:nth-of-type(1) {
                background: url(~@/assets/csts/jtfwjbss5.png) no-repeat;
                background-size: 100% 100%;
              }
              &:nth-of-type(2) {
                background: url(~@/assets/csts/jtfwjbss6.png) no-repeat;
                background-size: 100% 100%;
              }
            }
          }
          &:nth-of-type(8) {
            .contentItem {
              &:nth-of-type(1) {
                background: url(~@/assets/csts/jtfwjbss7.png) no-repeat;
                background-size: 100% 100%;
              }
              &:nth-of-type(2) {
                background: url(~@/assets/csts/jtfwjbss8.png) no-repeat;
                background-size: 100% 100%;
              }
            }
          }
        }
      }
      .box6 {
      }
    }
  }
}
</style>