<template>
  <div class="leader_box">
    <div class="leader_sjts">
      <div class="left">
        <div class="left1">
          <!-- <BlockBox
            title="种植业"
            subtitle="Planting"
            class="box box1"
            :isListBtns="false"
            :blockHeight="280"
          >
            <div class="cont">
              <pieChartTsgz :data="box1Data" />
            </div>
          </BlockBox>-->

          <!-- <BlockBox
            title="畜牧业"
            subtitle="Animal husbandry"
            class="box box2"
            :isListBtns="false"
            :blockHeight="360"
          >
            <div class="cont">
              <div class="sjzl_box">
                <div class="sjzl_box_top">
                  <ul>
                    <li v-for="(item, index) in box2TopData" :key="index">
                      <div class="line_">
                        <countTo
                          ref="countTo"
                          :startVal="$countTo.startVal"
                          :decimals="$countTo.decimals(item.num)"
                          :endVal="item.num"
                          :duration="$countTo.duration"
                        />
                        <span class="unit_">{{ item.unit }}</span>
                      </div>
                      <img src="@/assets/shzl/sjzl_top.png" alt />
                      <span>{{ item.tit }}</span>
                    </li>
                  </ul>
                </div>
                <div class="sjzl_box_bottom">
                  <LineColumnarChart
                    :data="box2BottomData"
                    :options="box2Options"
                    :init-option="box2InitOption"
                  ></LineColumnarChart>
                </div>
              </div>
            </div>
          </BlockBox>-->
          <!-- <BlockBox
            title="产值分析"
            subtitle="Output value analysis"
            class="box box3"
            :isListBtns="false"
            :blockHeight="220"
          >
            <div class="cont">
              <div class="left_">
                <ul>
                  <li v-for="(item, index) in box3LeftData" :key="index">
                    <p>{{ item.name }}</p>
                    <p>{{ item.value }}</p>
                  </li>
                </ul>
              </div>
              <div class="right_">
                <ul>
                  <li v-for="(item, index) in box3RightData" :key="index">
                    <img :src="item.icon" alt="" />
                    <div>
                      <p>{{ item.value }}<span>亿元</span></p>
                      <p>{{ item.name }}</p>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </BlockBox>-->

          <BlockBox
            title="产量统计"
            subtitle="Production statistics"
            class="box box1"
            :isListBtns="false"
            :blockHeight="200"
          >
            <div class="cont">
              <ul>
                <li v-for="(item, index) in box1Data" :key="index">
                  <img :src="item.url" alt />
                  <div>
                    <p>
                      {{ item.value }}
                      <span>{{ item.unit }}</span>
                    </p>
                    <p>{{ item.name }}</p>
                  </div>
                </li>
              </ul>
            </div>
          </BlockBox>
          <BlockBox
            title="产值统计"
            subtitle="Output value statistics"
            class="box box2"
            :isListBtns="false"
            :blockHeight="320"
          >
            <div class="cont">
              <pieChartTsgz :data="box2Data" :unit="'亿元'" />
            </div>
          </BlockBox>
          <BlockBox
            title="近5年产值分析"
            subtitle="Analysis of output value in the past 5 years"
            class="box box3"
            :isListBtns="false"
            :blockHeight="340"
          >
            <div class="cont">
              <lineBarEcharts :data="box3Data" />
            </div>
          </BlockBox>
        </div>
      </div>
      <div class="right">
        <div class="right1">
          <BlockBox
            title="现代产业农业园"
            subtitle="Modern Industrial Agricultural Park"
            class="box box4"
            :isListBtns="false"
            :blockHeight="600"
            :textArr="[]"
          >
            <div class="cont">
              <div class="pic_">
                <swiper class="swiper content" :options="swiperOption">
                  <swiper-slide v-for="(it, i) in box6TopData" class="width: 100%" :key="i">
                    <img :src="it.url" alt />
                    <!-- <div class="pic_title">
                      {{ it.originalFileName.slice(0, it.originalFileName.indexOf('.')) }}
                    </div>-->
                  </swiper-slide>
                  <div class="swiper-pagination" slot="pagination"></div>
                </swiper>
              </div>
              <div class="introduce">
                <p>{{ introWord }}</p>
              </div>
              <ul>
                <li v-for="(item, index) in box6BottomData" :key="index">
                  <img :src="item.url" alt />
                  <div>
                    <p>
                      {{ item.value }}
                      <span>{{ item.unit }}</span>
                    </p>
                    <p>{{ item.name }}</p>
                  </div>
                </li>
              </ul>
            </div>
          </BlockBox>
          <BlockBox
            title="魅力菊展"
            subtitle="Charming Chrysanthemum Exhibition"
            class="box box5"
            :showMore="true"
            :isListBtns="false"
            :blockHeight="280"
            :textArr="[]"
            @handleShowMore="showJhzVideo"
          >
            <div class="cont">
              <!-- <TrendLineChart :data="box4Data" :options="box4Options" :initOption="box4InitOption" /> -->
              <hlsVideo style="height: 100%;width: 100%;" :src="testUrl1"></hlsVideo>
              <!-- <LivePlayer
                style="height: 100%;width: 100%;"
                :videoUrl="testUrl1"
                fluent
                autoplay
                live
                stretch
              ></LivePlayer>-->
            </div>
          </BlockBox>
          <!-- <BlockBox
            title="水情监测"
            subtitle="Water monitoring"
            class="box box5"
            :isListBtns="false"
            :blockHeight="320"
          >
            <div class="cont">
              <swiper-table
                :data="box5Data"
                :titles="['监测站', '实时水位', '警戒水位', '保障水位', '超警戒', '超保证']"
                :widths="['25%', '15%', '15%', '15%', '15%', '15%']"
                content-height="260px"
              />
            </div>
          </BlockBox>
          <BlockBox
            title="视频监控"
            subtitle="Video surveillance"
            class="box box6"
            :showMore="true"
            :isListBtns="false"
            :blockHeight="296"
            @handleShowMore="showMoreFn(6)"
          >
            <div class="cont">
              <VideoSurveillance :cameraId="cameraId1" v-if="cameraId1" class="video_" />
              <VideoSurveillance :cameraId="cameraId2" v-if="cameraId2" class="video_" />
              <VideoSurveillance :cameraId="cameraId3" v-if="cameraId3" class="video_" />
              <VideoSurveillance :cameraId="cameraId4" v-if="cameraId4" class="video_" />
            </div>
          </BlockBox>-->
        </div>
      </div>
    </div>
    <!-- 侧边菜单 -->
    <ZhddAside @marker="marker" :list="leftMenuList" />
    <!-- 地图 -->
    <div class="map_box">
      <LeafletMapNy ref="leafletMap" @poiClick="showDialog" />
    </div>
    <!-- 视频监控 -->
    <spjkPop
      v-if="spjkShow"
      @closeEmit="spjkShow = false"
      @operate="handleSsgjOperate"
      :leftTreeData="spjkLeftTree"
    ></spjkPop>
    <!-- 地图弹窗 -->
    <infoPop :infoData="tscyyData" v-if="isInfoPopShow" @close="isInfoPopShow = false" />
    <!-- 基础信息 -->
    <BasicInformation
      v-model="basicInfomationShow"
      :title="basicInfomationTitle"
      :list="basicInfomationList"
      :btnShow="basicInfomationBtnShow"
      :btns="basicInfomationBtns"
    />
    <jhzPop v-if="jhzShow" @closeEmitai="jhzShow = false" />
  </div>
</template>

<script>
import BlockBox from '@/components/leader/common/blockBox.vue'
import HeaderMain from '@/components/leader/common/headerMains'
import SwiperTable from '@/components/djyl/SwiperTable.vue'
// 各模块
import ZhddAside from '@/components/djyl/Aside.vue'
import LeafletMapNy from '@/components/map/LeafletMapNy.vue'
import 'swiper/css/swiper.css'
import pieChartTsgz from '@/components/tsgz/pieChartTsgz.vue'
import infoPop from '@/components/zhny/infoPop.vue'
import BasicInformation from '@/views/leader/components/zhdd/dialog/BasicInformation.vue'
import VideoSurveillance from '@/views/leader/components/zhdd/VideoSurveillance.vue'
import lineBarEcharts from '@/components/zhny/lineBarEcharts.vue'
// 接口
import { getCameraMakers, getCameraXq } from '@/api/hs/hs.api.js'
import {
  getTscyyInfo,
  getFarmInfo,
  getRainfall,
  getLtqyInfo,
  getNyInfo,
  getCzfx,
  getJhzVideo
} from '@/api/hs/hs.zhny.js'
import hlsVideo from '@/components/leader/hlsVideo'
import LivePlayer from '@liveqing/liveplayer'
import jhzPop from '@/views/leader/components/hszl/jhzPop.vue'

export default {
  name: 'zhny',
  components: {
    BlockBox,
    HeaderMain,
    SwiperTable,
    ZhddAside,
    LeafletMapNy,
    pieChartTsgz,
    infoPop,
    BasicInformation,
    VideoSurveillance,
    lineBarEcharts,
    hlsVideo,
    LivePlayer,
    jhzPop
  },
  data () {
    return {
      testUrl1: '',
      // box1Data: [
      //   ['product', '人口信息'],
      //   ['农作物', 1025],
      //   ['蔬菜', 1001],
      //   ['水果', 1391],
      //   ['药材', 2694],
      //   ['花卉', 2123],
      //   ['其他', 1757],
      // ],
      box1Data: [
        { url: require('@/assets/zhny/map/cltj1.png'), name: '粮食', value: '59575', unit: '吨' },
        { url: require('@/assets/zhny/map/cltj2.png'), name: '蔬菜', value: '235347', unit: '吨' },
        { url: require('@/assets/zhny/map/cltj3.png'), name: '水产品', value: '8936', unit: '吨' },
      ],
      box2Data: [
        ['product', ''],
        ['农业', 14.37],
        ['林业', 0.58],
        ['牧业', 0.54],
        ['渔业', 6],
        ['农林牧渔服务业', 1.61],
      ],
      box3Data: [
        ['product', '增速', '生产总值'],
        ['2018', 10.06, 66.6],
        ['2019', 10.06, 73.3],
        ['2020', 7.91, 79.1],
        ['2021', 13.91, 90.1],
        ['2022', 4.66, 85.97],
      ],
      box2TopData: [
        {
          num: 18.43,
          tit: '常年存栏量',
          unit: '亿元',
        },
        {
          num: 36.13,
          tit: '当前存栏量',
          unit: '亿元',
        },
        {
          num: 51.01,
          tit: '常年存栏生猪当量',
          unit: '%',
        },
      ],
      cameraId1: '',
      cameraId2: '',
      cameraId3: '',
      cameraId4: '',
      spjkShow: false,
      box2BottomData: [
        ['product', '数量'],
        ['存栏量', 8],
        ['出栏量', 6],
        ['死淘量', 10],
        ['补栏量', 12],
      ],
      box2Options: {
        gradientColors: [['#00A3D7', '#99E4FA']],
        yAxis: {
          unit: '',
        },
        grid: {
          top: 10,
          left: 2,
          right: 2,
          bottom: '28%',
        },
        isToolTipInterval: true,
      },
      box4Data: [
        ['product', '事件'],
        ['00.00', 77],
        ['04.00', 97],
        ['08.00', 95],
        ['12.00', 81],
        ['16.00', 15],
        ['20.00', 6],
        ['24.00', 28],
        // ['00.00', 1],
        // ['00.00', 54],
        // ['00.00', 12],
        // ['00.00', 26],
        // ['00.00', 50],
      ],
      box4Options: {
        legend: {
          show: false,
        },
      },

      box5Data: [
        ['苏州水库', 45, 60, 100, -15, -55],
        ['句容河', 62, 60, 100, 2, -17],
        ['漂水河', 45, 60, 100, -15, -55],
        ['解溪河', 45, 60, 100, -15, -55],
        ['梁台河', 45, 60, 100, -15, -55],
        ['同进河', 45, 60, 100, -15, -55],
      ],
      box6TopData: [],
      box6BottomData: [
        { name: '总面积', unit: '亩', value: '', url: require('@/assets/zhny/map/nyy1.png') },
        { name: '稻米产业', unit: '亩', value: '', url: require('@/assets/zhny/map/nyy2.png') },
        { name: '水产产业', unit: '亩', value: '', url: require('@/assets/zhny/map/nyy3.png') },
        { name: '园艺产业', unit: '亩', value: '', url: require('@/assets/zhny/map/nyy4.png') },
        { name: '育秧中心', unit: 'm²', value: '', url: require('@/assets/zhny/map/nyy5.png') },
        // { name: '高标准农田', unit: '亩', value: '', url: require('@/assets/zhny/map/nyy6.png') },
      ],
      box6BottomUrl: [
        require('@/assets/zhny/map/nyy1.png'),
        require('@/assets/zhny/map/nyy2.png'),
        require('@/assets/zhny/map/nyy3.png'),
        require('@/assets/zhny/map/nyy4.png'),
        require('@/assets/zhny/map/nyy5.png'),
        require('@/assets/zhny/map/nyy6.png'),
      ],
      introWord: '',
      leftMenuList: [
        {
          bg: require('@/assets/aside/bg1.png'),
          bgActive: require('@/assets/aside/bg2.png'),
          icon: require('@/assets/zhny/map/icon1.png'),
          iconActive: require('@/assets/zhny/map/icon1_active.png'),
          label: '特色产业园',
          active: false,
        },
        {
          bg: require('@/assets/aside/bg1.png'),
          bgActive: require('@/assets/aside/bg2.png'),
          icon: require('@/assets/zhny/map/icon2.png'),
          iconActive: require('@/assets/zhny/map/icon2_active.png'),
          label: '家庭农场',
          active: false,
        },
        {
          bg: require('@/assets/aside/bg1.png'),
          bgActive: require('@/assets/aside/bg2.png'),
          icon: require('@/assets/zhny/map/icon3.png'),
          iconActive: require('@/assets/zhny/map/icon3_active.png'),
          label: '龙头企业',
          active: false,
        },
        {
          bg: require('@/assets/aside/bg1.png'),
          bgActive: require('@/assets/aside/bg2.png'),
          icon: require('@/assets/zhny/map/icon4.png'),
          iconActive: require('@/assets/zhny/map/icon4_active.png'),
          label: '农业合作社',
          active: false,
        },
      ],
      isInfoPopShow: false,
      box3LeftData: [
        { name: '当前产值 (亿元)', value: '18.43' },
        { name: '计划产值 (亿元)', value: '36.13' },
        { name: '完成率(%)', value: '51.01' },
      ],
      box3RightData: [
        { name: '种植业', value: '6.95', icon: require('@/assets/zhny/map/czfx1.png') },
        { name: '渔业', value: '2.43', icon: require('@/assets/zhny/map/czfx1.png') },
        { name: '畜牧业', value: '3.62', icon: require('@/assets/zhny/map/czfx1.png') },
        { name: '林业', value: '1.08', icon: require('@/assets/zhny/map/czfx1.png') },
      ],
      spjkLeftTree: [],
      tscyyData: {
        name: '湖熟现代农业示范区',
        type: '农业服务',
        peopelName: '李晓飞',
        phone: '13890234897',
        address: '南京市江宁区湖熟街道湖熟现代农业示范园',
        synopsis:
          '湖熟现代农业示范区位于南京市主城区东南部，自古就是“鱼米之乡”，地势平坦、水网密集、光照充足、兩量充沛，自然环境优越，适宜各种优质果蔬生长，素有“小南京"的美誉。湖熟街道距南京市主城区20公里，距南京禄口国际机场13公里，距新生圩港37公里，宁杭高速、省道337穿境而过。即将开通的龙眠大道南延线全长6公里，极大地拉近了湖熟与主城区的距离，10分钟内即可到达地铁一号线南延线的“中国药科大学”站，“五分钟上高速、十五分钟到机场、二十分钟到市中心”已成为湖熟的对外交通新格局。湖熟菊花展分为露天菊花和温室大棚展览，露天的菊花品种已经开放了60%-70%，温室种植的菊花则开放了30%-40%。菊花品种,在湖熟菊花园，400亩菊花形成壮观花海，争奇斗艳。园内共有3300多个品种的菊花，不仅有各种传统秋菊，更有绿色大药、草莓药等少见品种，双色、绿色、间色等市场稀有品种菊花，在这里都可以找到。',
      },
      basicInfomationShow: false,
      basicInfomationTitle: '',
      basicInfomationList: [],
      basicInfomationBtnShow: false,
      basicInfomationBtns: [],
      swiperOption: {
        observer: true,
        observeParents: true,
        autoHeight: true,
        spaceBetween: 0,
        autoplay: {
          delay: 3500,
          disableOnInteraction: false,
        },
        slidesPerView: 'auto',
        grabCursor: true,
        autoplayDisableOnInteraction: false,
        mousewheelControl: true,
        pagination: {
          el: '.swiper-pagination',
        },
        tscyyDataRight: [],
      },
      jhzShow: false
    }
  },
  created () { },
  mounted () {
    this.cameraMarker()
    this.getRainfallFn()
    this.getCzfxFn()
    this.getTsCyyFn()
    this.queryExhibition()

    // 获取滚动容器和文本元素
    const scrollingText = document.querySelector('.introduce')
    const text = document.querySelector('.introduce p')
    // 获取文本元素的高度
    const textHeight = text.offsetHeight

    // 定义滚动函数
    function scroll () {
      // console.log('scrollingText.scrollTop',scrollingText.scrollTop);
      // 如果文本元素已经完全滚动出去，则将其重置到滚动容器的顶部
      if (scrollingText.scrollTop >= 96) {
        scrollingText.scrollTop = 0
      }
      // 否则，将滚动容器向上滚动一个像素的距离
      else {
        scrollingText.scrollTop += 1
      }
    }

    // 每隔20毫秒调用一次滚动函数
    this.timer = window.setInterval(scroll, 100)
  },
  watch: {},
  computed: {
    formatNumber () {
      return function (param) {
        console.log(param)
        if (this.isEmpty(param) || param.length < 4 || isNaN(param)) {
          return param
        }

        let small = ''
        let isSmall = false
        if (param.split('.').length === 2) {
          // 有小数
          isSmall = true
          small = param.split('.')[1]
          param = param.split('.')[0]
        }

        let result = ''

        while (param.length > 3) {
          result = ',' + param.slice(-3) + result
          param = param.slice(0, param.length - 3)
        }

        if (param) {
          result = param + result
        }

        if (isSmall) {
          result = result + '.' + small
        }
        console.log(result)
        return result
      }
    },
    box2InitOption () {
      return {
        // xAxis: {
        //   axisLabel: {
        //     formatter: function (value) {
        //       // 将标签中的每两个字符之间插入一个换行符
        //       var lines = value.replace(/(.{2})/g, '$1\n')
        //       return lines
        //     },
        //   },
        // },
        yAxis: {
          name: '',
        },
        tooltip: {
          formatter: (params) => {
            var relVal = params[0].name
            for (var i = 0, l = params.length; i < 1; i++) {
              relVal += '<br/>' + params[i].marker + params[i].value + ' 件'
            }
            return relVal
          },
        },
      }
    },
    box4InitOption () {
      return {
        // xAxis: {
        //   axisLabel: {
        //     formatter: function (value) {
        //       // 将标签中的每两个字符之间插入一个换行符
        //       var lines = value.replace(/(.{2})/g, '$1\n')
        //       return lines
        //     },
        //   },
        // },
        yAxis: {
          name: '降雨量/mn',
        },
        tooltip: {
          formatter: (params) => {
            var relVal = params[0].name
            for (var i = 0, l = params.length; i < 1; i++) {
              relVal += '<br/>' + params[i].marker + params[i].value + ' mn'
            }
            return relVal
          },
        },
        dataZoom: [
          {
            type: 'inside', // 内置型式的 dataZoom 组件
            xAxisIndex: 0, // 对应 x 轴的索引，默认为 0
            start: 0, // 起始位置的百分比
            end: 40, // 结束位置的百分比
            // realtime: true // 启用实时滚动
          },
        ],
      }
    },
  },
  methods: {
    marker (val, i) {
      if (i + 1 == 1 && val) {
        this.mapMarker1()
      } else if (i + 1 == 1 && !val) {
        this.cleaerMapMarker('tscyy')
      }
      if (i + 1 == 2 && val) {
        this.mapMarker2()
      } else if (i + 1 == 2 && !val) {
        this.cleaerMapMarker('jtnc')
      }
      if (i + 1 == 3 && val) {
        this.mapMarker3()
      } else if (i + 1 == 3 && !val) {
        this.cleaerMapMarker('ltqy')
      }
      if (i + 1 == 4 && val) {
        this.mapMarker4()
      } else if (i + 1 == 4 && !val) {
        this.cleaerMapMarker('nmhzs')
      }
    },
    mapMarker (data, layerId) {
      this.$refs.leafletMap.drawPoiMarker(data, layerId, true, false, false)
    },
    async getTsCyyFn () {
      let res = await getTscyyInfo()
      if (res?.code == '200') {
        this.tscyyDataRight = res.result
        if (res.result.length > 0) {
          this.introWord = res.result[0].sysnopsis
          this.box6BottomData[0].value = res.result[0].area
          this.box6BottomData[1].value = res.result[0].area1
          this.box6BottomData[2].value = res.result[0].area2
          this.box6BottomData[3].value = res.result[0].area3
          this.box6BottomData[4].value = res.result[0].area4
          // this.box6BottomData[5].value = res.result[0].area5
          if (res.result[0].tbAppendixList.length > 0) {
            this.box6TopData = res.result[0].tbAppendixList || []
          }
        }
      }
    },
    async mapMarker1 () {
      let data = this.setMapArrayofPoint(
        this.tscyyDataRight,
        require('@/assets/zhny/map/map_icon1_new.png'),
        'tscyy'
      )
      this.mapMarker(data, 'tscyy')
    },
    async mapMarker2 () {
      let res = await getFarmInfo()
      if (res?.code == '200') {
        let data = this.setMapArrayofPoint(
          res.result,
          require('@/assets/zhny/map/map_icon2_new.png'),
          'jtnc'
        )
        this.mapMarker(data, 'jtnc')
      }
    },
    async mapMarker3 () {
      let res = await getLtqyInfo()
      if (res?.code == '200') {
        let data = this.setMapArrayofPoint(
          res.result,
          require('@/assets/zhny/map/map_icon3_new.png'),
          'ltqy'
        )
        this.mapMarker(data, 'ltqy')
      }
    },
    async mapMarker4 () {
      let res = await getNyInfo()
      if (res?.code == '200') {
        let data = this.setMapArrayofPoint(
          res.result,
          require('@/assets/zhny/map/map_icon4_new.png'),
          'nmhzs'
        )
        this.mapMarker(data, 'nmhzs')
      }
    },
    showJhzVideo () {

      console.log('jhzShow')
      this.jhzShow = true
    },
    async queryExhibition () {
      let res = await getJhzVideo()
      this.testUrl1 = res.data.cameraDataVLO[0].url
      console.log(this.testUrl1, '视频')
    },
    cleaerMapMarker (type) {
      // 清除标记点
      this.$refs.leafletMap.removeLayer(type)
    },
    // 地图弹窗
    showDialog (layerId, it) {
      const id = it.props.id
      const info = it.info
      console.log(layerId, id, it)

      if (layerId == 'jtnc') {
        this.basicInfomationList = [
          {
            label: '农场名称：',
            value: info.name,
          },
          // {
          //   label: '农场级别：',      
          //   value: info.level,
          // },
          {
            label: '产业类型：',
            value: info.type,
          },
          {
            label: '农场地址：',
            value: info.address,
          },
          {
            label: '农场介绍：',
            value: info.synopsis,
          },

          // {
          //   label: '联系人：',
          //   value: info.owner,
          // },
          // {
          //   label: '联系方式：',
          //   value: info.phone,
          // },
        ]
        this.basicInfomationBtnShow = false
        this.basicInfomationShow = true
        this.basicInfomationTitle = '家庭农场'
      } else if (layerId == 'nmhzs') {
        this.basicInfomationList = [
          {
            label: '农业合作社名称：',
            value: info.name,
          },
          {
            label: '法人：',
            value: info.legalPerson,
          },
          {
            label: '经营范围',
            value: info.business,
          },
          {
            label: '地址：',
            value: info.address,
          },
        ]
        this.basicInfomationBtnShow = false
        this.basicInfomationShow = true
        this.basicInfomationTitle = '农业合作社'
      } else if (layerId == 'ltqy') {
        this.basicInfomationList = [
          {
            label: '企业名称：',
            value: info.name,
          },
          {
            label: '级别：',
            value: info.level,
          },
          {
            label: '联系人',
            value: info.linkPerson,
          },
          {
            label: '联系方式：',
            value: info.legalPhone,
          },
          {
            label: '主营业务：',
            value: info.business,
          },
        ]
        this.basicInfomationBtnShow = false
        this.basicInfomationShow = true
        this.basicInfomationTitle = '龙头企业'
      } else {
        this.tscyyData = info
        console.log('this.tscyyData', this.tscyyData)
        this.isInfoPopShow = true
      }
    },
    async cameraXq (id) {
      // console.log('id', id)
      const res = await getCameraXq(id)
      // console.log('res', res.body.data[0].url)
      if (res && res.body && res.body.data && res.body.data.length > 0 && res.body.data[0].url) {
        this.hkTestUrl = res.body.data[0].url
        this.cameraCode = id //传给hls组件，当视频播放失败时再次调用
        this.hkVideShow = true
      }
    },
    // 显示更多
    showMoreFn (i) {
      if (i === 6) {
        this.spjkShow = true
        if (this.spjkLeftTree.length == 0) {
          this.cameraMore()
        }
      }
    },
    handleSsgjOperate () { },
    async cameraMarker () {
      const res = await getCameraMakers()
      console.log('getCameraMakers', res)
      if (res && res.length > 0) {
        // console.log(2222)
        // console.log(res[0])
        // let filerRes = res.filter((item) => item.id)
        // console.log('filerRes',filerRes);
        // console.log('this.cameraId1',this.cameraId1);
        this.cameraId1 = res[5].id
        this.cameraId2 = res[6].id
        this.cameraId3 = res[7].id
        this.cameraId4 = res[8].id

        this.spjkLeftTree = []
        this.spjkLeftTree.unshift({ id: 'xlgcId', name: '雪亮工程', children: [] })
        this.spjkLeftTree[0].children = res
      }
    },
    setMapArrayofPoint (points, iconUrl, layerId) {
      const data = points.map((it, i) => ({
        latlng: [it.latitude || 0, it.longitude || 0],
        icon: {
          iconUrl: iconUrl,
          iconSize: [32, 42],
          iconAnchor: [16, 42],
          html: '123',
        },
        props: {
          id: it.id,
          name: it.name || '-',
          type: layerId,
        },
        info: it,
      }))
      return data
    },
    async cameraMore () {
      const res = await getCameraMakers()
      console.log('getCameraMakers', res)
      if (res && res.length > 0) {
        this.spjkLeftTree = []
        this.spjkLeftTree.unshift({ id: 'xlgcId', name: '雪亮工程', children: [] })
        this.spjkLeftTree[0].children = res
      }
    },
    async getRainfallFn () {
      let res = await getRainfall()
      if (res?.code == '200') {
        this.box4Data = []
        this.box4Data = [['product', ''], ...res.result.map((it) => [it.label, Number(it.value)])]
      }
    },
    async getCzfxFn () {
      let res = await getCzfx()
      if (res?.code == '200') {
        this.box3Data = []
        this.box3Data = [
          ['product', '增速', '生产总值'],
          ...res.result.map((it) => [Number(it.value1), Number(it.value3), Number(it.value2)]),
        ]
      }
    },
  },
}
</script>

<style lang="less" scoped>
@font-face {
  font-family: QuartzRegular;
  src: url(~@/assets/leader/font/QuartzRegular.ttf);
}
@keyframes rotateS {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes rotateN {
  0% {
    transform: rotate(360deg);
  }
  100% {
    transform: rotate(0);
  }
}
@keyframes rotateY {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotateY(360deg);
  }
}

.leader_sjts {
  width: 1920px;
  height: 1080px;
  position: absolute;
  top: 0;
  display: flex;
  justify-content: space-between;
  .leader_city_box {
    width: 450px;
    .leader_zt_contain {
      height: 100%;
    }
  }
  .box {
    .cont {
      width: 100%;
      height: 100%;
    }

    .ybsscont {
      height: 100%;
      padding: 14px 44px 24px 37px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: space-between;
      li {
        position: relative;
        width: 165px;
        height: 66px;
        background: url(~@/assets/shzl/ybss_bg.png) no-repeat;
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: left;
        padding-left: 92px;
        .icon {
          position: absolute;
          width: 26px;
          height: 26px;
          top: 10px;
          left: 31px;
          animation: move infinite 3s ease-in-out;
          @keyframes move {
            0% {
              transform: translateY(0px) rotateY(0);
            }
            50% {
              transform: translateY(-10px) rotateY(180deg);
            }
            100% {
              transform: translateY(0px) rotateY(360deg);
            }
          }
        }
        &:nth-child(2) {
          span {
            animation-delay: 0.3s;
          }
        }
        &:nth-child(3) {
          span {
            animation-delay: 0.6s;
          }
        }
        &:nth-child(4) {
          span {
            animation-delay: 0.9s;
          }
        }
        &:nth-child(5) {
          span {
            animation-delay: 1.2s;
          }
        }
        &:nth-child(6) {
          span {
            animation-delay: 1.5s;
          }
        }
        .tit {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 20px;
          white-space: nowrap;
        }
        .num {
          font-size: 18px;
          font-family: PingFangSC, PingFang SC;
          font-weight: normal;
          color: #ffffff;
          line-height: 22px;
        }
      }
    }
    .wrapper4 {
      width: 100%;
      height: 100%;
      padding: 20px 0;
      ul {
        width: 100%;
        height: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;
        li {
          display: flex;
          height: 49px;
          gap: 10px;
          .icon {
            width: 46px;
            height: 49px;
          }
          .info {
            margin-top: -6px;
            .label {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
              text-align: left;
              padding-left: 5px;
            }
            .line {
              width: 87px;
              height: 6px;
              background: url('~@/assets/hszl/line1.png') no-repeat;
            }
            .count {
              text-align: left;
              margin-top: 4px;
              width: 87px;
              height: 22px;
              line-height: 22px;
              background: linear-gradient(96deg, #1d4975 0%, rgba(19, 56, 87, 0.24) 100%);
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #a8b1b9;
              padding-left: 5px;
              span {
                font-size: 18px;
                font-family: PingFangSC, PingFang SC;
                font-weight: normal;
                color: #ffffff;
                line-height: 22px;
                margin-right: 3px;
              }
            }
          }
        }
      }
    }
  }
  .box1 {
    .cont {
      padding: 40px 10px 0 10px;
      ul {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        li {
          display: flex;
          &:nth-of-type(1) {
            margin-bottom: 56px;
          }
          img {
            width: 54px;
            height: 54px;
            margin-right: 10px;
          }
          & p:first-of-type {
            font-size: 22px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 26px;
            span {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
            }
          }
          & p:last-of-type {
            text-align: left;
            margin-top: 8px;
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 20px;
          }
        }
      }
    }
    // .cont {
    //   padding: 13px 0 23px;
    //   display: flex;
    //   flex-wrap: wrap;
    //   justify-content: space-between;
    //   align-content: space-between;
    //   li {
    //     position: relative;
    //     width: 222px;
    //     height: 61px;
    //     padding: 10px 7px;
    //     display: flex;
    //     background: url(~@/assets/hszl/bg2.png) no-repeat;
    //     .icon {
    //       width: 41px;
    //       height: 41px;
    //       background: url(~@/assets/hszl/bg3.png) no-repeat;
    //       font-size: 17px;
    //       img {
    //         animation: move infinite 3s ease-in-out;
    //         @keyframes move {
    //           0% {
    //             transform: rotateY(0);
    //           }
    //           50% {
    //             transform: rotateY(180deg);
    //           }
    //           100% {
    //             transform: rotateY(360deg);
    //           }
    //         }
    //       }
    //       .name {
    //         font-size: 10px;
    //         font-family: PingFangSC, PingFang SC;
    //         font-weight: normal;
    //         color: #ffffff;
    //         line-height: 11px;
    //         text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
    //       }
    //     }
    //     .line {
    //       position: absolute;
    //       left: 50px;
    //       top: 5px;
    //       width: 1px;
    //       height: 55px;
    //       border: 1px solid;
    //       border-image: linear-gradient(
    //           180deg,
    //           rgba(0, 83, 171, 0),
    //           rgba(0, 140, 213, 0.6),
    //           rgba(0, 83, 171, 0)
    //         )
    //         1 1;
    //     }
    //     .desc {
    //       position: relative;
    //       margin-left: 15px;
    //       text-align: left;
    //       padding-left: 18px;
    //       .xsj {
    //         position: absolute;
    //         width: 10px;
    //         height: 10px;
    //         left: 0;
    //         top: 7px;
    //         background: url(~@/assets/csts/xsj1.png) no-repeat;
    //       }
    //       .num {
    //         font-size: 20px;
    //         font-family: PingFangSC, PingFang SC;
    //         font-weight: normal;
    //         color: #ffffff;
    //         line-height: 24px;
    //       }
    //       .tit {
    //         font-size: 14px;
    //         font-family: PingFangSC, PingFang SC;
    //         font-weight: 400;
    //         color: #ffffff;
    //         line-height: 20px;
    //         white-space: nowrap;
    //       }
    //     }
    //   }
    // }
  }
  .box2 {
    .cont {
      .sjzl_box {
        padding: 20px 22px 23px;
        .sjzl_box_top {
          margin-bottom: 23px;
          ul {
            display: flex;
            justify-content: space-between;
            li {
              display: flex;
              flex-direction: column;
              &:first-of-type {
                & span:first-of-type {
                  font-size: 20px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: normal;
                  color: #ffffff;
                  line-height: 24px;
                  // text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.5);
                  background: linear-gradient(180deg, #ffffff 0%, #00fffa 67%, #04a1ff 100%);
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                }
                & span:last-of-type {
                  font-size: 14px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: 400;
                  // color: #ffffff;
                  line-height: 20px;
                  background: linear-gradient(180deg, #ffffff 0%, #00fffa 67%, #04a1ff 100%);
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                }
              }
              &:nth-of-type(2) {
                & span:first-of-type {
                  font-size: 20px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: normal;
                  color: #ffffff;
                  line-height: 24px;
                  // text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.5);
                  background: linear-gradient(180deg, #ffffff 0%, #ffd500 67%, #ffb904 100%);
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                }
                & span:last-of-type {
                  font-size: 14px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: 400;
                  // color: #ffffff;
                  line-height: 20px;
                  background: linear-gradient(180deg, #ffffff 0%, #ffd500 67%, #ffb904 100%);
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                }
              }
              &:nth-of-type(3) {
                & span:first-of-type {
                  font-size: 20px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: normal;
                  color: #ffffff;
                  line-height: 24px;
                  // text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.5);
                  background: linear-gradient(180deg, #ffffff 0%, #86ff00 67%, #1cff04 100%);
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                }
                & span:last-of-type {
                  font-size: 14px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: 400;
                  // color: #ffffff;
                  line-height: 20px;
                  background: linear-gradient(180deg, #ffffff 0%, #86ff00 67%, #1cff04 100%);
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                }
              }
            }
          }
        }
        .sjzl_box_bottom {
          width: 406px;
          height: 220px;
        }
      }
    }
  }
  .box3 {
    .cont {
      display: flex;
      padding: 18px 4px 0;
      .left_ {
        ul {
          li {
            width: 134px;
            height: 47px;
            background: url(~@/assets/zhny/map/czfx_left.png) no-repeat center / 100% 100%;
            margin-bottom: 13px;
            & p:first-of-type {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: normal;
              color: #ffffff;
              line-height: 26px;
            }
            & p:last-of-type {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
            }
          }
        }
      }
      .right_ {
        margin-left: 25px;
        ul {
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;
          li {
            display: flex;
            &:nth-of-type(1) {
              margin-bottom: 56px;
            }
            img {
              width: 54px;
              height: 54px;
            }
            & p:first-of-type {
              font-size: 22px;
              font-family: PingFangSC, PingFang SC;
              font-weight: normal;
              color: #ffffff;
              line-height: 26px;
              span {
                font-size: 14px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                color: #ffffff;
                line-height: 20px;
              }
            }
            & p:last-of-type {
              margin-top: 8px;
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
            }
          }
        }
      }
    }
  }
  .box4 {
    // .cont_img {
    //   // border: 1px solid red;
    //   padding: 10px 0 10px 0;
    //   .img_box {
    //     display: flex;
    //     justify-content: space-between;
    //     padding: 0 40px;
    //     .img_ {
    //       width: 185px;
    //       height: 140px;
    //       flex-shrink: 0;
    //     }
    //     // img {
    //     //   width: 100%;
    //     //   height: 80%;
    //     // }
    //     p {
    //       color: #fff;
    //       position: relative;
    //       z-index: 99;
    //       font-size: 14px;
    //       overflow: hidden;
    //       text-overflow: ellipsis;
    //       white-space: nowrap;
    //       top: 4px;
    //     }
    //   }
    // }
    .cont {
      .pic_ {
        position: relative;
        .pic_title {
          position: absolute;
          left: 50px;
          bottom: 0;
          color: #fff;
          font-size: 16px;
        }
      }
      img {
        width: 360px;
        height: 200px;
      }
      .introduce {
        margin-top: 20px;
        width: 100%;
        height: 136px;
        padding: 18px 26px;
        background: url(~@/assets/hszl/bg1.png) no-repeat;
        background-size: 100% 100%;
        font-size: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #4fddff;
        line-height: 22px;
        letter-spacing: 2px;
        text-align: left;
        overflow: hidden;
        position: relative;
        p {
          position: absolute;
          top: 0;
          left: 0;
          margin: 0;
          padding: 10px;
          font-size: 16px;
        }
      }
      ul {
        margin-top: 20px;
        padding: 20px 10px;
        display: flex;
        flex-wrap: wrap;
        // justify-content: space-between;
        li {
          width: 33.33%;
          display: flex;
          &:nth-of-type(1) {
            margin-bottom: 26px;
          }
          img {
            width: 54px;
            height: 54px;
            margin-right: 10px;
          }
          & p:first-of-type {
            font-size: 22px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 26px;
            span {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
            }
          }
          & p:last-of-type {
            text-align: left;
            margin-top: 8px;
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 20px;
          }
        }
      }
    }
  }

  .box5 {
    .cont {
      padding: 20px 2px 6px;
      .video_ {
        width: 316px;
        height: 169px;
      }
    }
  }
  .box6 {
    .cont {
      padding: 10px 10px 0 0px;
      overflow: hidden;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .video_ {
        width: 220px;
        height: 120px;
        flex-shrink: 0;
      }
    }
  }
  .left_bg {
    width: 460px;
    height: 1033px;
    background: url(~@/assets/leader/img/component/left_bg.png) no-repeat center / 100% 100%;
    position: absolute;
    left: 50px;
    top: 25px;
    z-index: 102;
  }
  .right_bg {
    width: 520px;
    height: 1033px;
    background: url(~@/assets/leader/img/component/right_bg.png) no-repeat center / 100% 100%;
    position: absolute;
    right: 50px;
    top: 25px;
    z-index: 102;
  }
  .left {
    width: 450px;
    display: flex;
    justify-content: space-between;
    margin-top: 123px;
    margin-left: 50px;
    position: relative;
    z-index: 1003;
  }
  .right {
    width: 450px;
    margin-right: 60px;
    display: flex;
    justify-content: space-between;
    margin-top: 136px;
    position: relative;
    z-index: 1003;
  }
  .map_box {
    position: absolute;
    width: 1920px;
    height: 970px;
    top: 100px;
    left: 0;
  }
}
.jgzzBox {
  width: 109px;
  height: 170px;
  display: flex;
  flex-direction: column;
  position: absolute;
  left: 525px;
  bottom: 93px;
  z-index: 999;
  background: url(~@/assets/csts/btnBg11.png) no-repeat center / 100% 100%;
  .jgzzItem {
    width: 103px;
    height: 35px;
    margin: 2px 3px 2px 3px;
    background: url(~@/assets/csts/btn11.png) no-repeat;
    img {
      float: left;
      margin: 7px 0 0 10px;
    }
    span {
      float: left;
      font-size: 17px;
      font-family: PingFangSC, PingFang SC;
      color: #e9fffe;
      line-height: 35px;
      background: linear-gradient(180deg, #ffffff 0%, #2f8af4 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .jgzzActive {
    width: 105px;
    height: 37px;
    margin: 1px 2px 1px 2px;
    background: url(~@/assets/csts/btn12.png) no-repeat;
  }
}
.zdcsBox {
  width: 109px;
  height: 248px;
  display: flex;
  flex-direction: column;
  position: absolute;
  left: 1197px;
  bottom: 93px;
  z-index: 999;
  background: url(~@/assets/csts/btnBg12.png) no-repeat center / 100% 100%;
  .jgzzItem {
    width: 103px;
    height: 35px;
    margin: 2px 3px 2px 3px;
    background: url(~@/assets/csts/btn11.png) no-repeat;
    img {
      float: left;
      margin: 7px 0 0 10px;
    }
    span {
      float: left;
      font-size: 17px;
      font-family: PingFangSC, PingFang SC;
      color: #e9fffe;
      line-height: 35px;
      background: linear-gradient(180deg, #ffffff 0%, #2f8af4 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .jgzzActive {
    width: 105px;
    height: 37px;
    margin: 1px 2px 1px 2px;
    background: url(~@/assets/csts/btn12.png) no-repeat;
  }
}
.left,
.right {
  ::v-deep {
    .el-carousel {
      width: 450px;
      height: 920px;
      .el-carousel__indicators {
        .el-carousel__indicator {
          &.is-active {
            .el-carousel__button {
              height: 4px;
              background-color: #49e6ff;
            }
          }
          .el-carousel__button {
            height: 4px;
            background-color: rgba(255, 255, 255, 0.4);
            border-radius: 2px;
          }
        }
      }
    }
  }
}
.right {
  ::v-deep {
    .el-carousel {
      margin-left: auto;
    }
  }
}
::v-deep .el-carousel--horizontal {
  width: 450px;
  overflow-x: hidden;
}

/*先去掉默认样式*/
.swiper-button-prev:after {
  display: none;
}
.swiper-button-next:after {
  display: none;
}

/*再自定义样式*/
.swiper-button-prev {
  display: inline-block;
  width: 26px;
  height: 32px;
  background: url(~@/assets/djyl/btn-pre.png) no-repeat;
  bottom: 15px;
}
.swiper-button-next {
  display: inline-block;
  width: 26px;
  height: 32px;
  background: url(~@/assets/djyl/btn-next.png) no-repeat;
  bottom: 15px;
}

/deep/ .video-js .vjs-control {
  width: 2em;
}
/deep/ .video-js .vjs-volume-panel.vjs-volume-panel-vertical {
  width: 0em;
}
</style>
