<template>
  <div class="header">
    <div class="title">
      <!-- home切换 -->
      <div class="home_icon" v-if="false">
        <img
          src="@/assets/leader/img/component/header/home_icon.png"
          alt
          @click="showChildMenu = true"
        />
        <div class="icons" v-if="showChildMenu" @mouseleave="showChildMenu = false">
          <ul>
            <li v-for="(it, i) of icons" :key="i" @click="clickIcon(i)">{{ it }}</li>
          </ul>
        </div>
      </div>
      <!-- 下拉 -->
      <!-- <div class="dropDown">全国
        <img src="@/assets/head/xlsj.png" alt="">
      </div> -->
      <!-- 左边导航tab -->
      <div class="left_tab">
        <div
          v-for="(item, index) in leftBtns"
          :key="index"
          :class="['header_left', currentIndex == index ? 'active' : '']"
          @click="changeBtn(index, 'header_left')"
        >
          <span>{{ item }}</span>
        </div>
      </div>
      <!-- 中间标题 -->
      <div class="middle">
        <!-- <img src="@/assets/djyl/logo_header.png" alt />
        <span>一网统管综合管理平台</span> -->
        <div class="middle1">全国农机作业指挥调度平台</div>
        <div class="middle2">National Agricultural Machinery Operation Command And Dispatch Platform</div>
        <img src="@/assets/mskx/bg_head1.png" />
      </div>
      <!-- 右边导航tab -->
      <div class="right_tab">
        <div
          v-for="(item, index) in rightBtns"
          :key="index"
          :class="['header_right', currentIndex == index + 1 ? 'active' : '']"
          @click="changeBtn(index, 'header_right')"
        >
          <span>{{ item }}</span>
        </div>
      </div>
      <!-- 天气 -->
      <!-- <div class="weather">
        <img src="@/assets/leader/img/component/header/weather_icon.png" alt />
        <span>{{ tq.temperature }}°c</span>
        <span>{{ tq.weather }}</span>
      </div> -->
      <!-- 时间 -->
      <div class="time">
        <span class="time1">{{time}}</span>
        <span class="time2">{{time1}}</span>
      </div>
    </div>
  </div>
</template>

<script>
// import { tianqiapi } from '@/api/common/common'
import { bjnjUrl } from '@/utils/leader/const'
export default {
  props: {
    icons: {
      type: Array,
      default() {
        return ['城市体征', '统一门户', '指挥调度', '监测预警', '事件处置', '考核研判']
      }
    }
  },
  data() {
    return {
      currentIndex: -1,
      leftBtns: ['首页'],
      rightBtns: ['指挥调度'],
      timer: null,
      time: '',
      time1: '',
      time2: '',
      tq: {},
      showChildMenu: false,
    }
  },
  methods: {
    changeBtn(index, type) {
      if (type == 'header_left') {
        this.currentIndex = index
      } else {
        // if (index + 4 == 7) {
        //   return  this.$router.push('/tymh')
        // }
        this.currentIndex = index + 1
      }
      console.log(this.currentIndex)
      switch (this.currentIndex) {
        case 0:
          // this.$router.push('/zt')
          window.open('https://portal.camtc.cn/home/<USER>')
          break
        case 1:
          this.$router.push('/zt')
          window.open(bjnjUrl + '/api/oauth/jumpSceenOauth')
          break
        case 2:
          this.$router.push('/zt')
          break
        case 3:
          this.$router.push('/zt')
          break
        default:
          break
      }
    },
    //  时间格式化
    getTime() {
      let myDate = new Date()
      let wk = myDate.getDay()
      let weeks = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
      this.time = this.dayjs().format('YYYY/MM/DD')
      this.time1 = this.dayjs().format('HH:mm:ss')
      this.time2 = weeks[wk]
    },
    // 天气
    // gettq() {
    //   tianqiapi().then(res => {
    //     // console.log(999, res)
    //     this.tq = res.lives[0]
    //   })
    // },
    clickIcon(i) {
      console.log(i)
    }
  },
  watch: {
    $route(to, from) {
      console.log(to.path)
      switch (to.path) {
        case '/hszl':
          this.currentIndex = 0
          break
        case '/djyl':
          this.currentIndex = 1
          break
        case '/tsgz':
          this.currentIndex = 2
          break
        case '/zhny':
          this.currentIndex = 4
          break
        // case '/zhdd':
        //   this.currentIndex = 4
        //   break
        // case '/aqjg':
        //   this.currentIndex = 5
        //   break
        case '/fxyp':
          this.currentIndex = 5
          break
        case '/tymh':
          this.currentIndex = 6
          break
        default:
          break
      }
    },
    invokeRHtx(newValue, oldValue) {
      console.log('融合通信状态：','newValue'+ newValue, 'oldValue' + oldValue);
    }
  },
  mounted() {
    this.getTime()
    this.timer = setInterval(this.getTime, 1000)
    // this.gettq()
    // this.tqtime = setInterval(this.gettq, 3600000)
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  computed: {
    invokeRHtx() {
      return this.$store.state.rhtxState; 
    }
  },
}
</script>

<style lang="less" scoped>
.header {
  position: relative;
  z-index: 1003;
  .title {
    display: flex;
    z-index: 999;
    position: relative;
    .weather {
      display: flex;
      align-items: center;
      position: absolute;
      top: 49px;
      left: 50px;
      img {
        width: 53px;
        height: 39px;
      }
      & > span:first-of-type {
        font-size: 20px;
        font-family: PangMenZhengDao;
        color: #ffffff;
        line-height: 23px;
        text-shadow: 0px 2px 0px rgba(0, 32, 56, 0.5);
        margin-left: 8px;
      }
      & > span:last-of-type {
        font-size: 18px;
        font-family: PangMenZhengDao;
        color: #ffffff;
        line-height: 21px;
        letter-spacing: 1px;
        text-shadow: 0px 2px 0px rgba(0, 32, 56, 0.5);
        margin-left: 12px;
      }
    }
    .left_tab {
      position: absolute;
      left: 307px;
      top: 7px;
      display: flex;
      z-index: 99;
      .header_left {
        width: 187px;
        height: 39px;
        background: url(~@/assets/head/icon12.png) no-repeat;
        flex-shrink: 0;
        font-size: 20px;
        line-height: 39px;
        text-align: center;
        cursor: pointer;
        padding-left: 5px;
        span {
          display: inline-block;
          width: 87px;
          height: 29px;
          font-family: YouSheBiaoTiHei;
          font-size: 22px;
          color: #B0E0FF;
          line-height: 29px;
          text-shadow: 0px 1px 0px rgba(0,32,56,0);
          text-align: center;
          font-style: normal;
          background-image: -webkit-gradient(linear, 0 0, 0 bottom, from(#FFFFFF), to(#89D9FF));
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        &:nth-of-type(2) {
          margin-left: 6px;
        }
        &:nth-of-type(3) {
          margin-left: 6px;
        }
        &:nth-of-type(4) {
          margin-left: 6px;
        }
        &.active {
          background: url(~@/assets/head/icon11.png) no-repeat;
          span {
            display: inline-block;
            width: 87px;
            height: 29px;
            font-family: YouSheBiaoTiHei;
            font-size: 22px;
            color: #B0E0FF;
            line-height: 29px;
            text-shadow: 0px 1px 0px rgba(0,32,56,0);
            text-align: center;
            font-style: normal;
            background-image: -webkit-gradient(linear, 0 0, 0 bottom, from(#FFFFFF), to(#FFF076));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
      }
    }
    .right_tab {
      display: flex;
      position: absolute;
      right: 325px;
      top: 7px;
      z-index: 99;
      .header_right {
        width: 187px;
        height: 39px;
        background: url(~@/assets/head/icon13.png) no-repeat;
        flex-shrink: 0;
        font-size: 20px;
        line-height: 39px;
        text-align: center;
        cursor: pointer;
        padding-right: 5px;
        span {
          display: inline-block;
          width: 87px;
          height: 29px;
          font-family: YouSheBiaoTiHei;
          font-size: 22px;
          color: #B0E0FF;
          line-height: 29px;
          text-shadow: 0px 1px 0px rgba(0,32,56,0);
          text-align: center;
          font-style: normal;
          background-image: -webkit-gradient(linear, 0 0, 0 bottom, from(#FFFFFF), to(#89D9FF));
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        &:nth-of-type(2) {
          margin-left: 6px;
        }
        &:nth-of-type(3) {
          margin-left: 6px;
        }
        &:nth-of-type(4) {
          margin-left: 6px;
        }
        &.active {
          background: url(~@/assets/head/icon14.png) no-repeat;
          span {
            display: inline-block;
            width: 87px;
            height: 29px;
            font-family: YouSheBiaoTiHei;
            font-size: 22px;
            color: #B0E0FF;
            line-height: 29px;
            text-shadow: 0px 1px 0px rgba(0,32,56,0);
            text-align: center;
            font-style: normal;
            background-image: -webkit-gradient(linear, 0 0, 0 bottom, from(#FFFFFF), to(#FFF076));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
      }
    }
    .middle {
      position: absolute;
      left: 0px;
      top: 0px;
      width: 1920px;
      height: 141px;
      // background: url(~@/assets/head/bg2.png) no-repeat;
      font-size: 48px;
      font-family: PangMenZhengDao;
      color: #ffffff;
      line-height: 100px;
      letter-spacing: 6px;
      text-shadow: -2px -4px 0px rgba(0, 33, 57, 0.5);
      // -webkit-background-clip: text;
      // -webkit-text-fill-color: transparent;
      img {
        width: 1920px;
        height: 141px;
        position: absolute;
        left: 0%;
        top: 0;
        z-index: -1;
      }
      span {
        font-size: 48px;
        font-family: PangMenZhengDao;
        color: #ffffff;
        line-height: 55px;
        letter-spacing: 2px;
        text-shadow: -2px -4px 0px rgba(0, 33, 57, 0.5);
        // -webkit-background-clip: text;
        // -webkit-text-fill-color: transparent;
      }
      .middle1 {
        width: 605px;
        height: 57px;
        font-family: YouSheBiaoTiHei;
        font-size: 44px;
        color: #FFFFFF;
        line-height: 57px;
        letter-spacing: 6px;
        text-shadow: 0px 5px 0px rgba(0,0,0,0);
        text-align: left;
        font-style: normal;
        background-image: -webkit-gradient(linear, 0 0, 0 bottom, from(#FFFFFF), to(#28CFFF));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin: 0 auto;
        margin-top: 14px;
        text-align: center;
      }
      .middle2 {
        width: 384px;
        height: 18px;
        font-family: DINOT, DINOT;
        font-weight: normal;
        font-size: 14px;
        color: #3B4E5A;
        line-height: 18px;
        text-shadow: 0px 5px 0px rgba(0,0,0,0);
        text-align: left;
        font-style: normal;
        text-transform: uppercase;
        background-image: -webkit-gradient(linear, 0 0, 0 bottom, from(#ECF5FF), to(#ADDAF4));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin: 0 auto;
        margin-top: -5px;
      }
    }
    .time {
      position: absolute;
      top: 15px;
      right: 21px;
      .time1 {
        display: inline-block;
        font-family: YouSheBiaoTiHei;
        font-size: 14px;
        color: #7BF3FB;
        line-height: 18px;
        text-shadow: 0px 2px 0px rgba(0,32,56,0.5);
        text-align: right;
        font-style: normal;
        margin-right: 4px;
        vertical-align: middle;
        margin-top: -7px;
      }
      .time2 {
        display: inline-block;
        font-family: YouSheBiaoTiHei;
        font-size: 20px;
        color: #FFFFFF;
        line-height: 26px;
        text-shadow: 0px 2px 0px rgba(0,32,56,0.5);
        text-align: right;
        font-style: normal;
      }
    }
    .dropDown {
      position: absolute;
      top: 9px;
      left: 37px;
      width: 112px;
      height: 36px;
      background: linear-gradient(0deg , rgba(36,132,212,0.5) 0%, rgba(17,77,168,0) 100%);
      box-shadow: inset 0px 0px 2px 0px #57AFFF;
      border-radius: 2px;
      border: 1px solid;
      border-image: linear-gradient(230deg, rgba(99, 173, 235, 1), rgba(19, 89, 194, 1), rgba(19, 89, 194, 1), rgba(99, 173, 235, 0.57)) 1 1;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #FFFFFF;
      line-height: 36px;
      text-shadow: 0px 2px 3px rgba(0,48,72,0.5);
      text-align: left;
      font-style: normal;
      padding-left: 13px;
      img {
        float: right;
        margin: 9px 10px 0 0;
      }
    }
  }
}
.home_icon {
  position: absolute;
  top: 49px;
  right: 20px;
  display: flex;
  align-items: center;
  // margin-left: 24px;
  & span:first-of-type {
    font-size: 18px;
    font-family: PangMenZhengDao;
    color: #7bf3fb;
    line-height: 21px;
    text-shadow: 0px 2px 0px rgba(0, 32, 56, 0.5);
  }
  & > div {
    margin-left: 11px;
    width: 1px;
    height: 20px;
    border: 1px solid;
    border-image: linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(123, 243, 251, 1)) 1 1;
  }
  & span:last-of-type {
    margin-left: 11px;
    font-size: 22px;
    font-family: PangMenZhengDao;
    color: #ffffff;
    line-height: 25px;
    text-shadow: 0px 2px 0px rgba(0, 32, 56, 0.5);
  }
  img {
    margin-left: 14px;
    width: 44px;
    height: 44px;
    cursor: pointer;
  }
  .icons {
    position: absolute;
    width: 284px;
    height: 193px;
    background: url(~@/assets/head/icon_bg.png) no-repeat;
    right: 0;
    top: 50px;
    margin-left: -11px;
    border: none;
    padding: 26px 12px 16px;
    ul {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      li {
        width: 126px;
        height: 45px;
        background: url(~@/assets/head/icon_normal.png) no-repeat;
        font-size: 18px;
        font-family: PangMenZhengDao;
        color: #cdf6ff;
        line-height: 45px;
        letter-spacing: 1px;
        cursor: pointer;
        &:hover {
          background: url(~@/assets/head/icon_active.png) no-repeat;
          color: #fff6cd;
        }
      }
    }
  }
}
</style>
