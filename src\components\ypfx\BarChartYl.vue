<template>
  <chart class="charts" :autoresize="true" :option="options" :loading="loading"></chart>
</template>

<script setup>
import { ref } from 'vue'
// import { graphic } from 'echarts/lib/export/api.js'
const loading = ref(true)
const options = ref({
  tooltip: {
    //提示框组件
    trigger: 'axis',
    backgroundColor: 'rgba(0, 33, 59, 0.8)',
    textStyle: {
      color: '#fff'
    },
    borderColor: 'rgba(24, 174, 236, 1)',
    formatter: function(params) {
      return `${params[0].seriesName} ${params[0].data}<br>${params[2].seriesName} ${params[2].data}`
    },
    axisPointer: {
      type: 'shadow',
      label: {
        backgroundColor: '#6a7985'
      }
    }
  },
  grid: {
    left: '10%',
    right: '10%',
    bottom: '5%',
    top: 40,
    padding: '0 0 10 0',
    containLabel: true
  },
  legend: {
    //图例组件，颜色和名字
    right: 10,
    top: 0,
    itemGap: 16,
    itemWidth: 11,
    itemHeight: 8,
    data: [
      {
        name: '敏感'
      },
      {
        name: '非敏感'
      }
    ],
    textStyle: {
      color: 'rgba(157, 226, 255, 1)',
      fontStyle: 'normal',
      fontSize: 14
    }
  },
  xAxis: [
    {
      type: 'category',
      boundaryGap: true, //坐标轴两边留白
      data: ['05/07', '05/08', '05/09', '05/10', '05/11'],
      axisLabel: {
        //坐标轴刻度标签的相关设置。
        interval: 0, //设置为 1，表示『隔一个标签显示一个标签』
        margin: 10,
        padding: 10,
        // rotate: 30,
        textStyle: {
          color: 'rgba(157, 226, 255, 1)',
          fontStyle: 'normal',
          fontFamily: '微软雅黑',
          fontSize: 12
        }
      },
      axisTick: {
        //坐标轴刻度相关设置。
        show: false
      },
      axisLine: {
        //坐标轴轴线相关设置
        lineStyle: {
          color: '#fff',
          opacity: 0.2
        }
      },
      splitLine: {
        //坐标轴在 grid 区域中的分隔线。
        show: false
      }
    }
  ],
  yAxis: [
    {
      type: 'value',
      splitNumber: 5,
      axisLabel: {
        textStyle: {
          color: 'rgba(157, 226, 255, 1)',
          fontStyle: 'normal',
          fontFamily: '微软雅黑',
          fontSize: 12
        }
      },
      axisLine: {
        show: true,
        color: '#fff'
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: false,
        lineStyle: {
          color: ['#fff'],
          opacity: 0.06
        }
      }
    }
  ],
  series: [
    {
      name: '非敏感',
      type: 'bar',
      data: [0.49, 0.73, 0.92, 0.56, 0.77],
      barWidth: 5,
      barGap: 0, //柱间距离
      label: {
        show: true,
        formatter: '{a|}',
        color: '#fff',
        position: 'top',
        distance: -5,
        backgroundColor: 'rgba(96, 254, 254, 1)',
        padding: 2,
        borderRadius: 4,
        rich: {
          a: {
            width: 2,
            height: 2,
            borderRadius: 2,
            lineHeight: 2,
            backgroundColor: '#fff'
          }
        }
      },
      itemStyle: {
        //图形样式
        normal: {
          barBorderRadius: [3, 3, 0, 0],
            color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 1,
                color: 'rgba(52, 104, 138, 1)' // 0% 处的颜色
              },
              {
                offset: 0,
                color: 'rgba(44, 186, 175, 1)' // 100% 处的颜色
              }
            ],
            globalCoord: false // 缺省为 false
          }
        }
      },
      z: 1
    },
    {
      name: '非敏感',
      type: 'bar',
      barGap: '-145%',
      data: [0.52, 0.76, 0.95, 0.59, 0.8],
      barWidth: 10,
      itemStyle: {
        normal: {
          color: 'none',
          borderColor: 'rgba(52, 104, 138, 1)',
          borderWidth: 1,
          barBorderRadius: [3, 3, 0, 0]
        }
      },
      z: 3
    },
    {
      name: '敏感',
      type: 'bar',
      data: [0.29, 0.5, 0.44, 0.27, 0.57],
      barWidth: 5,
      barGap: '-155%', //柱间距离
      label: {
        show: true,
        formatter: '{a|}',
        color: '#fff',
        position: 'top',
        distance: -5,
        backgroundColor: 'rgba(241, 132, 43, 1)',
        padding: 2,
        borderRadius: 4,
        rich: {
          a: {
            width: 2,
            height: 2,
            borderRadius: 2,
            lineHeight: 2,
            backgroundColor: '#fff'
          }
        }
      },
      itemStyle: {
        //图形样式
        normal: {
          barBorderRadius: [3, 3, 0, 0],
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 1,
                color: 'rgba(136, 96, 79, 0.7)' // 0% 处的颜色
              },
              {
                offset: 0,
                color: 'rgba(241, 132, 43, 1)' // 100% 处的颜色
              }
            ],
            globalCoord: false // 缺省为 false
          }
        }
      },
      z: 2
    },
    {
      name: '敏感',
      type: 'bar',
      barGap: '-155%',
      data: [0.32, 0.53, 0.47, 0.3, 0.6],
      barWidth: 10,
      itemStyle: {
        normal: {
          color: 'none',
          borderColor: 'rgba(52, 104, 138, 1)',
          borderWidth: 1,
          barBorderRadius: [3, 3, 0, 0]
        }
      },
      z: 3
    }
  ]
})
setTimeout(() => {
  loading.value = false
}, 300)
</script>

<style lang="scss" scoped>
.charts {
  width: 100%;
  height: 100%;
}
</style>