<template>
	<div>
		<div ref="smLeaMap" class="mapContainer" />
		<!-- <div class="clear2" @click="clearLayers()" v-if="clear2"></div>

		<div class="back2" @click="backLayers()" v-if="back2"></div> -->

		<!-- <div class="weather" @click="showLayers">图形</div> -->
	</div>
</template>

<script>
var clusterIndex
var clusterMarkers
const DataVUrl = 'https://geo.datav.aliyun.com/areas_v3/bound/'
import { fromArrayBuffer } from 'geotiff'
import * as plotty from 'plotty'
import testData from './testData.json'
import gcoord from 'gcoord'
import RadarScanner from './utils/RadarScanner'
import { picTif, walarmPic, meteorologicalPic, rainfallPic, windPic, extractFile } from '@/api/common/common'
import iconNjBUrl from '@/assets/mskx/icon_nj_b.png'
import simplify from 'simplify-js'
import { remove } from 'lodash'
import jiangShuiJsonData from '@/assets/json/qxData/qxData.json'
import dayjs from 'dayjs'

export default {
	components: {},
	props: {
		clear2: {
			type: Boolean,
			default: true,
		},
		back2: {
			type: Boolean,
			default: true,
		},
		xzpd: {
			type: Boolean,
			default: true,
		},
		// haveRight: {
		// 	type: Boolean,
		// 	default: true,
		// },
	},
	data() {
		return {
			marker1: [],
			polygon: null,
			gridLayers: [],
			map: null,
			mapOptions: {
				// 中心点
				// center: this.haveRight ? [35.595703125, 105.029296875] : [39, 98.029296875],
				center:  [35, 97.029296875],
				// 当前显示层级
				zoom: 4,
				// 最小显示层级
				minZoom: 3,
				maxZoom: 17,
			},
			rootLayerGroup: null,
			layerList: [],
			tdtKey: 'd9e005e98452c5eb2ad792489d2a369b',
			bounds: [100.063476, 23.805449, 119.0215, 40.713955],
			dPolyline: null,
			dPolygon: null,
			maskLayer: null,
			imageMaskLayer: null,
			geojsonLayer: null,
			outerGeojsonLayer: null,
			dynamicCicle: null,
			dynamicCicleInterval: null,
			// lastAreaCode: [100000],
			lastAreaCode: [],
			circleList: [],
			radarScanner: null,
			wmsTemwLayer: null,
			canvasIconLayer_: null,
			sstsMarkers: {},
			markerSourceData: {},
			currentLayerId: '',
			currentMapZoom: 4,
			timer001: null,
			timerLoadGeoJsonLayer: null,

			newZoomCluster: 0,
			maxZoomCluster: 9,
			oldZoomCluster: 0,
			isClickGrid: false,
			currentMapLevel: '',

			mapTheme: true, //地图样式：true:行政区划图，false:路线图
			showHeat: false,
			isChangeMapBg: true,

			showQxyjLayer: false,
			layerCount: 0,
			selectedRange: '24h',
			currentUnit: 0,
			moduleType: '',
			// [minLon, minLat, maxLon, maxLat]
			// [3.58, 73.33],
			// 						[53.55, 135.05]
			regionCodeBbox: {
				minLng: 73.33,
				minLat: 3.58,
				maxLng: 135.05,
				maxLat: 53.55,
			},

			mapLevel: 0,

			geometry: [],
			tempLines: null,
			lines: null,
			timer: null,
			canGridClick: true,
			polygonPoints: [],
			currentBoundary: null,
			heatLayer: null,

			//------------新增变量---------------
			currentDataUrl: null,
			currentGeoData: null,
			velocityLayer: null,
			//---------------------------
		}
	},
	mounted() {
		this.initMap()
	},
	methods: {
		/**
		 * 初始化地图
		 */
		async initMap() {
			this.map = L.map(this.$refs.smLeaMap, {
				crs: L.CRS.EPSG4326,
				// crs: crs,
				center: this.mapOptions.center,
				zoomDelta: 0.5,
				zoom: this.mapOptions.zoom,
				minZoom: this.mapOptions.minZoom,
				maxZoom: this.mapOptions.maxZoom,
				// 不添加属性说明控件
				attributionControl: true,
				zoomControl: false,
				editable: true,
				doubleClickZoom: false,
			})

			//#region 天地图地图加载
			// 天地图
			// L.tileLayer(
			//   'http://t1.tianditu.com/img_c/wmts?layer=img&style=default&tilematrixset=c&Service=WMTS&Request=GetTile&Version=1.0.0&Format=tiles&TileMatrix={z}&TileCol={x}&TileRow={y}&tk=' +
			//     this.tdtKey,
			//   {
			//     tileSize: 256,
			//     zoomOffset: 1
			//   }
			// ).addTo(this.map)
			// L.tileLayer(
			//   'http://t1.tianditu.com/cia_c/wmts?layer=cia&style=default&tilematrixset=c&Service=WMTS&Request=GetTile&Version=1.0.0&Format=tiles&TileMatrix={z}&TileCol={x}&TileRow={y}&tk=' +
			//     this.tdtKey,
			//   {
			//     tileSize: 256,
			//     zoomOffset: 1
			//   }
			// ).addTo(this.map)
			//#endregion

			this.loadBoundaryFill()

			this.loadTdtXYZ()

			// this.InformationMarker()

			// 添加根图层，方便管理其他图层
			this.rootLayerGroup = this.group ? this.group : L.featureGroup().addTo(this.map)

			this.onMapClick()
			// this.onMapZoomend()

			this.map.on('moveend', this.updateCluster)

			this.$nextTick(() => {
				// 加载行政区域
				this.loadGeoJsonLayerFromUrl(DataVUrl + '100000_full.json', 'geojson', true)
				// 随机生成1000个点

				// 轨迹
				const trackData = testData.track
				// 数字点位（模仿聚合点）

				const that = this

				that.canvasIconLayer_ = L.canvasIconLayer({ moveReset: false, collisionFlg: true, isImg: true, zIndex: 999 }).addTo(that.map)
				//为标记点创建点击事件
				that.canvasIconLayer_.addOnClickListener(function(event, data) {
					if (that.map.getZoom() >= 10) {
						const { info: data_ } = data[0].data.options.data.props
						const { agmachId } = data_
						that.$emit('poiClick', that.currentLayerId, [agmachId])
					}
				})
				that.map.on('zoomstart', () => {
					// that.removeLayer(that.currentLayerId)

					if (this.map.getZoom() >= 10) {
						// this.removeLayer(this.currentLayerId)
					} else if (this.map.getZoom() <= 9) {
						if (this.currentMapLevel !== '') {
							// this.clearCluster()
						}
					}
				})
				that.map.on('zoomend', that.onWheelMap)
				this.$store.commit('updateMapLoaded', true)
			})
		},

		async onWheelMap(ev) {
			this.currentMapZoom = this.map.getZoom()
			clearTimeout(this.timer001)

			this.timer001 = setTimeout(() => {
				if (!this.isClickGrid) {
					// //给鼠标滚动添加防抖
					this.newZoomCluster = this.map.getZoom()

					if (
						(this.newZoomCluster > this.maxZoomCluster && this.oldZoomCluster < this.maxZoomCluster) ||
						(this.newZoomCluster < this.maxZoomCluster && this.oldZoomCluster > this.maxZoomCluster)
					) {
						this.oldZoomCluster = this.newZoomCluster

						if (this.map.getZoom() >= 10) {
							if (this.currentLayerId == 'zxnjType') {
								// await this.removeLayer(this.currentLayerId)
								// this.drawSstsPoiMarker(this.markerSourceData[this.currentLayerId] || [], this.currentLayerId) //调用4marker标点方法
							}
						} else if (this.map.getZoom() <= 9) {
							if (this.currentMapLevel !== '') {
								// await this.clearCluster()
								// this.drawSstsBigDataClusterPoint(
								// 	this.markerSourceData[this.currentLayerId] || [],
								// 	require('@/assets/mskx/icon_nj_b.png'),
								// 	[48, 62],
								// 	{
								// 		largeIconUrl: './imgs/cluster/nj1.png',
								// 		mediumIconUrl: './imgs/cluster/nj2.png',
								// 		smallIconUrl: './imgs/cluster/nj3.png',
								// 	},
								// 	this.currentLayerId,
								// )
							}
						}
					}
				}
				// if (this.showQxyjLayer) {
				// 	this.loadWaterLayer('precipitationType', this.layerCount + 1, this.selectedRange, this.currentUnit)
				// }
			}, 500)
		},

		serefresh(point) {
			this.polygon = L.polygon(point, { color: 'yellow', fillColor: 'yellow', fillOpacity: 1 }).addTo(this.map)
			// zoom the map to the polygon
			this.map.fitBounds(this.polygon.getBounds())
		},
		/**
		 * 天地图XYZ方式加载
		 */
		loadTdtXYZ() {
			const tdt_url_bottom = 'https://t{s}.tianditu.gov.cn/DataServer?T=img_c&x={x}&y={y}&l={z}&tk='
			const tdt_url_label = 'https://t{s}.tianditu.gov.cn/DataServer?T=cia_c&x={x}&y={y}&l={z}&tk='

			const layer_bottom = L.tileLayer(tdt_url_bottom + this.tdtKey, {
				zoomOffset: 1,
				tileSize: 256,
				minZoom: 3,
				maxZoom: 17,
				subdomains: [0, 1, 2, 3, 4, 5, 6, 7],
			})
			const layer_label = L.tileLayer(tdt_url_label + this.tdtKey, {
				zoomOffset: 1,
				tileSize: 256,
				minZoom: 3,
				maxZoom: 17,
				subdomains: [0, 1, 2, 3, 4, 5, 6, 7],
			})
			layer_bottom.addTo(this.map)
			layer_label.addTo(this.map)

			// L.tileLayer(
			// 	`http://t{s}.tianditu.gov.cn/vec_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=vec&tileMatrixSet=w&TileMatrix={z}&TileRow={y}&TileCol={x}&style=default&format=tiles&tk=${this.tdtKey}`,
			// 	{
			// 		zoomOffset: 1,
			// 		tileSize: 256,
			// 		minZoom: 3,
			// 		// maxZoom: 17,
			// 		// subdomains: [0, 1, 2, 3, 4, 5, 6, 7],
			// 		maxZoom: 17,
			// 		offset: [],
			// 		// 子域名
			// 		subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
			// 	},
			// ).addTo(this.map)

			// L.tileLayer.chinaProvider('TianDiTu.Normal.Map', { maxZoom: 17, minZoom: 3, key: this.tdtKey }).addTo(this.map)
			// L.tileLayer.chinaProvider('TianDiTu.Normal.Annotion', { maxZoom: 17, minZoom: 3, key: this.tdtKey }).addTo(this.map) // 设置地图图层，可以按需引入；this.mapKey是自己的天地图key值
		},

		loadBoundaryFill() {},

		/**
		 * 地图点击
		 */
		onMapClick() {
			this.map.on('click', (e) => {})
		},
		drawCircle(data, r = 2, color = '#c53114') {
			this.map.setView([data[0].lat, data[0].lon], 12)
			this.removeCicleList()
			this.circleList = []
			if (data.length) {
				data.forEach((item, index) => {
					// 创建圆形对象
					let circle = L.circle([item.lat, item.lon], {
						color: color,
						fillColor: color,
						fill: true,
						radius: r, // 单位：米
						weight: 0,
						fillOpacity: '1',
					})
					this.circleList.push(circle)
					// 将圆形添加到地图上
					circle.addTo(this.map)
				})
			}
		},
		removeCicleList() {
			if (this.circleList.length) {
				this.circleList.map((item) => {
					this.map.removeLayer(item)
				})
			}
		},
		/**
		 * 加载动态光圈
		 * @param {*} center 中心点[纬度，经度]
		 * @param {*} radius 半径（单位米）
		 * @param {*} color 颜色
		 */
		loadDaynamicCicle(center, radius, color = '#FF9900') {
			this.dynamicCicle = L.circle(center, {
				radius: 5, // 初始半径
				color: color,
				fillColor: color,
				fillOpacity: 0.2,
				weight: 2,
			}).addTo(this.map)
			const radiuschange = Math.round(radius / 25)
			var that = this
			this.dynamicCicleInterval = setInterval(() => {
				that.dynamicCicle.setRadius(that.dynamicCicle.getRadius() + radiuschange)
				if (that.dynamicCicle.getRadius() > radius) {
					that.dynamicCicle.setRadius(5)
				}
			}, 200)
		},
		/**
		 * 移除动态光圈
		 */
		removeDaynamicCicle() {
			this.dynamicCicleInterval && clearInterval(this.dynamicCicleInterval)
			this.dynamicCicle && this.map.removeLayer(this.dynamicCicle)
		},
		/**
		 * 加载雷达扫描效果
		 * @param {*} coord 扫描中心点[118.7644, 32.0977]
		 * @param {*} radius 半径
		 */
		loadRadarScan(coord, radius) {
			this.removeRadarScan()
			this.radarScanner = new RadarScanner(this.map, gcoord)
			this.radarScanner.initRadarLayer(coord, radius)
		},
		removeRadarScan() {
			this.radarScanner && this.radarScanner.removeRadarLayer()
		},
		/**
		 * 加载wms气象图层
		 * @param {*} layerId 图层id
		 * @param {*} layers 查询图层名
		 * @param {*} category 智能格点
		 * @param {*} element 要素名
		 * @param {*} height 高度
		 * @param {*} publish_time 发布时间
		 * @param {*} valid_time 时间区间
		 * @param {*} opacity 透明度 范围0-1
		 */
		async loadWmsLayer(layerId, layers, category, element, geoHeight, publish_time, valid_time, opacity, previousLayerId) {
			const wmsLayer = L.tileLayer.wms('http://101.201.56.243/geoserver/gtweb/wms', {
				layers: layers,
				format: 'image/png',
				transparent: true,
				width: 1500,
				height: 900,
				// format_options: 'antialias:full;dpi:180',
				// styles: 'polygon_smoothing',
				// 过滤查询参数
				// cql_filter: `product_category='WF-NWFD' and product_element='TMA' and geo_height='L88' and coverage_area='CHN' and publish_time='20240416080000' and valid_time='00000-02400'`,
				// cql_filter: `product_category='CLDAS2' and product_element='TMA' and geo_height='L2M' and coverage_area='CHN' and publish_time='20240416080000' and valid_time='02400-00000'`,
				cql_filter: `product_category='${category}' and product_element='${element}' and geo_height='${geoHeight}' and coverage_area='CHN' and publish_time='${publish_time}' and valid_time='${valid_time}'`,
				opacity: opacity,
			})
			this.rootLayerGroup.addLayer(wmsLayer)
			this.layerList.push({
				id: layerId,
				layer: wmsLayer,
			})

			setTimeout(async () => {
				await this.removeLayer(previousLayerId)
			}, 800)
		},
		/**
		 * 加载GeoTiff图层
		 * @param {String} layerId 图层id
		 * @param {String} dataUrl 数据地址
		 * @return {*}
		 */
		async loadGeoTiffLayer(layerId, dataUrl) {
			this.removeLayer(layerId)
			const geotiffLayer = await this.renderTIFF(dataUrl)
			this.rootLayerGroup.addLayer(geotiffLayer)
			this.layerList.push({
				id: layerId,
				layer: geotiffLayer,
			})
		},
		/**
		 * @description: 渲染tif
		 * @param {*} url
		 * @return {*}
		 */
		async renderTIFF(url) {
			const response = await fetch(url)
			const arrayBuffer = await response.arrayBuffer()
			const tiff = await fromArrayBuffer(arrayBuffer)
			const img = await tiff.getImage()
			const data = await img.readRasters()
			const bounds = img.getBoundingBox()
			const canvas = document.createElement('canvas')
			canvas.width = img.getWidth()
			canvas.height = img.getHeight()
			const arr = data[0]
			let max = arr[0],
				min = arr[0]
			arr.forEach((item) => (max = item > max ? item : max))
			arr.forEach((item) => (min = item < min ? item : min))
			const plot = new plotty.plot({
				canvas,
				data: data[0],
				width: img.getWidth(),
				height: img.getHeight(),
				domain: [min, max - 10],
				colorScale: 'diverging_2',
				applyDisplayRange: false,
				clampLow: false,
				clampHigh: false,
				// noDataValue: max
			})
			plot.render({
				// noDataValueColor: 'transparent' // 设置透明色
			})
			let b64 = canvas.toDataURL('image/png')
			return L.imageOverlay(
				b64,
				[
					[bounds[1], bounds[0]],
					[bounds[3], bounds[2]],
				],
				{
					opacity: 0.5,
				},
			)
		},

		/**
		 * @description: 加载降水量预测图
		 * @param {*} layerId 图层id
		 * @param {*} imageBounds 经纬度范围
		 * @return {*}
		 */
		async loadWaterLayer(layerId, count, selectedRange, currentUnit) {
			/**
			 * 方法一，自定义图片图层
			 */
			// this.selectedRange = selectedRange
			// this.currentUnit = currentUnit
			// this.layerCount = count
			// // [[最小纬度，最小经度], [最大纬度，最大经度]]
			// // [minLat, minLng, maxLat, maxLng]
			// //使用降水量图层渲染
			// this.regionCodeBbox = await this.getRegionCodeBbox(this.currentGeoData)

			// const imageBounds = [
			// 	[parseFloat(this.regionCodeBbox.minLat), parseFloat(this.regionCodeBbox.minLng)],
			// 	[parseFloat(this.regionCodeBbox.maxLat), parseFloat(this.regionCodeBbox.maxLng)],
			// ]
			// const imgWidth = 1500
			// const imgHeight = 1600

			// const publish_time = getPublishTime()
			// // console.log('publish_time--==', publish_time)
			// const valid_time = getValidTimeRange(selectedRange, currentUnit)
			// // console.log('valid_time--==', valid_time)
			// const layersType = getLayersType(selectedRange)

			// const imgUrl = `http://101.201.56.243/geoserver/gtweb/wms?SERVICE=WMS&VERSION=1.1.0&REQUEST=GetMap&FORMAT=image/png&TRANSPARENT=true&LAYERS=${layersType}&WIDTH=${imgWidth}&HEIGHT=${imgHeight}&STYLES=&SRS=EPSG:4326&FORMAT_OPTIONS=application/openlayers&bbox=${parseFloat(
			// 	imageBounds[0][1],
			// )},${parseFloat(imageBounds[0][0])},${parseFloat(imageBounds[1][1])},${parseFloat(
			// 	imageBounds[1][0],
			// )}&CQL_FILTER=product_category='WF-NWFD' and product_element='PRCPV' and geo_height='L88' and coverage_area='CHN' and publish_time=${publish_time} and valid_time='${valid_time}'`
			// console.log('imgUrl--==', imgUrl)

			// // const imgLayer = L.imageOverlay(require('@/assets/img/qx/gtweb-pre24hour.png'), imageBounds, {

			// const imgLayer = await L.imageOverlay(imgUrl, imageBounds, {
			// 	opacity: 1,
			// })
			// const currentLayerId = `${layerId}${count}`

			// await this.rootLayerGroup.addLayer(imgLayer)
			// await this.layerList.push({
			// 	id: currentLayerId,
			// 	// id: layerId,
			// 	layer: imgLayer,
			// })
			// console.log('layerCount--==', this.layerCount)

			// setTimeout(async () => {
			// 	const previousLayerId = `${layerId}${count - 1}`
			// 	await this.removeLayer(previousLayerId)
			// }, 800)
			// this.$emit('updateWaterLayerCount', this.layerCount)

			/**
			 * 方法二，自定义切片图层
			 */
			const publish_time = getPublishTime()
			// console.log('publish_time--==', publish_time)
			const valid_time = getValidTimeRange(selectedRange, currentUnit)
			// console.log('valid_time--==', valid_time)
			const layersType = getLayersType(selectedRange)

			const currentLayerId = `${layerId}${count}`
			const previousLayerId = `${layerId}${count - 1}`

			this.loadWmsLayer(currentLayerId, layersType, 'WF-NWFD', 'PRCPV', 'L88', publish_time, valid_time, 0.8, previousLayerId)

			function getLayersType(selectedRange) {
				if (selectedRange == '24h') {
					return 'pre1hour'
				} else {
					return 'pre24hour'
				}
			}

			function getPublishTime() {
				const now = new Date()
				const currentHour = now.getHours()
				const currentDate = now.getDate()

				// 获取基准时间（用于处理跨天情况）
				const baseDate = new Date(now)

				// 判断当前应该使用的发布时次
				let publishHour
				if (currentHour >= 5 && currentHour < 17) {
					// 05:00-16:59 使用当天08时的预报
					publishHour = 8
				} else {
					// 17:00-04:59 使用当天20时的预报
					publishHour = 20

					// 如果当前时间小于5点，实际上使用的是前一天的20时预报
					if (currentHour < 5) {
						baseDate.setDate(currentDate - 1)
					}
				}
				publishHour = 8

				// 格式化为YYYYMMDDHH0000
				const year = baseDate.getFullYear()
				const month = String(baseDate.getMonth() + 1).padStart(2, '0')
				const day = String(baseDate.getDate()).padStart(2, '0')
				const hour = String(publishHour).padStart(2, '0')

				return `${year}${month}${day}${hour}0000`
			}

			function getValidTimeRange(selectedRange, currentUnit) {
				// 确保currentUnit是数字类型
				currentUnit = Number(currentUnit)
				console.log('selectedRange--==', selectedRange)

				// 处理24小时模式
				if (selectedRange === '24h') {
					// 验证currentUnit范围
					if (currentUnit < 0 || currentUnit >= 24) {
						throw new Error('24小时模式下，currentUnit应在0-23之间')
					}

					// 格式化小时数为两位数
					const startHour = currentUnit.toString().padStart(2, '0')
					const endHour = (currentUnit + 1).toString().padStart(2, '0')

					return `0${startHour}00-0${endHour}00`
				}
				// 处理3天或7天模式
				else if (selectedRange === '3d' || selectedRange === '7d') {
					const maxDays = selectedRange === '3d' ? 3 : 7

					// 验证currentUnit范围
					if (currentUnit < 0 || currentUnit >= maxDays) {
						throw new Error(`${selectedRange}模式下，currentUnit应在0-${maxDays - 1}之间`)
					}

					// 计算小时数
					const startHours = (currentUnit * 24).toString().padStart(3, '0')
					const endHours = ((currentUnit + 1) * 24).toString().padStart(3, '0')
					return `${startHours}00-${endHours}00`
				}
				// 处理无效的selectedRange
				else {
					throw new Error('selectedRange参数必须是"24h"、"3d"或"7d"')
				}
			}

			/**
			 * 方法三，使用.grb2文件
			 */
			this.byGrb2(layerId)
		},

		async byGrb2() {
			// 1. 加载GRIB2文件
			const jiangShuiData_ = jiangShuiJsonData.variables
			console.log('jiangShuiData_--==', jiangShuiData_)
			const { lon, lat, Total_precipitation_surface_24_Hour_Accumulation } = jiangShuiData_
			console.log('lon--==', lon)
			console.log('lat--==', lat)
			console.log('降水量数据--==', Total_precipitation_surface_24_Hour_Accumulation.data)
		},

		/**
		 * @description: 加载风场图层
		 * @param {*} layerId 图层id
		 * @param {*} dataUrl 数据地址
		 * @return {*}
		 */
		async loadWindLayer(layerId, dataUrl) {
			const response = await fetch(dataUrl)
			if (response.status == 200) {
				const data = await response.json()
				this.removeLayer(layerId)
				const windLayer = L.velocityLayer({
					// displayValues: true,
					// displayOptions: {
					//   velocityType: "Global Wind",
					//   position: "bottomleft",
					//   emptyString: "No wind data",
					// },
					data: data,
					// maxVelocity: 15,
				})
				this.rootLayerGroup.addLayer(windLayer)
				this.layerList.push({
					id: layerId,
					layer: windLayer,
				})
			} else {
				console.error('无风场数据！！！')
			}
		},

		removeWaterLayer(layerId, count) {
			for (let index = 0; index <= count; index++) {
				const currentLayerId = `${layerId}${index}`
				this.removeLayer(currentLayerId)
			}
		},
		/**
		 * @description: 叠加图片图层
		 * @param {*} layerId 图层id
		 * @param {*} imageUrl 图片地址
		 * @param {*} imageBounds 图片经纬度范围 [[10, 70],[54, 140]]
		 * @return {*}
		 */
		loadImageLayer(layerId, imageUrl, imageBounds) {
			this.removeLayer(layerId)
			const imgLayer = L.imageOverlay(imageUrl, imageBounds)
			this.rootLayerGroup.addLayer(imgLayer)
			this.layerList.push({
				id: layerId,
				layer: imgLayer,
			})
		},
		/**
		 * @description: 加载作业中心功能图层（日期地点弹出层、行军路线层）
		 * @param {*} layerId 图层id
		 * @param {*} data [{name: '保定', date: '2024-01-01', latlon: [38.8780, 115.4905]}, ...]
		 * @return {*}
		 */
		loadZyzxLayer(layerId, data) {
			this.removeLayer(layerId)
			let layer = new L.featureGroup()
			this.rootLayerGroup.addLayer(layer)
			this.layerList.push({
				id: layerId,
				layer: layer,
			})

			for (let i = 0; i < data.length; i++) {
				let marker = L.marker(data[i].latlon, {
					icon: L.divIcon({
						className: 'custom-div-icon',
						html: `
                    <div class="marker-zyzx" style="background-image: url('./imgs/poi/zyzx.png')"><div>${data[i].name}</div><div>${data[i].date}</div></div>
                  `,
						iconSize: [155, 145], // 设置图标尺寸
						iconAnchor: [30, 140], // 设置图标的锚点，使得图标正好在点的位置上
					}),
				}).on('click', () => {
					if (data[i]) {
						this.$emit('poiClick', layerId, data[i])
					}
				})
				layer.addLayer(marker)
				if (i < data.length - 1) {
					// 新样式
					let line = L.Plot.fineArrow([data[i].latlon, data[i + 1].latlon], {
						color: '#f00',
						tailWidthFactor: 0.02,
						neckWidthFactor: 0.05,
						headWidthFactor: 0.08,
					})
					layer.addLayer(line)
				}
			}
		},
		/**
		 * 地图缩放
		 */
		onMapZoomend() {
			this.map.on('zoomend', () => {
				var zoomLevel = this.map.getZoom()

				// this.removeLayer(this.currentLayerId)
				// this.drawSstsPoiMarker(bounds, this.currentLayerId) //调用4marker标点方法
			})
		},

		/**
		 * 绘制大数据量聚合点
		 * @param {聚合点数据 数组类型[{lon: '', lat: '', props: {相关属性值}}, ...]} data
		 * @param {打点图标} iconUrl
		 * @param {打点图标大小} iconSize
		 * @param {聚合样式} clusterStyle
		 */
		async drawSstsBigDataClusterPoint(data, iconUrl, iconSize, clusterStyle, layerId, multiIcon = false) {
			this.currentLayerId = layerId
			this.removeLayer(layerId)
			// if (this.showHeat) {
			// 	this.showHeat = false
			// await this.redrawGeoJSON()
			// }

			await this.clearCluster()
			// let defaultIcon = L.icon({
			// 	iconUrl: iconUrl,
			// 	iconSize: iconSize,
			// })
			this.markerSourceData[layerId] = data
			this.isClickGrid = false
			if (data.length == 0) {
				this.$store.commit('updateMapLoaded', true)
				return
			}
			let defaultIcon = L.icon({
				iconUrl: iconUrl,
				iconSize: iconSize,
			})

			let clusterComponents = []
			const data_ = data.filter((item) => {
				if (!item || !item.props.info.agmachId) {
					return false
				} else {
					return true
				}
			})
			for (let i = 0; i < data_.length; i++) {
				clusterComponents.push({
					type: 'Feature',
					geometry: {
						type: 'Point',
						coordinates: [Number(data_[i].latlng[1]), Number(data_[i].latlng[0])],
					},
					properties: {
						index: i,
						...data_[i],
					},
				})
			}
			clusterMarkers = L.geoJson(null, {
				pointToLayer: createClusterIcon,
			}).addTo(this.map)
			clusterIndex = new Supercluster({
				radius: 45,
				extent: 256,
				minZoom: 3,
			}).load(clusterComponents)

			var that = this
			clusterMarkers.on('click', function(e) {
				var clusterId = e.layer.feature.properties.cluster_id
				var center = e.latlng
				var expansionZoom
				if (clusterId) {
					expansionZoom = clusterIndex.getClusterExpansionZoom(clusterId)
					that.map.flyTo(center, expansionZoom)
				}
			})

			await this.updateCluster()
			this.$store.commit('updateMapLoaded', true)
			function createClusterIcon(feature, latlng) {
				if (!feature.properties.cluster) {
					const { props } = feature.properties
					const { info } = props
					return L.marker(latlng, {
						icon: getIcon(info),
					}).on('click', () => {
						console.log('click', feature.properties)
						that.$emit('poiClick', layerId, feature.properties)
					})
				}
				const count = feature.properties.point_count
				const size = count < 100 ? 'small' : count < 1000 ? 'medium' : 'large'

				const icon = L.divIcon({
					html:
						layerId == 'njfbType' && that.mapLevel == 3 ? `<div><span>${feature.properties.point_count_abbreviated}</span></div>` : '',
					className: layerId == 'njfbType' && that.mapLevel == 3 ? `marker-cluster marker-cluster-${size}` : '',
					iconSize: layerId == 'njfbType' && that.mapLevel == 3 ? L.point(40, 40) : L.point(0, 0),
				})
				return L.marker(latlng, { icon })
			}

			function getIcon(info) {
				if (!info.status || info.status == '当前在线') {
					return defaultIcon
				} else if (info.status == '离线') {
					return L.icon({
						// iconUrl: require('@/assets/mskx/icon_nj_b3.png'),
						iconUrl: require('@/assets/mskx/offline-icon.png'),
						iconSize: iconSize,
						iconAnchor: [5, 6],
					})
				} else if (info.status == '当日在线') {
					return L.icon({
						// iconUrl: require('@/assets/mskx/icon_nj_b2.png'),
						iconUrl: require('@/assets/mskx/current-day-online-icon.png'),

						iconSize: iconSize,
						iconAnchor: [5, 6],
					})
				} else {
					return L.icon({
						// iconUrl: require('@/assets/mskx/icon_nj_b2.png'),
						iconUrl: require('@/assets/mskx/current-online-icon.png'),

						iconSize: iconSize,
						iconAnchor: [5, 6],
					})
				}
			}
			return Promise.resolve()
		},
		/**
		 * 更新聚合点
		 */
		updateCluster() {
			const bounds = this.map.getBounds()
			const bbox = [bounds.getWest(), bounds.getSouth(), bounds.getEast(), bounds.getNorth()]
			let zoom = this.map.getZoom()
			console.log('zoom--==', zoom)

			// if (zoom === 5) zoom += 0.5
			let clusters = null
			if (clusterIndex) {
				clusters = clusterIndex.getClusters(bbox, zoom)
			}
			if (clusterMarkers) {
				clusterMarkers.clearLayers()
				clusterMarkers.addData(clusters)
			}

			clearTimeout(this.timer001)

			this.timer001 = setTimeout(() => {
				if (!this.isClickGrid) {
					// //给鼠标滚动添加防抖
					this.newZoomCluster = this.map.getZoom()

					if (
						(this.newZoomCluster > this.maxZoomCluster && this.oldZoomCluster < this.maxZoomCluster) ||
						(this.newZoomCluster < this.maxZoomCluster && this.oldZoomCluster > this.maxZoomCluster)
					) {
						this.oldZoomCluster = this.newZoomCluster
					}
				}
				// if (this.showQxyjLayer) {
				// 	this.loadWaterLayer('precipitationType', this.layerCount + 1, this.selectedRange, this.currentUnit)
				// }
			}, 500)
		},
		/**
		 * 清除聚合点
		 */
		clearCluster() {
			if (clusterMarkers) {
				clusterMarkers.clearLayers()
				clusterMarkers = null
			}
		},
		/**
		 * 绘制poi点
		 * @param {poi点数据 数组类型} data
		 * @param {图层id} layerId
		 * @param {是否聚合} cluster
		 * @param {是否跳转第一个点} flyFirstPoint
		 */
		async drawPoiMarker(data, layerId, cluster = false, flyFirstPoint = false, iconShow = false) {
			this.removeLayer(layerId)
			if (this.showHeat) {
				this.showHeat = false
				await this.redrawGeoJSON()
			}
			// 设置poi点图层
			let markerLayer
			if (cluster) {
				markerLayer = L.markerClusterGroup({
					showCoverageOnHover: false,
				})
			} else {
				markerLayer = L.featureGroup()
			}

			// 保存图层方便根据id删除
			this.layerList.push({
				id: layerId,
				layer: markerLayer,
			})
			// 处理点数据
			const markers = data
				.filter((it) => {
					if (it == null) {
						return false
					}
					return true
				})
				.map((it) => {
					// 初始化点 坐标、图标
					if (!iconShow) {
						const marker = L.marker(it.latlng, {
							icon: L.icon(it.icon),
						})
						// 绑定点击事件
						marker.on('click', () => {
							if (it.props) {
								// 可根据传入的props属性去判断点击的是哪类点
								this.$emit('poiClick', layerId, it)
							}
						})
						return marker
					} else {
						// 创建divIcon来作为标记图标，同时添加图片和文字标签
						const divIcon = L.divIcon({
							className: 'custom-div-icon',
							html: `
                    <div class="marker-icon" style="background-image: url('${it.icon.iconUrl}')"><div>${it.props.name}</div></div>
                  `,
							iconSize: [178, 69], // 设置图标尺寸
							iconAnchor: [89, 89], // 设置图标的锚点，使得图标正好在点的位置上
						})

						// 创建标记
						const marker = L.marker(it.latlng, { icon: divIcon })

						// 绑定点击事件
						marker.on('click', () => {
							if (it.props) {
								// 可根据传入的props属性去判断点击的是哪类点
								this.$emit('poiClick', layerId, it)
							}
						})
						return marker
					}
				})
			// 点图层添加点
			markers.forEach((p) => {
				markerLayer.addLayer(p)
			})
			// 将markerLayer添加到根图层
			this.rootLayerGroup.addLayer(markerLayer)
			// 视角跳转第一个点
			if (flyFirstPoint) {
				this.flyToPoint(markers[0].getLatLng(), 16)
			}
		},
		/**
		 * 绘制poi散点/农机位置点
		 * @param {poi点数据 数组类型} data
		 * @param {图层id} layerId
		 * @param {是否聚合} cluster
		 */
		async drawSstsPoiMarker(data, layerId, cluster = false) {
			if (!layerId) {
				this.$store.commit('updateMapLoaded', true)

				return
			}
			this.currentLayerId = layerId
			await this.clearCluster()
			await this.removeLayer(layerId)
			if (this.showHeat) {
				this.showHeat = false
				await this.redrawGeoJSON()
			}

			const that = this
			if (data.length == 0) {
				this.$store.commit('updateMapLoaded', true)

				return
			} else {
			}
			this.isClickGrid = false
			that.markerSourceData[layerId] = data
			let markerLayer

			// 设置poi点图层
			if (cluster) {
				markerLayer = L.markerClusterGroup({
					showCoverageOnHover: false,
				})
			} else {
				markerLayer = L.featureGroup()
			}

			// 处理点数据
			const markers = data
				.filter((it) => {
					if (it == null) {
						return false
					}
					return true
				})
				.map((it) => {
					const marker = L.marker(it.latlng, { icon: L.icon(it.icon) })

					// 绑定点击事件
					marker.on('click', () => {
						if (it.props) {
							// 可根据传入的props属性去判断点击的是哪类点
							that.$emit('poiClick', layerId, it)
						}
					})

					return marker
				})

			// 点图层添加点
			markers.forEach((p) => {
				markerLayer.addLayer(p)
			})
			// 保存图层方便根据id删除
			that.layerList.push({
				id: layerId,
				layer: markerLayer,
			})

			// 将markerLayer添加到根图层
			that.rootLayerGroup.addLayer(markerLayer)

			this.$store.commit('updateMapLoaded', true)

			return Promise.resolve()
		},
		/**
		 * 绘制信息框
		 */
		InformationMarker(list) {
			for (var i = 0; i < list.length; i++) {
				let shjd = list[i].shjd + '%'
				let marker1 = L.marker([list[i].lat, list[i].lng], {
					opacity: 0,
				})
					.addTo(this.map)
					.bindPopup(shjd, {
						autoClose: false,
						closeOnClick: false,
						offset: [0, 30],
					})
					.openPopup()
				marker1.off('click')
				this.marker1.push(marker1)
			}
		},
		/**
		 * 关闭信息框
		 */
		CloseMarker() {
			for (var i = 0; i < this.marker1.length; i++) {
				this.marker1[i].closePopup()
			}
		},
		/**
		 * 绘制poi点(自定义聚合样式)
		 * @param {poi点数据 数组类型} data
		 * @param {图层id} layerId
		 * @param {是否聚合} cluster
		 * @param {是否跳转第一个点} flyFirstPoint
		 * @param {自定义聚合样式} clusterStyle
		 */
		drawPoiMarkerCustomCluster(data, layerId, cluster = false, flyFirstPoint = false, iconShow = false, clusterStyle) {
			this.removeLayer(layerId)
			// 设置poi点图层
			let markerLayer
			if (cluster) {
				markerLayer = L.markerClusterGroup({
					iconCreateFunction: function(cluster) {
						var cnt = cluster.getChildCount()
						if (cnt >= 100) {
							return L.divIcon({
								html:
									`<div style="background-image: url(${clusterStyle.largeIconUrl}) "><span>` +
									cluster.getChildCount() +
									'</span></div>',
								className: 'custom-cluster custom-cluster-large',
								iconSize: new L.Point(50, 50),
							})
						} else if (cnt >= 10 && cnt < 100) {
							return L.divIcon({
								html:
									`<div style="background-image: url(${clusterStyle.mediumIconUrl}) "><span>` +
									cluster.getChildCount() +
									'</span></div>',
								className: 'custom-cluster custom-cluster-medium',
								iconSize: new L.Point(40, 40),
							})
						} else {
							return L.divIcon({
								html:
									`<div style="background-image: url(${clusterStyle.smallIconUrl}) "><span>` +
									cluster.getChildCount() +
									'</span></div>',
								className: 'custom-cluster custom-cluster-small',
								iconSize: new L.Point(30, 30),

								// html:
								// 	`<div style="background-image: url(${clusterStyle.mediumIconUrl}) "><span>` +
								// 	cluster.getChildCount() +
								// 	'</span></div>',
								// className: 'custom-cluster custom-cluster-medium',
								// iconSize: new L.Point(40, 40),
							})
						}
					},
					showCoverageOnHover: false,
				})
			} else {
				markerLayer = L.featureGroup()
			}

			// 保存图层方便根据id删除
			this.layerList.push({
				id: layerId,
				layer: markerLayer,
			})
			// 处理点数据
			const markers = data
				.filter((it) => {
					if (it == null) {
						return false
					}
					return true
				})
				.map((it) => {
					// 初始化点 坐标、图标
					if (!iconShow) {
						const marker = L.marker(it.latlng, {
							icon: L.icon(it.icon),
						})
						// 绑定点击事件
						marker.on('click', () => {
							if (it.props) {
								// 可根据传入的props属性去判断点击的是哪类点
								this.$emit('poiClick', layerId, it)
							}
						})
						return marker
					} else {
						// 创建divIcon来作为标记图标，同时添加图片和文字标签
						const divIcon = L.divIcon({
							className: 'custom-div-icon',
							html: `
                    <div class="marker-icon" style="background-image: url('${it.icon.iconUrl}')"><div>${it.props.name}</div></div>
                  `,
							iconSize: [178, 69], // 设置图标尺寸
							iconAnchor: [89, 89], // 设置图标的锚点，使得图标正好在点的位置上
						})

						// 创建标记
						const marker = L.marker(it.latlng, { icon: divIcon })

						// 绑定点击事件
						marker.on('click', () => {
							if (it.props) {
								// 可根据传入的props属性去判断点击的是哪类点
								this.$emit('poiClick', layerId, it)
							}
						})
						return marker
					}
				})
			// 点图层添加点
			markers.forEach((p) => {
				markerLayer.addLayer(p)
			})
			// 将markerLayer添加到根图层
			this.rootLayerGroup.addLayer(markerLayer)
			// 视角跳转第一个点
			if (flyFirstPoint) {
				this.flyToPoint(markers[0].getLatLng(), 16)
			}

			this.$store.commit('updateMapLoaded', true)
		},
		/**
		 * 绘制数字点位
		 * @param {*} data
		 * @param {*} layerId
		 */
		drawNumberMarker(data, layerId) {
			this.removeLayer(layerId)
			let numMarkerLayer = L.featureGroup()
			this.layerList.push({
				id: layerId,
				layer: numMarkerLayer,
			})
			const numMarkers = data.map((it) => {
				var customIcon = L.divIcon({
					className: 'custom-icon',
					html: `<div class="number-marker-out"><div class="number-marker-in">${it.props.num}</div></div>`,
					iconSize: [50, 50],
				})
				const marker = L.marker(it.latlng, { icon: customIcon })
				marker.on('click', () => {
					if (it.props) {
						// 可根据传入的props属性去判断点击的是哪类点
						// console.log('数字点位', it.props)
						// this.$emit('numMarkerClick', layerId, it)
						this.$emit('poiClick', layerId, it)
					}
				})
				return marker
			})
			numMarkers.forEach((p) => {
				numMarkerLayer.addLayer(p)
			})
			this.rootLayerGroup.addLayer(numMarkerLayer)
		},

		/**
		 * 使用各省份数字数据绘制数字点位
		 * @param {*} data
		 * @param {*} layerId
		 */
		async drawSstsNumberMarker(data, layerId) {
			if (!layerId) {
				this.$store.commit('updateMapLoaded', true)
				return
			}
			this.currentLayerId = layerId
			const that = this
			that.removeLayer(layerId)
			if (this.showHeat) {
				this.showHeat = false
				await this.redrawGeoJSON()
			}

			await this.clearCluster()
			that.markerSourceData[layerId] = data
			that.sstsMarkers[layerId] = [] // 存放海量点的数组
			if (data.length == 0) {
				this.$store.commit('updateMapLoaded', true)
				return
			} else {
			}
			that.isClickGrid = false
			let numMarkerLayer = L.featureGroup()

			// data.map((point) => {
			// 	var myIcon1 = L.divIcon({
			// 		className: 'custom-icon',
			// 		iconUrl: `<div class="number-marker-out"><div class="number-marker-in">${point.props.num}</div></div>`,
			// 		// html: `<div class="number-marker-out"><div class="number-marker-in">${point.props.num}</div></div>`,
			// 		iconSize: [50, 50],
			// 	})
			// 	let market = L.marker(point.latlng, { icon: myIcon1, data: point })

			// 	that.sstsMarkers[layerId].push(market)
			// })
			// that.canvasIconLayer_.addLayers(that.sstsMarkers[layerId]) //海量点标点
			// that.layerList.push({
			// 	id: layerId,
			// 	type: 'canvasIconLayer',
			// 	layer: that.sstsMarkers[layerId],
			// })
			let numMarkers = []
			data.map((it) => {
				var customIcon = L.divIcon({
					className: 'custom-icon',
					html: `<div class="number-marker-out"><div class="number-marker-in">${it.props.num}</div></div>`,
					// iconSize: [50, 50],
					iconSize: [58, 53],
					iconAnchor: [28, 46],
				})
				const marker = L.marker(it.latlng, { icon: customIcon })
				marker.on('click', () => {
					if (it.props) {
						// 可根据传入的props属性去判断点击的是哪类点
						//点击了数字点位
						that.gridClick(it.props)
					}
				})
				if (it.props.num !== 0) {
					numMarkers.push(marker)
				}
			})
			numMarkers.forEach((p) => {
				numMarkerLayer.addLayer(p)
			})
			that.rootLayerGroup.addLayer(numMarkerLayer)
			that.layerList.push({
				id: layerId,
				layer: numMarkerLayer,
			})

			this.$store.commit('updateMapLoaded', true)
			return Promise.resolve()
		},

		//清除多边形
		async deleteDrawPolygon() {
			if (this.polygon) {
				this.map.removeLayer(this.polygon)
				this.polygonPoints = []
				this.$emit('polygonResult', this.polygonPoints)
			}
			this.canGridClick = true
			await this.redrawGeoJSON()
		},

		/**
		 * 绘制多边形
		 * @param {*} data
		 * @param {*} layerId
		 * @param {*} iconShow
		 * @param {*} flyFirstPoint
		 */
		async drawPolygon() {
			this.canGridClick = false
			await this.redrawGeoJSON()

			this.polygonPoints = []

			this.polygon = L.polygon(this.polygonPoints, { color: 'yellow', fillColor: 'yellow', fillOpacity: 0.5 }).addTo(this.map)

			this.map.on('mousedown', (e) => {
				this.polygonPoints.push([e.latlng.lat, e.latlng.lng])

				this.map.on('mousemove', (event) => {
					this.polygon.setLatLngs([...this.polygonPoints, [event.latlng.lat, event.latlng.lng]])
				})
			})

			this.map.on('dblclick', () => {
				this.map.off('mousedown')
				this.map.off('mousemove')
				this.$emit('polygonResult', this.polygonPoints)
			})
		},

		/**
		 * 根据图层id移除图层
		 * @param {*} layerId
		 */
		removeLayer(layerId) {
			const layer = this.layerList.find((e) => e.id == layerId)
			if (layer) {
				if (!layer.type) {
					this.rootLayerGroup.removeLayer(layer.layer)
					this.layerList = this.layerList.filter((e) => e.id != layerId)
				} else if (layer.type == 'canvasIconLayer') {
					this.layerList = this.layerList.filter((e) => e.id != layerId)
					if (!this.sstsMarkers[layerId]) {
					} else {
						//在标点前，先清空已有标点。
						if (this.sstsMarkers[layerId].length > 0) {
							this.canvasIconLayer_.removeLayers(this.sstsMarkers[layerId])
						}
					}
					// this.rootLayerGroup.clearLayers()
				}
			}
		},
		/**
		 * 移除所有图层
		 */
		async clearLayers() {
			if (this.showHeat) {
				this.showHeat = false
				await this.redrawGeoJSON()
			}

			this.rootLayerGroup.clearLayers()
			this.layerList = []
			if (this.trackLayer) {
				this.trackLayer.clearLayers()
			}

			await this.clearCluster()
			this.removeDaynamicCicle()
			this.removeLayer('trcak')
			this.removeLayer(`precipitationType${this.layerCount}`)
			//清除散点
			if (this.sstsMarkers[this.currentLayerId]) {
				if (this.sstsMarkers[this.currentLayerId].length > 0) {
					this.canvasIconLayer_.removeLayers(this.sstsMarkers[this.currentLayerId])
				}
			}

			this.sstsMarkers[this.currentLayerId] = []
			this.markerSourceData[this.currentLayerId] = []
			this.removeCicleList()
			this.$emit('qctc', false)
		},
		/**
		 * @description: 生成geojson
		 * @param {*} coordinates
		 * @param {*} properties
		 * @return {*}
		 */
		setGeojson(coordinates, properties) {
			// coordinates = coordinates.map(e => {
			// });
			var geojson = {
				type: 'FeatureCollection',
				features: [
					{
						type: 'Feature',
						geometry: {
							type: 'MultiPolygon',
							coordinates: coordinates,
						},
						properties: properties,
					},
				],
			}
			return geojson
		},
		backLayers() {
			if (this.$store.state.mapLoaded) {
				clearTimeout(this.timerLoadGeoJsonLayer)
				this.timerLoadGeoJsonLayer = setTimeout(async () => {
					this.$store.commit('updateMapLoaded', false)
					const lastAreaCode_ = [...this.lastAreaCode]
					if (this.lastAreaCode.length > 2) {
						if (this.currentMapLevel == 'district') {
							this.currentMapLevel = 'city'
						} else if (this.currentMapLevel == 'city') {
							this.currentMapLevel = 'province'
						} else if (this.currentMapLevel == 'province') {
							this.currentMapLevel = ''
						}

						this.isChangeMapBg = true

						if (this.lastAreaCode[3] && this.lastAreaCode[2] !== this.lastAreaCode[3]) {
							this.lastAreaCode.splice(3)
						} else {
							this.lastAreaCode.splice(2)
						}
						console.log('this.lastAreaCode', this.lastAreaCode)
						await this.loadGeoJsonLayerFromUrl(
							DataVUrl + this.lastAreaCode[this.lastAreaCode.length - 1] + '_full.json',
							'geojson',
						).then(() => {
							this.$emit('operate', lastAreaCode_[lastAreaCode_.length - 2], lastAreaCode_[lastAreaCode_.length - 1])
						})
					} else if (this.lastAreaCode.length == 2) {
						if (this.currentMapLevel == 'district') {
							this.currentMapLevel = 'city'
						} else if (this.currentMapLevel == 'city') {
							this.currentMapLevel = 'province'
						} else if (this.currentMapLevel == 'province') {
							this.currentMapLevel = ''
						}

						this.isChangeMapBg = true
						this.lastAreaCode.splice(this.lastAreaCode.length - 1, 1)

						this.loadGeoJsonLayerFromUrl(
							DataVUrl + this.lastAreaCode[this.lastAreaCode.length - 1] + '_full.json',
							'geojson',
							true,
						).then(async () => {
							this.map.fitBounds(this.geojsonLayer.getBounds())

							await this.$emit('operate', lastAreaCode_[lastAreaCode_.length - 2], lastAreaCode_[lastAreaCode_.length - 1])
						})
					} else {
						this.isChangeMapBg = false
						this.removeLayer('trcak')
						this.$message.warning('当前已经是最高一级啦！')
						this.$store.commit('updateMapLoaded', true)
					}
				}, 500)

				// this.$store.commit('updateMapLoaded', false)
			} else {
				this.$message.warning('请等待地图加载完成！')
			}
		},
		async loadGeoJsonLayerFromUrl(dataUrl, layerId, isChina = false) {
			const response = await fetch(dataUrl)
			if (response.status == 200) {
				let data = await response.json()
				// gcoord.transform(data, gcoord.GCJ02, gcoord.WGS84)
				console.log('this.showQxyjLayer', this.showQxyjLayer, 'this.isChangeMapBg', this.isChangeMapBg)
				if (this.showQxyjLayer) {
					// if (this.imageMaskLayer) {
					// 	await this.map.removeLayer(this.imageMaskLayer)
					// }
				} else {
					if (this.isChangeMapBg) {
						this.loadImageMaskLayer(data, './imgs/map-theme1-bg-1.png')
					} else {
						// if (this.imageMaskLayer) {
						// 	await this.map.removeLayer(this.imageMaskLayer)
						// }
					}
				}

				if (this.geojsonLayer) this.map.removeLayer(this.geojsonLayer)
				//设置地图的背景图
				this.geojsonLayer = L.geoJSON(data, {
					style: (feature) => {
						if (this.moduleType == 'zyjd' && this.showQxyjLayer) {
							let style_ = {
								color: '#fff', // 边框颜色
								weight: 2, // 边框粗细
								opacity: 1, // 透明度
								// fillColor: '#269CA6', // 区域填充颜色
								fillColor: '#fff',
								fillOpacity: 0, // 区域填充颜色的透明
								dashArray: 'dashed',
							}
							return style_
						} else {
							if (this.mapTheme) {
								const { properties } = feature
								const { adcode } = properties
								const adcodeStart_ = String(adcode)[0] || null
								let style_ = {
									color: '#FFFFFF', // 边框颜色
									weight: 2, // 边框粗细
									opacity: 1, // 透明度
									// fillColor: '#269CA6', // 区域填充颜色
									fillColor: '#75CBC6',
									fillOpacity: this.showHeat ? 0.3 : 0.6, // 区域填充颜色的透明
									dashArray: 'dashed',
								}
								if (adcode == 100000 || adcode === '100000') {
									style_.weight = 5
								}

								style_.fillColor = '#188CF8'
								return style_
							}
							return {
								// color: 'transparent', // 隐藏边框颜色
								// weight: 0, // 边框宽度为0
								// // fillOpacity: 0, // 填充色透明度为0
								// zIndexOffset: 500,

								color: '#FFFFFF', // 边框颜色
								weight: 2, // 边框粗细
								opacity: 1, // 透明度
								fillColor: '#269CA6', // 区域填充颜色
								fillOpacity: 0, // 区域填充颜色的透明
								dashArray: 'dashed',
							}
						}
					},
					pane: 'overlayPane', //控制层级顺序

					onEachFeature: (feature, layer) => {
						layer.on({
							click: () => {
								// alert('行政编码：' + feature.properties.adcode)
								if (this.xzpd) {
									this.gridClick(feature.properties)
								}
							},
							mouseover: () => {
								console.log('feature.properties--==', feature)
								this.gridMouseover(layer)
							},
							mouseout: () => {
								this.gridMouseout(layer)
							},
						})
					},
				}).addTo(this.map)
				// // 添加到图层控制
				// var baseLayers = {
				// 	OpenStreetMap: osm,
				// 	Heatmap: heat,
				// }

				// var overlays = {
				// 	GeoJSON: this.geojsonLayer,
				// }

				// L.control.layers(baseLayers, overlays).addTo(this.map)

				this.bounds = this.getBBox(this.geojsonLayer.getBounds())
				console.log('isChina--==', isChina)
				console.log('this.geojsonLayer.getBounds()--==', this.geojsonLayer)

				if (!isChina) {
					this.$nextTick(() => {
						// if (this.mapLevel == 3) {
						// 	this.map.fitBounds(this.geojsonLayer.getBounds())
						// 	this.setZoom()
						// } else {
						// 	if (this.lastAreaCode[this.lastAreaCode.length - 1] == '460000') {
						// 		// [19.32748524, 109.4041358]
						// 		this.map.setView([18.82748524, 109.4041358], 7)
						// 	} else {
						// 		this.map.fitBounds(this.geojsonLayer.getBounds())
						// 		console.log('this.CurrentZoom--==', this.map.getZoom())
						// 	}
						// }

						if (this.lastAreaCode[this.lastAreaCode.length - 1] == '460000') {
							// [19.32748524, 109.4041358]
							this.map.setView([18.82748524, 109.4041358], 7)
						} else {
							this.map.fitBounds(this.geojsonLayer.getBounds())
							console.log('this.CurrentZoom--==', this.map.getZoom())
						}
					})
				}
				this.currentGeoData = data
				this.isChangeMapBg = false
				this.currentDataUrl = dataUrl
			} else {
				console.error('无geojson数据！！！')
			}
			this.$store.commit('updateMapLoaded', true)
			return Promise.resolve()
		},
		getRegionCodeBbox(data) {
			let lngLats = []
			// if (data.features && data.features.length > 0) {
			// 	/**
			// 	 * 方法1
			// 	 */
			// 	// 2. 获取行政区划的边界多边形数据
			// 	data.features.forEach((item) => {
			// 		const temp = item.geometry.coordinates[0]
			// 		if (temp.length > 1) {
			// 			lngLats.push(...temp)
			// 		} else {
			// 			lngLats.push(...temp[0])
			// 		}
			// 	})
			// 	if (lngLats.length === 0) {
			// 		throw new Error('该行政区划无边界数据')
			// 	}
			// 	// 3. 解析多边形坐标（天地图返回的是"经度,纬度 经度,纬度 ..."格式）
			// 	const points = lngLats.map((lngLatItem) => {
			// 		const lng = lngLatItem[0]
			// 		const lat = lngLatItem[1]
			// 		return {
			// 			lng: parseFloat(lng),
			// 			lat: parseFloat(lat),
			// 		}
			// 	})
			// 	// 4. 计算最小/最大经纬度
			// 	const lngs = points.map((p) => p.lng)
			// 	const lats = points.map((p) => p.lat)
			// 	return {
			// 		minLng: Math.min(...lngs),
			// 		minLat: Math.min(...lats),
			// 		maxLng: Math.max(...lngs),
			// 		maxLat: Math.max(...lats),
			// 	}
			// 	/**
			// 	 * 方法2
			// 	 */
			// 	// // 获取当前地图视图的边界范围
			// 	// var bounds = this.map.getBounds()
			// 	// // 提取最小纬度、最小经度（西南角）
			// 	// var southWest = bounds.getSouthWest() // { lat: 最小纬度, lng: 最小经度 }
			// 	// var minLat = southWest.lat
			// 	// var minLng = southWest.lng
			// 	// // 提取最大纬度、最大经度（东北角）
			// 	// var northEast = bounds.getNorthEast() // { lat: 最大纬度, lng: 最大经度 }
			// 	// var maxLat = northEast.lat
			// 	// var maxLng = northEast.lng
			// 	// return {
			// 	// 	minLng,
			// 	// 	minLat,
			// 	// 	maxLng,
			// 	// 	maxLat,
			// 	// }

			// 	/**
			// 	 * 方法3
			// 	 */

			// 	// let bounds_ = this.map.getBounds()
			// 	// const { _northEast, _southWest } = bounds_
			// 	// console.log('bounds_--==', bounds_);

			// 	// return {
			// 	// 	minLng: _southWest.lng,
			// 	// 	minLat: _southWest.lat,
			// 	// 	maxLng: _northEast.lng,
			// 	// 	maxLat: _northEast.lat,
			// 	// }
			// } else {
			// 	throw new Error(data.message || '未找到行政区划数据')
			// }

			let bounds_ = this.map.getBounds()
			const { _northEast, _southWest } = bounds_
			console.log('bounds_--==', bounds_)

			return {
				minLng: _southWest.lng,
				minLat: _southWest.lat,
				maxLng: _northEast.lng,
				maxLat: _northEast.lat,
			}
		},

		gridMouseover(layer) {
			if (this.canGridClick) {
				if (this.moduleType == 'zyjd' && this.showQxyjLayer) {
					layer.setStyle({
						color: '#C2DBDC',
						weight: 3,
						fillColor: '#fff',
						// color:'linear-gradient(to bottom, #47CFB8, #2DA0A8);',
						fillOpacity: 0.5,
					})
				} else {
					if (this.mapTheme) {
						layer.setStyle({
							color: '#C2DBDC',
							weight: 3,
							fillColor: '#45CEB8',
							// color:'linear-gradient(to bottom, #47CFB8, #2DA0A8);',
							fillOpacity: 0.5,
						})
					} else {
						layer.setStyle({
							// color: '#C2DBDC',
							// weight: 3,

							color: '#007FFF',
							fillOpacity: 0.1,
							weight: 2,
							// fillColor: '#45CEB8',
							// color:'linear-gradient(to bottom, #47CFB8, #2DA0A8);',
							// fillOpacity: 1,
						})
					}
				}

				if (!L.Browser.ie && !L.Browser.opera && !L.Browser.edge) {
					layer.bringToFront()
				}
			}
		},
		gridMouseout(layer) {
			if (this.canGridClick) {
				this.geojsonLayer.resetStyle(layer)
			}
		},

		async loadGeoJsonByHeatDataLayerFromUrl(dataUrl, layerId, isChina = false, heatData) {
			if (heatData.length == 0) {
				return
			}
			const response = await fetch(dataUrl)

			const heatDescData = generateDescList(heatData)

			if (response.status == 200) {
				let data = await response.json()
				gcoord.transform(data, gcoord.GCJ02, gcoord.WGS84)
				if (this.isChangeMapBg) {
					// this.loadImageMaskLayer(data, './imgs/map-theme1-bg-1.png')
				} else {
				}

				if (this.geojsonLayer) this.map.removeLayer(this.geojsonLayer)

				//设置地图的背景图
				this.geojsonLayer = L.geoJSON(data, {
					style: (feature) => {
						// if (this.mapTheme) {
						const { properties } = feature
						const { adcode } = properties
						const adcodeStart_ = String(adcode)[0] || null

						let style_ = {
							color: '#FFFFFF', // 边框颜色
							weight: 2, // 边框粗细
							opacity: this.mapTheme ? 1 : 0, // 透明度
							// fillColor: '#269CA6', // 区域填充颜色
							fillColor: '#188CF8',
							// fillOpacity: this.mapTheme ? 0.6 : 0, // 区域填充颜色的透明
							fillOpacity: this.mapTheme ? 0.5 : 0, // 区域填充颜色的透明

							dashArray: 'dashed',
						}

						// '0~0.2': '#f83d09',
						// '0.2~0.4': '#c94e2c',
						// '0.4~0.6': '#ff0',
						// '0.6~0.8': '#0f0',
						// '0.8~1': '#0ff',

						if (isInPlantRegion(adcode)) {
							const adcodeIndex = heatDescData.findIndex((item) => item.areaId == adcode)
							const adcodeIndexPercent = ((adcodeIndex + 1) / heatDescData.length).toFixed(2)
							if (heatDescData.length > 1) {
								if (adcodeIndexPercent > 0 && adcodeIndexPercent <= 0.2) {
									style_.fillColor = '#135200'
									if (this.mapTheme) {
									} else {
										style_.fillOpacity = 0.5
									}
								} else if (adcodeIndexPercent > 0.2 && adcodeIndexPercent <= 0.4) {
									style_.fillColor = '#389E0D'
									if (this.mapTheme) {
									} else {
										style_.fillOpacity = 0.5
									}
								} else if (adcodeIndexPercent > 0.4 && adcodeIndexPercent <= 0.6) {
									style_.fillColor = '#73D13D'
									if (this.mapTheme) {
									} else {
										style_.fillOpacity = 0.5
									}
								} else if (adcodeIndexPercent > 0.6 && adcodeIndexPercent <= 0.8) {
									style_.fillColor = '#B7EB8F'
									if (this.mapTheme) {
									} else {
										style_.fillOpacity = 0.5
									}
								} else if (adcodeIndexPercent > 0.8) {
									style_.fillColor = '#F6FFED'
									if (this.mapTheme) {
									} else {
										style_.fillOpacity = 0.5
									}
								}
							} else if (heatDescData.length > 0) {
								style_.fillColor = '#135200'
								if (this.mapTheme) {
								} else {
									style_.fillOpacity = 0.5
								}
							}

							// if (adcodeIndexPercent > 0 && adcodeIndexPercent <= 0.2) {
							// 	style_.fillColor = '#f83d09'
							// 	if (this.mapTheme) {
							// 	} else {
							// 		style_.fillOpacity = 0.6
							// 	}
							// } else if (adcodeIndexPercent > 0.2 && adcodeIndexPercent <= 0.4) {
							// 	style_.fillColor = '#c94e2c'
							// 	if (this.mapTheme) {
							// 	} else {
							// 		style_.fillOpacity = 0.6
							// 	}
							// } else if (adcodeIndexPercent > 0.4 && adcodeIndexPercent <= 0.6) {
							// 	style_.fillColor = '#ff0'
							// 	if (this.mapTheme) {
							// 	} else {
							// 		style_.fillOpacity = 0.6
							// 	}
							// } else if (adcodeIndexPercent > 0.6 && adcodeIndexPercent <= 0.8) {
							// 	style_.fillColor = '#0f0'
							// 	if (this.mapTheme) {
							// 	} else {
							// 		style_.fillOpacity = 0.6
							// 	}
							// } else if (adcodeIndexPercent > 0.8) {
							// 	style_.fillColor = '#0ff'
							// 	if (this.mapTheme) {
							// 	} else {
							// 		style_.fillOpacity = 0.6
							// 	}
							// }
						}

						if (adcode == 100000 || adcode === '100000') {
							style_.weight = 5
						}
						return style_
						// }
						// return {
						// 	color: 'transparent', // 隐藏边框颜色
						// 	weight: 0, // 边框宽度为0
						// 	// fillOpacity: 0, // 填充色透明度为0
						// 	zIndexOffset: 500,
						// }
					},
					pane: 'overlayPane', //控制层级顺序

					onEachFeature: (feature, layer) => {
						layer.on({
							click: () => {
								// alert('行政编码：' + feature.properties.adcode)
								if (this.xzpd) {
									this.gridClick(feature.properties)
								}
							},
							mouseover: () => {
								this.getGridMouseover(layer)
							},
							mouseout: () => {
								this.geojsonLayer.resetStyle(layer)
							},
						})
					},
				}).addTo(this.map)

				// // 添加到图层控制
				// var baseLayers = {
				// 	OpenStreetMap: osm,
				// 	Heatmap: heat,
				// }

				// var overlays = {
				// 	GeoJSON: this.geojsonLayer,
				// }

				// L.control.layers(baseLayers, overlays).addTo(this.map)

				this.bounds = this.getBBox(this.geojsonLayer.getBounds())
				if (!isChina) {
					this.map.fitBounds(this.geojsonLayer.getBounds())
					// if (this.lastAreaCode.length == 0 || this.lastAreaCode[this.lastAreaCode.length - 1] == 100000) {
					// 	this.map.setView(this.mapOptions.center, this.mapOptions.zoom)
					// }
				}
				this.isChangeMapBg = false
			} else {
				console.error('无geojson数据！！！')
			}

			function generateDescList(list) {
				// 使用sort()方法按sum属性值降序排列
				return list.sort((a, b) => b.total - a.total)
			}

			function isInPlantRegion(acode) {
				// 遍历列表，判断是否有对象的areaId与x相等
				for (let i = 0; i < heatData.length; i++) {
					if (heatData[i].areaId == acode) {
						return true // 如果找到匹配的，返回true
					}
				}
				return false // 如果没有匹配的，返回false
			}
			this.$store.commit('updateMapLoaded', true)
			return Promise.resolve()
		},

		loadJiangShuiData() {
			// const jiangShuiData_ = jiangShuiJsonData.variables
			// console.log('jiangShuiData_--==', jiangShuiData_);
			// const { lon, lat, Total_precipitation_surface_24_Hour_Accumulation } = jiangShuiData_
			// console.log('lon--==', lon)
			// console.log('lat--==', lat)
			// console.log('降水量数据--==', Total_precipitation_surface_24_Hour_Accumulation.data)
		},

		getGridMouseover(layer) {
			if (this.canGridClick) {
				if (this.mapTheme) {
					layer.setStyle({
						color: '#C2DBDC',
						weight: 3,
						fillColor: '#45CEB8',
						// color:'linear-gradient(to bottom, #47CFB8, #2DA0A8);',
						fillOpacity: 0.5,
					})
				} else {
					layer.setStyle({
						// color: '#C2DBDC',
						// weight: 3,

						color: '#007FFF',
						fillOpacity: 0.3,
						weight: 2,
						// fillColor: '#45CEB8',
						// color:'linear-gradient(to bottom, #47CFB8, #2DA0A8);',
						// fillOpacity: 1,
					})
				}

				if (!L.Browser.ie && !L.Browser.opera && !L.Browser.edge) {
					layer.bringToFront()
				}
			}
		},

		async switchMapType(mapLevel) {
			this.mapLevel = mapLevel
			this.mapTheme = !this.mapTheme
			if (!this.mapTheme) {
				// this.loadTdtXYZ() //卫星图
			}

			clearTimeout(this.timerLoadGeoJsonLayer)
			this.timerLoadGeoJsonLayer = setTimeout(async () => {
				if (mapLevel == 3) {
					await this.loadGeoJsonLayerFromUrl(DataVUrl + this.lastAreaCode[this.lastAreaCode.length - 1] + '.json', 'geojson')
				} else {
					if (this.lastAreaCode.length == 0) {
						await this.loadGeoJsonLayerFromUrl(DataVUrl + '100000_full.json', 'geojson', true)
					} else {
						if (this.lastAreaCode[this.lastAreaCode.length - 1] == '100000') {
							await this.loadGeoJsonLayerFromUrl(
								DataVUrl + this.lastAreaCode[this.lastAreaCode.length - 1] + '_full.json',
								'geojson',
								true,
							)
						} else {
							await this.loadGeoJsonLayerFromUrl(DataVUrl + this.lastAreaCode[this.lastAreaCode.length - 1] + '_full.json', 'geojson')
						}
					}
				}
			})
		},

		async redrawGeoJSON() {
			if (this.mapLevel == 3) {
				await this.loadGeoJsonLayerFromUrl(DataVUrl + this.lastAreaCode[this.lastAreaCode.length - 1] + '.json', 'geojson')
			} else {
				if (this.lastAreaCode.length == 0) {
					await this.loadGeoJsonLayerFromUrl(DataVUrl + '100000_full.json', 'geojson', true)
				} else {
					if (this.lastAreaCode[this.lastAreaCode.length - 1] == '100000') {
						await this.loadGeoJsonLayerFromUrl(
							DataVUrl + this.lastAreaCode[this.lastAreaCode.length - 1] + '_full.json',
							'geojson',
							true,
						)
					} else {
						await this.loadGeoJsonLayerFromUrl(DataVUrl + this.lastAreaCode[this.lastAreaCode.length - 1] + '_full.json', 'geojson')
					}
				}
			}
		},

		gridClick(data) {
			if (this.canGridClick) {
				if (this.$store.state.mapLoaded) {
					if (this.lastAreaCode.indexOf(data.adcode) == -1) {
						this.lastAreaCode.push(data.adcode)
					}
					if (this.map.getZoom() >= 11) {
					} else {
						console.log('this.currentMapLevel--==', this.currentMapLevel)

						if (this.currentMapLevel != 'district') {
							this.isChangeMapBg = true
							this.$store.commit('updateMapLoaded', false)

							clearTimeout(this.timerLoadGeoJsonLayer)
							this.timerLoadGeoJsonLayer = setTimeout(() => {
								this.currentMapLevel = data.level
								if (data.level == 'district') {
									console.log('this.isChangeMapBg--==', this.isChangeMapBg)

									this.loadGeoJsonLayerFromUrl(DataVUrl + data.adcode + '.json', 'geojson').then(() => {
										this.isClickGrid = true
										this.$emit('gridClick', data)
									})
								} else {
									this.loadGeoJsonLayerFromUrl(DataVUrl + data.adcode + '_full.json', 'geojson').then(() => {
										this.isClickGrid = true
										this.$emit('gridClick', data)
									})
								}
							}, 500)
						}
					}
				} else {
					this.$message.warning('请等待地图加载完成！')
				}
			}
		},

		loadMaskLayer(data) {
			if (this.maskLayer) this.map.removeLayer(this.maskLayer)
			this.maskLayer = L.mask(data, {
				map: this.map,
				// color: '#f10909',
				// weight: 8,
				fillColor: '#fff',
				fillOpacity: 1,
			}).addTo(this.map)
		},
		async loadImageMaskLayer(data, imgUrl) {
			if (this.imageMaskLayer) {
				await this.map.removeLayer(this.imageMaskLayer)
			}

			const geojson = L.geoJSON(data)
			this.imageMaskLayer = L.imageMask(imgUrl, {
				padding: 1,
				polygons: geojson.getLayers(),
				mode: 'clip', //clip:指定区域不显示背景图,show:指定区域显示背景图
			}).addTo(this.map)
		},
		/**
		 * 定位点
		 * @param {坐标点} position
		 * @param {层级} zoom
		 */
		flyToPoint(position, zoom) {
			this.map.flyTo(position, zoom)
		},
		/**
		 * @description: 获取边界坐标
		 * @return {*}
		 */
		getBBox(bounds) {
			return [bounds._southWest.lng, bounds._southWest.lat, bounds._northEast.lng, bounds._northEast.lat]
		},
		/**
		 * @description: 生成随机点
		 * @param {*} layerId 图层id
		 * @param {*} number 返回随机点数
		 * @param {*} img 点图标 require('@/assets/img/map/jz/jz.png')
		 * @return {*}
		 */
		randomPoint(layerId, number, img, size) {
			if (this.bounds.length === 0) {
				// alert('请选择区域范围');
				return false
			}
			var points = turf.randomPoint(number, { bbox: this.bounds })
			var features = points.features.map((e, index) => {
				let coordinates = e.geometry.coordinates.reverse()
				// var latLng = L.CRS.EPSG3857.unproject(L.point(coordinates));
				let pointInfo = {
					latlng: coordinates,
					icon: {
						iconUrl: img,
						iconSize: size,
					},
					props: {
						name: '某某poi点',
						type: layerId,
						id: index,
					},
				}
				return pointInfo
			})
			this.drawPoiMarkerCustomCluster(features, layerId, true, false, false, {
				largeIconUrl: './imgs/cluster/large-icon.png',
				mediumIconUrl: './imgs/cluster/medium-icon.png',
				smallIconUrl: './imgs/cluster/small-icon.png',
			})

			// 单个点位
			// let features = {
			//   icon: {
			//     iconUrl: 'img/people.7eff8f7f.png',
			//     iconSize: [47, 90]
			//   },
			//   latlng: [31.80757463704106, 119.21717254828414],
			//   props: {
			//     name: '某某poi点',
			//     type: 'people',
			//     id: 1
			//   }
			// }
			// this.drawPoiMarker([features], layerId, true)
		},
		/**
		 * @description: 加载轨迹图层
		 * @param {*} layerId
		 * @param {*} data
		 * @return {*}
		 */
		async loadTrackLayer(layerId, data) {
			this.removeLayer(layerId)
			this.map.setView([data[0][1], data[0][0]], 16)
			const lineLayer = L.layerGroup()
			this.rootLayerGroup.addLayer(lineLayer)
			this.layerList.push({
				id: layerId,
				layer: lineLayer,
			})
			const latents = data.map((e) => {
				if (isNaN(e[0]) || isNaN(e[1])) {
				} else {
					return [e[1], e[0]]
				}
			})
			//修改轨迹线
			const routeLine = L.polyline(latents, {
				weight: 20,
				color: 'rgb(6,229,255)',
				opacity: 0.6,
			})
			lineLayer.addLayer(routeLine)
			const realRouteLine = L.polyline([], {
				weight: 5,
				color: '#C9F928',
			})
			lineLayer.addLayer(realRouteLine)

			const carIcon = L.icon({
				// iconSize: [37, 26],
				iconSize: [24, 31],
				iconAnchor: [0, 0],
				iconUrl: './imgs/car.png',
				// iconUrl:require('@/assets/mskx/icon_nj_b.png')
			})
			// 动态marker
			let animatedMarker = L.animatedMarker(routeLine.getLatLngs(), {
				interval: 700,
				icon: carIcon,
				playCall: updateRealLine,
			})
			lineLayer.addLayer(animatedMarker)
			// const { lat, lng } = routeLine.getLatLngs()[0]
			// const newLatlngs = [
			// 	{
			// 		lat,
			// 		lng,
			// 	},
			// ]
			const newLatlngs = [routeLine.getLatLngs()[0]]

			// 绘制已行走轨迹线（橙色那条）
			function updateRealLine(latlng) {
				newLatlngs.push(latlng)
				realRouteLine.setLatLngs(newLatlngs)
			}

			setTimeout(() => {
				animatedMarker.start()
			}, 1000)
			/**
			 * 通过多边形覆盖 无用
			 */
			// 过滤无效的点，确保经纬度是有效的
			// if (latents.length < 3) {
			// 	alert('至少需要3个有效的点来计算凸包')
			// 	return
			// }
			// // 使用 Turf.js 计算凸包
			// const polygon_ = turf.convex(turf.featureCollection(latents.map((point) => turf.point(point))))
			// const { geometry } = polygon_
			// const { coordinates } = geometry
			// L.polygon(coordinates, {stroke: false, color:'red' ,fillOpacity:0.7 }).addTo(this.map)
		},
		/**
		 * @description: 加载热力图
		 * @return {*}
		 */
		async loadHeatLayer(layerId, heatDatas) {
			this.currentLayerId = layerId
			let heatData = heatDatas.filter((item) => !isNaN(Number(item.latitude)))
			this.removeLayer(layerId)
			let heatDataList = []
			if (heatData && heatData.length > 0) {
				this.showHeat = true
				await this.redrawGeoJSON()
				heatDataList = heatData.map((e) => {
					return [e.latitude, e.longitude].concat(1)
				})
				const heatLayer = L.heatLayer(heatDataList, {
					radius: 24,
					minOpacity: 0.2,
					gradient: {
						'0.2': '#0ff',
						'0.3': '#0f0',
						'0.5': '#ff0',
						'0.7': '#f63b05',
					},
				})
				this.rootLayerGroup.addLayer(heatLayer)
				this.layerList.push({
					id: layerId,
					layer: heatLayer,
				})

				// 获取heatLayer的容器元素并修改其z-index
				var heatContainer = document.querySelector('.leaflet-heatmap-layer')
				if (heatContainer) {
					// heatContainer.style.zIndex = 1000 // 设置一个较高的z-index值
				}
			}
			this.$store.commit('updateMapLoaded', true)
		},

		// getPicType(type, date) {
		//   picTif({
		//     type: type,
		//     date: date
		//   }).then(res => {
		//     let picurl = res.content[0].picurl + '?key=e503e4a74a7d3a3063827ef324303bc6';
		//     if (type == 4091 || type == 4092 || type == 4093 || type == 4094 || type == 4095 || type == 4096 || type == 4097 || type == 4098) {
		//       this.loadGeoTiffLayer(
		//         'geotiff',
		//         picurl
		//       )
		//     } else if (type == 1318) {
		//       this.loadImageLayer('jy', picurl, [
		//         [0, 70],
		//         [60, 140]
		//       ])
		//     } else if (type == 1402) {
		//       this.loadImageLayer('jy2', picurl, [
		//         [10.1556, 73.4413],
		//         [53.5656, 135.0913]
		//       ])
		//     }
		//   })
		// },

		getPicType(type, date) {
			let params = {
				picType: type,
				startTime: this.dayjs(date)
					.subtract(1, 'days')
					.format('YYYYMMDD'),
				endTime: date,
				page_no: '1',
				page_size: '20',
			}
			if (type == '4094' || type == '4095' || type == '4096' || type == '4097') {
				walarmPic(params).then((res) => {
					if (res.code == 0) {
						this.loadGeoTiffLayer('geotiff', res.data[0].rows[0].picurl + '?key=e503e4a74a7d3a3063827ef324303bc6')
					}
				})
			} else if (type == '4091' || type == '4092' || type == '4093') {
				meteorologicalPic(params).then((res) => {
					if (res.code == 0) {
						this.loadGeoTiffLayer('geotiff', res.data[0].rows[0].picurl + '?key=e503e4a74a7d3a3063827ef324303bc6')
					}
				})
			} else if (type == 1318) {
				rainfallPic(params).then((res) => {
					if (res.code == 0) {
						this.loadImageLayer('jy', res.data[0].rows[0].picurl + '?key=e503e4a74a7d3a3063827ef324303bc6', [
							[0, 70],
							[60, 140],
						])
					}
				})
			} else if (type == 1402) {
				rainfallPic(params).then((res) => {
					if (res.code == 0) {
						this.loadImageLayer('jy2', res.data[0].rows[0].picurl + '?key=e503e4a74a7d3a3063827ef324303bc6', [
							[10.1556, 73.4413],
							[53.5656, 135.0913],
						])
					}
				})
			} else if (type == 'gz_windflowobs1h') {
				// this.loadWindLayer('wind', './json/gfvu-world-202403010800.json')
				params = {
					startTime: this.dayjs(date)
						.subtract(1, 'days')
						.format('YYYYMMDDhhmm'),
					endTime: this.dayjs(date).format('YYYYMMDDhhmm'),
					page_no: '1',
					page_size: '20',
				}
				windPic(params).then((res) => {
					if (res.code == 0) {
						extractFile({
							fileUrl: res.data[0].rows[0].picurl,
						}).then((res) => {
							this.removeLayer('wind')
							const windLayer = L.velocityLayer({
								data: res,
							})
							this.rootLayerGroup.addLayer(windLayer)
							this.layerList.push({
								id: 'wind',
								layer: windLayer,
							})
						})
						//this.loadWindLayer('wind', res.data[0].rows[0].picurl + '?key=e503e4a74a7d3a3063827ef324303bc6')
					}
				})
			}
		},

		showLayers(areaId, picType, date) {
			this.removeLayer('geotiff')
			this.removeLayer('jy')
			this.removeLayer('jy2')
			this.removeLayer('wind')
			this.$nextTick(async () => {
				await this.loadGeoJsonLayerFromUrl(DataVUrl + `${areaId || 100000}_full.json`, 'geojson', true)

				if (picType) {
					this.getPicType(picType, this.dayjs(date).format('YYYYMMDD'))
				}
			})
		},
	},
}
</script>

<style lang="less" scoped>
/deep/ .custom-cluster-small {
}

/deep/ .custom-icon-box {
	background-size: cover;
	background-position: center center;
	background-repeat: no-repeat;
}

.custom-marker {
	z-index: 999;
	.custom-marker-icon {
		width: 50px;
		height: 60px;
		background-size: 100% 100%;
	}
}

.mapContainer {
	// width: 100%;
	// height: 100%;
	z-index: 0;
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0px;
	left: 0px;
}
// .zyjd-mapContainer {
// 	// width: 100%;
// 	// height: 100%;
// 	width: calc(100% - 455px);
// 	margin-left: 455px;
// 	box-sizing: border-box;
// }

.clear {
	z-index: 1;
	position: absolute;
	width: 44px;
	height: 44px;
	top: 151px;
	left: 1337px;
	background: url(~@/assets/map/icon3.png) no-repeat;
	cursor: pointer;
}
.clear2 {
	z-index: 1;
	position: absolute;
	width: 44px;
	height: 44px;
	top: 201px;
	left: 1337px;
	background: url(~@/assets/map/icon3.png) no-repeat;
	cursor: pointer;
}

.back {
	z-index: 1;
	position: absolute;
	width: 44px;
	height: 44px;
	top: 201px;
	left: 1337px;
	background: url(~@/assets/map/back_default.png) no-repeat;
	background-size: 100% 100%;
	cursor: pointer;
}
.back2 {
	z-index: 1;
	position: absolute;
	width: 44px;
	height: 44px;
	top: 251px;
	left: 1337px;
	background: url(~@/assets/map/back_default.png) no-repeat;
	background-size: 100% 100%;
	cursor: pointer;
}

// .back:hover {
//   background: url(~@/assets/map/back_active.png) no-repeat;
// }

.weather {
	z-index: 1;
	position: absolute;
	width: 44px;
	height: 44px;
	top: 801px;
	left: 1337px;
	background: url(~@/assets/map/icon3.png) no-repeat;
}

// 修改地图滤镜
// /deep/ .leaflet-zoom-animated img {
//   -webkit-filter: sepia(100%) invert(90%) !important;
//   -ms-filter: sepia(100%) invert(90%) !important;
//   -moz-filter: sepia(100%) invert(90%) !important;
//   filter: sepia(100%) invert(90%) !important;
// }

/deep/ .leaflet-tooltip {
	background-color: unset;
	color: #fff;
	text-shadow: -1px 0 #333, 0 1px #333, 1px 0 #333, 0 -1px #333;
	border: unset;
	box-shadow: unset;
	font-size: 16px;
	// font-weight: 800;
}

/deep/ .my-popup {
	.leaflet-popup-content-wrapper {
		box-shadow: unset;
		background: unset;
		color: unset;

		.leaflet-popup-content {
			width: unset !important;
			margin: 0px;
		}
	}

	.leaflet-popup-close-button {
		top: 16px;
		right: 1rem;
	}
}

/deep/ .iclient-leaflet-logo {
	display: none;
}

/deep/ .leaflet-measure-path-measurement {
	position: absolute;
	font-size: 10px;
	color: black;
	text-shadow: -1px 0 0 white, -1px -1px 0 white, 0 -1px 0 white, 1px -1px 0 white, 1px 0 0 white, 1px 1px 0 white, 0 1px 0 white,
		-1px 1px 0 white;
	white-space: nowrap;
	transform-origin: 0;
	pointer-events: none;
}

/deep/ .leaflet-measure-path-measurement > div {
	position: relative;
	margin-top: -25%;
	left: -50%;
}

/deep/ .zxnj-div-icon {
	display: flex;
	.zxnj-marker-icon {
		width: 12px;
		height: 12px;
		border-radius: 50%;
	}
}

/deep/ .custom-div-icon {
	display: flex;

	.marker-icon {
		color: #fff;
		width: 138px;
		height: 69px;
		background-size: 100% 100%;

		div {
			white-space: nowrap;
			margin: 18px 0 0 60px;
			font-size: 18px;
			font-family: FZSKJW--GB1-0, FZSKJW--GB1;
			font-weight: normal;
			color: #ffffff;
		}
	}
}
</style>
