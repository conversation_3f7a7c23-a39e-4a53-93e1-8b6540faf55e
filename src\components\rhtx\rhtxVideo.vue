<template>
  <div class="map_video">
    <!-- 视频连线 -->
    <div>
      <div class="del" @click="closeVideo"></div>
      <div class="title">
        <span>{{callType =='video'?"视频连线":'语音连线'}}</span>
      </div>
      <div class="content" ref="video"></div>
    </div>
  </div>
</template>

<script type='text/ecmascript-6'>
import rtc from '@/rhtx/core/index'
export default {
  name: 'rhtxVideo',
  props: {
    phone: { type: String, default: '' },
    callType: { type: String, default: '' }
  },
  data() {
    return {}
  },
  created() {},
  mounted() {
    this.zhddClick()
  },
  methods: {
    closeVideo() {
      this.$emit('closeVideoEmit')
      if (this.dryyth) {
        this.dryyth.session.terminate()
      }
    },
    zhddClick() {
      console.log('this.phone',this.phone);
      // 视频通话
      if (this.callType == 'video') {
        this.$nextTick(async () => {
          this.dryyth = await rtc.p2p.callPop({
            el: this.$refs.video, //页面挂载元素
            target: this.phone, //手机端的账号
            callType: 'voice_video'
          })
          this.dryyth.session.on('hangup', () => {
            console.log('视频通话关闭')
            this.closeVideo()
          })
        })
      } else {
        this.$nextTick(async () => {
          this.dryyth = await rtc.p2p.callPop({
            el: this.$refs.video, //页面挂载元素
            target: this.phone, //手机端的账号
            callType: 'voice'
          })
          console.log('this.dryyth', this.dryyth)
          this.dryyth.session.on('hangup', () => {
            console.log('语音通话关闭')
            this.closeVideo()
          })
        })
      }
    }
  }
}
</script>

<style lang='scss' scoped>
.map_video {
  width: 700px;
  height: 640px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999999999999;
  background: url(~@/assets/shzl/map/ai_popBg.png) no-repeat center / 100% 100%;
  background-size: 100% 100%;
  .del {
    width: 29px;
    height: 36px;
    background: url(~@/assets/shzl/map/camera/close.png) no-repeat;
    position: absolute;
    right: 41px;
    top: 30px;
    cursor: pointer;
    z-index: 1;
  }
  .title {
    background: url(~@/assets/shzl/map/title_bg.png) no-repeat center / 100% 100%;
    background-size: 100% 100%;
    height: 47px;
    margin: 20px auto 0;
    line-height: 47px;
    text-align: left;
    text-indent: 27px;
    font-size: 18px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #ffffff;
    position: relative;
    width: 90%;

    span {
      background: linear-gradient(180deg, #ffffff 0%, #2aa6ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-left: 20px;
    }
  }
  .content {
    width: 80%;
    height: 500px;
    margin: 0px auto 0;
  }
}
</style>
