<template>
	<div :class="['chat', { 'chat-hide': !isExpand }]" :style="chatStyle">
		<div id="chat-container" class="chat-content">
			<div class="chat-message" v-for="(message, index) of messageList" :key="'m' + index">
				<span class="message-origin">{{ message.userId.split('_')[2] }}：</span>
				<span class="message-content">{{ message.data }}</span>
			</div>
		</div>
		<div class="chat-foot" v-show="isMonitorIn">
			<el-input ref="chatInput" id="inputEle" @change="sendMessage" v-model="inputValue" placeholder="说点什么...">
				<chooseEmoji slot="prefix" @setEmo="insertEmojiToMessage"></chooseEmoji>
				<div class="slide" slot="suffix">
					<div class="slide-icon" @click="slide"></div>
				</div>
			</el-input>
		</div>
		<div :class="['expand-icon', { 'expand-icon-show': !this.isExpand }]" @click="expand">
			<img class="icon" src="@/assets/bjnj/dialog-icon.png" alt="" />
		</div>
	</div>
</template>

<script>
export default {
	name: 'instantMessage',
	components: {
		ChooseEmoji: () => import('./chooseEmoji.vue'),
	},
	props: {
		// 消息内容
		messageList: {
			type: Array,
			default: () => [],
		},
		// 聊天组件外层自定义样式
		chatStyle: {
			type: Object,
			default: () => ({}),
		},
		isMonitorIn: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		messageNum() {
			return this.messageList.length
		},
	},
	data() {
		return {
			inputValue: '',
			isExpand: true,
		}
	},
	watch: {
		messageNum(newVal) {
			this.$nextTick(() => {
				this.scrollToBottom()
			})
		},
	},
	methods: {
		scrollToBottom() {
			let container = document.getElementById('chat-container')
			if (!container) {
				console.error('不存在对应元素')
				return
			}
			container.scrollTop = container.scrollHeight + 24
			// 判断当前是否在最底部,若不在移动到最底部
			// if(container.scrollHeight - Math.abs(container.scrollTop) !== container.clientHeight) {
			//   container.scrollTop = container.scrollHeight - container.clientHeight + 44
			// }
		},
		sendMessage() {
			this.$emit('messageSend', this.inputValue)
      this.inputValue = ''
		},
		clearInput() {
			this.inputValue = ''
		},
		// 将表情插入到文本框中
		insertEmojiToMessage(e) {
			this.$message({
				message: '暂不支持表情发送',
				type: 'warning',
			})
			// let newContent = ''
			// if (window.getSelection().anchorNode.className.includes('el-input')) {
			//   let dom = document.getElementById('inputEle')
			//   let selectionStart = dom.selectionStart
			//   newContent =
			//     this.inputValue.substring(0, selectionStart) +
			//     e +
			//     this.inputValue.substring(selectionStart)
			// } else {
			//   newContent = this.inputValue + e
			// }
			// this.inputValue = newContent
			// this.$refs['chatInput'].focus()
		},
		// 伸缩与延展
		slide() {
			this.isExpand = false
		},
		expand() {
			this.isExpand = true
		},
	},
}
</script>

<style lang="scss" scoped>
.v-list-move,
.v-list-enter-active,
.v-list-leave-active {
	transition: all 0.5s ease;
}

.v-list-enter-from,
.v-list-leave-to {
	opacity: 0;
}
.v-list-leave-active {
	position: absolute;
}
.chat {
	position: absolute;
	left: 16px;
	bottom: 46px;
	z-index: 2000;
	transition: all 0.5s ease;
	&-hide {
		left: -295px;
	}
	&-content {
		width: 100%;
		height: calc(100% - 44px);
		overflow-y: scroll;
		margin-bottom: 12px;
		display: flex;
		flex-direction: column;
		// justify-content: flex-end;
		&::-webkit-scrollbar {
			display: none !important; /* Chrome Safari */
		}
		scrollbar-width: none; /* firefox */
		-ms-overflow-style: none; /* IE 10+ */
		.chat-message {
			font-family: Source Han Sans CN;
			font-weight: 400;
			font-size: 16px;
			background: rgba(84, 85, 85, 0.8);
			border-radius: 8px;
			width: fit-content;
			max-width: 100%;
			padding: 10px 10px;
			text-align: left;
			.message-origin {
				line-height: 28px;
				color: #feba3f;
				white-space: nowrap;
			}
			.message-content {
				line-height: 28px;
				color: #ffffff;
				white-space: wrap;
			}
			&:first-child {
				margin-top: auto;
			}
			&:not(:last-of-type) {
				margin-bottom: 4px;
			}
		}
	}
	&-foot {
		width: 100%;
		height: 32px;
		text-align: left;
		::v-deep .el-input {
			width: 192px;
			.el-input__inner {
				padding-left: 56px;
				padding-right: 35px;
				background: rgba(84, 85, 85, 0.8);
				border-radius: 8px;
				border: none;
				color: #ffffff;
				// border: 1px solid #DEDEDE;
			}
			.el-input__prefix {
				width: 56px;
				height: 100%;
				left: 0;
				top: 0;
				position: absolute;
				&::before {
					content: '';
					width: 1px;
					height: 20px;
					background: #999999;
					position: absolute;
					top: 10px;
					right: 10px;
				}
			}
			.el-input__suffix {
				cursor: pointer;
				width: 35px;
				height: 100%;
				right: 0;
				top: 0;
				position: absolute;
				&::before {
					content: '';
					width: 1px;
					height: 20px;
					background: #999999;
					position: absolute;
					top: 10px;
					left: 0px;
				}
				.slide-icon {
					width: 7px;
					height: 10px;
					position: absolute;
					left: 12px;
					top: 14px;
					background: url('~@/assets/bjnj/emoji_hide.png') no-repeat center / 100% 100%;
				}
			}
		}
	}
	.expand-icon {
		width: 48px;
		height: 48px;
		background: rgba(115, 115, 115, 0.8);
		border-radius: 0px 8px 8px 0px;
		display: flex;
		align-items: center;
		justify-content: center;
		opacity: 0;
		position: absolute;
		bottom: 0;
		right: -48px;
		.icon {
			width: 16px;
			height: 16px;
		}
		&-show {
			opacity: 1;
			transition-delay: 0.5s;
		}
	}
}
</style>
