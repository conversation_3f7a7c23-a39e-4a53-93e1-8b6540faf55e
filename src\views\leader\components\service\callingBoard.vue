<template>
  <div class="call-board">
    <el-input v-model="callingMobile" placeholder="请输入手机号码"></el-input>
    <div class="call-board-btn" @click="callByBoard">视频呼叫</div>
  </div>
</template>

<script>
  import { validateCallNum } from "./tool/index"
  export default {
    name: 'callBoard',
    data() {
      return {
        callingMobile: ""
      }
    },
    methods: {
      callByBoard() {
        if(validateCallNum(this.callingMobile)) {
          this.$bus.emit('launchPointMeeting', this.callingMobile)
          this.$emit('closeCallingBoard')
        } else {
          this.$message({
            type: 'error',
            message: '手机号不合法，请重新输入'
          })
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .call-board {
    width: 341px;
    height: 79px;
    position: absolute;
    z-index: 1500;
    right: 594px;
    bottom: 535px;
    background: url('~@/assets/service/board-bg.png') no-repeat center / 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    &-btn {
      width: 88px;
      line-height: 36px;
      text-align: center;
      background: url('~@/assets/service/board-btn-bg.png') no-repeat center / 100% 100%;
      cursor: pointer;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #FFFFFF;
    }
    ::v-deep {
      .el-input {
        width: 200px !important;
        margin-right: 10px;
        .el-input__inner {
          height: 32px;
          line-height: 32px;
          border: 1px solid #CBE5FF !important;
          background-color: transparent !important;
          font-size: 16px !important;
          color: #FFFFFF !important;
        }
      }
    }
  }
</style>