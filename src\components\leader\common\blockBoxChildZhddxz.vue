<template>
	<div class="child-block" :style="{ height: blockHeight + 'px' }">
		<slot></slot>

		<div class="content-box" v-if="!loading && data.length > 0">
			<div class="content-item-box" v-for="(item, index) in data" :key="index">
				<div class="left">
					<div class="line1-box">
						<div class="line1-left-box">
							<img :src="item.iconUrl" alt="" />
							<div class="line1-label">{{ item.label || '-' }}</div>
						</div>

						<div
							class="right"
							@click="
								() => {
									item.func(item)
								}
							"
						>
							<img :src="item.messageIcon" alt="" />
							消息管理
						</div>
					</div>
					<!-- <div class="line2-box">{{ item.content || '-' }}</div> -->
				</div>
			</div>
		</div>

		<div v-else-if="loading" class="alert-empty content-box">加载中...</div>
		<div v-else-if="data.length == 0" class="alert-empty content-box">暂无数据</div>
		<div class="btns-box">
			<div class="btn-item-box" v-for="(item, index) in btns" @click="() => item.func(item)">
				<i class="icon-box" :class="item.iconClass"></i>
				{{ item.label }}
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		data: {
			type: Array,
			default: () => {},
		},
		blockHeight: {
			type: Number,
			default: 330,
		},
		btns: {
			type: Array,
			default: () => {},
		},
		loading: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {}
	},
	computed: {},
	methods: {
		getMessageGroupToken() {
			const messageGroupToken = window.sessionStorage.getItem('messageGroupToken')
		},
	},

	watch: {
		data() {
		},
	},
}
</script>

<style lang="less" scoped>
.alert-empty {
	width: calc(100% - 20px) !important;
	height: 100%;
	display: flex;
	justify-content: center !important;
	align-items: center !important;
	font-family: PangMenZhengDao-3, PangMenZhengDao-3;
	font-weight: 500;
	font-size: 20px;
	color: #dbf1ff;
	text-shadow: 0px 0px 10px #bddcf1;
	text-align: center;
	font-style: normal;
	text-transform: none;
}
.child-block {
	width: 455px;
	box-sizing: border-box;
	z-index: 101;
	position: relative;

	&::-webkit-scrollbar {
		width: 4px; /* 滚动条宽度 */
	}

	//轨道
	&::-webkit-scrollbar-track {
		background: #04244e; /* 滚动条轨道背景 */
		border-radius: 10px;
		padding: 5px 0;
	}

	//滑块
	&::-webkit-scrollbar-thumb {
		background: #82aed0; /* 滑块背景颜色 */
		border-radius: 10px;
		height: 15px;
	}

	//悬停滑块颜色
	&::-webkit-scrollbar-thumb:hover {
		background: #ff823b; /* 鼠标悬停时滑块颜色 */
	}

	//箭头
	&::-webkit-scrollbar-button {
		display: none; /* 隐藏滚动条的箭头按钮 */
	}

	.content-box {
		width: 100%;
		height: calc(100% - 90px);
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
		overflow: auto;

		padding: 0 16px;
		padding-left: 22px;

		&::-webkit-scrollbar {
			width: 4px; /* 滚动条宽度 */
		}

		//轨道
		&::-webkit-scrollbar-track {
			background: #04244e; /* 滚动条轨道背景 */
			border-radius: 10px;
			padding: 5px 0;
		}

		//滑块
		&::-webkit-scrollbar-thumb {
			background: #82aed0; /* 滑块背景颜色 */
			border-radius: 10px;
			height: 15px;
		}

		//悬停滑块颜色
		&::-webkit-scrollbar-thumb:hover {
			background: #ff823b; /* 鼠标悬停时滑块颜色 */
		}

		//箭头
		&::-webkit-scrollbar-button {
			display: none; /* 隐藏滚动条的箭头按钮 */
		}

		.content-item-box {
			width: 100%;
			padding: 16px;
			border-bottom: 1px solid #222b38;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.left {
				width: 100%;
				.line1-box {
					width: 100%;
					height: 20px;
					display: flex;
					justify-content: space-between;
					margin-bottom: 8px;
					align-items: flex-start;

					.line1-left-box {
						height: 20px;
						display: flex;
						justify-content: flex-start;
						align-items: flex-start;

						img {
							width: 22px;
							height: 22px;
							margin-right: 5px;
							margin-top: 3px;
						}

						.line1-icon-box {
							width: 20px;
							height: 20px;
							background: skyblue;
							margin-right: 5px;
						}
						.line1-label {
							font-family: PingFang SC, PingFang SC;
							font-weight: bold;
							font-size: 14px;
							color: #e5642e;
							text-align: left;
							font-style: normal;
							text-transform: none;
							white-space: nowrap;
							overflow: hidden;
							text-align: left;
							text-overflow: ellipsis;
						}
					}
				}

				.line2-box {
					width: 100%;
					height: 100%;
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 14px;
					color: #abd0f2;
					white-space: nowrap;
					overflow: hidden;
					text-align: left;
					text-overflow: ellipsis;
				}
			}

			.right {
				height: 20px;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				cursor: pointer;

				font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
				font-weight: normal;
				font-size: 14px;
				color: #159aff;
				text-align: left;
				font-style: normal;
				text-transform: none;

				img {
					width: 16px;
					height: 16px;
					margin-right: 5px;
				}
			}
		}
	}

	.btns-box {
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 20px;
		margin-top: 25px;
		.btn-item-box {
			background-color: #04244e;
			border-radius: 4px 4px 4px 4px;
			border: 1px solid #159aff;
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 8px 16px;

			font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
			font-weight: normal;
			font-size: 14px;
			color: #159aff;
			text-align: left;
			font-style: normal;
			text-transform: none;
			cursor: pointer;

			.icon-box {
				font-size: 16px;
				margin-right: 4px;
				color: #159aff;
			}

			&:hover {
				color: #fff;
				background: #159aff;
				.icon-box {
					color: #fff;
				}
			}
		}
	}
}

.popDropDown {
	::v-deep .el-dropdown {
		color: #caecff;
		font-family: YouSheBiaoTiHei;
		font-size: 16px;
		line-height: 21px;
		text-align: right;
		font-style: normal;
		line-height: 40px;
		right: 0;
		position: absolute;
	}
	::v-deep .el-dropdown-menu {
		background-color: #00173b !important;
		border: 1px solid #2b7bbb !important;
	}
	.el-dropdown-menu__item {
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
		line-height: 40px;
		padding: 0 30px;
		&:hover {
			background: #185493;
			color: rgba(255, 255, 255, 0.8);
		}
	}
}
</style>
