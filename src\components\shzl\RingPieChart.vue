<template>
  <chart
    ref="chart"
    class="chart"
    :autoresize="true"
    :option="option"
    :loading="loading"
    v-on="$listeners"
  />
</template>

<script>
import { cloneDeep, merge } from 'lodash';
// import { graphic } from 'echarts/lib/export/api.js'

export default {
  name: 'RingPieChartB01',
  props: {
    // 数据
    data1: {
      type: Array,
      default: () => [
        ['name', 'value'],
        ['涉黄', 102],
        ['涉赌', 489],
        ['涉毒', 113]
      ]
    },
    // data2: {
    //   type: Array,
    //   default: () => [
    //     ['name', 'value'],
    //     ['男', 56],
    //     ['女', 44]
    //   ]
    // },
    // 初始化属性
    initOption: {
      type: Object,
      default: () => ({})
    },
    // 配置项
    options: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    /* const data = cloneDeep(this.data)
        .filter((item, index) => index > 0)
        .map(item => {
          return {
            name: item[0],
            value: item[1]
          }
        }) */
    this.defaultOptions = {
      color: ['#A0CE3A', '#31C5C0', '#1E9BD1', '#0F347B', '#7F6AAD', '#585247'],
      textColor: '#fff',
      labelColor: '#fff',
      itemGap: 4,
      //textSize: 16,
      labelSize: 18,
      centerX: '30%',
      centerY: '50%',
      titleX: '20%',
      titleY: 'center'
    };

    const option = {
      color: this.defaultOptions.color,
      // backgroundColor: '#081736',
      title: {
        //text: '',
        subtext: '',
        itemGap: this.defaultOptions.itemGap,
        subtextStyle: {
          fontSize: this.defaultOptions.labelSize,
          align: 'center',

          color: this.defaultOptions.labelColor
        },

        x: 'center',
        y: 'center'
      },
      legend: {
        show: true,
        top: 'center',
        left: '50%',
        itemWidth: 14,
        itemHeight: 14,
        width: 50,
        padding: [0, 5],
        itemGap: 12,
        icon: 'circle',
        data: [],
        formatter: function(name) {
          return '{title|' + name + '}';
        },
        textStyle: {
          rich: {
            title: {
              fontSize: 14,
              color: '#AEC2FD'
            }
          }
        }
      },
      grid: cloneDeep(this.$_config.grid),
      series: [
        // 边框的设置
        {
          radius: ['50%', '60%'],
          center: [this.defaultOptions.centerX, this.defaultOptions.centerY],
          type: 'pie',
          z: 2,
          silent: true,
          label: {
            normal: {
              show: false
            },
            emphasis: {
              show: false
            }
          },
          labelLine: {
            normal: {
              show: false
            },
            emphasis: {
              show: false
            }
          },
          animation: false,
          tooltip: {
            show: false
          },
          data: [
            {
              value: 1,
              itemStyle: {
                color: 'rgba(12,28,83,0.4)'
              }
            }
          ]
        },

        {}
      ]
    };
    return {
      defaultOptions: this.defaultOptions,
      option: option,
      loading: false
    };
  },
  watch: {
    data1: {
      immediate: false,
      deep: true,
      handler() {
        this.createSery();
      }
    },
    // data2: {
    //   immediate: false,
    //   deep: true,
    //   handler(newVal) {
    //     // this.loading = false
    //     //const data=newVal.filter((item, index) => index > 0)
    //     this.option.title.subtext =
    //       '男性：' + newVal[1][1] + '%' + '\n' + '\n' + '女性：' + newVal[2][1] + '%';
    //   }
    // },
    options: {
      immediate: true,
      deep: true,
      handler(newVal) {
        this.parseOptopns(merge(this.defaultOptions, newVal));
      }
    }
  },

  methods: {
    createSery() {

      const data = cloneDeep(this.data1)
        .filter((item, index) => index > 0)
        .map(item => {
          return {
            name: item[0],
            value: item[1]
          };
        });
      console.log(122,data);


      this.option.color = this.defaultOptions.color;
      const value = cloneDeep(this.data1)
        .filter((item, index) => index > 0)
        .map(it => it[1])
        .reduce((a, b) => a + b, 0);
      this.option.legend.show = this.options.legend.show;
      this.option.legend.data = [];
      /* this.option.legend.data.push(
        data.map(it => {
          return {
            name: it.name + ':' + ((it.value / value) * 100).toFixed(2) + '%'
          };
        })
      ); */
      this.option.legend.data = data;
      console.log(122,data);
      for (let i = 0; i < data.length; i++) {
        this.option.legend.data[i].name =
          data[i].name + '：'+ data[i].value + ' ' + ((data[i].value / value) * 100).toFixed(2) + '%';
      }
      this.option.title.itemGap = this.defaultOptions.itemGap;
      this.option.title.subtextStyle = {
        fontSize: this.defaultOptions.labelSize,
        color: this.defaultOptions.labelColor,
        align: 'center'
      };

      this.option.title.y = this.defaultOptions.titleY;
      this.option.title.x = this.defaultOptions.titleX;

      //console.log(this.defaultOptions);
      this.option.series[1] = {
        radius: ['50%', '73%'],
        center: [this.defaultOptions.centerX, this.defaultOptions.centerY],
        type: 'pie',
        label: {
          show: false,
          // formatter: "{a} <br/>{b} : {c}",
          formatter: function(a) {
            // console.log(111,a);
            let str = '';
            if (a.dataIndex === 0) {
              str = '{color1|' + ((a.value / value) * 100).toFixed(2) + '%' + '}';
            } else if (a.dataIndex === 1) {
              str = '{color2|' + ((a.value / value) * 100).toFixed(2) + '%' + '}';
            } else if (a.dataIndex === 2) {
              str = '{color3|' + ((a.value / value) * 100).toFixed(2) + '%' + '}';
            } else if (a.dataIndex === 3) {
              str = '{color4|' + ((a.value / value) * 100).toFixed(2) + '%' + '}';
            } else if (a.dataIndex === 4) {
              str = '{color5|' + ((a.value / value) * 100).toFixed(2) + '%' + '}';
            } else {
              str = '{color6|' + ((a.value / value) * 100).toFixed(2) + '%' + '}';
            }
            return str;
          },
          position: 'center',
          rich: {
            color1: {
              color: this.defaultOptions.color[0],
              fontSize: 18
            },
            color2: {
              color: this.defaultOptions.color[1],
              fontSize: 18
            },
            color3: {
              color: this.defaultOptions.color[2],
              fontSize: 18
            },
            color4: {
              color: this.defaultOptions.color[3],
              fontSize: 18
            },
            color5: {
              color: this.defaultOptions.color[4],
              fontSize: 18
            },
            color6: {
              color: this.defaultOptions.color[5],
              fontSize: 18
            }
          },
          emphasis: {
            show: true
          }
        },
        labelLine: {
          showAbove: true,
          show: true,
          length: 20,
          length2: 26,
          // maxSurfaceAngle: 90,
          emphasis: {
            show: true
          }
        },
        z: 1,
        name: '总量',
        data: data
      };
    },
    parseOptopns(options) {
      this.defaultOptions = options;
      //console.log(this.defaultOptions);
      this.createSery();
    }
  }
};
</script>

<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 100%;
  position: relative;
  top: 20px;
}
</style>
