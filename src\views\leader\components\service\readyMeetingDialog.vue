<template>
	<div class="ready-dialog">
		<div class="ready-dialog-head">
			<div class="close-icon" @click="closeDialog"></div>
		</div>
		<div class="theme">
			<el-input v-model="theme" placeholder="请输入会议主题"></el-input>
		</div>
		<div class="ready-dialog-content">
			<video id="video-ele" class="video-ele" autoplay></video>
		</div>
		<div class="ready-dialog-foot">
			<div class="dialog-foot-group">
				<div class="foot-btn" v-if="microphoneStatus" @click.stop="changeLocalMicroStatus">
					<div class="foot-btn-icon mic-on"></div>
					<div>麦克风</div>
				</div>
				<div class="foot-btn" v-else @click.stop="changeLocalMicroStatus">
					<div class="foot-btn-icon mic-off"></div>
					<div>麦克风</div>
				</div>
				<div class="foot-btn" v-if="cameraStatus" @click.stop="changeLocalCameraStatus">
					<div class="foot-btn-icon cam-on"></div>
					<div>摄像头</div>
				</div>
				<div class="foot-btn" v-else @click.stop="changeLocalCameraStatus">
					<div class="foot-btn-icon cam-off"></div>
					<div>摄像头</div>
				</div>
			</div>
			<div class="btn" @click="startMeeting">开始会议</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'readyMeetingDialog',
	props: {
		launchType: {
			type: String,
			default: 'launch',
		},
	},
	computed: {
		username() {
			return this.$store.state.username
		},
	},
	data() {
		return {
			theme: '',
			cameraStatus: true,
			microphoneStatus: true,
			allTracks: [],
		}
	},
	watch: {
		async cameraStatus(newVal) {
			const constrict = {
				audio: this.microphoneStatus,
				video: newVal,
			}
			await this.getDefaultDeviceStream(constrict)
		},
		async microphoneStatus(newVal) {
			const constrict = {
				audio: newVal,
				video: this.cameraStatus,
			}
			await this.getDefaultDeviceStream(constrict)
		},
	},
	methods: {
		closeDialog() {
			this.$emit('closeDialog')
		},
		changeLocalMicroStatus() {
			this.microphoneStatus = !this.microphoneStatus
		},
		changeLocalCameraStatus() {
			this.cameraStatus = !this.cameraStatus
		},
		startMeeting() {
			if (this.theme) {
				if (this.theme.length > 20) {
					this.$messageNew.message('error', {
						message: '会议名称最多为20个字符',
					})
				} else {
					this.$emit('meetingStart', {
						cameraStatus: this.cameraStatus,
						microphoneStatus: this.microphoneStatus,
						meetingName: this.theme,
						allTracks: this.allTracks,
					})

					this.closeDialog()
				}
			} else {
				this.$message({
					type: 'error',
					message: '会议名称不能为空',
				})
			}
		},
		clearAllTrack() {
			if (this.allTracks.length > 0) {
				this.allTracks.forEach((track) => {
					track.stop()
				})
			}
		},
		async getDefaultDeviceStream(constrict) {
			console.log(constrict)
			try {
				this.clearAllTrack()
				// 如果视频和音频都关闭，就不需要请求新的媒体流
				if (!constrict.video && !constrict.audio) {
					let videoEle = document.querySelector('#video-ele')
					if (videoEle) {
						videoEle.srcObject = null
					}
					return
				}
				const stream = await navigator.mediaDevices.getUserMedia(constrict)
				console.log('stream', stream)
				let videoEle = document.querySelector('#video-ele')
				console.log('videoEle', videoEle)
				if (videoEle) {
					this.allTracks = stream.getTracks()
					videoEle.srcObject = stream
					videoEle.onloadedmetadata = function(e) {
						videoEle.play()
					}
				}
			} catch (err) {
				this.cameraStatus = false
			}
		},
		streamPause() {
			let videoEle = document.querySelector('#video-ele')
			if (videoEle) {
				videoEle.pause()
				videoEle.srcObject = null
				videoEle.src = ''
			}
			this.clearAllTrack()
		},
	},
	async mounted() {
		if (this.launchType === 'quickLaunch') {
			this.theme = this.username + '的视频会议'
		}
		const constrict = {
			audio: this.microphoneStatus,
			video: this.cameraStatus
				? {
						facingMode: 'user',
				  }
				: this.cameraStatus,
		}
		await this.getDefaultDeviceStream(constrict)
	},
	beforeDestroy() {
		this.streamPause()
	},
}
</script>

<style lang="scss" scoped>
.ready-dialog {
	position: absolute;
	z-index: 5000;
	width: 666px;
	background: url('~@/assets/bjnj/ready-dialog-bg.png') no-repeat center / 100% 100%;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	padding: 0 33px;
	&-head {
		width: 100%;
		height: 68px;
		display: flex;
		justify-content: flex-end;
		align-items: center;
		padding-right: 13px;
		.close-icon {
			cursor: pointer;
			width: 32px;
			height: 32px;
			background: url('~@/assets/bjnj/close-icon.png') no-repeat center / 100% 100%;
		}
	}
	.theme {
		width: 100%;
		:deep(.el-input) {
			.el-input__inner {
				border-radius: 5px;
				background: #254f87;
				color: #c2ddfc;
				font-family: Source Han Sans CN;
				font-weight: 400;
				font-size: 20px;
				height: 52px;
				line-height: 52px;
				border: none;
				text-align: center;
			}
		}
	}
	&-content {
		padding: 20px 0;
		.video-ele {
			width: 100%;
			height: 339px;
			border-radius: 8px;
			object-fit: cover;
		}
	}
	&-foot {
		width: 100%;
		padding: 0 20px;
		height: 55px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		.dialog-foot-group {
			display: flex;
			align-items: center;
			flex-wrap: nowrap;
			.foot-btn {
				display: flex;
				align-items: center;
				padding: 0 32px;
				font-family: Source Han Sans CN;
				font-weight: 400;
				font-size: 14px;
				color: #ffffff;
				cursor: pointer;
				&:not(:last-of-type) {
					border-right: 1px solid;
					border-image: linear-gradient(180deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.1)) 1 1;
				}
				&-icon {
					width: 28px;
					height: 28px;
					margin-right: 6px;
				}
				.mic-on {
					background: url('~@/assets/service/mic-on.png') no-repeat center / 100% 100%;
				}
				.mic-off {
					background: url('~@/assets/service/mic-off.png') no-repeat center / 100% 100%;
				}
				.cam-on {
					background: url('~@/assets/service/cam-on.png') no-repeat center / 100% 100%;
				}
				.cam-off {
					background: url('~@/assets/service/cam-off.png') no-repeat center / 100% 100%;
				}
			}
		}
		.btn {
			width: 108px;
			height: 31px;
			line-height: 31px;
			text-align: center;
			font-family: Source Han Sans CN;
			font-weight: 400;
			font-size: 16px;
			color: #ffffff;
			background: url('~@/assets/bjnj/btn-bg.png') no-repeat center / 100% 100%;
		}
	}
}
</style>
