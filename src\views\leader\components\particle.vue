<template>
  <div class="particle">
    <div class="particle-mask" />
    <template v-for="(item, index) in dots">
      <div v-if="show > index" :key="item.id" class="particle-dot" :style="randomPosition[index]" />
    </template>
  </div>
</template>

<script>
import { randomString } from '@/utils/index.js';
export default {
  props: {
    color: {
      type: String,
      default: '#79DEEA'
    },
    dotNum: {
      type: [Number, String],
      default: 50
    }
  },
  data() {
    return {
      show: -1,
      inter: null
    };
  },
  computed: {
    dots() {
      const len = parseInt(this.dotNum);
      if (isNaN(len)) {
        return [];
      } else {
        const res = Array(len)
          .fill(null)
          .map(() => ({
            id: randomString(10)
          }));
        return res;
      }
    },
    randomPosition() {
      const len = parseInt(this.dotNum);
      if (isNaN(len)) {
        return [];
      } else {
        const res = Array(len)
          .fill(null)
          .map(() => ({
            left: 35 + Math.floor(Math.random() * 65) + '%',
            top: Math.floor(Math.random() * 100) + '%',
            backgroundColor: this.color
          }));
        return res;
      }
    }
  },
  watch: {
    dotNum: {
      immediate: true,
      handler(val) {
        this.inter && clearInterval(this.inter);
        this.show = -1;
        const num = parseInt(val);
        if (isNaN(num)) {
          return false;
        } else {
          this.inter = setInterval(() => {
            this.show++;
            if (this.show > num) clearInterval(this.inter);
          }, 20);
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.particle {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  @keyframes move {
    100% {
      left: 120%;
    }
  }
  .particle-mask {
    width: 100%;
    height: 100%;
    z-index: 2;
    position: absolute;
    top: 0;
    left: 0;
  }
  .particle-dot {
    position: absolute;
    z-index: 1;
    width: 2px;
    height: 2px;
    animation: move infinite 3s linear;
  }
}
</style>
