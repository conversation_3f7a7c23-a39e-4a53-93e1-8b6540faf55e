import http from '@/utils/leader/request'
import { baseUrl, zbxtUrl, bjnjUrl } from '@/utils/leader/const'

export const getNjProcess = () => {
	return http.get(zbxtUrl + '/dataService/gjStandardDb/gjWorkagmach/JEihPNhCuq')
}

// 作业中心-作物类型
export const getCropType = (data) => {
	return http.get(bjnjUrl + '/api/screen/workCenter/getCropType', data)
}

// 作业中心-作业总览
export const getOverview = (data) => {
	return http.get(bjnjUrl + '/api/screen/workCenter/getOverview', data)
}

// 作业中心-作业面积日
export const getAreaDay = (data) => {
	return http.get(bjnjUrl + '/api/screen/workCenter/getAreaDay', data)
}

// 农机投入类型
export const queryTAgmachType = (data) => {
	return http.get(bjnjUrl + '/api/dp/zyzx/queryTAgmachType', data)
}

// 农机作业类型
export const queryTAgmachWorkInfo = (data) => {
	return http.get(bjnjUrl + '/api/dp/zyzx/queryTAgmachWorkInfo', data)
}

// 投入趋势
export const queryTAgmachTrend = (data) => {
	return http.get(bjnjUrl + '/api/dp/zyzx/queryTAgmachTrend', data)
}

export const getWorkProgress = (data) => {
	return http.get(bjnjUrl + '/api/screen/workCenter/getWorkProgress', data)
}

// 作业进度-作业总览
export const getOverviewNew = (data) => {
	return http.get(bjnjUrl + '/api/screen/workCenter/getOverviewNew', data)
}

// 作业进度-种植面积
export const getPlantingArea = (data) => {
	return http.get(bjnjUrl + '/api/screen/workCenter/getPlantingArea', data)
}

// 作业进度-机收面积
export const getReceiptArea = (data) => {
	return http.get(bjnjUrl + '/api/screen/workCenter/getReceiptArea', data)
}

// 作业进度-收获进度
export const getWorkProgressNew = (data) => {
	return http.get(bjnjUrl + '/api/screen/workCenter/getWorkProgressNew', data)
}

// 作业进度-作业进度实况信息
export const queryAreaByLevel = (data) => {
	return http.get(bjnjUrl + '/areaInfo/queryAreaByLevel', data)
}

// 作业进度-地图进度展示
export const mapScheduleInfo = (data) => {
	return http.get(bjnjUrl + '/api/screen/workCenter/mapScheduleInfo', data)
}

// 作业进度-作业进度实况列表查询
export const workScheduleList = (data) => {
	return http.get(bjnjUrl + '/api/screen/workCenter/workScheduleList', data)
}

// 作业进度-作业进度实况详情
export const workScheduleInfo = (data) => {
	return http.get(bjnjUrl + '/api/screen/workCenter/workScheduleInfo', data)
}

// 作业进度-双抢作业进度
export const bkcgobjTDK = (data) => {
	return http.get(bjnjUrl + '/api/screen/bkcgobjTDK', data)
}

// 三夏作业数据
export const api_getSanxWorkData = (data) => {
	return http.get(bjnjUrl + '/api/screen/nFhQFeKLwN', data)
}

export const xnrEpNKvpj = (data) => {
	return http.get(bjnjUrl + '/api/screen/xnrEpNKvpj', data)
}
 
export const dhACIovrQh = (data) => {
	return http.get(bjnjUrl + '/api/screen/dhACIovrQh', data)
}

export const getAlarmByProvince = (data) => {
	return http.post(bjnjUrl + '/api/weather/getAlarmByProvince', data)
}

export const getAlarmStasticsByProvince = (data) => {
	return http.post(bjnjUrl + '/api/weather/getAlarmStasticsByProvince', data)
}

//获取三夏农时全国各省的数据
export const api_getHighlightMultiArea = (data)=>{
	return http.get(bjnjUrl + '/api/screen/hmbeGGEaGx', data)
}
