<template>
  <div v-if="show">
    <div class="ai_waring">
      <div class="title">
        <span>跨区作业</span>
      </div>
      <div class="close_btn" @click="closeEmitai"></div>
      <div class="content">
        <LeaMap ref="leafletMap1" @poiClick="poiClick" :clear2="clear2" :back2="back2" :xzpd="xzpd" />
        <!-- <div class="info" v-if="njInfoShow">
          <ul>
            <li v-for="(item, index) in njInfo" :key="index">
              <span class="name">{{ item.name }}：</span>
              <span class="value">{{ item.value }}{{ item.unit }}</span>
            </li>
          </ul>
        </div> -->
      </div>
      <div class="tjxxBox" :class="areaLevel == 0 ? 'tjxxBox1' : ''">
        <div class="tjxxItem" :class="(areaLevel == 0 && index == 2) || ((areaLevel == 1 || areaLevel == 2 || areaLevel == 3) && (index == 0 || index == 1)) ? 'tjxxItem1' : ''" v-for="(item, index) of tjxxList" :key="index">
          <div class="tjxxName">{{item.name}}</div>
          <div class="tjxxNum">{{item.value}}<span>{{item.unit}}</span></div>
        </div>
      </div>
      <div class="njtlList" v-if="areaLevel != 0">
        <div class="njtlItem">
          <img src="@/assets/bjnj/kqzyImg1.png" alt="">
          <div class="njtlName">跨出作业农机</div>
        </div>
        <div class="njtlItem">
          <img src="@/assets/bjnj/kqzyImg2.png" alt="">
          <div class="njtlName">跨入作业农机</div>
        </div>
      </div>
      <!-- <div class="info">
        <ul>
          <li v-for="(item, index) in njInfo" :key="index">
            <span class="name">{{ item.name }}：</span>
            <span class="value">{{ item.value }}{{ item.unit }}</span>
          </li>
        </ul>
      </div> -->
    </div>
  </div>
</template>

<script>
import LeaMap from '@/components/map/LeafletMapNj.vue'
export default {
  name: 'mapPop',
  mixins: [],
  props: {
    value: {
      type: Boolean,
      default: false
    },
    njInfoShow:{
      type: Boolean,
      default: true
    }
  },
  components: {
    LeaMap
  },
  data() {
    return {
      back2: false,
      clear2: false,
      xzpd: false,
      njInfo: [
        {
          name: '农机品目',
          value: '履带式拖拉机',
          unit: ''
        },
        {
          name: '农机型号',
          value: '-',
          unit: ''
        },
        {
          name: '联系人',
          value: '-',
          unit: ''
        },
        {
          name: '联系方式',
          value: '-',
          unit: ''
        },
        {
          name: '所属单位',
          value: '-',
          unit: ''
        },
        {
          name: '作业时间',
          value: '-',
          unit: ''
        },
        {
          name: '运行时长',
          value: '-',
          unit: 'h'
        },
        {
          name: '运行里程',
          value: '-',
          unit: '公里'
        },
        {
          name: '作业面积',
          value: '-',
          unit: '亩'
        }
      ],
      tjxxList: [
        {
          name: '跨出作业总数',
          value: 0,
          unit: '台',
        },
        {
          name: '跨入作业总数',
          value: 0,
          unit: '台',
        },
        {
          name: '跨区作业总数',
          value: 0,
          unit: '台',
        },
      ],
      areaLevel: 0,
    }
  },
  computed: {
    show() {
      return this.value
    }
  },
  mounted() {},
  methods: {
    track1(trackData) {
      let data = trackData.map(item => {
        if (item.lat && item.lon) {
          return {
            latlng: [item.lat, item.lon],
            icon: {
              iconUrl: require('@/assets/bjnj/kqzyImg2.png'),
              iconSize: [48, 62],
              iconAnchor: [16, 42]
            },
            props: {
              id: 'kqzyId1',
              type: 'kqzyType1',
              info: { ...item }
            }
          }
        }
      })
      this.$refs.leafletMap1.drawPoiMarkerCustomCluster(data, 'kqzyType1', true, false, false, {
        largeIconUrl: './imgs/cluster/qy1.png',
        mediumIconUrl: './imgs/cluster/qy2.png',
        smallIconUrl: './imgs/cluster/qy3.png'
      })
    },
    track2(trackData) {
      let data = trackData.map(item => {
        if (item.lat && item.lon) {
          return {
            latlng: [item.lat, item.lon],
            icon: {
              iconUrl: require('@/assets/bjnj/kqzyImg1.png'),
              iconSize: [48, 62],
              iconAnchor: [16, 42]
            },
            props: {
              id: 'kqzyId2',
              type: 'kqzyType2',
              info: { ...item }
            }
          }
        }
      })
      this.$refs.leafletMap1.drawPoiMarkerCustomCluster(data, 'kqzyType2', true, false, false, {
        largeIconUrl: './imgs/cluster/zx1.png',
        mediumIconUrl: './imgs/cluster/zx2.png',
        smallIconUrl: './imgs/cluster/zx3.png'
      })
    },
    exitCount(tjxxList,areaLevel) {
      this.areaLevel = areaLevel 
      console.log('areaLevel',areaLevel)
      this.tjxxList[0].value = tjxxList.exitCount
      this.tjxxList[1].value = tjxxList.entryCount
      this.tjxxList[2].value = tjxxList.entryCount
    },
    closeEmitai() {
      this.$emit('input', false)
    },
    poiClick(layerId, it) {
      console.log('layerId',layerId)
      console.log('it',it)
      this.$emit('poiClick', layerId, it)
    },
    // getNjInfo(data) {
    //   console.log('data+++++++++++++++', data)
    //   const { agmachTypeName, mdlName, userName, phoneNo, workStartTime, workTime, workArea } = data
    //   this.njInfo[0].value = agmachTypeName || '-'
    //   this.njInfo[1].value = mdlName && mdlName != 'null' ? mdlName : '-'
    //   this.njInfo[2].value = userName || '-'
    //   this.njInfo[3].value = phoneNo || '-'
    //   this.njInfo[4].value = '-'
    //   this.njInfo[5].value = workStartTime || '-'
    //   this.njInfo[6].value = workTime || '-'
    //   this.njInfo[7].value = '-'
    //   this.njInfo[8].value = workArea || '-'
    // }
  }
}
</script>

<style lang="less" scoped>
.ai_waring {
  width: 1535px;
  height: 900px;
  background: rgba(9, 19, 34, 0.95);
  box-shadow: 0px 0px 22px 0px rgba(11, 54, 138, 0.35), 0px 2px 8px 0px rgba(0, 0, 0, 0.7),
    inset 0px 0px 8px 0px rgba(17, 146, 214, 0.35);
  border-radius: 11px;
  border: 1px solid #015c8c;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1200;

  .title {
    margin: 0 auto 0;
    width: 635px;
    height: 76px;
    line-height: 76px;
    background: url(~@/assets/map/dialog/title2.png) no-repeat;
    display: grid;
    place-items: center;

    span {
      display: inline-block;
      font-size: 36px;
      font-family: PingFangSC, PingFang SC;
      background: linear-gradient(180deg, #ffffff 40%, #0079ff 70%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .close_btn {
    position: absolute;
    width: 23px;
    height: 23px;
    top: 25px;
    right: 40px;
    background: url(~@/assets/map/dialog/btn3.png) no-repeat;
    cursor: pointer;
  }

  .content {
    position: relative;
    height: calc(100% - 120px);
    width: 1428px;
    margin-left: 40px;
    .info {
      position: absolute;
      bottom: 0;
      // height: 150px;
      padding: 20px;
      width: 1428px;
      background: rgba(0,0,0,0.6);
      border-radius: 0px 0px 8px 8px;
      backdrop-filter: blur(5px);
      ul {
        width: 100%;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        // padding-top: 10px;

        li {
          text-align: left;
          width: 33.3%;
          margin-bottom: 10px;
          padding-left: 33px;
          display: flex;
          align-items: center;
          // justify-content: space-between;
          .name {
            height: 17px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: rgba(106, 146, 187, 1);
            line-height: 17px;
            text-align: center;
            font-style: normal;
          }

          .value {
            height: 17px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #ffffff;
            line-height: 17px;
            text-align: center;
            font-style: normal;
          }
        }
      }
    }
  }
  .tjxxBox {
    width: 400px;
    height: 74px;
    position: absolute;
    top: 124px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1003;
    .tjxxItem {
      width: 180px;
      height: 74px;
      margin: 0 10px;
      float: left;
      display: none;
      .tjxxName {
        width: 180px;
        height: 36px;
        background: url(~@/assets/bjnj/tjxxBg.png) no-repeat center / 100% 100%;
        font-family: PingFangSC, PingFang SC;
        font-size: 18px;
        color: #DFF3FF;
        line-height: 36px;
        text-align: center;
        font-style: normal;
      }
      .tjxxNum {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 26px;
        color: #FFFFFF;
        line-height: 38px;
        text-shadow: 0px 0px 1px rgba(0,18,57,0.84);
        text-align: center;
        font-style: normal;
        span {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #FFFFFF;
          line-height: 20px;
          text-shadow: 0px 0px 1px rgba(0,18,57,0.84);
          text-align: left;
          font-style: normal;
        }
      }
    }
    .tjxxItem1 {
      display: block;
    }
  }
  .tjxxBox1 {
    width: 200px;
  }
  .njtlList {
    width: 210px;
    height: 150px;
    position: absolute;
    bottom: 40px;
    right: 60px;
    z-index: 1099;
    background: url(~@/assets/bjnj/njtlBg.png) no-repeat center / 100% 100%;
    padding-top: 33px;
    .njtlItem {
      width: 100%;
      height: 38px;
      margin-bottom: 10px;
      padding-left: 35px;
      img {
        width: 31px;
        height: 38px;
        float: left;
      }
      .njtlName {
        float: left;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 38px;
        text-align: left;
        font-style: normal;
        margin-left: 16px;
      }
    }
  }
}
</style>
