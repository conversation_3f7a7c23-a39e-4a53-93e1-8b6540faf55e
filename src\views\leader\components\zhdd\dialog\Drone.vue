<template>
  <div v-if="show">
    <div class="ai_waring">
      <div class="title">
        <span>无人机列表</span>
      </div>
      <div class="close_btn" @click="closeEmitai"></div>
      <div class="content">
        <div class="check_types">
          <div class="jjcd">
            <span>查询条件：</span>
            <el-input v-model="inputValue" placeholder="设备名称/设备编号"></el-input>
          </div>
          <div class="type_btns">
            <div>查询</div>
            <div>重置</div>
          </div>
        </div>
        <div class="table_box">
          <DroneTable
            :titles="['序号', '设备名称', '设备编号', '状态', '操作']"
            :widths="['15%', '20%', '20%', '15%', '30%']"
            :data="list"
            :contentHeight="'500px'"
            @handleClick="handleClick"
          ></DroneTable>
        </div>
        <div class="fy_page">
          <Page :total="100"></Page>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DroneTable from '../table/DroneTable.vue'
import { getDroneToken, getDroneList, getCameraMakers, getCameraXq } from '@/api/hs/hs.api.js'

export default {
  name: 'Drone',
  components: { DroneTable },
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      inputValue: '',
      tabActive: 0,
      tabs: ['应急队伍', '机具保有量', '应急车辆', '医疗机构', '避难场所'],
      btnsArr: [
        {
          name: '未办结',
          normalBg: require('@/assets/shzl/map/left_n.png'),
          activeBg: require('@/assets/shzl/map/left_a.png')
        },
        {
          name: '已办结',
          normalBg: require('@/assets/shzl/map/right_n.png'),
          activeBg: require('@/assets/shzl/map/right_a.png')
        }
      ],
      list: [],
      proityValue: '',
      timeOutValue: '',
      settled: false,
      cameraUrl: '',
      cameraCode: ''
    }
  },
  computed: {
    show() {
      return this.value
    }
  },
  methods: {
    closeEmitai() {
      this.$emit('input', false)
    },
    dealMethod() {
      console.log(1111)
      this.$parent.dealMethod()
    },
    dwMethod() {
      this.$parent.dwMethod()
    },
    changeTab(i) {
      this.tabActive = i
      i === 0 ? (this.settled = true) : (this.settled = false)
    },
    handleClick(i, it) {
      if (i === 0) {
        this.$emit('markDrone', i, it)
      } else if (i === 2) {
        // 开启视频
        this.$emit('openWrj', i, it)
      }
    },
    async getToken() {
      try {
        const res = await getDroneToken()
        console.log('getDroneToken',res);
        if (res?.code === 200) {
          // this.token = res.token
          this.token = res.result
          this.getList()
        }
      } catch (e) {
        this.list = [
          [
            '1',
            '水务-无人机',
            'SW-001',
            '正常',
            'http://1257120875.vod2.myqcloud.com/0ef121cdvodtransgzp1257120875/3055695e5285890780828799271/v.f230.m3u8'
          ]
        ]
        this.$emit('pushDroneCount', 0)
      }
    },
    async getList() {
      const res = await getDroneList(this.token)
      console.log('getDroneList',res);
      if (res?.code === 200 && res.data?.length > 0) {
        this.list = res.data.map((it, i) => [i + 1, it.uavName, it.recordId, '正常', it.hls])
        this.$emit('pushDroneCount', this.list.length)
      } else {
        this.list = [
          [
            '1',
            '水务-无人机',
            'SW-001',
            '正常',
            // this.cameraUrl
            'http://1257120875.vod2.myqcloud.com/0ef121cdvodtransgzp1257120875/3055695e5285890780828799271/v.f230.m3u8'
          ]
        ]
        this.$emit('pushDroneCount', 0)
      }
    },
    async cameraMarker() {
      const res = await getCameraMakers()
      console.log('res', res)
      if (res && res.length > 0) {
        this.cameraCode = res[5].id
        this.cameraXq(res[5].id)
      } else {
        this.$message({
          message: '未查询到事件信息',
          type: 'warning'
        })
      }
    },
    async cameraXq(id) {
      const res = await getCameraXq(id)
      if (res && res.body && res.body.data && res.body.data.length > 0 && res.body.data[0].url) {
        this.cameraUrl = res.body.data[0].url
        console.log('this.cameraUrl', this.cameraUrl)
        // this.cameraCode = id //传给hls组件，当视频播放失败时再次调用
        
      }
    }
  },
  mounted() {
    this.cameraMarker()
    this.getToken()
  }
}
</script>

<style lang="less" scoped>
.bg-header {
  position: absolute;
  width: 100%;
  height: 1080px;
  background: rgba(2, 14, 50, 0.75);
  z-index: 999999;
  top: 0;
  left: 0;
}
/deep/ .ivu-select {
  width: 168px;
}
/deep/ .ivu-select-selection {
  width: 168px;
  height: 34px;
  background: rgba(0, 74, 143, 0.4)
    linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
}
/deep/ .ivu-select-selection .ivu-select-selected-value {
  font-size: 13px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 34px;
}

/deep/ .ivu-input {
  background: rgba(0, 74, 143, 0.4)
    linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 22px;
}
/deep/ .ivu-select-placeholder {
  font-size: 13px !important;
  line-height: 34px !important;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff !important;
}
/deep/ .ivu-page-item {
  background: transparent;
  color: #fff;
}
/deep/ .ivu-page-item a {
  color: #fff;
}
/deep/ .ivu-page-prev,
/deep/ .ivu-page-next {
  background: transparent;
}
.ai_waring {
  width: 1435px;
  height: 759px;
  background: rgba(9, 19, 34, 0.95);
  box-shadow: 0px 0px 22px 0px rgba(11, 54, 138, 0.35), 0px 2px 8px 0px rgba(0, 0, 0, 0.7),
    inset 0px 0px 8px 0px rgba(17, 146, 214, 0.35);
  border-radius: 11px;
  border: 1px solid #015c8c;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1100;
  .title {
    margin: 0 auto 23px;
    width: 635px;
    height: 76px;
    line-height: 76px;
    background: url(~@/assets/map/dialog/title2.png) no-repeat;
    display: grid;
    place-items: center;
    span {
      font-size: 36px;
      font-family: PingFangSC, PingFang SC;
      line-height: 36px;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .tabs {
    display: flex;
    gap: 13px;
    margin: 28px 0 42px 28px;
    .tab {
      width: 191px;
      height: 41px;
      line-height: 41px;
      background: url('~@/assets/map/dialog/bg5.png') no-repeat center / 100% 100%;
      cursor: pointer;
      span {
        font-size: 24px;
        font-family: PingFangSC, PingFang SC;
        color: #ffffff;
      }
      &.active {
        background: url('~@/assets/map/dialog/bg4.png') no-repeat center / 100% 100%;
        span {
          font-size: 30px;
          font-family: PingFangSC, PingFang SC;
          background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
  }
  .close_btn {
    position: absolute;
    width: 23px;
    height: 23px;
    top: 25px;
    right: 40px;
    background: url(~@/assets/map/dialog/btn3.png) no-repeat;
    cursor: pointer;
  }
  .content {
    padding: 0 30px;
    .btns {
      // border: 1px solid red;
      margin: 0 auto;
      display: flex;
      justify-content: center;
      & > div {
        width: 154px;
        height: 53px;
        span {
          font-size: 24px;
          font-family: PingFangSC, PingFang SC;
          color: #ffffff;
          line-height: 53px;
          background: linear-gradient(180deg, #ffffff 0%, #ffeba0 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
    .check_types {
      text-align: left;
      display: flex;
      .jjcd {
        display: flex;
        align-items: center;
        margin-right: 24px;
        span {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
        }
      }
      .report_time {
        margin-left: 26px;
        display: flex;
        align-items: center;
        span {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
      }
      .istime_out {
        margin-left: 26px;
        align-items: center;
        display: flex;
        span {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
      }
      .type_btns {
        margin-left: 27px;
        display: flex;
        align-items: center;
        & div {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 43px;
          background: url(~@/assets/shzl/map/type_btn.png) no-repeat center / 100% 100%;
          background-size: 100% 100%;
          text-align: center;
          cursor: pointer;
          &:first-of-type {
            width: 98px;
            height: 43px;
          }
          &:nth-of-type(2) {
            margin-left: 20px;
            width: 98px;
            height: 43px;
          }
          &:nth-of-type(3) {
            margin-left: 20px;
            width: 98px;
            height: 43px;
          }
        }
      }
    }
    .table_box {
      margin-top: 31px;
      overflow: hidden;
    }
    .fy_page {
      margin-top: 20px;
    }
  }
}
/deep/ .el-input {
  width: 200px;
  height: 34px;
  .el-input__inner {
    height: 34px;
    background: rgba(0, 74, 143, 0.4)
      linear-gradient(
        360deg,
        rgba(181, 223, 248, 0) 0%,
        rgba(29, 172, 255, 0.29) 100%,
        #ffffff 100%
      );
    border: 1px solid rgba(0, 162, 255, 0.6);
    color: #fff;
    padding: 0 30px 0 15px;
  }
}
</style>
