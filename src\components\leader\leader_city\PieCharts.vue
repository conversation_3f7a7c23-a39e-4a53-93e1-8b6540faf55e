<template>
  <div class="chart-warrper">
    <div class="chart-box">
      <div class="chart" ref="chart"></div>
    </div>
    <!-- <div class="box-item item-flex">
      <div
        class="chart-legend"
        v-for="(o, idx) in sourceData"
        :class="{ active: idx === currentActiveIndex }"
        :key="`${o.name}-${idx}`"
        @mouseenter="handleItemMouseover(idx)"
      >
        <div class="legend-title">{{ (o.value / totalCount).toFixed(2) * 100 }}%</div>
        <div class="legend-data">
          <span></span>
          <span>{{ o.name }}</span>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script>
import echarts from 'echarts';
import ResizeListener from 'element-resize-detector';
export default {
  name: 'PieHoverChart',
  data() {
    return {
      option: null,
      myChart: null,
      currentActiveIndex: 0,
      timer: null,
      totalCount: 0
    };
  },
  props: {
    data: {
      type: Array,
      default: () => []
    },
    type: {
      type: Number,
      default: 1
    },
    text: {
      type: String,
      default: '来源分析'
    },
    x: {
      type: String,
      default: '70'
    },
    isOpenTimer: {
      type: Boolean,
      default: false
    },
    sourceData: {
      type: Array,
      default: () => [
        { name: '问题1', value: 21 },
        { name: '问题2', value: 21 },
        { name: '问题3', value: 21 },
        { name: '问题4', value: 21 },
        { name: '问题5', value: 21 },
        { name: '问题6', value: 21 }
      ]
    },
    chartOption: {
      type: Object,
      default: () => ({})
    }
  },
  beforeDestroy() {
    this.myChart.clear();
    this.myChart.dispose();
    clearInterval(this.timer);
  },
  async mounted() {
    console.log(this.sourceData);
    const total = this.sourceData.reduce((pre, item) => pre + item.value, 0);
    const formatterLegend = name => {
      const value = this.sourceData.find(item => item.name === name).value;
      const rate = ((value / total) * 100).toFixed(1) + '%';
      if (this.type == 1) {
        return `{label|${name}}{value|${value}}{rate|${rate}}`;
      } else if (this.type == 2) {
        return `{label|${name}}`;
      }
    };
    this.sourceData.map(item => {
      this.totalCount += parseInt(item.value);
    });
    console.log(this.totalCount);

    this.option = {
      color: [
        '#AEE97B',
        '#F7B13F',
        '#F9E164',
        '#3785FE',
        '#37FEE7',
        '#8A71FF',
        '#9de2ff',
        '#b3421b'
      ],
      title: {
        text: this.text,
        textStyle: {
          color: '#fff',
          fontSize: 24,
          align: 'center',
          fontWeight: 500
        },
        x: this.x,
        y: 'center'
      },
      grid: {
        bottom: 0,
        left: 0,
        right: 0,
        top: 0
      },
      legend: {
        orient: 'vertical',
        top: 'center',
        left: '50%',
        icon: 'roundRect',
        itemWidth: 9,
        itemHeight: 8,
        data: this.sourceData,
        textStyle: {
          color: `rgba(139, 199, 255, 1)`,
          rich: {
            label: {
              width: 70,
              color: '#fff',
              fontSize: 14
            },
            value: {
              width: 40,
              color: '#fff',
              fontSize: 18,
              fontWeight: 'normal',
              fontFamily: 'DINPro-Medium'
            },
            rate: {
              fontSize: 18,
              color: '#fff',
              fontWeight: 'normal',
              fontFamily: 'DINPro-Medium'
            }
          }
        },
        formatter: formatterLegend
      },
      series: [
        // 主要展示层的
        {
          radius: ['54', '84'],
          center: ['119', '50%'],
          type: 'pie',
          label: {
            normal: {
              show: false
            },
            emphasis: {
              show: false
            }
          },
          labelLine: {
            normal: {
              show: true,
              length: 30,
              length2: 55
            },
            emphasis: {
              show: true
            }
          },
          itemStyle: {
            borderWidth: 5,
            borderColor: '#000'
          },
          name: '来源分析',
          data: this.sourceData
        },
        // 边框的设置
        {
          radius: ['44', '64'],
          center: ['119', '50%'],
          type: 'pie',
          label: {
            normal: {
              show: false
            },
            emphasis: {
              show: false
            }
          },
          labelLine: {
            normal: {
              show: false
            },
            emphasis: {
              show: false
            }
          },
          animation: false,
          tooltip: {
            show: false
          },
          data: [
            {
              value: 1,
              itemStyle: {
                color: 'rgba(0,0,0,0.3)'
              }
            }
          ]
        }
        // {
        //   name: '外边框',
        //   type: 'pie',
        //   clockWise: false, //顺时加载
        //   hoverAnimation: false, //鼠标移入变大
        //   center: ['119', '50%'],
        //   radius: ['99', '99'],
        //   label: {
        //     normal: {
        //       show: false
        //     }
        //   },
        //   data: [
        //     {
        //       value: 9,
        //       name: '',
        //       itemStyle: {
        //         normal: {
        //           borderWidth: 2,
        //           borderColor: 'RGBA(18, 34, 68, 1)'
        //         }
        //       }
        //     }
        //   ]
        // }
      ]
    };
    this.myChart = echarts.init(this.$refs.chart);
    this.myChart.setOption(this.option, true);
    // this.myChart.on('mouseover', params => {
    //   this.handleItemMouseover(params.dataIndex);
    // });
    // this.handleItemMouseover(this.currentActiveIndex);
    // this.addChartResizeListener();
  },
  methods: {
    handleItemMouseover(i) {
      this.myChart.dispatchAction({
        type: 'downplay',
        seriesIndex: 0,
        dataIndex: this.currentActiveIndex
      });
      this.currentActiveIndex = i;
      this.myChart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: i
      });
    },
    addChartResizeListener() {
      const instance = ResizeListener({
        strategy: 'scroll',
        callOnAdd: true
      });

      instance.listenTo(this.$el, () => {
        if (!this.myChart) return;
        this.myChart.resize();
      });
    },
    openTimer() {
      let _count = 1;
      setTimeout(() => {
        this.timer = setInterval(() => {
          if (_count > 1) {
            _count = 0;
          }
          this.handleItemMouseover(_count);
          _count++;
        }, 1000);
      }, 1000);
    }
  },
  watch: {
    data(newVal) {
      this.option.series[0].data = newVal;
      // 更新之前先清空图表 不然会有数字重叠的问题
      this.myChart.clear();
      this.myChart.setOption(this.option, true);
      this.handleItemMouseover(0);
    },
    isOpenTimer: {
      immediate: true,
      handler(newValue) {
        if (newValue) {
          this.openTimer();
        } else {
          if (this.timer) {
            clearInterval(this.timer);
          }
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.chart-warrper {
  width: 100%;
  height: 100%;
  // display: flex;
  .chart-box {
    flex: 0 0 70%;
    height: 100%;
    .chart {
      height: 100%;
    }
  }
  .box-item {
    display: flex;
    flex: 1;
    height: 100%;
    flex-direction: column;
    justify-content: space-evenly;
    box-sizing: border-box;
    padding: 10px;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.7);
    .chart-legend {
      font-size: 16px;
      position: relative;
      height: 30%;
      width: 100%;
      padding-left: 1px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .legend-title {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #1e92bc;
        font-size: 18px;
        font-weight: 500;
      }
      .legend-data {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;

        span {
          display: inline-block;
          &:nth-child(1) {
            width: 12px;
            height: 12px;
            background-color: #1e92bc;
            margin-right: 10px;
          }
        }
      }
      &:nth-child(2) {
        .legend-title {
          color: #31dcc9;
        }
        .legend-data {
          span {
            &:nth-child(1) {
              background-color: #31dcc9;
            }
          }
        }
      }
    }
  }
}
</style>
