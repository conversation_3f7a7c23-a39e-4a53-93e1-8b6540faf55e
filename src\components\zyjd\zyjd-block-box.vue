<template>
	<div class="child-block">
		<div class="content">
			<div class="data-item ">
				<div class="label-box">总面积({{ faceUnit }})：</div>
				<div class="number-box">{{ contentData.totalFace }}</div>
			</div>
			<div class="data-item ">
				<div class="left-box">
					<div class="label-box">已收面积({{ faceUnit }})：</div>
					<div class="number-box">{{ contentData.collectedArea }}</div>
				</div>
				<div class="right-box">
					<div class="label-box">麦收进度：</div>
					<div class="number-box">{{ contentData.harvestProgress }}%</div>
				</div>
			</div>
			<div class="data-item ">
				<div class="left-box">
					<div class="label-box">机收面积({{ faceUnit }})：</div>
					<div class="number-box">{{ contentData.machFace }}</div>
				</div>
				<div class="right-box">
					<div class="label-box">机收占比：</div>
					<div class="number-box">{{ contentData.machPercent }}%</div>
				</div>
			</div>
			<div class="data-item ">
				<div class="left-box">
					<div class="label-box">当日投入收割机({{ machUnit }})：</div>
					<div class="number-box">{{ contentData.currentDayPutIntoShougeji }}</div>
				</div>
				<div class="right-box">
					<div class="label-box">当日在线收割机({{ machUnit }})：</div>
					<div class="number-box">{{ contentData.currentOnlineShougeji }}</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { type } from 'jquery'

export default {
	props: {
		contentData: {
			type: Object,
			default: () => {
				return {
					totalFace: 0, //总面积
					collectedArea: 0, //已收面积
					harvestProgress: 0, //麦收进度
					machFace: 0, //机收面积
					machPercent: 0, //机收占比
					currentDayPutIntoShougeji: 0, //当日投入收割机
					currentOnlineShougeji: 0, //当日在线收割机
				}
			},
		},
		faceUnit: {
			type: String,
			default: '万亩',
		},
		machUnit: {
			type: String,
			default: '万台',
		},

		blockHeight: {
			type: Number,
			default: 683,
		},
	},
	data() {
		return {}
	},
	computed: {},
	methods: {},

	watch: {},
}
</script>

<style lang="less" scoped>
.child-block {
	width: 446px;
	height: 100%;
	box-sizing: border-box;
	z-index: 101;
	position: relative;
	padding: 10px 16px 0 16px;
	.content {
		position: relative;
		width: 100%;
		// height: calc(100% - 74px);
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;

		&.no-active-content {
			height: 0;
			display: none;
		}

		& > .data-item:nth-child(odd) {
			background: linear-gradient(90deg, rgba(16, 40, 73, 0) 0%, #102849 51%, rgba(16, 40, 73, 0) 100%);
			border-radius: 0px 0px 0px 0px;
			border: 0px solid;
			border-image: linear-gradient(90deg, rgba(0, 145, 255, 0), rgba(0, 145, 255, 0.6), rgba(0, 145, 255, 0)) 0 0;
		}
		& > .data-item:nth-child(even) {
		}

		.data-item {
			width: 100%;
			height: 44px;
			// height: 40px;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			cursor: pointer;
			.label-box {
				font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
				font-weight: normal;
				font-size: 14px;
				color: #b0e0ff;
				text-align: left;
				font-style: normal;
				text-transform: none;
				white-space: nowrap;
			}
			.number-box {
				font-family: PangMenZhengDao-3, PangMenZhengDao-3;
				font-weight: 400;
				font-size: 18px;
				color: #dbf1ff;
				text-shadow: 0px 0px 10px #27a6ff;
				text-align: left;
				font-style: normal;
				text-transform: none;
				white-space: nowrap;
			}
			.left-box {
				width: 50%;
				height: 100%;
				display: flex;
				justify-content: flex-start;
				align-items: center;
			}
			.right-box {
				width: 50%;
				height: 100%;
				display: flex;
				justify-content: flex-end;
				align-items: center;
			}
		}

		.data-item:hover {
			// background: url(~@/assets/img/block-box/block-box-child-item-active-bg.png);
			/* 背景不重复 */
			background-repeat: no-repeat;
			/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
			background-size: cover;
			/* 背景居中显示（可选） */
			background-position: center;
		}
	}

	.alert-empty {
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		font-family: PangMenZhengDao-3, PangMenZhengDao-3;
		font-weight: 500;
		font-size: 20px;
		color: #dbf1ff !important;
		text-shadow: 0px 0px 10px #bddcf1 !important;
		text-align: center;
		font-style: normal;
		text-transform: none;
	}

	.empty-box {
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		.zwsjImg {
			width: 240px;
			margin: 20px 0;
		}
	}
}

.popDropDown {
	::v-deep .el-dropdown {
		color: #caecff;
		font-family: YouSheBiaoTiHei;
		font-size: 16px;
		line-height: 21px;
		text-align: right;
		font-style: normal;
		line-height: 40px;
		right: 0;
		position: absolute;
	}
	::v-deep .el-dropdown-menu {
		background-color: #00173b !important;
		border: 1px solid #2b7bbb !important;
	}
	.el-dropdown-menu__item {
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
		line-height: 40px;
		padding: 0 30px;
		&:hover {
			background: #185493;
			color: rgba(255, 255, 255, 0.8);
		}
	}
}
</style>
