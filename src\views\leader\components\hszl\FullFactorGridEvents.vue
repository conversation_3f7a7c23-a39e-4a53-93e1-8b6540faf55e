<template>
  <div class="wrap">
    <div class="info">
      <div class="count">{{ total }}</div>
      <div class="sign">
        <div class="label">总数/件</div>
        <img class="icon" src="@/assets/hszl/icon14.png" alt />
      </div>
    </div>
    <div class="chart-wrap">
      <LineColumnarChart
        :data="chartData"
        :options="optionsData"
        :init-option="initOption"
        v-if="chartData.length > 0"
      ></LineColumnarChart>
      <img class="light" src="@/assets/shzl/zhuzhuangtu_lz.png" alt />
    </div>
  </div>
</template>

<script>
export default {
  name: 'FullFactorGridEvents',
  props: {
    total: {
      type: Number,
      default: 0,
    },
    chartData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      // total: 706,
      // chartData: [
      //   ['product', '数量'],
      //   ['城市管理', 242],
      //   ['社会治安', 2],
      //   ['民生服务', 88],
      //   ['专项活动', 0],
      //   ['环境保护', 5],
      //   ['公共安全', 366],
      //   ['重要紧急', 3],
      // ],
      optionsData: {
        gradientColors: [['#00A3D7', '#99E4FA']],
        yAxis: {
          unit: '',
        },
        grid: {
          top: 10,
          left: 2,
          right: 2,
          bottom: '28%',
        },
        isToolTipInterval: true,
      },
    }
  },
  computed: {
    initOption() {
      return {
        xAxis: {
          axisLabel: {
            formatter: function (value) {
              // 将标签中的每两个字符之间插入一个换行符
              var lines = value.replace(/(.{2})/g, '$1\n')
              return lines
            },
          },
        },
        yAxis: {
          name: '',
        },
        tooltip: {
          formatter: (params) => {
            var relVal = params[0].name
            for (var i = 0, l = params.length; i < 1; i++) {
              relVal += '<br/>' + params[i].marker + params[i].value + ' 件'
            }
            return relVal
          },
        },
      }
    },
  },
}
</script>

<style lang="less" scoped>
.wrap {
  display: flex;
  width: 100%;
  height: 100%;
  padding-left: 8px;
  padding-top: 38px;
  .info {
    width: 146px;
    height: 100%;
    padding-top: 9px;
    .count {
      height: 26px;
      font-size: 22px;
      font-family: PingFangSC, PingFang SC;
      font-weight: normal;
      color: #ffffff;
      line-height: 26px;
      margin-bottom: 7px;
    }
    .sign {
      position: relative;
      width: 146px;
      height: 121px;
      background: url('~@/assets/hszl/bg6.png') no-repeat;
      .label {
        position: relative;
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
        top: -5px;
      }
      .icon {
        position: absolute;
        left: 50px;
        top: 17px;
        animation: move infinite 3s ease-in-out;
        @keyframes move {
          0% {
            transform: rotateY(0);
          }
          50% {
            transform: rotateY(180deg);
          }
          100% {
            transform: rotateY(360deg);
          }
        }
      }
    }
  }
  .chart-wrap {
    position: relative;
    width: 306px;
    height: 100%;
    padding: 0 0 10px;
    .light {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
    }
  }
}
</style>
