<template>
	<div class="echarts-box" ref="refChartContainer" :style="{ minHeight: minHeight + 'px' }" :id="eChartsId"></div>
</template>

<script>
import * as echarts from 'shzl-datav/node_modules/echarts'

export default {
	name: 'i-bar-chart',
	props: {
		xData: {
			type: Array,
			default() {
				return []
			},
		},
		yDatas: {
			type: Object,
			default() {
				return {}
			},
		},
		yUnitName: {
			type: String,
			default() {
				return ''
			},
		},
		xUnit: {
			type: String,
			default() {
				return '--'
			},
		},
		yUnit: {
			type: String,
			default() {
				return '--'
			},
		},
		formatterType: {
			type: String,
			default() {
				return ''
			},
		},
		top: {
			type: String,
			default() {
				return '26'
			},
		},
		bottom: {
			type: String,
			default() {
				return '0'
			},
		},
		right: {
			type: String,
			default() {
				return '20'
			},
		},
		left: {
			type: String,
			default() {
				return '10'
			},
		},
		itemColor: {
			type: String,
			default() {
				return '21, 154, 255'
			},
		},
		minHeight: {
			type: Number,
			default() {
				return 289
			},
		},
		minWidth: {
			type: String,
			default() {
				return '425px'
			},
		},
		chartsLegend: {
			type: Array,
			default() {
				return []
			},
		},
		isTooltipPositionDiy: {
			type: Boolean,
			default: false,
		},
		showBarBg: {
			type: Boolean,
			default: false,
		},
		xDataType: {
			type: String,
			default() {
				return ''
			},
		},
		dataType: {
			type: String,
			default() {
				return ''
			},
		},
		tooltipTitleName: {
			type: String,
			default() {
				return '农机数据'
			},
		},
		tabIndex: {
			type: Number,
			default() {
				return 0
			},
		},
	},
	data() {
		return {
			eChartsId: `id_charts_${String(Math.random()).slice(2)}`,
			myCharts: null,
			isOverWidth: false,
			yAxisMax: 0,
			option: {},
		}
	},
	watch: {
		xData: {
			handler() {
				this.watchDataChange()
			},
		},
		yDatas: {
			handler() {
				this.watchDataChange()
			},
			immediate: true,
		},
		tabIndex: {
			handler() {
				this.watchDataChange()
			},
		},
		minHeight: {
			handler() {
				this.watchDataChange()
			},
		},
	},
	mounted() {
		if (this.xData.length > 0 && Object.keys(this.yDatas).length > 0) {
			if (this.$refs.refChartContainer) {
				this.myCharts = echarts.init(this.$refs.refChartContainer)

				// 加载eCharts图表配置...
				this.myCharts.setOption(this.option)
			}
		}
	},
	computed: {
		// option() {
		// 	const that = this
		// 	return {
		// 		legend: {
		// 			show: true,
		// 			data: that.chartsLegend,
		// 		},
		// 		toolbox: {
		// 			show: true,
		// 			feature: {
		// 				// magicType: { type: ['line', 'bar'] },
		// 			},
		// 		},
		// 		tooltip: {
		// 			show: true,
		// 			trigger: 'axis',
		// 			className: 'custom-tooltip-box',
		// 			formatter: function(params) {
		// 				const { name, value: value_line } = params[0]
		// 				const nameText = `${name}`
		// 				const seriesName = params[0].seriesName
		// 				const htmlText = ''
		// 				//#region 农机类型为单选时
		// 				if (!that.isOverWidth) {
		// 					return `<div class="t-tooltip-box" ">
		// 								<div class="flag-leg"></div>
		// 								<div class="flag-content">
		// 									<div class="content-box">
		// 										<div class="t-text-box">
		// 											<div class="t-content-title"><div class="light"></div> ${nameText}</div>
		// 											<div class="t-content-value">${!that.tooltipTitleName ? '数量' : that.tooltipTitleName + '：'}${value_line} ${that.yUnit}</div>
		// 										</div>
		// 									</div>
		// 								</div>
		// 							</div>`
		// 				} else {
		// 					return `<div class="t-tooltip-convert-box" ">
		// 									<div class="flag-content">
		// 										<div class="content-box">
		// 											<div class="t-text-box">
		// 												<div class="t-content-title">${nameText} <div class="light"></div></div>
		// 												<div class="t-content-value">${!that.tooltipTitleName ? '数量' : that.tooltipTitleName + '：'}${value_line} ${that.yUnit}</div>
		// 											</div>
		// 										</div>
		// 									</div>
		// 									<div class="flag-leg"></div>
		// 							</div>`
		// 				}
		// 				//#endregion
		// 			},
		// 			// /*1.设置x轴左右固定，上下跟随。*/
		// 			// position: function (point, params, dom, rect, size) {
		// 			// 	return [0, point[1]];
		// 			// },
		// 			/*2.设置Y轴上下固定，X左右跟随。*/
		// 			position: function(point, params, dom, rect, size) {
		// 				// return [point[0], point[1]]
		// 				// 鼠标坐标和提示框位置的参考坐标系是：以外层div的左上角那一点为原点，x轴向右，y轴向下
		// 				var x = 0 // x坐标位置
		// 				var y = 0 // y坐标位置
		// 				var pointX = point[0]
		// 				var pointY = point[1]
		// 				// 提示框大小
		// 				var boxWidth = size.contentSize[0]
		// 				var boxHeight = size.contentSize[1]
		// 				// boxWidth > pointX 说明鼠标左边放不下提示框
		// 				if (boxWidth > pointX) {
		// 					x = point[0]
		// 					that.isOverWidth = false
		// 				} else {
		// 					that.isOverWidth = true
		// 					// 左边放的下
		// 					x = pointX - boxWidth + 5
		// 				}
		// 				// boxHeight > pointY 说明鼠标上边放不下提示框
		// 				if (boxHeight > pointY) {
		// 					y = 5
		// 				} else {
		// 					// 上边放得下
		// 					y = pointY - boxHeight
		// 				}
		// 				if (that.isTooltipPositionDiy) {
		// 					return [x, y]
		// 				} else {
		// 					return [point[0], point[1]]
		// 				}
		// 			},
		// 		},
		// 		grid: {
		// 			minheight: that.minHeight,
		// 			minWidth: that.minWidth,
		// 			// 是否显示直角坐标系内网格
		// 			show: false,
		// 			// 是否显示直角坐标系内边框
		// 			borderWidth: 1,
		// 			// 直角坐标系内边框颜色
		// 			borderColor: 'rgba(0,0,0,0)',
		// 			// 直角坐标系内背景色，只在设置了 backgroundColor 的情况下有效
		// 			backgroundColor: 'rgba(0,0,0,0)', // 透明背景
		// 			// 直角坐标系的位置，默认左上角为原点 (0, 0)
		// 			left: that.left,
		// 			right: that.right,
		// 			bottom: that.bottom,
		// 			top: that.top,
		// 			// 是否包含坐标轴的标签在内
		// 			containLabel: true,
		// 			// 网格的宽高比
		// 			// ... 其他 grid 配置项
		// 		},
		// 		backgroundColor: 'rgba(0, 0, 0, 0)',
		// 		xAxis: {
		// 			type: 'category',
		// 			boundaryGap: true,
		// 			data: that.xData,
		// 			splitLine: {
		// 				show: false,
		// 				lineStyle: {
		// 					color: 'rgba(78,89,105,0.31)',
		// 					type: 'dashed', // 设置网格线为虚线
		// 					width: 1,
		// 				},
		// 			},
		// 			axisTick: {
		// 				show: false,
		// 				lineStyle: {
		// 					color: '#4F6274',
		// 				},
		// 				alignWithLabel: true,
		// 			},
		// 			axisLabel: {
		// 				interval: that.dataType == 'njData' ? 0 : that.xData.length > 32 ? 52 : that.xData.length > 7 ? 5 : 0,
		// 				color: '#B0E0FF', // 文本颜色
		// 				fontSize: 12, // 字体大小
		// 				lineHeight: 14,
		// 				textWrap: 'wrap',
		// 				// 设置文本旋转角度
		// 				formatter: function(value, index) {
		// 					// if (that.formatterType == 'qrzx' || that.formatterType == 'qrzy') {
		// 					// 	const labelText = `${value.slice(0, 4)}${value.slice(4)}`
		// 					// 	// 使用字符串的split和join方法，将数据按换行符分割，并重新合并
		// 					// 	return labelText
		// 					// } else {
		// 					// 	// const isLengthOver = value.length > 3
		// 					// 	// const labelText = `${value.slice(0, 3)}${isLengthOver ? '...' : ''}`
		// 					// 	const isLengthOver = value.length > 4
		// 					// 	const labelText = `${value.slice(0, 4)}\n${value.slice(4)}`
		// 					// 	// 使用字符串的split和join方法，将数据按换行符分割，并重新合并
		// 					// 	return labelText
		// 					// }
		// 					if (that.xDataType == 'date') {
		// 						let labelText = ''
		// 						const dateSplitArray = value.split('-')
		// 						if (dateSplitArray.length > 2) {
		// 							labelText = `${dateSplitArray[1]}-${dateSplitArray[2]}`
		// 						}
		// 						return labelText
		// 					} else {
		// 						// const isLengthOver = value.length > 3
		// 						// const labelText = `${value.slice(0, 3)}${isLengthOver ? '...' : ''}`
		// 						const isLengthOver = value.length > 4
		// 						const labelText = isLengthOver ? `${value.slice(0, 4)}\n${value.slice(4)}` : value
		// 						// 使用字符串的split和join方法，将数据按换行符分割，并重新合并
		// 						return labelText
		// 					}
		// 				},
		// 			},
		// 			axisPointer: {
		// 				show: true,
		// 				type: 'shadow',
		// 			},
		// 		},
		// 		dataZoom: {
		// 			type: 'inside', //放大缩小x轴数值
		// 		},
		// 		yAxis: [
		// 			{
		// 				type: 'value',
		// 				name: that.yUnit,
		// 				minInterval: 1,
		// 				nameGap: 12,
		// 				splitNumber: 4,
		// 				axisTick: {
		// 					length: 5,
		// 					lineStyle: {
		// 						type: 'dashed',
		// 					},
		// 				},
		// 				nameTextStyle: {
		// 					// 设置Y轴名称的文本样式
		// 					fontStyle: 'normal',
		// 					fontWeight: 'normal',
		// 					fontSize: 12,
		// 					// 其他文本样式属性...
		// 				},
		// 				splitLine: {
		// 					show: false,
		// 					lineStyle: {
		// 						color: 'rgba(78,89,105,0.31)',
		// 						type: 'dashed', // 设置网格线为虚线
		// 						width: 2,
		// 					},
		// 				},
		// 				axisLabel: {
		// 					color: '#6C8097', // 文本颜色
		// 					fontSize: 14, // 字体大小
		// 					// 设置文本旋转角度
		// 				},
		// 			},
		// 		],
		// 		series: that.getSeriesData(that.yDatas),
		// 	}
		// },
	},

	methods: {
		watchDataChange() {
			this.handleRefreshOption().then(() => {
				if (this.$refs.refChartContainer) {
					// 加载eCharts图表配置...
					this.myCharts.clear()
					console.log('this.minHeight--==', this.minHeight)
					this.myCharts.setOption(this.option)
					this.myCharts.resize({ height: this.minHeight})
				}
			})
		},
		handleRefreshOption() {
			const that = this
			this.option = {
				legend: {
					show: true,
					data: that.chartsLegend,
				},
				toolbox: {
					show: true,
					feature: {
						// magicType: { type: ['line', 'bar'] },
					},
				},
				tooltip: {
					show: true,
					trigger: 'axis',

					className: 'custom-tooltip-box',
					formatter: function(params) {
						const { name, value: value_line } = params[0]
						const nameText = `${name}`
						const seriesName = params[0].seriesName
						const htmlText = ''
						//#region 农机类型为单选时

						if (!that.isOverWidth) {
							return `<div class="t-tooltip-box" ">
										<div class="flag-leg"></div>
										<div class="flag-content">
											<div class="content-box">
												<div class="t-text-box">
													<div class="t-content-title"><div class="light"></div> ${nameText}</div>
													<div class="t-content-value">${!that.tooltipTitleName ? '数量' : that.tooltipTitleName + '：'}${value_line} ${that.yUnit}</div>
												</div>
											</div>
										</div>
									</div>`
						} else {
							return `<div class="t-tooltip-convert-box" ">
											<div class="flag-content">
												<div class="content-box">
													<div class="t-text-box">
														<div class="t-content-title">${nameText} <div class="light"></div></div>
														<div class="t-content-value">${!that.tooltipTitleName ? '数量' : that.tooltipTitleName + '：'}${value_line} ${that.yUnit}</div>
													</div>
												</div>
											</div>
											<div class="flag-leg"></div>
									</div>`
						}
						//#endregion
					},

					// /*1.设置x轴左右固定，上下跟随。*/
					// position: function (point, params, dom, rect, size) {
					// 	return [0, point[1]];
					// },
					/*2.设置Y轴上下固定，X左右跟随。*/
					position: function(point, params, dom, rect, size) {
						// return [point[0], point[1]]
						// 鼠标坐标和提示框位置的参考坐标系是：以外层div的左上角那一点为原点，x轴向右，y轴向下
						var x = 0 // x坐标位置
						var y = 0 // y坐标位置

						var pointX = point[0]
						var pointY = point[1]

						// 提示框大小
						var boxWidth = size.contentSize[0]
						var boxHeight = size.contentSize[1]

						// boxWidth > pointX 说明鼠标左边放不下提示框
						if (boxWidth > pointX) {
							x = point[0]
							that.isOverWidth = false
						} else {
							that.isOverWidth = true
							// 左边放的下
							x = pointX - boxWidth + 5
						}

						// boxHeight > pointY 说明鼠标上边放不下提示框
						if (boxHeight > pointY) {
							y = 5
						} else {
							// 上边放得下
							y = pointY - boxHeight
						}

						if (that.isTooltipPositionDiy) {
							return [x, y]
						} else {
							return [point[0], point[1]]
						}
					},
				},
				grid: {
					minheight: that.minHeight,
					minWidth: that.minWidth,
					// 是否显示直角坐标系内网格
					show: false,
					// 是否显示直角坐标系内边框
					borderWidth: 1,
					// 直角坐标系内边框颜色
					borderColor: 'rgba(0,0,0,0)',
					// 直角坐标系内背景色，只在设置了 backgroundColor 的情况下有效
					backgroundColor: 'rgba(0,0,0,0)', // 透明背景
					// 直角坐标系的位置，默认左上角为原点 (0, 0)
					left: that.left,
					right: that.right,
					bottom: that.bottom,
					top: that.top,
					// 是否包含坐标轴的标签在内
					containLabel: true,
					// 网格的宽高比
					// ... 其他 grid 配置项
				},
				backgroundColor: 'rgba(0, 0, 0, 0)',

				xAxis: {
					type: 'category',
					boundaryGap: true,
					data: that.xData,
					splitLine: {
						show: false,
						lineStyle: {
							color: 'rgba(78,89,105,0.31)',
							type: 'dashed', // 设置网格线为虚线
							width: 1,
						},
					},
					axisTick: {
						show: false,
						lineStyle: {
							color: '#4F6274',
						},
						alignWithLabel: true,
					},
					axisLabel: {
						interval: that.dataType == 'njData' ? 0 : that.xData.length > 32 ? 52 : that.xData.length > 7 ? 5 : 0,
						color: '#B0E0FF', // 文本颜色
						fontSize: 12, // 字体大小
						lineHeight: 14,
						textWrap: 'wrap',
						// 设置文本旋转角度
						formatter: function(value, index) {
							// if (that.formatterType == 'qrzx' || that.formatterType == 'qrzy') {
							// 	const labelText = `${value.slice(0, 4)}${value.slice(4)}`
							// 	// 使用字符串的split和join方法，将数据按换行符分割，并重新合并
							// 	return labelText
							// } else {
							// 	// const isLengthOver = value.length > 3
							// 	// const labelText = `${value.slice(0, 3)}${isLengthOver ? '...' : ''}`

							// 	const isLengthOver = value.length > 4
							// 	const labelText = `${value.slice(0, 4)}\n${value.slice(4)}`

							// 	// 使用字符串的split和join方法，将数据按换行符分割，并重新合并
							// 	return labelText
							// }
							if (that.xDataType == 'date') {
								let labelText = ''
								const dateSplitArray = value.split('-')
								if (dateSplitArray.length > 2) {
									labelText = `${dateSplitArray[1]}-${dateSplitArray[2]}`
								}
								return labelText
							} else {
								// const isLengthOver = value.length > 3
								// const labelText = `${value.slice(0, 3)}${isLengthOver ? '...' : ''}`

								const isLengthOver = value.length > 4
								const labelText = isLengthOver ? `${value.slice(0, 4)}\n${value.slice(4)}` : value

								// 使用字符串的split和join方法，将数据按换行符分割，并重新合并
								return labelText
							}
						},
					},

					axisPointer: {
						show: true,
						type: 'shadow',
					},
				},
				dataZoom: {
					type: 'inside', //放大缩小x轴数值
				},
				yAxis: [
					{
						type: 'value',
						name: that.yUnit,
						minInterval: 1,
						nameGap: 12,
						splitNumber: 4,
						axisTick: {
							length: 5,
							lineStyle: {
								type: 'dashed',
							},
						},
						nameTextStyle: {
							// 设置Y轴名称的文本样式
							fontStyle: 'normal',
							fontWeight: 'normal',
							fontSize: 12,
							// 其他文本样式属性...
						},
						splitLine: {
							show: false,
							lineStyle: {
								color: 'rgba(78,89,105,0.31)',
								type: 'dashed', // 设置网格线为虚线
								width: 2,
							},
						},
						axisLabel: {
							color: '#6C8097', // 文本颜色
							fontSize: 14, // 字体大小

							// 设置文本旋转角度
						},
					},
				],
				series: that.getSeriesData(that.yDatas),
			}
			return Promise.resolve()
		},

		getSeriesData(yDatas) {
			let series_ = []
			const keys = Object.keys(yDatas)
			keys.forEach((key) => {
				series_.push({
					name: key,
					data: yDatas[key],
					type: this.tabIndex == 1 ? 'line' : 'bar',
					itemStyle: {
						normal: {
							color: new echarts.graphic.LinearGradient(
								0,
								0,
								0,
								1, // 渐变方向为从上到下
								[
									{ offset: 0, color: `rgba(${this.itemColor} , 0.1)` }, // ，完全不透明
									{ offset: 0.5, color: `rgba(${this.itemColor} , 0.6)` }, // ，半透明
									{ offset: 1, color: `rgba(${this.itemColor} , 1)` }, // ，完全透明
								],
							),
						},
						emphasis: {
							color: new echarts.graphic.LinearGradient(
								0,
								0,
								0,
								1, // 渐变方向为从上到下
								[
									{ offset: 0, color: `rgba(255,128,12 , 0.1)` }, // ，完全不透明
									{ offset: 0.5, color: `rgba(255,128,12 , 0.6)` }, // ，半透明
									{ offset: 1, color: `rgba(255,128,12 , 1)` }, // ，完全透明
								],
							),
						},
					},
					// boxShadow: 'inset 0px 3px 0px 0px #00DEFE',
					barWidth: this.xData.length > 30 ? '40%' : this.xData.length > 7 ? '50%' : '16px',
					// barWidth: '20%',
					barMaxWidth: '16px',

					silent: true,
					// label: {
					// 	// show: yDatas.length == 1,
					// 	show: true,
					// 	color: '#B0E0FF',
					// 	fontSize: '14px',
					// 	position: 'top',
					// },
				})

				if (this.xData.length <= 7 || this.dataType == 'njData') {
					if (this.tabIndex == 0) {
						series_.push({
							name: '',
							type: 'pictorialBar',
							itemStyle: {
								normal: {
									color: `rgb(${this.itemColor})`,
								},
								emphasis: {
									color: `rgb(255,128,12)`,
								},
							},
							symbol: 'rect', // 图形类型，这里是矩形
							symbolRotate: 0,
							// symbolSize: ['65%', '3'],
							// symbolSize: ['16px', '3'],
							symbolSize: ['16px', '3'],
							barMaxWidth: '16px',

							symbolPosition: 'end',
							data: yDatas[key],
							z: 3,
						})
					}
				}

				if (this.showBarBg) {
					series_.push({
						name: 'maxBg',
						type: 'custom',
						itemStyle: {
							color: 'rgba(255,255,255,0.1)',
						},
						renderItem: (params, api) => {
							// 获取对应类目的axisTick中心点坐标
							var start = api.coord([api.value(0)])

							// 通过坐标系的宽度和类目数，计算单个类目的背景
							var width = (params.coordSys.width / (yDatas[key] || []).length) * 0.8 // 【注意】有几条数据数组就用 params.coordSys.width 除以几

							return {
								type: 'rect',
								shape: {
									// 相对左上角坐标
									x: start[0] - width / 2,
									y: params.coordSys.y,
									width: width,
									height: params.coordSys.height,
								},
								style: api.style(),
							}
						},
					})
				}
			})

			const yData_ = yDatas[keys[0]]

			// 找出纵坐标最大值用于设置自定义背景
			const maxValue = Math.max(...yData_)
			this.yAxisMax = maxValue

			//处理series中的data数据
			series_.map((item) => {
				if (item.name === 'maxBg') {
					// 通过最大值设置重点区域背景填满纵坐标
					item.data = yData_.map((item) => {
						return item ? maxValue : 0
					})
				}

				return item
			})

			return series_
		},

		getMaxYData(yData) {
			// 找出纵坐标最大值用于设置自定义背景
			const maxValue = Math.max(...yData) + 20
			// 通过最大值设置重点区域背景填满纵坐标
			const maxYData = yData.map((item) => {
				return item.isKeyArea ? maxValue : 0
			})
			return maxYData
		},
	},
}
</script>

<style lang="less" scoped>
.echarts-box {
	width: 100%;
	color: rgb(21, 154, 255);
	// background-color: skyblue;

	// [
	//         // 开始部分透明
	//         {offset: 0, color: 'rgba(12, 95, 113, 1)'}, // 0% 处的颜色，完全透明
	//         // 中间部分不透明
	//         {offset: 0.2, color: 'rgba(12, 95, 113, 1)'}, // 20% 处的颜色，完全不透明
	//         // 继续不透明
	//         {offset: 0.7, color: 'rgba(12, 95, 113, 1)'}, // 50% 处的颜色，完全不透明
	//         // 结尾部分开始透明
	//         {offset: 0.9, color: 'rgba(12, 95, 113, 0.5)'}, // 80% 处的颜色，半透明
	//         // 结尾部分完全透明
	//         {offset: 1, color: 'rgba(12, 95, 113, 0)'} // 100% 处的颜色，完全透明
	//     ],

	::v-deep(.custom-tooltip-box) {
		width: 200px !important;
		padding: 0 !important;
		border: none !important;
		// background-color: rgba(17, 32, 48, 0) !important;
		box-sizing: border-box;

		// 给子盒子自定义样式
		.t-tooltip-box {
			margin-top: -105px;
			// margin-left: -5px;
			display: flex;
			justify-content: flex-start;
			align-items: flex-start;

			.flag-leg {
				width: 15px;
				height: 105px;
				/* 使用 linear-gradient 设置渐变背景 */
				background: linear-gradient(to bottom, #c6d5e4, transparent);
				border-radius: 20px;
			}
			.flag-content {
				// width: 190px;
				// height: 40px;
				display: flex;
				align-items: center;
				padding: 5px 0;
				padding-right: 15px;
				box-sizing: border-box;
				// background: linear-gradient(to left, #c6d5e4, transparent);
				background: linear-gradient(to right, transparent 1%, rgba(208, 222, 238) 50%);
				.content-box {
					width: 100%;
					// height: 55px;
					padding-left: 10px;
					display: flex;
					justify-content: flex-start;
					align-items: flex-start;
					box-sizing: border-box;

					.t-light-box {
						/* 设置 div 的宽度和高度，确保它们是相等的以形成一个圆形 */
						width: 15px;
						height: 26px;
						display: flex;
						justify-content: center;
						align-items: center;
						margin-right: 5px;
						.light {
							width: 15px;
							height: 15px;
							/* 使用 border-radius 将 div 变成圆形 */
							border-radius: 50%;

							/* 使用 radial-gradient 创建从上到下的渐变背景 */
							background: radial-gradient(circle at center, #ff6200, transparent);

							/* 可以添加其他样式，如边框、阴影等 */
							box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
						}
					}
					.t-text-box {
						// width: 160px;
						// height: 70px;
						display: flex;
						flex-direction: column;
						justify-content: flex-start;
						align-items: flex-start;

						.t-content-title,
						.t-content-value {
							font-family: PingFangSC, PingFang SC;
							font-weight: bold;
							font-size: 14px;
							color: #191b2e;
							font-style: normal;
						}

						.t-content-title {
							display: flex;
							align-items: center;
							margin-right: 5px;

							.light {
								width: 15px;
								height: 15px;
								/* 使用 border-radius 将 div 变成圆形 */
								border-radius: 50%;
								margin-right: 5px;

								/* 使用 radial-gradient 创建从上到下的渐变背景 */
								background: radial-gradient(circle at center, #ff6200, transparent);

								/* 可以添加其他样式，如边框、阴影等 */
								box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
							}
						}
						.t-content-value {
							display: flex;
							align-items: center;
							margin-left: 20px;
						}
					}
				}
			}
		}

		.t-tooltip-convert-box {
			margin-top: -105px;
			display: flex;
			justify-content: flex-end;
			align-items: flex-start;
			.flag-leg {
				width: 15px;
				height: 105px;
				/* 使用 linear-gradient 设置渐变背景 */
				background: linear-gradient(to bottom, #c6d5e4, transparent);
				border-radius: 20px;
			}
			.flag-content {
				// width: 190px;
				// height: 40px;
				display: flex;
				align-items: center;
				padding: 5px 0;
				padding-left: 15px;
				box-sizing: border-box;
				// background: linear-gradient(to left, #c6d5e4, transparent);
				background: linear-gradient(to left, transparent 1%, rgba(208, 222, 238) 50%);

				.content-box {
					width: 100%;
					// height: 55px;
					padding-right: 10px;
					display: flex;
					justify-content: flex-end;
					align-items: flex-start;
					box-sizing: border-box;

					// .t-light-box {
					// 	/* 设置 div 的宽度和高度，确保它们是相等的以形成一个圆形 */
					// 	width: 15px;
					// 	min-height: 26px;
					// 	display: flex;
					// 	justify-content: center;
					// 	align-items: flex-start;
					// 	margin-left: 5px;
					// 	.light {
					// 		width: 15px;
					// 		height: 15px;
					// 		/* 使用 border-radius 将 div 变成圆形 */
					// 		border-radius: 50%;

					// 		/* 使用 radial-gradient 创建从上到下的渐变背景 */
					// 		background: radial-gradient(circle at center, #ff6200, transparent);

					// 		/* 可以添加其他样式，如边框、阴影等 */
					// 		box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
					// 	}
					// }
					.t-text-box {
						// width: 160px;
						// height: 70px;
						display: flex;
						flex-direction: column;
						justify-content: flex-start;
						align-items: flex-end;

						.t-content-title,
						.t-content-value {
							font-family: PingFangSC, PingFang SC;
							font-weight: 600;
							font-size: 16px;
							color: #191b2e;
							font-style: normal;
						}

						.t-content-title {
							display: flex;
							align-items: center;
							.light {
								width: 15px;
								height: 15px;
								/* 使用 border-radius 将 div 变成圆形 */
								border-radius: 50%;
								margin-left: 10px;
								/* 使用 radial-gradient 创建从上到下的渐变背景 */
								background: radial-gradient(circle at center, #ff6200, transparent);

								/* 可以添加其他样式，如边框、阴影等 */
								box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
							}
						}

						.t-content-value {
							display: flex;
							align-items: center;
							margin-right: 20px;
						}
					}
				}
			}
		}
	}
}
</style>
