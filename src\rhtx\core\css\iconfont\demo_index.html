<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=2492578" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe63d;</span>
                <div class="name">全屏</div>
                <div class="code-name">&amp;#xe63d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe627;</span>
                <div class="name">展示列</div>
                <div class="code-name">&amp;#xe627;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe651;</span>
                <div class="name">时间</div>
                <div class="code-name">&amp;#xe651;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe691;</span>
                <div class="name">人员小组</div>
                <div class="code-name">&amp;#xe691;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe784;</span>
                <div class="name">取消分组 表格</div>
                <div class="code-name">&amp;#xe784;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xea00;</span>
                <div class="name">设备类_电视墙</div>
                <div class="code-name">&amp;#xea00;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60c;</span>
                <div class="name">点击</div>
                <div class="code-name">&amp;#xe60c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe897;</span>
                <div class="name">退格</div>
                <div class="code-name">&amp;#xe897;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe68b;</span>
                <div class="name">24I云台</div>
                <div class="code-name">&amp;#xe68b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63c;</span>
                <div class="name">上一步</div>
                <div class="code-name">&amp;#xe63c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe901;</span>
                <div class="name">404监控、摄像头</div>
                <div class="code-name">&amp;#xe901;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe954;</span>
                <div class="name">24gl-telephoneKeypad4</div>
                <div class="code-name">&amp;#xe954;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe712;</span>
                <div class="name">react</div>
                <div class="code-name">&amp;#xe712;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe620;</span>
                <div class="name">连接</div>
                <div class="code-name">&amp;#xe620;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xea24;</span>
                <div class="name">链接,断开</div>
                <div class="code-name">&amp;#xea24;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65d;</span>
                <div class="name">协助</div>
                <div class="code-name">&amp;#xe65d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60a;</span>
                <div class="name">视频</div>
                <div class="code-name">&amp;#xe60a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe633;</span>
                <div class="name">语音输入2</div>
                <div class="code-name">&amp;#xe633;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63a;</span>
                <div class="name">地图</div>
                <div class="code-name">&amp;#xe63a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe800;</span>
                <div class="name">停止</div>
                <div class="code-name">&amp;#xe800;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe641;</span>
                <div class="name">左 左</div>
                <div class="code-name">&amp;#xe641;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe642;</span>
                <div class="name">右 右</div>
                <div class="code-name">&amp;#xe642;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec09;</span>
                <div class="name">下一步</div>
                <div class="code-name">&amp;#xec09;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8b7;</span>
                <div class="name">删除</div>
                <div class="code-name">&amp;#xe8b7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe616;</span>
                <div class="name">18-右下角</div>
                <div class="code-name">&amp;#xe616;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xea82;</span>
                <div class="name">24gf-playCircle</div>
                <div class="code-name">&amp;#xea82;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8b8;</span>
                <div class="name">205设置</div>
                <div class="code-name">&amp;#xe8b8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec0a;</span>
                <div class="name">查看</div>
                <div class="code-name">&amp;#xec0a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe69a;</span>
                <div class="name">查看</div>
                <div class="code-name">&amp;#xe69a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6a5;</span>
                <div class="name">文件类型-标准图-声音文件</div>
                <div class="code-name">&amp;#xe6a5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6a9;</span>
                <div class="name">文件类型-缩略图-未知文件</div>
                <div class="code-name">&amp;#xe6a9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe648;</span>
                <div class="name">暂停日志</div>
                <div class="code-name">&amp;#xe648;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe602;</span>
                <div class="name">音乐演奏</div>
                <div class="code-name">&amp;#xe602;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61e;</span>
                <div class="name">语音</div>
                <div class="code-name">&amp;#xe61e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63f;</span>
                <div class="name">聊天</div>
                <div class="code-name">&amp;#xe63f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe701;</span>
                <div class="name">返回</div>
                <div class="code-name">&amp;#xe701;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe611;</span>
                <div class="name">截图</div>
                <div class="code-name">&amp;#xe611;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe689;</span>
                <div class="name">Headset-2</div>
                <div class="code-name">&amp;#xe689;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe68a;</span>
                <div class="name">Headset-4</div>
                <div class="code-name">&amp;#xe68a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8de;</span>
                <div class="name">301加-线性方框</div>
                <div class="code-name">&amp;#xe8de;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66e;</span>
                <div class="name">减</div>
                <div class="code-name">&amp;#xe66e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe601;</span>
                <div class="name">更多</div>
                <div class="code-name">&amp;#xe601;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">手机</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe654;</span>
                <div class="name">刷新</div>
                <div class="code-name">&amp;#xe654;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe615;</span>
                <div class="name">1广播</div>
                <div class="code-name">&amp;#xe615;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe614;</span>
                <div class="name">下拉</div>
                <div class="code-name">&amp;#xe614;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe650;</span>
                <div class="name">详情</div>
                <div class="code-name">&amp;#xe650;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe638;</span>
                <div class="name">视频</div>
                <div class="code-name">&amp;#xe638;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65b;</span>
                <div class="name">刷新</div>
                <div class="code-name">&amp;#xe65b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b4;</span>
                <div class="name">查询</div>
                <div class="code-name">&amp;#xe6b4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe608;</span>
                <div class="name">下载</div>
                <div class="code-name">&amp;#xe608;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66a;</span>
                <div class="name">全屏</div>
                <div class="code-name">&amp;#xe66a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe656;</span>
                <div class="name">会议室</div>
                <div class="code-name">&amp;#xe656;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe636;</span>
                <div class="name">加载</div>
                <div class="code-name">&amp;#xe636;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe612;</span>
                <div class="name">空记录</div>
                <div class="code-name">&amp;#xe612;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe72b;</span>
                <div class="name">IM聊天</div>
                <div class="code-name">&amp;#xe72b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63e;</span>
                <div class="name">左箭头</div>
                <div class="code-name">&amp;#xe63e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe617;</span>
                <div class="name">视频</div>
                <div class="code-name">&amp;#xe617;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ad;</span>
                <div class="name">文件夹</div>
                <div class="code-name">&amp;#xe6ad;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe607;</span>
                <div class="name">笑脸</div>
                <div class="code-name">&amp;#xe607;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61c;</span>
                <div class="name">拨打电话</div>
                <div class="code-name">&amp;#xe61c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8b6;</span>
                <div class="name">201checkbox-选中部分</div>
                <div class="code-name">&amp;#xe8b6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe628;</span>
                <div class="name">5救援物资</div>
                <div class="code-name">&amp;#xe628;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62c;</span>
                <div class="name">指令模板管理</div>
                <div class="code-name">&amp;#xe62c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63b;</span>
                <div class="name">应急平台</div>
                <div class="code-name">&amp;#xe63b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe687;</span>
                <div class="name">救援直升机</div>
                <div class="code-name">&amp;#xe687;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe685;</span>
                <div class="name">周边游</div>
                <div class="code-name">&amp;#xe685;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe688;</span>
                <div class="name">调度中心</div>
                <div class="code-name">&amp;#xe688;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe695;</span>
                <div class="name">模拟器</div>
                <div class="code-name">&amp;#xe695;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe772;</span>
                <div class="name">已处置告警</div>
                <div class="code-name">&amp;#xe772;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe604;</span>
                <div class="name">1</div>
                <div class="code-name">&amp;#xe604;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe686;</span>
                <div class="name">语音通话</div>
                <div class="code-name">&amp;#xe686;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe635;</span>
                <div class="name">-s-取消共享</div>
                <div class="code-name">&amp;#xe635;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe74f;</span>
                <div class="name">缩小 退出全屏 小屏幕</div>
                <div class="code-name">&amp;#xe74f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe668;</span>
                <div class="name">警告</div>
                <div class="code-name">&amp;#xe668;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61d;</span>
                <div class="name">关闭 重启</div>
                <div class="code-name">&amp;#xe61d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe634;</span>
                <div class="name">共享桌面</div>
                <div class="code-name">&amp;#xe634;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe681;</span>
                <div class="name">语音</div>
                <div class="code-name">&amp;#xe681;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe67a;</span>
                <div class="name">禁言</div>
                <div class="code-name">&amp;#xe67a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe655;</span>
                <div class="name">踢出教室</div>
                <div class="code-name">&amp;#xe655;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe68e;</span>
                <div class="name">设为主屏</div>
                <div class="code-name">&amp;#xe68e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6fb;</span>
                <div class="name">icon_九分屏</div>
                <div class="code-name">&amp;#xe6fb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6fc;</span>
                <div class="name">icon_十六分屏</div>
                <div class="code-name">&amp;#xe6fc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6fd;</span>
                <div class="name">icon_八分屏</div>
                <div class="code-name">&amp;#xe6fd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6fe;</span>
                <div class="name">icon_五分屏</div>
                <div class="code-name">&amp;#xe6fe;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ff;</span>
                <div class="name">icon_一分屏</div>
                <div class="code-name">&amp;#xe6ff;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe700;</span>
                <div class="name">icon_四分屏</div>
                <div class="code-name">&amp;#xe700;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xea30;</span>
                <div class="name">分屏左右</div>
                <div class="code-name">&amp;#xea30;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe67f;</span>
                <div class="name">声音</div>
                <div class="code-name">&amp;#xe67f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61b;</span>
                <div class="name">关闭声音</div>
                <div class="code-name">&amp;#xe61b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe653;</span>
                <div class="name">照片 拍照</div>
                <div class="code-name">&amp;#xe653;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe609;</span>
                <div class="name">录像</div>
                <div class="code-name">&amp;#xe609;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe748;</span>
                <div class="name">停止录像</div>
                <div class="code-name">&amp;#xe748;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64d;</span>
                <div class="name">保持</div>
                <div class="code-name">&amp;#xe64d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64e;</span>
                <div class="name">挂断</div>
                <div class="code-name">&amp;#xe64e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64f;</span>
                <div class="name">全屏</div>
                <div class="code-name">&amp;#xe64f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe605;</span>
                <div class="name">取消保持</div>
                <div class="code-name">&amp;#xe605;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6a0;</span>
                <div class="name">会议</div>
                <div class="code-name">&amp;#xe6a0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe613;</span>
                <div class="name">会议</div>
                <div class="code-name">&amp;#xe613;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60b;</span>
                <div class="name">接听</div>
                <div class="code-name">&amp;#xe60b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe658;</span>
                <div class="name">录像</div>
                <div class="code-name">&amp;#xe658;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe678;</span>
                <div class="name">彩友多_合买详情-方案详情</div>
                <div class="code-name">&amp;#xe678;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d6;</span>
                <div class="name">消息message-fill</div>
                <div class="code-name">&amp;#xe7d6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60e;</span>
                <div class="name">删 除</div>
                <div class="code-name">&amp;#xe60e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe606;</span>
                <div class="name">挂断</div>
                <div class="code-name">&amp;#xe606;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe637;</span>
                <div class="name">选中</div>
                <div class="code-name">&amp;#xe637;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65a;</span>
                <div class="name">文件夹</div>
                <div class="code-name">&amp;#xe65a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ea;</span>
                <div class="name">文件夹</div>
                <div class="code-name">&amp;#xe6ea;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xebab;</span>
                <div class="name">文件</div>
                <div class="code-name">&amp;#xebab;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65e;</span>
                <div class="name">听音乐</div>
                <div class="code-name">&amp;#xe65e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61a;</span>
                <div class="name">删除</div>
                <div class="code-name">&amp;#xe61a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe669;</span>
                <div class="name">搜索</div>
                <div class="code-name">&amp;#xe669;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe603;</span>
                <div class="name">箭头</div>
                <div class="code-name">&amp;#xe603;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xebac;</span>
                <div class="name">箭头</div>
                <div class="code-name">&amp;#xebac;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64a;</span>
                <div class="name">箭头</div>
                <div class="code-name">&amp;#xe64a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe639;</span>
                <div class="name">人</div>
                <div class="code-name">&amp;#xe639;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe69f;</span>
                <div class="name">密码</div>
                <div class="code-name">&amp;#xe69f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60d;</span>
                <div class="name">账号</div>
                <div class="code-name">&amp;#xe60d;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1675063908909') format('woff2'),
       url('iconfont.woff?t=1675063908909') format('woff'),
       url('iconfont.ttf?t=1675063908909') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont iconquanping2"></span>
            <div class="name">
              全屏
            </div>
            <div class="code-name">.iconquanping2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzhanshilie"></span>
            <div class="name">
              展示列
            </div>
            <div class="code-name">.iconzhanshilie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshijian"></span>
            <div class="name">
              时间
            </div>
            <div class="code-name">.iconshijian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconrenyuanxiaozu"></span>
            <div class="name">
              人员小组
            </div>
            <div class="code-name">.iconrenyuanxiaozu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconquxiao-shangqiang"></span>
            <div class="name">
              取消分组 表格
            </div>
            <div class="code-name">.iconquxiao-shangqiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshangqiang"></span>
            <div class="name">
              设备类_电视墙
            </div>
            <div class="code-name">.iconshangqiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icondianji"></span>
            <div class="name">
              点击
            </div>
            <div class="code-name">.icondianji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontuige"></span>
            <div class="name">
              退格
            </div>
            <div class="code-name">.icontuige
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icona-24Iyuntai"></span>
            <div class="name">
              24I云台
            </div>
            <div class="code-name">.icona-24Iyuntai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshangyibu"></span>
            <div class="name">
              上一步
            </div>
            <div class="code-name">.iconshangyibu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjiankongshexiangtou"></span>
            <div class="name">
              404监控、摄像头
            </div>
            <div class="code-name">.iconjiankongshexiangtou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconbohaopan"></span>
            <div class="name">
              24gl-telephoneKeypad4
            </div>
            <div class="code-name">.iconbohaopan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconreact"></span>
            <div class="name">
              react
            </div>
            <div class="code-name">.iconreact
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconlianjie"></span>
            <div class="name">
              连接
            </div>
            <div class="code-name">.iconlianjie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconlink-off"></span>
            <div class="name">
              链接,断开
            </div>
            <div class="code-name">.iconlink-off
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconxiezhu"></span>
            <div class="name">
              协助
            </div>
            <div class="code-name">.iconxiezhu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshipin1"></span>
            <div class="name">
              视频
            </div>
            <div class="code-name">.iconshipin1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyuyin-wenjian"></span>
            <div class="name">
              语音输入2
            </div>
            <div class="code-name">.iconyuyin-wenjian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconditu"></span>
            <div class="name">
              地图
            </div>
            <div class="code-name">.iconditu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontingzhi"></span>
            <div class="name">
              停止
            </div>
            <div class="code-name">.icontingzhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzuozuo-"></span>
            <div class="name">
              左 左
            </div>
            <div class="code-name">.iconzuozuo-
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyouyou-"></span>
            <div class="name">
              右 右
            </div>
            <div class="code-name">.iconyouyou-
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconxiayibu"></span>
            <div class="name">
              下一步
            </div>
            <div class="code-name">.iconxiayibu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshanchu2"></span>
            <div class="name">
              删除
            </div>
            <div class="code-name">.iconshanchu2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyouxiajiao"></span>
            <div class="name">
              18-右下角
            </div>
            <div class="code-name">.iconyouxiajiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon24gf-playCircle"></span>
            <div class="name">
              24gf-playCircle
            </div>
            <div class="code-name">.icon24gf-playCircle
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshezhi"></span>
            <div class="name">
              205设置
            </div>
            <div class="code-name">.iconshezhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconchakan-copy"></span>
            <div class="name">
              查看
            </div>
            <div class="code-name">.iconchakan-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconchakan"></span>
            <div class="name">
              查看
            </div>
            <div class="code-name">.iconchakan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconwenjianleixing-biaozhuntu-shengyinwenjian"></span>
            <div class="name">
              文件类型-标准图-声音文件
            </div>
            <div class="code-name">.iconwenjianleixing-biaozhuntu-shengyinwenjian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconwenjianleixing-suolvetu-weizhiwenjian"></span>
            <div class="name">
              文件类型-缩略图-未知文件
            </div>
            <div class="code-name">.iconwenjianleixing-suolvetu-weizhiwenjian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzantingyinyue"></span>
            <div class="name">
              暂停日志
            </div>
            <div class="code-name">.iconzantingyinyue
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconbofangyinyue"></span>
            <div class="name">
              音乐演奏
            </div>
            <div class="code-name">.iconbofangyinyue
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyuyin1"></span>
            <div class="name">
              语音
            </div>
            <div class="code-name">.iconyuyin1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconliaotian"></span>
            <div class="name">
              聊天
            </div>
            <div class="code-name">.iconliaotian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfanhui"></span>
            <div class="name">
              返回
            </div>
            <div class="code-name">.iconfanhui
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjietu"></span>
            <div class="name">
              截图
            </div>
            <div class="code-name">.iconjietu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzhilong"></span>
            <div class="name">
              Headset-2
            </div>
            <div class="code-name">.iconzhilong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconunzhilong"></span>
            <div class="name">
              Headset-4
            </div>
            <div class="code-name">.iconunzhilong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjia-fangkuang"></span>
            <div class="name">
              301加-线性方框
            </div>
            <div class="code-name">.iconjia-fangkuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjian-fangkuang"></span>
            <div class="name">
              减
            </div>
            <div class="code-name">.iconjian-fangkuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icongengduo"></span>
            <div class="name">
              更多
            </div>
            <div class="code-name">.icongengduo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshouji"></span>
            <div class="name">
              手机
            </div>
            <div class="code-name">.iconshouji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshuaxin1"></span>
            <div class="name">
              刷新
            </div>
            <div class="code-name">.iconshuaxin1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconguangbo"></span>
            <div class="name">
              1广播
            </div>
            <div class="code-name">.iconguangbo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconxiala"></span>
            <div class="name">
              下拉
            </div>
            <div class="code-name">.iconxiala
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconxiangqing"></span>
            <div class="name">
              详情
            </div>
            <div class="code-name">.iconxiangqing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshipinchakan"></span>
            <div class="name">
              视频
            </div>
            <div class="code-name">.iconshipinchakan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshuaxin"></span>
            <div class="name">
              刷新
            </div>
            <div class="code-name">.iconshuaxin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconchaxun"></span>
            <div class="name">
              查询
            </div>
            <div class="code-name">.iconchaxun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconxiazai"></span>
            <div class="name">
              下载
            </div>
            <div class="code-name">.iconxiazai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconquanping1"></span>
            <div class="name">
              全屏
            </div>
            <div class="code-name">.iconquanping1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconicon_huiyishi"></span>
            <div class="name">
              会议室
            </div>
            <div class="code-name">.iconicon_huiyishi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjiazai"></span>
            <div class="name">
              加载
            </div>
            <div class="code-name">.iconjiazai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconkongjilu"></span>
            <div class="name">
              空记录
            </div>
            <div class="code-name">.iconkongjilu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconIMliaotian"></span>
            <div class="name">
              IM聊天
            </div>
            <div class="code-name">.iconIMliaotian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzuojiantou"></span>
            <div class="name">
              左箭头
            </div>
            <div class="code-name">.iconzuojiantou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshipin"></span>
            <div class="name">
              视频
            </div>
            <div class="code-name">.iconshipin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icongroup"></span>
            <div class="name">
              文件夹
            </div>
            <div class="code-name">.icongroup
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconxiaolian"></span>
            <div class="name">
              笑脸
            </div>
            <div class="code-name">.iconxiaolian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icondadianhua"></span>
            <div class="name">
              拨打电话
            </div>
            <div class="code-name">.icondadianhua
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconhalf"></span>
            <div class="name">
              201checkbox-选中部分
            </div>
            <div class="code-name">.iconhalf
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconchuzhijiuyuan"></span>
            <div class="name">
              5救援物资
            </div>
            <div class="code-name">.iconchuzhijiuyuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconronghetongxin"></span>
            <div class="name">
              指令模板管理
            </div>
            <div class="code-name">.iconronghetongxin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconchemical"></span>
            <div class="name">
              应急平台
            </div>
            <div class="code-name">.iconchemical
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshipinjiankong"></span>
            <div class="name">
              救援直升机
            </div>
            <div class="code-name">.iconshipinjiankong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontongxundiaodu"></span>
            <div class="name">
              周边游
            </div>
            <div class="code-name">.icontongxundiaodu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconziyuandiaodu"></span>
            <div class="name">
              调度中心
            </div>
            <div class="code-name">.iconziyuandiaodu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzhilingshoufa"></span>
            <div class="name">
              模拟器
            </div>
            <div class="code-name">.iconzhilingshoufa
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshipinhuishang"></span>
            <div class="name">
              已处置告警
            </div>
            <div class="code-name">.iconshipinhuishang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconicon-test"></span>
            <div class="name">
              1
            </div>
            <div class="code-name">.iconicon-test
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icondianhua"></span>
            <div class="name">
              语音通话
            </div>
            <div class="code-name">.icondianhua
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-s-quxiaogongxiang1"></span>
            <div class="name">
              -s-取消共享
            </div>
            <div class="code-name">.icon-s-quxiaogongxiang1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfullscreen-exit"></span>
            <div class="name">
              缩小 退出全屏 小屏幕
            </div>
            <div class="code-name">.iconfullscreen-exit
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjinggao"></span>
            <div class="name">
              警告
            </div>
            <div class="code-name">.iconjinggao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconguanbizhongqi"></span>
            <div class="name">
              关闭 重启
            </div>
            <div class="code-name">.iconguanbizhongqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icongongxiangzhuomian"></span>
            <div class="name">
              共享桌面
            </div>
            <div class="code-name">.icongongxiangzhuomian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyuyin"></span>
            <div class="name">
              语音
            </div>
            <div class="code-name">.iconyuyin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjinyan"></span>
            <div class="name">
              禁言
            </div>
            <div class="code-name">.iconjinyan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontichujiaoshi"></span>
            <div class="name">
              踢出教室
            </div>
            <div class="code-name">.icontichujiaoshi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconsheweizhupingbeifen"></span>
            <div class="name">
              设为主屏
            </div>
            <div class="code-name">.iconsheweizhupingbeifen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconicon_jiufenping"></span>
            <div class="name">
              icon_九分屏
            </div>
            <div class="code-name">.iconicon_jiufenping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconicon_shiliufenping"></span>
            <div class="name">
              icon_十六分屏
            </div>
            <div class="code-name">.iconicon_shiliufenping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconicon_bafenping"></span>
            <div class="name">
              icon_八分屏
            </div>
            <div class="code-name">.iconicon_bafenping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconicon_wufenping"></span>
            <div class="name">
              icon_五分屏
            </div>
            <div class="code-name">.iconicon_wufenping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconicon_yifenping"></span>
            <div class="name">
              icon_一分屏
            </div>
            <div class="code-name">.iconicon_yifenping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconicon_sifenping"></span>
            <div class="name">
              icon_四分屏
            </div>
            <div class="code-name">.iconicon_sifenping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfenpingzuoyou"></span>
            <div class="name">
              分屏左右
            </div>
            <div class="code-name">.iconfenpingzuoyou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshengyin"></span>
            <div class="name">
              声音
            </div>
            <div class="code-name">.iconshengyin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconguanbishengyin"></span>
            <div class="name">
              关闭声音
            </div>
            <div class="code-name">.iconguanbishengyin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzhaopianpaizhao"></span>
            <div class="name">
              照片 拍照
            </div>
            <div class="code-name">.iconzhaopianpaizhao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconluxiang1"></span>
            <div class="name">
              录像
            </div>
            <div class="code-name">.iconluxiang1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontingzhiluxiang"></span>
            <div class="name">
              停止录像
            </div>
            <div class="code-name">.icontingzhiluxiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconbaochi1"></span>
            <div class="name">
              保持
            </div>
            <div class="code-name">.iconbaochi1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconguaduan"></span>
            <div class="name">
              挂断
            </div>
            <div class="code-name">.iconguaduan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconquanping"></span>
            <div class="name">
              全屏
            </div>
            <div class="code-name">.iconquanping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconquxiaobaochi1"></span>
            <div class="name">
              取消保持
            </div>
            <div class="code-name">.iconquxiaobaochi1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconm3-meeting-fill"></span>
            <div class="name">
              会议
            </div>
            <div class="code-name">.iconm3-meeting-fill
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconhuiyi1"></span>
            <div class="name">
              会议
            </div>
            <div class="code-name">.iconhuiyi1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjieting"></span>
            <div class="name">
              接听
            </div>
            <div class="code-name">.iconjieting
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconluxiang"></span>
            <div class="name">
              录像
            </div>
            <div class="code-name">.iconluxiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconcaiyouduo_hemaixiangqing-fanganxiangqing"></span>
            <div class="name">
              彩友多_合买详情-方案详情
            </div>
            <div class="code-name">.iconcaiyouduo_hemaixiangqing-fanganxiangqing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconxiaoximessage-fill"></span>
            <div class="name">
              消息message-fill
            </div>
            <div class="code-name">.iconxiaoximessage-fill
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshanchu1"></span>
            <div class="name">
              删 除
            </div>
            <div class="code-name">.iconshanchu1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconguaduan1"></span>
            <div class="name">
              挂断
            </div>
            <div class="code-name">.iconguaduan1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconxuanzhong"></span>
            <div class="name">
              选中
            </div>
            <div class="code-name">.iconxuanzhong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_wenjianjia"></span>
            <div class="name">
              文件夹
            </div>
            <div class="code-name">.icon_wenjianjia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconwenjianjia"></span>
            <div class="name">
              文件夹
            </div>
            <div class="code-name">.iconwenjianjia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfile-fill"></span>
            <div class="name">
              文件
            </div>
            <div class="code-name">.iconfile-fill
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconkefu"></span>
            <div class="name">
              听音乐
            </div>
            <div class="code-name">.iconkefu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshanchu"></span>
            <div class="name">
              删除
            </div>
            <div class="code-name">.iconshanchu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconsousuo"></span>
            <div class="name">
              搜索
            </div>
            <div class="code-name">.iconsousuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjiantou1"></span>
            <div class="name">
              箭头
            </div>
            <div class="code-name">.iconjiantou1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjiantou-top"></span>
            <div class="name">
              箭头
            </div>
            <div class="code-name">.iconjiantou-top
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjiantou"></span>
            <div class="name">
              箭头
            </div>
            <div class="code-name">.iconjiantou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconren"></span>
            <div class="name">
              人
            </div>
            <div class="code-name">.iconren
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconmima"></span>
            <div class="name">
              密码
            </div>
            <div class="code-name">.iconmima
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzhanghao"></span>
            <div class="name">
              账号
            </div>
            <div class="code-name">.iconzhanghao
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont iconxxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconquanping2"></use>
                </svg>
                <div class="name">全屏</div>
                <div class="code-name">#iconquanping2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzhanshilie"></use>
                </svg>
                <div class="name">展示列</div>
                <div class="code-name">#iconzhanshilie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshijian"></use>
                </svg>
                <div class="name">时间</div>
                <div class="code-name">#iconshijian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconrenyuanxiaozu"></use>
                </svg>
                <div class="name">人员小组</div>
                <div class="code-name">#iconrenyuanxiaozu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconquxiao-shangqiang"></use>
                </svg>
                <div class="name">取消分组 表格</div>
                <div class="code-name">#iconquxiao-shangqiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshangqiang"></use>
                </svg>
                <div class="name">设备类_电视墙</div>
                <div class="code-name">#iconshangqiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icondianji"></use>
                </svg>
                <div class="name">点击</div>
                <div class="code-name">#icondianji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontuige"></use>
                </svg>
                <div class="name">退格</div>
                <div class="code-name">#icontuige</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icona-24Iyuntai"></use>
                </svg>
                <div class="name">24I云台</div>
                <div class="code-name">#icona-24Iyuntai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshangyibu"></use>
                </svg>
                <div class="name">上一步</div>
                <div class="code-name">#iconshangyibu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjiankongshexiangtou"></use>
                </svg>
                <div class="name">404监控、摄像头</div>
                <div class="code-name">#iconjiankongshexiangtou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconbohaopan"></use>
                </svg>
                <div class="name">24gl-telephoneKeypad4</div>
                <div class="code-name">#iconbohaopan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconreact"></use>
                </svg>
                <div class="name">react</div>
                <div class="code-name">#iconreact</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconlianjie"></use>
                </svg>
                <div class="name">连接</div>
                <div class="code-name">#iconlianjie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconlink-off"></use>
                </svg>
                <div class="name">链接,断开</div>
                <div class="code-name">#iconlink-off</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxiezhu"></use>
                </svg>
                <div class="name">协助</div>
                <div class="code-name">#iconxiezhu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshipin1"></use>
                </svg>
                <div class="name">视频</div>
                <div class="code-name">#iconshipin1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyuyin-wenjian"></use>
                </svg>
                <div class="name">语音输入2</div>
                <div class="code-name">#iconyuyin-wenjian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconditu"></use>
                </svg>
                <div class="name">地图</div>
                <div class="code-name">#iconditu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontingzhi"></use>
                </svg>
                <div class="name">停止</div>
                <div class="code-name">#icontingzhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzuozuo-"></use>
                </svg>
                <div class="name">左 左</div>
                <div class="code-name">#iconzuozuo-</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyouyou-"></use>
                </svg>
                <div class="name">右 右</div>
                <div class="code-name">#iconyouyou-</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxiayibu"></use>
                </svg>
                <div class="name">下一步</div>
                <div class="code-name">#iconxiayibu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshanchu2"></use>
                </svg>
                <div class="name">删除</div>
                <div class="code-name">#iconshanchu2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyouxiajiao"></use>
                </svg>
                <div class="name">18-右下角</div>
                <div class="code-name">#iconyouxiajiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon24gf-playCircle"></use>
                </svg>
                <div class="name">24gf-playCircle</div>
                <div class="code-name">#icon24gf-playCircle</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshezhi"></use>
                </svg>
                <div class="name">205设置</div>
                <div class="code-name">#iconshezhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconchakan-copy"></use>
                </svg>
                <div class="name">查看</div>
                <div class="code-name">#iconchakan-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconchakan"></use>
                </svg>
                <div class="name">查看</div>
                <div class="code-name">#iconchakan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconwenjianleixing-biaozhuntu-shengyinwenjian"></use>
                </svg>
                <div class="name">文件类型-标准图-声音文件</div>
                <div class="code-name">#iconwenjianleixing-biaozhuntu-shengyinwenjian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconwenjianleixing-suolvetu-weizhiwenjian"></use>
                </svg>
                <div class="name">文件类型-缩略图-未知文件</div>
                <div class="code-name">#iconwenjianleixing-suolvetu-weizhiwenjian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzantingyinyue"></use>
                </svg>
                <div class="name">暂停日志</div>
                <div class="code-name">#iconzantingyinyue</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconbofangyinyue"></use>
                </svg>
                <div class="name">音乐演奏</div>
                <div class="code-name">#iconbofangyinyue</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyuyin1"></use>
                </svg>
                <div class="name">语音</div>
                <div class="code-name">#iconyuyin1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconliaotian"></use>
                </svg>
                <div class="name">聊天</div>
                <div class="code-name">#iconliaotian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfanhui"></use>
                </svg>
                <div class="name">返回</div>
                <div class="code-name">#iconfanhui</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjietu"></use>
                </svg>
                <div class="name">截图</div>
                <div class="code-name">#iconjietu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzhilong"></use>
                </svg>
                <div class="name">Headset-2</div>
                <div class="code-name">#iconzhilong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconunzhilong"></use>
                </svg>
                <div class="name">Headset-4</div>
                <div class="code-name">#iconunzhilong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjia-fangkuang"></use>
                </svg>
                <div class="name">301加-线性方框</div>
                <div class="code-name">#iconjia-fangkuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjian-fangkuang"></use>
                </svg>
                <div class="name">减</div>
                <div class="code-name">#iconjian-fangkuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icongengduo"></use>
                </svg>
                <div class="name">更多</div>
                <div class="code-name">#icongengduo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshouji"></use>
                </svg>
                <div class="name">手机</div>
                <div class="code-name">#iconshouji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshuaxin1"></use>
                </svg>
                <div class="name">刷新</div>
                <div class="code-name">#iconshuaxin1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconguangbo"></use>
                </svg>
                <div class="name">1广播</div>
                <div class="code-name">#iconguangbo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxiala"></use>
                </svg>
                <div class="name">下拉</div>
                <div class="code-name">#iconxiala</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxiangqing"></use>
                </svg>
                <div class="name">详情</div>
                <div class="code-name">#iconxiangqing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshipinchakan"></use>
                </svg>
                <div class="name">视频</div>
                <div class="code-name">#iconshipinchakan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshuaxin"></use>
                </svg>
                <div class="name">刷新</div>
                <div class="code-name">#iconshuaxin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconchaxun"></use>
                </svg>
                <div class="name">查询</div>
                <div class="code-name">#iconchaxun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxiazai"></use>
                </svg>
                <div class="name">下载</div>
                <div class="code-name">#iconxiazai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconquanping1"></use>
                </svg>
                <div class="name">全屏</div>
                <div class="code-name">#iconquanping1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconicon_huiyishi"></use>
                </svg>
                <div class="name">会议室</div>
                <div class="code-name">#iconicon_huiyishi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjiazai"></use>
                </svg>
                <div class="name">加载</div>
                <div class="code-name">#iconjiazai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconkongjilu"></use>
                </svg>
                <div class="name">空记录</div>
                <div class="code-name">#iconkongjilu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconIMliaotian"></use>
                </svg>
                <div class="name">IM聊天</div>
                <div class="code-name">#iconIMliaotian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzuojiantou"></use>
                </svg>
                <div class="name">左箭头</div>
                <div class="code-name">#iconzuojiantou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshipin"></use>
                </svg>
                <div class="name">视频</div>
                <div class="code-name">#iconshipin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icongroup"></use>
                </svg>
                <div class="name">文件夹</div>
                <div class="code-name">#icongroup</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxiaolian"></use>
                </svg>
                <div class="name">笑脸</div>
                <div class="code-name">#iconxiaolian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icondadianhua"></use>
                </svg>
                <div class="name">拨打电话</div>
                <div class="code-name">#icondadianhua</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconhalf"></use>
                </svg>
                <div class="name">201checkbox-选中部分</div>
                <div class="code-name">#iconhalf</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconchuzhijiuyuan"></use>
                </svg>
                <div class="name">5救援物资</div>
                <div class="code-name">#iconchuzhijiuyuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconronghetongxin"></use>
                </svg>
                <div class="name">指令模板管理</div>
                <div class="code-name">#iconronghetongxin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconchemical"></use>
                </svg>
                <div class="name">应急平台</div>
                <div class="code-name">#iconchemical</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshipinjiankong"></use>
                </svg>
                <div class="name">救援直升机</div>
                <div class="code-name">#iconshipinjiankong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontongxundiaodu"></use>
                </svg>
                <div class="name">周边游</div>
                <div class="code-name">#icontongxundiaodu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconziyuandiaodu"></use>
                </svg>
                <div class="name">调度中心</div>
                <div class="code-name">#iconziyuandiaodu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzhilingshoufa"></use>
                </svg>
                <div class="name">模拟器</div>
                <div class="code-name">#iconzhilingshoufa</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshipinhuishang"></use>
                </svg>
                <div class="name">已处置告警</div>
                <div class="code-name">#iconshipinhuishang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconicon-test"></use>
                </svg>
                <div class="name">1</div>
                <div class="code-name">#iconicon-test</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icondianhua"></use>
                </svg>
                <div class="name">语音通话</div>
                <div class="code-name">#icondianhua</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-s-quxiaogongxiang1"></use>
                </svg>
                <div class="name">-s-取消共享</div>
                <div class="code-name">#icon-s-quxiaogongxiang1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfullscreen-exit"></use>
                </svg>
                <div class="name">缩小 退出全屏 小屏幕</div>
                <div class="code-name">#iconfullscreen-exit</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjinggao"></use>
                </svg>
                <div class="name">警告</div>
                <div class="code-name">#iconjinggao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconguanbizhongqi"></use>
                </svg>
                <div class="name">关闭 重启</div>
                <div class="code-name">#iconguanbizhongqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icongongxiangzhuomian"></use>
                </svg>
                <div class="name">共享桌面</div>
                <div class="code-name">#icongongxiangzhuomian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyuyin"></use>
                </svg>
                <div class="name">语音</div>
                <div class="code-name">#iconyuyin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjinyan"></use>
                </svg>
                <div class="name">禁言</div>
                <div class="code-name">#iconjinyan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontichujiaoshi"></use>
                </svg>
                <div class="name">踢出教室</div>
                <div class="code-name">#icontichujiaoshi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconsheweizhupingbeifen"></use>
                </svg>
                <div class="name">设为主屏</div>
                <div class="code-name">#iconsheweizhupingbeifen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconicon_jiufenping"></use>
                </svg>
                <div class="name">icon_九分屏</div>
                <div class="code-name">#iconicon_jiufenping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconicon_shiliufenping"></use>
                </svg>
                <div class="name">icon_十六分屏</div>
                <div class="code-name">#iconicon_shiliufenping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconicon_bafenping"></use>
                </svg>
                <div class="name">icon_八分屏</div>
                <div class="code-name">#iconicon_bafenping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconicon_wufenping"></use>
                </svg>
                <div class="name">icon_五分屏</div>
                <div class="code-name">#iconicon_wufenping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconicon_yifenping"></use>
                </svg>
                <div class="name">icon_一分屏</div>
                <div class="code-name">#iconicon_yifenping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconicon_sifenping"></use>
                </svg>
                <div class="name">icon_四分屏</div>
                <div class="code-name">#iconicon_sifenping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfenpingzuoyou"></use>
                </svg>
                <div class="name">分屏左右</div>
                <div class="code-name">#iconfenpingzuoyou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshengyin"></use>
                </svg>
                <div class="name">声音</div>
                <div class="code-name">#iconshengyin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconguanbishengyin"></use>
                </svg>
                <div class="name">关闭声音</div>
                <div class="code-name">#iconguanbishengyin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzhaopianpaizhao"></use>
                </svg>
                <div class="name">照片 拍照</div>
                <div class="code-name">#iconzhaopianpaizhao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconluxiang1"></use>
                </svg>
                <div class="name">录像</div>
                <div class="code-name">#iconluxiang1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontingzhiluxiang"></use>
                </svg>
                <div class="name">停止录像</div>
                <div class="code-name">#icontingzhiluxiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconbaochi1"></use>
                </svg>
                <div class="name">保持</div>
                <div class="code-name">#iconbaochi1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconguaduan"></use>
                </svg>
                <div class="name">挂断</div>
                <div class="code-name">#iconguaduan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconquanping"></use>
                </svg>
                <div class="name">全屏</div>
                <div class="code-name">#iconquanping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconquxiaobaochi1"></use>
                </svg>
                <div class="name">取消保持</div>
                <div class="code-name">#iconquxiaobaochi1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconm3-meeting-fill"></use>
                </svg>
                <div class="name">会议</div>
                <div class="code-name">#iconm3-meeting-fill</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconhuiyi1"></use>
                </svg>
                <div class="name">会议</div>
                <div class="code-name">#iconhuiyi1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjieting"></use>
                </svg>
                <div class="name">接听</div>
                <div class="code-name">#iconjieting</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconluxiang"></use>
                </svg>
                <div class="name">录像</div>
                <div class="code-name">#iconluxiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconcaiyouduo_hemaixiangqing-fanganxiangqing"></use>
                </svg>
                <div class="name">彩友多_合买详情-方案详情</div>
                <div class="code-name">#iconcaiyouduo_hemaixiangqing-fanganxiangqing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxiaoximessage-fill"></use>
                </svg>
                <div class="name">消息message-fill</div>
                <div class="code-name">#iconxiaoximessage-fill</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshanchu1"></use>
                </svg>
                <div class="name">删 除</div>
                <div class="code-name">#iconshanchu1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconguaduan1"></use>
                </svg>
                <div class="name">挂断</div>
                <div class="code-name">#iconguaduan1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxuanzhong"></use>
                </svg>
                <div class="name">选中</div>
                <div class="code-name">#iconxuanzhong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_wenjianjia"></use>
                </svg>
                <div class="name">文件夹</div>
                <div class="code-name">#icon_wenjianjia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconwenjianjia"></use>
                </svg>
                <div class="name">文件夹</div>
                <div class="code-name">#iconwenjianjia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfile-fill"></use>
                </svg>
                <div class="name">文件</div>
                <div class="code-name">#iconfile-fill</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconkefu"></use>
                </svg>
                <div class="name">听音乐</div>
                <div class="code-name">#iconkefu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshanchu"></use>
                </svg>
                <div class="name">删除</div>
                <div class="code-name">#iconshanchu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconsousuo"></use>
                </svg>
                <div class="name">搜索</div>
                <div class="code-name">#iconsousuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjiantou1"></use>
                </svg>
                <div class="name">箭头</div>
                <div class="code-name">#iconjiantou1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjiantou-top"></use>
                </svg>
                <div class="name">箭头</div>
                <div class="code-name">#iconjiantou-top</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjiantou"></use>
                </svg>
                <div class="name">箭头</div>
                <div class="code-name">#iconjiantou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconren"></use>
                </svg>
                <div class="name">人</div>
                <div class="code-name">#iconren</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconmima"></use>
                </svg>
                <div class="name">密码</div>
                <div class="code-name">#iconmima</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzhanghao"></use>
                </svg>
                <div class="name">账号</div>
                <div class="code-name">#iconzhanghao</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
