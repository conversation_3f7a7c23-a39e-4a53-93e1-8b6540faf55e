<template>
	<div class="right-user-drawer-wrap" v-if="value">
		<div class="right-user-drawer">
			<div class="right-user-drawer-content" v-if="isShow">
				1111111111
			</div>
		</div>
	</div>
</template>

<script>
import { Transition } from 'vue'

export default {
	name: 'rightUserDrawer',
	props: {
		value: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			isShow: true, // 默认打开
		}
	},
}
</script>
<style lang="scss" scoped>
.right-user-drawer-wrap {
	position: absolute;
	right: -341px;
	top: -77px;
	width: 360px;
	height: 947px;
	display: flex;
	align-items: center;
	.left-open-icon {
		width: 19px;
		height: 66px;
		cursor: pointer;
	}
	.left-close-icon {
		width: 19px;
		height: 66px;
		cursor: pointer;
	}
	.right-user-drawer {
		flex: 1;
		height: 100%;
		background: #000;
		width: 341px;
		margin-left: 19px;
		transition: width 1s;
		-webkit-transition: width 1s;
	}
}
</style>
