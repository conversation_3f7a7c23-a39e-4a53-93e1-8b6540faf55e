<template>
  <div class="dialog" v-if="show">
    <div class="title">
      <span>文件通知新增</span>
    </div>
    <div class="close_btn" @click="$emit('input', false)"></div>
    <div class="content">
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item label="标题: ">
          <el-input v-model="form.address"></el-input>
        </el-form-item>
        <el-form-item label="备注: ">
          <el-input type="textarea" :rows="4" v-model="form.desc"></el-input>
        </el-form-item>
        <el-form-item label="上传附件: ">
          <el-upload
            class="upload-demo"
            action="https://jsonplaceholder.typicode.com/posts/"
            :on-change="handleChange"
            :file-list="form.fileList"
          >
            <el-button size="small" type="primary" class="upload">选择文件</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <SwiperTable
        :titles="['序号', '姓名', '手机号', '所属部门']"
        :widths="['15%', '25%', '25%', '35%']"
        :data="tableData"
        :contentHeight="'336px'"
        :isNeedOperate="false"
      ></SwiperTable>
      <div class="btns">
        <div class="btn" @click="$emit('input', false)">关闭</div>
        <div class="btn" @click="handle(1)">保存</div>
      </div>
    </div>
  </div>
</template>

<script>
import SwiperTable from '../table/SwiperTable.vue'
export default {
  name: 'FileNotification',
  components: { SwiperTable },
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    show() {
      return this.value
    }
  },
  data() {
    return {
      form: {
        urgent: '0',
        time: '',
        origin: '0',
        address: '',
        type: '',
        person: '',
        desc: '',
        fileList: []
      },
      tableData: [
        ['1', '李晓飞', '15951703313', '南京仁盛木业有限公司'],
        ['2', '甘胜华', '15951703313', '南京仁盛木业有限公司'],
        ['3', '姚小腊', '15951703313', '中国石化销售股份有限公司江苏南京江宁建中加油站'],
        ['4', '王娜', '15951703313', '南京佳盛机电器材制造有限公司'],
        ['5', '张文明', '15951703313', '中国石化销售股份有限公司江苏南京江宁景明大街便利加油站']
      ]
    }
  },
  methods: {
    handleChange(file, fileList) {
      console.log(file, fileList)
    }
  }
}
</script>

<style lang="less" scoped>
.dialog {
  width: 699px;
  height: 773px;
  background: rgba(9, 19, 34, 0.95);
  box-shadow: 0px 0px 22px 0px rgba(11, 54, 138, 0.35), 0px 2px 8px 0px rgba(0, 0, 0, 0.7),
    inset 0px 0px 8px 0px rgba(17, 146, 214, 0.35);
  border-radius: 11px;
  border: 1px solid #015c8c;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1003;
  .title {
    margin: 0 auto 23px;
    width: 635px;
    height: 76px;
    line-height: 76px;
    background: url(~@/assets/map/dialog/title2.png) no-repeat;
    display: grid;
    place-items: center;
    span {
      font-size: 36px;
      font-family: PingFangSC, PingFang SC;
      line-height: 36px;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .close_btn {
    position: absolute;
    width: 23px;
    height: 23px;
    top: 25px;
    right: 40px;
    background: url(~@/assets/map/dialog/btn3.png) no-repeat;
    cursor: pointer;
  }
  .content {
    padding: 0 30px;
    .upload-demo {
      text-align: left;
      display: flex;
      gap: 20px;
      :deep(.el-upload-list) {
        display: flex;
        align-items: center;
        .el-upload-list__item {
          margin-top: 0;
          &:first-child {
            margin-top: 0;
          }
        }
      }
    }
    .btns {
      margin-top: 30px;
      display: flex;
      justify-content: space-evenly;
      gap: 10px;
      .btn {
        width: 150px;
        height: 33px;
        line-height: 33px;
        font-size: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        background: url('~@/assets/map/dialog/btn1.png') no-repeat;
        cursor: pointer;
      }
    }
  }
}
/deep/ .el-form-item__label {
  font-size: 14px;
  font-family: SourceHanSansCN-Normal, SourceHanSansCN;
  font-weight: 400;
  color: #ffffff;
}
/deep/ .el-input__inner {
  height: 34px;
  background: rgba(0, 74, 143, 0.4)
    linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
  font-size: 13px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
}
/deep/ .el-textarea__inner {
  background: rgba(0, 74, 143, 0.4)
    linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
  font-size: 13px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
}
</style>
