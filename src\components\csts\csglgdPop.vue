<template>
  <div class="ai_waring">
    <div class="title">
      <span>城市管理更多</span>
    </div>
    <img @click="closeEmit" class="close_btn" src="@/assets/shzl/map/close_btn2.png" alt="" />
    <div class="content">
      <div class="left">
        <BlockBox
          title="事部件案件预警"
          class="box box1"
          :isListBtns="false"
          :blockHeight="722"
        >
          <div class="sbjajyj1">
            <div class="title">案件态势</div>
            <div class="sbjajyj1content">
              <GroupColChart :data="qyeqData" :options="qyeqOptions"></GroupColChart>
            </div>
          </div>
          <div class="sbjajyj2">
            <div class="title">高发区域</div>
            <div class="sbjajyj2content">
              <GroupColChart :data="qyeqData2" :options="qyeqOptions2"></GroupColChart>
            </div>
          </div>
        </BlockBox>
      </div>
      <div class="middle">
        <BlockBox
          title="城市事项总览"
          class="box box1"
          :isListBtns="false"
          :blockHeight="722"
        >
          <div class="cssxzl1">
            <div class="title">渣土车总览</div>
            <div class="cssxzl1content">
              <div class="cssxzl1item">
                <div class="name">运营企业（家）</div>
                <div class="num">43</div>
              </div>
              <div class="cssxzl1item">
                <div class="name">渣土车辆（辆）</div>
                <div class="num">43</div>
              </div>
            </div>
          </div>
          <div class="cssxzl2">
            <div class="title">渣土车事件高发区域</div>
            <div class="cssxzl2content">
              <RankBarChartE02 :data="data" :options="options" />
            </div>
          </div>
          <div class="cssxzl3">
            <div class="title">环卫总览</div>
            <div class="cssxzl3content">
              <div class="cssxzl3item">
                <div class="num">123<span>个</span></div>
                <div class="name">公厕</div>
              </div>
              <div class="cssxzl3item">
                <div class="num">2011<span>个</span></div>
                <div class="name">生活垃圾焚烧厂</div>
              </div>
              <div class="cssxzl3item">
                <div class="num">1972<span>个</span></div>
                <div class="name">生活垃圾转运站</div>
              </div>
              <div class="cssxzl3item">
                <div class="num">123<span>个</span></div>
                <div class="name">建筑垃圾资源化中心</div>
              </div>
              <div class="cssxzl3item">
                <div class="num">2011<span>个</span></div>
                <div class="name">餐厨废弃处置中心</div>
              </div>
              <div class="cssxzl3item">
                <div class="num">1972<span>个</span></div>
                <div class="name">生活垃圾分类小区</div>
              </div>
            </div>
          </div>
        </BlockBox>
      </div>
      <div class="right">
        <BlockBox
          title="公共出行服务"
          class="box box1"
          :isListBtns="false"
          :blockHeight="323"
        >
          <div class="ggcxfw">
            <img class="fy_out" src="@/assets/csts/fy_out.png" alt="" />
            <img class="fy_in" src="@/assets/csts/fy_in2.png" alt="" />
            <div class="ggcxfwItem1">
              <div class="num">23,789</div>
              <div class="name">停车场（个）</div>
            </div>
            <div class="ggcxfwItem2">
              <div class="num">2,078</div>
              <div class="name">共享单车（个）</div>
            </div>
            <div class="ggcxfwItem3">
              <div class="num">578</div>
              <div class="name">公共停车位（位）</div>
            </div>
            <div class="ggcxfwItem4">
              <div class="num">100</div>
              <div class="name">共享单车投放（个）</div>
            </div>
          </div>
        </BlockBox>
        <BlockBox
          title="区域长效管理测评"
          class="box box1"
          :isListBtns="false"
          :blockHeight="399"
        >
          <div class="center">
            <SwiperTable
              :titles="['区域', '受理', '结案', '超期', '结案率', '超期率', '群众满意度']"
              :widths="['10%', '10%', '10%', '10%', '15%', '15%', '30%']"
              :data="zdxmData"
              :contentHeight="'220px'"
              :tabelHeight="'44px'"
            ></SwiperTable>
          </div>
        </BlockBox>
      </div>
    </div>
  </div>
</template>

<script>
import BlockBox from '@/components/leader/common/blockBox2.vue'
import SwiperTable from '@/components/leader/leader_zwjs/SwiperTable.vue'
export default {
  name:'csglgdPop',
  data() {
    return {
      qyeqData: [
        ['product', '派遣量', '处置量', '结案量'],
        ['浦口', 200, 160, 140],
        ['秦淮', 140, 90, 180],
        ['建邺', 102, 105, 120],
        ['江宁', 200, 90, 130],
        ['雨花', 150, 200, 150]
      ],
      qyeqOptions: {
        color: ['#01FF6C', '#8300D7', '#FF9201'],
        stack: true,
        isGradient: true,
        barMaxWidth: 10,
        gradientColors: [
          ['#01FF6C', '#00ED8B'],
          ['#8300D7', '#7800ED'],
          ['#FF9201', '#EDB000']
        ],
        isHor: false,
        barGap: '30%',
        tooltip: {
          color: '#fff',
          show: true,
          type: 'shadow'
        }
      },
      qyeqData2: [
        ['product', ''],
        ['新街口街道', 200],
        ['凤凰街道', 140],
        ['迈皋桥街道', 102],
        ['小市街道', 200],
        ['华侨路街道', 150]
      ],
      qyeqOptions2: {
        color: ['#00A3D7'],
        stack: true,
        isGradient: true,
        barMaxWidth: 10,
        gradientColors: [
          ['#00A3D7', '#0080ED'],
        ],
        isHor: false,
        barGap: '30%',
        tooltip: {
          color: '#fff',
          show: true,
          type: 'shadow'
        }
      },
      data: [
        ['name', 'value'],
        ['迈皋桥街道（次）', '15'],
        ['洪武路街道（次）', '13'],
        ['尧化门街道（次）', '11'],
      ],
      options: {
        color: ['#F8753A', '#FFD84E', '#3AF8DF', '#00A5FF', '#00A5FF'],
        barWidth: 7.88,
        fontSize: 18,
        labelSize: 14,
        top: '-100%',
        width: '95%',
      },
      zdxmData: [
        ['鼓楼区', '61', '61', '61', '90%', '90%', '90%'],
        ['玄武区', '94', '94', '94', '90%', '90%', '90%'],
        ['秦淮区', '62', '62', '62', '90%', '90%', '90%'],
        ['建邺区', '71', '71', '71', '90%', '90%', '90%'],
        ['江宁区', '72', '72', '72', '90%', '90%', '90%']
      ],
    }
  },
  components: {
    BlockBox,
    SwiperTable,
  },
  methods: {
    closeEmit() {
      this.$emit('closeEmit')
    },
  },
}
</script>

<style lang='less' scoped>
@keyframes rotateS {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes rotateN {
  0% {
    transform: rotate(360deg);
  }
  100% {
    transform: rotate(0);
  }
}
@keyframes rotateY {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotateY(360deg);
  }
}
.ai_waring {
  width: 1841px;
  height: 839px;
  background: rgba(0,23,59,0.95);
  box-shadow: 0px 0px 22px 0px rgba(11,54,138,0.35), 0px 2px 8px 0px rgba(0,0,0,0.7), inset 0px 0px 8px 0px rgba(17,146,214,0.35);
  border-radius: 11px;
  border: 1px solid;
  border-image: linear-gradient(360deg, rgba(57, 133, 255, 0.5), rgba(0, 205, 255, 0)) 1 1;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
  .title {
    background: url(~@/assets/shzl/map/title_bg2.png) no-repeat center / 100% 100%;
    background-size: 100% 100%;
    width: 914px;
    height: 74px;
    margin: 0 auto;
    line-height: 74px;
    text-align: center;
    position: relative;
    span {
      font-size: 40px;
      font-family: YouSheBiaoTiHei;
      color: #FFFFFF;
      line-height: 52px;
      background: linear-gradient(180deg, #FFFFFF 0%, #2AEBFF 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      display: inline-block;
      margin-top: 11px;
    }
  }
  .close_btn {
    position: absolute;
    top: 16px;
    right: 21px;
    cursor: pointer;
  }
  .content {
    width: 1841px;
    height: 765px;
    .title {
      width: 492px;
      height: 17px;
      background: url(~@/assets/leader/img/component/title_bg3.png) no-repeat;
      background-size: 100% 100%;
      font-size: 22px;
      font-family: PangMenZhengDao;
      color: #FFBC6C;
      line-height: 25px;
      letter-spacing: 2px;
      text-align: left;
      padding-left: 29px;
    }
    .left {
      width: 533px;
      float: left;
      margin-left: 67px;
      padding-top: 43px;
      .sbjajyj1 {
        width: 492px;
        height: 319px;
        margin-top: 27px;
        margin-left: 14px;
        .sbjajyj1content {
          height: 302px;
        }
      }
      .sbjajyj2 {
        width: 492px;
        height: 326px;
        margin-left: 14px;
        .sbjajyj2content {
          height: 309px;
        }
      }
    }
    .middle {
      width: 533px;
      float: left;
      margin-left: 54px;
      padding-top: 43px;
      .cssxzl1 {
        width: 492px;
        height: 112px;
        margin-top: 26px;
        margin-left: 14px;
        .cssxzl1content {
          height: 95px;
          .cssxzl1item {
            width: 223px;
            height: 44px;
            float: left;
            background: url(~@/assets/leader/img/component/ztczlBg.png) no-repeat;
            background-size: 100% 100%;
            margin: 23px 11px 0 12px;
            .name {
              float: left;
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 44px;
              padding-left: 20px;
            }
            .num {
              float: right;
              font-size: 20px;
              font-family: PingFangSC-Medium, PingFang SC;
              font-weight: 500;
              color: #FFFFFF;
              line-height: 44px;
              padding-right: 19px;
            }
          }
        }
      }
      .cssxzl2 {
        width: 492px;
        height: 207px;
        margin-left: 14px;
        .cssxzl2content {
          height: 190px;
        }
      }
      .cssxzl3 {
        width: 492px;
        height: 327px;
        margin-left: 14px;
        .cssxzl3content {
          height: 310px;
          padding-top: 24px;
          .cssxzl3item {
            width: 132px;
            height: 107px;
            float: left;
            margin: 0 0 14px 24px;
            .num {
              font-size: 26px;
              font-family: DINAlternate-Bold, DINAlternate;
              font-weight: bold;
              color: #FFFFFF;
              line-height: 30px;
              margin-top: 42px;
              span {
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #ffffff;
                line-height: 20px;
                margin-left: 7px;
              }
            }
            .name {
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
              margin-top: 8px;
            }
            &:nth-of-type(1) {
              background: url(~@/assets/leader/img/component/hwzl1.png) no-repeat;
              background-size: 100% 100%;
            }
            &:nth-of-type(2) {
              background: url(~@/assets/leader/img/component/hwzl2.png) no-repeat;
              background-size: 100% 100%;
            }
            &:nth-of-type(3) {
              background: url(~@/assets/leader/img/component/hwzl3.png) no-repeat;
              background-size: 100% 100%;
            }
            &:nth-of-type(4) {
              background: url(~@/assets/leader/img/component/hwzl4.png) no-repeat;
              background-size: 100% 100%;
            }
            &:nth-of-type(5) {
              background: url(~@/assets/leader/img/component/hwzl5.png) no-repeat;
              background-size: 100% 100%;
            }
            &:nth-of-type(6) {
              background: url(~@/assets/leader/img/component/hwzl6.png) no-repeat;
              background-size: 100% 100%;
            }
          }
        }
      }
    }
    .right {
      width: 533px;
      float: left;
      margin-left: 70px;
      padding-top: 43px;
      .ggcxfw {
        width: 475px;
        height: 248px;
        background: url(~@/assets/csts/bg3.png) no-repeat;
        background-size: 100% 100%;
        margin: 15px 0 0 24px;
        position: relative;
        .fy_out {
          position: absolute;
          top: 3%;
          left: 27%;
          z-index: 99;
          transform: translate(-50%, -50%);
          animation: rotateS infinite 12s linear;
        }
        .fy_in {
          position: absolute;
          top: 44%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
        .ggcxfwItem1 {
          width: 170px;
          height: 124px;
          float: left;
          .num {
            font-size: 22px;
            font-family: DINAlternate-Bold, DINAlternate;
            font-weight: bold;
            color: #FFFFFF;
            line-height: 30px;
            margin-top: 37px;
          }
          .name {
            font-size: 18px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 20px;
            margin-top: 8px;
          }
        }
        .ggcxfwItem2 {
          width: 170px;
          height: 124px;
          float: right;
          .num {
            font-size: 22px;
            font-family: DINAlternate-Bold, DINAlternate;
            font-weight: bold;
            color: #FFFFFF;
            line-height: 30px;
            margin-top: 37px;
          }
          .name {
            font-size: 18px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 20px;
            margin-top: 8px;
          }
        }
        .ggcxfwItem3 {
          width: 170px;
          height: 124px;
          float: left;
          .num {
            font-size: 22px;
            font-family: DINAlternate-Bold, DINAlternate;
            font-weight: bold;
            color: #FFFFFF;
            line-height: 30px;
            margin-top: 37px;
          }
          .name {
            font-size: 18px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 20px;
            margin-top: 8px;
          }
        }
        .ggcxfwItem4 {
          width: 170px;
          height: 124px;
          float: right;
          .num {
            font-size: 22px;
            font-family: DINAlternate-Bold, DINAlternate;
            font-weight: bold;
            color: #FFFFFF;
            line-height: 30px;
            margin-top: 37px;
          }
          .name {
            font-size: 18px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 20px;
            margin-top: 8px;
          }
        }
      }
      .center {
        height: 349px;
        padding: 24px 0 0 5px;
      }
    }
  }
}
</style>