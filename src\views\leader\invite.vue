<template>
	<div class="dialog-contain" v-if="inRoom">
		<div style="float: right;position: relative;right: 10px;top: 5px;" v-show="colScreen">{{ meetingDuration }}</div>
		<div class="title" style="text-align: center;" v-show="colScreen">{{ roomName }}的视频会议</div>
		<div
			class="screen-contain"
			v-show="remoteScreenViews.length != 0"
			:style="{ top: colScreen ? '50%' : '', translate: colScreen ? '0 -50%' : '' }"
		>
			<div v-for="item in remoteScreenViews" :key="item" :id="item" class="remote-screen-container">
				<div class="screen-share-name">{{ item.split('_')[2] }}正在共享屏幕</div>
			</div>
		</div>
		<div v-show="remoteScreenViews.length == 0">
			<div
				id="room-contain"
				style="display: flex;flex-wrap: wrap;max-height: calc(100vh - 75px);overflow-y:auto;"
				:class="['meeting-contain']"
			>
				<div id="local" class="local-stream-content participant">
					<div v-if="!cameraStatus" class="board" style="">
						<div class="board-img"></div>
					</div>
					<div :id="`describe-${userId.split('_')[1]}`" class="describe">
						<div
							:id="`microphone-${userId.split('_')[1]}`"
							:class="['microphone', microphoneStatus ? 'microphone-active' : 'microphone-inactive']"
						></div>
						<div :id="`${userId.split('_')[1]}`" class="identity">{{ userName }}(我)</div>
					</div>
				</div>

				<div v-for="item in remoteUsersViews" :key="item" :id="item" class="remote-stream-container participant">
					<div v-if="remoteStopVideoUserIdList.has(item.split('_main')[0])" class="board">
						<div class="board-img"></div>
					</div>
					<div :id="`describe-${item.split('_')[1]}`" class="describe">
						<div
							:id="`microphone-${item.split('_')[1]}`"
							:class="[
								'microphone',
								remoteStartAudioUserIdList.has(item.split('_main')[0]) ? 'microphone-active' : 'microphone-inactive',
							]"
						></div>
						<div :id="`${item.split('_')[1]}`" class="identity">{{ item.split('_')[2] }}</div>
					</div>
				</div>
			</div>
		</div>
		<!-- 聊天面板 -->
		<div class="chat-panel" :class="{ active: showChat }" v-show="colScreen">
			<div class="chat-header" @click="toggleChat">
				<span>会议聊天</span>
				<span class="unread-count" v-if="unreadCount > 0">{{ unreadCount }}</span>
				<i :class="['arrow', showChat ? 'up' : 'down']"></i>
			</div>
			<!-- <div class="messages-container" v-if="messageList.length == 0">暂未有人发送消息...</div> -->
			<div class="messages-container" ref="messagesContainer" v-show="showChat">
				<div
					v-for="(message, index) in messageList"
					:key="index"
					:class="['message', message.userId.split('_')[1] === userId.split('_')[1] ? 'sent' : 'received']"
				>
					<div class="sender" v-if="message.userId.split('_')[1] != userId.split('_')[1]">
						{{ message.userId.split('_')[2] }}
					</div>
					<div class="content">
						{{ message.data }}
					</div>
					<div class="time">
						{{ formatTime(message.timestamp) || '' }}
					</div>
				</div>
			</div>

			<div class="input-container" v-show="showChat">
				<button class="emoji-btn" @click="toggleEmojiPicker">
					😊
				</button>
				<input v-model="newMessage" type="text" placeholder="输入消息..." @keyup.enter="sendMessage" ref="messageInput" />
				<button class="send-btn" :disabled="!newMessage.trim()" @click="sendMessage">
					发送
				</button>
			</div>
		</div>
		<div style="position: fixed;bottom: 5px;justify-content: center;width: 100vw;" class="dialog-foot-group">
			<div style="display: block;" class="foot-btn" v-if="microphoneStatus" @click.stop="changeLocalMicroStatus">
				<div class="foot-btn-icon mic-on"></div>
			</div>
			<div style="display: block;" class="foot-btn" v-else @click.stop="changeLocalMicroStatus">
				<div class="foot-btn-icon mic-off"></div>
			</div>
			<div style="display: block;" class="foot-btn" v-if="cameraStatus" @click.stop="changeLocalCameraStatus">
				<div class="foot-btn-icon cam-on"></div>
			</div>
			<div style="display: block;" class="foot-btn" v-else @click.stop="changeLocalCameraStatus">
				<div class="foot-btn-icon cam-off"></div>
			</div>
			<div style="display: block;" class="foot-btn" @click.stop="changeLocalCamera">
				<div class="foot-btn-icon cam-change"></div>
			</div>
			<div style="display: block;" class="foot-btn" @click.stop="exit">
				<div class="foot-btn-icon hang-off"></div>
			</div>
		</div>
	</div>
	<div v-else style="display: flex;align-items: center;flex-direction: column;">
		<div class="title">{{ roomName }}的视频会议</div>
		<div class="romeNum">{{ roomId }}</div>
		<div class="ready-dialog-content">
			<div v-if="!cameraStatus" class="board" style="width: calc(100vw - 40px);height: 339px;margin-top: 20px;margin-bottom: 8px;">
				<div class="board-img"></div>
			</div>
			<video v-else id="video-ele" class="video-ele" autoplay></video>
		</div>
		<div class="dialog-foot-group">
			<div class="foot-btn" v-if="microphoneStatus" @click.stop="changeLocalMicroStatus">
				<div class="foot-btn-icon mic-on"></div>
			</div>
			<div class="foot-btn" v-else @click.stop="changeLocalMicroStatus">
				<div class="foot-btn-icon mic-off"></div>
			</div>
			<div class="foot-btn" v-if="cameraStatus" @click.stop="changeLocalCameraStatus">
				<div class="foot-btn-icon cam-on"></div>
			</div>
			<div class="foot-btn" v-else @click.stop="changeLocalCameraStatus">
				<div class="foot-btn-icon cam-off"></div>
			</div>
		</div>
		<div class="user-name">
			用户姓名：<input
				style="border: none;border-bottom: 1px solid;border-radius: 0;background-color: transparent;"
				v-model="userName"
				type="text"
			/>
		</div>
		<div class="btn" @click="getAcross">加入会议</div>
	</div>
</template>

<script>
import { getSig, getRoom, joinRoom, quitRoom } from '@/api/bjnj/zhdd.js'
import util from '@/libs/util.js'
import rtc from '@/components/mixins/rtc.js'
import TRTC from 'trtc-sdk-v5'
import LibGenerateTestUserSig from '@/utils/lib-generate-test-usersig.min.js'
import { isMobile } from '@/utils/utils'
import { WebSocketManager } from '@/views/leader/components/service/tool/index.js'
let meetingInterval = null
export default {
	name: 'multipleMeeting',
	mixins: [rtc],
	components: {},
	props: {},
	data() {
		return {
			inRoom: false,
			allTracks: [],
			// 房间号
			roomNum: '',
			// 房间名
			roomName: '',
			duration: 0,
			// 房间与会者列表
			participants: [],
			// 成员管理列表
			memberList: [],
			// 本地用户设备状态
			microphoneStatus: false,
			cameraStatus: false,
			localmicrophoneStatus: false,
			localcameraStatus: false,
			cameraIndex: 0,
			// messageList: [
			// 	{
			// 		fromName: '系统消息',
			// 		content: '会议内文本聊天已启用',
			// 	},
			// ],
			chatStyle: {
				width: '295px',
				height: '184px',
			},
			userName: '',
			liveClient: null,
			roomId: 14251,
			sdkAppId: 1600089532,
			sdkSecretKey: '3a6edb36029177424289091eb1d729befde4297a6426c3d6f9e8df97ada4cb0b',
			userId: 'user_651265',
			userSig: null,
			showChat: false,
			newMessage: '',
			messages: [
				{ sender: 'Alice', content: '大家好！会议马上开始', timestamp: new Date(Date.now() - 3600000) },
				{ sender: 'me', content: '我已经准备好了', timestamp: new Date(Date.now() - 1800000) },
				{ sender: 'Bob', content: '我的摄像头有点问题', timestamp: new Date() },
			],
			unreadCount: 0,
			previewTimeInterval: null,
			isClickExit: false,
			colScreen: true, //竖屏状态
		}
	},

	computed: {
		meetingDuration() {
			let minute, second
			if (Math.floor(this.duration / 60) > 0) {
				minute = Math.floor(this.duration / 60) <= 9 ? '0' + Math.floor(this.duration / 60) : Math.floor(this.duration / 60)
			} else {
				minute = '00'
			}
			if (Math.floor(this.duration % 60) > 0) {
				second = Math.floor(this.duration % 60) <= 9 ? '0' + Math.floor(this.duration % 60) : Math.floor(this.duration % 60)
			} else {
				second = '00'
			}
			return `${minute}：${second}`
		},
		participantLength() {
			return this.participants.length
		},
		moreDialogOptionList() {
			if (this.role === 'host') {
				return ['关闭摄像头', '修改名称', '移出会议']
			} else {
				return ['修改名称']
			}
		},
		// username() {
		// 	return this.$store.state.username
		// },
	},
	watch: {
		async cameraStatus(newVal) {
			const constrict = {
				audio: this.microphoneStatus,
				video: newVal
					? {
							facingMode: 'user',
					  }
					: newVal,
			}
			await this.getDefaultDeviceStream(constrict)
		},
		async microphoneStatus(newVal) {
			const constrict = {
				audio: newVal,
				video: this.cameraStatus
					? {
							facingMode: 'user',
					  }
					: this.cameraStatus,
			}
			await this.getDefaultDeviceStream(constrict)
		},
		camStatus(newVal) {
			if (newVal === 'stopped') this.cameraStatus = false
		},
		micStatus(newVal) {
			if (newVal === 'stopped') this.microphoneStatus = false
		},
		roomStatus(newVal) {
			if (newVal === 'exited') {
				this.clearAllTrack()
				this.inRoom = false
				this.cameraStatus = false
				this.microphoneStatus = false
				if (this.isClickExit || this.isBeiTi)
					quitRoom({ roomNumber: this.roomId, mobile: this.getUrlParam('userId').split('_')[1] }).then((res) => {})
				this.isBeiTi = false
				// 移除事件监听
				window.removeEventListener('orientationchange', () => {})
			}
		},
	},
	beforeDestroy() {
		this.exit()
		if (this.previewTimeInterval) {
			clearInterval(this.previewTimeInterval)
			this.previewTimeInterval = null
		}
		if (this.liveClient) {
			this.liveClient.close()
		}
	},
	async mounted() {
		TRTC.isSupported().then(async (checkResult) => {
			const { isBrowserSupported, isWebRTCSupported } = checkResult.detail
			if (!checkResult.result || !isBrowserSupported || !isWebRTCSupported) {
				// SDK 不支持当前浏览器，引导用户使用最新版的 Chrome 浏览器。
				alert('当前浏览器不支持视频会商功能，建议使用最新版的 Chrome 浏览器！')
				return
			} else {
				//链接挂参为roomId/?phoone=手机号
				// this.initClientEvent()
				this.roomId = Number(this.getUrlParam('roomId'))
				this.roomName = this.getUrlParam('roomName')
				// this.liveClient = new WebSocket(
				// 	'wss://dpzs.camtc.cn/ws/meeting-websocket/' + this.roomId + '?phone=' + this.getUrlParam('userId').split('_')[1],
				// 	// 'ws://172.16.50.211:9019/meeting-websocket/' + this.roomId + '?phone=' + this.getUrlParam('userId').split('_')[1],
				// )
				// this.initClientEvent()
				this.liveClient = new WebSocketManager(
					'wss://dpzs.camtc.cn/ws/meeting-websocket/' + this.roomId + '?phone=' + this.getUrlParam('userId').split('_')[1],
					{
						onOpen: (event) => {
							console.log('会议室连接已建立')
						},
						onMessage: (event) => {
							console.log('收到消息:', event)
							if (event.data.indexOf('disband') > -1) {
								this.outRoom()
								this.showCustomAlert1('房主解散了会议！', 3000)
							} else if (event.data == 'agree') {
								this.startMeeting()
							} else if (event.data == 'refuse') {
								// 使用示例
								this.showCustomAlert1('管理员拒绝了您的入会请求！', 3000)
							}
						},
						onClose: (event) => {
							console.log('会议室连接已断开')
						},
						onError: (error) => {
							console.error('WebSocket error:', error)
						},
						reconnectInterval: 1000,
						maxReconnectAttempts: 10,
						heartbeatInterval: 20000,
						heartbeatMsg: JSON.stringify({ acceptUser: 'root', type: 0 }),
					},
				)
				// this.previewTimeInterval = setInterval(() => {
				// 	let data = {
				// 		acceptUser: 'root',
				// 		type: 0,
				// 	}
				// 	this.liveClient.send(JSON.stringify(data))
				// }, 20000)
				this.trtc = TRTC.create()

				const constrict = {
					audio: this.microphoneStatus,
					video: this.cameraStatus
						? {
								facingMode: 'user',
						  }
						: this.cameraStatus,
				}
				await this.getDefaultDeviceStream(constrict)
				// 监听键盘弹出/收起
				window.addEventListener('resize', () => {
					if (this.showChat) {
						this.scrollToBottom()
					}
				})
			}
		})
	},
	methods: {
		showCustomAlert1(message, duration = 3000) {
			const alertBox = document.createElement('div')
			alertBox.style.position = 'fixed'
			alertBox.style.top = '50%'
			alertBox.style.left = '50%'
			alertBox.style.transform = 'translate(-50%, -50%)'
			alertBox.style.padding = '20px'
			alertBox.style.background = 'white'
			alertBox.style.border = '1px solid #ccc'
			alertBox.style.borderRadius = '5px'
			alertBox.style.boxShadow = '0 0 10px rgba(0,0,0,0.2)'
			alertBox.style.zIndex = '9999'
			alertBox.style.textAlign = 'center'
			alertBox.textContent = message

			document.body.appendChild(alertBox)

			// 3秒后自动移除
			setTimeout(() => {
				document.body.removeChild(alertBox)
			}, duration)
		},
		initClientEvent() {
			if (!this.liveClient) {
				console.log('客户端未初始化')
				return
			}
			// 连接打开时触发
			this.liveClient.onopen = (event) => {
				console.log('连接已建立')
			}
			// 接收消息时触发
			this.liveClient.onmessage = (event) => {
				console.log('收到消息:', event)
				if (event.data.indexOf('disband') > -1) {
					this.outRoom()
					this.showCustomAlert1('房主解散了会议！', 3000)
				} else if (event.data == 'agree') {
					this.startMeeting()
				} else if (event.data == 'refuse') {
					// 使用示例
					this.showCustomAlert1('管理员拒绝了您的入会请求！', 3000)
				}
			}
			this.liveClient.onclose = (event) => {
				this.liveClient = null
			}
		},
		getUrlParam(key) {
			const url = decodeURI(window.location.href.replace(/^[^?]*\?/, ''))
			const regexp = new RegExp(`(^|&)${key}=([^&#]*)(&|$|)`, 'i')
			const paramMatch = url.match(regexp)

			return paramMatch ? paramMatch[2] : null
		},
		getAcross() {
			TRTC.isSupported().then(async (checkResult) => {
				const { isBrowserSupported, isWebRTCSupported } = checkResult.detail
				if (!checkResult.result || !isBrowserSupported || !isWebRTCSupported) {
					// SDK 不支持当前浏览器，引导用户使用最新版的 Chrome 浏览器。
					alert('当前浏览器不支持视频会商功能，建议使用最新版的 Chrome或者Edge 浏览器！')
					return
				} else {
					if (!this.userName) {
						alert('请输入用户姓名！')
						return
					}
					getRoom(this.roomId).then((res) => {
						if (res.code != 0 || res.data.roomStatus == 2) {
							this.$message({
								message: '会议不存在或已结束！',
								type: 'error',
							})
							return
						}
						let data = {
							type: '1', //1申请入会，2被解散
							message: this.getUrlParam('userId').split('_')[1] + '_' + this.userName,
							sendUser: this.getUrlParam('userId').split('_')[1],
							acceptUser: res.data.mobile,
						}
						console.log(data, res)
						this.liveClient.send(JSON.stringify(data))
						// 创建自定义弹窗元素
						function showCustomAlert(message, duration = 3000) {
							const alertBox = document.createElement('div')
							alertBox.style.position = 'fixed'
							alertBox.style.top = '50%'
							alertBox.style.left = '50%'
							alertBox.style.transform = 'translate(-50%, -50%)'
							alertBox.style.padding = '20px'
							alertBox.style.background = 'white'
							alertBox.style.border = '1px solid #ccc'
							alertBox.style.borderRadius = '5px'
							alertBox.style.boxShadow = '0 0 10px rgba(0,0,0,0.2)'
							alertBox.style.zIndex = '9999'
							alertBox.style.textAlign = 'center'
							alertBox.textContent = message

							document.body.appendChild(alertBox)

							// 3秒后自动移除
							setTimeout(() => {
								document.body.removeChild(alertBox)
							}, duration)
						}

						// 使用示例
						showCustomAlert('请等待管理员同意入会...', 3000)
					})
				}
			})
		},
		async startMeeting(e) {
			joinRoom({
				roomNumber: this.roomId,
				mobile: this.getUrlParam('userId').split('_')[1],
				name: this.userName,
			}).then((res) => {
				// this.userSig = this.getUrlParam('userSig')
				this.userId = this.getUrlParam('userId') + '_' + this.userName
				//  location.href = location.href.slice(0, location.href.indexOf('?') > 0 ? location.href.indexOf('?') : location.href.length);
				const userSigGenerator = new LibGenerateTestUserSig(
					1600089532,
					'3a6edb36029177424289091eb1d729befde4297a6426c3d6f9e8df97ada4cb0b',
					604800,
				)
				// this.userSig = userSigGenerator.genTestUserSig(this.userId)
				getSig({
					userid: this.userId,
					expire: 1800,
					roomid: this.roomId,
					privilegeMap: 255,
				}).then(async (res) => {
					this.userSig = res.data
					await this.enterRoom()
					if (this.microphoneStatus) this.handleStartLocalAudio()
					if (this.cameraStatus) this.handleStartLocalVideo()
					// this.generateInviteLink()
					console.log('????????iiiiiiiiiii', this.remoteUsersViews)
					meetingInterval = setInterval(() => {
						this.duration++
					}, 1000)
					this.inRoom = true
					window.addEventListener('beforeunload', (e) => {
						e.preventDefault()
						// e.returnValue = '确定要离开当前页面吗？离开将直接退出会议室！'
						// confirm('确定要离开当前页面吗？离开将直接退出会议室！')
						return '确定要离开当前页面吗？离开将直接退出会议室！'
					})
					// 监听屏幕方向变化
					window.addEventListener('orientationchange', () => {
						// 判断当前方向
						if (window.orientation === 90 || window.orientation === -90) {
							// 横屏状态
							this.colScreen = false
						} else {
							this.colScreen = true
						}
					})
				})
			})
		},
		generateInviteLink() {
			// let sdkAppId = 1600089532
			// let sdkSecretKey = '3a6edb36029177424289091eb1d729befde4297a6426c3d6f9e8df97ada4cb0b'
			// // const { sdkAppId, sdkSecretKey, roomId } = this
			// const inviteUserId = `user_${parseInt(Math.random() * 100000000, 10)}`
			// const userSigGenerator = new LibGenerateTestUserSig(sdkAppId, sdkSecretKey, 604800)
			// const inviteUserSig = userSigGenerator.genTestUserSig(inviteUserId)
			// /* this.inviteLink = encodeURI(
			// 	`${location.origin}${location.pathname}#/invite?sdkAppId=${sdkAppId}&userSig=${inviteUserSig}&roomId=${roomId}&userId=${inviteUserId}`,
			// ) */
			// console.log(
			// 	encodeURI(
			// 		`${location.origin}${location.pathname}#/invite?sdkAppId=${sdkAppId}&userSig=${inviteUserSig}&roomId=${this.roomId}&userId=${inviteUserId}`,
			// 	),
			// 	'ttttttttttttt',
			// )
		},
		reportSuccessEvent(name) {
			const ext3 = name === 'enterRoom' ? this.sdkAppId : 0
			this.$aegis?.reportEvent({
				name,
				ext1: `${name}-success`,
				ext2: this.$DEMOKEY,
				ext3,
			})
		},
		reportFailedEvent(name, error, type = 'rtc') {
			this.$aegis?.reportEvent({
				name,
				ext1: `${name}-failed#${this.roomId}*${type === 'share' ? this.shareUserId : this.userId}*${error.message}`,
				ext2: this.$DEMOKEY,
				ext3: 0,
			})
		},
		changeLocalMicroStatus() {
			if (this.inRoom) {
				if (this.microphoneStatus) this.handleStopLocalAudio()
				else this.handleStartLocalAudio()
			}
			this.microphoneStatus = !this.microphoneStatus
		},
		changeLocalCameraStatus() {
			if (this.inRoom) {
				if (this.cameraStatus) this.handleStopLocalVideo()
				else this.handleStartLocalVideo()
			}
			this.cameraIndex = 0
			this.cameraStatus = !this.cameraStatus
		},
		async changeLocalCamera() {
			if (!this.cameraStatus) return

			const cameraList = await TRTC.getCameraList()
			console.log(cameraList, '移动端摄像头列表')

			// 如果没有第二个摄像头，直接返回
			if (cameraList.length < 2) return

			// 直接取反 cameraIndex（0 → 1，1 → 0）
			this.cameraIndex = this.cameraIndex === 0 ? 1 : 0

			// 切换到目标摄像头
			await this.trtc.updateLocalVideo({
				option: { cameraId: cameraList[this.cameraIndex].deviceId },
			})
		},
		async exit() {
			this.isClickExit = true
			await this.exitRoom()
		},
		async outRoom() {
			this.isClickExit = false
			await this.exitRoom()
		},
		clearAllTrack() {
			if (this.allTracks.length > 0) {
				this.allTracks.forEach((track) => {
					track.stop()
				})
			}
		},
		async getDefaultDeviceStream(constrict) {
			console.log(constrict)
			try {
				this.clearAllTrack()
				// 如果视频和音频都关闭，就不需要请求新的媒体流
				if (!constrict.video && !constrict.audio) {
					let videoEle = document.querySelector('#video-ele')
					if (videoEle) {
						videoEle.srcObject = null
					}
					return
				}
				const stream = await navigator.mediaDevices.getUserMedia(constrict)
				console.log('stream', stream)
				let videoEle = document.querySelector('#video-ele')
				console.log('videoEle', videoEle)
				if (videoEle) {
					this.allTracks = stream.getTracks()
					videoEle.srcObject = stream
					videoEle.onloadedmetadata = function(e) {
						videoEle.play()
					}
				}
			} catch (err) {
				this.cameraStatus = false
			}
		},
		// 用户点击挂断
		hangOff() {
			this.leaveRoom()
			// this.closeDialog()
		},
		toggleChat() {
			this.showChat = !this.showChat
			if (this.showChat) {
				this.unreadCount = 0
				this.$nextTick(() => {
					this.scrollToBottom()
					this.$refs.messageInput.focus()
				})
			}
		},
		sendMessage() {
			if (!this.newMessage.trim()) return

			this.messageList.push({
				// sender: 'me',
				// content: this.newMessage,
				// timestamp: new Date(),
				cmdId: 1,
				data: this.newMessage,
				seq: 11,
				userId: this.userId,
				timestamp: new Date(),
			})
			this.trtc.sendCustomMessage({
				cmdId: 1,
				data: new TextEncoder().encode(this.newMessage).buffer,
				timestamp: new Date(),
			})
			this.newMessage = ''
			this.scrollToBottom()

			// // 模拟回复
			// setTimeout(() => {
			// 	this.messages.push({
			// 		sender: '系统',
			// 		content: '您的消息已发送',
			// 		timestamp: new Date(),
			// 	})
			// 	this.scrollToBottom()
			// }, 500)
			if (!this.showChat) {
				this.unreadCount++
			}
		},
		scrollToBottom() {
			this.$nextTick(() => {
				const container = this.$refs.messagesContainer
				container.scrollTop = container.scrollHeight
			})
		},
		formatTime(date) {
			return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
		},
		toggleEmojiPicker() {
			// 这里可以集成emoji选择器
			alert('表情选择器暂未实现')
		},
	},
}
</script>

<style lang="scss" scoped>
.local-stream-content {
	width: 100%;
	height: 100%;
}
.remote-stream-container {
	width: 100%;
	height: 100%;
	// width: 320px;
	// height: 240px;
	margin: 0 10px 10px 0;
}

.title {
	margin-top: 5px;
	font-size: 17px;
	font-weight: bold;
}
.romeNum {
	border-bottom: 1px solid rgba(60, 60, 60, 0.12);
	padding: 3px 20px;
	margin-top: 40px;
}
.user-name {
	margin-top: 20px;
	font-size: 17px;
	font-weight: bold;
}
.video-ele {
	width: 100%;
	height: 339px;
	border-radius: 8px;
	object-fit: cover;
	margin-top: 20px;
	padding: 0 20px;
	box-sizing: border-box;
}
.btn {
	font-size: 18px;
	padding: 5px 25px;
	background-color: #59a1ff;
	border-radius: 6px;
	color: #fff;
	margin-top: 20px;
}
.participant {
	margin: 5px;
	border-radius: 8px;
	position: relative;
	width: calc(50% - 12px);
	background-color: #000;
	display: flex;
	height: 20vh;
}
.dialog-foot-group {
	display: flex;
	align-items: center;
	flex-wrap: nowrap;
	.foot-btn {
		display: flex;
		min-width: 60px;
		align-items: center;
		// padding: 0 32px;
		font-family: Source Han Sans CN;
		font-weight: 400;
		font-size: 14px;
		color: #000;
		cursor: pointer;
		&:not(:last-of-type) {
			border-right: 1px solid;
			border-image: linear-gradient(180deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.1)) 1 1;
		}
		&-icon {
			width: 28px;
			height: 28px;
			margin-right: 6px;
		}
		.mic-on {
			background: url('~@/assets/service/mic-on.png') no-repeat center / 100% 100%;
		}
		.mic-off {
			background: url('~@/assets/service/mic-off.png') no-repeat center / 100% 100%;
		}
		.cam-on {
			background: url('~@/assets/service/cam-on.png') no-repeat center / 100% 100%;
		}
		.cam-off {
			background: url('~@/assets/service/cam-off.png') no-repeat center / 100% 100%;
		}
		.cam-change {
			background: url('~@/assets/service/change_camera.png') no-repeat center / 100% 100%;
		}
		.hang-off {
			background: url('~@/assets/service/hang-off.png') no-repeat center / 100% 100%;
		}
	}
}
.meeting-contain {
	width: 100vw;
	height: 100%;
	padding: 8px;
	-ms-overflow-style: none;
	overflow-y: auto;
	scrollbar-width: none;
	&::-webkit-scrollbar {
		display: none; /* Chrome Safari */
	}
}
.board {
	height: 20vh;
	box-sizing: border-box;
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	// position: absolute;
	// left: 0;
	// top: 0;
	background: #f1f6fc;
	border-radius: 8px;
	border: 1px solid #316abe;
	&-avatar {
		width: 22%;
		height: auto;
		border-radius: 50%;
		aspect-ratio: 1/1;
	}
	&-icon {
		width: 22%;
		height: auto;
		border-radius: 50%;
		aspect-ratio: 1/1;
		display: flex;
		align-items: center;
		justify-content: center;
		background: #316abe;
		// width: 54px;
		// height: 54px;
		// margin-right: 20px;
		// border-radius: 50%;
		font-size: 24px;
		color: #ffffff;
	}
	&-img {
		width: 22%;
		height: auto;
		border-radius: 50%;
		aspect-ratio: 1/1;
		background: url('~@/assets/service/bohao.png') no-repeat center / 100% 100%;
	}
	.describe {
		background: rgba(0, 0, 0, 0.4);
	}
}
.describe {
	position: absolute;
	padding: 0 9px 0 10px;
	max-width: 70%;
	display: flex;
	align-items: center;
	font-family: Source Han Sans CN;
	font-weight: 400;
	font-size: 14px;
	color: #ffffff;
	left: 0px;
	bottom: 0px;
	background: rgba(0, 0, 0, 0.4);
	border-radius: 8px;
	.microphone {
		width: 11px;
		height: 16px;
		margin-right: 6px;
		&-active {
			background: url('~@/assets/service/active-micro.png') no-repeat center / 100% 100%;
		}
		&-inactive {
			background: url('~@/assets/service/ban-micro.png') no-repeat center / 100% 100%;
		}
	}
	.identity {
		white-space: nowrap;
	}
}
/* 聊天面板 */
.chat-panel {
	position: fixed;
	bottom: 40px;
	left: 0;
	width: 100%;
	background: white;
	border-top-left-radius: 16px;
	border-top-right-radius: 16px;
	box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
	transform: translateY(calc(100% - 48px));
	transition: transform 0.3s ease;
	z-index: 100;
	max-height: 70vh;
	display: flex;
	flex-direction: column;
}

.chat-panel.active {
	transform: translateY(0);
}

.chat-header {
	padding: 12px 16px;
	background: #f5f5f5;
	border-top-left-radius: 16px;
	border-top-right-radius: 16px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	font-weight: bold;
	cursor: pointer;
}

.unread-count {
	background: #ff4757;
	color: white;
	border-radius: 50%;
	width: 20px;
	height: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 12px;
	margin-left: 8px;
}

.arrow {
	border: solid #666;
	border-width: 0 2px 2px 0;
	display: inline-block;
	padding: 3px;
	transition: transform 0.3s;
}

.arrow.up {
	transform: rotate(-135deg);
}

.arrow.down {
	transform: rotate(45deg);
}

/* 消息区域 */
.messages-container {
	flex: 1;
	overflow-y: auto;
	padding: 12px;
	background: #fff;
	height: 20vh;
}

.message {
	margin-bottom: 16px;
	max-width: 80%;
}

.message.sent {
	margin-left: auto;
}

.message.received {
	margin-right: auto;
}

.sender {
	font-size: 12px;
	color: #666;
	margin-bottom: 4px;
}

.content {
	padding: 10px 14px;
	border-radius: 18px;
	font-size: 15px;
	line-height: 1.4;
	word-break: break-word;
}

.sent .content {
	background: #007aff;
	color: white;
	border-bottom-right-radius: 4px;
}

.received .content {
	background: #e5e5ea;
	color: black;
	border-bottom-left-radius: 4px;
}

.time {
	font-size: 11px;
	color: #999;
	margin-top: 4px;
	text-align: right;
}

/* 输入区域 */
.input-container {
	display: flex;
	padding: 8px;
	background: #f5f5f5;
	border-top: 1px solid #ddd;
	align-items: center;
}

input {
	flex: 1;
	border: 1px solid #ddd;
	border-radius: 20px;
	padding: 10px 15px;
	margin: 0 8px;
	font-size: 15px;
	outline: none;
}

.emoji-btn {
	background: none;
	border: none;
	font-size: 20px;
	padding: 8px;
	cursor: pointer;
}

.send-btn {
	background: #007aff;
	color: white;
	border: none;
	border-radius: 20px;
	padding: 10px 16px;
	font-size: 15px;
	font-weight: bold;
	cursor: pointer;
}

.send-btn:disabled {
	background: #ccc;
	cursor: not-allowed;
}
.screen-contain {
	width: 92vw;
	position: absolute;
	// top: 50%;
	// translate: 0 -50%;
	margin-left: 4vw;
	// height: 100vw;
	display: flex;
	align-items: center;
	justify-content: center;
	.remote-screen-container {
		width: 100%;
		.screen-share-name {
			position: absolute;
			width: 100%;
			text-align: center;
			font-size: 17px;
			font-weight: bold;
		}
	}
}
</style>
<style lang="scss">
// @import './style/multipleMeetingStyle.scss';
</style>
