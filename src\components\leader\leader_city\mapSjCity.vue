<template>
  <div class="sj_box">
    <div class="title">
      <p>事件详情</p>
      <div class="del" @click="close"></div>
    </div>
    <div class="content">
      <div class="info">
        <div class="line">
          <div>* 事件编号:</div>
          <div>20220805745</div>
        </div>
        <div class="line">
          <div>* 案件来源:</div>
          <div>巡查上报</div>
        </div>
        <div class="line">
          <div>* 事件分类:</div>
          <div>市容环境-占道经营</div>
        </div>
        <div class="line">
          <div>* 权责部门:</div>
          <div>城管局</div>
        </div>
        <div class="line">
          <div>* 事件描述:</div>
          <div>街面违规经营事件发现</div>
        </div>
        <div class="line">
          <div>* 所属网格:</div>
          <div>xx街道xx网格</div>
        </div>
        <div class="line">
          <div>* 队长姓名:</div>
          <div>董小伟</div>
        </div>
        <div class="line">
          <div>* 队长电话:</div>
          <div>13098746183</div>
        </div>
        <div class="line">
          <div>* 现场照片:</div>
          <div></div>
        </div>
      </div>
      <div class="pics_">
        <div v-for="(item, index) in srcList" :key="index">
          <el-image style="width: 135px; height: 68px" :src="item" :preview-src-list="srcList">
          </el-image>
        </div>
      </div>
    </div>
  </div>
</template>

<script type="text/ecmascript-6">
export default {
  name: 'mapSjCity',
  data() {
    return {
      srcList: [
        require('@/assets/leader/img/leader_city/xc_pic1.png'),
        require('@/assets/leader/img/leader_city/xc_pic2.png'),
      ],
    }
  },
  created() {},
  mounted() {},
  methods: {
    close() {
      this.$emit('close')
    },
  },
}
</script>

<style lang="scss" scoped>
@font-face {
  font-family: FZSKJWGB1;
  src: url(~@/assets/leader/font/FZSKJWGB1.ttf);
}
.sj_box {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 101;
  width: 351px;
  height: 445px;
  background: url(~@/assets/leader/img/leader_ldzh/map/map_sj_bg.png) no-repeat;
  background-size: 100% 100%;
  padding: 0 0 28px 0;
  // border: 1px solid red;
  .title {
    margin: 20px 29px 25px 31px;
    background: url(~@/assets/leader/img/leader_ldzh/map/map_sj_title.png) no-repeat;
    background-size: 100% 100%;
    width: 291px;
    height: 33px;
    position: relative;
    p {
      font-size: 18px;
      font-family: FZSKJWGB1;
      font-weight: normal;
      color: #22fcff;
      height: 33px;
      letter-spacing: 1px;
      text-shadow: 0px 0px 4px #00353a;
      background: linear-gradient(180deg, #ffffff 0%, #01c3ff 100%);
      -webkit-background-clip: text;
      // -webkit-text-fill-color: transparent;
    }
    .del {
      background: url(~@/assets/leader/img/leader_ldzh/map/map_sj_del.png) no-repeat;
      background-size: 100% 100%;
      width: 29px;
      height: 36px;
      position: absolute;
      right: -12px;
      bottom: 0;
    }
  }
  .content {
    // border: 1px solid red;
    .info {
      padding: 0 34px 0 33px;
      .line {
        height: 14px;
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
        & > div:first-of-type {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 20px;
        }
        & > div:last-of-type {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 20px;
        }
      }
    }
    .pics_ {
      padding: 0 36px 0 34px;
      margin-top: 11px;
      display: flex;
      justify-content: space-between;
      & > div {
        width: 135px;
        height: 68px;
      }
    }
  }
}
</style>
