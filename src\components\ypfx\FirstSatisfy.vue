<template>
  <div class="container">
    <div class="progress">
      <div class="satisfied" :style="{width: `${data.value1}%`}"></div>
      <div class="normal_satisfied" :style="{width: `${data.value2}%`}"></div>
      <div class="unsatisfied" :style="{width: `${data.value3}%`}"></div>
    </div>
    <div class="text_">
      <div class="text_satisfied">满意{{ data.value1 }}%</div>
      <div class="text_normal_satisfied">不满意{{ data.value2 }}%</div>
      <div class="text_unsatisfied">其他{{ data.value3 }}%</div>
    </div>
  </div>
</template>
<script>
export default {
  components: {},
  props: {
    data: {
      type: Object,
      default: () => {
        return {
          value1: 30,
          value2: 20,
          value3: 50
        }
      }
    }
  },
  data () {
    return {
    }
  },
  created () { },
  mounted () { },
  watch: {},
  computed: {},
  methods: {
  },
};
</script>
<style lang="scss" scoped>
.container {
  width: calc(100% - 20px);
  height: 50px;
  .progress {
    height: 30px;
    // display: flex;
    border: 1px solid #000;
    padding: 1px;
    line-height: 28px;
    gap: 1px;
    .satisfied {
      height: 100%;
      background: #50e9c3;
      float: left;
    }
    .normal_satisfied {
      height: 100%;
      background: #ff6d6d;
      float: left;
    }
    .unsatisfied {
      height: 100%;
      background: #ffb165;
      float: left;
      white-space: nowrap;
    }
  }
  .text_ {
    height: 20px;
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
    color: #ffffff;
    .text_satisfied {
    }
    .text_normal_satisfied {
    }
    .text_unsatisfied {
    }
  }
}
</style>