import * as L from 'leaflet';
import 'leaflet/dist/leaflet.css';
// 引入 leaflet.markercluster
import 'leaflet.markercluster/dist/MarkerCluster.css';
import 'leaflet.markercluster/dist/MarkerCluster.Default.css';
import { MarkerClusterGroup } from 'leaflet.markercluster';

// 解决默认 maker 无法显示的问题
import icon from 'leaflet/dist/images/marker-icon.png';
import iconShadow from 'leaflet/dist/images/marker-shadow.png';

/* 
  leaflet 新版本没有挂载聚合图层，需要自己挂载
*/
L.markerClusterGroup = MarkerClusterGroup;
const DefaultIcon = L.icon({
  iconUrl: icon,
  shadowUrl: iconShadow
});
L.Marker.prototype.options.icon = DefaultIcon;

/**
 * 创建聚合图层(环卫数据)
 */
export const createMakerCluster = () => {
  return new L.markerClusterGroup({
    spiderfyOnMaxZoom: true,
    showCoverageOnHover: false,
    zoomToBoundsOnClick: false,
    chunkedLoading: true,
    disableClusteringAtZoom: 19, // 不聚合级别
    maxClusterRadius: 60
  });
};

export const createMaker = (latlng, iconUrl, opt) => {
  let { lat, lon } = transformGCJ2WGS(latlng[0], latlng[1]);
  let icon = createIcon(iconUrl, opt);
  let coordinates = new L.LatLng(lat, lon);
  return L.marker(coordinates, { icon: icon });
};
/**
 * 创建图标
 */
export const createIcon = (iconUrl, options) => {
  let icon = new L.Icon({
    iconUrl: iconUrl,
    // shadowUrl: '',
    iconSize: [options.width||60, options.height||60],
    iconAnchor: [options.left, options.top],
    popupAnchor: [1, 15]
    // shadowSize: [41, 41]
  });
  return icon;
};

const PI = 3.14159265358979324;
/**
 * 纠偏
 * @param {*} gcjLat
 * @param {*} gcjLon
 */
export function transformGCJ2WGS(gcjLat, gcjLon) {
  let d = delta(gcjLat, gcjLon);
  return {
    lat: gcjLat - d.lat,
    lon: gcjLon - d.lon
  };
}

function delta(lat, lon) {
  let a = 6378245.0; //  a: 卫星椭球坐标投影到平面地图坐标系的投影因子。
  let ee = 0.00669342162296594323; //  ee: 椭球的偏心率。
  let dLat = transformLat(lon - 105.0, lat - 35.0);
  let dLon = transformLon(lon - 105.0, lat - 35.0);
  let radLat = (lat / 180.0) * PI;
  let magic = Math.sin(radLat);
  magic = 1 - ee * magic * magic;
  let sqrtMagic = Math.sqrt(magic);
  dLat = (dLat * 180.0) / (((a * (1 - ee)) / (magic * sqrtMagic)) * PI);
  dLon = (dLon * 180.0) / ((a / sqrtMagic) * Math.cos(radLat) * PI);
  return {
    lat: dLat,
    lon: dLon
  };
}

function transformLat(x, y) {
  let ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
  ret += ((20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0) / 3.0;
  ret += ((20.0 * Math.sin(y * PI) + 40.0 * Math.sin((y / 3.0) * PI)) * 2.0) / 3.0;
  ret += ((160.0 * Math.sin((y / 12.0) * PI) + 320 * Math.sin((y * PI) / 30.0)) * 2.0) / 3.0;
  return ret;
}

function transformLon(x, y) {
  let ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
  ret += ((20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0) / 3.0;
  ret += ((20.0 * Math.sin(x * PI) + 40.0 * Math.sin((x / 3.0) * PI)) * 2.0) / 3.0;
  ret += ((150.0 * Math.sin((x / 12.0) * PI) + 300.0 * Math.sin((x / 30.0) * PI)) * 2.0) / 3.0;
  return ret;
}

export default L;
