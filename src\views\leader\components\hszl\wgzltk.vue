<template>
  <div class="tkBox">
    <div class="tkTitle"><span>网格治理</span></div>
    <img @click="closeEmitai" src="@/assets/hszl/tkGb.png" class="tkGb" />
    <div class="tkContent">
      <BlockBox
        title="一标三实"
        subtitle="One bid, three real·"
        class="tkList"
        :isListBtns="false"
        :blockHeight="296"
      >
        <ul class="ybsscont">
          <li v-for="(it, index) of ybssData" :key="index">
            <span class="icon" :style="{ background: `url(${it.img}) no-repeat` }"></span>
            <div class="tit">{{ it.tit }}</div>
            <div class="num">
              <countTo
                ref="countTo"
                :startVal="$countTo.startVal"
                :decimals="$countTo.decimals(it.num)"
                :endVal="it.num"
                :duration="$countTo.duration"
              />
              <span>{{ it.unit }}</span>
            </div>
          </li>
        </ul>
      </BlockBox>
      <BlockBox
        title="网格建设情况"
        subtitle="Grid construction"
        class="tkList"
        :isListBtns="false"
        :blockHeight="296"
      >
        <div class="wrapper wrapper2">
          <div class="wrapperList1">
            <div class="wrapperItem">
              <img src="@/assets/hszl/wgzlImg5.png" class="wrapperLeft" />
              <div class="wrapperRight">
                <div class="wrapperNum">{{ wgjsValue1 }}<span>个</span></div>
                <div class="wrapperNamu">网格数</div>
              </div>
            </div>
            <div class="wrapperItem">
              <img src="@/assets/hszl/wgzlImg6.png" class="wrapperLeft" />
              <div class="wrapperRight">
                <div class="wrapperNum">{{ wgjsValue2 }}<span>个</span></div>
                <div class="wrapperNamu">综合网格数</div>
              </div>
            </div>
            <div class="wrapperItem">
              <img src="@/assets/hszl/wgzlImg7.png" class="wrapperLeft" />
              <div class="wrapperRight">
                <div class="wrapperNum">{{ wgjsValue3 }}<span>个</span></div>
                <div class="wrapperNamu">专属网格数</div>
              </div>
            </div>
          </div>
          <div class="fy_box">
            <img class="fy_out" src="@/assets/shzl/fy_out.png" alt="" />
            <img class="fy_in" src="@/assets/shzl/fy_in.png" alt="" />
            <div>
              <ul>
                <li v-for="(it, index) in fyData.slice(0, 2)" :key="index">
                  <countTo
                    ref="countTo"
                    :startVal="$countTo.startVal"
                    :decimals="$countTo.decimals(it.num)"
                    :endVal="it.num"
                    :duration="$countTo.duration"
                  />
                  <span>{{ it.name }}</span>
                </li>
              </ul>
              <ul>
                <li v-for="(it, index) in fyData.slice(2)" :key="index">
                  <countTo
                    ref="countTo"
                    :startVal="$countTo.startVal"
                    :decimals="$countTo.decimals(it.num)"
                    :endVal="it.num"
                    :duration="$countTo.duration"
                  />
                  <span>{{ it.name }}</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </BlockBox>
      <BlockBox
        v-if="false"
        title="网格规范达标率重点指标"
        subtitle=""
        class="tkList"
        :isListBtns="false"
        :blockHeight="296"
      >
        <div class="wrapper wrapper4">
          <div class="wrapperList" v-for="(item, i) in wggfData" :key="i">
            <div class="wrapperList1">
              <div class="wrapperLeft">{{ item.name }}</div>
              <div class="wrapperRight">{{ item.sysvalue }}<span>%</span></div>
            </div>
            <div class="wrapperList2">
              <div class="wrapperSelected" :style="{ width: item.sysvalue }"></div>
            </div>
          </div>
          <!-- <div class="wrapperList">
          <div class="wrapperList1">
            <div class="wrapperLeft">巡查走访率</div>
            <div class="wrapperRight">95<span>%</span></div>
          </div>
          <div class="wrapperList2">
            <div class="wrapperSelected"></div>
          </div>
        </div>
        <div class="wrapperList">
          <div class="wrapperList1">
            <div class="wrapperLeft">事件办结率</div>
            <div class="wrapperRight">99.9<span>%</span></div>
          </div>
          <div class="wrapperList2">
            <div class="wrapperSelected"></div>
          </div>
        </div>
        <div class="wrapperList">
          <div class="wrapperList1">
            <div class="wrapperLeft">人员关联率</div>
            <div class="wrapperRight">100<span>%</span></div>
          </div>
          <div class="wrapperList2">
            <div class="wrapperSelected"></div>
          </div>
        </div> -->
        </div>
      </BlockBox>
      <BlockBox
        v-if="false"
        title="网格事件分类统计"
        subtitle=""
        class="tkList"
        :isListBtns="false"
        :blockHeight="296"
      >
        <div class="wrapper">
          <div class="select_" v-if="false">
            <el-dropdown @command="wgsjYearMethod">
              <el-button class="select_" type="primary">
                {{ wgcurrentYear }}年<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu class="select_" slot="dropdown">
                <el-dropdown-item v-for="item in wgyearList" :key="item" :command="item"
                  >{{ item }}年</el-dropdown-item
                >
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <div class="select_month" v-if="false">
            <el-dropdown @command="wgsjMonthMethod">
              <el-button class="select_month" type="primary">
                {{ wgcurrentMonth }}月<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu class="select_month" slot="dropdown">
                <el-dropdown-item v-for="item in wgsjmonthList" :key="item" :command="item"
                  >{{ item }}月</el-dropdown-item
                >
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <PieChart3D type="1" :data="chartData" :options="box4Options" />
        </div>
      </BlockBox>
    </div>
    <div class="center">
      <!-- <glWgMap @wgInfoItem="wgInfoItem" /> -->
      <wgEcharts @wgInfoItem="wgInfoItem" />
    </div>
    <wgTable
      v-if="iswgTableSHow"
      :wgTableObj="wgTableObj"
      @videoMethod="videoMethod"
      @close="iswgTableSHow = false"
    />
    <!-- 融合通信单人 -->
    <rhtxVideo
      v-if="singleVideoShow"
      @closeVideoEmit="singleVideoShow = false"
      :phone="singlePhone"
      :callType="singleCallType"
    />
  </div>
</template>

<script>
import BlockBox from '@/components/leader/common/blockBox.vue'
import SwiperTable from '@/components/leader/leader_zwjs/SwiperTable.vue'
import glWgMap from '@/components/common/glWgMap.vue'
import wgTable from '@/components/zhny/wgTable.vue'
import rhtxVideo from '@/components/rhtx/rhtxVideo.vue'
import wgEcharts from '@/components/hszl/wgEcharts.vue'
import { getYbss, getWgjsqk, getWgGf, getWgSjFltj, zlWgInfo } from '@/api/hs/hs.hszl.js'

export default {
  name: 'wgzltk',
  components: {
    BlockBox,
    SwiperTable,
    glWgMap,
    wgTable,
    rhtxVideo,
    wgEcharts,
  },
  data() {
    return {
      wgcurrentYear: this.dayjs().year(),
      wgcurrentMonth: '1',
      wgsjmonthList: [1, 2, 3],
      wgyearList: [],
      fyData: [
        {
          num: 22,
          name: '网格长/人',
        },
        {
          num: 114,
          name: '专属网格员/人',
        },
        {
          num: 17,
          name: '综合网格员/人',
        },
        {
          num: 346,
          name: '微网格员/人',
        },
      ],
      chartData: [
        ['product', '社会面小场所'],
        ['消防安全管理 10%', 100],
        ['矛盾治理 15%', 150],
        ['治安安全 20%', 200],
        ['城市事件管理 25%', 250],
        ['城市部件管理 30%', 300],
      ],
      box4Options: {
        unit: '件',
        title: {
          fontSize: '16px',
          top: 60,
        },
        subtitle: {
          fontSize: '14px',
          top: 80,
        },
      },
      ybssData: [
        {
          num: 31083,
          tit: '标准地址(个)',
          img: require('@/assets/hszl/wgzlImg1.png'),
        },
        {
          num: 79856,
          tit: '实有人口(人)',
          img: require('@/assets/hszl/wgzlImg2.png'),
        },
        {
          num: 36918,
          tit: '实有房屋(个)',
          img: require('@/assets/hszl/wgzlImg3.png'),
        },
        {
          num: 2088,
          tit: '实有单位(个)',
          img: require('@/assets/hszl/wgzlImg4.png'),
        },
      ],
      options: {
        yAxisMax: 1000,
        barColor: '#02C4DD',
        showBarLabel: true,
      },
      data: [
        ['produce', '系列名'],
        ['2018', '830'],
        ['2019', '460'],
        ['2020', '700'],
        ['2021', '800'],
        ['2022', '580'],
      ],
      box4BottomData: {
        data: [
          ['product', '事件总数'],
          ['燃气监测', 861],
          ['水质监测', 1026],
          ['烟感监测', 334],
          ['环境监测', 107],
        ],
        options: {
          colors: ['#80CF83', '#CDCF72', '#0280DF', '#13A4C0'],
          legend: {
            top: 2,
            // left: 100,
            // orient: 'vertical',
            itemWidth: 10,
            itemHeight: 10,
            itemRadius: 2,
            itemPadding: 5,
            itemDistance: 12,
            itemMarginTop: 12,
            textColor: 'rgba(255, 255, 255, 0.85)',
            fontWeight: '400',
            fontSize: '12px',
            fontFamily: 'DINPro-Medium',
            letterSpacing: '3px',
          },
          position: ['50%', '10%'],
          bgImg: {
            width: '60%',
            height: '60%',
            top: '42%',
            left: '50%',
          },
          unit: '件',
          title: {
            fontSize: '16px',
            top: 80,
          },
          subtitle: {
            fontSize: '14px',
            top: 100,
          },
        },
      },
      box1BottomData: {
        data: [
          ['product', '总量：吨', '系列名2', '总量：立方'],
          ['02', 50, 100, 99],
          ['04', 55, 100, 80],
          ['06', 25, 100, 70],
          ['08', 55, 100, 60],
          ['10', 55, 100, 77],
          ['12', 55, 100, 66],
        ],
        options: {},
      },
      wgjsValue1: '',
      wgjsValue2: '',
      wgjsValue3: '',
      wggfData: [],
      wgTableObj: {},
      iswgTableSHow: false,
      singleVideoShow: '',
      singlePhone: '',
      singleCallType: '',
    }
  },
  created() {
    let oldYear1 = 2020
    let years1 = this.dayjs().year() - oldYear1
    console.log(years1)
    for (let i = 0; i < years1 + 1; i++) {
      this.wgyearList.push(oldYear1 + i)
    }

    this.getYbssFn()
    this.getWgjsqkFn()
    this.getWgGfFn()
    this.getWgSjFltjFn()
  },
  methods: {
    closeEmitai() {
      this.$emit('closeEmitai')
    },
    wgsjYearMethod(item) {
      this.wgcurrentYear = item
    },
    wgsjMonthMethod(item) {
      this.wgcurrentMonth = item
    },
    async getYbssFn() {
      let res = await getYbss()
      if (res?.code == '200') {
        this.ybssData[0].num = Number(res.result[0].sysvalue)
        this.ybssData[1].num = Number(res.result[1].sysvalue)
        this.ybssData[2].num = Number(res.result[2].sysvalue)
        this.ybssData[3].num = Number(res.result[3].sysvalue)
      }
    },
    async getWgjsqkFn() {
      let res = await getWgjsqk()
      if (res?.code == '200') {
        this.wgjsValue1 = Number(res.result[0].sysvalue)
        this.wgjsValue2 = Number(res.result[1].sysvalue)
        this.wgjsValue3 = Number(res.result[2].sysvalue)
        this.fyData[0].num = Number(res.result[3].sysvalue)
        this.fyData[1].num = Number(res.result[4].sysvalue)
        this.fyData[2].num = Number(res.result[5].sysvalue)
        this.fyData[3].num = Number(res.result[6].sysvalue)
      }
    },
    async getWgGfFn() {
      let res = await getWgGf()
      if (res?.code == '200') {
        this.wggfData = res.result
      }
    },
    async getWgSjFltjFn() {
      let res = await getWgSjFltj()
      if (res?.code == '200') {
        this.chartData = [
          ['product', ''],
          ...res.result.map((item) => [item.name, Number(item.sysvalue)]),
        ]
      }
    },
    async wgInfoItem(val) {
      // console.log(val)
      let res = await zlWgInfo({ gridName: val })
      if (res.code == '200') {
        if (!res.result) {
          return this.$message.warning('无数据')
        }
        this.wgTableObj = res.result
        this.iswgTableSHow = true
      }
    },
    videoMethod(val) {
      this.singleVideoShow = true
      this.singlePhone = val
      this.singleCallType = 'video'
    },
  },
}
</script>

<style lang="less" scoped>
@keyframes rotateS {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
.tkBox {
  width: 1200px;
  height: 843px;
  background: url(~@/assets/hszl/tkBg.png) no-repeat center / 100% 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10000;
  padding: 0 43px;
  .tkTitle {
    width: 634px;
    height: 74px;
    background: url(~@/assets/hszl/tkTitle.png) no-repeat center / 100% 100%;
    margin: 0 auto;
    margin-top: 22px;
    line-height: 74px;
    span {
      font-size: 36px;
      font-family: PingFangSC, PingFang SC;
      color: #ffffff;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .tkGb {
    position: absolute;
    top: 38px;
    right: 43px;
  }
  .tkContent {
    width: 100%;
    height: calc(100% - 74px - 90px);
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    align-content: space-between;
    justify-content: space-between;
    .tkList {
      margin: 0 32px;
      margin-top: 26px;
      // position: relative;
      .wrapper {
        width: 460px;
        height: 263px;
        .select_ {
          position: absolute;
          right: 36px;
          top: -28px;
          ::v-deep .el-dropdown button {
            width: 88px;
            height: 24px;
            border-radius: 12px;
            background: url(~@/assets/shzl/select_bg.png) no-repeat center / 100% 100%;
            border: none;
            padding: 0;
            // background: linear-gradient(180deg, #0e63ac 0%, #253c69 100%);
            // border-radius: 2px;
            // border: 2px solid;
            // border-image: linear-gradient(180deg, rgba(63, 148, 224, 0.2), rgba(218, 250, 255, 0.3))
            //   2 2;
          }
        }
        .select_month {
          position: absolute;
          right: 52px;
          top: -27px;
          ::v-deep .el-dropdown button {
            width: 56px;
            height: 24px;
            right: -46px;
            top: -28px;
            border-radius: 12px;
            background: url(~@/assets/shzl/select_bg.png) no-repeat center / 100% 100%;
            border: none;
            padding: 0;
            // background: linear-gradient(180deg, #0e63ac 0%, #253c69 100%);
            // border-radius: 2px;
            // border: 2px solid;
            // border-image: linear-gradient(180deg, rgba(63, 148, 224, 0.2), rgba(218, 250, 255, 0.3))
            //   2 2;
          }
        }
      }
      .ybsscont {
        height: 100%;
        padding: 14px 44px 24px 37px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-around;
        li {
          position: relative;
          width: 165px;
          height: 66px;
          background: url(~@/assets/shzl/ybss_bg.png) no-repeat;
          display: flex;
          flex-direction: column;
          justify-content: center;
          text-align: left;
          padding-left: 92px;
          .icon {
            position: absolute;
            width: 26px;
            height: 26px;
            top: 10px;
            left: 31px;
            animation: move infinite 3s ease-in-out;
            @keyframes move {
              0% {
                transform: translateY(0px) rotateY(0);
              }
              50% {
                transform: translateY(-10px) rotateY(180deg);
              }
              100% {
                transform: translateY(0px) rotateY(360deg);
              }
            }
          }
          &:nth-child(2) {
            span {
              animation-delay: 0.3s;
            }
          }
          &:nth-child(3) {
            span {
              animation-delay: 0.6s;
            }
          }
          &:nth-child(4) {
            span {
              animation-delay: 0.9s;
            }
          }
          &:nth-child(5) {
            span {
              animation-delay: 1.2s;
            }
          }
          &:nth-child(6) {
            span {
              animation-delay: 1.5s;
            }
          }
          .tit {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 20px;
            white-space: nowrap;
          }
          .num {
            font-size: 18px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 22px;
          }
        }
      }
      .wrapper2 {
        .wrapperList1 {
          width: 460px;
          height: 47px;
          margin-top: 19px;
          .wrapperItem {
            width: 33.33%;
            height: 47px;
            float: left;
            .wrapperLeft {
              float: left;
              margin-left: 13px;
            }
            .wrapperRight {
              float: left;
              text-align: left;
              margin-left: 12px;
              .wrapperNum {
                font-size: 22px;
                font-family: DINOT-Black, DINOT;
                font-weight: 900;
                color: #ffffff;
                line-height: 22px;
                margin-top: 1px;
                span {
                  font-size: 14px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: 400;
                  color: #ffffff;
                  line-height: 20px;
                  margin-left: 4px;
                }
              }
              .wrapperNamu {
                font-size: 14px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                color: #ffffff;
                line-height: 20px;
                margin-top: 1px;
              }
            }
          }
        }
        .fy_box {
          padding: 32px 0 0 23px;
          // border: 1px solid red;
          position: relative;
          display: flex;
          position: relative;
          & > div:last-of-type {
            // border: 1px solid red;
            width: 393px;
            height: 169px;
            background: url(~@/assets/shzl/fy_bg.png) no-repeat center / 100% 100%;
            background-size: 100% 100%;
            padding: 13px 3px 12px 22px;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-content: space-between;
            ul {
              height: 100%;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              li {
                display: flex;
                flex-direction: column;
                text-align: left;
                & span:first-of-type {
                  font-size: 20px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: normal;
                  color: #ffffff;
                  line-height: 24px;
                }
                & span:last-of-type {
                  margin-top: 4px;
                  font-size: 14px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: 400;
                  color: #ffffff;
                  line-height: 17px;
                }
              }
            }
          }
          .fy_out {
            position: absolute;
            top: 8%;
            left: 26%;
            z-index: 99;
            transform: translate(-50%, -50%);
            animation: rotateS infinite 12s linear;
          }
          .fy_in {
            position: absolute;
            top: 58%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
        }
      }
      .wrapper4 {
        padding-top: 10px;
        .wrapperList {
          width: 423px;
          height: 56px;
          margin: 0 auto;
          .wrapperList1 {
            overflow: hidden;
            margin-top: 18px;
            .wrapperLeft {
              float: left;
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
            }
            .wrapperRight {
              float: right;
              font-size: 20px;
              font-family: DINCond-Black, DINCond;
              font-weight: 900;
              color: #ffffff;
              line-height: 24px;
              span {
                font-size: 14px;
              }
            }
          }
          .wrapperList2 {
            width: 423px;
            height: 6px;
            background: rgba(66, 104, 146, 0.6);
            border-radius: 4px;
            opacity: 0.56;
            margin-top: 7px;
            .wrapperSelected {
            }
          }
          &:nth-child(1) .wrapperList2 {
            .wrapperSelected {
              // width: 95%;
              height: 6px;
              background: linear-gradient(270deg, #fc1405 0%, #ff6f4c 100%);
              border-radius: 4px;
            }
          }
          &:nth-child(2) .wrapperList2 {
            .wrapperSelected {
              // width: 95%;
              height: 6px;
              background: linear-gradient(270deg, #ffb819 0%, #ffde94 100%);
              border-radius: 4px;
            }
          }
          &:nth-child(3) .wrapperList2 {
            .wrapperSelected {
              // width: 99.9%;
              height: 6px;
              background: linear-gradient(270deg, #1afff8 0%, #bdfffe 100%);
              border-radius: 4px;
            }
          }
          &:nth-child(4) .wrapperList2 {
            .wrapperSelected {
              // width: 100%;
              height: 6px;
              background: linear-gradient(270deg, #12acff 0%, #9bdbff 100%);
              border-radius: 4px;
            }
          }
        }
      }
    }
  }
  .center {
    position: absolute;
    right: 0%;
    // transform: translateX(-50%);
    top: 96px;
    z-index: 103;
    width: 700px;
    height: calc(100% - 74px - 46px);
  }
}
</style>
