<template>
	<div class="leader_box">
		<!-- 左侧和相关农机数据可视化 -->
		<div class="leader_sjts">
			<div class="left" :class="mkdx ? 'mkdxLeft' : ''">
				<!-- 左侧数据可视化 -->
				<FarmingSeasonSelect
					:dtDay="dtDay"
					:curSeason="curSeason"
					@dateSwitch="dateSwitch"
					@seasonSwitch="seasonSwitch"
				></FarmingSeasonSelect>
				<BlockBox
					leftTitle="作业进度"
					rightTitle="NONGYEFUWUCHANYE"
					style="z-index: 998"
					subtitle="Full Factor Grid Events"
					class="box box8"
					blockTitleClass="use-long-title-header-bg"
					:isListBtns="false"
					:blockHeight="236"
					:blockBackgroundImage="block3BackImg"
				>
					<ZyjdBlockBox style="z-index: 997" :contentData="zyjdStatisticData"></ZyjdBlockBox>
				</BlockBox>
				<BlockBox
					leftTitle="机械化率"
					style="z-index: 998"
					rightTitle="JIXIEHUALU"
					subtitle="Party Building Information"
					class="box box7"
					:isListBtns="false"
					:blockHeight="421"
					:blockBackgroundImage="block2BackImg"
				>
					<BlockBoxChildNoHeader1 style="z-index: 997" contentNumberUnit="%" :contentData="mechRateDataList"></BlockBoxChildNoHeader1>
				</BlockBox>

				<BlockBox
					leftTitle="农机应急力量"
					rightTitle="NONGYEFUWUCHANYE"
					style="z-index: 998"
					subtitle="Full Factor Grid Events"
					class="box box8"
					blockTitleClass="use-long-title-header-bg"
					:isListBtns="false"
					:blockHeight="188"
					:blockBackgroundImage="block3BackImg"
				>
					<ZyjdBlockBox style="z-index: 997" :contentData="zyjdStatisticData"></ZyjdBlockBox>
				</BlockBox>
			</div>
			<div class="top-middle" :style="{ left: mapBtnLeft + 'px' }">
				<div class="view-type-box" :style="{ top: mapBtnTop + 'px', left: 0 + 'px' }">
					<div class="layer-select-box" @click="handleClickLayer">
						图层
					</div>
				</div>
				<div
					v-show="showLayerList"
					:style="{ top: mapBtnTop + 60 + 'px', left: 0 + 'px' }"
					:class="showLayerList ? '' : 'hide-njfbBox'"
					class="njfbBox"
				>
					<el-tree
						ref="layerTree"
						:data="layerData"
						show-checkbox
						node-key="id"
						:default-expanded-keys="defaultExpandedKeys"
						:default-checked-keys="defaultCheckedKeys"
						:props="defaultProps"
						@check="handleCheck($event)"
					>
					</el-tree>
				</div>
			</div>

			<div class="right" :class="mkdx ? 'mkdxRight' : ''">
				<!-- 右侧农机数据可视化 -->
				<div class="right1">
					<!-- :marginTop="72" -->
				</div>
			</div>
		</div>
		<!-- 底部农机数据可视化 -->
		<div class="middle" :class="mkdx ? 'mkdxMiddle' : ''"></div>
		<!-- 地图组件 -->
		<div class="map_box">
			<LeaMap
				ref="leafletMap"
				@poiClick="poiClick"
				@gridClick="gridClick"
				@operate="operate"
				@qctc="qctc"
				@updateWaterLayerCount="updateWaterLayerCount"
				:clear2="true"
				:back2="true"
			/>
		</div>
		<!-- 搜索地方 -->
		<div class="ssPop" v-if="sspopShow" :style="{ bottom: operateBoxBottom + 38 + 'px', right: operateBoxRight + 'px' }">
			<div class="ssPop1">
				<input type="text" v-model="ssnr" @change="ssqy" />
				<div class="ssImgBox" @click="ssqy">
					<img src="@/assets/bjnj/ss.png" alt="" />
				</div>
			</div>
			<div class="ssPop2" v-if="sspopList">
				<div class="ssList" v-for="(item, index) in ssList" :key="index" @click="ssxz(item)">{{ item.areaName }}</div>
			</div>
		</div>
		<!-- 图例 -->
		<div class="jiangshuiTlList" :style="{ bottom: mapTlBottom + 'px', left: mapTlLeft + 'px' }" v-if="showQixiangtl">
			<div class="label-box">图例（毫米）</div>
			<div class="jiangshuiTlItem" v-for="item in jiangshuiTlList">
				<div :style="{ backgroundColor: item.bgColor }" class="color-box">
					<!-- <i class="el-icon-check icon-box"></i> -->
				</div>
				<div class="jiangshuiTlName">{{ item.text }}</div>
			</div>
		</div>

		<QixiangPlayAxis
			v-if="showQixiangtl"
			class="range-axis-box"
			:style="{ bottom: trackLineBoxBottom + 'px', left: trackLineBoxleft + 'px' }"
			@timeUpdate="handleTimeUpdate"
		></QixiangPlayAxis>

		<!-- 地图相关的操作按钮 -->
		<div class="map-operate-box" :style="{ bottom: operateBoxBottom + 'px', right: operateBoxRight + 'px' }">
			<div class="operate-box">
				<div
					class="operate-item-box"
					v-show="!operateItem.hide"
					v-for="(operateItem, index) in operateBtnList"
					:key="index"
					@click="operateItem.func()"
				>
					<img :src="operateItem.iconUrl" alt="" />
					<div class="operate-title">{{ operateItem.title }}</div>
					<div class="devider"></div>
				</div>
			</div>
		</div>
		<!-- 基础信息弹窗 -->
		<BasicInformation
			v-model="basicInfomationShow"
			:title="basicInfomationTitle"
			:list="basicInfomationList"
			:btnShow="basicInfomationBtnShow"
			:btns="basicInfomationBtns"
			@handle="clickBasicInformationBtn"
		/>

		<!-- 农机基本信息弹窗 -->
		<njInfo v-if="njInfoShow" :agmachInfo="currentAgmachInfo" @handleClick="handleNjInfo" @closeEmitai="njInfoShow = false" />

		<!-- 历史作业弹窗 -->
		<historyWorkPop
			ref="historyWorkPop"
			v-model="workPopShow"
			:agmachInfo="currentAgmachInfo"
			:agmachId="agmachId"
			@operate="eventInfoBtnClick15"
		/>

		<!-- 作业轨迹弹窗 -->
		<mapPop ref="mapPop" v-model="isMapShow" />
		<JxhfzDataReport
			ref="jxhfzDataReportRef"
			v-model="jxhfzDataReportShow"
			:year="currentYear"
			@closeEmitai="jxhfzDataReportShow = false"
			@updateJxhfzData="updateJxhfzData"
		></JxhfzDataReport>
	</div>
</template>

<script>
const DataVUrl = 'https://geo.datav.aliyun.com/areas_v3/bound/'

import BlockBox from '@/components/leader/common/jxhfz-block-box.vue'

import BlockBox8 from '@/components/leader/common/blockBox8.vue'
import BlockBoxChildNoHeader from '@/components/leader/common/blockBoxChildNoHeader.vue'
import BlockBoxChildNoHeader1 from '@/components/leader/common/blockBoxChildNoHeader1.vue'

import HeaderMain from '@/components/leader/common/headerMain.vue'
import effectRankBarSwiper from './components/effectRankBar-swiper.vue'
import particle from './components/particle.vue'
import leaderMiddle from '@/components/leader/common/leaderMiddle.vue'
import yjzyPop from '@/components/bjnj/yjzyPop.vue' //应急资源
import myRobot from '@/components/robot/myRobot.vue'
import NumScroll from '@/components/leader/num_scrolls'
import Area from '@/components/leader/leader_csts/area.vue'
import svgMap from '@/components/common/svgMap.vue'
import zlBg from '@/components/common/zlBg.vue'
import LivePlayer from '@liveqing/liveplayer'
import SwiperTable from '@/components/bjnj/SwiperTable.vue'
import LeaMap from '@/components/map/LeafletMapNj4.vue'
import { cscpCurrentUserDetails, getAgmachGuiJiInfo, queryByList, getZxnjDistributionCount } from '@/api/bjnj/zhdd.js'
import { BasicInformation } from './components/zhdd/dialog'
import AmachTypeSwiper from './components/zhdd/AmachTypeSwiper.vue'
import znzyPop from '@/components/bjnj/znzyPop.vue'
import njInfo from '@/components/bjnj/njInfo.vue'
import historyWorkPop from '@/components/bjnj/historyWorkPop.vue'
import mapPop from '@/components/bjnj/mapPop.vue'
import JxhfzDataReport from './components/jxhfz/jxhfzDataReport.vue'
import { aqjgGetToken } from '@/api/hs/hs_aqjg.api.js'
import {
	locHistory2,
	distributionhour,
	api_getJxhfzqkByYear,
	api_getAreaListByCondition,
	api_getTopDescripteDataList,
} from '@/api/njzl/hs.api.js'

//新优化
import DrawDataBar from '@/components/ssts/draw-data-bar.vue'
import TopHeaderDescripte from '@/components/leader/common/top-header-descripte.vue'
import 'swiper/css/swiper.css'
import dayjs from 'dayjs'

//------------新引入--------------
import FarmingSeasonSelect from '@/components/zyjd/farming-season-select.vue'
import ZyjdBlockBox from '@/components/zyjd/zyjd-block-box.vue'
import { color } from 'highcharts'
import QixiangPlayAxis from '@/components/zyjd/qixiang-play-axis.vue'
//-------------------------------

export default {
	name: 'Hszl',
	components: {
		BlockBox,
		BlockBox8,
		BlockBoxChildNoHeader,
		BlockBoxChildNoHeader1,
		HeaderMain,
		effectRankBarSwiper,
		particle,
		leaderMiddle,
		// gdMap,
		// cesiumMap,
		myRobot,
		Area,
		NumScroll,
		// LandUse,
		// FullFactorGridEvents,
		// StreetSurvey,
		// jjfztk,
		// hswhtk,
		// stjstk,
		// wgzltk,
		svgMap,
		zlBg,
		LivePlayer,
		SwiperTable,
		LeaMap,
		BasicInformation,
		znzyPop,
		njInfo,
		historyWorkPop,
		yjzyPop,
		mapPop,
		AmachTypeSwiper,
		//新优化
		DrawDataBar,
		TopHeaderDescripte,
		JxhfzDataReport,
		//----------新组件------------
		FarmingSeasonSelect,
		ZyjdBlockBox,
		QixiangPlayAxis,
		//---------------------------
	},
	data() {
		return {
			ssnr: '',
			ssList: [],
			ssboxShow: true,
			sspopShow: false,
			sspopList: false,
			startDate: '',
			endDate: '',

			njyylUnit: '万台',
			contentNumberUnitOne: '万台',
			contentNjNumberUnitOne: '万',

			dqbf: dayjs()
				.subtract(1, 'year')
				.format('YYYY'),
			dqyf: '',
			dqyfs: '',

			groupLevel: 2,
			mapShow: 1, //1:机械总动力；2:耕种收综合机械化率；3:机械作业面积

			time: 0,
			dwType: '',
			mkdx: false,
			agmachId: '',
			areaId: '100000',
			areaIdList: [],
			areaLevel_init: 0,
			//areaLevel 0：全国，1：省份，2：地市，3：区县
			areaLevel: 0,
			areaIdNew: 0,
			trajectoryMark: '',

			workPopShow: false,
			njInfoShow: false,
			isnjtlListShow: false,
			isMapShow: false,
			// 控制基本信息弹窗显示
			basicInfomationShow: false,
			// 基础信息标题
			basicInfomationTitle: '',
			// 基础信息内容
			basicInfomationList: [],
			// 控制基础信息按钮显示
			basicInfomationBtnShow: false,
			// 基础信息按钮
			basicInfomationBtns: [],
			activaIdx: 1, //当前地图中渲染的数据是什么数据
			currentAgmachInfo: {},
			lastAreaLevel: [],

			//新优化
			haveNjDataList: [],
			mechRateDataList: [],

			argmachByProvinceXData: [],
			argmachByProvinceYData: {},
			currentClickArgmachType: '',
			mapBtnTop: 20,

			// mapBtnLeft: 480,
			// operateBoxBottom: 10,
			// mapTlLeft: 480,
			// trackLineBoxleft: 610,
			// operateBoxRight: 400,
			//-----------显示左边时注释下面的内容-----------
			mapBtnLeft: 100,
			operateBoxBottom: 10,
			mapTlLeft: 100,
			trackLineBoxleft: 380,
			operateBoxRight: 630,
			//--------------------------------------------

			mapTlBottom: 60,
			trackLineBoxBottom: 60,

			currentTabValue: 'in',

			block1BackImg: require('@/assets/img/block-box/block-yongyouliang-bg.png'),
			block2BackImg: require('@/assets/img/block-box/block-jixiehualv-bg.png'),
			block3BackImg: require('@/assets/img/block-box/block-nongyefuwu-bg.png'),
			block4BackImg: require('@/assets/img/block-box/block-jxhfz-nongjishujv-bg.png'),
			currentYear: dayjs(new Date()).format('YYYY'),
			jxhfzDataReportShow: false,
			userAreaId: '100000',

			//---------新增变量------------
			dtDay: dayjs()
				.subtract(1, 'day')
				.format('YYYY-MM-DD'),
			curSeason: 'sanx',
			zyjdStatisticData: {
				totalFace: 0, //总面积
				collectedArea: 0, //已收面积
				harvestProgress: 0, //麦收进度
				machFace: 0, //机收面积
				machPercent: 0, //机收占比
				currentDayPutIntoShougeji: 0, //当日投入收割机
				currentOnlineShougeji: 0, //当日在线收割机
			},
			showLayerList: false,

			defaultExpandedKeys: [0, 1],
			defaultCheckedKeys: ['0-0'],
			defaultProps: {
				children: 'children',
				label: 'label',
			},
			layerData: [
				{
					id: 0,
					key: 'tq',
					label: '天气',
					children: [
						{ id: '0-0', key: 'tq', label: '预报数据' },
						// { id: '0-1', key: 'tq', label: '预警数据' },
					],
				},
				// {
				// 	id: 1,
				// 	key: 'onlineMach',
				// 	label: '在线农机',
				// 	children: [
				// 		{ id: '100000', key: 'onlineMach', label: '全部' },
				// 		{ id: '510101', key: 'onlineMach', label: '轮式拖拉机' },
				// 		{ id: '150105', key: 'onlineMach', label: '谷物联合收割机' },
				// 		{ id: '150106', key: 'onlineMach', label: '玉米收获机' },
				// 		{ id: '120401', key: 'onlineMach', label: '水稻插秧机' },
				// 		{ id: '150302', key: 'onlineMach', label: '花生收获机' },
				// 		{ id: '150303', key: 'onlineMach', label: '油菜籽收获机' },
				// 		{ id: '150403', key: 'onlineMach', label: '甘蔗联合收获机' },
				// 		{ id: '000000', key: 'onlineMach', label: '其他' },
				// 	],
				// },
				// {
				// 	id: 2,
				// 	key: 'workProgress',
				// 	label: '作业进度',
				// 	children: [],
				// },
			],
			qxTypes: ['0-0'],
			onlineMachTypes: [],
			workProgressType: false,
			showQixiangtl: false,
			selectedRange: '24h',
			layerCount: 0,
			timer001: null,
			currentUnit: 0,
			jiangshuiTlList: [
				{
					bgColor: '#A6F192',
					text: '0-10',
				},
				{
					bgColor: '#3EB83F',
					text: '10-25',
				},
				{
					bgColor: '#67B5F7',
					text: '25-50',
				},
				{
					bgColor: '#0100FC',
					text: '50-100',
				},
				{
					bgColor: '#F902FB',
					text: '100-250',
				},
				{
					bgColor: '#7D0844',
					text: '≥250',
				},
			],
			//-----------------------------
		}
	},
	created() {},
	beforeDestroy() {
		this.timer && clearInterval(this.timer)
		this.timer1 && clearInterval(this.timer1)
	},
	mounted() {
		this.cscpCurrentUserDetails()
	},
	watch: {},
	computed: {
		formatNumber() {
			return function(param) {
				if (this.isEmpty(param) || param.length < 4 || isNaN(param)) {
					return param
				}

				let small = ''
				let isSmall = false
				if (param.split('.').length === 2) {
					// 有小数
					isSmall = true
					small = param.split('.')[1]
					param = param.split('.')[0]
				}

				let result = ''

				while (param.length > 3) {
					result = ',' + param.slice(-3) + result
					param = param.slice(0, param.length - 3)
				}

				if (param) {
					result = param + result
				}

				if (isSmall) {
					result = result + '.' + small
				}
				return result
			}
		},
		initOptions1() {
			return {
				yAxis: {
					name: '台/天',
					nameTextStyle: {
						fontSize: 12,
						padding: [30, 0, 0, 0],
					},
					axisLabel: {
						textStyle: {
							fontSize: 12,
						},
					},
				},
				xAxis: {
					axisLabel: {
						show: true,
						textStyle: {
							fontSize: 12,
						},
					},
				},
			}
		},

		//新优化

		operateBtnList() {
			return [
				{
					iconClass: 'el-icon-back',
					title: '返回上级',
					iconUrl: require('@/assets/img/operate/return.png'),
					func: () => {
						//返回上一级函数
						this.$refs.leafletMap.backLayers()
					},
				},
				{
					iconClass: 'el-icon-full-screen',
					title: '页面全屏',
					iconUrl: require('@/assets/img/operate/all-screen.png'),

					func: () => {
						//页面全屏
						this.mkssBtn()
					},
				},
				{
					iconClass: 'el-icon-refresh-right',
					title: '刷新内容',
					iconUrl: require('@/assets/img/operate/refresh.png'),

					func: () => {
						//刷新内容
						this.sxTk()
					},
				},
				// {
				// 	iconClass: 'el-icon-menu',
				// 	title: '图例',
				// 	func: () => {
				// 		//图例
				// 		this.njtlTk()
				// 	},
				// },
				{
					iconClass: 'el-icon-view',
					title: '隐藏标记',
					iconUrl: require('@/assets/img/operate/show-operate.png'),
					func: () => {
						//隐藏标记
						this.mapShow = -1
						this.showQixiangtl = false
						this.$refs.layerTree.setCheckedKeys([])
						this.$refs.leafletMap.clearLayers()
						this.$refs.leafletMap.showQxyjLayer = false
						this.$refs.leafletMap.isChangeMapBg = true
						this.$refs.leafletMap.redrawGeoJSON()
						this.$refs.leafletMap.removeLayer(`precipitationType${this.layerCount}`)
						this.layerCount = 0
						this.selectedRange = '24h'
						this.currentUnit = 0
					},
				},
				{
					iconClass: 'el-icon-sort',
					type: 'switch',
					title: '切换地图',
					iconUrl: require('@/assets/img/operate/switch-map.png'),
					hide: this.showQixiangtl,
					func: () => {
						//切换地图
						this.$refs.leafletMap.switchMapType(this.areaLevel)
						if (this.mapShow != -1) {
						}
					},
				},
				{
					iconClass: 'el-icon-search',
					title: '点击搜索地方',
					hide: !this.ssboxShow,
					iconUrl: require('@/assets/img/operate/search-location.png'),

					func: () => {
						//点击搜索地方
						this.ssmkQhs()
					},
				},
			]
		},
	},
	methods: {
		//----------新增方法----------
		updateWaterLayerCount(value) {
			this.layerCount = value
		},
		handleTimeUpdate(currentUnit, selectedRange) {
			// 根据selectedRange和currentUnit更新您的数据或视图
			// 24小时模式下currentUnit是0-23表示小时
			// 天模式下currentUnit是0-2或0-6表示天数

			clearTimeout(this.timer001)

			this.timer001 = setTimeout(() => {
				this.currentUnit = currentUnit
				this.selectedRange = selectedRange
				this.getQxLayer()
			}, 500)
		},
		initLayerSelected() {
			this.getQxLayer()
			this.getOnlineMachData()
			this.getWorkProgressData()
		},
		dateSwitch(value) {
			this.dtDay = value
		},
		seasonSwitch(value) {
			this.curSeason = value
		},

		getZyjdStatisticData() {},

		handleClickLayer() {
			this.showLayerList = !this.showLayerList
		},

		handleCheck(ev) {
			console.log('ev--==', ev)
			console.log('getCheckedKeys()--==', this.$refs.layerTree.getCheckedKeys())
			console.log('getCheckedNodes()--==', this.$refs.layerTree.getCheckedNodes())
			const checkedNodes = this.$refs.layerTree.getCheckedNodes()
			if (checkedNodes.length == 0) {
			}
			this.qxTypes = []
			this.onlineMachTypes = []
			this.workProgressType = false
			checkedNodes.forEach((item) => {
				if (!item.children || item.children.length == 0) {
					if (item.key == 'tq') {
						this.qxTypes.push(item.id)
					} else if (item.key == 'onlineMach') {
						this.onlineMachTypes.push(item.id)
					} else if (item.key == 'workProgress') {
						this.workProgressType = true
					}
				}
			})

			if (this.qxTypes.indexOf('0-0') > -1) {
			} else {
				this.showQixiangtl = false
				this.$refs.leafletMap.showQxyjLayer = false
				this.$refs.leafletMap.isChangeMapBg = true
				this.$refs.leafletMap.removeLayer(`precipitationType${this.layerCount}`)
				this.$refs.leafletMap.redrawGeoJSON()
			}

			console.log('qxTypes--==', this.qxTypes)
			console.log('onlineMachTypes--==', this.onlineMachTypes)
			console.log('workProgressType--==', this.workProgressType)

			this.getQxLayer()
			this.getOnlineMachData()
			this.getWorkProgressData()
		},
		getQxLayer() {
			this.qxTypes.forEach((item) => {
				if (item == '0-0') {
					this.drawJiangyuliang()
				} else if (item == '0-1') {
					this.getYujingData()
				}
			})
		},
		getOnlineMachData() {
			//在此根据选择的农机类型获取在线农机数据
		},
		getWorkProgressData() {
			//在此获取作业进度数据并展示
		},
		async drawJiangyuliang() {
			this.showQixiangtl = true
			this.$refs.leafletMap.showQxyjLayer = true
			this.$refs.leafletMap.isChangeMapBg = false
			await this.$refs.leafletMap.redrawGeoJSON()
			console.log('执行了！！！！')

			// console.log('selectedRange--==', this.selectedRange)
			// console.log('currentUnit--==', this.currentUnit)
			this.layerCount += 1
			this.$refs.leafletMap.loadWaterLayer('precipitationType', this.layerCount, this.selectedRange, this.currentUnit)
		},
		getYujingData() {},

		//---------------------------
		updateJxhfzData() {
			this.jxhfzDataReportShow = false
			this.sxTk()
		},

		//新优化
		dateSwitch() {
			this.sxTk()
		},

		toFixed(number, unit = 10000) {
			if (number === undefined || number === null || isNaN(number)) {
				return 0
			}

			// 将数字除以 10000
			let result = number / unit

			// 如果结果是整数，则返回整数
			if (Number.isInteger(result)) {
				return Number(result)
			} else {
				// 否则，保留两位小数
				return Number(result.toFixed(2))
			}
		},

		async getArgmachByProvinceData(currentClickArgmachType, columnCode) {
			if (!currentClickArgmachType) {
				this.argmachByProvinceXData = []
				this.argmachByProvinceYData = {}
				this.contentNjNumberUnitOne = ''
			} else {
				this.currentClickArgmachType = currentClickArgmachType
				const params = {
					type: 2,
					year: this.currentYear,
					areaId: this.areaId,
					code: currentClickArgmachType,
				}
				if (this.areaLevel == 3) {
					this.argmachByProvinceXData = []
					this.argmachByProvinceYData = {}
				}
				const argmachByProvinceRes = await api_getAreaListByCondition(params)
				console.log('argmachByProvinceRes--==', argmachByProvinceRes)
				const data_ = argmachByProvinceRes.data.sort((a, b) => b.value - a.value)
				this.contentNjNumberUnitOne = this.areaLevel == 0 || this.areaLevel == 1 ? '万台' : '台'
				if (data_.length > 0) {
					this.argmachByProvinceXData = []
					this.argmachByProvinceYData = {}
					let argmachByProvinceYData_ = []

					data_.forEach((item) => {
						this.argmachByProvinceXData.push(item.areaId)
						argmachByProvinceYData_.push(
							Number(
								this.areaLevel == 0 || this.areaLevel == 1
									? Number(Number(item.value || 0) / 10000).toFixed(4)
									: Number(item.value || 0).toFixed(0),
							),
						)
					})
					this.argmachByProvinceYData['农机数据'] = argmachByProvinceYData_
				} else {
					this.argmachByProvinceXData = []
					this.argmachByProvinceYData = {}
					let argmachByProvinceYData_ = []
				}
			}
		},
		// 搜索地方
		ssqy() {
			this.ssList = []
			if (this.ssnr) {
				this.sspopList = true
				this.queryByList(this.ssnr)
			} else {
				this.sspopList = false
			}
		},
		async queryByList() {
			this.ssList = []
			let res = await queryByList({
				areaName: this.ssnr,
			})
			if (res.code == 0) {
				this.sspopList = true
				this.ssList = []
				this.ssList = res.data
			} else if (res.code == -1) {
				this.sspopList = false
				this.ssList = []
				this.$message({
					message: res.msg,
					type: 'warning',
				})
			}
		},
		ssxz(item) {
			this.areaLevel = item.level
			this.areaId = item.areaId
			this.$store.commit('invokerightShow5', item)
			if (this.$refs.leafletMap.lastAreaCode.indexOf(item.areaId) == -1) {
				this.$refs.leafletMap.lastAreaCode.push(item.areaId)
			}
			if (item.level == '3') {
				//区县级别
				setTimeout(() => {
					this.areaLevel = 3
					this.$refs.leafletMap.isChangeMapBg = true
					this.$refs.leafletMap.currentMapLevel = 'district'
					this.$refs.leafletMap.moduleType = 'zyjd'
					this.$refs.leafletMap.loadGeoJsonLayerFromUrl(
						`https://geo.datav.aliyun.com/areas_v3/bound/${item.areaId}.json`,
						'geojson',
						false,
					)
				}, 300)
			} else {
				setTimeout(() => {
					this.$refs.leafletMap.isChangeMapBg = true
					this.$refs.leafletMap.moduleType = 'zyjd'
					this.$refs.leafletMap.loadGeoJsonLayerFromUrl(
						`https://geo.datav.aliyun.com/areas_v3/bound/${item.areaId}_full.json`,
						'geojson',
						item.level == 0 ? true : false,
					)
				}, 300)
			}
			this.removeAllPoi()
			this.activaIdx = -1
			this.mapShow = -1
			this.getData()
			this.ssboxShow = false
			this.sspopShow = false
			this.sspopList = false
			this.ssnr = ''
			this.ssList = []
		},

		async mapActive(index, directDraw = false) {
			this.removeAllPoi()
			await this.$refs.leafletMap.redrawGeoJSON()
			if (directDraw) {
				this.mapShow = index
				this.activaIdx = index
			} else {
				if (this.mapShow == index) {
					this.mapShow = -1
					this.activaIdx = -1
					this.$store.commit('updateMapLoaded', true)
					this.$refs.leafletMap.showHeat = false
					this.$refs.leafletMap.redrawGeoJSON()
					return
				}
				// this.$refs.leafletMap.showHeat = true
				this.mapShow = index
				this.activaIdx = index
			}
		},

		handleClickEditData() {
			console.log('打开数据编辑弹窗')
			this.jxhfzDataReportShow = true
		},

		async workHeat() {
			let res = await distributionhour({
				// time: this.dayjs()
				// 	.subtract(1, 'days')
				// 	.format('YYYY-MM-DD hh:mm:ss'),
				time: '2025-04-25 12:00:00',
				level: 2,
				areaId: this.areaId == '100000' ? '' : this.areaId,
				agmachTypeCode: '',
			})
			let data = res.data.filter((item) => {
				return Number(item.agmachNum) > 0
			})
			this.$refs.leafletMap.loadHeatLayer('heat', data)
		},

		//打开/关闭图例
		njtlTk() {
			this.isnjtlListShow = !this.isnjtlListShow
		},
		//刷新内容
		sxTk() {
			console.log('currentYear--==', this.currentYear)
			this.mapActive(this.mapShow, true)
			this.getData()
			//获取底部农机数据
			this.getArgmachByProvinceData(this.currentClickArgmachType, 'haveNj')
		},
		//打开/关闭搜索地方的弹窗
		ssmkQhs() {
			this.sspopShow = !this.sspopShow
			this.sspopList = false
			this.ssList = []
			this.ssnr = ''
		},
		qctc() {
			this.activaIdx = -1
			this.mapShow = -1
		},

		//点击作业轨迹打开轨迹弹窗 待定
		eventInfoBtnClick15(i, form) {
			this.getAgmachGuiJiInfo(
				{
					agmachId: form.agmachId,
					startTime: form.workStartTime,
					endTime: form.workEndTime,
				},
				form,
			)
		},
		//页面全屏
		async mkssBtn() {
			this.mkdx = !this.mkdx
			await this.$store.commit('invokerightShow4', this.mkdx)
			if (!this.mkdx) {
				// this.mapBtnLeft = 480
				// this.operateBoxBottom = 10
				// this.mapTlLeft = 480
				// this.trackLineBoxleft = 610
				// this.operateBoxRight = 400
				//-----------显示左边时注释下面的内容-----------
				this.mapBtnLeft = 100
				this.operateBoxBottom = 10
				this.mapTlLeft = 100
				this.trackLineBoxleft = 380
				this.operateBoxRight = 630
				//--------------------------------------------
			} else {
				this.mapBtnLeft = 100
				this.operateBoxBottom = 10
				this.mapTlLeft = 100
				this.trackLineBoxleft = 380
				this.operateBoxRight = 630
			}

			// this.$store.commit('invokerightShow', !this.mkdx)
		},
		//调用作业轨迹的接口
		async getAgmachGuiJiInfo(data, infoData) {
			let res = await getAgmachGuiJiInfo(data)
			if (res?.code == '0') {
				// this.workPopShow = false
				let trackData = []
				trackData = [].concat(res.data.map((it) => [it.lon, it.lat]))
				// this.$refs.leafletMap.loadTrackLayer('trcak', trackData)
				this.isMapShow = true
				this.$nextTick(() => {
					this.$refs.mapPop.track(trackData)
					this.$refs.mapPop.getNjInfo(infoData)
				})
			}
		},

		async cscpCurrentUserDetails(data) {
			let res = await cscpCurrentUserDetails(data)
			if (res?.code == '0') {
				this.roleNames = res.data.roleNames
				if (res.data.areaId) {
					const areaId_ = res.data.areaId == '0' ? 100000 : res.data.areaId
					this.areaId = areaId_
					this.userAreaId = areaId_
					this.areaIdList[0] = areaId_
					this.areaIdNew = res.data.areaIdNew
					this.areaLevel_init = res.data.areaLevel
					this.areaLevel = res.data.areaLevel
					console.log('this.$refs.leafletMap.lastAreaCode--==')
					this.$refs.leafletMap.lastAreaCode.push(Number(areaId_))
					this.lastAreaLevel.push(res.data.areaLevel)
					if (areaId_ != res.data.areaIdNew) {
						//区县级别
						setTimeout(() => {
							this.areaLevel = 3
							this.$refs.leafletMap.isChangeMapBg = true
							this.$refs.leafletMap.currentMapLevel = 'district'
							this.$refs.leafletMap.moduleType = 'zyjd'
							this.$refs.leafletMap.loadGeoJsonLayerFromUrl(
								`https://geo.datav.aliyun.com/areas_v3/bound/${areaId_}.json`,
								'geojson',
								false,
							)
						}, 300)
					} else {
						setTimeout(() => {
							this.$refs.leafletMap.moduleType = 'zyjd'
							this.$refs.leafletMap.loadGeoJsonLayerFromUrl(
								`https://geo.datav.aliyun.com/areas_v3/bound/${areaId_}_full.json`,
								'geojson',
								res.data.areaLevel == 0 ? true : false,
							)
						}, 300)
					}
				}

				setTimeout(() => {
					this.initLayerSelected()
					this.getData()
				}, 500)
			}
		},
		async getData() {
			let time = this.dqbf + '-' + this.dqyfs
			if (this.groupLevel == 2) {
				this.startDate = dayjs(this.dqbf + '-')
					.startOf('year')
					.format('YYYY-MM-DD')
				this.endDate = dayjs(this.dqbf + '-')
					.endOf('year')
					.format('YYYY-MM-DD')
			} else if (this.groupLevel == 3) {
				this.startDate = dayjs(time)
					.startOf('month')
					.format('YYYY-MM-DD')
				this.endDate = dayjs(time)
					.endOf('month')
					.format('YYYY-MM-DD')
			}

			//新优化
			//获取作业进度
			this.getZyjdStatisticData()

			//获取农机拥有量
			this.getNjData()
			//获取机械化率数据
			this.getMechRateData()
		},
		async getNjData() {
			const params = {
				areaId: this.areaId,
				type: 1,
				year: this.currentYear,
			}

			this.contentNumberUnitOne = this.areaLevel == 0 || this.areaLevel == 1 ? '万台' : '台'
			this.njyylUnit = this.areaLevel == 0 || this.areaLevel == 1 ? '万台' : '台'

			const njDataRes = await api_getJxhfzqkByYear(params)
			const data_ = njDataRes.data
			this.haveNjDataList = []
			if (njDataRes.code == 0) {
				if (data_.length > 0) {
					data_.forEach((item) => {
						let icon = ''
						if (item.indexName.search('拖拉机') != -1) {
							icon = require('@/assets/img/block-box/lstlj-icon.png')
						} else if (item.indexName.search('水稻插秧机') != -1) {
							icon = require('@/assets/img/block-box/cyj-icon.png')
						} else if (item.indexName.search('农用水泵') != -1) {
							icon = require('@/assets/img/block-box/nysb-icon.png')
						} else if (item.indexName.search('谷物联合收割机') != -1) {
							icon = require('@/assets/img/block-box/gwlhsgj-icon.png')
						} else if (item.indexName.search('玉米收获机') != -1) {
							icon = require('@/assets/img/block-box/ymshj-icon.png')
						} else if (item.indexName.search('大豆收获机') != -1) {
							icon = require('@/assets/img/block-box/ddshj-icon.png')
						} else if (item.indexName.search('油菜籽收获机') != -1) {
							icon = require('@/assets/img/block-box/yczshj-icon.png')
						} else if (item.indexName.search('马铃薯收获机') != -1) {
							icon = require('@/assets/img/block-box/mlsshj-icon.png')
						} else if (item.indexName.search('花生收获机') != -1) {
							icon = require('@/assets/img/block-box/hsshj-icon.png')
						} else if (item.indexName.search('甜菜收获机') != -1) {
							icon = require('@/assets/img/block-box/tcshj-icon.png')
						} else if (item.indexName.search('甘蔗收获机') != -1) {
							icon = require('@/assets/img/block-box/gzshj-icon.png')
						} else if (item.indexName.search('棉花收获机') != -1) {
							icon = require('@/assets/img/block-box/mhshj-icon.png')
						} else if (item.indexName.search('温室') != -1) {
							icon = require('@/assets/img/block-box/ws-icon.png')
						} else if (item.indexName.search('农产品初加工机械') != -1) {
							icon = require('@/assets/img/block-box/ncpcjgjx-icon.png')
						} else if (item.indexName.search('畜牧机械') != -1) {
							icon = require('@/assets/img/block-box/xmjx-icon.png')
						} else if (item.indexName.search('水产机械') != -1) {
							icon = require('@/assets/img/block-box/scjx-icon.png')
						} else if (item.indexName.search('农用航空器') != -1) {
							icon = require('@/assets/img/block-box/nyhkq-icon.png')
						} else {
							icon = require('@/assets/img/block-box/other-argmach-icon.png')
						}

						this.haveNjDataList.push({
							title: this.getTitle(item),
							// number: `${item.value} ${item.unit}`,
							number: this.getNumber(item),
							iconUrl: icon,
							argmachType: item.code,
							func: (argmachType) => {
								//获取该农机类型在各省的农机数据
								this.getArgmachByProvinceData(argmachType, 'haveNj')
							},
						})
					})
					if (this.haveNjDataList.length > 0) {
						this.currentClickArgmachType = this.haveNjDataList[0].argmachType
						//获取底部农机数据
						this.getArgmachByProvinceData(this.currentClickArgmachType, 'haveNj')
					}
				} else {
					this.argmachByProvinceXData = []
					this.argmachByProvinceYData = {}
					this.contentNjNumberUnitOne = ''
				}
			} else {
				this.argmachByProvinceXData = []
				this.argmachByProvinceYData = {}
				this.contentNjNumberUnitOne = ''
			}
		},

		getTitle(item) {
			if (item.code == '14') {
				return this.areaLevel == 0 || this.areaLevel == 1 ? `${item.indexName}（万平方米）` : `${item.indexName}（平方米）`
			} else if (item.code == '18') {
				return this.areaLevel == 0 || this.areaLevel == 1 ? `${item.indexName}（万架）` : `${item.indexName}（架）`
			} else {
				return item.indexName
			}
		},

		getNumber(item) {
			// if (item.code == '32') {
			// 	return Number(Number(item.value || 0).toFixed(6))
			// } else if (item.code == '14') {
			// 	return Number(
			// 		this.areaLevel == 0 || this.areaLevel == 1
			// 			? Number(Number(item.value || 0) / 10000).toFixed(6)
			// 			: Number(item.value || 0).toFixed(2),
			// 	)
			// } else {
			// 	return Number(
			// 		this.areaLevel == 0 || this.areaLevel == 1
			// 			? Number(Number(item.value || 0) / 10000).toFixed(4)
			// 			: Number(item.value || 0).toFixed(0),
			// 	)
			// }
			if (item.code == '32') {
				return Number(Number(item.value || 0).toFixed(1))
			} else if (item.code == '14') {
				return Number(
					this.areaLevel == 0 || this.areaLevel == 1
						? Number(Number(item.value || 0) / 10000).toFixed(1)
						: Number(item.value || 0).toFixed(1),
				)
			} else {
				return Number(
					this.areaLevel == 0 || this.areaLevel == 1
						? Number(Number(item.value || 0) / 10000).toFixed(1)
						: Number(item.value || 0).toFixed(0),
				)
			}
			// return Number(Number(item.value || 0).toFixed(1))
		},

		async getMechRateData() {
			const params = {
				areaId: this.areaId,
				type: 2,
				year: this.currentYear,
			}

			const jxhlRes = await api_getJxhfzqkByYear(params)
			console.log('jxhlRes--==', jxhlRes)
			const data_ = jxhlRes.data
			this.mechRateDataList = []
			let icon = require('@/assets/img/block-box/jxhl-item-icon.png')
			if (data_.length > 0) {
				data_.forEach((item) => {
					this.mechRateDataList.push({
						title: item.indexName,
						number: item.value,
						iconUrl: icon,
						argmachType: item.code,
						func: (argmachType) => {},
					})
				})
			}
		},

		//待定
		handleNjInfo() {
			if (this.dwType == 'njfbType2') {
				if (this.trajectoryMark == 1) {
					this.njInfoShow = false
					this.workPopShow = true
					this.$refs.historyWorkPop.init()
				} else {
					this.$message('这台农机无轨迹')
				}
			} else {
				this.njInfoShow = false
				this.workPopShow = true
				this.$refs.historyWorkPop.init()
			}
		},
		// 标记点基础信息弹窗内按钮点击事件 待定
		clickBasicInformationBtn(i, name) {
			if (name == '区域农机社会化服务中心详情') {
				this.basicInfomationShow = false
			}
			if (name == '农机应急作业服务队详情') {
				this.basicInfomationShow = false
			}
			if (name == '区域农业应急救灾中心详情') {
				this.basicInfomationShow = false
			}
		},

		//待定
		async aqjgGetTokenFn() {
			const res = await aqjgGetToken()
			if (res?.data) {
				localStorage.setItem('aqjgToken', res.data.access_token)
			}
		},

		//点击某个行政区划 所获取的数据
		gridClick(properties) {
			// mapShow:[1]:机械总动力；[2]耕种收综合机械化率；[3]机械作业面积
			this.$store.commit('invokerightShow2', properties)
			if (properties.level == 'province') {
				this.$refs.leafletMap.currentMapLevel = 'province'
				this.areaLevel = 1
				this.lastAreaLevel.push(this.areaLevel)
			} else if (properties.level == 'city') {
				this.$refs.leafletMap.currentMapLevel = 'city'
				this.areaLevel = 2
				this.lastAreaLevel.push(this.areaLevel)
			} else if (properties.level == 'district' && this.areaLevel != 3) {
				this.$refs.leafletMap.currentMapLevel = 'district'
				this.areaLevel = 3
				this.lastAreaLevel.push(this.areaLevel)
			}

			if (this.areaLevel != 2 && this.areaLevel != 3) {
				this.contentNumberUnitOne = '万台'
				this.contentNjNumberUnitOne = '万台'
			} else {
				this.contentNumberUnitOne = '台'
				this.contentNjNumberUnitOne = '台'
			}
			this.ssboxShow = false
			this.sspopShow = false
			this.sspopList = false
			this.ssnr = ''
			this.ssList = []
			this.areaId = properties.adcode

			this.removeAllPoi()
			this.getData()

			if (this.mapShow == 1 || this.mapShow == 2 || this.mapShow == 3) {
			} else {
				this.$store.commit('updateMapLoaded', true)
			}
		},
		//返回上一层地图后所获取的数据
		operate(areaId, areaId2) {
			this.ssboxShow = true
			this.sspopShow = false
			this.sspopList = false
			this.ssnr = ''
			this.ssList = []
			this.areaId = areaId
			this.$store.commit('invokerightShow3', areaId)
			if (this.areaId) {
				if (this.areaId == 100000) {
					this.areaLevel = 0
				} else {
					this.areaLevel = this.areaLevel_init + this.$refs.leafletMap.lastAreaCode.indexOf(areaId)
				}
			} else {
				this.areaLevel = 0
			}

			if (this.areaLevel != 2 && this.areaLevel != 3) {
				this.contentNumberUnitOne = '万台'
				this.contentNjNumberUnitOne = '万台'
			} else {
				this.contentNumberUnitOne = '台'
				this.contentNjNumberUnitOne = '台'
			}
			this.removeAllPoi()
			this.getData()
			//获取底部农机数据
			this.getArgmachByProvinceData(this.currentClickArgmachType, 'haveNj')
			this.removeAllPoi()
			if (this.mapShow == 1 || this.mapShow == 2 || this.mapShow == 3) {
			} else {
				this.$store.commit('updateMapLoaded', true)
			}
			setTimeout(() => {
				if (this.areaLevel == 0) {
					this.$refs.leafletMap.map.setView(this.$refs.leafletMap.mapOptions.center, this.$refs.leafletMap.mapOptions.zoom)
				}
			}, 1000)
		},
		//农机图标点击
		poiClick(layerId, it) {
			if (it.trajectoryMark) {
				this.trajectoryMark = it.trajectoryMark
			}
			this.ssboxShow = false
			this.sspopShow = false
			this.sspopList = false
			this.ssnr = ''
			this.ssList = []
			// this.agmachId = it[0]
			this.agmachId = it.props.info.agmachId
			this.dwType = layerId
			if (layerId == 'njfbType') {
				this.locHistory2(it.props.info.agmachId)
			} else if (layerId == 'zxnjType') {
				this.locHistory2(it.props.info.agmachId)
			} else if (layerId == 'njfbType2') {
				this.trajectoryMark = it[2]
				if (it[1] == 1) {
					this.locHistory2(it[0])
				} else {
					this.$message('这台农机无基本信息')
				}
			}
		},

		//获取农机历史数据
		async locHistory2(data) {
			let res = await locHistory2({
				agmachId: data,
				// page_no: 1,
				// page_size: 10
				pageNo: 1,
				pageSize: 10,
			})
			this.njInfoShow = true
			// this.currentAgmachInfo = res.data[0].rows[0]
			if (res.data) {
				this.currentAgmachInfo = res.data[0]
			} else {
				this.currentAgmachInfo = {}
			}
		},
		//移除地图中的所有覆盖物
		removeAllPoi() {
			this.$refs.leafletMap.removeLayer('njfbType')
			this.$refs.leafletMap.removeLayer('zxnjType')
			this.$refs.leafletMap.removeLayer('heat')
			this.$refs.leafletMap.removeLayer('hgzxType')
			this.$refs.leafletMap.removeLayer('yjwzType')
			this.$refs.leafletMap.removeLayer('yjjzType')
			this.$refs.leafletMap.removeLayer('fwdType')
			this.$refs.leafletMap.removeLayer('numberMarker')

			this.$refs.leafletMap.clearCluster()
			if (this.$refs.leafletMap.polygon) {
				this.$refs.leafletMap.polygon.remove()
			}
		},
	},
}
</script>

<style lang="less" scoped>
@font-face {
	font-family: QuartzRegular;
	src: url(~@/assets/leader/font/QuartzRegular.ttf);
}

@font-face {
	/*给字体命名*/
	font-family: 'YouSheBiaoTiHei';
	/*引入字体文件*/
	src: url(~@/assets/font/youshebiaotihei.ttf);
	font-weight: normal;
	font-style: normal;
}

@font-face {
	/*给字体命名*/
	font-family: 'AlibabaPuHuiTi';
	/*引入字体文件*/
	src: url(~@/assets/font/AlibabaPuHuiTi-2-85-Bold.ttf);
	font-weight: normal;
	font-style: normal;
}

@keyframes rotateS {
	0% {
		transform: rotate(0);
	}

	100% {
		transform: rotate(360deg);
	}
}

@keyframes rotateN {
	0% {
		transform: rotate(360deg);
	}

	100% {
		transform: rotate(0);
	}
}

@keyframes rotateY {
	0% {
		transform: rotate(0);
	}

	100% {
		transform: rotateY(360deg);
	}
}

.leader_sjts {
	width: 1920px;
	height: 1080px;
	position: absolute;
	top: 0;
	display: flex;
	justify-content: space-between;

	.leader_city_box {
		width: 450px;

		.leader_zt_contain {
			height: 100%;
		}
	}

	.box {
		.cont {
			width: 100%;
			height: 100%;
			position: relative;
		}

		.wrapper1 {
			width: 100%;
			height: 100%;

			.introduce_video {
				width: 459px;
				height: 192px;

				::v-deep .video-wrapper {
					padding-bottom: 41.25% !important;
				}
			}

			.introduce {
				margin-top: 11px;
				width: 100%;
				height: 120px;
				padding: 18px 26px;
				background: url(~@/assets/hszl/bg1.png) no-repeat;
				background-size: 100% 100%;
				font-size: 16px;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #4fddff;
				line-height: 22px;
				letter-spacing: 2px;
				text-align: left;
			}
		}

		.wrapper4 {
			width: 100%;
			height: 100%;
			padding: 30px 0 30px 8px;

			ul {
				width: 100%;
				height: 100%;
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;
				align-content: space-between;

				li {
					display: flex;
					height: 49px;
					gap: 10px;

					.icon {
						width: 46px;
						height: 49px;
					}

					.info {
						margin-top: -6px;

						.label {
							font-size: 14px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #ffffff;
							line-height: 20px;
							text-align: left;
							padding-left: 5px;
						}

						.line {
							width: 87px;
							height: 6px;
							background: url('~@/assets/hszl/line1.png') no-repeat;
						}

						.count {
							text-align: left;
							margin-top: 4px;
							width: 87px;
							height: 22px;
							line-height: 22px;
							background: linear-gradient(96deg, #1d4975 0%, rgba(19, 56, 87, 0.24) 100%);
							font-size: 14px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #a8b1b9;
							padding-left: 5px;

							span {
								font-size: 18px;
								font-family: PingFangSC, PingFang SC;
								font-weight: normal;
								color: #ffffff;
								line-height: 22px;
								margin-right: 3px;
							}
						}
					}
				}
			}
		}

		.wrapper5 {
			width: 100%;
			height: 100%;
			padding-top: 31px;

			.info {
				width: 100%;
				height: 71px;
				padding-left: 8px;
				display: flex;
				justify-content: space-between;

				.total {
					width: 130px;
					height: 100%;
					background: url('~@/assets/hszl/bg4.png') no-repeat;
					display: grid;
					place-items: center;

					.cont {
						padding-top: 13px;

						.label {
							font-size: 14px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #ffffff;
							line-height: 20px;
						}

						.value {
							font-size: 18px;
							font-family: PingFangSC, PingFang SC;
							font-weight: normal;
							color: #ffffff;
							line-height: 22px;
							background: linear-gradient(180deg, #ffffff 0%, #00fffa 67%, #04a1ff 100%);
							background-clip: text;
							-webkit-background-clip: text;
							-webkit-text-fill-color: transparent;

							span {
								font-size: 12px;
							}
						}
					}
				}

				.counts {
					width: 312px;
					height: 100%;
					background: url('~@/assets/hszl/bg5.png') no-repeat;
					display: flex;
					padding: 0 9px;

					.men {
						text-align: left;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						margin-right: 4px;

						.count {
							height: 21px;
							font-size: 16px;
							font-family: PingFangSC, PingFang SC;
							font-weight: normal;
							line-height: 19px;
							background: linear-gradient(180deg, #ffffff 0%, #00ceff 67%, #04a1ff 100%);
							background-clip: text;
							-webkit-background-clip: text;
							-webkit-text-fill-color: transparent;

							span {
								font-size: 14px;
							}
						}

						.chart {
							display: flex;
							gap: 4px;

							span {
								width: 10px;
								height: 25px;
								background-repeat: no-repeat;
							}
						}
					}

					.women {
						text-align: right;
						display: flex;
						flex-direction: column;
						justify-content: space-between;

						.count {
							height: 21px;
							font-size: 16px;
							font-family: PingFangSC, PingFang SC;
							font-weight: normal;
							line-height: 19px;
							background: linear-gradient(180deg, #ffffff 0%, #f076ff 66%, #ff04ff 100%);
							background-clip: text;
							-webkit-background-clip: text;
							-webkit-text-fill-color: transparent;

							span {
								font-size: 14px;
							}
						}

						.chart {
							display: flex;
							gap: 4px;

							span {
								width: 12px;
								height: 25px;
								background-repeat: no-repeat;
							}
						}
					}
				}
			}

			.chart_wrap {
				position: relative;
				width: 100%;
				height: 198px;
				top: 41px;

				.sign {
					position: absolute;
					left: 105px;
					top: 25px;

					.woman {
						position: absolute;
						width: 27px;
						height: 57px;
						left: 0;
						top: 0;
						background: url('~@/assets/hszl/woman.png') no-repeat;
					}

					.man {
						position: absolute;
						width: 23px;
						height: 57px;
						left: 95px;
						top: 47px;
						background: url('~@/assets/hszl/man.png') no-repeat;
					}

					.man_count {
						position: absolute;
						left: 10px;
						top: 76px;
						width: 109px;
						height: 31px;
						font-size: 20px;
						font-family: PingFangSC, PingFang SC;
						font-weight: normal;
						color: #ffffff;
						line-height: 24px;
					}

					.woman_count {
						position: absolute;
						left: 10px;
						top: 36px;
						width: 109px;
						height: 31px;
						font-size: 20px;
						font-family: PingFangSC, PingFang SC;
						font-weight: normal;
						color: #ffffff;
						line-height: 24px;
					}
				}
			}
		}
	}

	.box1 {
		.cout {
			width: 100%;
			height: 100%;
		}
		.cont1 {
			width: 100%;
			height: 60px;
			display: flex;
			justify-content: space-around;

			.contList {
				.contNum {
					width: 124px;
					height: 24px;
					margin-top: 10px;

					span {
						display: inline-block;
						width: 100%;
						height: 24px;
						font-family: PingFangSC, PingFang SC;
						font-weight: bold;
						font-size: 20px;
						color: #ffffff;
						line-height: 24px;
						text-shadow: 0px 0px 4px rgba(0, 0, 0, 0);
						text-align: center;
						font-style: italic;
						background: linear-gradient(180deg, #ffffff 0%, #00fffa 67%, #04a1ff 100%);
						-webkit-background-clip: text;
						-webkit-text-fill-color: transparent;
					}
				}

				&:nth-of-type(2) {
					.contNum {
						width: 124px;
						height: 24px;

						span {
							display: inline-block;
							width: 100%;
							height: 24px;
							font-family: PingFangSC, PingFang SC;
							font-weight: bold;
							font-size: 20px;
							color: #ffffff;
							line-height: 24px;
							text-shadow: 0px 0px 4px rgba(0, 0, 0, 0);
							text-align: center;
							font-style: italic;
							background: linear-gradient(180deg, #ffffff 0%, #ffd500 67%, #ffb904 100%);
							-webkit-background-clip: text;
							-webkit-text-fill-color: transparent;
						}
					}
				}

				&:nth-of-type(3) {
					.contNum {
						width: 124px;
						height: 24px;

						span {
							display: inline-block;
							width: 100%;
							height: 24px;
							font-family: PingFangSC, PingFang SC;
							font-weight: bold;
							font-size: 20px;
							color: #ffffff;
							line-height: 24px;
							text-shadow: 0px 0px 4px rgba(0, 0, 0, 0);
							text-align: center;
							font-style: italic;
							background: linear-gradient(180deg, #ffffff 0%, #86ff00 67%, #1cff04 100%);
							-webkit-background-clip: text;
							-webkit-text-fill-color: transparent;
						}
					}
				}

				img {
					display: block;
					width: 124px;
					height: 4px;
					margin-top: 4px;
				}

				.contName {
					width: 124px;
					height: 20px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 14px;
					color: #ffffff;
					line-height: 20px;
					text-align: center;
					font-style: normal;
				}
			}
		}

		.cont2 {
			width: 100%;
			height: 170px;
		}
	}

	.box2 {
		margin-top: 22px;
		.contLeft {
			position: absolute;
			left: 20px;
			top: 10px;
			text-align: left;
			z-index: 99;
		}

		.contRight {
			position: absolute;
			right: 20px;
			top: 10px;
			text-align: right;
			z-index: 99;
		}

		.contList {
			margin-top: 18px;
			cursor: pointer;
			.contNum {
				font-family: PingFangSC, PingFang SC;
				font-weight: bold;
				font-size: 24px;
				color: #ffffff;
				line-height: 28px;
				letter-spacing: 2px;
				font-style: normal;

				span {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 14px;
					color: #cbe5ff;
					line-height: 20px;
					letter-spacing: 1px;
					text-align: right;
					font-style: normal;
					margin-left: 4px;
				}
			}

			.contName {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #cbe5ff;
				line-height: 20px;
				letter-spacing: 1px;
				font-style: normal;
			}

			&:nth-of-type(1) {
				margin-top: 7px;
			}
		}

		.contImg {
			width: 405px;
			height: 197px;
			position: absolute;
			top: 52px;
			left: 20px;
		}

		.contImg2 {
			width: 59px;
			height: 61px;
			position: absolute;
			top: 93px;
			left: 194px;
		}
	}

	.box3 {
		.cont {
			padding: 12px 22px 0;

			video {
				width: 100%;
				height: 100%;
				object-fit: cover;

				&:not(:root):fullscreen {
					object-fit: contain;
				}
			}
		}
	}

	.box4 {
		.cont {
			padding: 15px 0;
			display: flex;
			flex-wrap: wrap;
			justify-content: space-evenly;

			li {
				position: relative;
				width: 200px;
				height: 66px;
				background: url(~@/assets/csts/bg2.png) no-repeat;
				display: flex;
				flex-direction: column;
				justify-content: center;
				text-align: left;
				padding-left: 92px;

				.icon {
					position: absolute;
					width: 26px;
					height: 26px;
					top: 10px;
					left: 31px;
					animation: move infinite 3s ease-in-out;

					@keyframes move {
						0% {
							transform: translateY(0px) rotateY(0);
						}

						50% {
							transform: translateY(-10px) rotateY(180deg);
						}

						100% {
							transform: translateY(0px) rotateY(360deg);
						}
					}
				}

				&:nth-child(2) {
					span {
						animation-delay: 0.3s;
					}
				}

				&:nth-child(3) {
					span {
						animation-delay: 0.6s;
					}
				}

				&:nth-child(4) {
					span {
						animation-delay: 0.9s;
					}
				}

				&:nth-child(5) {
					span {
						animation-delay: 1.2s;
					}
				}

				&:nth-child(6) {
					span {
						animation-delay: 1.5s;
					}
				}

				.tit {
					font-size: 14px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #ffffff;
					line-height: 20px;
				}

				.num {
					font-size: 18px;
					font-family: PingFangSC, PingFang SC;
					font-weight: normal;
					color: #ffffff;
					line-height: 22px;
				}
			}
		}
	}

	.box5 {
		.cont {
			display: flex;
			flex-direction: column;

			.bar_chart {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-evenly;

				li {
					position: relative;
					display: flex;
					align-items: center;
					padding: 0 30px 0 23px;

					.icon {
						width: 20px;
						height: 22px;
						margin-right: 7px;
					}

					.icon2 {
						width: 10px;
						height: 12px;
						position: absolute;
						left: 28px;
						top: 5px;
						animation: move infinite 3s ease-in-out;

						@keyframes move {
							0% {
								transform: translateY(0px) rotateY(0);
							}

							50% {
								transform: translateY(0px) rotateY(180deg);
							}

							100% {
								transform: translateY(0px) rotateY(360deg);
							}
						}
					}

					.lab {
						font-size: 14px;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						color: #ffffff;
						margin-right: 8px;
					}

					.progress {
						display: block;
						flex: 1;
						height: 100%;
						background: rgba(0, 201, 255, 0.14);

						.cur {
							width: 100%;
							border: 1px solid;
							height: 100%;
							overflow: hidden;
							transition: width 0.5s;
						}
					}

					.num {
						font-size: 16px;
						font-family: PingFangSC, PingFang SC;
						font-weight: normal;
						color: #ffffff;
						margin-left: 9px;
					}

					.percent {
						font-size: 16px;
						font-family: PingFangSC, PingFang SC;
						font-weight: normal;
						color: #ffffff;
						margin-left: 14px;
					}
				}
			}

			.pie_chart {
				height: 123px;
				display: flex;
				justify-content: space-evenly;

				.warp {
					width: 107px;
					height: 107px;
					padding-top: 0;

					/deep/.ring {
						width: 100% !important;
						height: 100% !important;
					}

					/deep/.label {
						margin-top: -65px;

						.name {
							font-size: 13px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #ffffff;
						}
					}
				}
			}
		}
	}

	.box6 {
		margin-top: 12px;

		.cont {
			.select_month {
				position: absolute;
				right: 0;
				top: -20px;

				::v-deep button {
					width: 56px;
					height: 24px;
					right: -46px;
					top: -28px;
					border-radius: 12px;
					background: url(~@/assets/shzl/select_bg.png) no-repeat center / 100% 100%;
					border: none;
					padding: 0;
					// background: linear-gradient(180deg, #0e63ac 0%, #253c69 100%);
					// border-radius: 2px;
					// border: 2px solid;
					// border-image: linear-gradient(180deg, rgba(63, 148, 224, 0.2), rgba(218, 250, 255, 0.3))
					//   2 2;
				}
			}

			// padding: 14px 28px 0;
			// .swiper-slide {
			//   height: 100%;
			// }
			// .swiper-pagination {
			//   text-align: right;
			//   bottom: 3px;
			// }
			// /deep/.swiper-pagination-bullet-active {
			//   background-color: rgba(255, 255, 255, 0.8);
			// }
			// .content {
			//   width: 100%;
			//   height: 100%;
			//   position: relative;
			//   img {
			//     position: absolute;
			//     width: 100%;
			//     height: 100%;
			//     top: 0;
			//     left: 0;
			//   }
			//   .cont {
			//     width: 100%;
			//     height: 30px;
			//     padding: 5px 10px;
			//     position: absolute;
			//     bottom: 0;
			//     font-size: 14px;
			//     font-family: PingFangSC, PingFang SC;
			//     font-weight: 500;
			//     color: #ffffff;
			//     line-height: 22px;
			//     text-align: left;
			//     background-color: rgba(0, 0, 0, 0.35);
			//   }
			// }
		}
	}

	.box7 {
		margin-top: 20px;
		.cont {
			display: grid;
			place-items: center;
			position: relative;

			.fy_out {
				position: absolute;
				top: 3%;
				left: 27%;
				z-index: 99;
				transform: translate(-50%, -50%);
				animation: rotateS infinite 12s linear;
			}

			.fy_in {
				position: absolute;
				top: 44%;
				left: 50%;
				transform: translate(-50%, -50%);
			}

			.wrap {
				position: relative;
				width: 393px;
				height: 205px;
				background: url(~@/assets/csts/bg3.png) no-repeat;

				li {
					position: absolute;
					text-align: left;

					.num {
						font-size: 20px;
						font-family: PingFangSC, PingFang SC;
						font-weight: normal;
						color: #ffffff;
						line-height: 24px;
					}

					.tit {
						font-size: 14px;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						color: #ffffff;
						line-height: 17px;
					}

					&:nth-child(1) {
						top: 28px;
						left: 24px;
					}

					&:nth-child(2) {
						top: 28px;
						left: 295px;
					}

					&:nth-child(3) {
						bottom: 32px;
						left: 24px;
					}

					&:nth-child(4) {
						bottom: 32px;
						left: 295px;
					}
				}
			}
		}
	}

	.box8 {
		margin-top: 16px;
		position: relative;
		.njfbDw {
			position: absolute;
			left: 50px;
			top: 20px;
			color: #ffffff;
			font-size: 14px;
			font-family: PingFangSC, PingFang SC;
			font-weight: 300;
		}
		.cout {
			width: 100%;
			height: 100%;
		}
	}

	.box9 {
		.cont {
			padding: 16px 23px 0;

			.title {
				width: 100%;
				height: 44px;
				line-height: 44px;
				display: flex;
				justify-content: space-between;
				padding: 0 24px;

				span {
					font-size: 14px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #ffffff;
				}
			}

			.detail {
				position: relative;
				width: 404px;
				height: 156px;
				background: url(~@/assets/csts/bg4.png) no-repeat center;
				display: flex;
				justify-content: space-between;

				.center_out {
					position: absolute;
					left: 29%;
					top: -2%;
					transform: translate(-50%, -50%);
					z-index: 99;
					animation: rotateS infinite 12s linear;
				}

				.center_in {
					position: absolute;
					left: 29%;
					top: 2px;
					transform: translate(-50%, -50%);
					z-index: 99;
					animation: rotateN infinite 12s linear;
				}

				.fs {
					display: flex;
					flex-direction: column;
					justify-content: space-evenly;
					gap: 9px;

					li {
						display: flex;
						justify-content: space-between;
						align-items: center;

						.icon {
							width: 13px;
							height: 16px;
						}

						.lab {
							font-size: 14px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #ffffff;
							line-height: 20px;
							padding-left: 6px;
							padding-right: 22px;
						}

						.num {
							font-size: 10px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #ffffff;

							span {
								font-size: 18px;
								font-family: PingFangSC, PingFang SC;
								font-weight: normal;
								color: #ffffff;
								line-height: 22px;
							}
						}
					}
				}

				.fq {
					display: flex;
					flex-direction: column;
					justify-content: space-evenly;
					gap: 9px;

					li {
						display: flex;
						justify-content: space-between;
						align-items: center;

						.icon {
							width: 14px;
							height: 14px;
						}

						.lab {
							font-size: 14px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #ffffff;
							line-height: 20px;
							padding-left: 22px;
							padding-right: 6px;
						}

						.num {
							font-size: 10px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #ffffff;

							span {
								font-size: 18px;
								font-family: PingFangSC, PingFang SC;
								font-weight: normal;
								color: #ffffff;
								line-height: 22px;
							}
						}
					}
				}

				.zdqy {
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);

					.num {
						font-size: 20px;
						font-family: PingFangSC, PingFang SC;
						font-weight: normal;
						color: #ffffff;
						line-height: 24px;
					}

					.lab {
						font-size: 18px;
						font-family: PingFangSC, PingFang SC;
						color: #ffffff;
						line-height: 21px;
						letter-spacing: 1px;
					}
				}
			}
		}
	}

	.box12 {
		position: relative;

		.gajq_lz {
			width: 100%;
			height: 100%;
			position: absolute;
			top: 0;

			img {
				width: 100%;
				height: 100%;
			}
		}
	}

	.left_bg {
		width: 460px;
		height: 1033px;
		background: url(~@/assets/leader/img/component/left_bg.png) no-repeat center / 100% 100%;
		position: absolute;
		left: 50px;
		top: 25px;
		z-index: 102;
	}

	.right_bg {
		width: 520px;
		height: 1033px;
		background: url(~@/assets/leader/img/component/right_bg.png) no-repeat center / 100% 100%;
		position: absolute;
		right: 50px;
		top: 25px;
		z-index: 102;
	}

	.left {
		// width: 450px;

		// height: calc(100vh - 165px - 55px);
		// height: 672.5px;

		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: center;
		margin-top: 104px;
		margin-left: 19px;
		position: relative;
		left: 0;
		opacity: 1;
		z-index: 1003;
		-webkit-transition: all 0.5s ease-in;
		-moz-transition: all 0.5s ease-in;
		transition: all 0.5s ease-in;
		display: none;
	}

	.top-middle {
		position: absolute;
		top: 110px;
		width: 100%;
		// width: 100%;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
		-webkit-transition: all 0.5s ease-in;
		-moz-transition: all 0.5s ease-in;
		transition: all 0.5s ease-in;

		/deep/ .el-tree {
			background: transparent !important;

			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 14px;
			color: #b0e0ff;
			text-align: left;
			font-style: normal;
			text-transform: none;

			.is-current {
				background-color: #178dfa !important;
				color: #fff !important;
			}

			.el-tree-node:focus > .el-tree-node__content {
				background-color: #178dfa !important;
				color: #fff !important;
			}

			.el-tree-node__content {
				height: 32px;

				&:hover {
					background: #178dfa !important;
					color: #fff !important;
				}
			}
		}

		.top-header-descripte {
			width: 880px;
			position: absolute;
			top: 0;
			left: 0;
			-webkit-transition: all 0.5s ease-in;
			-moz-transition: all 0.5s ease-in;
			transition: all 0.5s ease-in;
			&.top-header-descripte-move {
				top: -182px;
			}
		}
	}

	.view-type-box {
		// margin-top: 20px;
		// margin-left: 24px;

		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: center;
		z-index: 1001;
		position: absolute;
		left: 0;
		top: 72px;

		-webkit-transition: all 0.5s ease-in;
		-moz-transition: all 0.5s ease-in;
		transition: all 0.5s ease-in;

		.layer-select-box {
			width: 48px;
			height: 48px;
			background: skyblue;
			cursor: pointer;
			box-sizing: border-box;
			display: flex;
			justify-content: center;
			align-items: flex-end;
			padding-bottom: 2px;
			background-image: url(~@/assets/img/zyjd/map-layer-bg.png);

			/* 背景不重复 */
			background-repeat: no-repeat;
			/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
			background-size: cover;
			/* 背景居中显示（可选） */
			background-position: center;

			font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
			font-weight: normal;
			font-size: 12px;
			color: #c9e2fa;
			text-align: center;
			font-style: normal;
			text-transform: none;
		}

		.mapItem {
			width: 100%;
			height: 34px;
			margin-bottom: 4px;
			cursor: pointer;
			display: flex;
			justify-content: flex-start;
			align-items: center;

			.icon-box {
				width: 34px;
				height: 34px;
				display: flex;
				justify-content: center;
				align-items: center;

				background-image: url(~@/assets/img/jxhfz/map-btn-icon-bg.png);

				/* 背景不重复 */
				background-repeat: no-repeat;
				/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
				background-size: cover;
				/* 背景居中显示（可选） */
				background-position: center;

				img {
					// width: 18px;
					// height: 14px;
				}
			}

			span {
				width: calc(100% - 34px - 16px);
				height: 20px;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				padding-left: 8px;
				background: linear-gradient(91deg, #1485ff 0%, rgba(11, 100, 195, 0) 100%);
				border-radius: 10px 10px 10px 10px;
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 14px;
				color: #ffffff;
				line-height: 20px;
				text-align: left;
				font-style: normal;
			}

			&:last-child {
				margin-bottom: 0;
			}

			&.active-item {
				span {
					background: linear-gradient(90deg, #fc973b 0%, rgba(132, 67, 31, 0) 100%);
				}
				.icon-box {
					background-image: url(~@/assets/img/jxhfz/map-btn-icon-active-bg.png);
				}
			}
		}

		&.view-type-box-move {
			left: -480px;
		}
	}

	.mkdx-top-middle {
		top: -200px;
	}

	.mkdxLeft {
		left: -450px;
		opacity: 0;
	}

	.right {
		width: 450px;
		height: 706px;
		display: flex;
		justify-content: space-between;
		margin-top: 104px;
		margin-right: 32px;
		position: relative;
		right: 0;
		z-index: 1003;
		-webkit-transition: all 0.5s ease-in;
		-moz-transition: all 0.5s ease-in;
		transition: all 0.5s ease-in;
		display: none;
		.njlx_box {
			&::-webkit-scrollbar {
				width: 6px;
				background: transparent;
			}

			&::-webkit-scrollbar-thumb {
				/*滚动条里面小方块*/
				border-radius: 2px;
				box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
				background: rgb(45, 124, 228);
				height: 100px;
				width: 20px;
			}
		}

		.njlx_box {
			display: flex;
			flex-wrap: wrap;
			margin: 10px auto 0;
			text-align: left;
			width: 100%;
			height: calc(100% - 30px);
			overflow-y: scroll;
			-ms-overflow-style: none;
			/* IE 10+ */
			scrollbar-width: none;
			/* Firefox */

			.njlx_item {
				display: inline-block;
				margin: 0 3% 7px 3%;
				width: 27%;
				// height: 93px;
				// height:45%;
				// height:30%;
				// background-image: url(~@/assets/bjnj/njlx_bgImg.png);
				// background-size: 100% 100%;
				// background-repeat: no-repeat;
				// margin-bottom: 7px;
				// padding-top: 8px;
				box-sizing: border-box;
				text-align: center;

				.njlx_title {
					margin: 0 auto;
					max-width: fit-content;
					min-width: 100px;
					background-image: url(~@/assets/bjnj/njlx_titleImg.png);
					background-size: 100% 18px;
					background-repeat: no-repeat;
					// height: 18px;
					font-family: PingFangSC, PingFang SC;
					font-size: 16px;
					color: #edfbff;
					line-height: 18px;
					letter-spacing: 1px;
					text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.15);
					font-style: normal;
					text-align: center;
				}

				.njlx_number {
					margin: 8px auto;
					width: fit-content;
					height: 24px;
					font-family: PangMenZhengDao;
					font-size: 24px;
					color: #ffffff;
					line-height: 24px;
					letter-spacing: 1px;
					text-shadow: 0px 0px 4px rgba(73, 140, 255, 0.85);
					text-align: left;
					font-style: normal;
					background: linear-gradient(90deg, #00faff 0%, #00b1ff 100%);
					-webkit-background-clip: text;
					/*将设置的背景颜色限制在文字中*/
					-webkit-text-fill-color: transparent;
					/*给文字设置成透明*/
				}
			}
			.njlx_item2 {
				display: inline-block;
				margin: 0 10px;
				width: 28%;
				// height: 93px;
				height: 30%;
				// background-image: url(~@/assets/bjnj/njlx_bgImg2.png);
				background-size: 100% 100%;
				background-repeat: no-repeat;
				margin-bottom: 7px;
				// padding-top: 8px;
				box-sizing: border-box;
				text-align: center;

				.njlx_title2 {
					margin: 0 auto;
					max-width: fit-content;
					min-width: 100px;
					background-image: url(~@/assets/bjnj/njlx_titleImg2.png);
					background-size: 100% 100%;
					background-repeat: no-repeat;
					height: 18px;
					font-family: PingFangSC, PingFang SC;
					font-size: 16px;
					color: #edfbff;
					line-height: 18px;
					letter-spacing: 1px;
					text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.15);
					font-style: normal;
					text-align: center;
				}

				.njlx_number {
					margin: 8px auto;
					width: fit-content;
					height: 24px;
					font-family: PangMenZhengDao;
					font-size: 24px;
					color: #ffffff;
					line-height: 24px;
					letter-spacing: 1px;
					text-shadow: 0px 0px 4px rgba(255, 164, 73, 0.85);
					text-align: left;
					font-style: normal;
					background: linear-gradient(90deg, #ff9800 0%, #ffcb00 100%);
					-webkit-background-clip: text;
					/*将设置的背景颜色限制在文字中*/
					-webkit-text-fill-color: transparent;
					/*给文字设置成透明*/
				}
			}
		}
	}

	.mkdxRight {
		right: -450px;
		opacity: 0;
	}
	.zwsjImg {
		height: 80%;
		margin: 0 auto;
		margin-top: 36px;
	}
}
.middle {
	width: 100%;
	height: 237px;
	position: absolute;
	box-sizing: border-box;
	display: none !important;
	left: 50%;
	bottom: 24px;
	transform: translateX(-50%);
	// background: url(~@/assets/bjnj/dbmc.png) no-repeat center / 100% 100%;
	z-index: 1002;
	-webkit-transition: all 0.5s ease-in;
	-moz-transition: all 0.5s ease-in;
	transition: all 0.5s ease-in;
	padding: 0 20px;
	.box10 {
		.cont {
			width: 100%;
			height: 100%;
			margin: 0 auto;
			position: relative;
			.couts {
				width: 100%;
				height: 100%;
				.zwsjImg {
					height: 80%;
					margin: 0 auto;
					margin-top: 45px;
				}
			}
			.nfxz {
				position: absolute;
				left: 57.5%;
				top: 12px;
				z-index: 1003;
				width: 100px;
				height: 20px;
				::v-deep .el-input {
					font-size: 12px;
				}
				::v-deep .el-input__inner {
					color: #ccf4ff;
					height: 20px;
					line-height: 20px;
					background: linear-gradient(180deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%),
						rgba(0, 74, 143, 0.4);
					border: 1px solid rgba(0, 162, 255, 0.6);
				}
				::v-deep .el-input__icon {
					line-height: 20px;
				}
			}
			.yfxz {
				position: absolute;
				left: 63.5%;
				top: 12px;
				z-index: 1003;
				width: 100px;
				height: 20px;
				::v-deep .el-input {
					font-size: 12px;
				}
				::v-deep .el-input__inner {
					color: #ccf4ff;
					height: 20px;
					line-height: 20px;
					background: linear-gradient(180deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%),
						rgba(0, 74, 143, 0.4);
					border: 1px solid rgba(0, 162, 255, 0.6);
				}
				::v-deep .el-input__icon {
					line-height: 20px;
				}
			}
			.fhBox {
				width: 80px;
				border: 1px solid rgba(0, 162, 255, 0.6);
				border-radius: 5px;
				position: absolute;
				left: 69.5%;
				top: 12px;
				z-index: 1003;
				font-size: 12px;
				height: 20px;
				color: #7e94a9;
				padding-top: 1px;
				display: flex;
				align-items: center;
				padding: 0 10px;
				cursor: pointer;
				.fh {
					height: 16px;
					margin-right: 4px;
				}
			}
			.fhBox:active {
				color: #ccf4ff;
			}
		}
	}
}
.mkdxMiddle {
	bottom: -325px;
	opacity: 0;
}

.jgzzBox {
	width: 156px;
	height: 163px; // 204
	display: flex;
	flex-direction: column;
	position: absolute;
	left: 980px;
	bottom: 93px;
	z-index: 999;
	background: url(~@/assets/mskx/bg_resource.png) no-repeat center / 100% 100%;

	.jgzzItem {
		width: 153px;
		height: 36px;
		margin: 1px 2px 1px 2px;
		cursor: pointer;

		// background: url(~@/assets/csts/btn11.png) no-repeat;
		img {
			width: 22px;
			height: 22px;
			float: left;
			margin: 7px 3px 0 10px;
		}

		span {
			float: left;
			font-size: 17px;
			font-family: PingFangSC, PingFang SC;
			color: #e9fffe;
			line-height: 36px;
			background: linear-gradient(180deg, #ffffff 0%, #2f8af4 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}
	}

	.jgzzActive {
		width: 153px;
		height: 36px;
		margin: 1px 2px 1px 2px;
		// background: url(~@/assets/csts/btn12.png) no-repeat;
		background: rgba(108, 163, 255, 0.3);
	}
}

.zdcsBox {
	width: 109px;
	height: 248px;
	display: flex;
	flex-direction: column;
	position: absolute;
	left: 1197px;
	bottom: 93px;
	z-index: 999;
	background: url(~@/assets/csts/btnBg12.png) no-repeat center / 100% 100%;

	.jgzzItem {
		width: 103px;
		height: 35px;
		margin: 2px 3px 2px 3px;
		background: url(~@/assets/csts/btn11.png) no-repeat;

		img {
			width: 22px;
			height: 22px;
			float: left;
			margin: 7px 0 0 10px;
		}

		span {
			float: left;
			font-size: 17px;
			font-family: PingFangSC, PingFang SC;
			color: #e9fffe;
			line-height: 35px;
			background: linear-gradient(180deg, #ffffff 0%, #2f8af4 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}
	}

	.jgzzActive {
		width: 156px;
		height: 35px;
		margin: 1px 2px 1px 2px;
		background: url(~@/assets/csts/btn12.png) no-repeat;
	}
}
.tjxxBox {
	width: 800px;
	height: 74px;
	position: absolute;
	top: 124px;
	left: 50%;
	transform: translateX(-50%);
	z-index: 1003;
	.tjxxItem {
		width: 180px;
		height: 74px;
		margin: 0 10px;
		float: left;
		.tjxxName {
			width: 180px;
			height: 36px;
			background: url(~@/assets/bjnj/tjxxBg.png) no-repeat center / 100% 100%;
			font-family: AlibabaPuHuiTi;
			font-size: 18px;
			color: #dff3ff;
			line-height: 36px;
			text-align: center;
			font-style: normal;
		}
		.tjxxNum {
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 26px;
			color: #ffffff;
			line-height: 38px;
			text-shadow: 0px 0px 1px rgba(0, 18, 57, 0.84);
			text-align: center;
			font-style: normal;
			span {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #ffffff;
				line-height: 20px;
				text-shadow: 0px 0px 1px rgba(0, 18, 57, 0.84);
				text-align: left;
				font-style: normal;
			}
		}
	}
}
.mapBtn {
	width: 106px;
	position: absolute;
	z-index: 1003;

	-webkit-transition: all 0.5s ease-in;
	-moz-transition: all 0.5s ease-in;
	transition: all 0.5s ease-in;

	.mapItem2 {
		width: 160px;
		height: 40px;
		margin: 8px 0;
		background: url(~@/assets/bjnj/btn2.png) no-repeat center / 100% 100%;
		img {
			vertical-align: middle;
			margin-top: -10px;
		}
		span {
			margin-left: 4px;
			font-family: YouSheBiaoTiHei;
			font-size: 20px;
			color: #ffffff;
			line-height: 40px;
			text-align: left;
			font-style: normal;
			background: linear-gradient(180deg, #ffffff 0%, #ffca00 100%);
			background-clip: text;
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			cursor: pointer;
		}
	}
}

.range-axis-box {
	width: 1150px;
	position: absolute;
	z-index: 1002;
	padding: 12px;
	// background: linear-gradient(90deg, rgba(42, 130, 255, 0.5) 0%, rgba(0, 98, 234, 0.9) 100%);
	transition: all 0.5s ease-in;

	background: linear-gradient(90deg, rgba(42, 130, 255, 0.5) 0%, rgba(0, 98, 234, 0.9) 100%);
	border-radius: 4px;
	border: 1px solid rgba(0, 163, 255, 0.5);
}

.map-operate-box {
	position: absolute;
	z-index: 1002;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: center;

	// background: rgba(0, 163, 255, 0.05);
	background: linear-gradient(90deg, rgba(42, 130, 255, 0.5) 0%, rgba(0, 98, 234, 0.9) 100%);

	border-radius: 4px;

	border: 1px solid rgba(0, 163, 255, 0.5);

	-webkit-transition: all 0.5s ease-in;
	-moz-transition: all 0.5s ease-in;
	transition: all 0.5s ease-in;

	.operate-box {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		.operate-item-box {
			// width: 107px;
			height: 32px;
			display: flex;
			justify-content: center;
			align-items: center;
			cursor: pointer;
			padding: 0 13px;
			padding-right: 0;

			img {
				width: 16px;
				height: 16px;
				margin-right: 6px;
			}

			.operate-icon {
				font-size: 15px;
				color: #30a0a9;

				/* 背景不重复 */
				background-repeat: no-repeat;
				/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
				background-size: cover;
				/* 背景居中显示（可选） */
				background-position: center;
				margin-right: 6px;
				padding-left: 13px;
			}

			.icon-rotate {
				// transform: rotate(90deg);
				// transform-origin: center; /* 旋转的中心点为元素的中心 */
			}

			.operate-title {
				padding-right: 13px;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #b0e0ff;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}

			&:hover {
				background: rgba(255, 128, 12, 0.8);

				.operate-title {
					color: #fff;
				}
			}

			.devider {
				width: 1px;
				height: 12px;
				background: #848991;
			}

			&:last-child {
				.devider {
					display: none;
				}
			}
		}
	}
}

.zyfbBox {
	width: 240px;
	height: 120px;
	background: url(~@/assets/bjnj/mapBg.png) no-repeat center / 100% 100%;
	position: absolute;
	top: 220px;
	left: 665px;
	z-index: 1003;
	padding: 3px 0 0 24px;
	.zyfbItem {
		width: 210px;
		height: 36px;
		cursor: pointer;
		img {
			width: 22px;
			height: 22px;
			float: left;
			margin: 6px 8px 0 7px;
		}

		span {
			display: inline-block;
			width: 100%;
			text-align: center;
			float: left;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 16px;
			color: #ffffff;
			line-height: 36px;
			font-style: normal;
		}
	}
	.zyfbItem:hover {
		background: rgba(108, 163, 255, 0.3);
	}
	.jgzzActive {
		background: rgba(108, 163, 255, 0.3);
	}
}
.njfbBox {
	width: 244px;
	max-height: 368px;
	// background: url(~@/assets/bjnj/mapBg2.png) no-repeat center / 100% 100%;
	position: absolute;

	z-index: 1003;
	padding: 16px;

	background: #04244e;
	border-radius: 4px 4px 4px 4px;
	border: 1px solid #004267;
	overflow-y: auto;
	display: block;

	&.hide-njfbBox {
		display: none;
	}
	&::-webkit-scrollbar {
		width: 4px; /* 滚动条宽度 */
	}

	//轨道
	&::-webkit-scrollbar-track {
		background: #04244e; /* 滚动条轨道背景 */
		border-radius: 10px;
		padding: 5px 0;
	}

	//滑块
	&::-webkit-scrollbar-thumb {
		background: #82aed0; /* 滑块背景颜色 */
		border-radius: 10px;
		height: 15px;
	}

	//悬停滑块颜色
	&::-webkit-scrollbar-thumb:hover {
		background: #ff823b; /* 鼠标悬停时滑块颜色 */
	}

	//箭头
	&::-webkit-scrollbar-button {
		display: none; /* 隐藏滚动条的箭头按钮 */
	}
	.zyfbItem {
		width: 100%;
		height: 36px;
		color: #4e5969;
		cursor: pointer;
		img {
			width: 22px;
			height: 22px;
			float: left;
			margin: 6px 8px 0 7px;
		}

		span {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 16px;
			// color: #ffffff;
			color: #4e5969;

			line-height: 36px;
			text-align: center;
			font-style: normal;
		}
	}
	.zyfbItem:hover {
		// background: rgba(108, 163, 255, 0.3);
		background: #30a0b5;

		span {
			color: #fff;
		}
	}
	.jgzzActive {
		// background: rgba(108, 163, 255, 0.3);
		background: #30a0b5;

		span {
			color: #fff;
		}
	}
}
.njfbBox2 {
	width: 168px;
	height: 336px;
	// background: url(~@/assets/bjnj/mapBg2.png) no-repeat center / 100% 100%;
	position: absolute;
	// top: 346px;
	// left: 665px;
	z-index: 1003;
	padding: 3px;
	background: #d3e7e9;
	border: 1px solid #389892;
	.zyfbItem {
		width: 100%;
		height: 36px;
		cursor: pointer;
		img {
			width: 22px;
			height: 22px;
			float: left;
			margin: 6px 8px 0 7px;
		}

		span {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 16px;
			// color: #ffffff;
			color: #4e5969;

			line-height: 36px;
			text-align: center;
			font-style: normal;
		}
	}
	.zyfbItem:hover {
		// background: rgba(108, 163, 255, 0.3);
		background: #30a0b5;

		span {
			color: #fff;
		}
	}
	.jgzzActive {
		// background: rgba(108, 163, 255, 0.3);
		background: #30a0b5;

		span {
			color: #fff;
		}
	}
}

.left,
.right {
	::v-deep {
		.el-carousel {
			width: 450px;
			height: 920px;

			.el-carousel__indicators {
				.el-carousel__indicator {
					&.is-active {
						.el-carousel__button {
							height: 4px;
							background-color: #49e6ff;
						}
					}

					.el-carousel__button {
						height: 4px;
						background-color: rgba(255, 255, 255, 0.4);
						border-radius: 2px;
					}
				}
			}
		}
	}
}

.right {
	::v-deep {
		.el-carousel {
			margin-left: auto;
		}
	}
}

::v-deep .el-carousel--horizontal {
	width: 450px;
	overflow-x: hidden;
}

.map_box {
	position: absolute;
	width: 1920px;
	height: 1080px;
	top: 0px;
	left: 0;
	z-index: 999;
	// border: 1px solid red;
}

.mkss {
	width: 44px;
	height: 44px;
	position: absolute;
	bottom: 735px;
	right: 540px;
	z-index: 999;
	background: url(~@/assets/map/ss2.png) no-repeat center / 100% 100%;

	.mkss2 {
		font-size: 16px;
		color: #fff;
		opacity: 0;
	}
}

.mkss1 {
	width: 44px;
	height: 44px;
	position: absolute;
	bottom: 735px;
	right: 540px;
	z-index: 999;
	background: url(~@/assets/map/ss1.png) no-repeat center / 100% 100%;

	.mkss2 {
		font-size: 16px;
		color: #fff;
		opacity: 0;
	}
}
/* 样式用于控制滚动容器的高度、宽度、边框和滚动效果等 */
.introduce {
	// width: 500px;
	// height: 200px;
	overflow: hidden;
	position: relative;
}

/* 样式用于控制滚动文本的位置和颜色 */
.introduce p {
	position: absolute;
	top: 0;
	left: 0;
	margin: 0;
	padding: 10px;
	font-size: 16px;
}

.njtlBox {
	width: 44px;
	height: 44px;
	position: absolute;
	bottom: 685px;
	right: 539px;
	z-index: 1099;
	img {
		width: 44px;
		height: 44px;
	}
}
.ssBox {
	width: 44px;
	height: 44px;
	position: absolute;
	bottom: 585px;
	right: 539px;
	z-index: 1099;
	img {
		width: 44px;
		height: 44px;
	}
}
.sxBox {
	width: 44px;
	height: 44px;
	position: absolute;
	bottom: 635px;
	right: 539px;
	z-index: 1099;
	img {
		width: 44px;
		height: 44px;
	}
}
.ssPop {
	width: 308px;
	position: absolute;
	// top: 401px;
	// right: 589px;
	z-index: 1099;
	.ssPop1 {
		width: 308px;
		height: 43px;
		input {
			float: left;
			width: 256px;
			height: 43px;
			// background: linear-gradient(180deg, rgba(17, 77, 168, 0) 0%, rgba(36, 132, 212, 0.5) 100%), rgba(2, 52, 114, 0.8);
			background: #fff;

			box-shadow: inset 0px 0px 2px 0px #57afff;
			border-radius: 2px;
			// border: 1px solid #0a5eaa;
			border: 1px solid #a6d2d3;

			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 16px;
			color: #5e5d5d;
			line-height: 43px;
			// text-shadow: 0px 2px 3px rgba(0, 48, 72, 0.5);
			text-shadow: 0px 2px 3px #a6d2d3;

			text-align: left;
			font-style: normal;
			padding-left: 20px;
		}
		.ssImgBox {
			float: left;
			width: 52px;
			height: 43px;
			line-height: 43px;
			// background: linear-gradient(180deg, rgba(17, 77, 168, 0) 0%, rgba(36, 132, 212, 0.5) 100%), #1373d9;
			background: #fff;
			border-radius: 2px;
			// border: 1px solid #0a5eaa;
			border: 1px solid #a6d2d3;

			img {
				vertical-align: middle;
				margin-top: -3px;
			}
		}
	}
	.ssPop2 {
		width: 257px;
		height: 270px;
		// background: linear-gradient(180deg, rgba(17, 77, 168, 0) 0%, rgba(36, 132, 212, 0.5) 100%), rgba(2, 52, 114, 0.8);
		background: #fff;

		box-shadow: inset 0px 0px 2px 0px #57afff;
		border-radius: 2px;
		// border: 1px solid #0a5eaa;
		border: 1px solid #a6d2d3;

		overflow: auto;
		/* 隐藏全局滚动条 */
		&::-webkit-scrollbar {
			display: none;
		}

		.ssList {
			width: 257px;
			height: 45px;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 16px;
			// color: #c2ddfc;
			color: #5e5d5d;
			line-height: 40px;
			// text-shadow: 0px 2px 3px rgba(0, 48, 72, 0.5);
			text-shadow: 0px 2px 3px #a6d2d3;

			text-align: left;
			font-style: normal;
			padding-left: 20px;
		}
	}
}
.jiangshuiTlList {
	// width: 91px;
	// height: 161px;
	position: absolute;
	z-index: 1099;
	padding: 12px;

	// background: #d3e7e9;
	-webkit-transition: all 0.5s ease-in;
	-moz-transition: all 0.5s ease-in;
	transition: all 0.5s ease-in;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: flex-start;

	background: linear-gradient(90deg, rgba(42, 130, 255, 0.5) 0%, rgba(0, 98, 234, 0.9) 100%);
	border-radius: 4px;
	border: 1px solid rgba(0, 163, 255, 0.5);

	.label-box {
		font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
		font-weight: 400;
		font-size: 14px;
		color: #fff;
		// color: #000;
		text-align: left;
		font-style: normal;
		text-transform: none;
		margin-bottom: 12px;
	}

	.jiangshuiTlItem {
		width: 100%;
		// height: 38px;
		margin-bottom: 10px;
		display: flex;
		justify-content: flex-start;
		align-items: center;

		.color-box {
			width: 27px;
			height: 20px;
			margin-right: 10px;
			display: flex;
			justify-content: center;
			align-items: center;

			border-radius: 2px 2px 2px 2px;
			border: 1px solid rgba(255, 255, 255, 0.6);

			.icon-box {
				font-size: 12px;
				color: #fff;
			}
		}

		.jiangshuiTlName {
			font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
			font-weight: 400;
			font-size: 14px;
			color: #fff;
			// color: #000;

			text-align: left;
			font-style: normal;
			text-transform: none;
		}
		img {
			width: 31px;
			height: 38px;
			float: left;
		}

		&:last-child {
			margin-bottom: 0;
		}
	}
}
.wgsj_fx {
	width: 100%;

	// height: 100%;
	.select_m {
		position: absolute;
		right: 0;
		top: -50px;
		line-height: 30px;
		font-size: 16px;
		font-family: PingFangSC, PingFang SC;
		color: #caecff;
		text-shadow: 0px 0px 1px #00132e;

		::v-deep .el-dropdown button {
			font-family: PingFangSC, PingFang SC;
			font-size: 16px;
			color: #caecff;
			line-height: 21px;
			text-align: center;
			font-style: normal;

			// width: 100px;
			height: 30px;
			// background: url(~@/assets/mskx/icon_drop.png) no-repeat center / 100% 100%;
			background: transparent;
			border: none;
			padding: 0 0 0 5px;
		}

		::v-deep .el-icon-arrow-down:before {
			// content: '\e790' !important;
			color: #caecff;
		}

		::v-deep .el-popper[x-placement^='bottom'] .popper__arrow:after {
			display: none;
		}

		::v-deep .el-popper .popper__arrow:after {
			border-bottom-color: #185493 !important;
		}
	}
}
::v-deep .el-dropdown {
	color: #caecff;
	font-family: YouSheBiaoTiHei;
	font-size: 16px;
	line-height: 21px;
	text-align: right;
	font-style: normal;
	line-height: 40px;
	right: 0;
	position: absolute;
}
::v-deep .el-dropdown-menu {
	// background: #00173b;
	position: absolute;
	left: 0;
	z-index: 10;
	padding: 10px 0;
	margin: 5px 0;
	// border: 1px solid #2b7bbb;
	border-radius: 4px;
	background-color: #c6e2e7 !important;
	border: 1px solid #d2f2ea !important;
}
/deep/ .el-input__inner {
	background: #002450;
	color: #fff;
	border: 1px solid #1359c2;
}
</style>

<style lang="less">
.jxhfz-year-select-popover-box {
	margin-top: 4px !important;
	.el-year-table {
		.available:hover {
			.cell {
				color: #178dfa !important;
			}
		}
		.current {
			.cell {
				color: #178dfa !important;
			}
		}
	}
}
</style>
