<template>
  <div class="leader_box">
    <div class="leader_sjts">
      <div class="select_head">
        <el-dropdown trigger="click" @command="handleHeadDrop">
          <el-button class="select_" type="primary">
            {{ headName }}
            <i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu class="select_head" slot="dropdown">
            <el-dropdown-item v-for="item in headList" :key="item.name" :command="item.name">{{ item.name
              }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <div class="select_head2">
        <el-dropdown trigger="click" @command="handleHeadDrop2">
          <el-button class="select_" type="primary">
            {{ headName2 }}
            <i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu class="select_head2" slot="dropdown">
            <el-dropdown-item v-for="item in headList2" :key="item.name" :command="item.name">{{ item.name
              }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <div class="left" :class="mkdx ? 'mkdxLeft' : ''">
        <div class="left1">
          <BlockBox title="作业总览" class="box" :isListBtns="false" :blockHeight="283">
            <div class="cout box1">
              <div class="box1_left">
                <div class="liquidPercent">{{ (liquidData * 100).toFixed(2) }}%</div>
                <div class="liquidPercent2">机械化占比</div>
                <liquid-chart :data="liquidData" :init-option="liquidInitOptions" :options="liquidOptions" />
              </div>
              <div class="box1_right">
                <ul class="ybsscont">
                  <li v-for="(it, index) of ybssData" :key="index">
                    <span class="icon" :style="{ background: `url(${it.img}) no-repeat` }"></span>
                    <div class="tit">{{ it.tit }}</div>
                    <div class="num">
                      <countTo ref="countTo" :startVal="$countTo.startVal" :decimals="$countTo.decimals(it.num)"
                        :endVal="it.num" :duration="$countTo.duration" />
                      <span class="unit">{{ it.unit }}</span>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </BlockBox>
          <BlockBox title="作业类型" class="box box2" :isListBtns="false" :blockHeight="295">
            <div class="cout" v-show="pieData1.length > 0">
              <PieChart3D type="2" :data="pieData1" :init-option="box5InitOptions" :options="box5Options" />
            </div>
            <div class="cout" v-show="pieData1.length == 0">
              <img class="zwsjImg" src="@/assets/bjnj/zwsj.png" alt="">
            </div>
          </BlockBox>
          <BlockBox title="作业进度" class="box box3" :isListBtns="false" :blockHeight="335">
            <div class="wgsj_fx">
              <div class="select_s">
                <el-dropdown @command="wgsjYearMethod">
                  <el-button class="select_" type="primary">
                    {{ gdCommunityName }}
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu class="select_sb" slot="dropdown" style="background-color: rgba(15, 43, 87, 0.9);
                  border: 1px solid #2b7bbb;">
                    <el-dropdown-item v-for="item in trendList1" :key="item.name" :command="item.name" style="color: rgba(255, 255, 255, 0.8);">{{ item.name
                      }}</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </div>
            <div v-show="barChartDataTrue.length > 0" class="njfbDw">万亩</div>
            <hor-bar-chart v-show="barChartDataTrue.length > 0" :data="barChartDataTrue" :init-option="barInitOptions" :options="zyjdBarOptions"  />
            <div class="cout" v-show="barChartDataTrue.length == 0">
              <img class="zwsjImg" src="@/assets/bjnj/zwsj.png" alt="">
            </div>
          </BlockBox>
        </div>
      </div>
      <div class="right" :class="mkdx ? 'mkdxRight' : ''">
        <div class="right1">
          <BlockBox title="作业趋势" class="box box7" :isListBtns="false" :blockHeight="305">
            <div class="wgsj_fx">
              <div class="select_r1">
                <el-dropdown @command="handleTrendDrop">
                  <el-button class="select_r1" type="primary">
                    {{ trendName }}
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu class="select_sb" slot="dropdown">
                    <el-dropdown-item v-for="item in trendList1" :key="item.name" :command="item.name">{{ item.name
                      }}</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </div>
            <TowerChart v-show="czsrData.length > 0" :options="czsrOptions" :data="czsrData" :init-option="initOption1" />
            <div class="cout" v-show="czsrData.length == 0">
              <img class="zwsjImg" src="@/assets/bjnj/zwsj.png" alt="">
            </div>
          </BlockBox>
          <BlockBox title="作物类型" class="box box8" :isListBtns="false" :blockHeight="305" v-if="barShow_tr">
            <StereoscopicBarChart v-show="qyeqData.length > 0" :data="qyeqData" :options="qyeqOptions" :init-option="initOptions5" />
            <div class="cout" v-show="qyeqData.length == 0">
              <img class="zwsjImg" src="@/assets/bjnj/zwsj.png" alt="">
            </div>
          </BlockBox>
          <BlockBox title="投入类型" class="box box8" :isListBtns="false" :blockHeight="305" v-else>
            <hor-bar-chart v-show="barChartData_tr.length > 0" :data="barChartData_tr" :options="boxOptions_char" :init-option="boxInitOptions_char"
              ref="barChar" />
            <div class="cout" v-show="barChartData_tr.length == 0">
              <img class="zwsjImg" src="@/assets/bjnj/zwsj.png" alt="">
            </div>
          </BlockBox>
          <BlockBox title="投入趋势" class="box box6" :isListBtns="false" :blockHeight="305">
            <TrendLineChart v-show="zczData.length > 0" :options="zczOptions" :data="zczData" :init-option="initOptions4" />
            <div class="cout" v-show="zczData.length == 0">
              <img class="zwsjImg" src="@/assets/bjnj/zwsj.png" alt="">
            </div>
          </BlockBox>
        </div>
      </div>
    </div>
    <div class="map_box">
      <!-- <zlBg @emitMenu="emitMenu" /> -->
      <!-- <svgMap /> -->
      <LeaMap ref="leafletMap" @gridClick="gridClick" @operate="operate" @qctc="qctc" />
    </div>
    <leaderFooter :btns="[]" :activaIdx="activaIdx" />
    <div class="mkss" :class="!mkdx ? 'mkss1' : ''" @click="mkssBtn">
      <div class="mkss2">收缩</div>
    </div>
    <div class="sxBox" @click="sxTk">
      <img src="@/assets/bjnj/sxan.png" alt="">
    </div>
  </div>
</template>

<script>
import BlockBox from '@/components/leader/common/blockBox6.vue'
import leaderFooter from '@/components/leader/common/leaderFooter2.vue'
import LeaMap from '@/components/map/LeafletMapNj.vue'
import {
  getNjProcess,
  getCropType,
  getOverview,
  getAreaDay,
  queryTAgmachType,
  queryTAgmachWorkInfo,
  queryTAgmachTrend,
  getWorkProgress
} from '@/api/njzl/zyzx.js'
import {
  cscpCurrentUserDetails
} from '@/api/bjnj/zhdd.js'
import {
  geojson
} from '@/api/njzl/hs.api.js'


export default {
  components: {
    BlockBox,
    leaderFooter,
    LeaMap
  },
  props: {},
  data() {
    return {
      mkdx: false,
      year: 2023,
      areaId: '100000',
      areaFlag: 0,
      areaLevel_init: 0,
      barShow: true,
      barShow_tr: true,
      barChartData: [
      ],
      barChartData_tr: [],
      barChartDataTrue: [],
      time: 0,
      activaIdx: -1,
      pieData1: [
        // ['product', '人口信息'],
        // ['机耕面积', 18.94],
        // ['机种面积', 29.52],
        // ['机收面积', 51.54]
      ],
      timer1: null,
      box5Options: {
        colors: ['#6BCBB9', '#E6F973', '#FFBA4F', '#5AE29D'],
        position: ['35%', '10%'],
        legend: {
          top: 40,
          right: 0,
          orient: 'vertical',
          align: 'right',
          verticalAlign: 'center',
        },
        bgImg: {
          width: '70%',
          height: '70%',
          top: '52%',
          left: '37%',
        },
        unit: '%',
        title: {
          fontSize: '16px',
          top: 50,
        },
        subtitle: {
          fontSize: '14px',
          top: 70,
        },
      },
      boxOptions_char: {
        gradientColor: [
            "rgba(42,155,244,0.2)",
            "#0080e0"
          ]
      },
      box5InitOptions: {
        legend: {
          // top: 10,
          // itemWidth: 8,
          labelFormatter: function () {
            return (
              '<span style="color:#fff;fontSize:14px">' +
              this.name + this.percentage.toFixed(2) + '%' +
              '</span>'
            )
          }
        },
        title: {
          x: -550,
          y: 80,
        },
        subtitle: {
          x: -550,
          y: 100,
        },
        tooltip: {
          enabled: true,
          backgroundColor: 'rgba(0, 33, 59, 0.8)',
          // borderColor: 'rgba(24, 174, 236, 1)',
          padding: 10,
          style: {
            color: '#fff',
            fontSize: 14
          },
          useHTML: true, // 允许使用HTML
          formatter: function () {
            const calculationResult = ((this.y / this.total) * 100).toFixed(2)
            return `${this.key} </br>  <span style="color: ${this.point.color}">&#9679;</span> ${this.percentage.toFixed(2) + '%'}`
          }
        }
      },
      liquidData: 0,

      liquidOptions: {
        "options": {
          "colors": [
            "#4CECAA",
            "rgba(76,236,170,0.8)",
            "rgba(76,236,170,0.4)"
          ],
          "backgroundColor": "rgba(20, 31, 64, 1)",
          "borderColor": "rgba(76,236,170,0.6)",
          "shadowColor": "rgba(76,236,170,0.4)",
          "size": 80
        }
      },
      ybssData: [
        {
          num: 0,
          tit: '作业总量',
          img: require('@/assets/shzl/ybss5.png'),
          unit: '万亩'
        },
        {
          num: 0,
          tit: '机械化作业量',
          img: require('@/assets/shzl/ybss6.png'),
          unit: '万亩'
        },
        // {
        //   num: 32624,
        //   tit: '实有房屋(个)',
        //   img: require('@/assets/shzl/ybss3.png'),
        // },
        // {
        //   num: 9242,
        //   tit: '实有单位(个)',
        //   img: require('@/assets/shzl/ybss4.png'),
        // },
      ],
      czsrData: [
        // ['product', '系列名'],
        // ['2020', 800],
        // ['2021', 600],
        // ['2022', 400],
        // ['2023', 500],
        // ['2024', 300],
      ],
      czsrOptions: {
        yAxis: {
          unit: '',
        },
        barColor: "#73d5f7",
        // "openDataZoom": true
        // 是否开启轮播
        isSeriesScorll: true,
        // 轮播的间隔时间
        scorllTimes: 3000,
        // 超过多少的数量轮播
        dataZoomNum: 4,
      },
      zczOptions: {
        smooth: true,
        colors: [['#009BFF', '#0077FF']],
      },
      zczData: [
        // ['product', ''],
        // ['2020', 75],
        // ['2021', 100],
        // ['2022', 125],
        // ['2023', 100],
        // ['2024', 200],
      ],
      qyeqData: [
        ['product', '诉求数'],
        ['小麦', 0],
        ['早稻', 0],
        ['中稻', 0],
        ['玉米', 0],
        ['马铃薯', 0],
        ['大豆', 0],
      ],
      qyeqOptions: {
        "showMaxBar": true,
        "gradientColor": [
          [
            "#4CECAA",
            "#2F3BED"
          ],
          [
            "#00F7FF",
            "#2AD4A6"
          ],
          [
            "#FFD400",
            "#ADCC66"
          ],
          [
            "#FF9201",
            "#00C692"
          ],
          [
            "#DCFF5B",
            "#8A9FCF"
          ],
          [
            "#00A0FF",
            "#ADCC66"
          ]
        ],
        "showBarNumber": false,
        "maxFator": 0.02,
        "labelStyle": {
          "fontSize": "12px",
          "color": "#fff"
        },
        "offsetX": 10,
        "offsetY": 5,
        "tooltip": {
          "show": true,
          "type": "shadow"
        },
        "xAxis": {
          "rotate": 0
        },
        "yAxis": {
          "min": 0,
          "splitNumber": 5,
          "unit": ""
        },
        "legend": {
          "itemWidth": 10,
          "icon": "rect",
          "orient": "horizontal",
          "padding": 5
        },
        "color": [
          "#4CECAA",
          "#00F7FF",
          "#FFD400",
          "#FF9201",
          "#DCFF5B",
          "#00A0FF"
        ]
      },
      zyjdBarOptions:{
        gradientColor: ['#00B5FF','#BFE3FF']
      },
      barOptions: {
        "gradientColors": [
          [
            "rgba(47,59,237,0.3)",
            "#2F3BED"
          ],
          [
            "rgba(42,212,166,0.3)",
            "#2AD4A6"
          ],
          [
            "rgba(173,204,102,0.3)",
            "#ADCC66"
          ],
          [
            "rgba(0,198,146,0.3)",
            "#00C692"
          ],
          [
            "rgba(138,159,207,0.3)",
            "#8A9FCF"
          ],
          [
            "rgba(173,204,102,0.3)",
            "#ADCC66"
          ]
        ],
        "barWidth": 10,
        "isHor": true,
        "tooltip": {
          "show": true,
          "type": "shadow"
        },
        "xAxis": {
          "rotate": 0,
          "fontSize": 12,
          "showLabel": true,
          "padding": [
            4,
            0,
            0,
            0
          ],
          "interval": 0,
          "margin": 5,
          "showSplitLine": false
        },
        "yAxis": {
          "min": 0,
          "splitNumber": 5,
          "unit": ""
        },
        "legend": {
          "itemWidth": 10,
          "icon": "rect",
          "orient": "horizontal",
          "padding": 5
        },
        "color": [
          "#4CECAA",
          "#00F7FF",
          "#FFD400",
          "#FF9201",
          "#DCFF5B",
          "#00A0FF"
        ]
      },
      headName: '春耕',
      headName2: '2023',
      headList: [
        {
          name: '春耕',
          type: 'cg'
        },
        {
          name: '三夏',
          type: 'sanx'
        },
        {
          name: '三秋',
          type: 'sq'
        },
        {
          name: '双抢',
          type: 'sanq'
        },
      ],
      headList2: [
        {
          name: '2024',
          type: '2024'
        },
        {
          name: '2023',
          type: '2023'
        },
        {
          name: '2022',
          type: '2022'
        },
        {
          name: '2021',
          type: '2021'
        },
        {
          name: '2020',
          type: '2020'
        },
        {
          name: '2019',
          type: '2019'
        },
        {
          name: '2018',
          type: '2018'
        },
        {
          name: '2017',
          type: '2017'
        },
        {
          name: '2016',
          type: '2016'
        },
        {
          name: '2015',
          type: '2015'
        },
      ],
      gdCommunityName: '机耕面积',
      trendList: [],
      trendName: '机耕面积',
      trendList1: [
        {
          name: '机耕面积',
          type: 'farmland'
        },
        {
          name: '机播面积',
          type: 'sowing'
        },
        {
          name: '机收面积',
          type: 'harvest'
        }
      ],
      curSeason: 'cg',
      njType: 'farmland',
      njType1: 'farmland'
    }
  },
  created() { },
  beforeDestroy() {
    this.timer1 && clearInterval(this.timer1)
  },
  watch: {},
  computed: {
    boxInitOptions_char() {
      return {
        grid: {
          right: '10%'
        },
        xAxis: [{
          show: false,
          axisTick: {
            show: false // 不显示坐标轴刻度线
          },
          axisLine: {
            show: false, // 不显示坐标轴线
          },
          axisLabel: {
            show: false, // 不显示坐标轴上的文字
          },
        }],
        tooltip: {
          formatter: params => {
            var relVal = params[0].name
            for (var i = 0, l = params.length; i < l; i++) {
              var unit = '台'
              relVal += '<br/>' + params[i].marker + params[i].value + unit
            }
            return relVal
          }
        }
      }
    },
    liquidInitOptions() {
      return {

      }
    },
    barInitOptions() {
      return {
        yAxis: {
          nameTextStyle: {
            width: 200
          }
        },
        xAxis: [{
          show:false,
          axisTick: {
            show: false // 不显示坐标轴刻度线
          },
          axisLine: {
            show: false, // 不显示坐标轴线
          },
          axisLabel: {
            show: false, // 不显示坐标轴上的文字
          },
          // max: Math.max(...this.barChartData.map(item => { return item[1] } )),
          nameTextStyle: {
            width: 200
          }
        }],
        grid: {
          right: '20%',
          left: '5%'
        },
        tooltip: {
          show: true,
          triggerOn: 'mousemove',
          formatter: (params) => {
            var relVal = ''
            for (var i = 0, l = params.length; i < l; i++) {
              var unit = '万亩'
              relVal += params[i].marker + params[i].value + unit
            }
            return relVal
          },
        },
        // dataZoom: [
        //   {
        //     type: 'inside', // 内置型式的 dataZoom 组件
        //     yAxisIndex: [0], // 对应 x 轴的索引，默认为 0
        //     start: 0, // 起始位置的百分比
        //     end: 40, // 结束位置的百分比
        //     realtime: true,// 启用实时滚动
        //     zoomOnMouseWheel: false,  // 关闭滚轮缩放
        //     moveOnMouseWheel: true, // 开启滚轮平移
        //     moveOnMouseMove: true  // 鼠标移动能触发数据窗口平移
        //   },
        //   {
        //     type: 'slider',
        //     realtime: true,
        //     startValue: 0,
        //     endValue: 5,
        //     width: '2',
        //     height: '100%',
        //     yAxisIndex: [0], // 控制y轴滚动
        //     fillerColor: 'rgba(154, 181, 215, 1)', // 滚动条颜色
        //     borderColor: 'rgba(17, 100, 210, 0.12)',
        //     backgroundColor: '#cfcfcf', //两边未选中的滑动条区域的颜色
        //     handleSize: 0, // 两边手柄尺寸
        //     showDataShadow: false, //是否显示数据阴影 默认auto
        //     showDetail: false, // 拖拽时是否展示滚动条两侧的文字
        //     top: '1%',
        //     right: '5'
        //   }
        // ],
      }
    },
    initOption1() {
      return {
        yAxis: {
          name: '万亩',
        },
        xAxis: {
          axisLabel: {
            // interval: 2,
            // rotate: 40,
            textStyle: {
              fontSize: 12,
              // color: '#000',
            },
          },
        },
        tooltip: {
          formatter: params => {
            var relVal = params[0].name
            for (var i = 0, l = params.length; i < l; i++) {
              relVal += '<br/>' + params[i].marker + params[i].value + '万亩'
            }
            return relVal
          }
        },
      }
    },
    initOptions4() {
      return {
        yAxis: {
          name: '台',
        },
        xAxis: {
          axisLabel: {
            interval: 2,
            // rotate: 40,
            textStyle: {
              fontSize: 12,
              // color: '#000',
            },
          },
        },
        tooltip: {
          formatter: params => {
            var relVal = params[0].name
            for (var i = 0, l = params.length; i < l; i++) {
              var unit = '台'
              relVal += '<br/>' + params[i].marker + params[i].value + unit
            }
            return relVal
          }
        }
      }
    },
    initOptions5() {
      return {
        yAxis: {
          name: '万亩',
        },
        xAxis: {
          axisLabel: {
            interval: 0,
            // rotate: 40,
            textStyle: {
              fontSize: 12,
              // color: '#000',
            },
          },
        },
        dataZoom: [
          {
            type: 'inside', // 内置型式的 dataZoom 组件
            xAxisIndex: 0, // 对应 x 轴的索引，默认为 0
            start: 0, // 起始位置的百分比
            end: 60, // 结束位置的百分比
            realtime: true // 启用实时滚动
          },
        ],
        tooltip: {
          formatter: params => {
            var relVal = params[0].name
            for (var i = 0, l = params.length; i < l; i++) {
              var unit = '万亩'
              relVal += '<br/>' + params[i].marker + params[i].value + unit
            }
            return relVal
          }
        }
      }
    }

  },
  mounted() {
    // this.getNjProcess()
    this.cscpCurrentUserDetails()
    // this.geojson()
  },
  methods: {
    init() {
      this.getCropType()
      this.getOverview()
      this.getAreaDay()
      this.queryTAgmachType()
      this.queryTAgmachWorkInfo()
      this.queryTAgmachTrend()
      this.getWorkProgress()
    },
    mkssBtn() {
      this.mkdx = !this.mkdx
      console.log('this.mkdx', this.mkdx)
      this.$store.commit('invokerightShow4', this.mkdx)
    },
    sxTk() {
      this.init()
    },
    gridClick(properties) {
      this.$store.commit('invokerightShow2', properties)
      if (properties.level == "province") {
        this.areaFlag = 1
      } else if (properties.level == "city") {
        this.areaFlag = 2
      } else if (properties.level == "district" && this.areaFlag != 3) {
        this.areaFlag = 3
      }
      this.areaId = properties.adcode
      this.init()
      // this.removeAllPoi()
    },
    operate(areaId,areaId2) {
      this.areaId = areaId
      this.$store.commit('invokerightShow3', areaId2)
      console.log("areaId", this.areaId)
      console.log("lastAreaCode", this.$refs.leafletMap.lastAreaCode)
      if (this.areaId) {
        this.areaFlag = this.areaLevel_init + this.$refs.leafletMap.lastAreaCode.indexOf(areaId)
      }
      this.removeAllPoi()
      this.init();
    },
    qctc(){
      // this.toolTipShow=false;
      this.activaIdx = -1
      this.$refs.LeaderFooter.activaIdx = -1
      this.$refs.LeaderFooter.activeList = []
    },
    async cscpCurrentUserDetails(data) {
      let res = await cscpCurrentUserDetails(data)
      if (res?.code == '0') {
        this.areaId = res.data.areaId;
        this.areaLevel_init = res.data.areaLevel;
        this.areaFlag = res.data.areaLevel;
        this.$refs.leafletMap.lastAreaCode.push(res.data.areaId)
        if(res.data.areaId!=res.data.areaIdNew){ //区县级别
            this.$refs.leafletMap.loadGeoJsonLayerFromUrl(`https://geo.datav.aliyun.com/areas_v3/bound/${res.data.areaId}.json`, 'geojson', false)
            // this.$refs.leafletMap.loadGeoJsonLayerFromUrl(`https://dpzs.camtc.cn/areas_v3/bound/${res.data.areaId}.json`, 'geojson', false)
          }else{
            this.$refs.leafletMap.loadGeoJsonLayerFromUrl(`https://geo.datav.aliyun.com/areas_v3/bound/${res.data.areaId}_full.json`, 'geojson', res.data.areaLevel==0?true:false)
            // this.$refs.leafletMap.loadGeoJsonLayerFromUrl(`https://dpzs.camtc.cn/areas_v3/bound/${res.data.areaId}_full.json`, 'geojson', res.data.areaLevel==0?true:false)
          }
        if (res.data.areaLevel == 3) { //区县
          this.barShow_tr = false;
        } else {
          this.barShow_tr = true;
        }
        console.log("barShow_tr", this.barShow_tr)
        this.init()
      }
    },
    async getWorkProgress() {
      if (this.timer1) {
        clearInterval(this.timer1)
      }
      this.barShow = false
      let params = {
        farmingSeason: this.curSeason,
        type: this.njType1,
        year: this.headName2,
        areaId: this.areaId,
        areaFlag: this.areaFlag,
      }
      let res = await getWorkProgress(params)
      // this.barChartData = [['product', '']]
      // .concat(res.data.map(item => [item.province, item.data]))
      console.log('res.data',res.data.length)
      if (res.data.length > 0) {
        this.barChartData = [['product', '']]
        let arr = res.data.map(item => [item.area_name, item.data.toFixed(0)])
        this.barChartData.push(...arr)
        this.barShow = true
        this.forBarChart()
      } else {
        this.barChartDataTrue = []
      }
      // console.log(this.barChartData, this.barShow, res.data, 66666)
    },
    forBarChart() {
      const getBarChartDataTrue = () => {
        this.barChartDataTrue = this.barChartData.slice(this.time, this.time + 10)
        this.time += 1
        if (this.time + 10 > this.barChartData.length) {
          this.time = 0
        }
      }
      this.timer1 = setInterval(getBarChartDataTrue, 2000)
    },
    async queryTAgmachType() {
      let res = await queryTAgmachType({
        farmingSeason: this.curSeason,
        year: this.headName2,
        areaId: this.areaId,
        areaFlag: this.areaFlag,
      })
      this.barChartData_tr = []
      if (res.data.length > 0) {
        this.barChartData_tr = [['product', '']].concat(res.data.map(item => [item.type, item.num]))
      }
    },
    async queryTAgmachWorkInfo() {
      let res = await queryTAgmachWorkInfo({
        farmingSeason: this.curSeason,
        year: this.headName2,
        areaId: this.areaId,
        areaFlag: this.areaFlag,
      })
      if (res.data.length > 0) {
        this.pieData1 = [['product', '人口信息']].concat(res.data.map(item => [item.type, Number(item.percent.split('%')[0])]))
      } else {
        this.pieData1 = []
      }
    },
    async queryTAgmachTrend() {
      let res = await queryTAgmachTrend({
        farmingSeason: this.curSeason,
        year: this.headName2,
        areaId: this.areaId,
        areaFlag: this.areaFlag,
      })
      if (res.data.length > 0) {
        let zczData = res.data.reverse();
        this.zczData = [['product', '数量']].concat(zczData.map(item => [item.dt_day, item.agmach_total]))
      } else {
        this.zczData = []
      }
    },
    async getCropType() {
      let params = {
        farmingSeason: this.curSeason,
        year: this.headName2,
        areaId: this.areaId,
        areaFlag: this.areaFlag,
      }
      let res = await getCropType(params)
      if (res.data.length > 0) {
        let data = res.data[0]
        this.qyeqData = [['product', '人口信息']]
        this.qyeqData.push(['玉米', data.corn_ym])
        this.qyeqData.push(['小麦', data.wheat_xm])
        this.qyeqData.push(['冬小麦', data.wheat_dxm])
        this.qyeqData.push(['马铃薯', data.potato_mls])
        this.qyeqData.push(['大豆', data.soybean_dd])
        this.qyeqData.push(['甘蔗', data.sugarcane_gz])
        this.qyeqData.push(['棉花', data.cotton_mh])
        this.qyeqData.push(['花生', data.peanut_hs])
        this.qyeqData.push(['油菜', data.rape_yc])
        this.qyeqData.push(['冬油菜', data.rape_dyc])
        this.qyeqData.push(['早稻', data.rice_zd])
        this.qyeqData.push(['中稻', data.rice_zhd])
        this.qyeqData.push(['晚稻', data.rice_wd])
      } else {
        this.qyeqData = []
      }
    },
    async geojson() {
      let res = await geojson({
        areaId: '411525'
      })
      console.log(res)
      this.$refs.leafletMap.serefresh(res.data)
    },
    async getOverview() {
      let params = {
        farmingSeason: this.curSeason,
        year: this.headName2,
        areaId: this.areaId,
        areaFlag: this.areaFlag,
      }
      let res = await getOverview(params)
      if (res.data.length > 0) {
        let data = res.data[0]
        if(data.mechanization_percent&&data.mechanization_percent!='null'){
          this.liquidData = data.mechanization_percent
        }
        this.ybssData[0].num = Number(data.work_total.toFixed(1))
        this.ybssData[1].num = Number(data.mechanization_total.toFixed(1))
      } else {
        this.liquidData = 0
        this.ybssData[0].num = 0
        this.ybssData[1].num = 0
      }
    },
    async getAreaDay() {
      let params = {
        farmingSeason: this.curSeason,
        year: this.headName2,
        type: this.njType,
        areaId: this.areaId,
        areaFlag: this.areaFlag,
      }
      let res = await getAreaDay(params)
      if (res.data.length > 0) {
        let czsrData = res.data.reverse();
        this.czsrData = [['product', '系列名']].concat(czsrData.map(item => {
          return [item.dt_day, Number(item.data).toFixed(2)]
        }))
      } else {
        this.czsrData = []
      }
    },
    async getNjProcess() {
      let res = await getNjProcess()
    },
    handleHeadDrop(val) {
      this.headName = val
      let data = this.headList.find(item => { return item.name === this.headName })
      this.curSeason = data.type
      this.init()
    },
    handleHeadDrop2(val) {
      console.log(val)
      this.headName2 = val
      this.init()
    },
    wgsjYearMethod(item) {
      this.gdCommunityName = item
      let data = this.trendList1.find(item => { return item.name === this.gdCommunityName })
      this.njType1 = data.type
      this.getWorkProgress()
    },
    handleTrendDrop(item) {
      this.trendName = item
      let data = this.trendList1.find(item => { return item.name === this.trendName })
      this.njType = data.type
      this.getAreaDay()
    }
  },
};
</script>
<style lang="less" scoped>
@font-face {
  font-family: QuartzRegular;
  src: url(~@/assets/leader/font/QuartzRegular.ttf);
}

@keyframes rotateS {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes rotateN {
  0% {
    transform: rotate(360deg);
  }

  100% {
    transform: rotate(0);
  }
}

@keyframes rotateY {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotateY(360deg);
  }
}

.map_box {
  position: absolute;
  width: 1920px;
  height: 1080px;
  top: 0px;
  left: 0;
  z-index: 999;
  // border: 1px solid red;
}

.leader_sjts {
  width: 1920px;
  height: 1080px;
  position: absolute;
  top: 0;
  display: flex;
  justify-content: space-between;

  .leader_city_box {
    width: 450px;

    .leader_zt_contain {
      height: 100%;
    }
  }

  .left_bg {
    width: 460px;
    height: 1033px;
    background: url(~@/assets/leader/img/component/left_bg.png) no-repeat center / 100% 100%;
    position: absolute;
    left: 50px;
    top: 25px;
    z-index: 102;
  }

  .right_bg {
    width: 520px;
    height: 1033px;
    background: url(~@/assets/leader/img/component/right_bg.png) no-repeat center / 100% 100%;
    position: absolute;
    right: 50px;
    top: 25px;
    z-index: 102;
  }

  .select_head {
    position: absolute;
    left: 600px;
    top: 201px;
    width: 100px;
    height: 30px;
    line-height: 30px;
    font-size: 16px;
    z-index: 1001;
    font-family: PingFangSC, PingFang SC;
    color: #caecff;
    text-shadow: 0px 0px 1px #00132e;

    ::v-deep .el-dropdown button {
      width: 180px;
      height: 43px;
      background: url(~@/assets/mskx/bg_select.png) no-repeat center / 100% 100%;
      // background: transparent;
      border: none;
      padding: 0 0 0 5px;

      font-family: PingFangSC, PingFang SC;
      font-size: 22px;
      color: #caecff;
      line-height: 22px;
      text-align: center;
      font-style: normal;
    }

    ::v-deep .el-icon-arrow-down:before {
      // content: '\e790' !important;
      color: #caecff;
    }

    ::v-deep .el-popper[x-placement^='bottom'] .popper__arrow:after {
      border-bottom-color: #185493 !important;
    }
  }
  .select_head2 {
    position: absolute;
    left: 600px;
    top: 250px;
    width: 100px;
    height: 30px;
    line-height: 30px;
    font-size: 16px;
    z-index: 1001;
    font-family: PingFangSC, PingFang SC;
    color: #caecff;
    text-shadow: 0px 0px 1px #00132e;

    ::v-deep .el-dropdown button {
      width: 180px;
      height: 43px;
      background: url(~@/assets/mskx/bg_select.png) no-repeat center / 100% 100%;
      // background: transparent;
      border: none;
      padding: 0 0 0 5px;

      font-family: PingFangSC, PingFang SC;
      font-size: 22px;
      color: #caecff;
      line-height: 22px;
      text-align: center;
      font-style: normal;
    }

    ::v-deep .el-icon-arrow-down:before {
      // content: '\e790' !important;
      color: #caecff;
    }

    ::v-deep .el-popper[x-placement^='bottom'] .popper__arrow:after {
      border-bottom-color: #185493 !important;
    }
  }

  .box {
    margin-bottom: 20px;
    .cout {
      width: 100%;
      height: 100%;
    }
  }
  .box1 {
    display: flex;
    justify-content: space-between;

    .box1_left {
      height: 90%;
      width: calc(100% - 210px);
      position: relative;

      .liquidPercent {
        position: absolute;
        top: 40%;
        left: 90px;
        z-index: 10;
        font-size: 22px;
        font-family: PingFangSC, PingFang SC;
        font-weight: normal;
        color: #ffffff;
      }
      .liquidPercent2 {
        position: absolute;
        top: 60%;
        left: 90px;
        z-index: 10;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #ffffff;
        line-height: 20px;
        text-align: center;
        font-style: normal;
      }
    }

    .box1_right {
      width: 210px;
      height: 100%;

      .ybsscont {
        height: 100%;
        padding: 24px 0;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;

        li {
          position: relative;
          width: 204px;
          height: 80px;
          background: url(~@/assets/shzl/ybss_bgnew.png) no-repeat;
          background-size: 100% 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          text-align: left;
          padding-left: 102px;

          .icon {
            position: absolute;
            width: 26px;
            height: 26px;
            top: 10px;
            left: 36px;
            animation: move infinite 3s ease-in-out;

            @keyframes move {
              0% {
                transform: translateY(0px) translateX(0px) rotateY(0);
              }

              50% {
                transform: translateY(-10px) translateX(-5px) rotateY(180deg);
              }

              100% {
                transform: translateY(0px) translateX(0px) rotateY(360deg);
              }
            }
          }

          &:nth-child(2) {
            span {
              animation-delay: 0.3s;
            }
          }

          &:nth-child(3) {
            span {
              animation-delay: 0.6s;
            }
          }

          &:nth-child(4) {
            span {
              animation-delay: 0.9s;
            }
          }

          &:nth-child(5) {
            span {
              animation-delay: 1.2s;
            }
          }

          &:nth-child(6) {
            span {
              animation-delay: 1.5s;
            }
          }

          .tit {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 20px;
            white-space: nowrap;
          }

          .num {
            font-size: 18px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 22px;
            .unit{
              font-size: 12px;
              display: block;
            }
          }
        }
      }
    }
  }
  .box3 {
    position: relative;
    .njfbDw {
      position: absolute;
      left: 30px;
      top: 20px;
      color: #ffffff;
      font-size: 14px;
      font-family: 'PingFangSC';
      font-weight: 300;
    }
    .cout {
      width: 100%;
      height: 100%;
    }
  }
  .box6 {
    .cout {
      width: 100%;
      height: 100%;
    }
  }
  .box8 {
    .cout {
      width: 100%;
      height: 100%;
    }
  }

  .left {
    width: 450px;
    display: flex;
    justify-content: space-between;
    margin-top: 87px;
    margin-left: 32px;
    position: relative;
    left: 0;
    opacity: 1;
    z-index: 1003;
    -webkit-transition: all .5s ease-in;
    -moz-transition: all .5s ease-in;
    transition: all .5s ease-in;
  }
  .mkdxLeft {
    left: -450px;
    opacity: 0;
  }

  .right {
    width: 450px;
    display: flex;
    justify-content: space-between;
    margin-top: 87px;
    margin-right: 32px;
    position: relative;
    right: 0;
    opacity: 1;
    z-index: 1003;
    -webkit-transition: all .5s ease-in;
    -moz-transition: all .5s ease-in;
    transition: all .5s ease-in;
  }

  .mkdxRight {
    right: -450px;
    opacity: 0;
  }
  .zwsjImg {
    height: 80%;
    margin: 0 auto;
    margin-top: 36px;
  }
}

.mkss {
  width: 44px;
  height: 44px;
  position: absolute;
  bottom: 735px;
  right: 540px;
  z-index: 999;
  background: url(~@/assets/map/ss2.png) no-repeat center / 100% 100%;

  .mkss2 {
    font-size: 16px;
    color: #fff;
    opacity: 0;
  }
}

.mkss1 {
  width: 44px;
  height: 44px;
  position: absolute;
  bottom: 735px;
  right: 540px;
  z-index: 999;
  background: url(~@/assets/map/ss1.png) no-repeat center / 100% 100%;

  .mkss2 {
    font-size: 16px;
    color: #fff;
    opacity: 0;
  }
}
.sxBox {
  width: 44px;
  height: 44px;
  position: absolute;
  bottom: 685px;
  right: 539px;
  z-index: 1099;
  img {
    width: 44px;
    height: 44px;
  }
}
.wgsj_fx {
  width: 100%;

  // height: 100%;
  .select_s {
    position: absolute;
    right: 0;
    top: -40px;
    line-height: 30px;
    font-size: 16px;
    font-family: PingFangSC, PingFang SC;
    color: #caecff;
    text-shadow: 0px 0px 1px #00132e;

    ::v-deep .el-dropdown button {
      font-family: PingFangSC, PingFang SC;
      font-size: 16px;
      color: #caecff;
      line-height: 21px;
      text-align: center;
      font-style: normal;

      // width: 100px;
      height: 30px;
      // background: url(~@/assets/mskx/icon_drop.png) no-repeat center / 100% 100%;
      background: transparent;
      border: none;
      padding: 0 0 0 5px;
    }

    ::v-deep .el-icon-arrow-down:before {
      // content: '\e790' !important;
      color: #caecff;
    }

    ::v-deep .el-popper[x-placement^='bottom'] .popper__arrow:after {
      display: none;
    }

    ::v-deep .el-popper .popper__arrow:after {
      border-bottom-color: #185493 !important;
    }
  }

  .select_r1 {
    position: absolute;
    right: 0;
    top: -32px;
    line-height: 30px;
    font-size: 16px;
    font-family: PingFangSC, PingFang SC;
    color: #caecff;
    text-shadow: 0px 0px 1px #00132e;

    ::v-deep .el-dropdown button {
      // width: 100px;
      height: 30px;
      // background: url(~@/assets/mskx/bg_select.png) no-repeat center / 100% 100%;
      background: transparent;
      border: none;
      padding: 0 0 0 5px;

      font-family: PingFangSC, PingFang SC;
      font-size: 16px;
      color: #caecff;
      line-height: 21px;
      text-align: center;
      font-style: normal;
    }

    ::v-deep .el-icon-arrow-down:before {
      // content: '\e790' !important;
      color: #caecff;
    }

    ::v-deep .el-popper[x-placement^='bottom'] .popper__arrow:after {
      border-bottom-color: #185493 !important;
    }
  }
}
</style>