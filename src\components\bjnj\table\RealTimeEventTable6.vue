<template>
	<div
		class="table-wrapper"
		:style="{
			height: `calc(${contentHeight})`,
		}"
	>
		<div class="table-header" v-if="showHeader">
			<span v-for="(it, i) in widths" :key="i" :style="{ width: widths[i], padding: headerSpanPadding }">
				<div class="row-item" :style="{ padding: headerSpanPadding }">{{ titles[i] }}</div></span
			>
		</div>

		<div class="table-content" :style="{ height: `calc(${contentHeight} - 46px)` }">
			<swiper class="swiper" :options="swiperOption" ref="myBotSwiper">
				<swiper-slide v-for="(it, i) in dataList" :key="i">
					<div class="table-row" :class="{ stripe: i % 2 === 1, active: i === currentActiveRow }" @click="handleRowClick(it, i)">
						<div
							class="col"
							v-for="(ite, ind) in widths"
							:key="ind"
							:title="titles[ind] == '操作' ? '' : it[ind]"
							:style="{
								width: widths[ind],
								padding: spanPadding,
								fontSize: rowFontSize,
							}"
						>
							<template v-if="isNeedOperate">
								<div class="item_span" v-if="ind !== widths.length - 1 && ind !== 3">
									{{ it[ind] }}
								</div>
								<div class="item_span3" v-else-if="ind == 3">{{ it[ind] }}</div>
								<div class="item_spanLast" v-else>
									<div class="detail" @click="operate(0, it)">查看</div>
									<!-- <div class="detail" @click="operate(0, it)" v-if="it[widths.length - 2] == '待处置'">处置</div> -->
								</div>
							</template>
							<template v-else>
								<div class="item_span" :class="{ active: it[ind] === '在线' }">
									{{ it[ind] }}
								</div>
							</template>
						</div>
					</div>
				</swiper-slide>
			</swiper>
		</div>
	</div>
</template>

<script>
export default {
	name: 'RealTimeEventTable',
	props: {
		data: {
			type: Array,
			default: () => [['杂草乱扔', '2020.08.23 12:26:15', '全要素网格化', '公安交警', '环境保护', '待受理', '李念省']],
		},
		titles: {
			type: Array,
			default: () => ['事件标题', '创建时间', '信息来源', '分类', '状态', '上报人'],
		},
		widths: {
			type: Array,
			default: () => ['246px', '348px', '224px', '214px', '214px', '214px'],
		},
		contentHeight: {
			type: String,
			default: '220px',
		},
		showHeader: {
			type: Boolean,
			default: true,
		},
		spanPadding: {
			type: String,
			default: '0 10px',
		},
		headerSpanPadding: {
			type: String,
			default: '0 15px',
		},
		headerHeight: {
			type: String,
			default: '44px',
		},
		rowFontSize: {
			type: String,
			default: '14px',
		},
		settled: {
			type: Boolean,
			default: true,
		},
		isNeedOperate: {
			type: Boolean,
			default: true,
		},
	},
	computed: {
		// 将手机号的中间四位隐藏
		dataList() {
			return this.data.map((it) => {
				// let [name, mobile, time, totals, bfb, qwer] = it;
				// const reg = /^(\d{3})\d{4}(\d{4})$/;
				// mobile = mobile.replace(reg, '$1****$2');

				// 不需要隐藏手机号码就隐去上面的代码
				let qwer = it
				return qwer
			})
		},
		myBotSwiper() {
			return this.$refs.myBotSwiper.$swiper
		},
	},
	data() {
		return {
			currentActiveRow: 999,
			swiperOption: {
				autoHeight: true,
				direction: 'vertical',
				spaceBetween: 0,
				autoplay: {
					delay: 2500,
					disableOnInteraction: false,
					autoplayDisableOnInteraction: false,
				},
				slidesPerView: 'auto',
				grabCursor: true,
				autoplayDisableOnInteraction: false,
				mousewheelControl: true,
			},
		}
	},
	methods: {
		operate(i, it) {
			this.$emit('operate', i, it)
		},
		handleRowClick(it, i) {
			if (this.currentActiveRow == i) {
				this.currentActiveRow = 999
				this.swiperStart()
			} else {
				this.currentActiveRow = i
				this.swiperStop()
			}
			const currentIt = this.data[i]
			// this.swiperStop();
			this.$emit('change', currentIt)
		},
		swiperStop() {
			this.myBotSwiper.autoplay.stop()
		},
		swiperStart() {
			this.myBotSwiper.autoplay.start()
		},
		dealMethod() {
			console.log(111)
			this.$parent.dealMethod()
		},
		dwMethod() {
			console.log(222)
			this.$parent.dwMethod()
		},
	},
	watch: {
		data() {
			this.currentActiveRow = 0
		},
	},
}
</script>

<style lang="scss" scoped>
.table-wrapper {
	width: 100%;
	height: 497px;

	span {
		box-sizing: border-box;
	}

	.table-header {
		width: 100%;
		height: 46px;
		display: flex;
		background: rgba(0, 0, 0, 0);
		border: 1px solid #2968c7;

		span {
			padding: 0px !important;
			font-size: 14px;
			font-family: PingFangSC, PingFang SC;
			line-height: 46px;
			font-weight: 400;
			color: #37c1ff;
			line-height: 20px;
			border-right: 1px solid #2968c7;
			&:last-child {
				border-right: none;
			}

			.row-item {
				position: absolute;
				padding: 0 15px;
				padding: 0px !important;
				top: 50%;
				transform: translateY(-50%);
				overflow: hidden;
				text-overflow: ellipsis; /* 文本溢出时显示省略号来代表被修剪的文本 */
				white-space: normal;
				display: -webkit-box; /* 必须结合的属性 ，将对象作为弹性伸缩盒子模型显示 */
				-webkit-box-orient: vertical; /* 必须结合的属性 ，设置或检索伸缩盒对象的子元素的排列方式 */
				-webkit-line-clamp: 2; /* 文本需要显示多少行 */
				word-break: break-all;
			}

			&:nth-child(2) {
				text-align: center;
			}
		}

		span + span {
			margin-left: 2px;
		}
	}

	.table-content {
		width: 100%;
		height: 284px;

		.swiper {
			width: 100%;
			height: 100%;
			overflow-y: auto;

			&::-webkit-scrollbar {
				width: 4px; /* 滚动条宽度 */
				height: 4px;
			}

			//轨道
			&::-webkit-scrollbar-track {
				background: #04244e; /* 滚动条轨道背景 */
				border-radius: 10px;
				padding: 5px 0;
			}

			//滑块
			&::-webkit-scrollbar-thumb {
				background: #82aed0; /* 滑块背景颜色 */
				border-radius: 10px;
				height: 15px;
			}

			//悬停滑块颜色
			&::-webkit-scrollbar-thumb:hover {
				background: #ff823b; /* 鼠标悬停时滑块颜色 */
			}

			//箭头
			&::-webkit-scrollbar-button {
				display: none; /* 隐藏滚动条的箭头按钮 */
			}

			.table-row {
				width: 100%;
				height: 58px;
				line-height: 58px;
				cursor: pointer;
				display: flex;

				&.active {
					.col {
						color: rgba(131, 194, 238, 1);
						font-size: 14px;
						font-weight: bold;
					}
				}

				&.stripe {
					background: url('~@/assets/map/dialog/bg3.png') no-repeat center / 100% 100%;
				}

				.col {
					font-size: 14px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: rgba(255, 255, 255, 0.85);
					display: grid;
					place-items: center;
					box-sizing: border-box;
				}

				.swiper-item {
					width: 100%;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
			}
		}
	}

	span {
		height: 100%;
		// line-height: 100%;
		// display: inline-block;
		text-align: center;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		padding: 0 15px;
		display: flex;
		justify-content: center;
		position: relative;

		.row-point {
			display: flex;
			align-items: center;
			justify-content: center;

			.red-point {
				color: #08bf8a;
			}

			.green-point {
				color: #ff9090;
			}
		}

		.row-item {
			position: absolute;
			padding: 0 15px;
			top: 50%;
			transform: translateY(-50%);
			overflow: hidden;
			text-overflow: ellipsis; /* 文本溢出时显示省略号来代表被修剪的文本 */
			white-space: normal;
			display: -webkit-box; /* 必须结合的属性 ，将对象作为弹性伸缩盒子模型显示 */
			-webkit-box-orient: vertical; /* 必须结合的属性 ，设置或检索伸缩盒对象的子元素的排列方式 */
			-webkit-line-clamp: 2; /* 文本需要显示多少行 */
			word-break: break-all;
		}
	}
	.icon_ {
		display: flex;
		margin: 0 0 0 12px;
		display: flex;
		align-items: center;
		img {
			width: 12px;
			height: 16px;
			margin-left: 2px;
		}
	}
}
.item_span {
	width: 100%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	&.active {
		color: #29d241;
	}
}
.item_span3 {
	line-height: 18px;
	text-align: left;
	overflow: hidden;
	text-overflow: ellipsis; /* 文本溢出时显示省略号来代表被修剪的文本 */
	white-space: normal;
	display: -webkit-box; /* 必须结合的属性 ，将对象作为弹性伸缩盒子模型显示 */
	-webkit-box-orient: vertical; /* 必须结合的属性 ，设置或检索伸缩盒对象的子元素的排列方式 */
	-webkit-line-clamp: 2; /* 文本需要显示多少行 */
	word-break: break-all;
}
.item_spanLast {
	display: flex;
	align-items: center;
	gap: 20px;
	.detail {
		font-size: 14px;
		font-family: PingFangSC, PingFang SC;
		font-weight: 500;
		color: #37c1ff !important;
	}
	.sign_for {
		font-size: 14px;
		font-family: PingFangSC, PingFang SC;
		font-weight: 500;
		color: #82e16a;
	}
}
</style>
