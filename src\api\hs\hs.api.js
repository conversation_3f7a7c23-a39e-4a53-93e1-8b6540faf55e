/*
 * @Author: fanjialong <EMAIL>
 * @Date: 2023-04-17 15:06:09
 * @LastEditors: fanjialong 
 * @LastEditTime: 2023-09-12 19:30:57
 * @FilePath: \hs_dp\src\api\hs\hs.api.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import http from '@/utils/leader/request'
import { finaUrl, cameraMarkerUrl, cameraXqUrl, droneUrl, rhtxUrl,xtyyUrl, bjnjUrl } from '@/utils/leader/const'

// 获取事件信息
export const getEventInfo = () => {
  return http.get(finaUrl + '/api/app/orderInfo/getOrderLocation')
}

// 获取事件详情
export const getEventDetail = id => {
  return http.get(finaUrl + `/api/app/orderInfo/getOrderLocationDetail?id=${id}`)
}

// 获取事件类型
export const getEventType = () => {
  return http.get(finaUrl + `/api/eventManage/tbEventCategoryItems/getEventCategoryItemTree`)
}

// 获取事件来源
export const getEventResource = () => {
  return http.get(finaUrl + `/api/dic/cscpHxDicItems/getCscpBasicHxItemCode?dicCode=sjSource`)
}

// 获取负责人列表
export const getResponsiblePerson = () => {
  return http.get(finaUrl + `/api/app/orderInfo/getDealPerson`)
}

// 任务派发
export const createOrder = data => {
  return http.post(finaUrl + `/api/app/orderInfo/createOrder?userId=${data.userId}`, data)
}

// 获取未办结事件列表
export const getUnDoneEventList = () => {
  return http.get(finaUrl + `/api/app/orderInfo/getUnDone`)
}

// 获取已办结事件列表
export const getDoneEventList = () => {
  return http.get(finaUrl + `/api/app/orderInfo/getIsDone`)
}

// 周边资源-人员列表
export const getSurroundingResourcesPersonList = () => {
  return http.get(finaUrl + `/api/app/system/queryAllUserPhone`)
}

// 获取视频监控点位
export const getCameraMakers = id => {
  return http.get(cameraMarkerUrl + `/api/app/camera/getCamera`)
}

// 获取视频监控点位 雪亮
export const getCameraMakersNew = id => {
  return http.get(xtyyUrl + `/api/tbCameras/queryAll`)
}

// 获取视频监控点位 雪亮
export const getRightCamera = id => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryByModule?module=视频监控`)
}

// 获取视频监控详情
export const getCameraXq = id => {
  return http.get(
    cameraXqUrl + `/jnyth/hx/commandDispatch/cameraInfo/monitor/getMonitorStream?ids=${id}`
  )
}

// 烟杆点位
export const getSmokeDeviceMakers = () => {
  return http.get(
   cameraXqUrl +'/jnyth/hx/qlBigScreen/querySmokeDevice?street=湖熟街道'
  )
}

// // 获取无人机token
// export const getDroneToken = () => {
//   return http.post(droneUrl + `/auth/ticketLogin`, {
//     ticket: 'mlwuOq4PM/5qXE51ijU0yYkqyNhcA230TKDmD5+j+ME='
//   })
// }

// // 获取无人机列表
// export const getDroneList = token => {
//   return http.get(
//     droneUrl + `/ctuav/sqdx/uav/api/onlineuav?deptId=238`,
//     {},
//     {
//       Authorization: `Bearer ${token}`
//     }
//   )
// }


// 获取无人机token
export const getDroneToken = () => {
  return http.post(cameraMarkerUrl + `/api/app/camera/getToken`)
}

// 获取无人机列表
export const getDroneList = token => {
  return http.get(
    cameraMarkerUrl + `/api/app/camera/getOnlineWRJ?deptId=238` + '&token=' + token,
  )
}

// 获取人员轨迹
export const getPersonnelTrack = token => {
  return http.get(rhtxUrl + `/dispatch/api?cmd=guiji&user_id=55e5a7925e3d42cb993c7804e379e6e0`)
}

// 关闭会议
export const closeMeet = meetnum => {
  return http.get(rhtxUrl + `/dispatch/api?cmd=meet_off&meetnum=${meetnum}`)
}


// 区工单事件信息全量
export const getQgdList = (data) => {
  return http.get(xtyyUrl + `/api/tOrder/getOrderLocation`, data)
}

// 区工单事件信息 实时事件
export const getQgdSssj = (data) => {
  return http.get(xtyyUrl + `/api/tOrder/queryIsDoneByPage`, data)
}

// 区工单事件信息 未办结工单统计
export const getQgdWbjTj = (data) => {
  return http.get(xtyyUrl + `/api/tOrder/getEmeOrderCount`, data)
}


// 区工单事件信息详情
export const getQgdDetailJk = (data) => {
  return http.get(xtyyUrl + `/api/tOrderProcess/queryByOrderId`, data)
}

// 区工单数据字典 紧急程度
export const getQgdCodejjcd = () => {
  return http.get(xtyyUrl + `/api/tOrder/getDicLabelByValue?dicType=eme_level`)
}

// 区工单数据字典 是否公开
export const getQgdCodesfgk = () => {
  return http.get(xtyyUrl + `/api/tOrder/getDicLabelByValue?dicType=show_flag`)
}

// 区工单数据字典 诉求类型
export const getQgdCodesqlx = () => {
  return http.get(xtyyUrl + `/api/tOrder/getDicLabelByValue?dicType=order_appeal_type`)
}

// 区工单数据字典 事件来源
export const getQgdCodesjly = () => {
  return http.get(xtyyUrl + `/api/tOrder/getDicLabelByValue?dicType=form_origin`)
}

// 融合通信账号密码
export const rhtxLogin = data => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryByModule?module=` + '融合通信',
    data)
}

// 根据社区统计工单
export const getOrderVo = (data) => {
  return http.get(xtyyUrl + `/api/tOrder/getOrderVo`, data)
}

// 工单类型统计
export const getTypeOrderCount = (data) => {
  return http.get(xtyyUrl + `/api/tOrder/getTypeOrderCount`, data)
}

// 工单数量统计
export const getEmeOrderCount = (data) => {
  return http.get(xtyyUrl + `/api/tOrder/getEmeOrderCount`, data)
}

// 工单数量统计
export const getOrderCountByDate = (data) => {
  return http.get(xtyyUrl + `/api/tOrder/getOrderCountByDate`, data)
}

// 跳转链接
export const jumpJk = () => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryByModule?tag=0`)
}

// 跳转链接
export const getJumpUrl = (data) => {
  return http.get(xtyyUrl + `/api/tPlatModel/getSingleLoginUrl`, data)
}

// 根据主键id查询农机详情
export function queryTAgmachInfoByTableId(id) {
  return http.get(bjnjUrl + `/api/tAgmachInfos/queryTAgmachInfoByTableId/${id}`)
}

// 农机最新工况
export function getAgmachWorkConditionNew(agmachId) {
  return http.get(bjnjUrl + `/api/screen/getAgmachWorkConditionNew?agmachId=${agmachId}`)
}

// 农机实时位置信息
export function getAgmachlocNew(agmachId) {
  return http.get(bjnjUrl + `/api/screen/getAgmachlocNew?agmachId=${agmachId}`)
}

// 农机历史作业信息
export function getAgmachWorkHistory(data) {
  return http.get(bjnjUrl + `/api/screen/getAgmachWorkHistory`, data)
}