<template>
	<div class="child-block">
		<div class="content" v-if="contentDataList.length > 0">
			<div class="data-item" v-for="(data, index) in contentDataList" :key="index">
				<div class="data-child-item" v-for="(item, index_) in data" :key="index_">
					<div class="number-box">
						<el-tooltip effect="dark" popper-class="tooltip-item" :content="item.number" placement="top">
							<div class="number">{{ `${item.number}` }}</div>
						</el-tooltip>

						<!-- <div class="unit">{{ item.unit }}</div> -->
					</div>
					<el-tooltip effect="dark" popper-class="tooltip-item" :content="item.title" placement="top">
						<div class="title-box">{{ `${item.title}(${item.unit})` }}</div>
					</el-tooltip>
				</div>
			</div>
		</div>
		<div v-else class="alert-empty content">
			暂无数据
		</div>
	</div>
</template>

<script>
export default {
	props: {
		contentDataList: {
			type: Array,
			default: () => [],
		},

		blockHeight: {
			type: Number,
			default: 683,
		},
	},
	data() {
		return {}
	},
	computed: {},
	methods: {},

	watch: {},
}
</script>

<style lang="less" scoped>
.child-block {
	width: 446px;
	height: 100%;
	box-sizing: border-box;
	z-index: 101;
	position: relative;
	padding: 0 5px;
	padding-top: 20px;
	.content {
		position: relative;
		width: 100%;
		// height: calc(100% - 74px);
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;

		&.no-active-content {
			height: 0;
			display: none;
		}

		.data-item {
			width: 100%;
			height: 72px;

			// height: 40px;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-bottom: 24px;
			cursor: pointer;

			background: url(~@/assets/img/block-box/nyfwcy-item-child-bg.png);
			/* 背景不重复 */
			background-repeat: no-repeat;
			/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
			background-size: cover;
			/* 背景居中显示（可选） */
			background-position: center;

			.data-child-item {
				width: 33%;
				display: flex;
				flex-direction: column;
				justify-content: flex-start;
				align-items: center;
				margin: 0 10px;
				.number-box {
					display: flex;
					justify-content: center;
					align-items: flex-end;
					margin-bottom: 4px;
					.number {
						max-width: 130px;
						height: 21px;
						font-family: DINPro, DINPro;
						font-weight: bold;
						font-size: 20px;
						color: #4de4ff;
						line-height: 20px;
						text-align: left;
						font-style: normal;
						text-transform: none;

						white-space: nowrap; /* 强制文本不换行 */
						overflow: hidden; /* 超出部分隐藏 */
						text-overflow: ellipsis; /* 超出时显示省略号 */
					}

					.unit {
						font-family: DINAlternate, DINAlternate;
						font-size: 14px;
						color: #4de4ff;
						letter-spacing: 1px;
						line-height: 14px;
						text-align: left;
						font-style: normal;
					}
				}

				.title-box {
					width: 130px;
					height: 19px;

					font-family: Microsoft YaHei UI, Microsoft YaHei UI;
					font-weight: 400;
					font-size: 14px;
					color: #b0e0ff;
					text-align: center;
					font-style: normal;
					text-transform: none;

					white-space: nowrap; /* 强制文本不换行 */
					// overflow: hidden; /* 超出部分隐藏 */
					// text-overflow: ellipsis; /* 超出时显示省略号 */
				}
			}

			&:nth-child(1) {
				.data-child-item:nth-child(1) {
					.number,
					.unit {
						color: #b9e8ff;
					}
				}
				.data-child-item:nth-child(2) {
					.number,
					.unit {
						color: #4de4ff;
					}
				}
				.data-child-item:nth-child(3) {
					.number,
					.unit {
						color: #ff823b;
					}
				}
			}
			&:nth-child(2) {
				.data-child-item:nth-child(1) {
					.number,
					.unit {
						color: #f5e74f;
					}
				}
				.data-child-item:nth-child(2) {
					.number,
					.unit {
						color: #14cc8f;
					}
				}
				.data-child-item:nth-child(3) {
					.number,
					.unit {
						color: #ffffff;
					}
				}
			}
		}

		.data-item:hover {
			// background: url(~@/assets/img/block-box/block-box-child-item-active-bg.png);
			/* 背景不重复 */
			background-repeat: no-repeat;
			/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
			background-size: cover;
			/* 背景居中显示（可选） */
			background-position: center;
		}
	}

	.alert-empty {
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		font-family: PangMenZhengDao-3, PangMenZhengDao-3;
		font-weight: 500;
		font-size: 20px;
		color: #dbf1ff !important;
		text-shadow: 0px 0px 10px #bddcf1 !important;
		text-align: center;
		font-style: normal;
		text-transform: none;
	}

	.empty-box {
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		.zwsjImg {
			width: 240px;
			margin: 20px 0;
		}
	}
}

.popDropDown {
	::v-deep .el-dropdown {
		color: #caecff;
		font-family: YouSheBiaoTiHei;
		font-size: 16px;
		line-height: 21px;
		text-align: right;
		font-style: normal;
		line-height: 40px;
		right: 0;
		position: absolute;
	}
	::v-deep .el-dropdown-menu {
		background-color: #00173b !important;
		border: 1px solid #2b7bbb !important;
	}
	.el-dropdown-menu__item {
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
		line-height: 40px;
		padding: 0 30px;
		&:hover {
			background: #185493;
			color: rgba(255, 255, 255, 0.8);
		}
	}
}
</style>
