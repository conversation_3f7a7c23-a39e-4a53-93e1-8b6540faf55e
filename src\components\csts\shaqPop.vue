<template>
  <div class="ai_waring">
    <div class="title">
      <span>社会安全更多</span>
    </div>
    <img @click="closeEmit" class="close_btn" src="@/assets/shzl/map/close_btn2.png" alt="" />
    <div class="content">
      <div class="left">
        <BlockBox
          title="警情总览（日维度）"
          class="box box1"
          :isListBtns="false"
          :blockHeight="342"
        >
          <div class="contentBox">
            <div class="contentleft">
              <div class="contentleft1">
                <img src="@/assets/csts/jqzlImg1.png" alt="">
                <div class="contentJqzl">
                  <div class="contentName">114移车服务</div>
                  <div class="contentNum">88.46<span>起</span></div>
                </div>
              </div>
              <div class="contentleft2">
                <div class="contentleft3">
                  <div class="contentName">12345</div>
                  <div class="contentNum">31.54<span>起</span></div>
                </div>
                <div class="contentleft4">
                  <div class="contentName">110</div>
                  <div class="contentNum">51.92<span>起</span></div>
                </div>
              </div>
            </div>
            <div class="contentright">
              <img src="@/assets/csts/jqzlImg2.png" alt="">
              <div class="contentName">总报警</div>
              <div class="contentNum">1342<span>起</span></div>
            </div>
          </div>
        </BlockBox>
        <BlockBox
          title="接警数分类统计"
          class="box box2"
          :isListBtns="false"
          :blockHeight="380"
        >
          <pieChart></pieChart>
        </BlockBox>
      </div>
      <div class="middle">
        <BlockBox
          title="24h流量变化数据"
          class="box box3"
          :isListBtns="false"
          :blockHeight="342"
        >
          <div class="box3Content1">高速路口流量监测点</div>
          <div class="box3Content2">
            <div class="box3Wz">位置：<span>南京高速收费口</span></div>
            <div class="box3Ll">当前车流量：<span>48辆/分钟</span></div>
          </div>
          <div class="box3Content3">
            <TrendLineChart
              :options="xczfData.options"
              :data="xczfData.data"
              :init-option="{
                yAxis: {
                  name: '',
                },
              }"
            />
          </div>
        </BlockBox>
        <BlockBox
          title="各地区案件统计"
          class="box box4"
          :isListBtns="false"
          :blockHeight="380"
        >
          <div class="box4Content1">
            <div class="box4Name1">区名：鼓楼区</div>
            <div class="box4Name2">最近一月案件数：3000件</div>
          </div>
          <div class="box4Content2">
            <GroupColChart :data="qyeqData2" :options="qyeqOptions2"></GroupColChart>
          </div>
        </BlockBox>
      </div>
      <div class="right">
        <BlockBox
          title="全量访评机制"
          class="box box5"
          :isListBtns="false"
          :blockHeight="342"
        >
          <div class="box5Content1">
            <HorBarChart></HorBarChart>
          </div>
          <div class="box5Content2">
            <disc-circle :data="data" :options="options" />
          </div>
        </BlockBox>
        <BlockBox
          title="专项打击（月维度）"
          class="box box6"
          :isListBtns="false"
          :blockHeight="380"
        >
          <div class="box6Content">
            <div class="box6List" v-for="(item,index) in zxdjList" :key="index">
              <div class="box6Num">{{ item.num }}</div>
              <div class="box6Name">{{ item.name }}</div>
            </div>
          </div>
        </BlockBox>
      </div>
    </div>
  </div>
</template>

<script>
import BlockBox from '@/components/leader/common/blockBox2.vue'
import SwiperTable from '@/components/leader/leader_zwjs/SwiperTable.vue'
import pieChart from '@/components/csts/pieChart.vue'
import HorBarChart from '@/components/csts/HorBarChart';
export default {
  name:'shaqPop',
  data() {
    return {
      xczfData: {
        data: [
          ['product', ''],
          ['0', 20],
          ['2', 30],
          ['4', 20],
          ['6', 60],
          ['8', 40],
          ['10', 50],
        ],
        options: {
          smooth: true,
          colors: ['rgba(0, 155, 255, 1)'],
          isToolTipInterval: true,
          toolTipIntervalTime: 5000,
        },
      },
      qyeqData2: [
        ['product', ''],
        ['120急救', 200],
        ['开锁服务', 140],
        ['城管', 102],
        ['市政园林', 200],
        ['交通', 150],
        ['电梯救援', 170]
      ],
      qyeqOptions2: {
        color: ['#00A3D7'],
        stack: true,
        isGradient: true,
        barMaxWidth: 10,
        gradientColors: [
          ['#00A3D7', '#0080ED'],
        ],
        isHor: false,
        barGap: '30%',
        tooltip: {
          color: '#fff',
          show: true,
          type: 'shadow'
        }
      },
      data: {
        name: '社会治安满意度',
        value: 40
      },
      options: {
        size: 200
      },
      zxdjList: [
        {
          num: '123132',
          name: '下发处置指令',
        },
        {
          num: '2011',
          name: '刑事拘留',
        },
        {
          num: '1972',
          name: '骚扰警情',
        },
        {
          num: '123132',
          name: '治安处罚',
        },
        {
          num: '2011',
          name: '处置骚扰人员',
        },
        {
          num: '1972',
          name: '送院治疗',
        },
      ],
    }
  },
  components: {
    BlockBox,
    SwiperTable,
    pieChart,
    HorBarChart,
  },
  methods: {
    closeEmit() {
      this.$emit('closeEmit')
    },
  },
}
</script>

<style lang='less' scoped>
@keyframes rotateS {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes rotateN {
  0% {
    transform: rotate(360deg);
  }
  100% {
    transform: rotate(0);
  }
}
@keyframes rotateY {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotateY(360deg);
  }
}
.ai_waring {
  width: 1885px;
  height: 883px;
  padding: 22px;
  // background: rgba(0,23,59,0.95);
  background: url(~@/assets/csts/xztk.png) no-repeat;
  background-size: 100% 100%;
  box-shadow: 0px 0px 22px 0px rgba(11,54,138,0.35), 0px 2px 8px 0px rgba(0,0,0,0.7), inset 0px 0px 8px 0px rgba(17,146,214,0.35);
  border-radius: 11px;
  border: 1px solid;
  border-image: linear-gradient(360deg, rgba(57, 133, 255, 0.5), rgba(0, 205, 255, 0)) 1 1;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
  .title {
    background: url(~@/assets/shzl/map/title_bg2.png) no-repeat center / 100% 100%;
    background-size: 100% 100%;
    width: 914px;
    height: 74px;
    margin: 0 auto;
    line-height: 74px;
    text-align: center;
    position: relative;
    span {
      font-size: 40px;
      font-family: YouSheBiaoTiHei;
      color: #FFFFFF;
      line-height: 52px;
      background: linear-gradient(180deg, #FFFFFF 0%, #2AEBFF 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      display: inline-block;
      margin-top: 11px;
    }
  }
  .close_btn {
    position: absolute;
    top: 38px;
    right: 43px;
  }
  .content {
    width: 1841px;
    height: 765px;
    .title {
      width: 492px;
      height: 17px;
      background: url(~@/assets/leader/img/component/title_bg3.png) no-repeat;
      background-size: 100% 100%;
      font-size: 22px;
      font-family: PangMenZhengDao;
      color: #FFBC6C;
      line-height: 25px;
      letter-spacing: 2px;
      text-align: left;
      padding-left: 29px;
    }
    .left {
      width: 533px;
      float: left;
      margin-left: 67px;
      padding-top: 43px;
      .box1 {
        .contentBox {
          height: 292px;
          padding: 32px 0 0 26px;
          text-align: left;
          .contentleft {
            width: 332px;
            height: 221px;
            float: left;
            .contentleft1 {
              width: 332px;
              height: 104px;
              background: url(~@/assets/csts/jqzl1.png) no-repeat;
              background-size: 100% 100%;
              img {
                float: left;
                margin: 26px 0 0 52px;
              }
              .contentJqzl {
                float: left;
                margin-left: 17px;
                .contentName {
                  font-size: 16px;
                  font-family: PingFangSC-Regular, PingFang SC;
                  font-weight: 400;
                  color: #FFFFFF;
                  line-height: 22px;
                  margin-top: 19px;
                }
                .contentNum {
                  font-size: 36px;
                  font-family: DIN-BlackItalic, DIN;
                  font-weight: normal;
                  color: #6FDCFF;
                  line-height: 44px;
                  margin-left: 2px;
                  margin-top: -3px;
                  span {
                    font-size: 14px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #6FDCFF;
                    line-height: 20px;
                    margin-left: 12px;
                  }
                }
              }
            }
            .contentleft2 {
              width: 332px;
              height: 104px;
              margin-top: 13px;
              .contentleft3 {
                width: 162px;
                height: 104px;
                background: url(~@/assets/csts/jqzl2.png) no-repeat;
                background-size: 100% 100%;
                float: left;
                .contentName {
                  font-size: 16px;
                  font-family: PingFangSC-Regular, PingFang SC;
                  font-weight: 400;
                  color: #FFFFFF;
                  line-height: 22px;
                  margin-top: 19px;
                  padding-left: 22px;
                }
                .contentNum {
                  font-size: 36px;
                  font-family: DIN-BlackItalic, DIN;
                  font-weight: normal;
                  color: #6FDCFF;
                  line-height: 44px;
                  margin-top: -4px;
                  padding-left: 12px;
                  span {
                    font-size: 14px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #6FDCFF;
                    line-height: 20px;
                    margin-left: 6px;
                  }
                }
              }
              .contentleft4 {
                width: 160px;
                height: 104px;
                background: url(~@/assets/csts/jqzl3.png) no-repeat;
                background-size: 100% 100%;
                float: left;
                margin-left: 10px;
                .contentName {
                  font-size: 16px;
                  font-family: PingFangSC-Regular, PingFang SC;
                  font-weight: 400;
                  color: #FFFFFF;
                  line-height: 22px;
                  margin-top: 19px;
                  padding-left: 32px;
                }
                .contentNum {
                  font-size: 36px;
                  font-family: DIN-BlackItalic, DIN;
                  font-weight: normal;
                  color: #6FDCFF;
                  line-height: 44px;
                  margin-top: -4px;
                  padding-left: 11px;
                  span {
                    font-size: 14px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #6FDCFF;
                    line-height: 20px;
                    margin-left: 6px;
                  }
                }
              }
            }
          }
          .contentright {
            width: 140px;
            height: 221px;
            background: url(~@/assets/csts/jqzl4.png) no-repeat;
            background-size: 100% 100%;
            float: left;
            margin-left: 10px;
            text-align: center;
            img {
              margin-top: 36px;
            }
            .contentName {
              font-size: 16px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #FFFFFF;
              line-height: 22px;
              margin-top: 10px;
            }
            .contentNum {
              font-size: 36px;
              font-family: DIN-BlackItalic, DIN;
              font-weight: normal;
              color: #6FDCFF;
              line-height: 44px;
              margin-top: -1px;
              span {
                font-size: 14px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #6FDCFF;
                line-height: 20px;
                margin-left: 6px;
              }
            }
          }
        }
      }
    }
    .middle {
      width: 533px;
      float: left;
      margin-left: 54px;
      padding-top: 43px;
      .box3 {
        .box3Content1 {
          font-size: 14px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #FFFFFF;
          line-height: 20px;
          text-align: left;
          padding-left: 27px;
          margin-top: 10px;
        }
        .box3Content2 {
          text-align: left;
          margin-top: 3px;
          .box3Wz {
            width: 252px;
            height: 20px;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 20px;
            float: left;
            margin-left: 27px;
            span {
              color: #FFC501;
            }
          }
          .box3Ll {
            width: 252px;
            height: 20px;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 20px;
            float: left;
            margin-left: -73px;
            span {
              color: #FFC501;
            }
          }
        }
        .box3Content3 {
          width: 100%;
          height: 239px;
        }
      }
      .box4 {
        .box4Content1 {
          width: 521px;
          height: 44px;
          margin: 7px 6px 0 6px;
          background: url(~@/assets/csts/gdqajtj.png) no-repeat;
          background-size: 100% 100%;
          .box4Name1 {
            float: left;
            width: 130px;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 44px;
            margin-left: 36px;
          }
          .box4Name2 {
            float: left;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 44px;
            margin-left: 36px;
            margin-left: 10px;
          }
        }
        .box4Content2 {
          width: 100%;
          height: 209px;
        }
      }
    }
    .right {
      width: 533px;
      float: left;
      margin-left: 70px;
      padding-top: 43px;
      .box5 {
        .box5Content1 {
          float: left;
          width: 200px;
          height: 200px;
          margin-top: 46px;
        }
        .box5Content2 {
          float: left;
          width: 333px;
          height: 200px;
          margin-top: 46px;
        }
      }
      .box6 {
        .box6Content{
          width: 100%;
          height: 100%;
          padding: 0 47px;
          .box6List {
            width: 132px;
            height: 107px;
            float: left;
            margin: 23px 7px 0 7px;
            &:nth-child(1) {
              background: url(~@/assets/csts/zxdjBg1.png) no-repeat;
              background-size: 100% 100%;
            }
            &:nth-child(2) {
              background: url(~@/assets/csts/zxdjBg2.png) no-repeat;
              background-size: 100% 100%;
            }
            &:nth-child(3) {
              background: url(~@/assets/csts/zxdjBg3.png) no-repeat;
              background-size: 100% 100%;
            }
            &:nth-child(4) {
              background: url(~@/assets/csts/zxdjBg4.png) no-repeat;
              background-size: 100% 100%;
            }
            &:nth-child(5) {
              background: url(~@/assets/csts/zxdjBg5.png) no-repeat;
              background-size: 100% 100%;
            }
            &:nth-child(6) {
              background: url(~@/assets/csts/zxdjBg6.png) no-repeat;
              background-size: 100% 100%;
            }
            .box6Num {
              font-size: 26px;
              font-family: DINAlternate-Bold, DINAlternate;
              font-weight: bold;
              color: #FFFFFF;
              line-height: 30px;
              margin-top: 42px;
            }
            .box6Name {
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
              margin-top: 3px;
            }
          }
        }
      }
    }
  }
}
</style>