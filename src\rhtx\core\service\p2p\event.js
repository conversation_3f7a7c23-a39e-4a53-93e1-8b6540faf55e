import { BTN_CMD, CALL_TYPE } from '../../dict/index';
import {
  createTpl,
  tplReplace,
  isNodeEl,
  getBtnCmd,
  getBtnType,
  getBtn
} from '../../util';
import { ACTIONSBTNS, btnNextInfo } from './data';

export function handleActionsEvent(e, mediaEl) {
  let flag = true;
  const btnType = getBtnType(e);
  const cmd = getBtnCmd(e);
  switch (cmd) {
    case BTN_CMD.MUTED:
      this.session.muted();
      mediaEl.muted = true;
      break;
    case BTN_CMD.UNMUTED:
      this.session.cancelMuted();
      mediaEl.muted = false;
      break;
    case BTN_CMD.DEAF:
      this.session.deaf();
      break;
    case BTN_CMD.UNDEAF:
      this.session.cancelDeaf();
      break;
    case BTN_CMD.HANGUP:
      this.session.terminate();
      flag = false;
      break;
    case BTN_CMD.INFO_SHOW:
      this.loadReportInfo();
      this.reportInfoEl.setAttribute('data-show', cmd);
      break;
    case BTN_CMD.INFO_HIDE:
      this.stopReportInfo();
      this.reportInfoEl.setAttribute('data-show', cmd);
      break;
    default:
      flag = false;
      break;
  }
  if (flag) {
    changeBtnInfo.call(this, btnType, cmd);
  }
}

// 更改按钮信息
function changeBtnInfo(btnType, btnCmd) {
  const findBtn = this.actionsEl.querySelector(`.${btnType}`);
  const nextBtn = btnNextInfo[btnCmd];
  findBtn.setAttribute('btn-cmd', nextBtn.btnCmd);
  findBtn.innerText = nextBtn.text;
}
