<template>
  <div class="ai-alarm">
    <div class="waring-content">
      <span class="label">告警总数:</span>
      <span class="num-container">
        <span
          class="num"
          :class="{ zero: item === 'x' }"
          v-for="(item, index) in 'xxxx100'"
          :key="index"
          >{{ item === 'x' ? '0' : item }}</span
        >
        <span class="dou">,</span>
        <span class="dou dou2">,</span>
      </span>
    </div>
    <div class="detail-content">
      <div class="detal_box">
        <p class="deal_type">待处置</p>
        <p class="deal_num">100</p>
        <img src="@/assets/zhdd/icon35.png" alt="" />
      </div>
      <div class="detal_box">
        <p class="deal_type">已处置</p>
        <p class="deal_num">2080</p>
        <img src="@/assets/zhdd/icon36.png" alt="" />
      </div>
      <div class="detal_box">
        <p class="deal_type">处置率</p>
        <p class="deal_num">80</p>
        <img src="@/assets/zhdd/icon37.png" alt="" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AIIntelligentAlarmData'
}
</script>

<style lang="less" scoped>
.ai-alarm {
  width: 100%;
  height: 100%;
  padding-top: 18px;
  .waring-content {
    display: flex;
    align-items: center;
    height: 39px;
    padding-left: 43px;
  }
  .detail-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 32px;
    .deal_type {
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      color: #9de2ff;
      line-height: 16px;
      margin-top: -8px;
    }
    .deal_num {
      margin-top: 8px;
      font-size: 26px;
      font-family: DINAlternate-Bold, DINAlternate;
      font-weight: bold;
      color: #ffffff;
      line-height: 30px;
    }
    .detal_box {
      position: relative;
      width: 192px;
      height: 144px;
      flex-shrink: 0;
      &:first-of-type {
        background: url('~@/assets/fxyp/yjzl1.png') no-repeat;
        background-size: 100% 100%;
        margin-left: -40px;
      }
      &:nth-of-type(2) {
        background: url('~@/assets/fxyp/yjzl2.png') no-repeat;
        background-size: 100% 100%;
        margin-left: -40px;
      }
      &:last-of-type {
        background: url('~@/assets/fxyp/yjzl3.png') no-repeat;
        background-size: 100% 100%;
        margin-left: -40px;
      }
      img {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
  .num-container {
    position: relative;
    display: inline-block;
    width: 284px;
    height: 39px;
    background: url('~@/assets/img/syzl/ypyj/num-bg.png') no-repeat;
    background-size: 100% 100%;
    .dou {
      position: absolute;
      top: 10px;
      left: 39px;
      height: 33px;
      font-size: 25px;
      color: #ffffff;
      line-height: 33px;
      &.dou2 {
        position: absolute;
        left: 163px;
      }
    }
    .num {
      position: absolute;
      top: 3px;
      left: 10px;
      height: 33px;
      font-size: 25px;
      font-family: 'YSBTH';
      color: #ffffff;
      line-height: 33px;
      display: inline-block;
      width: 19px;
      text-align: center;
      &.zero {
        color: #2b3c51;
      }
      &:nth-child(2) {
        position: absolute;
        left: 57px;
      }
      &:nth-child(3) {
        position: absolute;
        left: 95px;
      }
      &:nth-child(4) {
        position: absolute;
        top: 3px;
        left: 132px;
      }
      &:nth-child(5) {
        position: absolute;
        top: 3px;
        left: 182px;
      }
      &:nth-child(6) {
        position: absolute;
        top: 3px;
        left: 220px;
      }
      &:nth-child(7) {
        position: absolute;
        top: 3px;
        left: 257px;
      }
    }
  }
  .label {
    height: 20px;
    font-size: 14px;
    // font-family: $fontN;
    font-weight: 400;
    color: #9de2ff;
    line-height: 20px;
    letter-spacing: 1px;
    margin-right: 10px;
  }
}
</style>
