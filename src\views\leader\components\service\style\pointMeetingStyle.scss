.meeting-room-contain {
  width: calc(100% - 24px);
  height: calc(100% - 24px);
  position: absolute;
  left: 8px;
  top: 0px;
  .layout-local {
    position: absolute;
    z-index: 200;
    top: 5px;
    right: 9px;
    width: 240px;
    height: 135px;
    .participant {
      width: 100%;
      height: 100%;
      box-shadow: 0px 4px 8px 0px rgba(0,0,0,0.26);
      border-radius: 8px;
      border: 1px solid #FFFFFF;
    }
  }
  .layout-participant {
    width: 100%;
    height: 100%;
    .participant {
      width: 100%;
      height: 100%;
      border-radius: 24px;
      .p-video {
        border-radius: 24px !important;
      }
      .describe {
        bottom: 40px !important;
      }
    }
  }
}
.participant {
  position: relative;
  border-radius: 8px;
  .p-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
  }
  .board {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 0;
    top: 0;
    background: #F1F6FC;
    border-radius: 8px;
    border: 1px solid #316ABE;
    &-avatar {
      width: 22%;
      height: auto;
      border-radius: 50%;
      aspect-ratio: 1/1;
    }
    &-icon {
      width: 22%;
      height: auto;
      border-radius: 50%;
      aspect-ratio: 1/1;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #316abe;
      // width: 54px;
      // height: 54px;
      // margin-right: 20px;
      // border-radius: 50%;
      font-size: 24px;
      color: #FFFFFF;
    }
    &-img {
      width: 22%;
      height: auto;
      border-radius: 50%;
      aspect-ratio: 1/1;
      background: url('~@/assets/service/bohao.png') no-repeat center / 100% 100%;
    }
    .describe {
      background: rgba(0,0,0,0.4);
    }
  }
  .describe {
    position: absolute;
    padding: 0 9px 0 10px;
    max-width: 70%;
    display: flex;
    align-items: center;
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #FFFFFF;
    left: 0px;
    bottom: 0px;
    background: rgba(0,0,0,0.4);
    border-radius: 8px;
    .microphone {
      width: 11px;
      height: 16px;
      margin-right: 6px;
      &-active {
        background: url('~@/assets/service/active-micro.png') no-repeat center / 100% 100%;
      }
      &-inactive {
        background: url('~@/assets/service/ban-micro.png') no-repeat center / 100% 100%;
      }
    }
    .identity {
      white-space: nowrap;
    }
  }
}