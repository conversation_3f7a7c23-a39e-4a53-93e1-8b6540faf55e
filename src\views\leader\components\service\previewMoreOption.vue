<template>
  <div :class="['more-dialog', dialogLayout]" :style="offset">
    <div class="option-list">
      <div :class="['option-item', {'option-item-warning': item === '移出会议'}]" v-for="item of optionList" :key="item" @click="handleOption(item)">{{ item }}</div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'previewMoreOption',
    props: {
      identity: {
        type: String,
        default: ''
      },
      previewMeetingData: {
        type: Object,
        default: () => ({})
      },
      offset: {
        type: Object,
        default: () => {
          return {
            left: '0px',
            top: '0px',
            zIndex: 4100
          }
        }
      },
      optionList: {
        type: Array,
        default: () => {
          return [
            '关闭摄像头',
            '修改名称',
            '移出会议'
          ]
        }
      }
    },
    computed: {
      dialogLayout() {
        if(this.optionList.length === 1) {
          return 'option-layout1'
        } else if(this.optionList.length === 3) {
          return 'option-layout3'
        } else {
          return 'option-layout-other'
        }
      }
    },
    methods: {
      handleOption(option) {
        if(option === '关闭摄像头') {
          this.$emit('closeCamera', this.identity)
        } else if(option === '修改名称') {
          this.$emit('changeParticipantName', this.identity)
        } else if(option === '移出会议') {
          this.$confirm('此操作将使该与会者移出会议, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$emit('removeParticipant', this.identity)
          })
        } else if(option === '结束会议') {
          this.$emit('endMeeting', this.previewMeetingData)
        } else if(option === '查看会议详情') {
          this.$emit('checkMeetingDetail', this.previewMeetingData)
        } else if(option === '修改会议信息') {
          this.$emit('updateMeetingDetail', this.previewMeetingData)
        } else if(option === '取消会议') {
          this.$emit('cancelMeeting', this.previewMeetingData)
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .more-dialog {
    width: 112px;
    overflow: hidden;
    position: fixed;
    transform: translateX(-50%);
    .option-list {
      width: 100%;

      .option-item {
        width: 100%;
        text-align: center;
        white-space: nowrap;
        cursor: pointer;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #ffffff;
        &-warning {
          color: #D06C63 !important;
        }
      }
    }
  }
  .option-layout1 {
    background: url('~@/assets/service/preview_option_bg1.png') no-repeat center / 100% 100%;
    .option-list {
      .option-item {
        line-height: 54px;
      }
    }
  }
  .option-layout3 {
    background: url('~@/assets/service/preview_option_bg3.png') no-repeat center / 100% 100%;
    .option-list {
      height: 144px;
      display: flex;
      justify-content: center;
      flex-direction: column;
      .option-item {
        line-height: 44px;
      }
    }
  }
</style>