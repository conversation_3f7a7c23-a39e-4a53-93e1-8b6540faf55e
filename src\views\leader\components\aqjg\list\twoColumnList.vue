<template>
  <ul>
    <li v-for="(it, i) of list" :key="i" :style="{ width: liWidth }">
      <div class="label" :style="{ width: labelWidth }">{{ it.label }}</div>
      <div class="value" :class="{ view: it.value === '预览' }" @click="view(it)">
        {{ it.value }}
      </div>
    </li>
  </ul>
</template>

<script>
export default {
  name: 'twoColumnList',
  props: {
    list: {
      type: Array,
      default: () => {
        return []
      }
    },
    liWidth: {
      type: String,
      default: '50%'
    },
    labelWidth: {
      type: String,
      default: '200px'
    }
  },
  methods: {
    view(it) {
      this.$emit('view', it)
    }
  }
}
</script>

<style lang="less" scoped>
ul {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  font-size: 16px;
  padding: 0 40px;
  li {
    line-height: 30px;
    display: flex;
    text-align: left;
    gap: 20px;
    .label {
      color: #6a92bb;
    }
    .value {
      flex: 1;
      color: #fff;
      &.view {
        cursor: pointer;
        &:hover {
          color: cornflowerblue;
        }
      }
    }
  }
  &::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 6px;
    background: transparent;
    // border: 1px solid #999;
    /*高宽分别对应横竖滚动条的尺寸*/
    // height: 1px;
  }

  &::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 2px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgb(45, 124, 228);
    height: 100px;
  }
}
</style>
