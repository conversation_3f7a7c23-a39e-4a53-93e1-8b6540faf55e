<template>
	<div class="block" :style="{ height: blockHeight + 'px' }">
		<div class="block_title" @click="clickBulletFrame">
			<span class="title">{{ title }}</span>
			<!-- <span class="subtitle">{{ subtitle }}</span> -->
			<ul class="btns" v-if="isListBtns">
				<li
					:class="['btnList', currentIndex == index ? 'active' : '']"
					v-for="(item, index) in textArr"
					:key="index"
					@click.stop="clickchange(index)"
				>
					{{ item }}
				</li>
			</ul>
			<div class="more" v-if="showMore" @click="showMoreFn">更多</div>
			<div class="more" v-if="showMore2" @click="showMoreFn2">返回</div>
			<div v-if="showMore3" class="popDropDown">
				<el-dropdown v-if="textArr2.length > 0" @command="clickchange">
					<span class="el-dropdown-link"> {{ textArr2[currentIndex][1] }}<i class="el-icon-arrow-down el-icon--right"></i> </span>
					<el-dropdown-menu slot="dropdown">
						<el-dropdown-item v-for="(item, index) in textArr2" :key="index" :command="index">
							{{ item[1] }}
						</el-dropdown-item>
					</el-dropdown-menu>
				</el-dropdown>
			</div>
		</div>
		<div class="content">
			<slot></slot>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		title: {
			type: String,
			default: '',
		},
		subtitle: {
			type: String,
			default: '',
		},
		textArr: {
			type: Array,
			default: () => ['社会保险', '住房保险'],
		},
		textArr2: {
			type: Array,
			default: () => [['', '全部']],
		},
		isListBtns: {
			type: Boolean,
			default: true,
		},
		showMore: {
			type: Boolean,
			default: false,
		},
		showMore2: {
			type: Boolean,
			default: false,
		},
		showMore3: {
			type: Boolean,
			default: false,
		},
		blockHeight: {
			type: Number,
			default: 277,
		},
	},
	data() {
		return {
			currentIndex: 0,
		}
	},
	methods: {
		clickchange(index) {
			this.currentIndex = index
			this.$emit('updateChange', this.currentIndex)
		},
		clickBulletFrame() {
			this.$emit('updateChange2', true)
		},
		showMoreFn() {
			this.$emit('handleShowMore', true)
		},
		showMoreFn2() {
			this.$emit('handleShowMore2', true)
		},
	},
}
</script>

<style lang="scss" scoped>
.block {
	width: 1856px;
	box-sizing: border-box;
	z-index: 101;
	position: relative;
	.block_title {
		position: relative;
		width: 100%;
		height: 48px;
		background: url(~@/assets/img/title_bg3.png) no-repeat;
		background-size: 100% 100%;
		display: flex;
		// justify-content: space-between;
		padding-left: 30px;
		position: relative;
		&.title_bg_Btn {
			background: url(~@/assets/leader/img/component/title_bg1.png) no-repeat;
			background-size: 100% 100%;
		}
		.title {
			font-family: YouSheBiaoTiHei;
			font-size: 22px;
			color: #ffffff;
			line-height: 36px;
			font-style: normal;
			// letter-spacing: 2px;
			background: linear-gradient(180deg, #ffffff 0%, #7ed1ff 100%);
			background-clip: text;
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}
		.subtitle {
			font-size: 14px;
			font-family: DINCond-RegularAlternate;
			font-weight: normal;
			color: #b3daff;
		}

		.btns {
			display: flex;
			align-items: center;
			height: 21px;
			// margin-right: 22px;
			position: absolute;
			top: 10px;
			right: 0;
			.btnList {
				// width: 50px;
				height: 21px;
				font-family: PingFangSC, PingFang SC;
				font-size: 16px;
				color: rgba(202, 236, 255, 0.6);
				line-height: 21px;
				text-align: center;
				font-style: normal;
				// background: url(~@/assets/leader/img/component/title_btn.png) no-repeat;
				background-size: 100% 100%;
				cursor: pointer;
				&.active {
					color: #caecff;
				}
				&:not(:last-of-type) {
					margin-right: 14px;
				}
			}
		}
		.more {
			position: absolute;
			right: 8px;
			top: 14px;
			height: 21px;
			text-shadow: 0px 0px 1px #00132e;
			cursor: pointer;
			font-family: YouSheBiaoTiHei;
			font-size: 16px;
			color: #caecff;
			line-height: 21px;
			text-align: center;
			font-style: normal;
		}
	}
	.content {
		position: relative;
		width: 100%;
		height: calc(100% - 54px);
		background: rgba(0, 14, 25, 0.4);
		border: 1px solid rgba(56, 99, 176, 0.75);
		margin-top: 6px;
	}
}

.popDropDown {
	::v-deep(.el-dropdown) {
		color: #caecff;
		font-family: YouSheBiaoTiHei;
		font-size: 16px;
		line-height: 21px;
		text-align: right;
		font-style: normal;
		line-height: 40px;
		right: 0;
		position: absolute;
	}
	::v-deep .el-dropdown-menu {
		// background: #00173b;
		position: absolute;
		left: 0;
		z-index: 10;
		padding: 10px 0;
		margin: 5px 0;
		// border:1px solid #2b7bbb;
		border-radius: 4px;

		background-color: #c6e2e7 !important;
		border: 1px solid #d2f2ea !important;
	}
	.el-dropdown-menu__item {
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
		line-height: 40px;
		padding: 0 30px;
		&:hover {
			background: #185493;
			color: rgba(255, 255, 255, 0.8);
		}
	}
}
</style>
