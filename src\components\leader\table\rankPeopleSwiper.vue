<template>
  <div class="chart">
    <swiper
      ref="firstSwiper"
      :style="{ height: list.length <= 1 ? '110px' : '100%' }"
      v-if="list.length > 0"
      class="swiper"
      :options="swiperOption"
    >
      <swiper-slide v-for="(it, i) in list" class="width: 100%" :key="i">
        <div class="item">
          <!-- <div class="no-num" v-if="showNoNumber">No.{{ i + 1 }}</div> -->
          <div class="rank-content">
            <!-- <div class="name" :title="it.name">No.{{ i + 1 }} {{ it.name }}</div> -->
            <div class="name">
              <img :src="it.src" alt="" /><span :title="it.name">{{ it.name }}</span>
            </div>
            <div class="bar">
              <!-- <div
                class="bar-num"
                :style="{
                  width: it.value + '%',
                  background: i < 3 ? 'rgba(209, 81, 81, 1)' : 'rgba(15, 209, 167, 1)'
                }"
              ></div> -->
              <div v-if="i ===0" class="bar-num" :style="{width: it.value + '%', background:'rgba(255, 205, 68, 1)'}"></div>
              <div v-else-if="i ===1" class="bar-num" :style="{width: it.value + '%', background:'rgba(255, 137, 67, 1)'}"></div>
              <div v-else-if="i ===2" class="bar-num" :style="{width: it.value + '%', background:'rgba(110, 255, 239, 1)'}"></div>
              <div v-else class="bar-num" :style="{width: it.value + '%', background:'rgba(75, 164, 255, 1)'}"></div>
             
            </div>
            <div v-if="i ===0" class="number" :style="{color:'rgba(255, 205, 68, 1)'}">{{ it.value }}%</div>
            <div v-else-if="i ===1" class="number" :style="{color:'rgba(255, 137, 67, 1)'}">{{ it.value }}%</div>
            <div v-else-if="i ===2" class="number" :style="{color:'rgba(110, 255, 239, 1)'}">{{ it.value }}%</div>
            <div v-else class="number" :style="{color:'rgba(75, 164, 255, 1)'}">{{ it.value }}%</div>
          </div>
        </div>
      </swiper-slide>
    </swiper>
  </div>
</template>

<script>
export default {
  name: 'bar-hor-no-chart',
  props: {
    list: {
      type: Array,
      default: () => [
        {
          src: require('@/assets/img/content/no1.png'),
          value: 34242,
          name: '一手房转移登记'
        },
        {
          src: require('@/assets/img/content/no2.png'),
          value: 17239,
          name: '二手房转移登记'
        },
        {
          src: require('@/assets/img/content/no3.png'),
          value: 15521,
          name: '营业执照'
        },
        {
          src: require('@/assets/img/content/no4.png'),
          value: 10111,
          name: '社保卡综合业务'
        },
        {
          src: require('@/assets/img/content/no5.png'),
          value: 8239,
          name: '其它综合业务'
        }
        // {
        //   // src: require('@/assets/img/content/no1.png'),
        //   value: 7921,
        //   name: '黄桥镇'
        // },
        // {
        //   // src: require('@/assets/img/content/no1.png'),
        //   value: 111,
        //   name: '万年镇'
        // },
        // {
        //   // src: require('@/assets/img/content/no1.png'),
        //   value: 1312,
        //   name: '义星镇'
        // }
      ]
    },
    /**
     * showNoNumber: 是否展示排名
     */
    showNoNumber: {
      type: Boolean,
      default: true
    },
    /**
     * barColor: 柱子颜色
     */
    barColor: {
      type: String,
      default: 'rgba(15, 209, 167, 1)'
    },
    /**
     * multiple: 最大值倍数
     */
    multiple: {
      type: Number,
      default: 1.1
    },
    /**
     * sort: 是否开启排序
     */
    sort: {
      type: Boolean,
      default: true
    },
    /**
     * order: 排序方式 0 为正序
     */
    order: {
      type: Number
    }
  },
  data() {
    return {
      max: 100,
      swiperOption: {
        observer: true,
        observeParents: true,
        autoHeight: true,
        direction: 'vertical',
        spaceBetween: 0,
        autoplay: {
          delay: 2500,
          disableOnInteraction: false
        },
        slidesPerView: 'auto',
        grabCursor: true,
        autoplayDisableOnInteraction: false,
        mousewheelControl: true
      }
    };
  },
  mounted() {
    this.sort &&
      this.list.sort((a, b) => {
        return this.order !== 0 ? b.value - a.value : a.value - b.value;
      });
  },
  watch: {
    list(newValue) {
      const valueArr = newValue.map(item => item.value);
      this.max = Math.max(...valueArr) * this.multiple;
      console.log(this.max);
    }
  }
};
</script>

<style lang="scss" scoped>
$height: 28px;
::v-deep .swiper-container {
  margin: 0;
  width: 100%;
}
.chart {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  box-sizing: border-box;
  .item {
    width: 100%;
    height: 80px;
    padding: 8px 0;
    align-items: center;
    .rank-content {
      width: 100%;
      height: 28px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .name {
        width: 265px;
        display: flex;
        align-items: center;
        span {
          display: block;
          width: 185px;
          font-size: 25px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          letter-spacing: 1px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
      .number {
        text-align: center;
        height: 26px;
        font-size: 25px;
        font-family: Arial-BoldMT, Arial;
        font-weight: normal;
        color: rgba(255, 255, 255, 0.85);
        line-height: 26px;
      }
    }
    .bar {
      width: 90%;
      margin: 0 2%;
      height: 12px;
      flex: 3.5;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 5px;
      .bar-num {
        height: 100%;
        border-radius: 5px;
        background: rgba(15, 209, 167, 1);
      }
    }
    .name {
      text-align: left;
      flex: 1.5;
      height: $height;
      font-size: 20px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.65);
      line-height: $height;
    }
  }
  .item + .item {
    margin-top: 5px;
  }
}
</style>
