<template>
  <div class="ai_waring" v-if="show">
    <div class="title">
      <span>非公党支部信息</span>
    </div>
    <div class="close_btn" @click="closeEmitai"></div>
    <!-- <div class="tabs">
        <div
          class="tab"
          :class="{ active: tabActive === i }"
          v-for="(it, i) of tabs"
          :key="i"
          @click="changeTab(i)"
        >
          <span>{{ it }}</span>
        </div>
      </div> -->
    <div class="content">
      <div class="check_types">
        <div class="jjcd">
          <span>组织名称：</span>
          <el-input v-model="inputValue" placeholder="请输入"></el-input>
        </div>
        <!-- <div class="jjcd">
            <span>场所名称：</span>
            <Select v-model="proityValue">
              <Option v-for="item in urgentOptions" :value="item.value" :key="item.value">{{
                item.label
              }}</Option>
            </Select>
          </div> -->
        <!-- <div class="jjcd">
          <span>场所类型：</span>
          <Select v-model="proityValue">
            <Option v-for="item in eventTypeOptions" :value="item.value" :key="item.value">{{
              item.label
            }}</Option>
          </Select>
        </div> -->
        <!-- <div class="jjcd">
            <span>队伍名称：</span>
            <el-input v-model="inputValue" placeholder="请输入队伍名称名称"></el-input>
          </div> -->
        <div class="type_btns">
          <div @click="searchBtn">查询</div>
          <div @click="resetBtn">重置</div>
        </div>
      </div>
      <div class="table_box">
        <SwiperTable
          :titles="['组织名称', '书记姓名', '单位名称', '联系电话']"
          :widths="['30%', '25%', '25%', '25%']"
          :data="sjtjData"
          :contentHeight="'510px'"
          :isNeedOperate="false"
        ></SwiperTable>
      </div>
      <div class="fy_page">
        <Page :total="total" @on-change="pageNumChange" show-total></Page>
      </div>
    </div>
    <!-- <SocialSmallPlaceDetail v-model="socialSmallPlaceDetailShow" /> -->
  </div>
</template>

<script>
import SwiperTable from '../table/SwiperTable.vue'
import SocialSmallPlaceDetail from './SocialSmallPlaceDetail.vue'
import { getFgdzbPage } from '@/api/hs/hs.djyl.js'
export default {
  name: 'PartyBranchInfo',
  components: { SwiperTable, SocialSmallPlaceDetail },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      socialSmallPlaceDetailShow: false,
      inputValue: '',
      tabActive: 0,
      tabs: ['应急队伍', '机具保有量', '应急车辆', '医疗机构', '避难场所'],
      btnsArr: [
        {
          name: '未办结',
          normalBg: require('@/assets/shzl/map/left_n.png'),
          activeBg: require('@/assets/shzl/map/left_a.png'),
        },
        {
          name: '已办结',
          normalBg: require('@/assets/shzl/map/right_n.png'),
          activeBg: require('@/assets/shzl/map/right_a.png'),
        },
      ],
      urgentOptions: [
        {
          value: '0',
          label: '普通',
        },
        {
          value: '1',
          label: '突发',
        },
        {
          value: '2',
          label: '重要',
        },
        {
          value: '3',
          label: '紧急',
        },
      ],
      eventTypeOptions: [
        {
          value: '0',
          label: '中介',
        },
        {
          value: '1',
          label: '五金店',
        },
        {
          value: '2',
          label: '人才市场',
        },
        {
          value: '3',
          label: '健身俱乐部',
        },
        {
          value: '4',
          label: '公司',
        },
        {
          value: '5',
          label: '办公室',
        },
      ],
      sjtjData: [
        [
          '中共西安市雁塔区枫林华府铭蓝幼儿园支部委员会',
          '集体企业',
          '王刚',
          '西安中惠建筑工程公司',
          '8765****0169',
        ],
        [
          '西安市高陵区玉祥天然气有限公司',
          '集体企业',
          '魏雪红',
          '陕西建勋房地产开发有限公司',
          '8765****0169',
        ],
        [
          '中共衡正国际工程咨询有限公司党支部',
          '其他企业',
          '白富琨',
          '陕西建勋房地产开发有限公司',
          '8765****0169',
        ],
        [
          '高陵区绪昌面粉有限公司党支部',
          '其他企业',
          '李海鹏',
          '陕西建勋房地产开发有限公司',
          '8765****0169',
        ],
        [
          '西安蓝剑保安服务有限公司党支部',
          '其他企业',
          '韩梅',
          '陕西建勋房地产开发有限公司',
          '8765****0169',
        ],
        [
          '西安中惠建筑工程公司党支部',
          '其他企业',
          '周晓宇',
          '陕西建勋房地产开发有限公司',
          '8765****0169',
        ],
      ],
      proityOptions: [
        {
          value: '0',
          label: '一般',
        },
        {
          value: '1',
          label: '紧急',
        },
        //  {
        //     value: '2',
        //     label: '紧急',
        //   },
      ],
      timeOutOptions: [
        {
          value: '0',
          label: '是',
        },
        {
          value: '1',
          label: '否',
        },
      ],
      proityValue: '',
      timeOutValue: '',
      settled: false,
      total: 0,
      pageNum: 0,
      pageSize: 10,
    }
  },
  computed: {
    show() {
      return this.value
    },
  },
  methods: {
    closeEmitai() {
      this.$emit('input', false)
    },
    dealMethod() {
      console.log(1111)
      this.$parent.dealMethod()
    },
    dwMethod() {
      this.$parent.dwMethod()
    },
    changeTab(i) {
      this.tabActive = i
    },
    // handleOperate(i, it) {
    //   console.log(i, it)
    //   this.socialSmallPlaceDetailShow = true
    // },
    searchBtn() {
      this.getFgdzbPageFn()
    },
    resetBtn() {
      this.inputValue = ''
      this.getFgdzbPageFn()
    },
    pageNumChange(val) {
      this.pageNum = val
      this.getFgdzbPageFn()
    },
    async getFgdzbPageFn() {
      let res = await getFgdzbPage(this.inputValue, this.pageNum, this.pageSize)
      if (res?.code == '200') {
        this.sjtjData = []
        this.sjtjData = res.result.data.map((it, i) => [
          it.name,
          it.secretaryName,
          it.enterpriseName,
          it.tel,
        ])
        this.total = res.result.recordsTotal
      }
    },
  },
  created() {
    this.getFgdzbPageFn()
  },
}
</script>

<style lang="less" scoped>
.bg-header {
  position: absolute;
  width: 100%;
  height: 1080px;
  background: rgba(2, 14, 50, 0.75);
  z-index: 999999;
  top: 0;
  left: 0;
}
/deep/ .ivu-select {
  width: 168px;
}
/deep/ .ivu-select-selection {
  width: 168px;
  height: 34px;
  background: rgba(0, 74, 143, 0.4)
    linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
}
/deep/ .ivu-select-selection .ivu-select-selected-value {
  font-size: 13px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 34px;
}

/deep/ .ivu-input {
  background: rgba(0, 74, 143, 0.4)
    linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 22px;
}
/deep/ .ivu-select-placeholder {
  font-size: 13px !important;
  line-height: 34px !important;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff !important;
}
/deep/ .ivu-page-item {
  background: transparent;
  color: #fff;
}
/deep/ .ivu-page-item a {
  color: #fff;
}
/deep/ .ivu-page-prev,
/deep/ .ivu-page-next {
  background: transparent;
}
.ai_waring {
  width: 1435px;
  height: 759px;
  background: rgba(9, 19, 34, 0.95);
  box-shadow: 0px 0px 22px 0px rgba(11, 54, 138, 0.35), 0px 2px 8px 0px rgba(0, 0, 0, 0.7),
    inset 0px 0px 8px 0px rgba(17, 146, 214, 0.35);
  border-radius: 11px;
  border: 1px solid #015c8c;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1003;
  .title {
    margin: 0 auto 23px;
    width: 635px;
    height: 76px;
    line-height: 76px;
    background: url(~@/assets/map/dialog/title2.png) no-repeat;
    display: grid;
    place-items: center;
    span {
      font-size: 36px;
      font-family: PingFangSC, PingFang SC;
      line-height: 36px;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .tabs {
    display: flex;
    gap: 13px;
    margin: 28px 0 42px 28px;
    .tab {
      width: 191px;
      height: 41px;
      line-height: 41px;
      background: url('~@/assets/map/dialog/bg5.png') no-repeat center / 100% 100%;
      cursor: pointer;
      span {
        font-size: 24px;
        font-family: PingFangSC, PingFang SC;
        color: #ffffff;
      }
      &.active {
        background: url('~@/assets/map/dialog/bg4.png') no-repeat center / 100% 100%;
        span {
          font-size: 30px;
          font-family: PingFangSC, PingFang SC;
          background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
  }
  .close_btn {
    position: absolute;
    width: 23px;
    height: 23px;
    top: 25px;
    right: 40px;
    background: url(~@/assets/map/dialog/btn3.png) no-repeat;
    cursor: pointer;
  }
  .content {
    padding: 0 30px;
    .btns {
      // border: 1px solid red;
      margin: 0 auto;
      display: flex;
      justify-content: center;
      & > div {
        width: 154px;
        height: 53px;
        span {
          font-size: 24px;
          font-family: PingFangSC, PingFang SC;
          color: #ffffff;
          line-height: 53px;
          background: linear-gradient(180deg, #ffffff 0%, #ffeba0 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
    .check_types {
      text-align: left;
      display: flex;
      .jjcd {
        display: flex;
        align-items: center;
        margin-right: 24px;
        span {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
        }
      }
      .report_time {
        margin-left: 26px;
        display: flex;
        align-items: center;
        span {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
      }
      .istime_out {
        margin-left: 26px;
        align-items: center;
        display: flex;
        span {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
      }
      .type_btns {
        margin-left: 27px;
        display: flex;
        align-items: center;
        & div {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 43px;
          background: url(~@/assets/shzl/map/type_btn.png) no-repeat center / 100% 100%;
          background-size: 100% 100%;
          text-align: center;
          cursor: pointer;
          &:first-of-type {
            width: 98px;
            height: 43px;
          }
          &:nth-of-type(2) {
            margin-left: 20px;
            width: 98px;
            height: 43px;
          }
          &:nth-of-type(3) {
            margin-left: 20px;
            width: 98px;
            height: 43px;
          }
        }
      }
    }
    .table_box {
      margin-top: 31px;
      overflow: hidden;
    }
    .fy_page {
      margin-top: 20px;
    }
  }
}
/deep/ .el-input {
  width: 200px;
  height: 34px;
  .el-input__inner {
    height: 34px;
    background: rgba(0, 74, 143, 0.4)
      linear-gradient(
        360deg,
        rgba(181, 223, 248, 0) 0%,
        rgba(29, 172, 255, 0.29) 100%,
        #ffffff 100%
      );
    border: 1px solid rgba(0, 162, 255, 0.6);
    color: #fff;
    padding: 0 30px 0 15px;
  }
}
</style>
