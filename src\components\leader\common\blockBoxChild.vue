<template>
	<div class="child-block" :style="{ height: blockHeight_ + 'px' }">
		<div class="block_title">
			<div class="left-box">
				<!-- <img class="nongji-icon-box" :src="blockboxIcon" alt="" /> -->
				<div v-if="tabs.length == 0" class="left-title">{{ title }}</div>
				<div v-else class="tabs-box">
					<div
						class="tab-item"
						v-for="(item, index) in tabs"
						:class="currentTabIndex == index ? 'tab-item-active' : ''"
						:key="index"
						@click="handleChangeTab(item, index)"
					>
						{{ item.label }}
					</div>
				</div>
			</div>
			<div class="right-box">
				<!-- <div class="right-title">{{ rightTitle }}</div> -->
				<div class="content-box">
					<div class="content-number">{{ contentNumberOne_ }}</div>
					<div class="content-unit">{{ contentNumberUnitOne }}</div>
				</div>
				<!-- <div class="devider-column-box"></div>
				<div class="content-box">
					<div class="content-number">{{ contentNumberTwo_ }}</div>
					<div class="content-unit">{{ contentNumberUnitTwo }}</div>
				</div> -->
				<div class="operate-hide-box" :class="isActive ? 'content-active' : 'content-hide'" @click="handleClickHide"></div>
			</div>
		</div>
		<div class="content" v-if="isActive">
			<slot></slot>
			<div
				class="data-item"
				v-for="(item, index) in dataList"
				:key="index"
				@click="
					() => {
						item.func(item.argmachType)
					}
				"
			>
				<img class="data-item-icon" :src="item.iconUrl" alt="" />
				<div class="data-item-content">
					<el-tooltip effect="dark" popper-class="tooltip-item" :content="item.title" placement="top">
						<div class="data-item-title">{{ item.title }}</div>
					</el-tooltip>
					<div class="data-item-number-box">
						<div class="data-item-number">{{ item.number }}</div>
						<div class="data-item-number-unit">{{ contentNumberUnitOne }}</div>
					</div>
				</div>
			</div>
			<div
				class="other-data-item"
				:class="isOpenOtherData ? 'other-data-item-active' : ''"
				v-if="otherDataList.length > 0 && isActive"
				@click="handleClickOtherData"
			>
				<div class="other-data-item-icon"></div>
				<div class="data-item-content">
					<div class="data-item-title">{{ '其他' }}</div>
					<div class="data-item-number-box">
						<div class="data-item-number">{{ otherArgmachNumber }}</div>
						<div class="data-item-number-unit">{{ contentNumberUnitOne }}</div>
					</div>
				</div>
			</div>
			<div
				class="other-data-box"
				v-if="isActive && isOpenOtherData"
				:style="boxLocation == 'left' ? { top: '-65px', left: '444px' } : { top: '120px', right: '450px' }"
			>
				<div class="other-data-body-box">
					<!-- <img src="@/assets/img/block-box/other-argmach-box-bg.png" alt="" /> -->
					<div class="other-data-header">
						<div class="other-data-title">{{ '其他' }}</div>
						<div class="other-data-close" @click="handleClickCloseOtherData"></div>
					</div>
					<div class="other-data-content">
						<div
							class="other-data-item"
							v-for="(item, index) in otherDataList"
							:key="index"
							@click="
								() => {
									item.func(item.argmachType)
								}
							"
						>
							<!-- <div class="other-data-item-icon"></div> -->
							<img class="data-item-icon" :src="item.iconUrl" alt="" />
							<div class="data-item-content">
								<div class="data-item-title">{{ item.title }}</div>
								<div class="data-item-number-box">
									<div class="data-item-number">{{ item.number }}</div>
									<div class="data-item-number-unit">{{ contentNumberUnitOne }}</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		blockboxIcon: {
			type: String,
			default: '',
		},
		title: {
			type: String,
			default: '',
		},
		contentNumberOne: {
			type: Number,
			default: 0,
		},
		contentNumberUnitOne: {
			type: String,
			default: '万台',
		},
		contentNumberTwo: {
			type: Number,
			default: 0,
		},
		contentNumberUnitTwo: {
			type: String,
			default: '%',
		},

		contentDataList: {
			type: Array,
			default: () => [],
		},

		boxLocation: {
			type: String,
			default: 'left',
		},

		rightTitle: {
			type: String,
			default: '',
		},
		subtitle: {
			type: String,
			default: '',
		},
		textArr: {
			type: Array,
			default: () => ['社会保险', '住房保险'],
		},
		textArr2: {
			type: Array,
			default: () => [['', '全部']],
		},
		isListBtns: {
			type: Boolean,
			default: true,
		},
		showMore: {
			type: Boolean,
			default: false,
		},
		showMore2: {
			type: Boolean,
			default: false,
		},
		showMore3: {
			type: Boolean,
			default: false,
		},
		blockHeight: {
			type: Number,
			default: 162,
		},
		tabs: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			currentIndex: 0,
			isActive: true,
			isOpenOtherData: false,
			dataList: [],
			otherDataList: [],
			blockHeight_: 162,
			currentTabIndex: 0,
		}
	},
	computed: {
		otherArgmachNumber() {
			let otherArgmachNumber = 0

			this.otherDataList.forEach((item) => {
				otherArgmachNumber += Number(item.number)
			})
			return otherArgmachNumber.toFixed(2)
		},
		contentNumberOne_() {
			return Number(this.contentNumberOne)
		},

		contentNumberTwo_() {
			return this.contentNumberTwo
		},
	},
	methods: {
		handleClickHide() {
			this.isActive = !this.isActive
			if (!this.isActive) {
				this.blockHeight_ = 46
			} else {
				this.blockHeight_ = this.blockHeight
			}
		},
		clickchange(index) {
			this.currentIndex = index
			this.$emit('updateChange', this.currentIndex)
		},
		clickBulletFrame() {
			this.$emit('updateChange2', true)
		},
		showMoreFn() {
			this.$emit('handleShowMore', true)
		},
		showMoreFn2() {
			this.$emit('handleShowMore2', true)
		},
		handleClickOtherData() {
			this.isOpenOtherData = !this.isOpenOtherData
		},
		handleClickCloseOtherData() {
			this.isOpenOtherData = false
		},
		handleChangeTab(item, index) {
			this.currentTabIndex = index
			this.$emit('handleChangeTab', item.value)
		},
	},

	watch: {
		contentDataList: {
			handler: function(val) {
				if (val.length > 5) {
					this.dataList = val.slice(0, 5)
					this.otherDataList = val.slice(5)
				} else {
					this.dataList = val || []
					this.otherDataList = []
				}
			},
			// immediate: true,
			deep: true,
		},

		tabs: {
			handler: function(val) {
				if (val.length > 0) {
					this.currentTabValue = val[0].value
				} else {
					this.currentTabValue = ''
				}
			},
			deep: true,
		},

		blockHeight: {
			handler: function(val) {
				this.blockHeight_ = val
			},
			// immediate: true,
			deep: true,
		},
	},
}
</script>

<style lang="less" scoped>
.child-block {
	width: 455px;
	box-sizing: border-box;
	z-index: 101;
	position: relative;
	padding-left: 22px;
	padding-right: 16px;
	margin-bottom: 56px;
	.block_title {
		position: relative;
		width: 422px;
		height: 33px;
		margin-bottom: 23px;
		background: url(~@/assets/img/block-box/child-header.png);

		/* 背景不重复 */
		background-repeat: no-repeat;
		/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
		background-size: cover;
		/* 背景居中显示（可选） */
		background-position: center;

		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		padding-left: 24px;
		padding-right: 13px;

		.left-box {
			height: calc(100% - 7px);
			display: flex;
			justify-content: flex-start;
			align-items: center;

			.nongji-icon-box {
				width: 16px;
				height: 16px;

				/* 背景不重复 */
				background-repeat: no-repeat;
				/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
				background-size: cover;
				/* 背景居中显示（可选） */
				background-position: center;
				margin-right: 30px;
			}

			.left-title {
				height: 100%;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				font-size: 14px;
				color: #b0e0ff;
				line-height: 21px;
				letter-spacing: 1px;
				text-shadow: 0px 6px 6px rgba(0, 0, 0, 0.3);
				text-align: justified;
				font-style: normal;
				text-transform: none;
				display: flex;
				justify-content: flex-start;
				align-items: center;
			}

			.tabs-box {
				height: 100%;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				padding-bottom: 6px;
				.tab-item {
					min-width: 42px;
					height: 100%;
					display: flex;
					justify-content: center;
					align-items: center;

					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 16px;
					color: #70839e;
					line-height: 22px;
					font-style: normal;
					cursor: pointer;
				}

				.tab-item-active {
					color: #ffffff;
					background: linear-gradient(180deg, rgba(254, 200, 109, 0) 0%, rgba(255, 128, 12, 0.88) 47%, #ff800c 100%);
				}
			}
		}

		.right-box {
			height: calc(100% - 7px);
			display: flex;
			justify-content: flex-end;
			align-items: center;

			.content-box {
				height: 100%;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				.content-number {
					height: 100%;
					font-family: DINCond-Bold, DINCond-Bold;
					font-weight: 400;
					font-size: 18px;
					color: #dbf1ff;
					text-align: right;
					font-style: normal;
					text-transform: none;
					font-weight: bold;

					margin-right: 4px;
					display: flex;
					justify-content: center;
					align-items: center;
				}
				.content-unit {
					height: 100%;
					font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
					font-weight: normal;
					font-size: 12px;
					color: #d0deee;
					text-align: right;
					font-style: normal;
					text-transform: none;

					display: flex;
					justify-content: center;
					align-items: flex-end;
					padding-bottom: 3px;
				}
			}

			.operate-hide-box {
				width: 12px;
				height: 8px;
				margin-left: 9px;
				cursor: pointer;
				margin-bottom: 3px;
				&.content-active {
					background: url(~@/assets/img/block-box/content-active-icon.png);
					/* 背景不重复 */
					background-repeat: no-repeat;
					/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
					background-size: cover;
					/* 背景居中显示（可选） */
					background-position: center;
				}
				&.content-hide {
					background: url(~@/assets/img/block-box/content-hide-icon.png);
					/* 背景不重复 */
					background-repeat: no-repeat;
					/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
					background-size: cover;
					/* 背景居中显示（可选） */
					background-position: center;
				}
			}

			.devider-column-box {
				width: 1px;
				height: 23px;
				// background: rgb(216, 216, 216, 1, 0.5);
				border: 1px solid rgb(151, 151, 151, 0.5);
				margin: 0 15px;
			}
		}

		&.title_bg_Btn {
			background: url(~@/assets/leader/img/component/title_bg1.png) no-repeat;
			background-size: 100% 100%;
		}
		.title {
			font-family: YouSheBiaoTiHei;
			font-size: 22px;
			color: #ffffff;
			line-height: 36px;
			font-style: normal;
			letter-spacing: 2px;
			background: linear-gradient(180deg, #ffffff 0%, #7ed1ff 100%);
			background-clip: text;
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}
		.subtitle {
			font-size: 14px;
			font-family: DINCond-RegularAlternate;
			font-weight: normal;
			color: #b3daff;
		}

		.btns {
			display: flex;
			align-items: center;
			height: 16px;
			// margin-right: 22px;
			position: absolute;
			top: 1px;
			right: 15px;
			.btnList {
				// width: 50px;
				height: 26px;
				font-size: 16px;
				font-family: PangMenZhengDao;
				color: #caecff;
				text-shadow: 0px 0px 1px #00132e;
				background: url(~@/assets/leader/img/component/title_btn.png) no-repeat;
				background-size: 100% 100%;
				line-height: 26px;
				cursor: pointer;
				&.active {
					color: #45daff;
					text-shadow: 0px 0px 4px rgba(0, 175, 255, 0.5), 0px 0px 1px rgba(0, 30, 50, 0.5);
				}
				&:not(:last-of-type) {
					margin-right: 14px;
				}
			}
		}
		.more {
			position: absolute;
			right: 8px;
			top: 14px;
			height: 21px;
			text-shadow: 0px 0px 1px #00132e;
			cursor: pointer;
			font-family: YouSheBiaoTiHei;
			font-size: 16px;
			color: #caecff;
			line-height: 21px;
			text-align: center;
			font-style: normal;
		}
	}

	.content {
		position: relative;
		width: 100%;
		// height: calc(100% - 74px);
		display: flex;
		justify-content: flex-start;
		flex-wrap: wrap;
		align-items: center;

		&.no-active-content {
			height: 0;
			display: none;
		}

		.data-item,
		.other-data-item {
			width: 128px;
			// height: 40px;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			margin-bottom: 24px;
			cursor: pointer;
			margin-right: 11px;
		}

		.data-item:hover,
		.other-data-item:hover {
			// background: url(~@/assets/img/block-box/block-box-child-item-active-bg.png);
			/* 背景不重复 */
			background-repeat: no-repeat;
			/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
			background-size: cover;
			/* 背景居中显示（可选） */
			background-position: center;
		}

		.other-data-item-active {
			// background: url(~@/assets/img/block-box/block-box-child-item-active-bg.png);
			/* 背景不重复 */
			background-repeat: no-repeat;
			/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
			background-size: cover;
			/* 背景居中显示（可选） */
			background-position: center;
		}

		.data-item-icon,
		.other-data-item-icon {
			width: 40px;
			height: 40px;
			/* 背景不重复 */
			background-repeat: no-repeat;
			/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
			background-size: cover;
			/* 背景居中显示（可选） */
			background-position: center;
			margin-right: 6px;
		}
		.other-data-item-icon {
			background: url(~@/assets/img/block-box/other-argmach-icon.png);
			/* 背景不重复 */
			background-repeat: no-repeat;
			/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
			background-size: cover;
			/* 背景居中显示（可选） */
			background-position: center;
		}
		.data-item-content {
			width: calc(100% - 46px);
			height: 100%;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: flex-start;

			.data-item-title {
				width: 84px;
				height: 14px;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				// color: #24818c;
				color: #b0e0ff;
				line-height: 14px;
				font-style: normal;
				white-space: nowrap; /* 强制文本不换行 */
				overflow: hidden; /* 超出部分隐藏 */
				text-overflow: ellipsis; /* 超出时显示省略号 */

				margin-bottom: 6px;
				line-height: 14px;
				text-align: left;
				// display: flex;
				// justify-content: flex-start;
				// align-items: center;
			}

			.data-item-number-box {
				width: 84px;
				height: 20px;
				display: flex;
				justify-content: flex-start;
				align-items: flex-end;
				.data-item-number {
					height: 20px;
					font-family: DINCondensed, DINCondensed;
					font-weight: bold;
					font-size: 20px;
					// color: #24818c;
					color: #4de4ff;

					line-height: 20px;
					font-style: normal;
					margin-right: 4px;
				}
				.data-item-number-unit {
					height: 14px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 300;
					font-size: 12px;
					// color: #4e5969;
					color: #4de4ff;

					line-height: 14px;
					font-style: normal;
				}
			}
		}

		.other-data-box {
			position: absolute;

			// width: 444px;
			// width: 365px;
			// height: 185px;
			// width: 406px;
			width: 528px;
			height: 425px;

			// background: rgba(255, 255, 255, 0.85);
			// box-shadow: inset 0px 0px 87px 0px rgba(36, 129, 140, 0.4);
			// border: 1px solid #24818c;

			z-index: 999;

			.other-data-item {
				width: 242px;

				.data-item-title {
					width: 100%;
				}
			}

			.other-data-body-box {
				position: relative;
				width: 100%;
				height: 100%;
				display: flex;
				flex-direction: column;
				justify-content: flex-start;
				align-items: center;
				background: #04244e !important;
				img {
					// width: 100%;
					// height: 100%;
					// background: url(~@/assets/img/block-box/other-argmach-box-bg.png);
				}
			}

			.other-data-header {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 28px;
				display: flex;
				justify-content: space-between;
				align-items: center;

				padding-left: 17px;

				.other-data-title {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 14px;
					// color: #24818c;
					color: #b0e0ff;
					line-height: 14px;
					font-style: normal;
				}
				.other-data-close {
					width: 40px;
					height: 27px;
					background: url(~@/assets/img/block-box/other-argmach-box-close-icon.png);
					/* 背景不重复 */
					background-repeat: no-repeat;
					/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
					background-size: cover;
					/* 背景居中显示（可选） */
					background-position: center;
					cursor: pointer;
				}
			}

			.other-data-content {
				position: absolute;
				top: 30px;
				left: 0;
				width: 100%;
				// height: calc(100% - 70px);
				max-height: calc(100% - 30px);

				display: flex;
				justify-content: flex-start;
				flex-wrap: wrap;
				align-items: flex-start;
				overflow-y: auto;

				padding: 17px 0 0 17px;

				&::-webkit-scrollbar {
					width: 4px; /* 垂直滚动条的宽度 */
					height: 4px; /* 水平滚动条的高度 */
				}

				&::-webkit-scrollbar-track {
					background-color: #b8e3e7; /* 轨道背景色 */
					border-radius: 10px; /* 轨道圆角 */
				}

				&::-webkit-scrollbar-thumb {
					background-color: #0a1c3f; /* 滑块背景色 */
					border-radius: 10px; /* 滑块圆角 */
					border: 3px solid #178dfa; /* 滑块与轨道之间的间隙 */
				}
			}
		}
	}
}

.popDropDown {
	::v-deep .el-dropdown {
		color: #caecff;
		font-family: YouSheBiaoTiHei;
		font-size: 16px;
		line-height: 21px;
		text-align: right;
		font-style: normal;
		line-height: 40px;
		right: 0;
		position: absolute;
	}
	::v-deep .el-dropdown-menu {
		background-color: #00173b !important;
		border: 1px solid #2b7bbb !important;
	}
	.el-dropdown-menu__item {
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
		line-height: 40px;
		padding: 0 30px;
		&:hover {
			background: #185493;
			color: rgba(255, 255, 255, 0.8);
		}
	}
}
</style>
