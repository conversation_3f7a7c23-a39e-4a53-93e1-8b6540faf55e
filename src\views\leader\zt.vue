<template>
  <div class="leader_box">
    <div class="leader_sjts">
      <!-- <div class="left_bg"></div>
      <div class="right_bg"></div>-->
      <div class="left">
        <div class="left1">
          <BlockBox
            title="农机接入"
            subtitle="Introduction to Hushu"
            class="box box1"
            :isListBtns="false"
            :blockHeight="284"
          >
            <div class="cont">
              <div class="cont1">
                <div class="contList" v-for="(it, i) of data3" :key="i">
                  <div class="contNum">
                    <span>{{ it.num }}</span>
                  </div>
                  <img src="@/assets/bjnj/njjrImg1.png" alt />
                  <div class="contName">{{ it.name }}</div>
                </div>
              </div>
              <div class="cont2">
                <TrendLineChart
                  :data="gdqsChartData"
                  :options="gdqsOptions"
                  :initOption="gdqsInitOption"
                />
              </div>
            </div>
          </BlockBox>
          <BlockBox
            title="资源接入"
            subtitle="Economic Overview"
            class="box box2"
            :isListBtns="false"
            :blockHeight="304"
          >
            <div class="cont">
              <div class="contLeft">
                <div class="contList" v-for="(it, i) of data5_1" :key="i">
                  <div class="contNum">
                    {{ it.num }}
                    <span>{{ it.unit }}</span>
                  </div>
                  <div class="contName">{{ it.name }}</div>
                </div>
              </div>
              <div class="contRight">
                <div class="contList" v-for="(it, i) of data5_2" :key="i">
                  <div class="contNum">
                    {{ it.num }}
                    <span>{{ it.unit }}</span>
                  </div>
                  <div class="contName">{{ it.name }}</div>
                </div>
              </div>
              <img class="contImg" src="@/assets/bjnj/zyjrImg2.png" alt />
              <img class="contImg2" src="@/assets/bjnj/zyjrImg1.png" alt />
            </div>
          </BlockBox>
          <BlockBox
            title="车企接入"
            subtitle="Air quality"
            class="box box3"
            :isListBtns="false"
            :blockHeight="332"
          >
            <div class="cont">
              <swiper-table
                :data="sewageDataList1"
                :titles="['序号', '企业名称', '农机数量', '占比']"
                :widths="['50px', '130px', '80px', '178px']"
                content-height="260px"
              />
            </div>
          </BlockBox>
        </div>
      </div>
      <div class="right">
        <div class="right1">
          <BlockBox
            title="农机类型"
            subtitle="Party Building Information"
            class="box box7"
            :isListBtns="false"
            :blockHeight="302"
          >
            <PieChart3D type="1" :data="chartData" :options="chartDataOptions" />
          </BlockBox>
          <BlockBox
            title="农机分布"
            subtitle="Population Survey"
            class="box box8"
            :isListBtns="false"
            :blockHeight="298"
          >
            <StereoscopicBarChart
              v-if="box1BottomData.data.length > 0"
              :data="box1BottomData.data"
              :options="box1BottomData.options"
              :init-option="initOptions1"
            />
          </BlockBox>
          <BlockBox
            title="活跃趋势"
            subtitle="Full Factor Grid Events"
            class="box box6"
            :isListBtns="false"
            :blockHeight="306"
          >
            <TrendLineChart
              :data="gdqsChartData1"
              :options="gdqsOptions"
              :initOption="gdqsInitOption"
            />
          </BlockBox>
        </div>
      </div>
    </div>
    <div class="map_box">
      <!-- <zlBg @emitMenu="emitMenu" /> -->
      <!-- <svgMap /> -->
      <LeaMap ref="leafletMap" @gridClick="gridClick" @poiClick="poiClick" />
    </div>
    <LeaderFooter :btns="btns" :activaIdx="activaIdx" @mark="mark" />

    <leftTooltip
      v-if="toolTipShow"
      :name1="toolName1"
      :name2="toolName2"
      :typeOptions1="typeOptions1"
      :typeOptions2="typeOptions2"
      @change="tooltipChange"
    />

    <EventDetail v-if="isShowEventDetail" @close="isShowEventDetail = false" />

    <CountryPart :isShow="isShowCountryPart" @close="closeCountryPart" @check="checkTree" />

    <yjPop v-if="yjPopShow" @close="yjPopShow = false" />
    <yjxqPop v-model="isyjxqShow" :list="yjxqList" @handle="eventInfoBtnClick5" />

    <!-- 基础信息 -->
    <BasicInformation
      v-model="basicInfomationShow"
      :title="basicInfomationTitle"
      :list="basicInfomationList"
      :btnShow="basicInfomationBtnShow"
      :btns="basicInfomationBtns"
      @handle="clickBasicInformationBtn"
    />

    <znzyPop v-model="znzyPopShow" @closeEmitai="znzyPopShow = false" />

    <njInfo
      v-if="njInfoShow"
      :agmachInfo="currentAgmachInfo"
      @handleClick="handleNjInfo"
      @closeEmitai="njInfoShow = false"
    />

    <historyWorkPop v-model="workPopShow" :agmachInfo="currentAgmachInfo" />
    <!-- <spjkPop v-if="isXlgcShow" @closeEmit="isXlgcShow = false" @moreXl="isXlgcShow1 = true" />
    <szyyPop v-if="isXlgcShow1" @closeEmit="isXlgcShow1 = false" />
    <wgyPop v-if="isWgllShow" @closeEmit="isWgllShow = false" />
    <dzzPop v-if="isdzzShow" @closeEmit="isdzzShow = false" />
    <xzqyPop v-if="isxzqyShow" @closeEmit="isxzqyShow = false" />
    <zwzxPop v-if="iszwzxShow" @closeEmit="iszwzxShow = false" />
    <zhzxPop v-if="iszhzxShow" @closeEmit="iszhzxShow = false" />
    <csglgdPop v-if="iscsglgdShow" @closeEmit="iscsglgdShow = false" />
    <shaqPop v-if="isshaqShow" @closeEmit="isshaqShow = false" />
    <sthbPop v-if="issthbShow" @closeEmit="issthbShow = false" />
    <whlyPop v-if="iswhlyShow" @closeEmit="iswhlyShow = false" />
    <yjfkPop v-if="isyjfkShow" @closeEmit="isyjfkShow = false" />
    <ggjtPop v-if="isggjtShow" @closeEmit="isggjtShow = false" />
    <zdqyPop v-if="iszdqyShow" @closeEmit="iszdqyShow = false" />
    <xxPop v-if="isxxShow" @closeEmit="isxxShow = false" />
    <wbPop v-if="iswbShow" @closeEmit="iswbShow = false" />
    <ylcsPop v-if="isylcsShow" @closeEmit="isylcsShow = false" />
    <hczPop v-if="ishczShow" @closeEmit="ishczShow = false" />
    <dyyPop v-if="isdyyShow" @closeEmit="isdyyShow = false" />
    <djylPop v-if="djylShow" @closeEmit="djylShow = false" />
    <bjxqPop v-if="bjxqShow" @closeEmit="bjxqShow = false" />
    <Area ref="area" v-show="showArea" :pop-area-info="popAreaInfo"></Area>
    <jjfztk v-if="jjfztkShow" @closeEmitai="jjfztkShow = false"></jjfztk>
    <hswhtk v-if="hswhtkShow" @closeEmitai="hswhtkShow = false"></hswhtk>
    <stjstk v-if="stjstkShow" @closeEmitai="stjstkShow = false"></stjstk>
    <wgzltk v-if="wgzltkShow" @closeEmitai="wgzltkShow = false"></wgzltk>-->
  </div>
</template>

<script>
import testData from '@/assets/json/cyjjPoint'
import BlockBox from '@/components/leader/common/blockBox6.vue'
import HeaderMain from '@/components/leader/common/headerMains'
import effectRankBarSwiper from './components/effectRankBar-swiper.vue'
import particle from './components/particle.vue'
import EventDetail from './components/EventDetail.vue'
import CountryPart from './components/CountryPart.vue'
import leaderMiddle from '@/components/leader/common/leaderMiddle.vue'
// import gdMap from '@/components/leader/gdMap/gdMap.vue'
import cesiumMap from '@/components/leader/cesiumMap/Cesium3dMap.vue'
// import cssjPoint from '@/assets/json/csts/cssjPoint.json'
// import spjkPoint from '@/assets/json/csts/spjkPoint.json'
// import csbjPoint from '@/assets/json/csts/csbjPoint.json'
// import ryllPoint from '@/assets/json/csts/ryllPoint.json'
import xzqhPoint from '@/assets/json/csts/xzqhPoint.json'
// import dzzPoint from '@/assets/json/csts/dzzPoint.json'
// import zwzxPoint from '@/assets/json/csts/zwzxPoint.json'
// import zzzxPoint from '@/assets/json/csts/zzzxPoint.json'
// import zdqyPoint from '@/assets/json/csts/zdqyPoint.json'
// import xxPoint from '@/assets/json/csts/xxPoint.json'
// import wbPoint from '@/assets/json/csts/wbPoint.json'
// import ylcsPoint from '@/assets/json/csts/ylcsPoint.json'
// import hczPoint from '@/assets/json/csts/hczPoint.json'
// import dyyPoint from '@/assets/json/csts/dyyPoint.json'
import LeaderFooter from '@/components/leader/common/leaderFooter3.vue'
import myRobot from '@/components/robot/myRobot.vue'
import NumScroll from '@/components/leader/num_scrolls'
import Area from '@/components/leader/leader_csts/area.vue'
import svgMap from '@/components/common/svgMap.vue'
import zlBg from '@/components/common/zlBg.vue'
import LivePlayer from '@liveqing/liveplayer'
import SwiperTable from '@/components/bjnj/SwiperTable.vue'
import LeaMap from '@/components/map/LeafletMapNj.vue'
import { getResourceList, getAllAlarmWithGPS, getCscpBasicHxItemCode } from '@/api/bjnj/zhdd.js'
import leftTooltip from '@/views/leader/components/mskx/leftTooltip.vue'
import yjPop from '@/components/bjnj/yjPop.vue'
import yjxqPop from '@/components/bjnj/yjxqPop.vue' //预警详情
import { BasicInformation } from './components/zhdd/dialog'
import znzyPop from '@/components/bjnj/znzyPop.vue'
import njInfo from '@/components/bjnj/njInfo.vue'
import historyWorkPop from '@/components/bjnj/historyWorkPop.vue'
import clusterData from '@/components/map/clusterData.json'

import rtc from '@/rhtx/core/index'
import { aqjgGetToken } from '@/api/hs/hs_aqjg.api.js'
import dayjs from 'dayjs'
import {
  getNjCount,
  getNjTodayCount,
  getNjOnlineCount,
  getNjCurrent7Count,
  getVehiclesCount,
  getNjAreaDayType,
  getAreaDistribute,
  getNjTrend,
  getNjLocation,
  getNjLocationrl,
  getDryingMap,
  getEmergencyMap,
  getNjLocations
} from '@/api/njzl/hs.api.js'

import 'swiper/css/swiper.css'
export default {
  name: 'Hszl',
  components: {
    BlockBox,
    HeaderMain,
    effectRankBarSwiper,
    particle,
    leaderMiddle,
    // gdMap,
    cesiumMap,
    LeaderFooter,
    EventDetail,
    CountryPart,
    myRobot,
    Area,
    NumScroll,
    // LandUse,
    // FullFactorGridEvents,
    // StreetSurvey,
    // jjfztk,
    // hswhtk,
    // stjstk,
    // wgzltk,
    svgMap,
    zlBg,
    LivePlayer,
    SwiperTable,
    LeaMap,
    leftTooltip,
    yjPop,
    BasicInformation,
    znzyPop,
    njInfo,
    historyWorkPop,
    yjxqPop
  },
  data () {
    return {
      areaId: '100000',
      resourceType: [
        {
          type: 'njfbType',
          flag: true
        },
        {
          type: 'yjjzType',
          flag: true
        },
        {
          type: 'fwdType',
          flag: true
        },
        {
          type: 'hgzxType',
          flag: true
        },
        // {
        //   type: 'yjwzType',
        //   flag: true
        // }
      ],
      workPopShow: false,
      njInfoShow: false,
      znzyPopShow: false,
      // 控制基本信息弹窗显示
      basicInfomationShow: false,
      // 基础信息标题
      basicInfomationTitle: '',
      // 基础信息内容
      basicInfomationList: [],
      // 控制基础信息按钮显示
      basicInfomationBtnShow: false,
      // 基础信息按钮
      basicInfomationBtns: [],
      yjPopShow: false,
      toolTipShow: false,
      toolName1: '预警类型',
      toolName2: '预警等级',
      typeOptions1: [
      ],
      typeOptions2: [
      ],
      btns: [
        {
          normalBg: require('@/assets/bjnj/btnImg1.png'),
          activeBg: require('@/assets/bjnj/btnImg2.png'),
          name: '热力图'
        },
        {
          normalBg: require('@/assets/bjnj/btnImg3.png'),
          activeBg: require('@/assets/bjnj/btnImg4.png'),
          name: '聚合图'
        }
      ],
      gdqsChartData1: [
        ['product', '事件'],
        ['1.1', 77],
        ['1.2', 97],
        ['1.3', 95],
        ['1.4', 81],
        ['1.5', 15],
        ['1.6', 6]
      ],
      gdqsChartData: [
        ['product', '事件'],
        ['1.1', 77],
        ['1.2', 97],
        ['1.3', 95],
        ['1.4', 81],
        ['1.5', 15],
        ['1.6', 6]
      ],
      gdqsOptions: {
        legend: {
          show: false
        },
        // unit: '台',
      },
      gdqsInitOption: {
        yAxis: {
          name: '台'
        },

      },
      sewageDataList1: [
        ['1', '光明农机公司', '4672', 22.87],
        ['2', '新希望农机公司', '4672', 18.87],
        ['3', '人本股份有限公司', '4672', 14.87],
        ['4', '新希望农机公司', '4672', 12.87],
        ['5', '光明农机公司', '4672', 22.87],
        ['6', '新希望农机公司', '4672', 18.87],
        ['7', '光明农机公司', '4672', 22.87],
        ['8', '新希望农机公司', '4672', 18.87],
        ['9', '人本股份有限公司', '4672', 14.87],
        ['10', '新希望农机公司', '4672', 12.87],
        ['11', '光明农机公司', '4672', 22.87],
        ['12', '新希望农机公司', '4672', 18.87]
      ],
      chartData: [
        ['product', '农机类型'],
        ['轮式拖拉机', 100],
        ['手扶拖拉机', 200],
        ['手扶拖拉机运输组', 150],
        ['履带拖拉机', 100],
        ['轮式拖拉机运输组', 200],
        ['轮式联合收割机', 150],
        ['履带式联合收割机', 150]
      ],
      chartDataOptions: {
        legend: {
          top: 2,
          itemWidth: 10,
          itemHeight: 10,
          itemRadius: 2,
          itemPadding: 5,
          itemDistance: 0,
          itemMarginTop: 12,
          textColor: 'rgba(255, 255, 255, 0.85)',
          fontWeight: '400',
          fontSize: '12px',
          fontFamily: 'DINPro-Medium',
          letterSpacing: '0'
        },
        position: ['50%', '10%'],
        bgImg: {
          width: '60%',
          height: '60%',
          top: '42%',
          left: '50%'
        },
        unit: '台',
        title: {
          fontSize: '16px',
          top: 60
        },
        subtitle: {
          fontSize: '14px',
          top: 80
        }
      },
      box1BottomData: {
        data: [
          ['product', '数量'],
          ['安徽', 50],
          ['河南', 90],
          ['湖南', 80],
          ['山东', 20],
          ['江西', 30],
          ['浙江', 40]
        ],
        options: {}
      },
      jjfztkShow: false,
      hswhtkShow: false,
      stjstkShow: false,
      wgzltkShow: false,
      man_active: require('@/assets/hszl/man_active.png'),
      man_normal: require('@/assets/hszl/man_normal.png'),
      woman_normal: require('@/assets/hszl/woman_normal.png'),
      woman_active: require('@/assets/hszl/woman_active.png'),
      jgzzShow: false,
      zdcsShow: false,
      djylShow: false,
      bjxqShow: false,
      showArea: false,
      isyjxqShow: false,
      yjxqList: {},
      popAreaInfo: {},
      areaPointList: [],
      jgzzActive: 0,
      zdcsActive: 0,
      csglTitleBtns: ['事件', '部件'],
      shbzTitleBtns: ['社会保险', '住房保险'],
      showCesiumMap: false,
      videoUrl: require('@/assets/leader/video/bg1.mp4'),
      // videoUrl: require('@/assets/leader/video/bg.mp4'),
      // data1: [
      //   {
      //     num: 55.08,
      //     tit: 'GDP地区生产总值',
      //     img: require('@/assets/csts/icon.png'),
      //     url: require('@/assets/hszl/icon1.png'),
      //     name: '亿元'
      //   },
      //   {
      //     num: 2.34,
      //     tit: '一般公共预算收入',
      //     img: require('@/assets/csts/icon.png'),
      //     url: require('@/assets/hszl/icon2.png'),
      //     name: '亿元'
      //   },
      //   {
      //     num: 11.09,
      //     tit: '全社会固定资产投资',
      //     img: require('@/assets/csts/icon.png'),
      //     url: require('@/assets/hszl/icon3.png'),
      //     name: '亿元'
      //   },
      //   {
      //     num: 40.62,
      //     tit: '规上工业生产总值',
      //     img: require('@/assets/csts/icon.png'),
      //     url: require('@/assets/hszl/icon4.png'),
      //     name: '亿元'
      //   },
      //   {
      //     num: 18.26,
      //     tit: '社会消费品零售总额',
      //     img: require('@/assets/csts/icon.png'),
      //     url: require('@/assets/hszl/icon5.png'),
      //     name: '元'
      //   },
      //   {
      //     num: 6.64,
      //     tit: '规上服务物业营业收入',
      //     img: require('@/assets/csts/icon.png'),
      //     url: require('@/assets/hszl/icon6.png'),
      //     name: '元'
      //   }
      // ],
      data1Url: {
        gdp: require('@/assets/hszl/icon1.png'),
        ybggys: require('@/assets/hszl/icon2.png'),
        gdzctz: require('@/assets/hszl/icon3.png'),
        gysczz: require('@/assets/hszl/icon4.png'),
        xflsze: require('@/assets/hszl/icon5.png'),
        fwyysr: require('@/assets/hszl/icon6.png')
      },
      // data2: [
      //   {
      //     tit: '参保人数',
      //     img: require('@/assets/csts/icon7.png'),
      //     cont: [
      //       {
      //         num: 49883,
      //         unit: '人'
      //       }
      //     ]
      //   },
      //   {
      //     tit: '参保缴费补贴',
      //     img: require('@/assets/csts/icon8.png'),
      //     cont: [
      //       {
      //         num: 13467,
      //         unit: '人'
      //       },
      //       {
      //         num: 2346.45,
      //         unit: '万'
      //       }
      //     ]
      //   },
      //   {
      //     tit: '养老金发放',
      //     img: require('@/assets/csts/icon9.png'),
      //     cont: [
      //       {
      //         num: 13467,
      //         unit: '人'
      //       },
      //       {
      //         num: 1277.75,
      //         unit: '万'
      //       }
      //     ]
      //   }
      // ],
      data3: [
        {
          name: '农机总量',
          num: '6000'
        },
        {
          name: '今日在线量',
          num: '2345'
        },
        {
          name: '近7日在线量',
          num: '55'
        }
      ],
      // data4: [
      //   {
      //     icon: require('@/assets/hszl/icon8.png'),
      //     label: '党委',
      //     count: 11
      //   },
      //   {
      //     icon: require('@/assets/hszl/icon9.png'),
      //     label: '党员',
      //     count: 3810
      //   },
      //   {
      //     icon: require('@/assets/hszl/icon10.png'),
      //     label: '党总支',
      //     count: 17
      //   },
      //   {
      //     icon: require('@/assets/hszl/icon11.png'),
      //     label: '直属党组织',
      //     count: 13
      //   },
      //   {
      //     icon: require('@/assets/hszl/icon12.png'),
      //     label: '党支部',
      //     count: 149
      //   },
      //   {
      //     icon: require('@/assets/hszl/icon13.png'),
      //     label: '二级党支部',
      //     count: 92
      //   }
      // ],
      data4Url: {
        djxxdw: require('@/assets/hszl/icon8.png'),
        djxxdy: require('@/assets/hszl/icon9.png'),
        djxxdzz: require('@/assets/hszl/icon10.png'),
        zsdzz: require('@/assets/hszl/icon11.png'),
        djxxdzb: require('@/assets/hszl/icon12.png'),
        djxxejdzb: require('@/assets/hszl/icon13.png')
      },
      data5_1: [
        {
          name: '应急救灾中心',
          num: '2290',
          unit: '个'
        },
        {
          name: '救灾机具',
          num: '2290',
          unit: '个'
        },
        {
          name: '机库棚',
          num: '7189',
          unit: '个'
        }
      ],
      data5_2: [
        {
          name: '应急救灾服务队',
          num: '96',
          unit: '个'
        },
        {
          name: '烘干中心',
          num: '2290',
          unit: '个'
        },
        {
          name: '机具保有量',
          num: '93',
          unit: '个'
        }
      ],
      data6: [
        {
          url: require('@/assets/csts/photo1.png'),
          title: '南京鸡鸣寺'
        },
        {
          url: require('@/assets/csts/photo1.png'),
          title: '南京鸡鸣寺'
        },
        {
          url: require('@/assets/csts/photo1.png'),
          title: '南京鸡鸣寺'
        }
      ],
      data7: [
        {
          num: 8,
          tit: '党工委 (个)'
        },
        {
          num: 10,
          tit: '党总支 (个)'
        },
        {
          num: 12,
          tit: '党支部 (个)'
        },
        {
          num: 2468,
          tit: '党员总数 (人)'
        }
      ],
      // data8: {
      //   data: [
      //     ['product', '年总数'],
      //     ['0-18', 10000],
      //     ['19-25', 10000],
      //     ['25-40', 10000],
      //     ['41-60', 10000],
      //     ['60岁以上', 10000]
      //   ],
      //   options: {
      //     colors: ['#2EF6FF', '#6D5AE2', '#2B8EF3', '#F7B13F', '#F5616F'],
      //     alpha: 65,
      //     pieSize: 220,
      //     pieInnerSize: 160,
      //     position: ['35%', '-50%'],
      //     bgImg: {
      //       top: '60%',
      //       left: '37%'
      //     },
      //     unit: '',
      //     title: {
      //       fontSize: '16px',
      //       top: 70,
      //       left: -20
      //       // textColor: 'rgba(255, 255, 255, 0)',
      //     },
      //     subtitle: {
      //       fontSize: '14px',
      //       top: 90
      //       // textColor: 'rgba(255, 255, 255, 0)',
      //     },
      //     legend: {
      //       orient: 'vertical',
      //       align: 'center',
      //       verticalAlign: 'bottom',
      //       top: -10,
      //       left: 160
      //     }
      //   }
      // },
      swiperOption: {
        observer: true,
        observeParents: true,
        autoHeight: true,
        spaceBetween: 0,
        autoplay: {
          delay: 3500,
          disableOnInteraction: false
        },
        slidesPerView: 'auto',
        grabCursor: true,
        autoplayDisableOnInteraction: false,
        mousewheelControl: true,
        pagination: {
          el: '.swiper-pagination'
        }
      },
      // data9_1: [
      //   {
      //     lab: 'PM2.5',
      //     num: 22,
      //     img: require('@/assets/csts/icon16.png')
      //   },
      //   {
      //     lab: 'SO2',
      //     num: 2,
      //     img: require('@/assets/csts/icon17.png')
      //   },
      //   {
      //     lab: 'CO',
      //     num: 0.7,
      //     img: require('@/assets/csts/icon18.png')
      //   }
      // ],
      // data9_2: [
      //   {
      //     lab: 'NO2',
      //     num: 28,
      //     img: require('@/assets/csts/icon19.png')
      //   },
      //   {
      //     lab: 'PM10',
      //     num: 30,
      //     img: require('@/assets/csts/icon20.png')
      //   },
      //   {
      //     lab: 'O3',
      //     num: 64,
      //     img: require('@/assets/csts/icon21.png')
      //   }
      // ],
      data10_1: [
        {
          lab: '警员(人)',
          num: 51,
          img: require('@/assets/csts/icon22.png')
        },
        {
          lab: '辅警(人)',
          num: 549,
          img: require('@/assets/csts/icon23.png')
        },
        {
          lab: '警车',
          num: 368,
          img: require('@/assets/csts/icon24.png')
        }
      ],
      data10_2: {
        data: [
          ['product', '事件总数'],
          ['群体性事件', 1245],
          ['恐怖袭击事件', 1245],
          ['刑事案件', 1245],
          ['信息安全事件', 1245]
        ],
        options: {
          colors: ['#80CF83', '#CDCF72', '#0280DF', '#13A4C0'],
          legend: {
            top: 15
          },
          bgImg: {
            width: '70%',
            height: '70%',
            top: '51%',
            left: '50%'
          },
          unit: '件',
          title: {
            fontSize: '16px',
            top: 70
          },
          subtitle: {
            fontSize: '14px',
            top: 90
          }
        }
      },
      data11: [
        ['product', '系列名'],
        ['当前车流量', 23],
        ['当前拥堵路段', 9],
        ['当前违章数', 18],
        ['当前警情', 11]
      ],
      data12: {
        data: [
          ['product', '救援成功', '隐患整改'],
          ['2017', 500, 310],
          ['2018', 310, 102],
          ['2019', 320, 190],
          ['2020', 350, 176],
          ['2021', 360, 290],
          ['2022', 250, 161]
        ],
        options: {
          // 颜色数据
          color: ['#ffd11a', 'rgba(46,200,207,1)'],
          // 柱子的宽度
          barWidth: 20,
          // 柱子X轴位置
          symbolOffsetX: ['-30%', '30%'],
          // 顶部的大小
          symbolSize: [20, 11]
        }
      },
      activaIdx: -1,
      isShowEventDetail: false,
      isShowCountryPart: false,
      isXlgcShow: false,
      isXlgcShow1: false,
      isWgllShow: false,
      isdzzShow: false,
      isxzqyShow: false,
      iszwzxShow: false,
      iszhzxShow: false,
      iscsglgdShow: false,
      isshaqShow: false,
      issthbShow: false,
      iswhlyShow: false,
      isyjfkShow: false,
      isggjtShow: false,
      iszdqyShow: false,
      isxxShow: false,
      iswbShow: false,
      isylcsShow: false,
      ishczShow: false,
      isdyyShow: false,
      introUrl: '',
      // introWord: '',
      // peoTotal: '',
      // maleNum: '',
      // femaleNum: '',
      // maleNumRate: '',
      // femaleNumRate: '',
      // qyswgTotal: 0,
      wgcurrentMonth: 8,
      wgsjmonthList: [],
      currentAgmachInfo: {},
      weatherAlarmType: "",
      weatherAlarmLevel: "",
    }
  },
  created () {
    // this.getHsjjVideoFn()
    // this.getHsjjFn()
    // this.getJjglFn()
    // this.getKqzlFn()
    // this.getDjxxFn()
    // this.getRkglFn()
    // this.getRkglAgeFn()
    // this.getQyswgsjFn()
  },
  beforeDestroy () {
    window.clearInterval(this.timer)
  },
  mounted () {
    // 获取滚动容器和文本元素
    // const scrollingText = document.querySelector('.introduce')
    // const text = document.querySelector('.introduce p')
    // // 获取文本元素的高度
    // const textHeight = text.offsetHeight

    // 定义滚动函数
    // function scroll() {
    //   // console.log('scrollingText.scrollTop',scrollingText.scrollTop);
    //   // 如果文本元素已经完全滚动出去，则将其重置到滚动容器的顶部
    //   if (scrollingText.scrollTop >= 96) {
    //     scrollingText.scrollTop = 0
    //   }
    //   // 否则，将滚动容器向上滚动一个像素的距离
    //   else {
    //     scrollingText.scrollTop += 1
    //   }
    // }

    // 每隔20毫秒调用一次滚动函数
    // this.timer = window.setInterval(scroll, 100)

    // // 获取安全监管系统的token
    // this.aqjgGetTokenFn()

    this.getNjAreaDayType()
    this.getNjCount()
    this.getNjTodayCount()
    this.getNjOnlineCount()
    this.getNjCurrent7Count()
    this.getVehiclesCount()
    this.getAreaDistribute()
    this.getNjTrend()
    this.getResourceList()

    this.getCscpBasicHxItemCode1('weatherAlarmType')
    this.getCscpBasicHxItemCode2('weatherAlarmLevel')
  },
  watch: {},
  computed: {
    formatNumber () {
      return function (param) {
        console.log(param)
        if (this.isEmpty(param) || param.length < 4 || isNaN(param)) {
          return param
        }

        let small = ''
        let isSmall = false
        if (param.split('.').length === 2) {
          // 有小数
          isSmall = true
          small = param.split('.')[1]
          param = param.split('.')[0]
        }

        let result = ''

        while (param.length > 3) {
          result = ',' + param.slice(-3) + result
          param = param.slice(0, param.length - 3)
        }

        if (param) {
          result = param + result
        }

        if (isSmall) {
          result = result + '.' + small
        }
        console.log(result)
        return result
      }
    },
    initOptions1 () {
      return {
        yAxis: {
          name: '台',
          nameTextStyle: {
            fontSize: 12,
          },
          axisLabel: {
            textStyle: {
              fontSize: 12,
            },
          },
        },
        xAxis: {
          axisLabel: {
            show: true,
            textStyle: {
              fontSize: 12,
            },
          },
        },
        dataZoom: [
          {
            type: 'inside', // 内置型式的 dataZoom 组件
            xAxisIndex: 0, // 对应 x 轴的索引，默认为 0
            start: 0, // 起始位置的百分比
            end: 12, // 结束位置的百分比
            realtime: true // 启用实时滚动
          },
        ],
      }
    },
  },
  methods: {
    async getCscpBasicHxItemCode1 (data) {
      let res = await getCscpBasicHxItemCode(data)
      console.log(res)
      if (res?.code == '0') {
        this.typeOptions1 = res.data
      }
    },
    async getCscpBasicHxItemCode2 (data) {
      let res = await getCscpBasicHxItemCode(data)
      console.log(res)
      if (res?.code == '0') {
        this.typeOptions2 = res.data
      }
    },
    async getDryingMap () {
      let res = await getDryingMap()
      let data = res.data.map(item => {
        return {
          latlng: [item.latitude, item.longitude],
          icon: {
            iconUrl: require('@/assets/mskx/icon_hgzx_b.png'),
            iconSize: [47, 62],
            iconAnchor: [16, 42]
          },
          props: {
            id: 'hgzxId',
            type: 'hgzxType',
            info: { ...item }
          }
        }
      })
      this.$refs.leafletMap.drawPoiMarker(data, 'hgzxType', true)
    },
    async getEmergencyMap (val = 1) {
      let res = await getEmergencyMap({ type: val })
      if (val == 1) {
        // 救灾中心
        let data = res.data.map(item => {
          return {
            latlng: [item.latitude, item.longitude],
            icon: {
              iconUrl: require('@/assets/mskx/icon_yjjz_b.png'),
              iconSize: [47, 62],
              iconAnchor: [16, 42]
            },
            props: {
              id: 'yjjzId',
              type: 'yjjzType',
              info: { ...item }
            }
          }
        })
        this.$refs.leafletMap.drawPoiMarker(data, 'yjjzType', true)
      } else if (val == 2) {
        // 服务队
        let data = res.data.map(item => {
          return {
            latlng: [item.latitude, item.longitude],
            icon: {
              iconUrl: require('@/assets/mskx/icon_fwd_b.png'),
              iconSize: [47, 62],
              iconAnchor: [16, 42]
            },
            props: {
              id: 'fwdId',
              type: 'fwdType',
              info: { ...item }
            }
          }
        })
        this.$refs.leafletMap.drawPoiMarker(data, 'fwdType', true)
      }
    },
    handleNjInfo () {
      this.njInfoShow = false
      // this.znzyPopShow = true
      this.workPopShow = true
    },
    // 标记点基础信息弹窗内按钮点击事件
    clickBasicInformationBtn (i, name) {
      console.log(i, name)
      if (name == '应急救灾中心详情') {
        this.basicInfomationShow = false
        this.znzyPopShow = true
      }
    },
    async getResourceList () {
      let res = await getResourceList()
      if (res?.code == '0') {
        this.data5_1[0].num = res.data.centerNum
        this.data5_1[1].num = res.data.serveNum
        this.data5_1[2].num = res.data.machineryNum
        this.data5_1[3].num = res.data.warehouseNum
        this.data5_1[4].num = res.data.dryingCenterNum
      }
    },
    async aqjgGetTokenFn () {
      const res = await aqjgGetToken()
      if (res?.data) {
        localStorage.setItem('aqjgToken', res.data.access_token)
      }
    },
    async getNjCount () {
      let res = await getNjCount()
      this.data3[0].num = res.data[0].count
    },
    async getNjTodayCount () {
      let res = await getNjTodayCount()
      this.data3[1].num = res.data[0].count
    },
    async getNjOnlineCount () {
      let params = {
        startDate: dayjs().subtract(7, 'days').format('YYYY-MM-DD'),
        endDate: dayjs().format('YYYY-MM-DD')
      }
      let res = await getNjOnlineCount(params)
      this.data3[2].num = res.data[0].count
    },
    async getNjCurrent7Count () {
      let params = {
        startDate: dayjs().subtract(7, 'days').format('YYYY-MM-DD'),
        endDate: dayjs().format('YYYY-MM-DD')
      }
      let res = await getNjCurrent7Count(params)
      if (res.data?.length > 0) {
        this.gdqsChartData = [['product', '在线量']].concat(res.data.map(item => {
          return [item.date,
          item.count]
        }))
      }
    },
    async getVehiclesCount () {
      this.sewageDataList1 = []
      let res = await getVehiclesCount()
      let count = res.data.slice(0, 30)
      this.sewageDataList1 = count.map((item, index) => {
        return [index + 1, item.proEntName, item.totalCount, item.rate]
      })
    },
    async getNjAreaDayType () {
      let params = {
        "areaId": '',
        "day": dayjs().subtract(7, 'days').format('YYYY-MM-DD'),
        "typeLevel": 1
      }
      let res = await getNjAreaDayType(params)
      if (res.data?.length > 0) {
        this.chartData = [['product', '农机类型']].concat(res.data.map(item => {
          return [item.agmachTypeName, item.count]
        }))
      }
    },
    async getAreaDistribute () {
      let params = {
        day: '2024-01-18',
        // "day": dayjs().subtract(1, 'days').format('YYYY-MM-DD'),
        "level": 1
      }
      let res = await getAreaDistribute(params)
      this.box1BottomData.data = [['product', '数量']].concat(res.data.map(item => {
        return [item.areaName, item.count]
      }))
    },
    // 近30日的趋势
    async getNjTrend () {
      let params = {
        "agmachTypeCode": 150000,
        "startDate": dayjs().subtract(180, 'days').format('YYYY-MM-DD'),
        "endDate": dayjs().format('YYYY-MM-DD'),
        "groupLevel": 2
      }
      let res = await getNjTrend(params)
      if (res.data?.length > 0) {
        this.gdqsChartData1 = [['product', '数量']].concat(res.data.map(item => {
          return [item.date, item.count]
        }))
      }
    },
    async getNjLocationrl () {
      let res = await getNjLocationrl({ page_no: 1,page_size: 400000 })
      console.log(res)
      let heatData = []
      heatData = [].concat(
        res.data[0].rows.map((it, i) => [it.lon, it.lat])
      )
      this.$refs.leafletMap.loadHeatLayer('heat', heatData)
    },
    async getNjLocation () {
      let res = await getNjLocation({ page_no: 1,page_size: 20000 })
      console.log(res)
      let data = res.data[0].rows.map(item => {
        return {
          latlng: [item.lat, item.lon],
          icon: {
            iconUrl: require('@/assets/mskx/icon_nj_b.png'),
            iconSize: [47, 62],
            iconAnchor: [16, 42]
          },
          props: {
            id: 'njfbId',
            type: 'njfbType',
            info: { ...item }
          }
        }
      })
      this.$refs.leafletMap.drawPoiMarker(data, 'njfbType', true)
    },
    // watchFlag () {
    //   let flag = this.$route.query.flag?.substr(0, 4)
    //   this.djylShow = false
    //   this.iscsglgdShow = false
    //   switch (flag) {
    //     case 'djyl':
    //       this.djylShow = true
    //       break
    //     case 'csgl':
    //       this.iscsglgdShow = true
    //       break
    //     default:
    //       break
    //   }
    // },
    // csglBulletFrame (val) {
    //   this.iscsglgdShow = val
    // },
    // shaqBulletFrame (val) {
    //   this.isshaqShow = val
    // },
    // sthbBulletFrame (val) {
    //   this.issthbShow = val
    // },
    // djylBulletFrame (val) {
    //   this.djylShow = val
    // },
    // whlyBulletFrame (val) {
    //   this.iswhlyShow = val
    // },
    // yjfkBulletFrame (val) {
    //   this.isyjfkShow = val
    // },
    // ggjtBulletFrame (val) {
    //   this.isggjtShow = val
    // },
    // shbzBtnMehtod (val) {
    //   console.log(val)
    //   if (val == 0) {
    //     this.data2 = [
    //       {
    //         tit: '参保人数',
    //         img: require('@/assets/csts/icon7.png'),
    //         cont: [
    //           {
    //             num: 49883,
    //             unit: '人'
    //           }
    //         ]
    //       },
    //       {
    //         tit: '参保缴费补贴',
    //         img: require('@/assets/csts/icon8.png'),
    //         cont: [
    //           {
    //             num: 13467,
    //             unit: '人'
    //           },
    //           {
    //             num: 2346.45,
    //             unit: '万'
    //           }
    //         ]
    //       },
    //       {
    //         tit: '养老金发放',
    //         img: require('@/assets/csts/icon9.png'),
    //         cont: [
    //           {
    //             num: 13467,
    //             unit: '人'
    //           },
    //           {
    //             num: 1277.75,
    //             unit: '万'
    //           }
    //         ]
    //       }
    //     ]
    //   } else if (val == 1) {
    //     this.data2 = [
    //       {
    //         tit: '实施保障户',
    //         img: require('@/assets/csts/icon37.png'),
    //         cont: [
    //           {
    //             num: 13.467,
    //             unit: '户'
    //           },
    //           {
    //             num: 2346,
    //             unit: '人'
    //           }
    //         ]
    //       },
    //       {
    //         tit: '廉租房',
    //         img: require('@/assets/csts/icon38.png'),
    //         cont: [
    //           {
    //             num: 13.467,
    //             unit: '户'
    //           },
    //           {
    //             num: 2346,
    //             unit: '人'
    //           }
    //         ]
    //       },
    //       {
    //         tit: '公租房',
    //         img: require('@/assets/csts/icon39.png'),
    //         cont: [
    //           {
    //             num: 13.467,
    //             unit: '户'
    //           },
    //           {
    //             num: 2346,
    //             unit: '人'
    //           }
    //         ]
    //       }
    //     ]
    //   }
    // },
    jgzz (index, sourceType) {
      this.jgzzActive = index
      // this.removeAllPoi()
      this.toolTipShow = false
      if (sourceType) {
        // dom点击事件来源
        this.resourceType[index].flag = !this.resourceType[index].flag
      }
      if (index == 0 && this.resourceType[0].flag) {
        this.getNjLocation()
        this.toolTipShow = true
        this.toolName1 = '农机类型'
        this.toolName2 = '基础农机'

        // let poiArr1 = [
        //   {
        //     latlng: [31.152411, 117.986739],
        //     icon: {
        //       iconUrl: require('@/assets/mskx/icon_nj_b.png'),
        //       iconSize: [47, 62],
        //       iconAnchor: [16, 42]
        //     },
        //     props: {
        //       id: 'njfbId',
        //       type: 'njfbType'
        //     }
        //   },
        //   {
        //     latlng: [33.752378, 118.936309],
        //     icon: {
        //       iconUrl: require('@/assets/mskx/icon_nj_b.png'),
        //       iconSize: [47, 62],
        //       iconAnchor: [16, 42]
        //     },
        //     props: {
        //       id: 'njfbId',
        //       type: 'njfbType'
        //     }
        //   },
        //   {
        //     latlng: [30.263897, 118.969401],
        //     icon: {
        //       iconUrl: require('@/assets/mskx/icon_nj_b.png'),
        //       iconSize: [47, 62],
        //       iconAnchor: [16, 42]
        //     },
        //     props: {
        //       id: 'njfbId',
        //       type: 'njfbType'
        //     }
        //   }
        // ]
        // this.$refs.leafletMap.drawPoiMarker(poiArr1, 'njfbType', true)
      } else if (index == 0 && !this.resourceType[0].flag) {
        this.resourceType[0].flag = false
        console.log(this.resourceType[0].flag, 66667)
        this.$refs.leafletMap.removeLayer('njfbType')
      }

      if (index == 1 && this.resourceType[1].flag) {
        // 救灾中心 1
        this.getEmergencyMap(1)
        // let poiArr1 = [
        //   {
        //     latlng: [31.152411, 117.986739],
        //     icon: {
        //       iconUrl: require('@/assets/mskx/icon_yjjz_b.png'),
        //       iconSize: [47, 62],
        //       iconAnchor: [16, 42]
        //     },
        //     props: {
        //       id: 'yjjzId',
        //       type: 'yjjzType'
        //     }
        //   },
        //   {
        //     latlng: [33.752378, 118.936309],
        //     icon: {
        //       iconUrl: require('@/assets/mskx/icon_yjjz_b.png'),
        //       iconSize: [47, 62],
        //       iconAnchor: [16, 42]
        //     },
        //     props: {
        //       id: 'yjjzId',
        //       type: 'yjjzType'
        //     }
        //   },
        //   {
        //     latlng: [30.263897, 118.969401],
        //     icon: {
        //       iconUrl: require('@/assets/mskx/icon_yjjz_b.png'),
        //       iconSize: [47, 62],
        //       iconAnchor: [16, 42]
        //     },
        //     props: {
        //       id: 'yjjzId',
        //       type: 'yjjzType'
        //     }
        //   }
        // ]
        // this.$refs.leafletMap.drawPoiMarker(poiArr1, 'yjjzType', true)
      } else if (index == 1 && !this.resourceType[1].flag) {
        this.resourceType[1].flag = false
        this.$refs.leafletMap.removeLayer('yjjzType')
      }

      if (index == 2 && this.resourceType[2].flag) {
        // 服务队
        this.getEmergencyMap(2)
        // let poiArr1 = [
        //   {
        //     latlng: [31.152411, 117.986739],
        //     icon: {
        //       iconUrl: require('@/assets/mskx/icon_fwd_b.png'),
        //       iconSize: [47, 62],
        //       iconAnchor: [16, 42]
        //     },
        //     props: {
        //       id: 'fwdId',
        //       type: 'fwdType'
        //     }
        //   },
        //   {
        //     latlng: [33.752378, 118.936309],
        //     icon: {
        //       iconUrl: require('@/assets/mskx/icon_fwd_b.png'),
        //       iconSize: [47, 62],
        //       iconAnchor: [16, 42]
        //     },
        //     props: {
        //       id: 'fwdId',
        //       type: 'fwdType'
        //     }
        //   },
        //   {
        //     latlng: [30.263897, 118.969401],
        //     icon: {
        //       iconUrl: require('@/assets/mskx/icon_fwd_b.png'),
        //       iconSize: [47, 62],
        //       iconAnchor: [16, 42]
        //     },
        //     props: {
        //       id: 'fwdId',
        //       type: 'fwdType'
        //     }
        //   }
        // ]
        // this.$refs.leafletMap.drawPoiMarker(poiArr1, 'fwdType', true)
      } else if (index == 2 && !this.resourceType[2].flag) {
        this.resourceType[2].flag = false

        this.$refs.leafletMap.removeLayer('fwdType')
      }

      if (index == 3 && this.resourceType[3].flag) {
        this.getDryingMap()
        // let poiArr1 = [
        //   {
        //     latlng: [31.152411, 117.986739],
        //     icon: {
        //       iconUrl: require('@/assets/mskx/icon_hgzx_b.png'),
        //       iconSize: [47, 62],
        //       iconAnchor: [16, 42]
        //     },
        //     props: {
        //       id: 'hgzxId',
        //       type: 'hgzxType'
        //     }
        //   },
        //   {
        //     latlng: [33.752378, 118.936309],
        //     icon: {
        //       iconUrl: require('@/assets/mskx/icon_hgzx_b.png'),
        //       iconSize: [47, 62],
        //       iconAnchor: [16, 42]
        //     },
        //     props: {
        //       id: 'hgzxId',
        //       type: 'hgzxType'
        //     }
        //   },
        //   {
        //     latlng: [30.263897, 118.969401],
        //     icon: {
        //       iconUrl: require('@/assets/mskx/icon_hgzx_b.png'),
        //       iconSize: [47, 62],
        //       iconAnchor: [16, 42]
        //     },
        //     props: {
        //       id: 'hgzxId',
        //       type: 'hgzxType'
        //     }
        //   }
        // ]
        // this.$refs.leafletMap.drawPoiMarker(poiArr1, 'hgzxType', true)
      } else if (index == 3 && !this.resourceType[3].flag) {
        this.resourceType[3].flag = false
        this.$refs.leafletMap.removeLayer('hgzxType')
      }

      // if (index == 4 && this.resourceType[4].flag) {
      //   let poiArr1 = [
      //     {
      //       latlng: [31.152411, 117.986739],
      //       icon: {
      //         iconUrl: require('@/assets/mskx/icon_yjwz_b.png'),
      //         iconSize: [47, 62],
      //         iconAnchor: [16, 42]
      //       },
      //       props: {
      //         id: 'yjwzId',
      //         type: 'yjwzType'
      //       }
      //     },
      //     {
      //       latlng: [33.752378, 118.936309],
      //       icon: {
      //         iconUrl: require('@/assets/mskx/icon_yjwz_b.png'),
      //         iconSize: [47, 62],
      //         iconAnchor: [16, 42]
      //       },
      //       props: {
      //         id: 'yjwzId',
      //         type: 'yjwzType'
      //       }
      //     },
      //     {
      //       latlng: [30.263897, 118.969401],
      //       icon: {
      //         iconUrl: require('@/assets/mskx/icon_yjwz_b.png'),
      //         iconSize: [47, 62],
      //         iconAnchor: [16, 42]
      //       },
      //       props: {
      //         id: 'yjwzId',
      //         type: 'yjwzType'
      //       }
      //     }
      //   ]
      //   this.$refs.leafletMap.drawPoiMarker(poiArr1, 'yjwzType', true)
      // } else if (index == 4 && !this.resourceType[4].flag) {
      //   this.resourceType[4].flag = false
      //   this.$refs.leafletMap.removeLayer('yjwzType')
      // }
    },
    zdcs (index) {
      this.isWgllShow = false
      this.isdzzShow = false
      this.isxzqyShow = false
      this.iszwzxShow = false
      this.iszhzxShow = false
      this.isShowEventDetail = false
      this.isXlgcShow = false
      this.iszdqyShow = false
      this.isxxShow = false
      this.iswbShow = false
      this.isylcsShow = false
      this.ishczShow = false
      this.isdyyShow = false
      this.bjxqShow = false
      console.log(index)
      this.zdcsActive = index
      this.$refs.CesiumMapRef.removeAllLayer() //移除所有点位
      if (index == 0) {
        // 地图打点
        let data = zdqyPoint.features.map(e => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'zdqyPoint')
      } else if (index == 1) {
        // 地图打点
        let data = xxPoint.features.map(e => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'xxPoint')
      } else if (index == 2) {
        // 地图打点
        let data = wbPoint.features.map(e => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'wbPoint')
      } else if (index == 3) {
        // 地图打点
        let data = ylcsPoint.features.map(e => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'ylcsPoint')
      } else if (index == 4) {
        // 地图打点
        let data = hczPoint.features.map(e => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'hczPoint')
      } else if (index == 5) {
        // 地图打点
        let data = dyyPoint.features.map(e => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'dyyPoint')
      }
    },
    // bubble (data) {
    //   this.isWgllShow = false
    //   this.isdzzShow = false
    //   this.isxzqyShow = false
    //   this.iszwzxShow = false
    //   this.iszhzxShow = false
    //   this.isShowEventDetail = false
    //   this.isXlgcShow = false
    //   this.iszdqyShow = false
    //   this.isxxShow = false
    //   this.iswbShow = false
    //   this.isylcsShow = false
    //   this.ishczShow = false
    //   this.isdyyShow = false
    //   this.bjxqShow = false
    //   if (data.id === 'ryll') {
    //     this.isWgllShow = true
    //   }
    //   if (data.id === 'dzz') {
    //     this.isdzzShow = true
    //   }
    //   if (data.id === 'xzqh') {
    //     this.isxzqyShow = true
    //   }
    //   if (data.id === 'zwzx') {
    //     this.iszwzxShow = true
    //   }
    //   if (data.id === 'zhzx') {
    //     this.iszhzxShow = true
    //   }
    //   if (data.id === 'cssj') {
    //     this.isShowEventDetail = true
    //   }
    //   if (data.id === 'spjk') {
    //     this.isXlgcShow = true
    //   }
    //   if (data.id === 'zdqy') {
    //     this.iszdqyShow = true
    //   }
    //   if (data.id === 'xx') {
    //     this.isxxShow = true
    //   }
    //   if (data.id === 'wb') {
    //     this.iswbShow = true
    //   }
    //   if (data.id === 'ylcs') {
    //     this.isylcsShow = true
    //   }
    //   if (data.id === 'hcz') {
    //     this.ishczShow = true
    //   }
    //   if (data.id === 'dyy') {
    //     this.isdyyShow = true
    //   }
    //   if (data.id === 'bjxq') {
    //     this.bjxqShow = true
    //   }
    // },
    eventInfoBtnClick5 (i) {
      console.log(i)
    },
    poiClick (layerId, it) {
      console.log(layerId, it)
      let info = it.props.info
      if (layerId == 'jcdwType') {
        this.yjxqList = [
          0,
          it.props.info.time,
          it.props.info.type,
          it.props.info.area,
          it.props.info.level,
          it.props.info.detail,
          it.props.info.title,
        ]
        this.isyjxqShow = true
        // this.yjPopShow = true
      } else if (layerId == 'njfbType') {
        this.njInfoShow = true
        this.currentAgmachInfo = it.props.info
      } else if (layerId == 'yjjzType') {
        this.basicInfomationShow = true
        this.basicInfomationTitle = '应急救灾中心详情'
        this.basicInfomationList = [
          {
            label: '中心名称：',
            value: info.name || '-'
          },
          {
            label: '联系人：',
            value: info.contact || '-'
          },
          {
            label: '联系电话：',
            value: info.phone || '-'
          },
          {
            label: '所属区域：',
            value: info.area || '-'
          },
          {
            label: '地址：',
            value: info.address || '-'
          }
        ]
        this.basicInfomationBtns = ['人员机具明细']
        this.basicInfomationBtnShow = true
      } else if (layerId == 'fwdType') {
        this.basicInfomationShow = true
        this.basicInfomationTitle = '应急救灾服务队详情'
        this.basicInfomationList = [
          {
            label: '中心名称：',
            value: info.name || '-'
          },
          {
            label: '联系人：',
            value: info.contact || '-'
          },
          {
            label: '联系电话：',
            value: info.phone || '-'
          },
          {
            label: '队伍人数：',
            value: info.num || '-'
          },
          {
            label: '所属区域：',
            value: info.area || '-'
          },
          {
            label: '地址：',
            value: info.address || '-'
          }
        ]
        this.basicInfomationBtnShow = false
      } else if (layerId == 'hgzxType') {
        this.basicInfomationShow = true
        this.basicInfomationTitle = '烘干中心详情'
        this.basicInfomationList = [
          {
            label: '站点名称：',
            value: info.name || '-'
          },
          {
            label: '烘干能力：',
            value: info.talent || '-'
          },
          {
            label: '联系人：',
            value: info.contact || '-'
          },
          {
            label: '联系电话：',
            value: info.phone || '-'
          },
          {
            label: '所属区域：',
            value: info.area || '-'
          },
          {
            label: '地址：',
            value: info.area || '-'
          }
        ]
        this.basicInfomationBtnShow = false
      } else if (layerId == 'yjwzType') {
        this.basicInfomationShow = true
        this.basicInfomationTitle = '机具保有量详情'
        this.basicInfomationList = [
          {
            label: '中心名称：',
            value: '光明应急救灾中心'
          },
          {
            label: '联系人：',
            value: '王建国'
          },
          {
            label: '联系电话：',
            value: '16788865445'
          },
          {
            label: '所属区域：',
            value: 'xxxxxx'
          },
          {
            label: '地址：',
            value: 'xxxxxx'
          }
        ]
        this.basicInfomationBtnShow = false
      }
    },
    gridClick (properties) {
      console.log('properties',properties)
      this.areaId = properties.adcode
      this.removeAllPoi()
      if (this.activaIdx == 0) {
        // this.getNjLocationrl()
        if (this.areaId == '100000') {
          this.$refs.leafletMap.loadHeatLayer('heat', clusterData)
        } else {
          this.getNjLocations1()
        }
      } else if (this.activaIdx == 1) {
        // this.getNjLocation()
        if (this.areaId == '100000') {
          this.$refs.leafletMap.drawBigDataClusterPoint(clusterData, require('@/assets/mskx/icon_nj_b.png'), [48, 62])
        } else {
          this.getNjLocations2()
        }
      }
    },
    removeAllPoi () {
      this.$refs.leafletMap.removeLayer('jcdwType')
      this.$refs.leafletMap.removeLayer('njfbType')
      this.$refs.leafletMap.removeLayer('hgzxType')
      this.$refs.leafletMap.removeLayer('yjwzType')
      this.$refs.leafletMap.removeLayer('yjjzType')
      this.$refs.leafletMap.removeLayer('fwdType')
      this.$refs.leafletMap.removeLayer('heat')
      this.$refs.leafletMap.clearCluster()
    },
    mark (i) {
      this.removeAllPoi()
      this.activaIdx = i
      if (this.activaIdx == 0) {
        // this.getNjLocationrl()
        if (this.areaId == '100000') {
          this.$refs.leafletMap.loadHeatLayer('heat', clusterData)
        } else {
          this.getNjLocations1()
        }
      } else if (this.activaIdx == 1) {
        // this.getNjLocation()
        if (this.areaId == '100000') {
          this.$refs.leafletMap.drawBigDataClusterPoint(clusterData, require('@/assets/mskx/icon_nj_b.png'), [48, 62])
        } else {
          this.getNjLocations2()
        }
      }
      // this.jgzzShow = false
      // this.activaIdx = i
      // this.toolTipShow = false
      // this.removeAllPoi()
      // for (let key in this.resourceType) {
      //   this.resourceType[key].flag = true
      // }

      // if (i == 0) {
      //   this.toolName1 = '预警类型'
      //   this.toolName2 = '预警等级'
      //   this.toolTipShow = true
      //   this.getAllAlarmWithGPS()
      //   // let poiArr1 = [
      //   //   {
      //   //     latlng: [31.152411, 117.986739],
      //   //     icon: {
      //   //       iconUrl: require('@/assets/mskx/icon_yj_y.png'),
      //   //       iconSize: [65, 62],
      //   //       iconAnchor: [16, 42]
      //   //     },
      //   //     props: {
      //   //       id: 'jcdwId',
      //   //       type: 'jcdwType'
      //   //     }
      //   //   },
      //   //   {
      //   //     latlng: [33.752378, 118.936309],
      //   //     icon: {
      //   //       iconUrl: require('@/assets/mskx/icon_yj_y.png'),
      //   //       iconSize: [65, 62],
      //   //       iconAnchor: [16, 42]
      //   //     },
      //   //     props: {
      //   //       id: 'jcdwId',
      //   //       type: 'jcdwType'
      //   //     }
      //   //   },
      //   //   {
      //   //     latlng: [30.263897, 118.969401],
      //   //     icon: {
      //   //       iconUrl: require('@/assets/mskx/icon_yj_y.png'),
      //   //       iconSize: [65, 62],
      //   //       iconAnchor: [16, 42]
      //   //     },
      //   //     props: {
      //   //       id: 'jcdwId',
      //   //       type: 'jcdwType'
      //   //     }
      //   //   }
      //   // ]
      //   // this.$refs.leafletMap.drawPoiMarker(poiArr1, 'jcdwType', true)

      // } else if (i == 1) {
      //   this.jgzzShow = true
      //   for (let key in this.resourceType) {
      //     if (this.resourceType[key].flag) {
      //       this.jgzz(key, false)
      //     }
      //   }
      // }
    },
    async getNjLocations1 () {
      let res = await getNjLocations({
        parentAreald: this.areaId,
      })
      console.log(res)
      this.$refs.leafletMap.loadHeatLayer('heat', res.data)
    },
    async getNjLocations2 () {
      let res = await getNjLocations({
        parentAreald: this.areaId,
      })
      console.log(res)
    },
    tooltipChange (e) {
      if (e.type == 'weatherAlarmType') {
        this.weatherAlarmType = e.value
      } else if (e.type == 'weatherAlarmLevel') {
        this.weatherAlarmLevel = e.value
      }
      this.removeAllPoi()
      this.getAllAlarmWithGPS()
    },
    async getAllAlarmWithGPS () {
      let res = await getAllAlarmWithGPS({
        weatherAlarmType: this.weatherAlarmType,
        weatherAlarmLevel: this.weatherAlarmLevel,
      })
      console.log(res)
      let data = res.data.map(item => {
        return {
          latlng: [item.latitude, item.longitude],
          icon: {
            iconUrl: require('@/assets/mskx/icon_yj_y.png'),
            iconSize: [65, 62],
            iconAnchor: [16, 42]
          },
          props: {
            id: 'jcdwId',
            type: 'jcdwType',
            info: { ...item }
          }
        }
      })
      this.$refs.leafletMap.drawPoiMarker(data, 'jcdwType', true)
    },
    mapLoad () {
      this.loadCarLayer('area')
      this.$refs.map.autoShowInfoWindow(this.areaPointList)
    },

    // 加载重点项目
    loadCarLayer (layerId) {
      if (layerId == 'area') {
        this.areaPointList = testData.markersArea.map(item => ({
          ...item,
          popup: this.$refs.area.$el,
          onclick: () => {
            this.popAreaInfo = {
              ...item.properties
            }
            this.showArea = true
          }
        }))
        this.$refs.map.addMarkers(
          this.areaPointList,
          require('@/assets/map/point/point2.png'),
          // [34, 84],
          [1, 1],
          layerId
        )
      }
    },
    closeCountryPart () {
      this.isShowCountryPart = false
      if (this.activaIdx === 3) this.activaIdx = -1
    },
    checkTree (e) {
      console.log(e)
      // 地图打点
      let data = csbjPoint.features.map(e => {
        return {
          position: e.geometry.coordinates.concat(100),
          properties: e.properties,
          img: e.img
        }
      })
      this.$refs.CesiumMapRef.loadPoints1(data, 'csbjPoint')
    },
    emitMenu (val) {
      console.log(val)
      switch (val) {
        case 1:
          this.stjstkShow = true
          break
        case 2:
          this.jjfztkShow = true
          break
        case 3:
          this.wgzltkShow = true
          break
        case 4:
          this.hswhtkShow = true
          break

        default:
          break
      }
    },
    // async getHsjjVideoFn () {
    //   let res = await getHsjjVideo()
    //   if (res?.code == '200') {
    //     this.introUrl = res.result.tbAppendixList[0].url
    //   }
    // },
    // async getHsjjFn () {
    //   let res = await getHsjj()
    //   if (res?.code == '200') {
    //     this.introWord = res.result[0].sysvalue
    //   }
    // },
    // async getJjglFn () {
    //   let res = await getJjgl()
    //   if (res?.code == '200') {
    //     this.data1 = []
    //     this.data1 = res.result.map(item => {
    //       return {
    //         num: Number(item.sysvalue),
    //         tit: item.name,
    //         img: require('@/assets/csts/icon.png'),
    //         url: this.data1Url[item.syskey],
    //         name: '亿元'
    //       }
    //     })
    //   }
    // },
    // async getKqzlFn () {
    //   let res = await getKqzl()
    //   if (res?.code == '200') {
    //     this.data9_1 = []
    //     this.data9_2 = []
    //     this.data9_1 = res.result
    //       .slice(0, 3)
    //       // .filter((element, index) => index % 2 !== 0)
    //       .map(it => {
    //         return { lab: it.name, num: Number(it.value) }
    //       })
    //     this.data9_2 = res.result
    //       .slice(3)
    //       // .filter((element, index) => index % 2 == 0)
    //       .map(it => {
    //         return { lab: it.name, num: Number(it.value) }
    //       })
    //   }
    // },
    // async getDjxxFn () {
    //   let res = await getDjxx()
    //   if (res?.code == '200') {
    //     this.data4 = []
    //     this.data4 = res.result.map(item => {
    //       return {
    //         count: Number(item.sysvalue),
    //         label: item.name,
    //         icon: this.data4Url[item.syskey]
    //       }
    //     })
    //   }
    // },
    // async getRkglFn () {
    //   let res = await getRkgl()
    //   if (res?.code == '200') {
    //     this.peoTotal = res.result.find(item => !item.syskey).sysvalue
    //     this.maleNum = res.result.find(item => item.syskey == 'male').sysvalue
    //     this.femaleNum = res.result.find(item => item.syskey == 'female').sysvalue

    //     this.maleNumRate = (this.maleNum / this.peoTotal).toFixed(2) * 100
    //     this.femaleNumRate = (this.femaleNum / this.peoTotal).toFixed(2) * 100
    //   }
    // },
    // async getRkglAgeFn () {
    //   let res = await getRkglAge()
    //   if (res?.code == '200') {
    //     this.data8.data = []
    //     this.data8.data = [
    //       ['product', '年总数'],
    //       ...res.result.map(item => [item.name, Number(item.sysvalue)])
    //     ]
    //   }
    // },
    // async getQyswgsjFn () {
    //   let res = await getQyswgsj()
    //   if (res?.code == '200') {
    //     this.qyswgTotal = res.result.reduce((total, obj) => total + Number(obj.sysvalue), 0)
    //     this.chartData = [
    //       ['product', '数量'],
    //       ...res.result.map(item => [item.name, Number(item.sysvalue)])
    //     ]
    //   }
    // }
  }
}
</script>

<style lang="less" scoped>
@font-face {
  font-family: QuartzRegular;
  src: url(~@/assets/leader/font/QuartzRegular.ttf);
}

@keyframes rotateS {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes rotateN {
  0% {
    transform: rotate(360deg);
  }

  100% {
    transform: rotate(0);
  }
}

@keyframes rotateY {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotateY(360deg);
  }
}

.leader_sjts {
  width: 1920px;
  height: 1080px;
  position: absolute;
  top: 0;
  display: flex;
  justify-content: space-between;

  .leader_city_box {
    width: 450px;

    .leader_zt_contain {
      height: 100%;
    }
  }

  .box {
    .cont {
      width: 100%;
      height: 100%;
      position: relative;
    }

    .wrapper1 {
      width: 100%;
      height: 100%;

      .introduce_video {
        width: 459px;
        height: 192px;

        ::v-deep .video-wrapper {
          padding-bottom: 41.25% !important;
        }
      }

      .introduce {
        margin-top: 11px;
        width: 100%;
        height: 120px;
        padding: 18px 26px;
        background: url(~@/assets/hszl/bg1.png) no-repeat;
        background-size: 100% 100%;
        font-size: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #4fddff;
        line-height: 22px;
        letter-spacing: 2px;
        text-align: left;
        // overflow-y: scroll;
        // &::-webkit-scrollbar {
        //   /*滚动条整体样式*/
        //   width: 6px;
        //   background: transparent;
        //   // border: 1px solid #999;
        //   /*高宽分别对应横竖滚动条的尺寸*/
        //   // height: 1px;
        // }

        // &::-webkit-scrollbar-thumb {
        //   /*滚动条里面小方块*/
        //   border-radius: 2px;
        //   box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        //   background: rgb(45, 124, 228);
        // }
      }
    }

    .wrapper4 {
      width: 100%;
      height: 100%;
      padding: 30px 0 30px 8px;

      ul {
        width: 100%;
        height: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;

        li {
          display: flex;
          height: 49px;
          gap: 10px;

          .icon {
            width: 46px;
            height: 49px;
          }

          .info {
            margin-top: -6px;

            .label {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
              text-align: left;
              padding-left: 5px;
            }

            .line {
              width: 87px;
              height: 6px;
              background: url('~@/assets/hszl/line1.png') no-repeat;
            }

            .count {
              text-align: left;
              margin-top: 4px;
              width: 87px;
              height: 22px;
              line-height: 22px;
              background: linear-gradient(96deg, #1d4975 0%, rgba(19, 56, 87, 0.24) 100%);
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #a8b1b9;
              padding-left: 5px;

              span {
                font-size: 18px;
                font-family: PingFangSC, PingFang SC;
                font-weight: normal;
                color: #ffffff;
                line-height: 22px;
                margin-right: 3px;
              }
            }
          }
        }
      }
    }

    .wrapper5 {
      width: 100%;
      height: 100%;
      padding-top: 31px;

      .info {
        width: 100%;
        height: 71px;
        padding-left: 8px;
        display: flex;
        justify-content: space-between;

        .total {
          width: 130px;
          height: 100%;
          background: url('~@/assets/hszl/bg4.png') no-repeat;
          display: grid;
          place-items: center;

          .cont {
            padding-top: 13px;

            .label {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
            }

            .value {
              font-size: 18px;
              font-family: PingFangSC, PingFang SC;
              font-weight: normal;
              color: #ffffff;
              line-height: 22px;
              background: linear-gradient(180deg, #ffffff 0%, #00fffa 67%, #04a1ff 100%);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;

              span {
                font-size: 12px;
              }
            }
          }
        }

        .counts {
          width: 312px;
          height: 100%;
          background: url('~@/assets/hszl/bg5.png') no-repeat;
          display: flex;
          padding: 0 9px;

          .men {
            text-align: left;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            margin-right: 4px;

            .count {
              height: 21px;
              font-size: 16px;
              font-family: PingFangSC, PingFang SC;
              font-weight: normal;
              line-height: 19px;
              background: linear-gradient(180deg, #ffffff 0%, #00ceff 67%, #04a1ff 100%);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;

              span {
                font-size: 14px;
              }
            }

            .chart {
              display: flex;
              gap: 4px;

              span {
                width: 10px;
                height: 25px;
                background-repeat: no-repeat;
              }
            }
          }

          .women {
            text-align: right;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .count {
              height: 21px;
              font-size: 16px;
              font-family: PingFangSC, PingFang SC;
              font-weight: normal;
              line-height: 19px;
              background: linear-gradient(180deg, #ffffff 0%, #f076ff 66%, #ff04ff 100%);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;

              span {
                font-size: 14px;
              }
            }

            .chart {
              display: flex;
              gap: 4px;

              span {
                width: 12px;
                height: 25px;
                background-repeat: no-repeat;
              }
            }
          }
        }
      }

      .chart_wrap {
        position: relative;
        width: 100%;
        height: 198px;
        top: 41px;

        .sign {
          position: absolute;
          left: 105px;
          top: 25px;

          .woman {
            position: absolute;
            width: 27px;
            height: 57px;
            left: 0;
            top: 0;
            background: url('~@/assets/hszl/woman.png') no-repeat;
          }

          .man {
            position: absolute;
            width: 23px;
            height: 57px;
            left: 95px;
            top: 47px;
            background: url('~@/assets/hszl/man.png') no-repeat;
          }

          .man_count {
            position: absolute;
            left: 10px;
            top: 76px;
            width: 109px;
            height: 31px;
            font-size: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 24px;
          }

          .woman_count {
            position: absolute;
            left: 10px;
            top: 36px;
            width: 109px;
            height: 31px;
            font-size: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 24px;
          }
        }
      }
    }
  }

  .box1 {
    .cont1 {
      width: 100%;
      height: 60px;
      display: flex;
      justify-content: space-around;

      .contList {
        .contNum {
          width: 124px;
          height: 24px;
          margin-top: 4px;

          span {
            display: inline-block;
            width: 100%;
            height: 24px;
            font-family: DIN, DIN;
            font-weight: normal;
            font-size: 20px;
            color: #ffffff;
            line-height: 24px;
            text-shadow: 0px 0px 4px rgba(0, 0, 0, 0);
            text-align: center;
            font-style: italic;
            background: linear-gradient(180deg, #ffffff 0%, #00fffa 67%, #04a1ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }

        &:nth-of-type(2) {
          .contNum {
            width: 124px;
            height: 24px;

            span {
              display: inline-block;
              width: 100%;
              height: 24px;
              font-family: DIN, DIN;
              font-weight: normal;
              font-size: 20px;
              color: #ffffff;
              line-height: 24px;
              text-shadow: 0px 0px 4px rgba(0, 0, 0, 0);
              text-align: center;
              font-style: italic;
              background: linear-gradient(180deg, #ffffff 0%, #ffd500 67%, #ffb904 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
        }

        &:nth-of-type(3) {
          .contNum {
            width: 124px;
            height: 24px;

            span {
              display: inline-block;
              width: 100%;
              height: 24px;
              font-family: DIN, DIN;
              font-weight: normal;
              font-size: 20px;
              color: #ffffff;
              line-height: 24px;
              text-shadow: 0px 0px 4px rgba(0, 0, 0, 0);
              text-align: center;
              font-style: italic;
              background: linear-gradient(180deg, #ffffff 0%, #86ff00 67%, #1cff04 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
        }

        img {
          display: block;
          width: 124px;
          height: 4px;
          margin-top: 4px;
        }

        .contName {
          width: 124px;
          height: 20px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #ffffff;
          line-height: 20px;
          text-align: center;
          font-style: normal;
        }
      }
    }

    .cont2 {
      width: 100%;
      height: 170px;
    }
  }

  .box2 {
    .contLeft {
      position: absolute;
      left: 20px;
      top: 0;
      text-align: left;
    }

    .contRight {
      position: absolute;
      right: 20px;
      top: 0;
      text-align: right;
    }

    .contList {
      margin-top: 20px;

      .contNum {
        font-family: DINAlternate, DINAlternate;
        font-weight: bold;
        font-size: 24px;
        color: #ffffff;
        line-height: 28px;
        letter-spacing: 2px;
        font-style: normal;

        span {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #cbe5ff;
          line-height: 20px;
          letter-spacing: 1px;
          text-align: right;
          font-style: normal;
          margin-left: 4px;
        }
      }

      .contName {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #cbe5ff;
        line-height: 20px;
        letter-spacing: 1px;
        font-style: normal;
      }

      &:nth-of-type(1) {
        margin-top: 7px;
      }
    }

    .contImg {
      width: 405px;
      height: 197px;
      position: absolute;
      top: 42px;
      left: 20px;
    }

    .contImg2 {
      width: 59px;
      height: 61px;
      position: absolute;
      top: 93px;
      left: 194px;
    }
  }

  .box3 {
    .cont {
      padding: 12px 22px 0;

      video {
        width: 100%;
        height: 100%;
        object-fit: cover;

        &:not(:root):fullscreen {
          object-fit: contain;
        }
      }
    }
  }

  .box4 {
    .cont {
      padding: 15px 0;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-evenly;

      li {
        position: relative;
        width: 200px;
        height: 66px;
        background: url(~@/assets/csts/bg2.png) no-repeat;
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: left;
        padding-left: 92px;

        .icon {
          position: absolute;
          width: 26px;
          height: 26px;
          top: 10px;
          left: 31px;
          animation: move infinite 3s ease-in-out;

          @keyframes move {
            0% {
              transform: translateY(0px) rotateY(0);
            }

            50% {
              transform: translateY(-10px) rotateY(180deg);
            }

            100% {
              transform: translateY(0px) rotateY(360deg);
            }
          }
        }

        &:nth-child(2) {
          span {
            animation-delay: 0.3s;
          }
        }

        &:nth-child(3) {
          span {
            animation-delay: 0.6s;
          }
        }

        &:nth-child(4) {
          span {
            animation-delay: 0.9s;
          }
        }

        &:nth-child(5) {
          span {
            animation-delay: 1.2s;
          }
        }

        &:nth-child(6) {
          span {
            animation-delay: 1.5s;
          }
        }

        .tit {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 20px;
        }

        .num {
          font-size: 18px;
          font-family: PingFangSC, PingFang SC;
          font-weight: normal;
          color: #ffffff;
          line-height: 22px;
        }
      }
    }
  }

  .box5 {
    .cont {
      display: flex;
      flex-direction: column;

      .bar_chart {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;

        li {
          position: relative;
          display: flex;
          align-items: center;
          padding: 0 30px 0 23px;

          .icon {
            width: 20px;
            height: 22px;
            margin-right: 7px;
          }

          .icon2 {
            width: 10px;
            height: 12px;
            position: absolute;
            left: 28px;
            top: 5px;
            animation: move infinite 3s ease-in-out;

            @keyframes move {
              0% {
                transform: translateY(0px) rotateY(0);
              }

              50% {
                transform: translateY(0px) rotateY(180deg);
              }

              100% {
                transform: translateY(0px) rotateY(360deg);
              }
            }
          }

          .lab {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            margin-right: 8px;
          }

          .progress {
            display: block;
            flex: 1;
            height: 100%;
            background: rgba(0, 201, 255, 0.14);

            .cur {
              width: 100%;
              border: 1px solid;
              height: 100%;
              overflow: hidden;
              transition: width 0.5s;
            }
          }

          .num {
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            margin-left: 9px;
          }

          .percent {
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            margin-left: 14px;
          }
        }
      }

      .pie_chart {
        height: 123px;
        display: flex;
        justify-content: space-evenly;

        .warp {
          width: 107px;
          height: 107px;
          padding-top: 0;

          /deep/.ring {
            width: 100% !important;
            height: 100% !important;
          }

          /deep/.label {
            margin-top: -65px;

            .name {
              font-size: 13px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
            }
          }
        }
      }
    }
  }

  .box6 {
    margin-top: 12px;

    .cont {
      .select_month {
        position: absolute;
        right: 52px;
        top: -20px;

        ::v-deep button {
          width: 56px;
          height: 24px;
          right: -46px;
          top: -28px;
          border-radius: 12px;
          background: url(~@/assets/shzl/select_bg.png) no-repeat center / 100% 100%;
          border: none;
          padding: 0;
          // background: linear-gradient(180deg, #0e63ac 0%, #253c69 100%);
          // border-radius: 2px;
          // border: 2px solid;
          // border-image: linear-gradient(180deg, rgba(63, 148, 224, 0.2), rgba(218, 250, 255, 0.3))
          //   2 2;
        }
      }

      // padding: 14px 28px 0;
      // .swiper-slide {
      //   height: 100%;
      // }
      // .swiper-pagination {
      //   text-align: right;
      //   bottom: 3px;
      // }
      // /deep/.swiper-pagination-bullet-active {
      //   background-color: rgba(255, 255, 255, 0.8);
      // }
      // .content {
      //   width: 100%;
      //   height: 100%;
      //   position: relative;
      //   img {
      //     position: absolute;
      //     width: 100%;
      //     height: 100%;
      //     top: 0;
      //     left: 0;
      //   }
      //   .cont {
      //     width: 100%;
      //     height: 30px;
      //     padding: 5px 10px;
      //     position: absolute;
      //     bottom: 0;
      //     font-size: 14px;
      //     font-family: PingFangSC, PingFang SC;
      //     font-weight: 500;
      //     color: #ffffff;
      //     line-height: 22px;
      //     text-align: left;
      //     background-color: rgba(0, 0, 0, 0.35);
      //   }
      // }
    }
  }

  .box7 {
    .cont {
      display: grid;
      place-items: center;
      position: relative;

      .fy_out {
        position: absolute;
        top: 3%;
        left: 27%;
        z-index: 99;
        transform: translate(-50%, -50%);
        animation: rotateS infinite 12s linear;
      }

      .fy_in {
        position: absolute;
        top: 44%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      .wrap {
        position: relative;
        width: 393px;
        height: 205px;
        background: url(~@/assets/csts/bg3.png) no-repeat;

        li {
          position: absolute;
          text-align: left;

          .num {
            font-size: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 24px;
          }

          .tit {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 17px;
          }

          &:nth-child(1) {
            top: 28px;
            left: 24px;
          }

          &:nth-child(2) {
            top: 28px;
            left: 295px;
          }

          &:nth-child(3) {
            bottom: 32px;
            left: 24px;
          }

          &:nth-child(4) {
            bottom: 32px;
            left: 295px;
          }
        }
      }
    }
  }

  .box8 {
    margin-top: 12px;
  }

  .box9 {
    .cont {
      padding: 16px 23px 0;

      .title {
        width: 100%;
        height: 44px;
        line-height: 44px;
        display: flex;
        justify-content: space-between;
        padding: 0 24px;

        span {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
        }
      }

      .detail {
        position: relative;
        width: 404px;
        height: 156px;
        background: url(~@/assets/csts/bg4.png) no-repeat center;
        display: flex;
        justify-content: space-between;

        .center_out {
          position: absolute;
          left: 29%;
          top: -2%;
          transform: translate(-50%, -50%);
          z-index: 99;
          animation: rotateS infinite 12s linear;
        }

        .center_in {
          position: absolute;
          left: 29%;
          top: 2px;
          transform: translate(-50%, -50%);
          z-index: 99;
          animation: rotateN infinite 12s linear;
        }

        .fs {
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          gap: 9px;

          li {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .icon {
              width: 13px;
              height: 16px;
            }

            .lab {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
              padding-left: 6px;
              padding-right: 22px;
            }

            .num {
              font-size: 10px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;

              span {
                font-size: 18px;
                font-family: PingFangSC, PingFang SC;
                font-weight: normal;
                color: #ffffff;
                line-height: 22px;
              }
            }
          }
        }

        .fq {
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          gap: 9px;

          li {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .icon {
              width: 14px;
              height: 14px;
            }

            .lab {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
              padding-left: 22px;
              padding-right: 6px;
            }

            .num {
              font-size: 10px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;

              span {
                font-size: 18px;
                font-family: PingFangSC, PingFang SC;
                font-weight: normal;
                color: #ffffff;
                line-height: 22px;
              }
            }
          }
        }

        .zdqy {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);

          .num {
            font-size: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 24px;
          }

          .lab {
            font-size: 18px;
            font-family: PingFangSC, PingFang SC;
            color: #ffffff;
            line-height: 21px;
            letter-spacing: 1px;
          }
        }
      }
    }
  }

  .box10 {
    .cont {
      padding: 10px 35px 0;

      .desc {
        height: 53px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        li {
          display: flex;

          .icon {
            width: 41px;
            height: 41px;
            margin-right: 11px;
          }

          .info {
            text-align: left;

            .num {
              font-size: 18px;
              font-family: PingFangSC, PingFang SC;
              font-weight: normal;
              color: #ffffff;
              line-height: 22px;
            }

            .lab {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
            }
          }
        }
      }

      .chart {
        height: calc(100% - 53px);
        margin-top: -20px;
      }
    }
  }

  .box12 {
    position: relative;

    .gajq_lz {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .left_bg {
    width: 460px;
    height: 1033px;
    background: url(~@/assets/leader/img/component/left_bg.png) no-repeat center / 100% 100%;
    position: absolute;
    left: 50px;
    top: 25px;
    z-index: 102;
  }

  .right_bg {
    width: 520px;
    height: 1033px;
    background: url(~@/assets/leader/img/component/right_bg.png) no-repeat center / 100% 100%;
    position: absolute;
    right: 50px;
    top: 25px;
    z-index: 102;
  }

  .left {
    width: 482px;
    display: flex;
    justify-content: space-between;
    margin-top: 87px;
    // margin-left: 32px;
    padding-left: 32px;
    position: relative;
    left: -420px;
    opacity: 0;
    z-index: 1003;
    -webkit-transition: all .5s ease-in;  
    -moz-transition: all .5s ease-in;  
    transition: all .5s ease-in;
  }
  .left:hover {
    left: 0;
    opacity: 1;
  }

  .right {
    width: 482px;
    display: flex;
    justify-content: space-between;
    margin-top: 87px;
    // margin-right: 32px;
    position: relative;
    right: -420px;
    opacity: 0;
    z-index: 1003;
    -webkit-transition: all .5s ease-in;  
    -moz-transition: all .5s ease-in;  
    transition: all .5s ease-in;
  }
  .right:hover {
    right: 0;
    opacity: 1;
  }
}

.jgzzBox {
  width: 156px;
  height: 163px; // 204
  display: flex;
  flex-direction: column;
  position: absolute;
  left: 980px;
  bottom: 93px;
  z-index: 999;
  background: url(~@/assets/mskx/bg_resource.png) no-repeat center / 100% 100%;

  .jgzzItem {
    width: 153px;
    height: 36px;
    margin: 1px 2px 1px 2px;
    cursor: pointer;

    // background: url(~@/assets/csts/btn11.png) no-repeat;
    img {
      float: left;
      margin: 7px 3px 0 10px;
    }

    span {
      float: left;
      font-size: 17px;
      font-family: PingFangSC, PingFang SC;
      color: #e9fffe;
      line-height: 36px;
      background: linear-gradient(180deg, #ffffff 0%, #2f8af4 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .jgzzActive {
    width: 153px;
    height: 36px;
    margin: 1px 2px 1px 2px;
    // background: url(~@/assets/csts/btn12.png) no-repeat;
    background: rgba(108, 163, 255, 0.3);
  }
}

.zdcsBox {
  width: 109px;
  height: 248px;
  display: flex;
  flex-direction: column;
  position: absolute;
  left: 1197px;
  bottom: 93px;
  z-index: 999;
  background: url(~@/assets/csts/btnBg12.png) no-repeat center / 100% 100%;

  .jgzzItem {
    width: 103px;
    height: 35px;
    margin: 2px 3px 2px 3px;
    background: url(~@/assets/csts/btn11.png) no-repeat;

    img {
      float: left;
      margin: 7px 0 0 10px;
    }

    span {
      float: left;
      font-size: 17px;
      font-family: PingFangSC, PingFang SC;
      color: #e9fffe;
      line-height: 35px;
      background: linear-gradient(180deg, #ffffff 0%, #2f8af4 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .jgzzActive {
    width: 156px;
    height: 35px;
    margin: 1px 2px 1px 2px;
    background: url(~@/assets/csts/btn12.png) no-repeat;
  }
}

.left,
.right {
  ::v-deep {
    .el-carousel {
      width: 450px;
      height: 920px;

      .el-carousel__indicators {
        .el-carousel__indicator {
          &.is-active {
            .el-carousel__button {
              height: 4px;
              background-color: #49e6ff;
            }
          }

          .el-carousel__button {
            height: 4px;
            background-color: rgba(255, 255, 255, 0.4);
            border-radius: 2px;
          }
        }
      }
    }
  }
}

.right {
  ::v-deep {
    .el-carousel {
      margin-left: auto;
    }
  }
}

::v-deep .el-carousel--horizontal {
  width: 450px;
  overflow-x: hidden;
}

.map_box {
  position: absolute;
  width: 1920px;
  height: 1080px;
  top: 0px;
  left: 0;
  z-index: 999;
  // border: 1px solid red;
}

/* 样式用于控制滚动容器的高度、宽度、边框和滚动效果等 */
.introduce {
  // width: 500px;
  // height: 200px;
  overflow: hidden;
  position: relative;
}

/* 样式用于控制滚动文本的位置和颜色 */
.introduce p {
  position: absolute;
  top: 0;
  left: 0;
  margin: 0;
  padding: 10px;
  font-size: 16px;
}
</style>
