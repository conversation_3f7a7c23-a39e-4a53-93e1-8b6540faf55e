<template>
  <div
    class="table-wrapper"
    :style="{
      width: 'calc(' + widths.reduce((i, j) => `${i} + ${j}`) + ` + ${(widths.length - 1) * 6}px)`,
      height: `calc(${contentHeight} + 36px)`
    }"
  >
    <!-- <div class="table-wrapper"> -->
    <div class="table-header" v-if="showHeader" :style="{ height: headerHeight }">
      <span
        v-for="(it, i) in widths"
        :key="i"
        :style="{ width: widths[i], padding: headerSpanPadding }"
      >
        <div class="row-item" :style="{ padding: headerSpanPadding }">{{ titles[i] }}</div></span
      >
    </div>
    <div class="table-content" :style="{ height: contentHeight }">
      <swiper class="swiper" :options="swiperOption" ref="myBotSwiper">
        <swiper-slide v-for="(it, i) in dataList" :key="i">
          <div
            class="table-row"
            :class="{ stripe: i % 2 === 1, active: i === currentActiveRow }"
            @click="handleRowClick(it, i)"
            :style="{ height: tabelHeight }"
          >
            <span
              v-for="(ite, ind) in widths"
              :key="ind"
              :title="it[ind]"
              :style="{
                width: widths[ind],
                height: tabelHeight,
                padding: spanPadding,
                fontSize: rowFontSize
              }"
            >
              <!-- <div v-if="it[ind] == '1' && ind===widths.length-1 " class="row-point" :style="{ width: widths[ind] }">
              <div class="red-point">已解决{{it[widths.length-1]}}</div>
              </div>
              <div v-else-if="it[ind] == '2'&& ind===widths.length-1 " class="row-point" :style="{ width: widths[ind] }">
              <div class="green-point">未解决</div>
              </div> -->
              <div  :style="{ width: widths[ind] }">
              <div >{{ it[ind] }}</div>
              </div>
            </span>
          </div>
        </swiper-slide>
      </swiper>
    </div>
  </div>
</template>

<script>
export default {
  name: 'swiper-table',
  props: {
    data: {
      type: Array,
      default: () => [
        [
          '杂草乱扔',
          '2020.08.23 12:26:15',
          '全要素网格化',
          '公安交警',
          '环境保护',
          '待受理',
          '李念省'
        ],
        [
          '杂草乱扔',
          '2020.08.23 12:26:15',
          '全要素网格化',
          '公安交警',
          '环境保护',
          '待受理',
          '李念省'
        ],
        [
          '杂草乱扔',
          '2020.08.23 12:26:15',
          '全要素网格化',
          '公安交警',
          '环境保护',
          '待受理',
          '李念省'
        ],
        [
          '杂草乱扔',
          '2020.08.23 12:26:15',
          '全要素网格化',
          '公安交警',
          '环境保护',
          '待受理',
          '李念省'
        ],
        [
          '杂草乱扔',
          '2020.08.23 12:26:15',
          '全要素网格化',
          '公安交警',
          '环境保护',
          '待受理',
          '李念省'
        ],
        [
          '杂草乱扔',
          '2020.08.23 12:26:15',
          '全要素网格化',
          '公安交警',
          '环境保护',
          '待受理',
          '李念省'
        ],
        [
          '杂草乱扔',
          '2020.08.23 12:26:15',
          '全要素网格化',
          '公安交警',
          '环境保护',
          '待受理',
          '李念省'
        ],
        [
          '杂草乱扔',
          '2020.08.23 12:26:15',
          '全要素网格化',
          '公安交警',
          '环境保护',
          '待受理',
          '李念省'
        ],
        [
          '杂草乱扔',
          '2020.08.23 12:26:15',
          '全要素网格化',
          '公安交警',
          '环境保护',
          '待受理',
          '李念省'
        ],
        [
          '杂草乱扔',
          '2020.08.23 12:26:15',
          '全要素网格化',
          '公安交警',
          '环境保护',
          '待受理',
          '李念省'
        ],
        [
          '杂草乱扔',
          '2020.08.23 12:26:15',
          '全要素网格化',
          '公安交警',
          '环境保护',
          '待受理',
          '李念省'
        ],
        [
          '杂草乱扔',
          '2020.08.23 12:26:15',
          '全要素网格化',
          '公安交警',
          '环境保护',
          '待受理',
          '李念省'
        ],
        [
          '杂草乱扔',
          '2020.08.23 12:26:15',
          '全要素网格化',
          '公安交警',
          '环境保护',
          '待受理',
          '李念省'
        ],
        [
          '杂草乱扔',
          '2020.08.23 12:26:15',
          '全要素网格化',
          '公安交警',
          '环境保护',
          '待受理',
          '李念省'
        ],
        [
          '杂草乱扔',
          '2020.08.23 12:26:15',
          '全要素网格化',
          '公安交警',
          '环境保护',
          '待受理',
          '李念省'
        ]
      ]
    },
    titles: {
      type: Array,
      default: () => ['事件标题', '创建时间', '信息来源', '分类', '状态', '上报人']
    },
    widths: {
      type: Array,
      default: () => ['246px', '348px', '224px', '214px', '214px', '214px']
    },
    contentHeight: {
      type: String,
      default: '356px'
    },
    showHeader: {
      type: Boolean,
      default: true
    },
    tabelHeight: {
      type: String,
      default: '60px'
    },
    spanPadding: {
      type: String,
      default: '0 0 0 20px'
    },
    headerSpanPadding: {
      type: String,
      default: '0 0 0 20px'
    },
    headerHeight: {
      type: String,
      default: ''
    },
    rowFontSize: {
      type: String,
      default: '25px'
    }
  },
  computed: {
    // 将手机号的中间四位隐藏
    dataList() {
      return this.data.map(it => {
        // let [name, mobile, time, totals, bfb, qwer] = it;
        // const reg = /^(\d{3})\d{4}(\d{4})$/;
        // mobile = mobile.replace(reg, '$1****$2');

        // 不需要隐藏手机号码就隐去上面的代码
        let qwer = it;
        return qwer;
      });
    },
    myBotSwiper() {
      return this.$refs.myBotSwiper.$swiper;
    }
  },
  data() {
    return {
      currentActiveRow: 999,
      swiperOption: {
        autoHeight: true,
        direction: 'vertical',
        spaceBetween: 0,
        autoplay: {
          delay: 2500,
          disableOnInteraction: false,
          autoplayDisableOnInteraction: false
        },
        slidesPerView: 'auto',
        grabCursor: true,
        autoplayDisableOnInteraction: false,
        mousewheelControl: true
      }
    };
  },
  methods: {
    handleRowClick(it, i) {
      if (this.currentActiveRow == i) {
        this.currentActiveRow = 999;
        this.swiperStart();
      } else {
        this.currentActiveRow = i;
        this.swiperStop();
      }
      const currentIt = this.data[i];
      // this.swiperStop();
      this.$emit('change', currentIt);
    },
    swiperStop() {
      this.myBotSwiper.autoplay.stop();
    },
    swiperStart() {
      this.myBotSwiper.autoplay.start();
    }
  },
  watch: {
    data() {
      this.currentActiveRow = 0;
    }
  }
};
</script>

<style lang="scss" scoped>

.table-wrapper {
  width: 100%;
  height: 497px;
  span {
    box-sizing: border-box;
  }
  .table-header {
    $height: 60px;
    width: 97%;
    height: $height;
    display: flex;
    background: rgba(3, 135, 255, 0.15);
    span {
      color: #83c2ee;
      //   background: rgba(255, 255, 255, 0.12);
      font-size: 28px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: rgba(131, 194, 238, 1);
      line-height: $height;
      .row-item {
        position: absolute;
        padding: 0 0 0 20px;
        padding: 0px!important;
        top: 50%;
        transform: translateY(-50%);
        overflow: hidden;
        text-overflow: ellipsis; /* 文本溢出时显示省略号来代表被修剪的文本 */
        white-space: normal;
        display: -webkit-box; /* 必须结合的属性 ，将对象作为弹性伸缩盒子模型显示 */
        -webkit-box-orient: vertical; /* 必须结合的属性 ，设置或检索伸缩盒对象的子元素的排列方式 */
        -webkit-line-clamp: 2; /* 文本需要显示多少行 */
        word-break: break-all;
      }
      &:nth-child(2) {
        text-align: left;
      }
    }
    span + span {
      margin-left: 2px;
    }
  }
  .table-content {
    width: 100%;
    height: 284px;
    margin-top: 2px;
    .swiper {
      width: 100%;
      height: 100%;
      .table-row {
        width: 100%;
        height: 60px;
        line-height: 60px;
        cursor: pointer;
        display: flex;
        &.active {
          span {
            color: rgba(131, 194, 238, 1);
            font-size: 25px;
            font-weight: bold;
          }
        }
        &.stripe span {
          background: rgba(255, 255, 255, 0.06);
        }
        span {
          font-size: 25px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: rgba(255, 255, 255, 0.85);
          &>div{
            &>div{
               white-space: nowrap;
               text-overflow: ellipsis;
               overflow: hidden;
               word-break: break-all;
            }
          }
        }
        span + span {
          margin-left: 2px;
        }
      }
    }
  }
  span {
    height: 100%;
    // line-height: 100%;
    // display: inline-block;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 0 0 20px;
    display: flex;
    justify-content: start;
    position: relative;
    .row-point {
      display: flex;
      align-items: center;
      justify-content: start;
      .red-point {
        color:  #08BF8A;
      }
      .green-point {
        color: #FF9090;
      }
    }
    .row-item {
      position: absolute;
      padding: 0 0 0 20px;
      top: 50%;
      transform: translateY(-50%);
      overflow: hidden;
      text-overflow: ellipsis; /* 文本溢出时显示省略号来代表被修剪的文本 */
      white-space: normal;
      display: -webkit-box; /* 必须结合的属性 ，将对象作为弹性伸缩盒子模型显示 */
      -webkit-box-orient: vertical; /* 必须结合的属性 ，设置或检索伸缩盒对象的子元素的排列方式 */
      -webkit-line-clamp: 2; /* 文本需要显示多少行 */
      word-break: break-all;
    }
  }
}
</style>
