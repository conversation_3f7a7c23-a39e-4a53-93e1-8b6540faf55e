import http from '@/utils/fzjc/request';
import { finaUrl } from '@/utils/fzjc/const';

// 环境问题高发区域
export const hjwtgfArea = () => {
  return http.get(
    finaUrl + '/api/envAnalysis/areaRank'
  );
};
// 环境事件爆发趋势
export const hjsjqs = () => {
  return http.get(
    finaUrl + '/api/envAnalysis/eventReportingTrend'
  );
};
// 环境问题类型分析
export const hjwtfl = (data) => {
  return http.get(
    finaUrl + '/api/envAnalysis/typeAnalysis', data
  );
};
// 环境事件当月上报数量，当月办结数量，办结率
export const overview = () => {
  return http.get(
    finaUrl + '/api/envAnalysis/overview'
  );
};
// 区域维度统计环境类型问题数量
export const envOrderDistribute = () => {
  return http.get(
    finaUrl + '/api/envAnalysis/envOrderDistribute'
  );
};

export const mapSpth = (userId) => {
  return http.get(
    'http://113.15.163.56:8081/ump/bigScreenQJTS/videoCall?userId='+ userId
  );
};