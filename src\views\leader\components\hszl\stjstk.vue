<template>
  <div class="tkBox">
    <div class="tkTitle">
      <span>湖熟文化</span>
    </div>
    <img @click="closeEmitai" src="@/assets/hszl/tkGb.png" class="tkGb" />
    <div class="content_box">
      <div class="left_">
        <BlockBox
          title="街道概况"
          subtitle="Street Overview"
          class="tkList"
          :isListBtns="false"
          :blockHeight="296"
        >
          <div class="wrapper wrapper1">
            <div class="wrapperList1">
              <span></span>基本情况
            </div>
            <div class="wrapperList2">
              <div class="wrapperItem">
                <img src="@/assets/hszl/stjsImg1.png" class="wrapperLeft" />
                <div class="wrapperRight">
                  <div class="wrapperNum">
                    {{ jdkgValue1 }}
                    <!-- <div class="pfgl_">平方公里</div> -->
                    <span>平方公里</span>
                  </div>
                  <div class="wrapperNamu">行政面积</div>
                  <!-- xzmj_ -->
                </div>
              </div>
              <div class="wrapperItem">
                <img src="@/assets/hszl/stjsImg2.png" class="wrapperLeft" />
                <div class="wrapperRight">
                  <div class="wrapperNum">
                    {{ jdkgValue2 }}
                    <span>个</span>
                  </div>
                  <div class="wrapperNamu">居民委员会</div>
                </div>
              </div>
              <div class="wrapperItem">
                <img src="@/assets/hszl/stjsImg3.png" class="wrapperLeft" />
                <div class="wrapperRight">
                  <div class="wrapperNum">
                    {{ jdkgValue3 }}
                    <span>个</span>
                  </div>
                  <div class="wrapperNamu">村民委员会</div>
                </div>
              </div>
            </div>
            <div class="wrapperList3">
              <span></span>人口
            </div>
            <div class="wrapperList4">
              <!-- <div class="wrapperItem">
                <img src="@/assets/hszl/stjsImg4.png" class="wrapperLeft" />
                <div class="wrapperRight">
                  <div class="wrapperNum">{{ jdkgValue4 }}<span>户</span></div>
                  <div class="wrapperNamu">户籍户数</div>
                </div>
              </div>-->
              <div class="wrapperItem">
                <img src="@/assets/hszl/stjsImg5.png" class="wrapperLeft" />
                <div class="wrapperRight">
                  <div class="wrapperNum">
                    {{ jdkgValue5 }}
                    <span>人</span>
                  </div>
                  <div class="wrapperNamu">户籍人口</div>
                </div>
              </div>
              <div class="wrapperItem">
                <img src="@/assets/hszl/stjsImg6.png" class="wrapperLeft" />
                <div class="wrapperRight">
                  <div class="wrapperNum">
                    {{ jdkgValue6 }}
                    <span>人</span>
                  </div>
                  <div class="wrapperNamu">实有人口</div>
                </div>
              </div>
              <div class="wrapperItem">
                <img src="@/assets/hszl/stjsImg6.png" class="wrapperLeft" />
                <div class="wrapperRight">
                  <div class="wrapperNum">
                    {{ jdkgValue7 }}
                    <span>人</span>
                  </div>
                  <div class="wrapperNamu">流动人口</div>
                </div>
              </div>
            </div>
          </div>
        </BlockBox>
        <BlockBox
          title="人口分布"
          subtitle="population distribution"
          class="tkList"
          :isListBtns="false"
          :blockHeight="296"
        >
          <div class="wrapper">
            <TowerChart
              :options="options"
              :data="data"
              :init-option="{
            yAxis: {
              name: '单位：人',
            },
          }"
            />
          </div>
        </BlockBox>

        <BlockBox
          title="市场主体"
          subtitle="market subject"
          class="tkList"
          :isListBtns="false"
          :blockHeight="296"
        >
          <div class="wrapper wrapper3">
            <div class="wrapperItem1">
              <img src="@/assets/hszl/stjsImg7.png" class="wrapperImg" />
              <div class="wrapperName">工贸企业总数(家)</div>
              <div class="wrapperNum">{{ scztTotal }}</div>
            </div>
            <div
              class="wrapperItem2"
              @click="showSczt(item)"
              v-for="(item, index) in scztData"
              :key="index"
            >
              <div class="wrapperLeft">
                <img :src="item.url" />
              </div>
              <div class="wrapperRight">
                <div class="wrapperNum">
                  <img src="@/assets/hszl/stjsImg8.png" alt />
                  {{ item.num }}
                </div>
                <div class="wrapperName">{{ item.name }}（家）</div>
              </div>
            </div>
            <!-- <div class="wrapperItem2">
              <div class="wrapperLeft"><img src="@/assets/hszl/stjsImg22.png" /></div>
              <div class="wrapperRight">
                <div class="wrapperNum">
                  <img src="@/assets/hszl/stjsImg8.png" alt="" />{{ scztValue2 }}
                </div>
                <div class="wrapperName">规上（家）</div>
              </div>
            </div>
            <div class="wrapperItem2">
              <div class="wrapperLeft"><img src="@/assets/hszl/stjsImg23.png" /></div>
              <div class="wrapperRight">
                <div class="wrapperNum">
                  <img src="@/assets/hszl/stjsImg8.png" alt="" />{{ scztValue3 }}
                </div>
                <div class="wrapperName">瞪羚（家）</div>
              </div>
            </div>
            <div class="wrapperItem2">
              <div class="wrapperLeft"><img src="@/assets/hszl/stjsImg24.png" /></div>
              <div class="wrapperRight">
                <div class="wrapperNum">
                  <img src="@/assets/hszl/stjsImg8.png" alt="" />{{ scztValue4 }}
                </div>
                <div class="wrapperName">独角兽（含培育/家）</div>
              </div>
            </div>
            <div class="wrapperItem2">
              <div class="wrapperLeft"><img src="@/assets/hszl/stjsImg25.png" /></div>
              <div class="wrapperRight">
                <div class="wrapperNum">
                  <img src="@/assets/hszl/stjsImg8.png" alt="" />{{ scztValue5 }}
                </div>
                <div class="wrapperName">专精特新（项）</div>
              </div>
            </div>-->
            <!-- <div class="wrapperItem2">
              <div class="wrapperLeft"><img src="@/assets/hszl/stjsImg26.png" /></div>
              <div class="wrapperRight">
                <div class="wrapperNum">
                  <img src="@/assets/hszl/stjsImg8.png" alt="" />{{ scztValue6 }}
                </div>
                <div class="wrapperName">科技型（家）</div>
              </div>
            </div>-->
          </div>
        </BlockBox>
        <BlockBox
          title="魅力菊花展"
          subtitle="Charming Chrysanthemum Exhibition"
          class="tkList"
          :isListBtns="false"
          :blockHeight="296"
        >
          <div class="wrapper wrapper4">
            <swiper class="swiper content" :options="swiperOption">
              <swiper-slide v-for="(it, i) in data6" class="width: 100%" :key="i">
                <img :src="it.url" alt />
                <!-- <div class="cont">
                  {{ it.title.slice(0, it.title.indexOf('.')) }}
                </div>-->
              </swiper-slide>
              <div class="swiper-pagination" slot="pagination"></div>
            </swiper>
          </div>
        </BlockBox>
      </div>

      <div class="right_">
        <BlockBox
          title="湖熟文物"
          subtitle="Hushu Cultural Relics"
          class="tkList"
          :isListBtns="false"
          :blockHeight="600"
        >
          <div class="wrapper wrapper5">
            <swiper-table
              :data="box5Data"
              :titles="['名称', '年代', '保护级别', '操作']"
              :widths="['30%', '30%', '20%', '20%']"
              content-height="440px"
              :isNeedOperate="true"
              :operateBtn="['详情']"
              @operate="handleOperate"
            />
          </div>
        </BlockBox>
      </div>
    </div>
    <hswhPop :hswhData="hswhData" v-if="isHswhPopShow" @close="isHswhPopShow = false" />
    <infoPdf :filePdfUrl="filePdfUrl" v-if="isInfoPdfShow" @close="isInfoPdfShow = false" />
    <sczttk :tabActive="checkId" v-if="isSczttkShow" @closeEmitai="isSczttkShow = false" />
  </div>
</template>

<script>
import BlockBox from '@/components/leader/common/blockBox.vue'
import SwiperTable from '@/components/zhny/SwiperTable.vue'
import hswhPop from '@/components/hszl/hswhPop.vue'
import infoPdf from '@/components/hszl/infoPdf.vue'
import sczttk from '@/components/hszl/sczttk.vue'
import {
  getJdgk,
  getScztTotal,
  getSczt,
  getRkfb,
  getHsHp,
  zlHswh,
  mlJhz,
} from '@/api/hs/hs.hszl.js'

export default {
  name: 'stjstk',
  components: {
    BlockBox,
    SwiperTable,
    hswhPop,
    infoPdf,
    sczttk,
  },
  data () {
    return {
      data6: [
        {
          url: require('@/assets/csts/photo1.png'),
          title: '湖熟航拍',
        },
        {
          url: require('@/assets/csts/photo1.png'),
          title: '湖熟航拍',
        },
        {
          url: require('@/assets/csts/photo1.png'),
          title: '湖熟航拍',
        },
      ],
      swiperOption: {
        observer: true,
        observeParents: true,
        autoHeight: true,
        spaceBetween: 0,
        autoplay: {
          delay: 3500,
          disableOnInteraction: false,
        },
        slidesPerView: 'auto',
        grabCursor: true,
        autoplayDisableOnInteraction: false,
        mousewheelControl: true,
        pagination: {
          el: '.swiper-pagination',
        },
      },
      options: {
        barColor: '#02C4DD',
        showBarLabel: true,
        // 是否开启轮播
        isSeriesScorll: true,
        // 轮播的间隔时间
        scorllTimes: 1500,
        // 超过多少的数量轮播
        dataZoomNum: 4,
      },
      data: [['product', '']],
      box4BottomData: {
        data: [
          ['product', '事件总数'],
          ['燃气监测', 861],
          ['水质监测', 1026],
          ['烟感监测', 334],
          ['环境监测', 107],
        ],
        options: {
          colors: ['#80CF83', '#CDCF72', '#0280DF', '#13A4C0'],
          legend: {
            top: 2,
            // left: 100,
            // orient: 'vertical',
            itemWidth: 10,
            itemHeight: 10,
            itemRadius: 2,
            itemPadding: 5,
            itemDistance: 12,
            itemMarginTop: 12,
            textColor: 'rgba(255, 255, 255, 0.85)',
            fontWeight: '400',
            fontSize: '12px',
            fontFamily: 'DINPro-Medium',
            letterSpacing: '3px',
          },
          position: ['50%', '10%'],
          bgImg: {
            width: '60%',
            height: '60%',
            top: '42%',
            left: '50%',
          },
          unit: '件',
          title: {
            fontSize: '16px',
            top: 80,
          },
          subtitle: {
            fontSize: '14px',
            top: 100,
          },
        },
      },
      box1BottomData: {
        data: [
          ['product', '总量：吨', '系列名2', '总量：立方'],
          ['02', 50, 100, 99],
          ['04', 55, 100, 80],
          ['06', 25, 100, 70],
          ['08', 55, 100, 60],
          ['10', 55, 100, 77],
          ['12', 55, 100, 66],
        ],
        options: {},
      },
      jdkgValue1: '',
      jdkgValue2: '',
      jdkgValue3: '',
      jdkgValue4: '',
      jdkgValue5: '',
      jdkgValue6: '',
      jdkgValue7: '',
      scztTotal: '',
      scztValue1: '',
      scztValue2: '',
      scztValue3: '',
      scztValue4: '',
      scztValue5: '',
      scztValue6: '',
      box5Data: [],
      box5DataAll: [],
      hswhData: {},
      isHswhPopShow: false,
      isInfoPdfShow: false,
      filePdfUrl: '',
      scztData: [],
      scztPics: [
        { pic: require('@/assets/hszl/stjsImg21.png'), value: '0' },
        { pic: require('@/assets/hszl/stjsImg22.png'), value: '1' },
        { pic: require('@/assets/hszl/stjsImg23.png'), value: '2' },
        { pic: require('@/assets/hszl/stjsImg24.png'), value: '3' },
        { pic: require('@/assets/hszl/stjsImg25.png'), value: '4' },
      ],
      isSczttkShow: '',
      checkId: '',
    }
  },
  created () {
    this.getJdgkFn()
    this.getScztTotalFn()
    this.getScztFn()

    this.getRkfbFn()
    this.getHsHpFn()
    this.zlHswhFn()
  },
  methods: {
    closeEmitai () {
      this.$emit('closeEmitai')
    },
    async getJdgkFn () {
      let res = await getJdgk()
      if (res?.code == '200') {
        this.jdkgValue1 = res.result[0].sysvalue
        this.jdkgValue2 = res.result[1].sysvalue
        this.jdkgValue3 = res.result[2].sysvalue

        this.jdkgValue5 = res.result[3].sysvalue
        this.jdkgValue6 = res.result[4].sysvalue
        this.jdkgValue7 = res.result[5].sysvalue
      }
    },
    async getScztTotalFn () {
      let res = await getScztTotal()
      if (res?.code == '200') {
        this.scztTotal = res.result[0].sysvalue
      }
    },
    async getScztFn () {
      let res = await getSczt()
      if (res?.code == '200') {
        this.scztData = res.data
        this.scztData.forEach((item, index) => {
          if (item.value == this.scztPics[index].value) {
            this.$set(item, 'url', this.scztPics[index].pic)
          }
        })
      }
    },
    async getRkfbFn () {
      let res = await getRkfb()
      if (res?.code == '200') {
        this.data = [
          ['product', ''],
          ...res.result.map((item) => [item.name, Number(item.sysvalue)]),
        ]
      }
    },
    async getHsHpFn () {
      let res = await getHsHp()
      if (res?.code == '200') {
        this.data6 = []
        this.data6 = res.result.tbAppendixList.map((item) => {
          return {
            title: item.originalFileName,
            url: item.url,
          }
        })
      }
    },
    async zlHswhFn () {
      let res = await zlHswh()
      if (res?.code == '200') {
        this.box5Data = []
        this.box5DataAll = res.result
        this.box5Data = res.result.map((item) => [item.name, item.years, item.protection])
      }
    },
    handleOperate (btnIndex, it, i) {
      this.hswhData = this.box5DataAll[i]
      this.isHswhPopShow = true
    },
    async jhzPdfFn () {
      let res = await mlJhz()
      if (res?.code == '200') {
        this.filePdfUrl = res.result
        this.isInfoPdfShow = true
      }
    },
    showSczt (item) {
      this.checkId = item.value
      this.isSczttkShow = true
    },
  },
  computed: {
    boxInitoptions () {
      return {
        yAxis: {
          name: '人',
          nameTextStyle: {},
          axisLabel: {
            textStyle: {},
          },
        },
        xAxis: {
          axisLabel: {
            show: true,
            textStyle: {
              fontSize: 14,
            },
          },
        },
        dataZoom: [
          {
            type: 'inside', // 内置型式的 dataZoom 组件
            xAxisIndex: 0, // 对应 x 轴的索引，默认为 0
            start: 0, // 起始位置的百分比
            end: 20, // 结束位置的百分比
            // realtime: true // 启用实时滚动
          },
        ],
      }
    },
  },
}
</script>

<style lang="less" scoped>
.tkBox {
  width: 1800px;
  height: 843px;
  background: url(~@/assets/hszl/tkBg.png) no-repeat center / 100% 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10000;
  padding: 0 43px;
  .tkTitle {
    width: 634px;
    height: 74px;
    background: url(~@/assets/hszl/tkTitle.png) no-repeat center / 100% 100%;
    margin: 0 auto;
    margin-top: 22px;
    line-height: 74px;
    span {
      font-size: 36px;
      font-family: PingFangSC, PingFang SC;
      color: #ffffff;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .tkGb {
    position: absolute;
    top: 38px;
    right: 43px;
  }
  .content_box {
    display: flex;
    align-content: space-between;
    justify-content: space-between;
    flex-wrap: wrap;
    .left_ {
      width: 68%;
      display: flex;
      align-content: space-between;
      justify-content: space-between;
      flex-wrap: wrap;
    }
    .tkList {
      // float: left;
      margin: 0 32px;
      margin-top: 26px;
      position: relative;

      .wrapper {
        width: 460px;
        height: 263px;
      }
      .wrapper1 {
        .wrapperList1 {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #b3daff;
          line-height: 22px;
          text-align: left;
          margin-top: 18px;
          span {
            float: left;
            width: 2px;
            height: 16px;
            background: #00a5ff;
            margin: 4px 8px 0 18px;
          }
        }
        .wrapperList2 {
          width: 460px;
          height: 47px;
          margin-top: 19px;
          .wrapperItem {
            &:first-of-type {
              width: 40%;
            }
            &:nth-of-type(2) {
              width: 30%;
            }
            &:nth-of-type(3) {
              width: 30%;
            }
            height: 47px;
            float: left;
            .wrapperLeft {
              float: left;
            }
            .wrapperRight {
              // float: left;
              text-align: left;
              margin-left: 6px;
              .wrapperNum {
                font-size: 22px;
                font-family: DINOT-Black, DINOT;
                font-weight: 900;
                color: #ffffff;
                line-height: 22px;
                margin-top: 1px;
                white-space: nowrap;
                display: flex;
                .pfgl_ {
                  width: 30px;
                  // white-space: w;
                  font-size: 14px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: 400;
                  color: #ffffff;
                  line-height: 20px;
                  margin-left: 34px;
                  // word-wrap: break-word;
                  white-space: normal;
                }

                span {
                  font-size: 14px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: 400;
                  color: #ffffff;
                  line-height: 20px;
                  margin-left: 4px;
                }
              }
              .wrapperNamu {
                font-size: 14px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                color: #ffffff;
                line-height: 20px;
                margin-top: 1px;
                &.xzmj_ {
                  position: relative;
                  bottom: 18px;
                }
              }
            }
          }
        }
        .wrapperList3 {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #b3daff;
          line-height: 22px;
          text-align: left;
          margin-top: 18px;
          span {
            float: left;
            width: 2px;
            height: 16px;
            background: #00a5ff;
            margin: 4px 8px 0 18px;
          }
        }
        .wrapperList4 {
          width: 460px;
          height: 47px;
          margin-top: 19px;
          .wrapperItem {
            width: 33.33%;
            height: 47px;
            float: left;
            .wrapperLeft {
              float: left;
              margin-left: 9px;
            }
            .wrapperRight {
              float: left;
              text-align: left;
              margin-left: 12px;
              .wrapperNum {
                font-size: 22px;
                font-family: DINOT-Black, DINOT;
                font-weight: 900;
                color: #ffffff;
                line-height: 22px;
                margin-top: 1px;
                span {
                  font-size: 14px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: 400;
                  color: #ffffff;
                  line-height: 20px;
                  margin-left: 4px;
                }
              }
              .wrapperNamu {
                font-size: 14px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                color: #ffffff;
                line-height: 20px;
                margin-top: 1px;
              }
            }
          }
        }
      }
      .wrapper3 {
        .wrapperItem1 {
          width: 273px;
          height: 62px;
          background: url(~@/assets/hszl/stjsBg1.png) no-repeat center / 100% 100%;
          margin: 2px auto 5px auto;
          .wrapperImg {
            float: left;
            margin: 9px 0 0 26px;
          }
          .wrapperName {
            float: left;
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 62px;
            margin-left: 36px;
          }
          .wrapperNum {
            float: left;
            font-size: 18px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 62px;
            margin-left: 14px;
          }
        }
        .wrapperItem2 {
          width: 221px;
          height: 61px;
          float: left;
          background: url(~@/assets/hszl/stjsBg2.png) no-repeat center / 100% 100%;
          margin: 2px 4.5px 5px 4.5px;
          .wrapperLeft {
            width: 42px;
            height: 41px;
            float: left;
            margin: 10px 10px 0 6px;
            img {
              margin-top: 5px;
            }
          }
          .wrapperRight {
            float: left;
            text-align: left;
            .wrapperNum {
              font-size: 20px;
              font-family: PingFangSC, PingFang SC;
              font-weight: normal;
              color: #ffffff;
              line-height: 24px;
              margin-top: 4px;
              img {
                margin-right: 3px;
              }
            }
            .wrapperName {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
              margin-top: 3px;
              margin-left: 11px;
            }
          }
          &:nth-child(2) .wrapperLeft {
            background: url(~@/assets/hszl/stjsImg11.png) no-repeat center / 100% 100%;
          }
          &:nth-child(3) .wrapperLeft {
            background: url(~@/assets/hszl/stjsImg12.png) no-repeat center / 100% 100%;
          }
          &:nth-child(4) .wrapperLeft {
            background: url(~@/assets/hszl/stjsImg13.png) no-repeat center / 100% 100%;
          }
          &:nth-child(5) .wrapperLeft {
            background: url(~@/assets/hszl/stjsImg14.png) no-repeat center / 100% 100%;
          }
          &:nth-child(6) .wrapperLeft {
            background: url(~@/assets/hszl/stjsImg15.png) no-repeat center / 100% 100%;
          }
          &:nth-child(7) .wrapperLeft {
            background: url(~@/assets/hszl/stjsImg16.png) no-repeat center / 100% 100%;
          }
        }
      }
      .wrapper4 {
        width: 407px;
        height: 229px;
        margin: 0 auto;
        margin-top: 41px;
        .swiper-slide {
          height: 100%;
        }
        .swiper-pagination {
          text-align: right;
          bottom: 3px;
        }
        /deep/.swiper-pagination-bullet-active {
          background-color: rgba(255, 255, 255, 0.8);
        }
        .content {
          width: 100%;
          height: 100%;
          position: relative;
          img {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
          }
          .cont {
            width: 100%;
            height: 30px;
            padding: 5px 10px;
            position: absolute;
            bottom: 0;
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #ffffff;
            line-height: 22px;
            text-align: left;
            background-color: rgba(0, 0, 0, 0.35);
          }
        }
      }
      .wrapper5 {
        padding: 20px 0 0 0;
        height: 100%;
      }
    }
  }
}
</style>
