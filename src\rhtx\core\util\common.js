/*
 * @Author: fanjialong 
 * @Date: 2023-07-21 09:34:10
 * @LastEditors: fanjialong 
 * @LastEditTime: 2023-08-10 11:16:21
 * @FilePath: /hs_dp/src/rhtx/core/util/common.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Message } from 'element-ui';
import CryptoJS from 'crypto-js';
async function supportDetection() {
  const support = {
    audio: false,
    video: false
  };
  if (!isSupportMediaDevices()) {
    // alert('无法获取设备  请检查是否是可信赖网址');
    console.log('无法获取设备  请检查是否是可信赖网址');
    return support;
  }
  if (navigator.mediaDevices.enumerateDevices) {
    let devices = await navigator.mediaDevices.enumerateDevices();
    //音频检测
    let { audio, video } = devices.reduce((total, item) => {
      if (item.kind === 'audioinput') {
        total.audio = true;
        support.audio = true;
      }
      if (item.kind === 'videoinput') {
        total.video = true;
        support.video = true;
      }
      return total;
    }, {});
    // !audio && Message.warning('融合通信已下线！');
    // !video && Message.warning('融合通信已下线！');
  }
  return support;
}

function isSupportMediaDevices() {
  return navigator.mediaDevices && navigator.mediaDevices.getUserMedia;
}

//获取uuid
const uuid = () => {
  var s = [];
  var hexDigits = '0123456789abcdef';
  for (var i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
  }
  s[14] = '4'; // bits 12-15 of the time_hi_and_version field to 0010
  s[19] = hexDigits.substr((Number(s[19]) & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[8] = s[13] = s[18] = s[23] = '-';

  var uuid = s.join('');
  return uuid;
};

function addCssByLink(url) {
  var doc = document;
  var link = doc.createElement('link');
  link.setAttribute('rel', 'stylesheet');
  link.setAttribute('type', 'text/css');
  link.setAttribute('href', url);

  var heads = doc.getElementsByTagName('head');
  if (heads.length) heads[0].appendChild(link);
  else doc.documentElement.appendChild(link);
}

function addScript(url) {
  var doc = document;
  var script = document.createElement('script');
  script.setAttribute('type', 'text/javascript');
  script.setAttribute('src', url);

  var heads = doc.getElementsByTagName('head');
  if (heads.length) heads[0].appendChild(script);
  else doc.documentElement.appendChild(script);
}


const encryption = (content) => {
  let key = 'xzk0kKx1ehUYGPxp';
  // key格式化处理
  key = CryptoJS.enc.Utf8.parse(key);
  // 偏移量长度为16位, 注：偏移量需要与后端定义好，保证一致
  let iv = '';
  const encryptedContent = CryptoJS.AES.encrypt(content, key, {
    iv: '',
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  });
  return CryptoJS.enc.Base64.stringify(encryptedContent.ciphertext);
};

export {
  encryption,
  supportDetection,
  isSupportMediaDevices,
  uuid,
  addCssByLink,
  addScript
};
