/*
 * @Author: fanjialong <EMAIL>
 * @Date: 2023-04-17 15:06:09
 * @LastEditors: fanjialong
 * @LastEditTime: 2023-09-12 19:30:57
 * @FilePath: \hs_dp\src\api\hs\hs.api.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import http from '@/utils/leader/request'
import {
	bjnjUrl,
	baseUrl,
	baseUrl21,
	baseTokenUrl,
	messageGroupTokenUrl,
	messageGroupUrl,
	appServiceInitAddressUrl,
} from '@/utils/leader/const'

// 指挥调度-应急救灾中心分布
export const getEmergencyCenterDistribution = () => {
	return http.get(bjnjUrl + '/api/zl/getEmergencyCenterDistribution')
}

// 指挥调度-应急救灾中心统计
export const getEmergencyCenterList = (data) => {
	return http.get(bjnjUrl + '/api/zl/getEmergencyCenterList', data)
}

// 指挥调度-应急救灾中心统计
export const todoBScreen = (data) => {
	return http.get(bjnjUrl + '/FlowController/todoBScreen', data)
}

// 区县级列表查询
export const ddrwScreen = (data) => {
	return http.post(bjnjUrl + '/FlowController/ddrwScreen', data)
}

// 气象预警
export const getAlarm = (data) => {
	return http.post(bjnjUrl + '/api/weather/getAlarm', data)
}

// 气象预警
export const getAllAlarmWithGPS = (data) => {
	return http.post(bjnjUrl + '/api/weather/getAllAlarmWithGPS', data)
}

//根据气象类型和预警等级、行政区划获取气象数据
export const getAllAlarmWithGPSV2 = (data) => {
	return http.post(bjnjUrl + '/api/weather/getAllAlarmWithGPSV2', data)
}

// 指挥调度-应急救灾中心统计
export const getCscpBasicHxItemCode = (data) => {
	return http.get(bjnjUrl + '/api/dic/cscpHxDicItems/getCscpBasicHxItemCode?dictCode=' + data)
}

//指挥调度机具进度详情
export const api_getJjjdItemScreen = (data) => {
	return http.post(bjnjUrl + '/FlowController/queryScheduleInfo', data)
}

// 列表查询
export const ddqqScreen = (data) => {
	return http.post(bjnjUrl + '/FlowController/ddqqScreen', data)
}

// 应急救灾中心
export const queryPageTEmergencyCenterByCondition = (data) => {
	return http.post(bjnjUrl + '/api/tEmergencyCenters/queryPageTEmergencyCenterByCondition', data)
}

// 所属区域
export const areaTree = () => {
	return http.get(bjnjUrl + '/areaInfo/areaTree')
}

// 应急救灾中心
export const queryPageTDryingCenterByCondition = (data) => {
	return http.post(bjnjUrl + '/api/tDryingCenters/queryPageTDryingCenterByCondition', data)
}

// 机具保有量
export const queryPageTAgmachInfoByCondition = (data) => {
	return http.post(bjnjUrl + '/api/tAgmachInfos/queryPageTAgmachInfoByCondition', data)
}

// 查看详情（流程未结束查看，zstatus不为2）
export const flowList = (data) => {
	return http.post(bjnjUrl + '/FlowController/flowList', data)
}

// 流程结束后zstatus为2的查看接口
export const queryFuPanById = (data) => {
	return http.post(bjnjUrl + '/FlowController/queryFuPanById', data)
}

// 上报
export const c = (data) => {
	return http.post(bjnjUrl + '/FlowController/c', data)
}

// 上报
export const i = (data) => {
	return http.post(bjnjUrl + '/FlowController/i', data)
}

// 任务下发
export const e = (data) => {
	return http.post(bjnjUrl + '/FlowController/e', data)
}

// 任务下发
export const distributionAction = () => {
	return http.post(bjnjUrl + '/FlowController/distributionAction')
}

// 作业完成
export const workConfirm = (data) => {
	return http.post(bjnjUrl + '/FlowController/workConfirm', data)
}

// 任务下发
export const distributionActionForECenter = (data) => {
	return http.get(bjnjUrl + '/FlowController/distributionActionForECenter1', data)
}

// 位置跟踪
export const wzgz = (data) => {
	return http.post(bjnjUrl + '/FlowController/wzgz', data)
}

// 作业监督
export const queryzyjdList = (data) => {
	return http.get(bjnjUrl + '/FlowController/queryzyjdList?id=' + data.id + '&startTime=' + data.startTime)
}

// 作业监督
export const cscpCurrentUserDetails = (data) => {
	return http.get(bjnjUrl + '/api/user/system/cscpCurrentUserDetails')
}

// 上报
export const h = (data) => {
	return http.post(bjnjUrl + '/FlowController/h', data)
}

// 合并上报
export const d = (data) => {
	return http.post(bjnjUrl + '/FlowController/d', data)
}

// 调度请求地图打点
export const ddqqScreenDd = (data) => {
	return http.post(bjnjUrl + '/FlowController/ddqqScreenDd', data)
}

// 调度任务地图打点
export const ddrwScreenDd = (data) => {
	return http.post(bjnjUrl + '/FlowController/ddrwScreenDd', data)
}

// 点位弹窗
export const screenDdInfo = (data) => {
	return http.post(bjnjUrl + '/FlowController/screenDdInfo?id=' + data)
}

// 点位弹窗
export const screenLogout = () => {
	return http.post(bjnjUrl + '/api/single/screenLogout')
}

// 查询自定义范围内的农机信息
export const peripheryList = (params) => {
	return http.get(bjnjUrl + '/FlowController/distributionActionForECenter2', params)
}

// 工况
export const getAgmachWorkConditionNew = (data) => {
	return http.get(bjnjUrl + '/api/screen/getAgmachWorkConditionNew', data)
}

// 3.5.23 指定日期范围内农机历史作业信息(农大田路分割)
export const getAgmachWorkHistory = (data) => {
	// return http.get(bjnjUrl + '/api/screen/getAgmachWorkHistoryNew', data)
	return http.get(baseUrl + '/daas/api/agmach/v1/work/segment', data)
}

// 3.4.2 查询农机历史轨迹信息
export const getAgmachGuiJiInfo = (data) => {
	// return http.get(bjnjUrl + '/api/screen/getAgmachGuiJiInfo', data)
	return http.get(baseUrl + '/daas/api/agmach/v1/loc/history', data)
}

// 3.4.2 查询农机实时轨迹信息
export const getAgmachRealTimeGuiJiInfo = (data) => {
	// return http.get(bjnjUrl + '/api/screen/getAgmachGuiJiInfo', data)
	return http.get(baseUrl21 + '/daas/api/agmach/v1/loc/history', data)
}

// 获取补贴信息
export const querySubsidyInfo = (data) => {
	// return http.get(bjnjUrl + '/api/screen/getAgmachWorkHistoryNew', data)
	return http.get(bjnjUrl + '/api/screen/querySubsidyInfo', data)
}

// 条件分页条件查询
export const getDrillTask = (data) => {
	return http.get(bjnjUrl + '/drillController/getDrillTask', data)
}

// 条件分页条件查询
export const getDrillTaskState = (data) => {
	return http.get(bjnjUrl + '/drillController/getDrillTaskState', data)
}

// 条件分页条件查询
export const getDrillTaskInfo = (data) => {
	return http.get(bjnjUrl + '/drillController/getDrillTaskInfo', data)
}

// 条件分页条件查询
export const getDrillTaskTrack = (data) => {
	return http.get(bjnjUrl + '/drillController/getDrillTaskTrack', data)
}

// 上报
export const drillConfirm = (data) => {
	return http.post(bjnjUrl + '/drillController/drillConfirm', data)
}

// 上报
export const getDrillTaskReport = (data) => {
	return http.get(bjnjUrl + '/drillController/getDrillTaskReport', data)
}

// 上报
export const assignableResource = (data) => {
	return http.post(bjnjUrl + '/FlowController/assignableResource', data)
}

// 上报
export const g = (data) => {
	return http.post(bjnjUrl + '/FlowController/g', data)
}

// 上报
export const getDrillTaskList = (data) => {
	return http.post(bjnjUrl + '/drillController/getDrillTaskList', data)
}

// 上报
export const getDrillTaskNum = (data) => {
	return http.get(bjnjUrl + '/drillController/getDrillTaskNum', data)
}

// 上报
export const beforeD = (data) => {
	return http.get(bjnjUrl + '/FlowController/beforeD', data)
}
// 上报
export const getJWD = (data) => {
	return http.get(bjnjUrl + '/FlowController/getJWD', data)
}

// 视频会商相关
// 获取通讯录组织树
export const getOrgTree = (data = {}) => {
	return http.get(bjnjUrl + '/api/deptManage/selectDeptAndUserTree', data)
}
// 发送短信
export const sendInviteMessage = (data = {}) => {
	return http.post(bjnjUrl + '/api/tMessagePushs/sendMeetingUrl', data)
}

export const queryWorkAreaTop5 = (data = {}) => {
	return http.get(bjnjUrl + '/api/screen/queryWorkAreaTop5', data)
}
// 生成小程序链接
export const generateWxLink = (data = {}, headers = {}) => {
	return http.get('https://msconsole.camtc.cn/app/api/wx/urlLink', data, headers)
}

// 上报
export const queryByList = (data) => {
	return http.get(bjnjUrl + '/areaInfo/queryByList', data)
}

// 农机云token
export const getDzToken = (data) => {
	return http.get(bjnjUrl + '/api/screen/getDzToken', data)
}
// 查询各区域农机分布数量
export const getDistributionCount = (data) => {
	return http.get(bjnjUrl + '/daas/api/agmach/v1/area/distribution/count', data)
}

//查询各区域在线农机分布数量
export const getZxnjDistributionCount = (data) => {
	return http.get(bjnjUrl + '/daas/api/agmach/v1/online/area/distribution/count', data)
}
// 发送短信（多个）
export const sendMessageToSocialResources = (data = {}) => {
	return http.post(bjnjUrl + '/api/tMessagePushs/sendMessageToSocialResources', data)
}

// 农机e通app对接

// ---------------新优化------------------

// 应急事件统计
export const todoAScreen = (data) => {
	return http.get(bjnjUrl + '/FlowController/todoAScreen', data)
}

//应急事件数据列表
export const yjsjScreen = (data) => {
	return http.post(bjnjUrl + '/FlowController/ddqqScreen', data)
}

//机具进度数据列表
export const api_jjjdScreen = (data) => {
	const { page, size, ...others } = data
	return http.post(bjnjUrl + `/FlowController/querySchedulePage?page=${page}&size=${size}`, { ...others })
}

// 应急事件地图打点
export const yjsjScreenDd = (data) => {
	return http.post(bjnjUrl + '/FlowController/ddqqScreenDd', data)
}

// 指挥调度-应急救灾中心统计
export const getResourceList = (data) => {
	return http.get(bjnjUrl + '/api/zl/getResourceList', data)
}

// 指挥调度-救灾机具统计
export const getMachineryType = (data) => {
	return http.get(bjnjUrl + '/api/zl/getMachineryType', data)
}

//查询气象预警大屏数据
export const api_getQxyjData = (data) => {
	return http.post(bjnjUrl + '/api/weather/getAlarmV2', data)
}

//查询机具进度数据
export const api_getJjjdDataList = (data) => {
	const { page, size, ...others } = data
	//列表获取
	// return http.post(bjnjUrl + `/FlowController/querySchedule?page=${page}&size=${size}`, { ...others })
	//分页获取
	return http.post(bjnjUrl + `/FlowController/querySchedulePage?page=${page}&size=${size}`, { ...others })
}

//根据多边形范围查询农机数据
export const api_getMachineryDataByPolygon = (data) => {
	const { type, level, pointData } = data
	//列表获取
	// return http.post(bjnjUrl + `/FlowController/querySchedule?page=${page}&size=${size}`, { ...others })
	//分页获取
	if (!level) {
		return http.post(bjnjUrl + `/api/tEmergencyCenters/getListByMap?type=${type}`, pointData)
	} else {
		return http.post(bjnjUrl + `/api/tEmergencyCenters/getListByMap?type=${type}&level=${level}`, pointData)
	}
}

export const api_scjgScreen = (data) => {
	return http.get(bjnjUrl + '/api/tEmergencyCenters/getCountyAgmachList', data)
}

export const getSig = (data) => {
	return http.post(bjnjUrl + '/api/trtc/sign', data)
}
export const sendNotice = (data) => {
	return http.post(bjnjUrl + '/api/trtc-message/to', data)
}
export const saveRoom = (data) => {
	return http.post(bjnjUrl + '/api/room-kit/save', data)
}
export const getRoom = (data) => {
	return http.post(bjnjUrl + '/api/room-kit/info/' + data)
}
export const endRoom = (data) => {
	return http.post(bjnjUrl + '/api/room-kit/end/' + data)
}
export const joinRoom = (data) => {
	return http.post(bjnjUrl + '/api/room-kit/join', data)
}
export const quitRoom = (data) => {
	return http.post(bjnjUrl + '/api/room-kit/quit', data)
}

//获取消息token
// export const api_getMessageGroupToken = (userName) => {
// 	let sessionStorage = window.sessionStorage

// 	const njCloudToken = sessionStorage.getItem('token')
// 	return http.get(
// 		messageGroupTokenUrl + `/open-api/token/v1/convert?userName=${userName}`,
// 		{},
// 		{
// 			Authorization: njCloudToken,
// 		},
// 	)
// }

//接口转接
export const api_getMessageGroupToken = (userName) => {
	let sessionStorage = window.sessionStorage

	const njCloudToken = sessionStorage.getItem('token')
	return http.get(bjnjUrl + `/api/idass/token`, { userName: userName }, {})
}

export const api_getWeatherData = (lng, lat) => {
	return http.get(
		`https://api.caiyunapp.com/v2.6/vzW22KoCx8dqshuq/${lng},${lat}/hourly?hourlysteps=1`,
		{},
		{ 'Access-Control-Allow-Origin': 'https://api.caiyunapp.com' },
	)
}

/**
 * 获取指挥小组列表的ids
 */
// export const api_getZhddxzList = (params) => {
// 	const { pageNumber, pageSize, lastMsgSeqId, types } = params
// 	let sessionStorage = window.sessionStorage
// 	const messageGroupParams = JSON.parse(sessionStorage.getItem('messageGroupParams') || '')
// 	const { userId } = messageGroupParams

// 	return http.get(
// 		messageGroupUrl +
// 			`/m-api/im/v1/chat/${userId}/chats?pageNumber=${pageNumber}&pageSize=${pageSize}&lastMsgSeqId=${lastMsgSeqId}&types=${types}`,
// 		{},
// 		{
// 			useIdaasToken: true,
// 		},
// 	)
// }

//接口转接
export const api_getZhddxzList = (params) => {
	let sessionStorage = window.sessionStorage
	const messageGroupParams = JSON.parse(sessionStorage.getItem('messageGroupParams') || '')
	const { userId } = messageGroupParams
	const params_ = {
		userId,
		...params,
	}
	return http.get(bjnjUrl + `/api/idass/chats`, params_, {
		useIdaasToken: true,
	})
}

// export const api_getZhddGroupInfoList = (groupIds) => {
// 	return http.get(
// 		appServiceInitAddressUrl + `/fs/m-api/v1/group/cache/list/simple`,
// 		{ groupIds: groupIds.join(',') },
// 		{ sourceType: appServiceInitAddressUrl },
// 	)
// }

//接口转换
export const api_getZhddGroupInfoList = (groupIds) => {
	return http.get(bjnjUrl + `/api/idass/chats/list`, { groupIds: groupIds.join(',') }, {useIdaasToken: true,})
}
