<template>
  <footer>
    <ul>
      <li
        v-for="(it, i) of btns"
        :key="i"
        :class="{ active: activaIdx === i }"
        @click="handleClick(i)"
      >
        <img :src=" activaIdx === i ? it.icon_a : it.icon" alt="" class="icons">
        <span class="label">{{ it.name }}</span>
      </li>
    </ul>
  </footer>
</template>

<script>
export default {
  name: 'LeaderFooter',
  props: {
    btns: {
      type: Array,
      default() {
        return []
      }
    },
    activaIdx: {
      type: Number,
      default: -1
    }
  },
  methods: {
    handleClick(i) {
      this.activaIdx = i
      this.$emit('mark', i)
    }
  }
}
</script>

<style lang="less" scoped>
footer {
  width: 1920px;
  height: 88px;
  position: absolute;
  left: 0;
  bottom: 0;
  background: url(~@/assets/csts/bottom-bg.png) no-repeat;
  z-index: 998;
  ul {
    display: flex;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 23px;
    li {
      position: relative;
      width: 110px;
      height: 53px;
      line-height: 41px;
      margin: 0 19px;
      background: url(~@/assets/cyjj/foot.png) no-repeat center / 100% 100%;
      display: flex;
      // align-items: center;
      padding: 7px 0 0 12px;
      .icons {
        width: 21px;
        height: 21px;
      }
      span {
        font-size: 17px;
        font-family: YouSheBiaoTiHei;
        color: #e9fffe;
        background: linear-gradient(180deg, #ffffff 0%, #2f8af4 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        cursor: pointer;
        display: block;
        line-height: 21px;
      }
      &.active {
        background: url(~@/assets/cyjj/footActive.png) no-repeat center / 100% 100%;
        span {
          color: #edfffd;
          background: linear-gradient(180deg, #fdfeff 0%, #cbedff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
  }
}
</style>
