<!--
 * @Author: fanjialong <EMAIL>
 * @Date: 2023-04-15 10:29:09
 * @LastEditors: fanjialong 
 * @LastEditTime: 2023-08-26 12:45:25
 * @FilePath: \hs_dp\src\views\leader\components\zhdd\VideoSurveillance.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="wrap">
    <hlsVideo class="video_" :src="cameraUrl" :cameraCode='cameraId'></hlsVideo>
  </div>
</template>

<script>
import LivePlayer from '@liveqing/liveplayer'
import { getCameraMakers, getCameraXq } from '@/api/hs/hs.api.js'
import hlsVideo from '@/components/leader/hlsVideo'

export default {
  name: 'ZhddVideoSurveillance',
  props: {
    padding: {
      type: String,
      default: '23px 22px 18px'
    },
    videos: {
      type: Array,
      default: () => [
        require('@/assets/leader/video/video1.mp4'),
        require('@/assets/leader/video/video2.mp4'),
        require('@/assets/leader/video/video4.mp4'),
        require('@/assets/leader/video/video8.mp4')
      ]
    },
    cameraId: {
      type: String,
      default: ''
    }
  },
  components: { LivePlayer, hlsVideo },
  data() {
    return {
      textUrl: 'http://**********:8088/fourthfloor/stream2_01/hls.m3u8',
      cameraUrl: null
    }
  },
  created(){
    console.log('this.cameraId', this.cameraId)
    this.cameraXq()
  },
  mounted() {
    // this.cameraXq()
  },
  methods: {
    async cameraXq() {
      const res = await getCameraXq(this.cameraId)
      console.log('res', res)
      if (res && res.body && res.body.data && res.body.data.length > 0 && res.body.data[0].url) {
        this.cameraUrl = res.body.data[0].url
      }
    }
  }
}
</script>

<style lang="less" scoped>
.wrap {
  width: 100%;
  height: 100%;
}
</style>
