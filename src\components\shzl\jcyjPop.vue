<template>
  <div>
    <div class="szyyPop">
      <img src="@/assets/shzl/map/camera/close.png" alt="" class="close" @click="close" />
      <div class="title">
        <div class="title_name">接入状态</div>
      </div>
      <div class="center">
        <div class="center_left">
          <!-- <div class="type_">
        <div class="active">列表模式</div>
        <div>随机模式</div>
      </div> -->
          <div class="center_left1">
            <div class="center_item1">设备数量</div>
            <div>
              <div class="center_item">
                <div class="center_num">2123</div>
                <img src="@/assets/shzl/map/camera//sp1.png" alt="" />
                <div class="center_name">全部</div>
              </div>
              <div class="center_item">
                <div class="center_num">2118</div>
                <img src="@/assets/shzl/map/camera//sp2.png" alt="" />
                <div class="center_name">在线</div>
              </div>
              <div class="center_item">
                <div class="center_num">5</div>
                <img src="@/assets/shzl/map/camera//sp3.png" alt="" />
                <div class="center_name">离线</div>
              </div>
            </div>
          </div>
          <div class="center_left2">
            <div class="center_item1">
              视频接入
              <!-- <div class="center_list" :class="title == 1 ? 'active' : ''" @click="titleShow(1)">
              专题搜索<img src="@/assets/shzl/map/camera//titleBg.png" v-if="title == 1" alt="" />
            </div>
            <div class="center_list" :class="title == 2 ? 'active' : ''" @click="titleShow(2)">
              区域搜索<img src="@/assets/shzl/map/camera//titleBg.png" v-if="title == 2" alt="" />
            </div> -->
            </div>
            <div class="center_item2">
              <el-input v-model="cameraValue" placeholder="搜索"></el-input>
              <!-- <input type="text" placeholder="搜索"> -->
              <img src="@/assets/shzl/map/camera//ss.png" alt="" />
            </div>
            <div class="center_item3">
              <el-tree
                :data="data1"
                show-checkbox
                node-key="id"
                :props="defaultProps"
                ref="jiedaoTree"
                default-expand-all
                :filter-node-method="filterNode"
              >
              </el-tree>
            </div>
          </div>
        </div>
        <div class="center_right">
          <div class="center_right1">2022/06/07 10:23:01</div>
          <div class="center_right2">
            <div class="center_right2Item">
              <!-- <img src="@/assets/shzl/map/camera//img1.png" alt="" /> -->
              <!-- <hlsVideo :src="testUrl1"></hlsVideo> -->
              <LivePlayer :videoUrl="video1" fluent autoplay live stretch></LivePlayer>
            </div>
            <div class="center_right2Item">
              <!-- <img src="@/assets/shzl/map/camera//img2.png" alt="" /> -->
              <!-- <hlsVideo :src="testUrl2"></hlsVideo> -->
              <LivePlayer :videoUrl="video2" fluent autoplay live stretch></LivePlayer>
            </div>
            <div class="center_right2Item">
              <!-- <img src="@/assets/shzl/map/camera//img3.png" alt="" /> -->
              <!-- <hlsVideo :src="testUrl3"></hlsVideo> -->
              <LivePlayer :videoUrl="video3" fluent autoplay live stretch></LivePlayer>
            </div>
            <div class="center_right2Item">
              <!-- <img src="@/assets/shzl/map/camera//img4.png" alt="" /> -->
              <!-- <hlsVideo :src="testUrl4"></hlsVideo> -->
              <LivePlayer :videoUrl="video4" fluent autoplay live stretch></LivePlayer>
            </div>
          </div>
          <div class="center_right3">
            <div class="center_right3Item">
              <el-select
                v-model="company2"
                filterable
                size="small"
                placeholder="请选择"
                @change="changeSelect1"
              >
                <el-option
                  v-for="item in optionsList1"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
            <img src="@/assets/shzl/map/camera//qp.png" alt="" />
          </div>
        </div>
      </div>
    </div>
    <transition name="fade">
      <div class="bg-header"></div>
    </transition>
  </div>
</template>

<script>
import hlsVideo from '@/components/leader/hlsVideo'
import LivePlayer from '@liveqing/liveplayer'
export default {
  props: ['data'],
  components: {
    hlsVideo,
    LivePlayer
  },
  data() {
    return {
      title: 1,
      data1: [
        {
          id: 1,
          label: '建邺区',
          children: [
            {
              id: 4,
              label: '江心洲街道',
              children: [
                {
                  id: 9,
                  label: '视频设备1'
                },
                {
                  id: 10,
                  label: '视频设备2'
                }
              ]
            },
            {
              id: 5,
              label: '沙洲街道',
              children: [
                {
                  id: 11,
                  label: '视频设备1'
                },
                {
                  id: 12,
                  label: '视频设备2'
                }
              ]
            },
            {
              id: 6,
              label: '和谐社区',
              children: []
            }
          ]
        }
      ],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      company2: 2,
      optionsList1: [
        {
          value: 1,
          label: '标清'
        },
        {
          value: 2,
          label: '高清'
        },
        {
          value: 3,
          label: '超清'
        }
      ],
      cameraValue: '',
      testUrl1: 'http://10.2.32.45:8088/firstfloor/stream1/hls.m3u8',
      testUrl2: 'http://10.2.32.45:8088/sixthfloor/stream1_01/hls.m3u8',
      testUrl3: 'http://10.2.32.45:8088/sixthfloor/stream1_01/hls.m3u8',
      testUrl4: 'http://10.2.32.45:8088/firstfloor/stream2/hls.m3u8',
      video1: require('@/assets/leader/video/video1.mp4'),
      video2: require('@/assets/leader/video/video2.mp4'),
      video3: require('@/assets/leader/video/video4.mp4'),
      video4: require('@/assets/leader/video/video8.mp4')
    }
  },
  methods: {
    close() {
      this.$emit('closeEmit')
    },
    titleShow(index) {
      this.title = index
    },
    changeSelect1() {},
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    }
  },
  watch: {
    cameraValue(val) {
      this.$refs.jiedaoTree.filter(val)
    }
  }
}
</script>

<style lang="scss" scoped>
.bg-header {
  position: absolute;
  width: 100%;
  height: 1080px;
  background: rgba(2, 14, 50, 0.75);
  z-index: 999999;
  top: 0;
  left: 0;
}
// @font-face {
//   font-family: YouSheBiaoTiHei;
//   src: url(~@/assets/special/font/YouSheBiaoTiHei-2.ttf);
// }
.szyyPop {
  width: 1232px;
  height: 656px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center;
  background-image: url(~@/assets/shzl/map/camera/szyyBg.png);
  padding: 0 16px;
  padding-top: 21px;
  position: absolute;
  z-index: 101;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -40%);
  z-index: 99999999;
  .close {
    position: absolute;
    top: 16px;
    right: 15px;
    cursor: pointer;
  }
  .title {
    width: 1200px;
    height: 32px;
    line-height: 32px;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
    background-image: url(~@/assets/shzl/map/camera/szyyTop.png);
    .title_name {
      font-size: 18px;
      font-family: FZSKJW--GB1-0, FZSKJW--GB1;
      font-weight: bold;
      color: #22fcff;
      text-align: center;
      letter-spacing: 1px;
      background: linear-gradient(180deg, #ffffff 0%, #01c3ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .center {
    width: 1200px;
    height: 600px;
    .type_ {
      display: flex;
      // justify-content: space-between;
      align-items: center;
      margin: 17px 0 8px 14px;
      & > div {
        width: 76px;
        height: 30px;
        line-height: 30px;
        font-size: 13px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ccf4ff;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        background-position: center;
        background-image: url(~@/assets/shzl/map/camera/type_bg.png);
        &.active {
          background-image: url(~@/assets/shzl/map/camera/type_bg_active.png);
        }
        &:first-of-type {
          margin-right: 10px;
        }
      }
    }
    .center_left {
      width: 300px;
      // height: 600px;
      float: left;
      margin-left: 15px;
      padding: 20px 0 0 0;
      .center_left1 {
        width: 300px;
        height: 149px;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        background-position: center;
        background-image: url(~@/assets/shzl/map/camera/left1.png);
        // margin: 20px 0 0 0;
        padding: 9px 18px 0px 18px;
        .center_item1 {
          margin: 0px 0 8px 7px;
          font-size: 20px;
          font-family: YouSheBiaoTiHei;
          color: #ffffff;
          line-height: 26px;
          // text-shadow: 0px 0px 4px #00575d;
          background: linear-gradient(180deg, #eeeeee 0%, #43f4ff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          text-align: left;
        }
        .center_item {
          width: 88px;
          float: left;
          // padding-top: 13px;
          .center_num {
            font-size: 22px;
            font-family: DINAlternate-Bold, DINAlternate;
            font-weight: bold;
            color: #ffffff;
            line-height: 26px;
            text-align: center;
            text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.5);
            background: linear-gradient(180deg, #ffffff 0%, #00d5ff 65%, #04d8ff 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          img {
            margin: 0 17px;
          }
          .center_name {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 20px;
            text-align: center;
          }
          &:nth-child(1) .center_num {
            background: linear-gradient(180deg, #ffffff 0%, #00d5ff 65%, #04d8ff 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          &:nth-child(2) .center_num {
            background: linear-gradient(180deg, #ffffff 0%, #00ffa8 67%, #04ffa4 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          &:nth-child(3) .center_num {
            background: linear-gradient(180deg, #ffffff 0%, #b7b7b7 67%, #9a9a9a 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
      }
      .center_left2 {
        width: 300px;
        height: 360px;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        background-position: center;
        background-image: url(~@/assets/shzl/map/camera/left2.png);
        margin: 8px 0 0 0;
        padding: 12px 0 0 0;
        .center_item1 {
          // width: 256px;
          // height: 51px;
          // line-height: 51px;
          margin: 0 0 13px 25px;
          font-size: 20px;
          font-family: YouSheBiaoTiHei;
          color: #ffffff;
          line-height: 26px;
          // text-shadow: 0px 0px 4px #00575d;
          background: linear-gradient(180deg, #eeeeee 0%, #43f4ff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          text-align: left;

          // .center_list {
          //   width: 50%;
          //   height: 51px;
          //   text-align: center;
          //   float: left;
          //   font-size: 20px;
          //   font-family: YouSheBiaoTiHei;
          //   color: rgba(129, 205, 255, 0.6);
          //   text-shadow: 0px 0px 4px #00575d;
          //   img {
          //     float: left;
          //     margin: -19px 0 0 24px;
          //   }
          // }
          // .active {
          //   font-size: 20px;
          //   font-family: YouSheBiaoTiHei;
          //   color: #ffffff;
          //   text-shadow: 0px 0px 4px #00575d;
          //   background: linear-gradient(180deg, #eeeeee 0%, #43f4ff 100%);
          //   -webkit-background-clip: text;
          //   -webkit-text-fill-color: transparent;
          // }
        }
        .center_item2 {
          width: 256px;
          height: 34px;
          line-height: 34px;
          color: #fff;
          text-align: left;
          background: rgba(0, 74, 143, 0.4)
            linear-gradient(
              360deg,
              rgba(181, 223, 248, 0) 0%,
              rgba(29, 172, 255, 0.29) 100%,
              #ffffff 100%
            );
          border-radius: 4px;
          border: 1px solid rgba(0, 162, 255, 0.6);
          margin: 0 auto;
          // padding-left: 16px;
          img {
            float: right;
            margin: 10px 10px 0 0;
          }
        }
        .center_item3 {
          padding: 0 22px;
          margin-top: 20px;
        }
      }
    }
    .center_right {
      width: 860px;
      height: 600px;
      float: left;
      margin-left: 15px;
      padding-top: 20px;
      .center_right1 {
        width: 860px;
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 20px;
        letter-spacing: 1px;
        padding-left: 5px;
        text-align: left;
      }
      .center_right2 {
        width: 860px;
        height: 502px;
        padding: 3px 0;
        .center_right2Item {
          width: 420px;
          height: 236px;
          float: left;
          margin: 5px;
        }
      }
      .center_right3 {
        width: 860px;
        height: 30px;
        .center_right3Item {
          float: left;
        }
        img {
          float: right;
          margin: 4px 11px 0 0;
        }
      }
    }
  }
}
::v-deep .el-tree {
  background: transparent;
  color: #f6feff;
  .el-tree-node__content:hover {
    background: transparent !important;
  }
  .el-checkbox__inner {
    background: transparent !important;
  }
}
::v-deep .el-tree-node:focus > .el-tree-node__content {
  background-color: transparent !important;
}

::v-deep .el-tree-node__content:hover {
  background: transparent !important;
}
::v-deep .el-select {
  width: 71px;
  height: 30px;
  .el-input__inner {
    font-size: 13px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ccf4ff;
    line-height: 18px;
    background: rgba(0, 74, 143, 0.4)
      linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(0, 162, 255, 0.08) 100%, #ffffff 100%);
    border-radius: 15px;
    border: 1px solid rgba(0, 162, 255, 0.6);
    padding: 0 20px 0 12px;
  }
}
::v-deep .el-input__inner {
  background-color: transparent !important;
  border: none;
  color: #ccf4ff;
  height: 34px;
}
</style>
