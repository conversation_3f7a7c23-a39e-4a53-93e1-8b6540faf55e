# 引用

 将layui放入项目中,并在html页面引入

```html
<link rel="stylesheet" href="./layui/css/layui.css">
<script src="./layui/layui.js"></script>
```

将core放入项目，引用core/index.js

```js
import rtc from "./core/index";
```



## 注册

### 接口映射

core/config.js下

| key         | value                                                        |
| ----------- | ------------------------------------------------------------ |
| API_URL_MAP | http接口映射   会根据key接口请求对应的http地址   匹配不到选择浏览器当前地址 格式(ip:port : http地址) |
| WS_URL_MAP  | ws接口映射 会根据key 接口请求对应的ws地址   匹配不到选择浏览器当前地址  格式(ip:port : ws地址) |

```js
// http映射   {host:目标origin}
const API_URL_MAP = {
  'localhost:8080': 'https://*************:8100',
  '*************':'https://*************:8100',
};

// ws接口映射   {host:目标ws地址}   
//如果是http就是ws://ip:port/ws
//如果是https就是wss://ip:port/wss
const WS_URL_MAP = {
  'localhost:8080': 'wss://*************:8100/wss',
  'localhost:8080': 'wss://*************:8100/wss'
};
```



**所有的方法必须在rtc.regiest注册后才能使用**

**所有的方法必须在rtc.regiest注册后才能使用**

**所有的方法必须在rtc.regiest注册后才能使用**



## 注册

#### method

| 方法名      | **说明** | **类型**                                      |
| ----------- | -------- | :-------------------------------------------- |
| rtc.regiest | 注册     | （loginParam:LoginParam） => Promise<RtcCore> |

#### LoginParam参数

| 属性名       | 说明                                              | 类型   | 必填  |
| ------------ | ------------------------------------------------- | ------ | ----- |
| account      | 登录账号                                          | string | true  |
| password     | 密码                                              | string | true  |
| *last_token* | 强制登录时携带 从userInfo.config.last_token中获取 | string | false |

#### 示例

```js
await rtc.regiest({
    url,
    account,
    password
});
```



## ws消息监听

#### method

| 方法名    | **说明**              | **类型**                                                     |
| --------- | --------------------- | ------------------------------------------------------------ |
| rtc.ws.on | 监听ws消息,并进行处理 | 批量监听: on('*',{cmd1:callBack1,cmd2:callBack2})  <br />单个监听: on('cmd1',callback1) |

##### 多个监听示例

```js
rtc.ws.on('*',{
  extension_state_notice:callback,
})

function callback(data){    //data 查看ws文档  消息data
    console.log('状态变更了',data)
}
```

##### 单个监听示例

```js
rtc.ws.on('extension_state_notice',(data) => { //data 查看ws文档  消息data
  console.log('状态变更了',data)
})
```



# 会议



#### 方法

| 方法名                    | 说明                                                         | 传参类型                                                     |
| ------------------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| rtc.meet.mounted          | 会议放入某个元素里 <br />不传以弹窗形式展现<br />传入元素id以内嵌的方式嵌入元素内部 | id?:string                                                   |
| rtc.meet.callPopFormal    | 呼叫正式会议 <br />meetnum:会议号码<br />users:添加人员手机号 多个以数组形式传入 | ({ *meetnum:string*, *users?:phone \| phone[]* }) => Promise<MeetSession> |
| rtc.meet.callPopTempVoice | 呼叫临时语音会议<br />users:添加人员手机号 多个以数组形式传入 | (users?:phone \| phone[]) => Promise<MeetSession>            |
| rtc.meet.callPopTempVideo | 呼叫临时视频会议<br />users:添加人员手机号 多个以数组形式传入 | (users?:phone \| phone[] ) => Promise<MeetSession>           |

##### 会议内嵌

###### 示例

```js
rtc.meet.mounted('元素id值')
```



##### 呼叫正式会议

###### 示例

```js

const meetSession = rtc.meet.callPopFormal({meetnum:20003,users:[6001,6002]})
```



##### 呼叫临时语音会议

###### 示例

```js
const meetSession = rtc.meet.callPopTempVoice({users:[6001,6002]})
```



##### 呼叫临时视频会议

###### 示例

```js
const meetSession = rtc.meet.callPopTempVideo({users:[6001,6002]})
```



#### 属性

###### meet

| 属性名      | 说明     | 类型        |
| ----------- | -------- | ----------- |
| meetSession | 会议信息 | MeetSession |

#### MeetSession

###### 属性

| 属性名        | 说明                                         | 类型    |
| ------------- | -------------------------------------------- | ------- |
| publishPcInfo | 推流信息                                     |         |
| playerPcInfo  | 拉流信息 主要获取stream属性                  |         |
| meet          | ws消息会议的meet信息 主要来自meet_in消息     |         |
| meetAllUsers  | 会议中人员 主要来自meet_in消息中meetAllUsers | Array   |
| isCompere     | 登陆人是否是会议发起者                       | Boolean |
| userInfo      | 登陆人信息                                   |         |
| state         | 会议状态  available:空闲  meetIn:入会        |         |

###### 方法

| 方法名  | 说明                                                         | 类型               |
| ------- | ------------------------------------------------------------ | ------------------ |
| addUser | 添加用户 phone:用户手机号 <br />例:单个addUser(6000)   多个addUser([6000,6001,6002]) | (phone) => Promise |

##### 会议添加人员

###### 示例

```js
meetSession.addUser([6001,6002])
```



# 点对点



#### ws重要通知

| 通知名称           | 说明                                 | 接收参数                                                   |
| :----------------- | :----------------------------------- | :--------------------------------------------------------- |
| before_mine_answer | 别人呼叫我，我点击接听按钮处理的操作 | {*target*:对方号码, *mountedEl:挂载函数*, *wsInfo:ws信息*} |

```js
let currentMountEl = document.querySelect('xxx');
window._rtc.ws.on('before_mine_answer', ({ target, mountedEl, wsInfo }) => {
  mountedEl(currentMountEl);//挂载
  console.log(target,wsInfo)
});
```

| 方法名   | 说明                   | 传参类型 |
| -------- | ---------------------- | -------- |
| moutedEl | 将视频挂载到某个元素内 | el       |



#### 方法

| 方法名          | 说明                                                         | 传参类型                                                    |
| --------------- | ------------------------------------------------------------ | ----------------------------------------------------------- |
| rtc.p2p.callPop | {<br />el:挂载元素<br />视频放入某个元素里 <br />不传以弹窗形式展现.<br />target:目标号码,<br />callType:呼叫类型} | {el?:Element,target:string,callType:'voice'\|'voice_video'} |

##### 主动呼叫

###### 视频呼叫内嵌示例

```js
const el = document.querySelector('xxx')
window._rtc.p2p.callPop({
    el: el,
    target: '6003',
    callType: 'voice_video'
});
```

###### 视频呼叫弹框示例

<!-- ```js
window._rtc.p2p.callPop({
    target: '6003',
    callType: 'voice'
});
``` -->
```js
rtc.p2p.callPop({
    el: _this.$refs.video,
    target: '6003',
    callType: 'voice'
});
```





#### 属性

###### meet

| 属性名      | 说明     | 类型        |
| ----------- | -------- | ----------- |
| meetSession | 会议信息 | MeetSession |

#### MeetSession

###### 属性

| 属性名        | 说明                                         | 类型    |
| ------------- | -------------------------------------------- | ------- |
| publishPcInfo | 推流信息                                     |         |
| playerPcInfo  | 拉流信息 主要获取stream属性                  |         |
| meet          | ws消息会议的meet信息 主要来自meet_in消息     |         |
| meetAllUsers  | 会议中人员 主要来自meet_in消息中meetAllUsers | Array   |
| isCompere     | 登陆人是否是会议发起者                       | Boolean |
| userInfo      | 登陆人信息                                   |         |
| state         | 会议状态  available:空闲  meetIn:入会        |         |

