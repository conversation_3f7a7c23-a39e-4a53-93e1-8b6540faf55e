import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store/leader'
import '@/assets/leader/css/main.css'
import '@/assets/leader/css/base.css'
import '@/assets/leader/css/font.css'
import '@/assets/map/map.css'
import '@/assets/leader/css/elementUi.less'
import '@/assets/rhtx/base.css'
import '@/assets/leader/css/iviews.less'
// import 'leaflet-canvas-marker'
import 'leaflet-canvas-marker-xrr2021'
import VueAwesomeSwiper from 'vue-awesome-swiper'
import 'swiper/css/swiper.css'
import ElementUI, { Message } from 'element-ui'
import ShowMessage from '@/utils/message.js'
import 'element-ui/lib/theme-chalk/index.css'
// import http from '@/utils/leader/request'
import { bjnjUrl } from '@/utils/leader/const'
import wgHttp from '@/utils/wangge/request'
// import L from './utils/map'
import echarts from 'echarts'
import 'echarts-liquidfill'
import VueBus from 'vue-bus'
// Vue.prototype.$EventBus = new Vue()
import HighCharts from 'highcharts'
// import ECharts from 'vue-echarts';
import highcharts3d from 'highcharts/highcharts-3d'
import { initAnimate } from '@/utils/leader/highcart-3d-animation.js'
// 引入 highcharts 做 3D饼图
highcharts3d(HighCharts)
initAnimate(HighCharts) // 动画切换 效果

import downloadFile from '@/libs/download.js'
Vue.prototype.$downloadFile = downloadFile
//
import VueAMap from 'vue-amap'
import shzlChart from 'shzl-datav/lib/shzl-datav.common' // 要编写对应的文档的包
import 'shzl-datav/lib/shzl-datav.css'
// const cors = require('cors');

Vue.use(shzlChart, {
	grid: {
		left: '5%',
		right: '5%',
		bottom: '5%',
		top: '18%',
		containLabel: true,
	},
	xAxis: {
		name: '',
		nameGap: 1,
		nameTextStyle: {
			color: '#C2DDFC',
			fontSize: 14,
			padding: 10,
		},
		type: 'category',
		axisTick: {
			show: false,
		},
		axisLine: {
			show: true,
			lineStyle: {
				color: 'rgba(194, 221, 252, 0.5)',
			},
		},
		axisLabel: {
			show: true,
			rotate: 0,
			interval: 0,
			textStyle: {
				padding: [4, 0, 0, 0],
				fontSize: 14,
				color: 'rgba(194, 221, 252, 1)',
			},
		},
		data: [],
	},
	// 水平 方向
	xHorAxis: [
		{
			name: '',
			nameGap: 1,
			nameTextStyle: {
				color: '#C2DDFC',
				fontSize: 14,
				padding: 10,
			},
			type: 'value',
			axisLine: {
				show: false,
			},
			axisTick: {
				show: false,
			},
			axisLabel: {
				color: '#C2DDFC',
				fontSize: 14,
				margin: 10,
			},
			splitNumber: 4,
			splitLine: {
				lineStyle: {
					type: 'dashed',
					color: 'rgba(255, 255, 255, 0.15)',
				},
			},
		},
	],
	tooltip: {
		show: true,
		trigger: 'axis',
		axisPointer: {
			type: 'line',
			lineStyle: {
				color: '#fff',
			},
		},
		formatter: (params) => {
			// console.log('params', params);
			var relVal = params[0].name
			for (var i = 0, l = params.length; i < l; i++) {
				const str = params[i].seriesName ? params[i].seriesName + '：' : ''
				relVal += '<br/>' + params[i].marker + str + params[i].value + ' '
			}
			return relVal
		},
		backgroundColor: 'rgba(0, 33, 59, 0.8)',
		borderColor: 'rgba(24, 174, 236, 1)',
		padding: [5, 10],
		textStyle: {
			color: '#fff',
		},
		extraCssText: 'box-shadow: 0 0 5px rgba(0,0,0,0.3)',
	},
	yAxis: {
		name: '单位：万人次',
		nameTextStyle: {
			color: '#C2DDFC',
			fontSize: 14,
			align: 'center',
			padding: [0, 35, 10, 0],
		},
		type: 'value',
		triggerEvent: true,
		splitLine: {
			show: true,
			lineStyle: {
				color: 'rgba(255, 255, 255, 0.15)',
				type: 'dashed',
			},
		},
		axisTick: {
			show: false,
		},
		axisLine: {
			show: false,
		},
		axisLabel: {
			show: true,
			textStyle: {
				fontSize: 14,
				color: '#C2DDFC',
			},
		},
	},
	// 水平 方向
	yHorAxis: [
		{
			name: '',
			nameTextStyle: {
				color: '#C2DDFC',
				fontSize: 14,
				align: 'center',
				padding: [0, 35, 0, 0],
			},
			type: 'category',
			axisLine: {
				lineStyle: {
					width: 1,
					color: '#C6C6C6',
				},
			},
			axisLabel: {
				fontSize: 12,
				color: '#C2DDFC',
				margin: 5,
				// interval: 0
			},
			axisTick: {
				show: false,
			},
		},
	],
	legend: {
		show: true,
		// 图例组件，颜色和名字
		right: 50,
		top: 10,
		itemGap: 16,
		itemWidth: 10,
		itemHeight: 10,
		data: [],
		textStyle: {
			color: '#a8aab0',
			fontStyle: 'normal',
			fontFamily: '微软雅黑',
			fontSize: 12,
		},
	},
	// // 是否开启toolTip轮播
	isToolTipInterval: true,
	// // toolTip轮播时间
	toolTipIntervalTime: 5000,
})

// Vue.use(
// 	cors()
// )

// 注册自定义指令
Vue.directive('drag', (el, binding) => {
	el.onmousedown = (e) => {
		let disX = e.clientX - el.offsetLeft
		let disY = e.clientY - el.offsetTop
		el.onmousemove = function(e) {
			//计算需要移动的距离
			let tX = e.clientX - disX
			let tY = e.clientY - disY
			//移动当前元素
			// if (tX >= 0 && tX <= window.innerWidth - el.offsetWidth) {
			el.style.left = tX + 'px'
			// }
			// if (tY >= 0 && tY <= window.innerHeight - el.offsetHeight) {
			el.style.top = tY + 'px'
			// }
		}
		el.onmouseup = function(e) {
			el.onmousemove = null
			el.onmouseup = null
		}
		window.onmouseup = function(e) {
			el.onmousemove = null
			el.onmouseup = null
		}
	}
})

// 导入iView组件库
import iView from 'iview'
//导入iView样式
import 'iview/dist/styles/iview.css'

// 使用iView组件库
Vue.use(iView)

// import * as echarts from 'echarts';
import 'echarts-gl'
import CreateAPI from 'vue-create-api'
import dayjs from 'dayjs'
Vue.prototype.dayjs = dayjs //可以全局使用dayjs

// 引入Leaflet对象 挂载到Vue上，便于全局使用，也可以单独页面中单独引用
// Vue.L = Vue.prototype.$L = L

// 北海地图挂载变量
// Vue.prototype.mapReade = window.mapReade
// Vue.prototype.mapInit = window.mapInit

import '@/utils/wangge/index'

// 全局组件
import aiWaringPop from '@/components/common/common.js'
import cameraPop from '@/components/common/common.js'
import tipsPop from '@/components/common/common.js'
Vue.use(aiWaringPop) //ai弹窗
Vue.use(cameraPop) //视频通话弹窗
Vue.use(tipsPop) //提示S弹窗

Vue.config.productionTip = false

Vue.use(ElementUI)
Vue.use(VueAwesomeSwiper)
Vue.use(CreateAPI)
Vue.use(VueBus)
Vue.use(VueAMap)
Vue.prototype.$message = Message
Vue.prototype.$messageNew = new ShowMessage()
VueAMap.initAMapApiLoader({
	key: '6ee81bb710ab9f05c364b928e13cb722',
	plugin: [
		'AMap.Autocomplete',
		'AMap.PlaceSearch',
		'AMap.Scale',
		'AMap.OverView',
		'AMap.ToolBar',
		'AMap.MapType',
		'AMap.PolyEditor',
		'AMap.CircleEditor',
		'AMap.Geolocation',
		'Geolocation',
		'AMap.DistrictSearch',
	],
	v: '1.4.4',
})

// axios请求
Vue.prototype.$http = bjnjUrl
Vue.prototype.$wgHttp = wgHttp

// 引入echarts
Vue.prototype.$echarts = echarts

Vue.prototype.isEmpty = (content) => {
	return content === undefined || content === null || content === 'null' || content === '' || content === []
}

Vue.prototype.isEmptyObject = (content) => {
	return Object.keys(content).length === 0
}

// 时间戳
Vue.prototype.getTimeStamp2 = () => {
	let date = new Date()
	let year = date.getFullYear().toString() //获取当前年份
	let mon = (date.getMonth() + 1).toString() //获取当前月份
	let da = date.getDate().toString() //获取当前日.toString()
	let h = date.getHours().toString() //获取小时
	let m = date.getMinutes().toString() //获取分钟
	let s = date.getSeconds().toString() //获取秒
	return year + mon + da + h + m + s + Math.ceil(Math.random() * 1000)
}

// 判断数值是否为空，不存在等情况，默认为0
Vue.prototype.isNoNum = (num) => {
	if (num === undefined || num === null || num === 'null' || num === '' || num === [] || !num) {
		return '0'
	} else {
		return num + ''
	}
}

// 判断数值是否为空，不存在等情况，默认为'-'
Vue.prototype.isNoNum_ = (num) => {
	if (num === undefined || num === null || num === 'null' || num === '' || num === [] || !num) {
		return '-'
	} else {
		return num
	}
}

import countTo from 'vue-count-to'
Vue.component('countTo', countTo)

// vue-counto-to 默认属性配置
Vue.prototype.$countTo = {
	startVal: 0.0,
	duration: 5000,
	decimals: (num) => {
		const y = String(num).indexOf('.') + 1
		if (y === 0) return 0
		return String(num).length - y
	},
}

console.log('process.env.VUE_APP_BASE_URL', process.env.VUE_APP_MAP_TOKEN)

// vue-create-api

new Vue({
	router,
	store,
	render: (h) => h(App),
}).$mount('#app')

// 16px = 1rem
// 5 - 0.3125rem;
// 11 - 0.6875rem;
// 15 - 0.9375rem;
// 17 - 1.0625rem;
// 18 - 1.125rem;
// 22 - 1.375rem;
// 23 - 1.4375rem;
// 25 - 1.5625rem;
// 28 - 1.75rem;
// 30 - 1.875rem;
// 37 - 2.3125rem;
// 42 - 2.625rem;
// 47 - 2.9375rem;
// 70 - 4.375rem;
// 75 - 4.6875rem;
// 82 - 5.125rem;
// 150 - 9.375rem;
// 150 - 9.375rem;

// 855.4 =

// 监控录屏
// http://59.211.94.140:18080/images/vlcrecord20211027.mp4

// 纵坐标 #BEE4FF
// 横坐标 rgba(255, 255, 255, 0.65)
