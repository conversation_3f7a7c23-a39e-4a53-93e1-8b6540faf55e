<template>
  <div class="warp">
    <div class="charts">
      <div class="chart" ref="piechart"></div>
      <div class="rorate"></div>
    </div>
    <div class="middle">
      <div class="middle_key">{{ title1 }}</div>
      <div class="middle_key">{{ title2 }}</div>
    </div>
    <div class="legend">
      <div
        class="item"
        v-for="(item, index) in resData"
        :key="index"
        @mouseenter="highlight(index)"
        @mouseleave="downplay(index)"
        @click="select({ item, index })"
        :class="{ active: activeIndex.includes(index) }"
      >
        <div class="item-left">
          <div class="dot" :style="{ borderColor: item.color }">
            <div class="inner-dot" :style="{ background: item.color }" />
          </div>
          <div class="info">
            <div class="label">{{ item.label }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      charts: null,
      //chartColor: this.color,
      activeIndex: []
    }
  },
  props: {
    data: {
      type: Array,
      default: () => {
        return [
          ['product', '法律咨询'],
          ['非常满意', 1234],
          ['满意', 234],
          ['基本满意', 1001],
          ['不满意', 1234],
          ['非常不满意', 345]
        ]
      }
    },
    title1: {
      type: String,
      default: '类型'
    },
    title2: {
      type: String,
      default: '分析'
    },
    // icon: {
    //   type: [Object, String],
    //   default: () => require('@/assets/img/cqyz/wzrh/block5-icon.png')
    // },
    color: {
      type: Array,
      default: () => ['#2EF6FF', '#FFBA4F', '#5AE29D', '#6D5AE2', '#E6F973']
    }
  },
  computed: {
    resData() {
      const res = this.data
        .filter((it, index) => index > 0)
        .map((item, index) => ({
          label: item[0],
          value: parseFloat(item[1]),
          color: this.color[index]
        }))
      const total = res.reduce((pre, item) => (pre += item.value), 0)
      res.forEach(item => {
        item.rate = Math.round((item.value / total) * 100) + '%'
      })
      return res
    }
  },
  watch: {
    data: {
      deep: true,
      immediate: true,
      handler(val) {
        this.$nextTick(() => {
          this.initchart(val)
        })
      }
    }
  },
  methods: {
    highlight(index) {
      this.charts.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: index
      })
    },
    downplay(index) {
      this.charts.dispatchAction({
        type: 'downplay',
        seriesIndex: 0,
        dataIndex: index
      })
    },
    select({ item, index }) {
      if (this.activeIndex.includes(index)) {
        const i = this.activeIndex.findIndex(item => item === index)
        this.activeIndex.splice(i, 1)
      } else {
        this.activeIndex.push(index)
      }
      this.charts.dispatchAction({
        type: 'legendToggleSelect',
        // 图例名称
        name: item.label
      })
    },
    initchart(data) {
      if (this.charts) {
        this.charts.clear()
      }
      this.charts = this.$echarts.init(this.$refs.piechart)
      this.activeIndex = data.filter((it, i) => i > 0).map((it, i) => i)
      const options = {
        color: this.color,
        dataset: {
          source: data
        },
        legend: {
          show: false,
          data: data.filter((it, index) => index > 0).map(item => ({ name: item[0] }))
        },
        series: [
          {
            type: 'pie',
            radius: ['38px', '50px'],
            center: [100, '50%'],
            label: {
              show: true,
              position: 'outside',
              fontSize: 14,
              formatter: function(params) {
                return `${params.value[1]}%`
              },
              distanceToLabelLine: 0
            },
            labelLine: {
              show: false,
              length: 5,
              length2: 1
            },
            animationType: 'scale',
            animationEasing: 'elasticOut',
            animationDuration: 2000,
            animationDelay: function(idx) {
              return idx * 100
            }
          },
          {
            type: 'pie',
            radius: ['35px', '43px'],
            center: [100, '50%'],
            data: [{ name: '', value: 100 }],
            itemStyle: {
              color: 'rgba(0,0,0,0.25)'
            },
            ledend: {
              show: false
            },
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            silent: true
          }
        ]
      }
      this.charts.setOption(options)
    }
  }
}
</script>

<style lang="scss" scoped>
.warp {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  // padding: 0 32px;
  .charts {
    width: 200px !important;
    height: 164px !important;
    position: relative;
    // margin-right: 19px;
    z-index: 1;
    @keyframes roate {
      0% {
        transform: translate(-50%, -50%) rotate(0);
      }
      100% {
        transform: translate(-50%, -50%) rotate(360deg);
      }
    }
    .rorate {
      width: 130px;
      height: 130px;
      position: absolute;
      display: block;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);

      &::after {
        width: 100%;
        height: 100%;
        content: '';
        position: absolute;
        display: block;
        top: 0;
        left: 0;
        background: url(~@/assets/leader/img/zwfw/block5-bg.png) no-repeat center / 100% 100%;
        animation: rotate 5s infinite ease-in-out;
      }
    }
    .chart {
      width: 100%;
      height: 100%;
    }
  }
  .middle {
    position: absolute;
    z-index: 2;
    width: 88px;
    height: 88px;
    left: 50%;
    top: 35px;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .middle_num {
      font-size: 20px;
      font-family: DIN-BlackItalic, DIN;
      font-weight: normal;
      color: #ffffff;
      line-height: 24px;
    }
    .middle_key {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #ffffff;
      line-height: 20px;
    }
  }
  .legend {
    width: 180px;
    display: flex;
    flex-wrap: wrap;
    .item {
      margin: 0 auto;
      width: 90px;
      height: 22.8px;
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      justify-content: space-evenly;
      &:not(.active) {
        filter: grayscale(100%);
      }
      .item-left {
        display: flex;
        align-items: center;
        .dot {
          width: 16.8px;
          height: 16.8px;
          border-radius: 50%;
          border: 1px solid;
          position: relative;
          margin-right: 3px;
          .inner-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
        }
        .info {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .label {
            text-align: left;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 25px;
          }
        }
      }
      .item-right {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 80px;
        .value {
          text-align: right;
          width: 30px;
          font-size: 14px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #ffba4f;
          line-height: 20px;
          text-shadow: 0px 0px 1px #00132e;
        }

        .rate {
          text-align: right;
          font-size: 16px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #2ef6ff;
          line-height: 25px;
          text-shadow: 0px 0px 1px #00132e;
        }
      }
    }
  }
}
</style>
