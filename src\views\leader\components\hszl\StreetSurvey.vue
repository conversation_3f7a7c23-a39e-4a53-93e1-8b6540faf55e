<template>
  <div class="wrap">
    <div class="item" v-for="(it, i) of list" :key="i">
      <div class="icon" :style="{ background: `url(${it.icon}) no-repeat` }"></div>
      <div class="bg"></div>
      <div class="count">
        <countTo
          ref="countTo"
          :startVal="$countTo.startVal"
          :decimals="$countTo.decimals(it.count)"
          :endVal="it.count"
          :duration="$countTo.duration"
        />
        个
      </div>
      <div class="label">{{ it.label }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StreetSurvey',
  data() {
    return {
      list: [
        {
          label: '网格数量',
          count: 131,
          icon: require('@/assets/hszl/icon15.png')
        },
        {
          label: '商企数量',
          count: 650,
          icon: require('@/assets/hszl/icon16.png')
        },
        {
          label: '工地数量',
          count: 7,
          icon: require('@/assets/hszl/icon17.png')
        },
        {
          label: '学校数量',
          count: 8,
          icon: require('@/assets/hszl/icon18.png')
        },
        {
          label: '在校生数量',
          count: 6362,
          icon: require('@/assets/hszl/icon19.png')
        }
      ]
    }
  }
}
</script>

<style lang="less" scoped>
.wrap {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  .item {
    position: relative;
    width: 80px;
    padding-top: 27px;
    .icon {
      width: 32px;
      height: 31px;
      position: absolute;
      left: 24px;
      top: 17px;
      animation: move infinite 3s ease-in-out;
      @keyframes move {
        0% {
          transform: rotateY(0);
        }
        50% {
          transform: rotateY(180deg);
        }
        100% {
          transform: rotateY(360deg);
        }
      }
    }
    .bg {
      width: 80px;
      height: 50px;
      background: url('~@/assets/hszl/bg7.png');
      margin-bottom: 4px;
    }
    .count {
      font-size: 11px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #cbe5ff;
      line-height: 15px;
      margin-bottom: 2px;
      span {
        font-size: 18px;
        font-family: PingFangSC, PingFang SC;
        font-weight: normal;
        color: #ffffff;
        line-height: 22px;
      }
    }
    .label {
      height: 20px;
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #ffffff;
      line-height: 20px;
    }
  }
}
</style>
