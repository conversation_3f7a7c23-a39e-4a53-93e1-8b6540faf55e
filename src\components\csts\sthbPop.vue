<template>
  <div class="ai_waring">
    <div class="title">
      <span>生态环保更多</span>
    </div>
    <img @click="closeEmit" class="close_btn" src="@/assets/shzl/map/close_btn2.png" alt="" />
    <div class="content">
      <div class="content1">
        <BlockBox3
          title="空气质量总览"
          class="box box1"
          :isListBtns="false"
          :blockHeight="379"
        >
          <div class="box1content1">
            <div class="contentTitle"><span></span>总览</div>
            <div class="contentBox">
              <div class="contentItem1">
                <img class="img1" src="@/assets/csts/zlImg1.png" alt="">
                <div class="zl1">
                  <img class="img2" src="@/assets/csts/zlImg2.png" alt="">
                  <div class="name">轻度污染</div>
                  <div class="num">101</div>
                </div>
                <div class="zl2">
                  <img class="img3" src="@/assets/csts/zlImg3.png" alt="">
                  <div class="name">省内排名</div>
                  <div class="num">3</div>
                </div>
              </div>
              <div class="contentItem2">
                <div class="contentList">
                  <div class="num">58 <span>ug/m³</span></div>
                  <img src="@/assets/csts/zlBg2.png" alt="">
                  <div class="name">PM2.5浓度</div>
                </div>
                <div class="contentList">
                  <div class="num">98 <span>ug/m³</span></div>
                  <img src="@/assets/csts/zlBg2.png" alt="">
                  <div class="name">PM10浓度</div>
                </div>
                <div class="contentList">
                  <div class="num">98 <span>ug/m³</span></div>
                  <img src="@/assets/csts/zlBg2.png" alt="">
                  <div class="name">O₃浓度</div>
                </div>
              </div>
              <div class="contentItem3">
                <div class="contentList">
                  <div class="num">58 <span>ug/m³</span></div>
                  <img src="@/assets/csts/zlBg2.png" alt="">
                  <div class="name">SO₃浓度</div>
                </div>
                <div class="contentList">
                  <div class="num">98 <span>ug/m³</span></div>
                  <img src="@/assets/csts/zlBg2.png" alt="">
                  <div class="name">NO2浓度</div>
                </div>
                <div class="contentList">
                  <div class="num">98 <span>ug/m³</span></div>
                  <img src="@/assets/csts/zlBg2.png" alt="">
                  <div class="name">CO浓度</div>
                </div>
              </div>
            </div>
          </div>
          <div class="box1content2">
            <div class="contentTitle"><span></span>趋势</div>
            <div class="contentBox">
              <TrendLineChart
                :options="xczfData.options"
                :data="xczfData.data"
                :init-option="{
                  yAxis: {
                    name: '',
                  },
                }"
              />
            </div>
          </div>
          <div class="box1content3">
            <div class="contentTitle"><span></span>优质天数</div>
            <div class="contentBox">
              <BarLineChart :data="sczzData1" :options="sczzOptions1"></BarLineChart>
            </div>
          </div>
        </BlockBox3>
      </div>
      <div class="content2">
        <div class="content2Left">
          <BlockBox
            title="河流污染类型分析"
            class="box box2"
            :isListBtns="false"
            :blockHeight="357"
          >
            <div class="box2content">
              <PieChart3D type="2" :data="data10_2.data" :options="data10_2.options" />
            </div>
          </BlockBox>
        </div>
        <div class="content2Right">
          <BlockBox4
            title="环保事件分析"
            class="box box3"
            :isListBtns="false"
            :blockHeight="357"
          >
            <div class="box3content1">
              <div class="contentTitle"><span></span>态势</div>
              <div class="contentBox">
                <div class="contentItem">
                  <div class="contentItem1">
                    <div class="num">32,972</div>
                    <div class="name">累计受理 (件)</div>
                  </div>
                  <div class="contentItem2">
                    <div class="charts" ref="cnspChart1"></div>
                    <div class="name">受理率</div>
                  </div>
                </div>
                <div class="contentItem">
                  <div class="contentItem1">
                    <div class="num">12,368</div>
                    <div class="name">累计办结 (件)</div>
                  </div>
                  <div class="contentItem2">
                    <div class="charts" ref="cnspChart2"></div>
                    <div class="name">办结率</div>
                  </div>
                </div>
                <div class="contentItem">
                  <div class="contentItem1">
                    <div class="num">8,975</div>
                    <div class="name">未办结 (件)</div>
                  </div>
                  <div class="contentItem2">
                    <div class="charts" ref="cnspChart3"></div>
                    <div class="name">按时办结率</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="box3content2">
              <div class="contentTitle"><span></span>事件</div>
              <div class="contentBox">
                <PieChart3D type="1" :data="data8.data" :options="data8.options" />
              </div>
            </div>
          </BlockBox4>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import BlockBox from '@/components/leader/common/blockBox2.vue'
import BlockBox3 from '@/components/leader/common/blockBox3.vue'
import BlockBox4 from '@/components/leader/common/blockBox4.vue'
import echarts from 'echarts'
export default {
  name:'sthbPop',
  data() {
    return {
      xczfData: {
        data: [
          ['product', ''],
          ['1月', 20],
          ['2月', 30],
          ['3月', 20],
          ['4月', 60],
          ['5月', 40],
          ['6月', 50],
          ['7月', 70],
        ],
        options: {
          smooth: true,
          colors: ['rgba(0, 155, 255, 1)'],
          isToolTipInterval: true,
          toolTipIntervalTime: 5000,
        },
      },
      sczzData1: [
        {
          name: '优质天数数量',
          data: [
            {
              name: '7月',
              value: 163,
            },
            {
              name: '8月',
              value: 130,
            },
            {
              name: '9月',
              value: 125,
            },
            {
              name: '10月',
              value: 146,
            },
            {
              name: '11月',
              value: 130,
            },
            {
              name: '12月',
              value: 152,
            },
          ],
          type: 'bar',
          yAxisIndex: 0,
          color: ['#FAE699', '#F3DB60'],
        },
        {
          name: '优质天数占比',
          data: [
            {
              name: '7月',
              value: 83,
            },
            {
              name: '8月',
              value: 99,
            },
            {
              name: '9月',
              value: 81,
            },
            {
              name: '10月',
              value: 94,
            },
            {
              name: '11月',
              value: 92,
            },
            {
              name: '12月',
              value: 97,
            },
          ],
          type: 'line',
          yAxisIndex: 1,
        },
      ],
      sczzOptions1: {
        barWidth: 10,
        barColor: ['#FF9201', '#EDB000'],
        areaColor: '#fff',
        lineColor: '#0077FF',
        leftYaxisName: '总量：亿元',
        rightYaxisName: '增速(%)',
        tooltip: {
          show: true,
          type: 'shadow',
        },
        yAxis: {
          min: 0,
          splitNumber: 5,
          unit: '',
        },
        legend: {
          show: false,
        },
      },
      data8: {
        data: [
          ['product', '年总数'],
          ['企业排污', 2456],
          ['河流污染', 1200],
          ['河道采砂', 2000],
          ['其他', 1800],
        ],
        options: {
          colors: ['#2EF6FF', '#E6F973', '#6D5AE2', '#5AE29D', '#FFBA4F', '#E05165'],
          alpha: 65,
          pieSize: 220,
          pieInnerSize: 160,
          position: ['50%', '35%'],
          bgImg: {
            top: '40%',
            left: '50%'
          },
          unit: '',
          title: {
            fontSize: '16px',
            top: 70,
          },
          subtitle: {
            fontSize: '14px',
            top: 90,
          },
        }
      },
      data10_2: {
        data: [
          ['product', '事件总数'],
          ['氨氮', 1245],
          ['PH值', 1245],
          ['COD', 1245],
          ['其他', 1245]
        ],
        options: {
          colors: ['#80CF83', '#CDCF72', '#0280DF', '#13A4C0'],
          legend: {
            top: 15
          },
          bgImg: {
            width: '70%',
            height: '70%',
            top: '51%',
            left: '50%'
          },
          unit: '件',
          title: {
            fontSize: '16px',
            top: 80,
          },
          subtitle: {
            fontSize: '14px',
            top: 100,
          },
        }
      },
    }
  },
  mounted() {
    this.cnspChartMethod(this.$refs.cnspChart1, 72)
    this.cnspChartMethod(this.$refs.cnspChart2, 73)
    this.cnspChartMethod(this.$refs.cnspChart3, 74)
  },
  components: {
    BlockBox,
    BlockBox3,
    BlockBox4,
  },
  methods: {
    closeEmit() {
      this.$emit('closeEmit')
    },
    cnspChartMethod(id, data) {
      let option = {
        angleAxis: {
          show: false,
          max: (100 * 360) / 180, //-45度到225度，二者偏移值是270度除360度
          type: 'value',
          startAngle: 180, //极坐标初始角度
          splitLine: {
            show: false,
          },
        },
        barMaxWidth: 10, //圆环宽度
        radiusAxis: {
          show: false,
          type: 'category',
        },
        //圆环位置和大小
        polar: {
          center: ['50%', '90%'],
          radius: '320%',
        },
        series: [
          {
            type: 'bar',
            stack: '测试',
            data: [
              {
                //上层圆环，显示数据
                value: data,
                name: 'sdfasdf',
                itemStyle: {
                  color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                    {
                      offset: 0,
                      color: '#A6E0FF',
                    },
                    {
                      offset: 1,
                      color: '#6CBDFF',
                    },
                  ]),
                },
              },
            ],
            barGap: '-100%', //柱间距离,上下两层圆环重合
            coordinateSystem: 'polar',
            roundCap: true, //顶端圆角从 v4.5.0 开始支持
            z: 2,
          },
          {
            //下层圆环，显示最大值
            type: 'bar',
            data: [
              {
                value: 100,
                itemStyle: {
                  color: {
                    //图形渐变颜色方法，四个数字分别代表，右，下，左，上，offset表示0%到100%
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1, //从左到右 0-1
                    y2: 0,
                    colorStops: [
                      {
                        offset: 0,
                        color: 'rgba(255,255,255,0.2)',
                      },
                      {
                        offset: 1,
                        color: '#979797',
                      },
                    ],
                  },
                },
              },
            ],
            barGap: '-100%',
            coordinateSystem: 'polar',
            roundCap: true,
            z: 1,
          },
          {
            stack: '测试',
            type: 'bar',
            data: [0.01],
            showBackground: false,
            coordinateSystem: 'polar',
            roundCap: true,
            barWidth: 10,
            itemStyle: {
              color: 'rgba(255, 255, 255, 1)',
              borderColor: 'rgba(255, 255, 255, 1)',
              borderWidth: 6,
              shadowColor: 'rgba(255, 255, 255, 1)',
              shadowBlur: 15,
              shadowOffsetY: 2,
            },
          },
          {
            name: '外部刻度',
            type: 'gauge',
            //  center: ['20%', '50%'],
            radius: '70%',
            min: 0, //最小刻度
            max: 100, //最大刻度
            splitNumber: 4, //刻度数量
            startAngle: 225,
            endAngle: -45,

            axisLine: {
              show: false,
              // 仪表盘刻度线
              lineStyle: {
                width: 1,
                color: [[1, '#FFFFFF']],
              },
            },
            //仪表盘文字
            axisLabel: {
              show: false,
            }, //刻度标签。
            axisTick: {
              show: false,
            }, //刻度样式
            splitLine: {
              show: false,
            }, //分隔线样式
            detail: {
              show: false,
            },
            pointer: {
              show: false,
            },
            title: {
              show: true,
              offsetCenter: ['5%', '80%'],
              textStyle: {
                fontSize: 20,
                color: '#fff',
                fontWeight: 'bold',
              },
            },
            data: [
              {
                name: data + '%',
              },
            ],
          },
        ],
      }
      let myChart = this.$echarts.init(id, 'ucdc')
      myChart.setOption(option)
      window.addEventListener('resize', function () {
        myChart.resize()
      })
    },
  },
}
</script>

<style lang='less' scoped>
@keyframes rotateS {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes rotateN {
  0% {
    transform: rotate(360deg);
  }
  100% {
    transform: rotate(0);
  }
}
@keyframes rotateY {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotateY(360deg);
  }
}
.ai_waring {
  width: 1885px;
  height: 937px;
  padding: 22px;
  // background: rgba(0,23,59,0.95);
  background: url(~@/assets/csts/xztk.png) no-repeat;
  background-size: 100% 100%;
  box-shadow: 0px 0px 22px 0px rgba(11,54,138,0.35), 0px 2px 8px 0px rgba(0,0,0,0.7), inset 0px 0px 8px 0px rgba(17,146,214,0.35);
  border-radius: 11px;
  border: 1px solid;
  border-image: linear-gradient(360deg, rgba(57, 133, 255, 0.5), rgba(0, 205, 255, 0)) 1 1;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
  .title {
    background: url(~@/assets/shzl/map/title_bg2.png) no-repeat center / 100% 100%;
    background-size: 100% 100%;
    width: 914px;
    height: 74px;
    margin: 0 auto;
    line-height: 74px;
    text-align: center;
    position: relative;
    span {
      font-size: 40px;
      font-family: YouSheBiaoTiHei;
      color: #FFFFFF;
      line-height: 52px;
      background: linear-gradient(180deg, #FFFFFF 0%, #2AEBFF 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      display: inline-block;
      margin-top: 11px;
    }
  }
  .close_btn {
    position: absolute;
    top: 38px;
    right: 43px;
  }
  .content {
    width: 1841px;
    height: 765px;
    .contentTitle {
      font-size: 18px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #ffffff;
      line-height: 25px;
      text-align: left;
      margin-top: 15px;
      span {
        float: left;
        width: 6px;
        height: 20px;
        background: #02B2CD;
        border-radius: 2px;
        margin-top: 3px;
        margin-left: 10px;
        margin-right: 8px;
      }
    }
    .content1 {
      width: 1736px;
      height: 379px;
      margin-left: 67px;
      margin-top: 43px;
      .box1 {
        .box1content1 {
          width: 533px;
          height: 329px;
          float: left;
          .contentBox {
            width: 533px;
            height: 289px;
            .contentItem1 {
              width: 382px;
              height: 72px;
              background: url(~@/assets/csts/zlBg.png) no-repeat;
              background-size: 100% 100%;
              margin: 17px 0 0 35px;
              .img1 {
                float: left;
                width: 52px;
                height: 41px;
                margin: 16px 0 0 30px;
              }
              .zl1 {
                float: left;
                height: 26px;
                margin-top: 25px;
                margin-left: 26px;
                .img2 {
                  float: left;
                  margin-top: 7px;
                }
                .name {
                  float: left;
                  font-size: 14px;
                  font-family: PingFangSC-Regular, PingFang SC;
                  font-weight: 400;
                  color: #ffffff;
                  line-height: 20px;
                  margin-top: 3px;
                  margin-left: 8px;
                }
                .num {
                  float: left;
                  font-size: 20px;
                  font-family: DINPro-Regular, DINPro;
                  font-weight: 400;
                  color: #23B8FF;
                  line-height: 26px;
                  margin-left: 13px;
                }
              }
              .zl2 {
                float: left;
                height: 26px;
                margin-top: 25px;
                margin-left: 19px;
                .img3 {
                  float: left;
                  margin-top: 7px;
                }
                .name {
                  float: left;
                  font-size: 14px;
                  font-family: PingFangSC-Regular, PingFang SC;
                  font-weight: 400;
                  color: #ffffff;
                  line-height: 20px;
                  margin-top: 3px;
                  margin-left: 8px;
                }
                .num {
                  float: left;
                  font-size: 20px;
                  font-family: DINPro-Regular, DINPro;
                  font-weight: 400;
                  color: #FFB500;
                  line-height: 26px;
                  margin-left: 13px;
                }
              }
            }
            .contentItem2 {
              height: 53px;
              margin-top: 23px;
              padding-left: 28px;
              .contentList {
                width: 116px;
                float: left;
                margin: 0 8px;
                text-align: left;
                .num {
                  font-size: 22px;
                  font-family: DINAlternate-Bold, DINAlternate;
                  font-weight: bold;
                  color: #FFFFFF;
                  line-height: 26px;
                  text-shadow: 0px 0px 4px rgba(0,0,0,0.5);
                  span {
                    font-size: 14px;
                  }
                }
                img {
                  margin-top: 3px;
                }
                .name {
                  font-size: 14px;
                  font-family: PingFangSC-Regular, PingFang SC;
                  font-weight: 400;
                  color: #ffffff;
                  line-height: 20px;
                  margin-top: -1px;
                }
                &:nth-child(1) {
                  .num {
                    background: linear-gradient(180deg, #FFFFFF 0%, #00FFFA 67%, #04A1FF 100%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                  }
                }
                &:nth-child(2) {
                  .num {
                    background: linear-gradient(180deg, #FFFFFF 0%, #FFCB00 67%, #FF8704 100%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                  }
                }
                &:nth-child(3) {
                  .num {
                    background: linear-gradient(180deg, #FFFFFF 0%, #00FF00 67%, #04FAFF 100%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                  }
                }
              }
            }
            .contentItem3 {
              height: 53px;
              margin-top: 31px;
              padding-left: 28px;
              .contentList {
                width: 116px;
                float: left;
                margin: 0 8px;
                text-align: left;
                .num {
                  font-size: 22px;
                  font-family: DINAlternate-Bold, DINAlternate;
                  font-weight: bold;
                  color: #FFFFFF;
                  line-height: 26px;
                  text-shadow: 0px 0px 4px rgba(0,0,0,0.5);
                  background: linear-gradient(180deg, #FFFFFF 0%, #00FFFA 67%, #04A1FF 100%);
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                  span {
                    font-size: 14px;
                  }
                }
                img {
                  margin-top: 3px;
                }
                .name {
                  font-size: 14px;
                  font-family: PingFangSC-Regular, PingFang SC;
                  font-weight: 400;
                  color: #ffffff;
                  line-height: 20px;
                  margin-top: -1px;
                }
                &:nth-child(1) {
                  .num {
                    background: linear-gradient(180deg, #FFFFFF 0%, #00FFFA 67%, #04A1FF 100%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                  }
                }
                &:nth-child(2) {
                  .num {
                    background: linear-gradient(180deg, #FFFFFF 0%, #FFCB00 67%, #FF8704 100%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                  }
                }
                &:nth-child(3) {
                  .num {
                    background: linear-gradient(180deg, #FFFFFF 0%, #00FF00 67%, #04FAFF 100%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                  }
                }
              }
            }
          }
        }
        .box1content2 {
          width: 533px;
          height: 329px;
          float: left;
          margin-left: 67px;
          .contentBox {
            width: 533px;
            height: 289px;
          }
        }
        .box1content3 {
          width: 533px;
          height: 329px;
          float: left;
          margin-left: 67px;
          .contentBox {
            width: 533px;
            height: 289px;
          }
        }
      }
    }
    .content2 {
      width: 1736px;
      height: 357px;
      margin-left: 67px;
      .content2Left {
        width: 533px;
        height: 357px;
        float: left;
        .box2content {
          width: 533px;
          height: 245px;
          margin-top: 42px;
        }
      }
      .content2Right {
        width: 1137px;
        height: 357px;
        float: left;
        margin-left: 66px;
        .box3content1 {
          float: left;
          width: 533px;
          height: 307px;
          .contentBox {
            width: 533px;
            height: 267px;
            padding-left: 39px;
            .contentItem {
              width: 140px;
              margin: 0 6px;
              float: left;
              .contentItem1 {
                width: 136px;
                height: 83px;
                background: url(~@/assets/csts/tsBg1.png) no-repeat;
                background-size: 100% 100%;
                margin: 8px 2px 13px 2px;
                padding-top: 15px;
                .num {
                  font-size: 20px;
                  font-family: DIN-BlackItalic, DIN;
                  font-weight: normal;
                  color: #FFFFFF;
                  line-height: 17px;
                }
                .name {
                  font-size: 14px;
                  font-family: PingFangSC-Regular, PingFang SC;
                  font-weight: 400;
                  color: #ffffff;
                  line-height: 20px;
                  margin-top: 8px;
                }
              }
              .contentItem2 {
                width: 140px;
                height: 164px;
                background: url(~@/assets/csts/tsBg2.png) no-repeat;
                background-size: 100% 100%;
                padding-top: 30px;
                .charts {
                  width: 130px;
                  height: 70px;
                  margin: 0 5px 10px 5px;
                }
                .name {
                  font-size: 14px;
                  font-family: PingFangSC-Regular, PingFang SC;
                  font-weight: 400;
                  color: #ffffff;
                  line-height: 20px;
                }
              }
            }
          }
        }
        .box3content2 {
          float: left;
          width: 533px;
          height: 307px;
          .contentBox {
            width: 533px;
            height: 267px;
          }
        }
      }
    }
  }
}
</style>