<template>
  <div class="amachTypeSwiper">
    <div class="swiper-content">
      <swiper class="swiper" :options="swiperOption" ref="amachTypeSwiper">
        <swiper-slide v-for="(item, index) in amachTypeList" :key="index">
          <div
            :class="['swiper-item', agmachTypeCodeActive == item.agmachTypeCode ? 'active' : '']"
            @click="agmachTypeClick(item)"
          >
            {{ item.agmachTypeName }}
          </div>
        </swiper-slide>
      </swiper>
      <div class="swiper-button-prev-custom" slot="button-prev"></div>
      <div class="swiper-button-next-custom" slot="button-next"></div>
    </div>
  </div>
</template>
<script>
import 'swiper/css/swiper.css'
export default {
  name: '',
  props: {
    agmachTypeCode: {
      type: String,
      default: '',
    },
    amachTypeList: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  data() {
    return {
      agmachTypeCodeActive: this.agmachTypeCode,
      swiperOption: {
        slidesPerView: 5,
        navigation: {
          prevEl: '.swiper-button-prev-custom',
          nextEl: '.swiper-button-next-custom',
        },
        // spaceBetween:10
      },
      //   amachTypeList: [
      //     {
      //       agmachTypeName: '谷物联合收割机',
      //       agmachTypeCode: '150105',
      //     },
      //     {
      //       agmachTypeName: '轮式拖拉机',
      //       agmachTypeCode: '510101',
      //     },
      //     {
      //       agmachTypeName: '玉米收获机',
      //       agmachTypeCode: '150106',
      //     },
      //     {
      //       agmachTypeName: '其他',
      //       agmachTypeCode: '000000',
      //     },
      //     {
      //       agmachTypeName: '花生收获机',
      //       agmachTypeCode: '150302',
      //     },
      //     {
      //       agmachTypeName: '油菜籽收获机',
      //       agmachTypeCode: '150303',
      //     },
      //   ],
    }
  },
  mounted() {},
  methods: {
    agmachTypeClick(e) {
      this.agmachTypeCodeActive = e.agmachTypeCode
      this.$emit('updateChange', this.agmachTypeCodeActive)
    },
  },
  computed: {
    amachTypeSwiper() {
      return this.$refs.amachTypeSwiper.$swiper
    },
  },
}
</script>
<style scoped lang="scss">
.amachTypeSwiper {
  position: absolute;
  width: 510px;
  margin: 10px auto 0;
  left: -300px;
  right: 0;
  .swiper-content {
    width: 510px;
    color: #fff;
    position: relative;
    .swiper-item {
      background-image: url(~@/assets/zhdd/recent.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      // width:60px;
      display: inline-block;
      width: 90px;
      height: 24px;
      font-size: 12px;
      color: RGBA(151, 201, 216, 1);
      line-height: 24px;
      text-shadow: 0px 0px 8px #00ace6;
      text-align: center;
      font-style: normal;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
      &.active {
        background-image: url(~@/assets/zhdd/recent_active.png);
        color: #ffffff;
        text-shadow: 0px 0px 8px #00ace6;
        font-weight: normal;
      }
    }
    .swiper-button-prev-custom {
      width: 24px;
      height: 24px;
      background-image: url(~@/assets/zhdd/swiperLeft.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      position: absolute;
      top: 0px;
      left: -20px;
      cursor: pointer;
      z-index: 9999;
    }
    .swiper-button-next-custom {
      width: 24px;
      height: 24px;
      background-image: url(~@/assets/zhdd/swiperLeft.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      position: absolute;
      top: 0px;
      right: -20px;
      rotate: 180deg;
      cursor: pointer;
      z-index: 9999;
    }
  }
}
</style>