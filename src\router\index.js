import Vue from 'vue'
import VueRouter from 'vue-router'
import routes1 from './leader/router'

Vue.use(VueRouter)
const routes = routes1
console.log(routes)
const router = new VueRouter({
	mode: 'hash',
	routes,
	base: process.env.BASE_URL,
})

// 路由守卫
router.beforeEach((to, from, next) => {
	//   // 获取本地存储的权限
	//   const user = sessionStorage.getItem('user');
	//   // 登录页直接跳转
	//   if (to.path == '/login') {
	//     next();
	//   } else if (user && user == 'jyadmin') {
	//     // 除了login都要判断是否通过验证
	//     const loginList = ['/', '/jyzt_jjjs', '/zl1_v4', '/zhdn', '/dj', '/mix'];
	//     if (loginList.indexOf(to.path) != -1) {
	//       console.log('aa');
	//       next();
	//     } else {
	//       // next(from.path);
	//     }
	//   } else if (user && user == 'hxhxhx') {
	//     next();
	//   } else {
	//     next('/login');
	//   }
	/* 路由发生变化修改页面title */
	if (to.meta.title) {
		// document.title = to.meta.title;
		document.title = '全国农机作业指挥调度平台'
	}
	next()
})
export default router
