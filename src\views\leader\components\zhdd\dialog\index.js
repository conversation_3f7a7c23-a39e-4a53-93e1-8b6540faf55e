/*
 * @Author: fanjialong 
 * @Date: 2023-07-21 09:34:10
 * @LastEditors: fanjialong 
 * @LastEditTime: 2023-08-21 10:17:13
 * @FilePath: /hs_dp/src/views/leader/components/zhdd/dialog/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import EventInfo from './EventInfo.vue'
import EventDetail from './EventDetail.vue'
import EventDetailQgd from './EventDetailQgd.vue'
import EventStatistic from './EventStatistic.vue'
import EventStatisticQgd from './EventStatisticQgd.vue'
import EmergencyResourcesDialog from './EmergencyResources.vue'
import OfficeronDuty from './OfficeronDuty.vue'
import GridMemberLoginStatusDialog from './GridMemberLoginStatus.vue'
import TaskDistribution from './TaskDistribution.vue'
import AIEventAnalysisDialog from './AIEventAnalysisDialog.vue'
import Drone from './Drone.vue'
import DroneVideo from './DroneVideo.vue'
import RealTimeEventDialog from './RealTimeEventDialog.vue'
import RealTimeEventDialogQgd from './RealTimeEventDialogQgd.vue'
import PersonInfo from './PersonInfo.vue'
import BasicInformation from './BasicInformation.vue'
import AlarmDetails from './AlarmDetails.vue'
import VideoSingle from './VideoSingle.vue'

export {
  EventInfo,
  EventDetail,
  EventDetailQgd,
  EventStatistic,
  EventStatisticQgd,
  EmergencyResourcesDialog,
  OfficeronDuty,
  GridMemberLoginStatusDialog,
  TaskDistribution,
  AIEventAnalysisDialog,
  Drone,
  DroneVideo,
  RealTimeEventDialog,
  RealTimeEventDialogQgd,
  PersonInfo,
  BasicInformation,
  AlarmDetails,
  VideoSingle
}
