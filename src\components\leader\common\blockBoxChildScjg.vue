<template>
	<div class="child-block" :style="{ height: blockHeight + 'px' }">
		<slot></slot>

		<div class="content-box">
			<div class="header-item-box" v-for="(item, index) in line1Data" :key="index">
				<img :src="item.iconUrl" alt="" />
				<!-- <div class="icon-box"></div> -->
				<div class="content-number-box">
					<div class="number">{{ item.number }}</div>
					<div class="label">{{ item.label }}</div>
				</div>
			</div>
		</div>

		<div class="content-box">
			<div class="header-item-box" v-for="(item, index) in line2Data" :key="index">
				<img :src="item.iconUrl" alt="" />
				<!-- <div class="icon-box"></div> -->
				<div class="content-number-box">
					<div class="number">{{ item.number }}</div>
					<div class="label">{{ item.label }}</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		data: {
			type: Array,
			default: () => [],
		},
		blockHeight: {
			type: Number,
			default: 206,
		},
	},
	data() {
		return {
			lin1Data: [],
			line2Data: [],
		}
	},
	computed: {},
	methods: {},

	watch: {
		data: {
			immediate: true,
			handler: function handler(newVal, oldVal) {
				if (newVal.length > 2) {
					this.line1Data = newVal.slice(0, 1)
					this.line2Data = newVal.slice(1)
				}
			},
		},
	},
}
</script>

<style lang="less" scoped>
.child-block {
	width: 455px;
	box-sizing: border-box;
	z-index: 101;
	position: relative;

	overflow: auto;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: center;
	padding-top: 9px;

	&::-webkit-scrollbar {
		width: 4px; /* 滚动条宽度 */
	}

	//轨道
	&::-webkit-scrollbar-track {
		background: #04244e; /* 滚动条轨道背景 */
		border-radius: 10px;
		padding: 5px 0;
	}

	//滑块
	&::-webkit-scrollbar-thumb {
		background: #82aed0; /* 滑块背景颜色 */
		border-radius: 10px;
		height: 15px;
	}

	//悬停滑块颜色
	&::-webkit-scrollbar-thumb:hover {
		background: #ff823b; /* 鼠标悬停时滑块颜色 */
	}

	//箭头
	&::-webkit-scrollbar-button {
		display: none; /* 隐藏滚动条的箭头按钮 */
	}

	.content-box {
		position: relative;
		width: 100%;
		height: 63px;
		// height: calc(100% - 74px);
		display: flex;
		justify-content: center;
		align-items: flex-start;
		margin-bottom: 6px;

		.header-item-box {
			width: 156px;
			height: 100%;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			margin: 0 29px;
			img {
				width: 76px;
				height: 100%;
				margin-right: 10px;
			}
			.icon-box {
				width: 76px;
				height: 100%;
				margin-right: 10px;
				background: skyblue;
			}
			.content-number-box {
				width: calc(100% - 76px - 10px);
				height: 100%;
				display: flex;
				flex-direction: column;
				justify-content: flex-end;
				align-items: flex-start;

				.number {
					font-family: DINPro, DINPro;
					font-weight: bold;
					font-size: 22px;
					color: #4de4ff;
					line-height: 22px;
					text-align: left;
					font-style: normal;
					text-transform: none;
					margin-bottom: 5px;
				}

				.label {
					width: 100%;

					font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
					font-weight: normal;
					font-size: 14px;
					color: #d0deee;
					line-height: 16px;
					text-align: left;
					font-style: normal;
					text-transform: none;
					overflow: hidden; /* 隐藏溢出文本 */
					display: -webkit-box; /* 使用弹性盒子模型 */
					-webkit-line-clamp: 2; /* 限制为两行 */
					-webkit-box-orient: vertical; /* 设置文本的排列方向 */
					text-overflow: ellipsis; /* 在文本溢出时显示省略号 */
				}
			}
		}
	}
}

.popDropDown {
	::v-deep .el-dropdown {
		color: #caecff;
		font-family: YouSheBiaoTiHei;
		font-size: 16px;
		line-height: 21px;
		text-align: right;
		font-style: normal;
		line-height: 40px;
		right: 0;
		position: absolute;
	}
	::v-deep .el-dropdown-menu {
		background-color: #00173b !important;
		border: 1px solid #2b7bbb !important;
	}
	.el-dropdown-menu__item {
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
		line-height: 40px;
		padding: 0 30px;
		&:hover {
			background: #185493;
			color: rgba(255, 255, 255, 0.8);
		}
	}
}
</style>
