/*
 * @Author: fanjialong <EMAIL>
 * @Date: 2023-04-17 15:06:09
 * @LastEditors: fanjialong 
 * @LastEditTime: 2023-09-13 10:38:10
 * @FilePath: \hs_dp\src\api\hs\hs.api.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import http from '@/utils/leader/request'
import {
  xtyyUrl
} from '@/utils/leader/const'

// 获取社区数据
export const getHsZlSq = community => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryByCommunity?community=` + community)
}
// 获取简介文字
export const getHsjj = id => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryByModule?module=` + '湖熟简介')
}
// 获取简介视频
export const getHsjjVideo = id => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryScenic?module=` + '湖熟简介视频')
}

// 获取经济概览
export const getJjgl = id => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryByModule?module=` + '经济概览')
}
// 获取空气质量
export const getKqzl = id => {
  return http.get(xtyyUrl + `/api/tStrdwgStationAqData/queryData`)
}
// 获取党建信息
export const getDjxx = id => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryByModule?module=` + '党建信息')
}
// 获取人口概览
export const getRkgl = id => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryByModule?module=` + '人口概览')
}
export const getRkglAge = id => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryByModule?module=` + '人口概览-年龄')
}
// 获取全要素网格事件
export const getQyswgsj = id => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryByModule?module=` + '全要素网格事件')
}

// 湖熟文化-街道概况
export const getJdgk = id => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryByModule?module=` + '街道概况')
}
// 湖熟文化-市场主体总数
export const getScztTotal = id => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryByModule?module=` + '市场主体')
}
// 湖熟文化-人口分布
export const getRkfb = id => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryByModule?module=` + '人口分布')
}
// 湖熟文化-航拍
export const getHsHp = id => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryScenic?module=` + '魅力湖熟')
}

// 经济发展-生产总值
export const getSczz = id => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryByModule?module=` + '生产总值')
}
// 经济发展-一般公共预算收入
export const getYbggyssr = id => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryByModule?module=` + '一般公共预算收入')
}
// 经济发展-三次产业占比
export const getSccyzb = id => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryByModule?module=` + '三次产业占比')
}
// 经济发展-重大项目
// export const getZdxm = id => {
//   return http.get(xtyyUrl + `/api/tDpConfig/queryByModule?module=` + '重大项目')
// }

export const getZdxm = id => {
  return http.get(xtyyUrl + `/api/tMajorProject/queryByPage`)
}

// 网格治理-一标三实
export const getYbss = id => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryByModule?module=` + '一标三实')
}
// 网格治理-网格建设情况
export const getWgjsqk = id => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryByModule?module=` + '网格建设情况')
}
// 网格治理-网格规范达标率重点指标
export const getWgGf = id => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryByModule?module=` + '网格规范达标率重点指标')
}
// 网格治理-网格事件分类统计
export const getWgSjFltj = id => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryByModule?module=` + '网格事件分类统计')
}

// 生态建设-土地使用
export const getTdsy = id => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryByModule?module=` + '土地使用')
}
// 生态建设-生态治理成果
export const getStjscg = id => {
  return http.get(xtyyUrl + `/api/tDpConfig/queryByModule?module=` + '生态治理成果')
}

// 总览弹窗 网格信息
export const zlWgInfo = data => {
  return http.get(xtyyUrl + `/api/tUserInfo/queryByGridName`,
    data)
}

// 总览弹窗 网格信息
export const zlHswh = data => {
  return http.get(xtyyUrl + `/api/tCulturalRelic/queryAll`,
    data)
}

// 总览弹窗 菊花展pdf
export const mlJhz = data => {
  return http.get(xtyyUrl + `/api/tbAppendix/getPreviewFileUrl?fileName=菊花展.pdf`,
    data)
}

//查询近七天日均空气质量
export const getAirWeek = data => {
  return http.get(xtyyUrl + `/api/tStrdwgStationAqData/queryByDate`,
    data)
}

//生态监测
export const getStjc = data => {
  return http.get(xtyyUrl + `/api/tStrdwgStationAqData/queryAQI`,
    data)
}

//市场主体
export const getSczt = data => {
  return http.get(xtyyUrl + `/api/tEnterpriseInfo/getScreenNum`,
    data)
}

//市场主体分页
export const getScztPage = (type, page, size) => {
  return http.get(xtyyUrl + `/api/tEnterpriseInfo/getScreenList?type=${type}&page=${page}&size=${size}`, )
}

// 生态治理成果
export const getStzlcg = () => {
  return http.get(xtyyUrl + `/api/tVillageInfo/queryCount`, )
}

// 生态治理成果
export const getStzlcgPage = (type, page, size) => {
  return http.get(xtyyUrl + `/api/tVillageInfo/queryByPage?type=${type}&page=${page}&size=${size}`, )
}
