<!--
 * @Author: wangcong
 * @Date: 2022-11-30 09:18:03
 * @LastEditTime: 2023-04-16 15:35:16
 * @LastEditors: fanjialong <EMAIL>
 * @Description: 
-->
<template>
  <div class="warp">
    <div class="charts" ref="piechart" />
    <!-- <div class="middle"><img :src="icon" class="icon" /></div> -->
    <div class="legend">
      <div
        class="item"
        v-for="(item, index) in resData"
        :key="index"
        @click="select({ item, index })"
        :class="{ active: activeIndex.includes(index) }"
      >
        <div class="dot" :style="{ borderColor: item.color }">
          <div class="inner-dot" :style="{ background: item.color }" />
        </div>
        <div class="info">
          <div class="label">{{ item.label }}</div>
          <div class="value">
            <countTo
              ref="countTo"
              :startVal="$countTo.startVal"
              :decimals="$countTo.decimals(item.value)"
              :endVal="item.value"
              :duration="$countTo.duration"
            />
          </div>
        </div>
        <div class="rate">{{ item.rate }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      charts: null,
      //chartColor: this.color,
      activeIndex: []
    }
  },
  props: {
    data: {
      type: Array,
      default: () => [
        ['product', '人口信息'],
        ['AI视频分析', 1234],
        ['数据研判', 234],
        ['社会矫正人员', 1001],
        ['涉教人员', 1234],
        ['涉毒人员', 1234]
      ]
    },
    icon: {
      type: [Object, String],
      default: () => require('@/assets/img/dw_icon.png')
    },
    color: {
      type: Array,
      default: () => [
        '#2EF6FF',
        '#FFBA4F',
        '#02ABFF',
        '#6D5AE2',
        'rgba(230, 249, 115, 1)',
        'rgb(2, 192, 149)',
        'rgb(128, 207, 131)'
      ]
    }
  },
  computed: {
    resData() {
      const res = this.data
        .filter((it, index) => index > 0)
        .map((item, index) => ({
          label: item[0],
          value: parseFloat(item[1]),
          color: this.color[index]
        }))
      const total = res.reduce((pre, item) => (pre += item.value), 0)
      res.forEach(item => {
        item.rate = Math.round((item.value / total) * 100) + '%'
      })
      return res
    }
  },
  watch: {
    data: {
      deep: true,
      immediate: true,
      handler(val) {
        this.$nextTick(() => {
          this.initchart(val)
        })
      }
    }
  },
  methods: {
    highlight(index) {
      console.log(index)
      this.charts.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: index
      })
    },
    downplay(index) {
      this.charts.dispatchAction({
        type: 'downplay',
        seriesIndex: 0,
        dataIndex: index
      })
    },
    select({ item, index }) {
      if (this.activeIndex.includes(index)) {
        const i = this.activeIndex.findIndex(item => item === index)
        this.activeIndex.splice(i, 1)
      } else {
        this.activeIndex.push(index)
      }
      this.charts.dispatchAction({
        type: 'legendToggleSelect',
        // 图例名称
        name: item.label
      })
    },
    initchart(data) {
      if (this.charts) {
        this.charts.clear()
      }
      this.charts = this.$echarts.init(this.$refs.piechart)
      this.activeIndex = data.filter((it, i) => i > 0).map((it, i) => i)
      const options = {
        color: this.color,
        dataset: {
          source: data
        },
        legend: {
          selectedMode: false,
          show: false,
          data: data.filter((it, index) => index > 0).map(item => ({ name: item[0] }))
        },
        series: [
          {
            legendHoverLink: false,
            type: 'pie',
            radius: ['27px', '50px'],
            center: [96, '50%'],
            label: {
              normal: {
                show: false,
                position: 'center',
                // formatter: '{value|{c}}\n{label|{b}}',
                // formatter: '{value|{c}}\n{label|{b}}',
                formatter: function(param) {
                  // console.log(99,param);
                  // console.log(99,param.value[1]);
                  return param.value[1] + '\n' + param.value[0]
                },
                // formatter: '{d}\n|{b}',
                rich: {
                  value: {
                    padding: 5,
                    align: 'center',
                    verticalAlign: 'middle',
                    fontSize: 12
                  },
                  label: {
                    align: 'center',
                    verticalAlign: 'middle',
                    fontSize: 12
                  }
                }
              },
              emphasis: {
                show: true,
                textStyle: {
                  fontSize: '14'
                }
              }
            },
            animationType: 'scale',
            animationEasing: 'elasticOut',
            animationDuration: 2000,
            animationDelay: function(idx) {
              return idx * 100
            }
          },
          {
            type: 'pie',
            legendHoverLink: false,
            radius: ['27px', '35px'],
            center: [96, '50%'],
            data: [{ name: '', value: 100 }],
            itemStyle: {
              color: 'rgba(0,0,0,0.25)'
            },
            ledend: {
              show: false
            },
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            silent: true
          }
        ]
      }
      this.charts.setOption(options)
      console.log(9999)
      let index = 0
      this.charts.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: 0
      }) //默认高亮
      this.charts.on('mouseover', e => {
        if (e.dataIndex !== index) {
          this.charts.dispatchAction({
            type: 'downplay',
            seriesIndex: 0,
            dataIndex: index
          })
        }
      })
      this.charts.on('mouseout', e => {
        index = e.dataIndex
        this.charts.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: e.dataIndex
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.warp {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  position: relative;
  .charts {
    width: 209px;
    height: 209px;
    position: relative;
    margin-right: 14px;
    z-index: 1;

    @keyframes roate {
      0% {
        transform: rotate(0);
      }
      100% {
        transform: rotate(360deg);
      }
    }
    &::before {
      width: 125px;
      height: 125px;
      content: '';
      position: absolute;
      display: block;
      top: 40px;
      left: 32px;
      background: url(~@/assets/shzl/zdry_bg.png) no-repeat center / 100% 100%;
      animation: rotate 5s infinite ease-in-out;
    }
  }
  .middle {
    position: absolute;
    z-index: 2;
    width: 111px;
    height: 111px;
    left: 104.5px;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: translateX(-50%);
    @keyframes move {
      0% {
        transform: rotateY(0);
      }
      30% {
        transform: rotateY(360deg);
      }
      100% {
        transform: rotateY(360deg);
      }
    }
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      animation: move infinite 4s ease-in-out;
    }
  }
  .legend {
    width: 180px;
    margin-left: -42px;
    .item {
      width: 100%;
      height: 26px;
      display: flex;
      align-items: center;
      padding: 0 14px 0 11px;
      margin-bottom: 8px;
      margin-left: 10px;
      &:not(.active) {
        filter: grayscale(100%);
      }
      .dot {
        width: 14px;
        height: 14px;
        border-radius: 50%;
        border: 1px solid;
        position: relative;
        margin-right: 4px;
        .inner-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
      .info {
        width: 82px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .label {
          text-align: left;
          font-size: 10px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 25px;
        }
        .value {
          text-align: right;
          font-size: 10px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #ffba4f;
          line-height: 25px;
          text-shadow: 0px 0px 1px #00132e;
        }
      }

      .rate {
        margin-left: 10px;
        text-align: right;
        font-size: 10px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #2ef6ff;
        line-height: 25px;
        text-shadow: 0px 0px 1px #00132e;
      }
    }
  }
}
</style>
