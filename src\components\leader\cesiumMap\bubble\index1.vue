<template>
  <div :class="isZhzxZsShow || isSphsZsShow ? 'box1' : 'box'">
    <div>22222</div>
    <div class="title_" v-if="!isZhzxZsShow && !isSphsZsShow">
      <span v-if="zsShow || sjtsShow">事件信息</span>
      <span v-if="zhzxShow">指挥中心信息</span>
      <span v-if="yjzyShow">应急场所信息</span>
      <span v-if="cameraShow">视频连线</span>
      <span v-if="isPhotoShow">视频连线</span>
      <span v-if="ispeopleShow && this.id == 'peopleZs'">人员力量</span>
      <span v-if="ispeopleShow && this.id == 'wjMapZs'">物件力量</span>
      <span v-if="ispeopleShow && this.id == 'zuzhiMapZs'">组织力量</span>
      <span v-if="iszyllShow">网格员信息</span>
      <span v-if="isZhzxZsShow">xx指挥中心</span>
      <span v-if="isSphsZsShow">视频会商</span>
      <div class="close_" @click="closeClick"></div>
    </div>
    <div class="title_box1" v-if="isZhzxZsShow">
      <span>xx指挥中心</span>
      <div class="close_" @click="closeClick"></div>
    </div>
    <div class="title_box1" v-if="isSphsZsShow">
      <span>视频会商</span>
      <div class="close_" @click="closeClick"></div>
    </div>
    <!-- 事件类型 -->
    <div v-if="zsShow || sjtsShow">
      <div class="content_" v-if="zsShow">
        <div class="line_">
          <div class="left_">标题类型:</div>
          <div class="right_">垃圾倾倒</div>
        </div>
        <div class="line_">
          <div class="left_">事件类型:</div>
          <div class="right_">城市事件管理</div>
        </div>
        <div class="line_">
          <div class="left_">发生时间:</div>
          <div class="right_">2022/04/26 15:23:00</div>
        </div>
        <div class="line_">
          <div class="left_">发生地点:</div>
          <div class="right_">南京市鼓楼区中央门街道109号</div>
        </div>
        <div class="line_">
          <div class="left_">详情描述:</div>
          <div class="right_">来访人反映，中央门街道路口出现大量垃圾，臭气熏天，无人处理。</div>
        </div>
        <div class="line_">
          <div class="left_">事件上报人:</div>
          <div class="right_">张四海</div>
        </div>
        <div class="line_">
          <div class="left_">手机号码:</div>
          <div class="right_">12689382913</div>
        </div>
        <div class="line1_">
          <div class="left_">事件流程:</div>
          <div class="process_">
            <div>开始</div>
            <div>指挥中心</div>
            <div>成员单位</div>
            <div>结束</div>
          </div>
        </div>
      </div>
      <div class="content_" v-if="sjtsShow">
        <div class="line_">
          <div class="left_">标题类型:</div>
          <div class="right_">社会治安</div>
        </div>
        <div class="line_">
          <div class="left_">事件类型:</div>
          <div class="right_">城市管理</div>
        </div>
        <div class="line_">
          <div class="left_">发生时间:</div>
          <div class="right_">2022/6/26 15:23:00</div>
        </div>
        <div class="line_">
          <div class="left_">发生地点:</div>
          <div class="right_">南京市鼓楼区</div>
        </div>
        <div class="line_">
          <div class="left_">详情描述:</div>
          <div class="right_">来访人反映，街道路口出现'僵尸'车，无人管理。</div>
        </div>
        <div class="line_">
          <div class="left_">事件上报人:</div>
          <div class="right_">李峰</div>
        </div>
        <div class="line_">
          <div class="left_">手机号码:</div>
          <div class="right_">13893829131</div>
        </div>
        <div class="line1_">
          <div class="left_">事件流程:</div>
          <div class="process_">
            <div>开始</div>
            <div>指挥中心</div>
            <div>成员单位</div>
            <div>结束</div>
          </div>
        </div>
      </div>
      <div class="btns_">
        <div @click="photoMethod">
          <img src="@/assets/img/sjInfo/camera_bg.png" alt="" />
          <span>视频通话</span>
        </div>
        <div v-if="this.id == 'sjtsPs'" @click="routerGoZs">
          <img src="@/assets/img/sjInfo/zs_bg.png" alt="" />
          <span>战时调度</span>
        </div>
        <div v-if="this.id.includes('sjtsZs')" @click="routerGoPs">
          <img src="@/assets/img/sjInfo/zs_bg.png" alt="" />
          <span>平时调度</span>
        </div>
      </div>
    </div>
    <!-- 指挥中心 -->
    <div v-if="zhzxShow">
      <div class="content_">
        <div class="line_">
          <div class="left_">指挥中心:</div>
          <div class="right_">xx县指挥中心</div>
        </div>
        <div class="line_">
          <div class="left_">负责人:</div>
          <div class="right_">王凡</div>
        </div>
        <div class="line_">
          <div class="left_">联系方式:</div>
          <div class="right_">13907283471</div>
        </div>
        <div class="line_">
          <div class="left_">详细地址:</div>
          <div class="right_">巡查员</div>
        </div>
        <div class="btn_" @click="photoMethod">
          <img src="@/assets/img/sjInfo/camera_bg.png" alt="" />
          <span>视频通话</span>
        </div>
      </div>
    </div>
    <!-- 应急资源 -->
    <div v-if="yjzyShow">
      <div class="content_">
        <div class="line_">
          <div class="left_">应急场所:</div>
          <div class="right_">xx区-xx街道应急场所</div>
        </div>
        <div class="line_">
          <div class="left_">容纳人数:</div>
          <div class="right_">220人</div>
        </div>
        <div class="line_">
          <div class="left_">姓名:</div>
          <div class="right_">王凡</div>
        </div>
        <div class="line_">
          <div class="left_">姓名:</div>
          <div class="right_">王凡</div>
        </div>
        <div class="line_">
          <div class="left_">联系方式:</div>
          <div class="right_">13907283471</div>
        </div>
        <div class="line_">
          <div class="left_">地址:</div>
          <div class="right_">南京市鼓楼区宁海路199号</div>
        </div>
      </div>
      <div class="btns_">
        <div @click="photoMethod">
          <img src="@/assets/img/sjInfo/camera_bg.png" alt="" />
          <span>视频通话</span>
        </div>
        <div @click="routerGoZs" v-if="this.id.includes('yjzyPs')">
          <img src="@/assets/img/sjInfo/zs_bg.png" alt="" />
          <span>战时调度</span>
        </div>
        <div @click="routerGoPs" v-if="this.id == 'yjzyZs'">
          <img src="@/assets/img/sjInfo/zs_bg.png" alt="" />
          <span>平时调度</span>
        </div>
      </div>
    </div>
    <!-- 视频连线落点 -->
    <div v-if="cameraShow">
      <div class="content_">
        <div class="line_">
          <div class="left_">摄像头编号:</div>
          <div class="right_">ecdcwe23t524115v41</div>
        </div>
        <div class="line_">
          <div class="left_">经纬度:</div>
          <div class="right_">120.234310，32.845474</div>
        </div>
        <div class="line_">
          <div class="left_">详细地址:</div>
          <div class="right_">南京鼓楼区凤凰街道凤凰社区凤凰西路189</div>
        </div>
        <div class="line_">
          <div class="left_">在线状态:</div>
          <div class="right_ online_">
            <div></div>
            <span>
              在线
            </span>
          </div>
        </div>
        <div class="btn_" v-if="!isCameraShow">
          <img src="@/assets/img/sjInfo/camera_bg.png" alt="" />
          <span @click="isCameraShow = true">查看监控画面</span>
        </div>
        <div class="camera_" v-if="isCameraShow">
          <img src="@/assets/img/camera_pic.png" alt="" />
        </div>
      </div>
    </div>
    <!-- 视频通话 -->
    <div v-if="isPhotoShow">
      <div class="content_ ">
        <div class="photo_box">
          <img class="sp_other" src="@/assets/img/map/sp.png" alt="" />
          <img class="sp_my" src="@/assets/img/map/sp1.png" alt="" />
          <div class="close_png" @click="closeClick">
            <img src="@/assets/img/map/close_photo.png" alt="" />
          </div>
        </div>
      </div>
    </div>
    <!-- 人员力量 -->
    <div v-if="ispeopleShow || iszyllShow">
      <div class="content_">
        <div class="line_">
          <div class="left_">应急场所:</div>
          <div class="right_">xx区-xx街道应急场所</div>
        </div>
        <div class="line_">
          <div class="left_">容纳人数:</div>
          <div class="right_">220人</div>
        </div>
        <div class="line_">
          <div class="left_">姓名:</div>
          <div class="right_">王凡</div>
        </div>
        <div class="line_">
          <div class="left_">姓名:</div>
          <div class="right_">王凡</div>
        </div>
        <div class="line_">
          <div class="left_">联系方式:</div>
          <div class="right_">13907283471</div>
        </div>
        <div class="line_">
          <div class="left_">地址:</div>
          <div class="right_">南京市鼓楼区宁海路199号</div>
        </div>
      </div>
      <div class="btns_">
        <div @click="photoMethod">
          <img src="@/assets/img/sjInfo/camera_bg.png" alt="" />
          <span>视频通话</span>
        </div>
        <div @click="routerGoPs">
          <img src="@/assets/img/sjInfo/zs_bg.png" alt="" />
          <span>平时调度</span>
        </div>
      </div>
    </div>
    <!-- 战时指挥中心 -->
    <div v-if="isZhzxZsShow">
      <div class="content_">
        <div class="top_">
          <div><img class="left_pic" src="@/assets/img/camera_pic.png" alt="" /></div>
          <div class="top_right">
            <div>
              <div class="top_right_line">
                <img src="@/assets/img/dw_icon.png" alt="" />
                <p>中心地区</p>
              </div>
              <p class="info_">凤凰街道凤凰西路189号凤凰街道凤凰西路</p>
            </div>

            <div>
              <div class="top_right_line">
                <img src="@/assets/img/leader_icon.png" alt="" />
                <p>值班领导</p>
              </div>
              <p class="info_"><span>周锡彬</span><span>18998370101</span></p>
            </div>

            <div>
              <div class="top_right_line">
                <img src="@/assets/img/zb_icon.png" alt="" />
                <p>值班人员列表</p>
              </div>
              <p class="info_"><span>张新成</span><span>18973643193</span></p>
              <p class="info_"><span>刘鑫鑫</span><span>18973643193</span></p>
              <p class="info_"><span>王伟志</span><span>18973643193</span></p>
            </div>
          </div>
        </div>
        <div class="bottom_">
          <img class="arrow" src="@/assets/img/left_arrow.png" alt="" />
          <div class="info_box">
            <div>
              <img src="@/assets/img/touxiang.png" alt="" />
              <div class="info">
                <p>* 张新成 (主任)</p>
                <p>* 18973643193</p>
                <p>* <span>值班中</span></p>
                <p @click="sphsMethod">
                  <img class="camera_btn" src="@/assets/img/map/camera_btn.png" alt="" />
                </p>
              </div>
            </div>
            <div>
              <img src="@/assets/img/touxiang.png" alt="" />
              <div class="info">
                <p>* 张新成 (主任)</p>
                <p>* 18973643193</p>
                <p>* <span>值班中</span></p>
                <p @click="sphsMethod">
                  <img class="camera_btn" src="@/assets/img/map/camera_btn.png" alt="" />
                </p>
              </div>
            </div>
            <div>
              <img src="@/assets/img/touxiang.png" alt="" />
              <div class="info">
                <p>* 张新成 (主任)</p>
                <p>* 18973643193</p>
                <p>* <span>值班中</span></p>
                <p @click="sphsMethod">
                  <img class="camera_btn" src="@/assets/img/map/camera_btn.png" alt="" />
                </p>
              </div>
            </div>
            <div>
              <img src="@/assets/img/touxiang.png" alt="" />
              <div class="info">
                <p>* 张新成 (主任)</p>
                <p>* 18973643193</p>
                <p>* <span>值班中</span></p>
                <p @click="sphsMethod">
                  <img class="camera_btn" src="@/assets/img/map/camera_btn.png" alt="" />
                </p>
              </div>
            </div>
          </div>
          <img src="@/assets/img/right_arrow.png" alt="" />
        </div>
      </div>
    </div>
    <!-- 视频会商 -->
    <div v-if="isSphsZsShow">
      <div class="content_ sphs">
        <img class="sphs_other" src="@/assets/img/map/hs.png" alt="" />
        <img class="sphs_my" src="@/assets/img/map/sp1.png" alt="" />
        <div @click="closeClick">
          <img class="close_photo" src="@/assets/img/map/close_photo.png" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import router from '@/router/index.js'
export default {
  components: {},
  name: 'DynamicLabel',
  props: {
    title: {
      type: String,
      default: '标题'
    },
    id: {
      type: String,
      default: '001'
    },
    state: {
      type: String,
      default: '001'
    }
    // mapPopShow:{
    //   type: Boolean,
    //   default: false
    // }
  },
  data() {
    return {
      sjtsShow: false,
      zsShow: false,
      zhzxShow: false,
      yjzyShow: false,
      cameraShow: false,
      isCameraShow: false,
      isPhotoShow: false,
      ispeopleShow: false,
      iszyllShow: false,
      isZhzxZsShow: false,
      isSphsZsShow: false
    }
  },
  activated() {
    console.log(99, this.id)
  },
  created() {
    console.log(99, this.id)
  },
  mounted() {
    console.log(99, this.id)
    if (this.id == '1122') {
      this.zsShow = true
    } else if (this.id == '11221018') {
      this.sjtsShow = true
    } else if (this.id == 'zhzxPs') {
      //指挥中心平时调度
      this.zhzxShow = true
    } else if (this.id == 'sjtsPs' || this.id.includes('sjtsZs')) {
      //事件态势平时调度
      this.sjtsShow = true
    } else if (this.id.includes('yjzyPs') || this.id == 'yjzyZs') {
      //应急资源平时调度
      this.yjzyShow = true
    } else if (this.id == 'cameraPs' || this.id == 'cameraZs') {
      //视频监控
      this.cameraShow = true
    } else if (this.id == 'peopleZs' || this.id == 'zuzhiMapZs' || this.id == 'wjMapZs') {
      this.ispeopleShow = true
    } else if (this.id == 'zyllMapZs') {
      //资源力量
      this.iszyllShow = true
    } else if (this.id == 'zhzxZs') {
      // 战时指挥
      this.isZhzxZsShow = true
    }
  },
  methods: {
    closeClick() {
      // if (this.closeEvent) {
      //   this.closeEvent();
      // }
    },

    routerGoZs() {
      console.log('zs')
      router.push('/zs-small')
    },
    routerGoPs() {
      console.log('ps')
      router.push('/small')
    },
    photoMethod() {
      this.isPhotoShow = true

      this.zsShow = false
      this.sjtsShow = false
      this.zhzxShow = false
      this.yjzyShow = false
      this.cameraShow = false
      this.ispeopleShow = false
      this.iszyllShow = false
    },
    sphsMethod() {
      this.isPhotoShow = false
      this.zsShow = false
      this.sjtsShow = false
      this.zhzxShow = false
      this.yjzyShow = false
      this.cameraShow = false
      this.ispeopleShow = false
      this.isZhzxZsShow = false
      this.isSphsZsShow = true
    }
  },
  watch: {
    $route: {
      handler: function(val, oldVal) {
        console.log(val)
      },
      // 深度观察监听
      deep: true
    }
    // mapPopShow: () =>{
    //   this.$emit('addClose')
    // }
  }
}
</script>

<style lang="scss" scoped>
.box {
  width: 570px;
  // height: 697px;
  position: relative;
  bottom: 0;
  left: 0;
  background: url('~@/assets/img/sjInfo/bg.png') no-repeat center / 100% 100%;
  background-size: 100% 100%;
  padding: 13px 0 0 0;
}

.close {
  position: absolute;
  color: #fff;
  top: 1px;
  right: 10px;
  text-shadow: 2px 2px 2px #022122;
  cursor: pointer;
  animation: fontColor 1s;
  line-height: 20px;
}
.box-wrap {
  position: absolute;
  left: 21%;
  top: 0;
  width: 100%;
  height: 163px;
  border-radius: 50px 0px 50px 0px;
  border: 1px solid #38e1ff;
  background-color: #38e1ff4a;
  box-shadow: 0 0 10px 2px #29baf1;
  animation: slide 2s;
}
.box-wrap .area {
  position: absolute;
  top: 20px;
  right: 0;
  width: 95%;
  height: 30px;
  background-image: linear-gradient(to left, #4cdef9, #4cdef96b);
  border-radius: 30px 0px 0px 0px;
  animation: area 1s;
}
.pine {
  position: absolute;
  // left: 0;
  // bottom: -83px;
  width: 100px;
  height: 100px;
  box-sizing: border-box;
  line-height: 120px;
  text-indent: 5px;
}

.pine::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: -83px;
  width: 40%;
  height: 60px;
  box-sizing: border-box;
  border-bottom: 1px solid #38e1ff;
  transform-origin: bottom center;
  transform: rotateZ(135deg) scale(1.5);
  animation: slash 0.5s;
  filter: drop-shadow(1px 0px 2px #03abb4);
  /* transition: slash 2s; */
}

.area .area-title {
  text-align: center;
  line-height: 30px;
}
.textColor {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 1px 1px 5px #002520d2;
  animation: fontColor 1s;
}
.yellowColor {
  font-size: 14px;
  font-weight: 600;
  color: #f09e28;
  text-shadow: 1px 1px 5px #002520d2;
  animation: fontColor 1s;
}

.fontColor {
  font-size: 16px;
  font-weight: 800;
  color: #ffffff;
  text-shadow: 1px 1px 5px #002520d2;
  animation: fontColor 1s;
}
.content {
  padding: 55px 10px 10px 10px;
}
.content .data-li {
  display: flex;
  line-height: 50px;
  margin-left: 10px;
}

@keyframes fontColor {
  0% {
    color: #ffffff00;
    text-shadow: 1px 1px 5px #00252000;
  }
  40% {
    color: #ffffff00;
    text-shadow: 1px 1px 5px #00252000;
  }
  100% {
    color: #ffffff;
    text-shadow: 1px 1px 5px #002520d2;
  }
}

@keyframes slide {
  0% {
    border: 1px solid #38e1ff00;
    background-color: #38e1ff00;
    box-shadow: 0 0 10px 2px #29baf100;
  }

  100% {
    border: 1px solid #38e1ff;
    background-color: #38e1ff4a;
    box-shadow: 0 0 10px 2px #29baf1;
  }
}
@keyframes area {
  0% {
    width: 0%;
  }
  25% {
    width: 0%;
  }

  100% {
    width: 95%;
  }
}

/* img{
            position:absolute;
            left:30%;
            top:0;
            width: 100%;
            box-shadow: 0 0 10px 2px #29baf1;
        } */

@keyframes slash {
  0% {
    transform: rotateZ(135deg) scale(0);
  }

  100% {
    transform: rotateZ(135deg) scale(1.5);
  }
}

.title_ {
  width: 539px;
  height: 76px;
  background: url('~@/assets/img/sjInfo/title_bg.png') no-repeat center / 100% 100%;
  background-size: 100% 100%;
  margin: 0 auto;
  position: relative;
  span {
    margin: 20px 0 0 44px;
    font-size: 36px;
    font-family: PangMenZhengDao;
    color: #2aebffff;
    line-height: 45px;
    // background: linear-gradient(180deg, #FFFFFF 0%, #D7FFD5 49%, #34FF2A 100%), radial-gradient(180deg, #FFEF32 0%, rgba(255,239,50,0) 100%), linear-gradient(180deg, #FFFFFF 0%, #2AEBFF 100%);
    // -webkit-background-clip: text;
    // -webkit-text-fill-color: transparent;
    position: absolute;
    z-index: 100;
  }
  .close_ {
    width: 36px;
    height: 36px;
    background: url('~@/assets/img/sjInfo/close_bg.png') no-repeat center / 100% 100%;
    background-size: 100% 100%;
    position: absolute;
    left: 487px;
    top: 19px;
    cursor: pointer;
  }
}
.content_ {
  padding: 35px 54px 30px 54px;
  .line_ {
    display: flex;
    justify-content: space-between;
    margin-bottom: 23px;
    .left_ {
      font-size: 18px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #abdcf4;
      line-height: 25px;
      width: 160px;
    }
    .right_ {
      font-size: 18px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 25px;
      text-align: right;
      &.online_ {
        display: flex;
        align-items: center;
        & > div {
          width: 9px;
          height: 9px;
          background: #25f200;
          border-radius: 50%;
          margin-right: 10px;
        }
        & > span {
          color: #25f200ff;
        }
      }
    }
  }
  .line1_ {
    .left_ {
      font-size: 18px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #abdcf4;
      line-height: 25px;
    }
    .process_ {
      display: flex;
      margin-top: 16px;
      height: 40px;
      & > div {
        width: 120px;
        height: 40px;
        flex-shrink: 0;
        color: #fff;
        text-align: center;
        line-height: 40px;
        font-size: 18px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        &:first-of-type {
          background: url('~@/assets/img/sjInfo/process1_.png') no-repeat center / 100%100%;
          background-size: 100% 100%;
        }
        &:nth-of-type(2) {
          background: url('~@/assets/img/sjInfo/process2_.png') no-repeat center / 100%100%;
          background-size: 100% 100%;
          margin-left: -8px;
        }
        &:nth-of-type(3) {
          background: url('~@/assets/img/sjInfo/process3_.png') no-repeat center / 100%100%;
          background-size: 100% 100%;
          margin-left: -8px;
        }
        &:last-of-type {
          background: url('~@/assets/img/sjInfo/process4_.png') no-repeat center / 100%100%;
          background-size: 100% 100%;
          margin-left: -8px;
        }
      }
    }
  }
}
.photo_box {
  // width: 304px;
  height: 467px;
  margin: 0 auto;
  border: 1px solid #eee;
  box-sizing: border-box;
  position: relative;
  .sp_other {
    width: 100%;
    height: 100%;
  }
  .sp_my {
    width: 95px;
    height: 130px;
    position: absolute;
    top: 0;
    right: 0;
  }
  .close_png {
    width: 50px;
    height: 50px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 10%;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
.btns_ {
  padding: 30px 98px 60px 106px;
  // margin-top: 60px;
  display: flex;
  justify-content: space-between;
  & > div {
    width: 169px;
    height: 33px;
    background: url('~@/assets/img/sjInfo/btn_bg.png') no-repeat center / 100%100%;
    background-size: 100% 100%;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
    position: relative;
    img {
      position: absolute;
      left: 31px;
      top: 4px;
    }
    span {
      position: absolute;
      left: 66px;
      top: 4px;
    }
  }
}
.btn_ {
  width: 169px;
  height: 33px;
  background: url('~@/assets/img/sjInfo/btn_bg.png') no-repeat center / 100%100%;
  background-size: 100% 100%;
  font-size: 16px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #ffffff;
  // position: relative;
  margin: 64px auto 0;
  display: flex;
  align-items: center;
  line-height: 33px;
  img {
    margin: 0px 0 0 20px;
  }
  span {
    display: inline-block;
    margin: 0px 0 0 6px;
  }
}
.camera_ {
  width: 468px;
  height: 263px;
  // border: 1px solid #eee;
  img {
    width: 100%;
    height: 100%;
  }
}
.box1 {
  width: 850px;
  position: relative;
  bottom: 0;
  left: 0;
  background: url('~@/assets/img/sjInfo/bg.png') no-repeat center / 100% 100%;
  background-size: 100% 100%;
  padding: 13px 0 0 0;
  .title_box1 {
    width: 100%;
    height: 50px;
    background-size: 100% 100%;
    position: relative;
    span {
      font-size: 36px;
      font-family: PangMenZhengDao;
      color: #2aebffff;
      line-height: 45px;
      position: absolute;
      z-index: 100;
      left: 50%;
      transform: translate(-50%);
    }
    .close_ {
      width: 36px;
      height: 36px;
      background: url('~@/assets/img/sjInfo/close_bg.png') no-repeat center / 100% 100%;
      background-size: 100% 100%;
      position: absolute;
      right: 20px;
      top: 0px;
      cursor: pointer;
    }
  }
  .top_ {
    display: flex;
    align-items: center;
    .left_pic {
      width: 540px;
      height: 300px;
    }
    .top_right {
      height: 300px;
      padding: 0 0 0 10px;
      .top_right_line {
        background: url('~@/assets/img/line_bg.png') no-repeat center / 100% 100%;
        background-size: 100% 100%;
        display: flex;
        img {
          width: 16px;
          height: 16px;
          margin: 6px 10px 0 10px;
        }
        p {
          font-size: 16px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #ffffff;
          line-height: 28px;
        }
      }
      .info_ {
        font-size: 16px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 28px;
        padding: 4px;
        display: flex;
        justify-content: space-between;
      }
    }
  }
  .content_ {
    padding: 10px 30px 20px 30px;
  }
  .bottom_ {
    display: flex;
    height: 100px;
    align-items: center;
    justify-content: space-between;
    .arrow {
      width: 34px;
      height: 51px;
    }
    .info_box {
      display: flex;
      align-items: center;
      justify-content: space-around;
      font-size: 10px;
      font-family: PingFangSC-Regular, PingFang SC;
      color: #ffffff;
      line-height: 22px;
      & > div {
        display: flex;
        align-items: center;
        justify-content: space-between;
        img {
          width: 80px;
          height: 100px;
          &.camera_btn {
            width: 30px;
            height: 20px;
          }
        }
        .info {
          height: 100px;
          padding: 4px 0 0 0;
          & p:nth-of-type(3) {
            color: #fffe00;
          }
          & p:nth-of-type(4) {
            margin-left: 8px;
          }
        }
      }
    }
  }
  .sphs {
    position: relative;
    .sphs_my {
      width: 95px;
      height: 130px;
      position: absolute;
      top: 12px;
      right: 37px;
    }
    & > .sphs_other {
      width: 784px;
      height: 465px;
    }
    .close_photo {
      position: absolute;
      width: 50px;
      height: 50px;
      left: 50%;
      transform: translateX(-50%);
      bottom: 10%;
      cursor: pointer;
    }
  }
}
</style>
