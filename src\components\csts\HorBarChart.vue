<template>
  <div class="chart" ref="chart"></div>
</template>

<script>
import echarts from 'echarts';
export default {
  name: 'BarH<PERSON><PERSON><PERSON>',
  props: {
    data: {
      type: Array,
      default: () => [
        ['2019-12', 700],
        ['2020-01', 600],
        ['2020-02', 550],
        ['2020-03', 450],
        ['2020-04', 300],
        ['2020-05', 200],
        ['2020-06', 600],
        ['2020-07', 550],
        ['2020-08', 450],
        ['2020-09', 300],
        ['2020-10', 200],
        ['2020-11', 419],
        ['2020-12', 319]
      ]
    }
  },
  data() {
    return {
      option: null,
      myChart: null,
      dataArr: 4,
    };
  },
  computed: {
    showData() {
      return [['product', '今年']].concat(this.data);
    }
  },
  mounted() {
    this.option = {
      series: [
        {
          name: "最外部进度条",
          type: "gauge",
          radius: '100%',
          startAngle: 130,
          endAngle: -80,
          splitNumber: 10,
          axisLine: {
            lineStyle: {
              color: [
                [this.dataArr / 10, new echarts.graphic.LinearGradient(
                  0, 0, 1, 0, [
                    {
                      offset: 0,
                      color: 'rgba(70,231,220,0)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(0, 166, 255, 1)',
                    }
                  ]
                )],
                [
                  1, 'rgba(28,128,245,.0)'
                ]
              ],
              width: 3
            },
          },
          axisLabel: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
          itemStyle: {
            show: false,
          },
          detail: {
            show: false
          },
          title: { //标题
            show: false,
          },
          data: [
            {
              name: "title",
              value: this.dataArr,
            },
          ],
          pointer: {
            show: false,
          },
          animationDuration: 4000,
        }, 
        {
          name: "ring5",  //绿点
          type: 'custom',
          coordinateSystem: "none",
          renderItem: function(params, api) {
            return {
              type: 'circle',
              shape: {
                cx: 166,
                cy: 28,
                r: 4
              },
              style: {
                stroke: "#00A6FF",      //绿
                fill: "#00A6FF"
              },
              silent: true
            };
          },
          data: [0]
        }, 
        {
          name: "最外部进度条",
          type: "gauge",
          radius: "46%",
          center: ["center", "center"], // 仪表位置
          splitNumber: 200,
          axisLine: {
            lineStyle: {
              color: [
                [
                  100,
                  new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                    {
                      offset: 0,
                      color: "rgba(145,207,255,0)",
                    },
                    {
                      offset: 0.5,
                      color: "rgba(70, 155 ,240,0.2)",
                    },
                    {
                      offset: 1,
                      color: "rgba(70, 155 ,240,0.8)",
                    },
                  ]),
                ],
                [1, "#f00"],
              ],
              width: 4,
            },
          },
          axisLabel: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
          itemStyle: {
            show: false,
          },
          detail: {
            show: false,
          },
          pointer: {
            show: false,
          },
          animationDuration: 4000,
        },
        {
          name: '内圈小',
          type: 'gauge',
          pointer:{
            show:false  
          },
          radius: '85%',
          startAngle: 220,
          endAngle: -40,
          splitNumber: 4,
          axisLine: { // 坐标轴线
            lineStyle: { // 属性lineStyle控制线条样式
              color: [
                [1, 'rgba(0, 68 ,102,0.3)']
              ],
              width: 30
            }
          },
          splitLine: { //分隔线样式
            show: false,
          },
          axisLabel: { //刻度标签
            show: false,
          },
          axisTick: { //刻度样式
            show: false,
          },
          detail: {
            // 其余属性默认使用全局文本样式，详见TEXTSTYLE
            "formatter": function(value) {
              return '110满意度'
            },
            "offsetCenter": [0, "110%"],
            "textStyle": {
              padding: [0, 0, 80, 0],
              "fontSize": 16,
              fontWeight: '400',
              color: '#C2DDFC'
            }
          },
        }, 
        {
          name: '内圈小',
          type: 'gauge',
          title : {
            // 其余属性默认使用全局文本样式，详见TEXTSTYLE
            fontWeight: 'bolder',
            fontSize: 30,
            fontStyle: 'italic',
            offsetCenter: [0, '33%'],
          },
          pointer:{
            show:true  
          },
          radius: '85%',
          startAngle: 220,
          endAngle: 30,
          splitNumber: 4,
          axisLine: { // 坐标轴线
            lineStyle: { // 属性lineStyle控制线条样式
              color: [
                [
                  100,
                  new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                    {
                      offset: 0,
                      color: "rgba(5, 90, 142, 0.53)",
                    },
                    {
                      offset: 1,
                      color: "rgba(69, 253, 231, 1)",
                    },
                  ]),
                ],
                [1, "#f00"],
              ],
              width: 30,
              shadowColor: '#0093ee', //默认透明
              shadowOffsetX: 0,
              shadowOffsetY: 0,
              shadowBlur: 10,
              opacity: 1,
            }
          },
          splitLine: { //分隔线样式
            show: false,
          },
          axisLabel: { //刻度标签
            show: false,
          },
          axisTick: { //刻度样式
            show: false,
          },
          detail: {
            // 其余属性默认使用全局文本样式，详见TEXTSTYLE
            "formatter": function(value) {
              return '80%'
            },
            "offsetCenter": [0, "50%"],
            "textStyle": {
              padding: [0, 0, 80, 0],
              "fontSize": 36,
              color: '#fff'
            }
          },
        }, 
      ]
    };
    this.myChart = echarts.init(this.$refs.chart);
    this.myChart.setOption(this.option);
  },
  watch: {
    showData(newVal) {
      this.option.dataset.source = newVal;
      this.myChart.setOption(this.option, true);
    }
  },
  methods: {
    getCirlPoint(x0, y0, r, angle) {
      let x1 = x0 + r * Math.cos(angle * Math.PI / 180)
      let y1 = y0 + r * Math.sin(angle * Math.PI / 180)
      return {
        x: x1,
        y: y1
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>
