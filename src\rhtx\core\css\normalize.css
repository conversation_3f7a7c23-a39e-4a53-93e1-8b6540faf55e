.rtc-base {
  /*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */
  /* Document
   ========================================================================== */
  /**
 * 1. Correct the line height in all browsers.
 * 2. Prevent adjustments of font size after orientation changes in iOS.
 */
  /* Sections
   ========================================================================== */
  /**
 * Remove the margin in all browsers.
 */
  /**
 * Render the `main` element consistently in IE.
 */
  /**
 * Correct the font size and margin on `h1` elements within `section` and
 * `article` contexts in Chrome, Firefox, and Safari.
 */
  /* Grouping content
   ========================================================================== */
  /**
 * 1. Add the correct box sizing in Firefox.
 * 2. Show the overflow in Edge and IE.
 */
  /**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
  /* Text-level semantics
   ========================================================================== */
  /**
 * Remove the gray background on active links in IE 10.
 */
  /**
 * 1. Remove the bottom border in Chrome 57-
 * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
 */
  /**
 * Add the correct font weight in Chrome, Edge, and Safari.
 */
  /**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
  /**
 * Add the correct font size in all browsers.
 */
  /**
 * Prevent `sub` and `sup` elements from affecting the line height in
 * all browsers.
 */
  /* Embedded content
   ========================================================================== */
  /**
 * Remove the border on images inside links in IE 10.
 */
  /* Forms
   ========================================================================== */
  /**
 * 1. Change the font styles in all browsers.
 * 2. Remove the margin in Firefox and Safari.
 */
  /**
 * Show the overflow in IE.
 * 1. Show the overflow in Edge.
 */
  /**
 * Remove the inheritance of text transform in Edge, Firefox, and IE.
 * 1. Remove the inheritance of text transform in Firefox.
 */
  /**
 * Correct the inability to style clickable types in iOS and Safari.
 */
  /**
 * Remove the inner border and padding in Firefox.
 */
  /**
 * Restore the focus styles unset by the previous rule.
 */
  /**
 * Correct the padding in Firefox.
 */
  /**
 * 1. Correct the text wrapping in Edge and IE.
 * 2. Correct the color inheritance from `fieldset` elements in IE.
 * 3. Remove the padding so developers are not caught out when they zero out
 *    `fieldset` elements in all browsers.
 */
  /**
 * Add the correct vertical alignment in Chrome, Firefox, and Opera.
 */
  /**
 * Remove the default vertical scrollbar in IE 10+.
 */
  /**
 * 1. Add the correct box sizing in IE 10.
 * 2. Remove the padding in IE 10.
 */
  /**
 * Correct the cursor style of increment and decrement buttons in Chrome.
 */
  /**
 * 1. Correct the odd appearance in Chrome and Safari.
 * 2. Correct the outline style in Safari.
 */
  /**
 * Remove the inner padding in Chrome and Safari on macOS.
 */
  /**
 * 1. Correct the inability to style clickable types in iOS and Safari.
 * 2. Change font properties to `inherit` in Safari.
 */
  /* Interactive
   ========================================================================== */
  /*
 * Add the correct display in Edge, IE 10+, and Firefox.
 */
  /*
 * Add the correct display in all browsers.
 */
  /* Misc
   ========================================================================== */
  /**
 * Add the correct display in IE 10+.
 */
  /**
 * Add the correct display in IE 10.
 */
}

.rtc-base main {
  display: block;
}

.rtc-base h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

.rtc-base hr {
  box-sizing: content-box;
  /* 1 */
  height: 0;
  /* 1 */
  overflow: visible;
  /* 2 */
}

.rtc-base pre {
  font-family: monospace, monospace;
  /* 1 */
  font-size: 1em;
  /* 2 */
}

.rtc-base a {
  background-color: transparent;
}

.rtc-base abbr[title] {
  border-bottom: none;
  /* 1 */
  text-decoration: underline;
  /* 2 */
  text-decoration: underline dotted;
  /* 2 */
}

.rtc-base b,
.rtc-base strong {
  font-weight: bolder;
}

.rtc-base code,
.rtc-base kbd,
.rtc-base samp {
  font-family: monospace, monospace;
  /* 1 */
  font-size: 1em;
  /* 2 */
}

.rtc-base small {
  font-size: 80%;
}

.rtc-base sub,
.rtc-base sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

.rtc-base sub {
  bottom: -0.25em;
}

.rtc-base sup {
  top: -0.5em;
}

.rtc-base img {
  border-style: none;
}

.rtc-base button,
.rtc-base input,
.rtc-base optgroup,
.rtc-base select,
.rtc-base textarea {
  font-family: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  line-height: 1.15;
  /* 1 */
  margin: 0;
  /* 2 */
}

.rtc-base button,
.rtc-base input {
  /* 1 */
  overflow: visible;
}

.rtc-base button,
.rtc-base select {
  /* 1 */
  text-transform: none;
}

.rtc-base button,
.rtc-base [type='button'],
.rtc-base [type='reset'],
.rtc-base [type='submit'] {
  -webkit-appearance: button;
}

.rtc-base button::-moz-focus-inner,
.rtc-base [type='button']::-moz-focus-inner,
.rtc-base [type='reset']::-moz-focus-inner,
.rtc-base [type='submit']::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

.rtc-base button:-moz-focusring,
.rtc-base [type='button']:-moz-focusring,
.rtc-base [type='reset']:-moz-focusring,
.rtc-base [type='submit']:-moz-focusring {
  outline: 1px dotted ButtonText;
}

.rtc-base fieldset {
  padding: 0.35em 0.75em 0.625em;
}

.rtc-base legend {
  box-sizing: border-box;
  /* 1 */
  color: inherit;
  /* 2 */
  display: table;
  /* 1 */
  max-width: 100%;
  /* 1 */
  padding: 0;
  /* 3 */
  white-space: normal;
  /* 1 */
}

.rtc-base progress {
  vertical-align: baseline;
}

.rtc-base textarea {
  overflow: auto;
}

.rtc-base [type='checkbox'],
.rtc-base [type='radio'] {
  box-sizing: border-box;
  /* 1 */
  padding: 0;
  /* 2 */
}

.rtc-base [type='number']::-webkit-inner-spin-button,
.rtc-base [type='number']::-webkit-outer-spin-button {
  height: auto;
}

.rtc-base [type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

.rtc-base [type='search']::-webkit-search-decoration {
  -webkit-appearance: none;
}

.rtc-base ::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

.rtc-base details {
  display: block;
}

.rtc-base summary {
  display: list-item;
}

.rtc-base template {
  display: none;
}

.rtc-base [hidden] {
  display: none;
}

.rtc-base ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

.rtc-base input {
  border: none;
  outline: none;
}

.rtc-base button {
  border: none;
  cursor: pointer;
  background-color: transparent;
}

.rtc-base button:hover {
  opacity: 0.8;
}

.rtc-base button:active {
  opacity: 0.6;
}

.rtc-base *,
.rtc-base ::after,
.rtc-base ::before {
  box-sizing: border-box;
}

.rtc-base h1,
.rtc-base h2,
.rtc-base h3,
.rtc-base h4,
.rtc-base h5,
.rtc-base h6 {
  font-weight: 400;
}

.rtc-base a,
.rtc-base body {
  color: #333;
}

.rtc-base blockquote,
.rtc-base body,
.rtc-base button,
.rtc-base dd,
.rtc-base div,
.rtc-base dl,
.rtc-base dt,
.rtc-base form,
.rtc-base h1,
.rtc-base h2,
.rtc-base h3,
.rtc-base h4,
.rtc-base h5,
.rtc-base h6,
.rtc-base input,
.rtc-base li,
.rtc-base ol,
.rtc-base p,
.rtc-base pre,
.rtc-base td,
.rtc-base textarea,
.rtc-base th,
.rtc-base ul {
  margin: 0;
  padding: 0;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.rtc-base a:active,
.rtc-base a:hover {
  outline: 0;
}

.rtc-base img {
  border: none;
}

.rtc-base li {
  list-style: none;
}

.rtc-base table {
  border-collapse: collapse;
  border-spacing: 0;
}

.rtc-base h4,
.rtc-base h5,
.rtc-base h6 {
  font-size: 100%;
}

.rtc-base button,
.rtc-base input,
.rtc-base optgroup,
.rtc-base option,
.rtc-base select,
.rtc-base textarea {
  font-family: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  outline: 0;
}

.rtc-base pre {
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
}

.rtc-base body {
  line-height: 1.6;
  color: rgba(0, 0, 0, 0.85);
  font: 14px Helvetica Neue, Helvetica, PingFang SC, Tahoma, Arial, sans-serif;
}

.rtc-base hr {
  line-height: 0;
  margin: 10px 0;
  padding: 0;
  border: none !important;
  border-bottom: 1px solid #eee !important;
  clear: both;
  background: 0 0;
}

.rtc-base a {
  text-decoration: none;
}

.rtc-base a:hover {
  color: #777;
}

.rtc-base a cite {
  font-style: normal;
  *cursor: pointer;
}
/* layui公共部分 */
.rtc-base h1,
.rtc-base h2,
.rtc-base h3,
.rtc-base h4,
.rtc-base h5,
.rtc-base h6 {
  font-weight: 400;
}

.rtc-base a,
.rtc-base body {
  color: #333;
}

.rtc-base blockquote,
.rtc-base body,
.rtc-base button,
.rtc-base dd,
.rtc-base div,
.rtc-base dl,
.rtc-base dt,
.rtc-base form,
.rtc-base h1,
.rtc-base h2,
.rtc-base h3,
.rtc-base h4,
.rtc-base h5,
.rtc-base h6,
.rtc-base input,
.rtc-base li,
.rtc-base ol,
.rtc-base p,
.rtc-base pre,
.rtc-base td,
.rtc-base textarea,
.rtc-base th,
.rtc-base ul {
  margin: 0;
  padding: 0;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.rtc-base a:active,
.rtc-base a:hover {
  outline: 0;
}

.rtc-base img {
  border: none;
}

.rtc-base li {
  list-style: none;
}

.rtc-base table {
  border-collapse: collapse;
  border-spacing: 0;
}

.rtc-base h4,
.rtc-base h5,
.rtc-base h6 {
  font-size: 100%;
}

.rtc-base button,
.rtc-base input,
.rtc-base optgroup,
.rtc-base option,
.rtc-base select,
.rtc-base textarea {
  font-family: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  outline: 0;
}

.rtc-base pre {
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
}

.rtc-base body {
  line-height: 1.6;
  color: rgba(0, 0, 0, 0.85);
  font: 14px Helvetica Neue, Helvetica, PingFang SC, Tahoma, Arial, sans-serif;
}

.rtc-base hr {
  line-height: 0;
  margin: 10px 0;
  padding: 0;
  border: none !important;
  border-bottom: 1px solid #eee !important;
  clear: both;
  background: 0 0;
}

.rtc-base a {
  text-decoration: none;
}

.rtc-base a:hover {
  color: #777;
}

.rtc-base a cite {
  font-style: normal;
  *cursor: pointer;
}
