<template>
	<div class="child-block">
		<div class="content" v-if="contentDataList.length > 0">
			<slot></slot>
			<div
				class="data-item"
				v-for="(item, index) in contentDataList"
				:key="index"
				@click="
					() => {
						item.func(item.argmachType)
					}
				"
			>
				<img class="data-item-icon" :src="item.iconUrl" alt="" />
				<div class="data-item-content">
					<div class="data-item-number-box">
						<div class="data-item-number">{{ item.number }}</div>
						<div class="data-item-number-unit">{{ contentNumberUnit }}</div>
					</div>
					<div class="data-item-title">{{ item.title }}</div>
				</div>
			</div>
		</div>
		<div v-else class="empty-box">
			<img class="zwsjImg" src="@/assets/bjnj/zwsj.png" />
		</div>
	</div>
</template>

<script>
export default {
	props: {
		contentDataList: {
			type: Array,
			default: () => [],
		},


		contentNumberUnit: {
			type: String,
			default: '万台',
		},
	},
	data() {
		return {}
	},
	computed: {},
	methods: {},

	watch: {},
}
</script>

<style lang="less" scoped>
.child-block {
	width: 448px;
	height: 100%;
	box-sizing: border-box;
	z-index: 101;
	position: relative;
	padding-left: 10px;
	padding-top: 20px;
	overflow-y: auto;

	&::-webkit-scrollbar {
		width: 4px; /* 滚动条宽度 */
	}

	//轨道
	&::-webkit-scrollbar-track {
		background: #04244e; /* 滚动条轨道背景 */
		border-radius: 10px;
		padding: 5px 0;
	}

	//滑块
	&::-webkit-scrollbar-thumb {
		background: #82aed0; /* 滑块背景颜色 */
		border-radius: 10px;
		height: 15px;
	}

	//悬停滑块颜色
	&::-webkit-scrollbar-thumb:hover {
		background: #ff823b; /* 鼠标悬停时滑块颜色 */
	}

	//箭头
	&::-webkit-scrollbar-button {
		display: none; /* 隐藏滚动条的箭头按钮 */
	}

	.content {
		position: relative;
		width: 100%;
		// height: calc(100% - 74px);
		display: flex;
		justify-content: flex-start;
		flex-wrap: wrap;
		align-items: center;
		margin-top: 0;

		&.no-active-content {
			height: 0;
			display: none;
		}

		.data-item {
			width: 141px;
			// height: 40px;
			display: flex;
			justify-content: flex-start;
			align-items: flex-start;
			margin-right: 4px;
			margin-bottom: 24px;
			padding: 0px;
			cursor: pointer;
			margin-right: 0;
		}

		.data-item:hover {
			// background: url(~@/assets/img/block-box/block-box-child-item-active-bg.png);
			/* 背景不重复 */
			background-repeat: no-repeat;
			/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
			background-size: cover;
			/* 背景居中显示（可选） */
			background-position: center;
		}

		.data-item-icon {
			width: 64px;
			height: 60px;
			/* 背景不重复 */
			background-repeat: no-repeat;
			/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
			background-size: cover;
			/* 背景居中显示（可选） */
			background-position: center;
			margin-right: 4px;
		}

		.data-item-content {
			width: calc(100% - 64px - 4px);
			height: 100%;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: flex-start;

			.data-item-title {
				width: 100%;
				height: 14px;

				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 12px;
				color: #b0e0ff;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}

			.data-item-number-box {
				width: 100%;
				height: 20px;
				display: flex;
				justify-content: flex-start;
				align-items: flex-end;
				margin-bottom: 8px;
				.data-item-number {
					height: 20px;

					margin-right: 4px;

					font-family: DINPro, DINPro;
					font-weight: bold;
					font-size: 20px;
					color: #4de4ff;
					line-height: 20px;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}
				.data-item-number-unit {
					height: 17px;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 12px;
					color: #4de4ff;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}
			}
		}
	}

	.empty-box {
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		.zwsjImg {
			width: 240px;
			margin: 20px 0;
		}
	}
}

.popDropDown {
	::v-deep .el-dropdown {
		color: #caecff;
		font-family: YouSheBiaoTiHei;
		font-size: 16px;
		line-height: 21px;
		text-align: right;
		font-style: normal;
		line-height: 40px;
		right: 0;
		position: absolute;
	}
	::v-deep .el-dropdown-menu {
		background-color: #00173b !important;
		border: 1px solid #2b7bbb !important;
	}
	.el-dropdown-menu__item {
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
		line-height: 40px;
		padding: 0 30px;
		&:hover {
			background: #185493;
			color: rgba(255, 255, 255, 0.8);
		}
	}
}
</style>
