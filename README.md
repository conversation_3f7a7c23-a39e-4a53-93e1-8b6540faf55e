**指挥调度大屏**

**一、运行环境:**

1、node 版本：14.21.3

2、npm 版本：6.14.18

3、将镜像设置为淘宝镜像地址：npm config set registry https://registry.npmmirror.com/

**二、使用说明:**

1、npm install 进行依赖安装

2、npm run serve 项目本地运行

3、npm run build 项目打包

测试服务器：
    172.16.130.45    root/centos7   /home/<USER>/data/front/static/zhdd-screen

正式环境
    地址:/data/fronted
    解压后运行命令:/usr/local/nginx/sbin/nginx -s reload

rimraf

#### 地图JSON数据获取
https://datav.aliyun.com/portal/school/atlas/area_selector