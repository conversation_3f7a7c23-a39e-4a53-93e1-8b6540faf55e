<template>
  <div class="tkBox">
    <div class="tkTitle"><span>生态治理成果</span></div>
    <img @click="closeEmitai" src="@/assets/hszl/tkGb.png" class="tkGb" />
    <div class="tabs">
      <div
        class="tab"
        :class="{ active: tabActive1 === it.type }"
        v-for="(it, i) of tabs"
        :key="i"
        @click="changeTab(it, i)"
      >
        <div class="item_">
          <span>{{ it.value }}</span>
          <span>{{ it.name }}</span>
        </div>
      </div>
    </div>
    <div class="table" >
      <SwiperTable
        :titles="['序号','社区','自然村','创建类型','创建时限']"
        :widths="['10%', '20%', '20%','30%', '20%']"
        :data="tableData"
        :contentHeight="'440px'"
        :isNeedOperate="true"
      ></SwiperTable>
    </div>
    <div class="fy_page">
      <Page :current="pageNum" :total="total" @on-change="pageNumChange" show-total></Page>
    </div>
  </div>
</template>

<script>
import SwiperTable from '@/components/leader/leader_zwjs/SwiperTable.vue'
import { getStzlcg, getStzlcgPage } from '@/api/hs/hs.hszl.js'

export default {
  name: 'stzlcgtk',
  components: {
    SwiperTable,
  },
  props: {
    tabActive: {
      type: [Number, String],
      default: 0,
    },
  },
  data() {
    return {
      tabs: [
        { name: '高企', value: '57' },
        { name: '规上', value: '98' },
        { name: '独角兽', value: '1' },
        { name: '瞪羚', value: '5' },
        { name: '专精特新', value: '9' },
        { name: '科技型', value: '105' },
        { name: '九小场所', value: '9030' },
        { name: '生成型企业', value: '414' },
        { name: '办公型企业', value: '605' },
        { name: '仓储企业', value: '150' },
      ],
      // tabActive1: 0,
      tableData1: [],
      tableData2: [],
      tableData3: [],
      tableData4: [],
      tableData5: [],
      tableData6: [],
      tableData: [],
      tableData10: [],
      total: 0,
      pageNum: 1,
      pageSize: 10,
      tabActive1:this.tabActive
    }
  },
  methods: {
    closeEmitai() {
      this.$emit('closeEmitai')
    },
    async getRkfbFn() {
      let res = await getRkfb()
      console.log(1111, res)
      this.box2data = [['produce', ''], ...res.result.map((it) => [it.name, it.sysvalue])]
    },
    changeTab(it, i) {
      this.pageNum = 1
      this.tabActive1 = it.type
      this.getTable()
    },
    async getScztFn() {
      this.tabs = []
      let res = await getStzlcg()
      if (res?.code == '200') {
        this.tabs = res.result
        this.getTable(this.tabActive1)
      }
    },
    async getTable() {
      this.tableData=[]
      let res = await getStzlcgPage(this.tabActive1, this.pageNum, this.pageSize)
      if (res?.code == '200') {
        this.tableData = res.result.data.map((it, i) => [
          this.pageSize * (this.pageNum - 1) + i + 1,
          it.community,
          it.village,
          it.typeName,
          it.createTime,
        ])
        this.total = res.result.recordsTotal
      }
    },
    pageNumChange(val) {
      this.pageNum = val
      this.getTable()
    },
  },
  mounted() {
    this.getScztFn()
  },
  computed: {
    
  },
}
</script>

<style lang="less" scoped>
.tkBox {
  width: 1700px;
  height: 843px;
  background: url(~@/assets/hszl/tkBg.png) no-repeat center / 100% 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10000;
  padding: 0 40px;
  .tkTitle {
    width: 634px;
    height: 74px;
    background: url(~@/assets/hszl/tkTitle.png) no-repeat center / 100% 100%;
    margin: 0 auto;
    margin-top: 22px;
    line-height: 74px;
    span {
      font-size: 36px;
      font-family: PangMenZhengDao;
      color: #ffffff;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .tkGb {
    position: absolute;
    top: 38px;
    right: 43px;
  }
  .tabs {
    display: flex;
    gap: 13px;
    margin: 28px 20px 28px 20px;
    .tab {
      width: 191px;
      line-height: 41px;
      background: url('~@/assets/map/dialog/bg5.png') no-repeat center / 100% 100%;
      cursor: pointer;
      .item_ {
        display: flex;
        flex-direction: column;
      }
      span {
        font-size: 24px;
        font-family: PangMenZhengDao;
        color: #ffffff;
      }
      &.active {
        background: url('~@/assets/map/dialog/bg4.png') no-repeat center / 100% 100%;
        span {
          font-family: PangMenZhengDao;
          background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
  }
  .table {
    padding: 0 20px;
  }
  /deep/ .ivu-page-item {
    background: transparent;
    color: #fff;
  }
  /deep/ .ivu-page-item a {
    color: #fff;
  }
  /deep/ .ivu-page-prev,
  /deep/ .ivu-page-next {
    background: transparent;
  }
  .fy_page {
    margin-top: 70px;
  }
}
</style>
