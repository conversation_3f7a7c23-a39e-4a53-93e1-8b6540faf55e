<template>
	<div id="mengCheng" v-if="!wwww">
		<HeaderMain2 v-if="url == '/zt' && !isPCmeeting"></HeaderMain2>
		<header-main v-if="!isPCmeeting"></header-main>
		<div class="left_bg1" v-if="!bgShow4 && !isPCmeeting"></div>
		<div class="right_bg1" v-if="bgShow && !isPCmeeting"></div>
		<!-- <leaderMiddle /> -->
		<!-- <router-view style="z-index: 2; position: absolute; top: 0; left: 0" /> -->
		<router-view />
		<!-- <myRobotAuto ref="myRobotAutoRef" /> -->
		<!-- <bottom-main></bottom-main> -->
		<!-- <div class="map_box" v-show="$route.path == '/leaderZt'">
      <cesiumMap ref="CesiumMapRef" @bubble="bubble"></cesiumMap>
    </div>-->
		<!-- 点对点视频会议组件 -->
		<PointToPoint
			v-model="pointMeetingShow"
			:invitedMobile="invitedMobile"
			:inviteWay="pointInviteWay"
			:userId="userId"
			v-drag
		></PointToPoint>
		<!-- 被邀请弹窗 -->
		<InviteBoard
			v-if="!hasOpenMeetingWindow"
			v-model="inviteBoardShow"
			:inviteBoardData="inviteBoardData"
			@refuse="refuseInvite"
			@accept="acceptInvite"
			v-drag
		></InviteBoard>

		<!-- 多人视频会议组件 -->
		<MultipleMeeting
			:roomName1="roomName1"
			:cameraStatus="cameraStatus"
			:allTracks="allTracks"
			:microphoneStatus="microPhoneStatus"
			ref="multipleMeeting"
			v-model="multipleMeetingShow"
			:meetingData="multipleMeetingData"
			:launchInviteList="launchInviteList"
			@localCameraChange="(e) => (multiLocalCameraStatus = e)"
			@localMicrophoneChange="(e) => (multiLocalMicroStatus = e)"
			@multiMeetingMinum="minumMultiMeeting"
			@multiMeetingClose="multiMeetingClose"
			@clearTracks="clearTracks"
			@sendNotice="sendNotice"
			v-drag
		></MultipleMeeting>
		<!-- 发起会议弹窗 -->
		<!-- <AddressBook :areaId="areaId" v-model="addressBookShow" type="launch" @launchMeeting="launchMeeting" @launchPointToPoint="launchPointToPoint"></AddressBook> -->
		<MeetingEnterDialog
			:areaId="areaId"
			v-model="addressBookShow"
			@launchMeeting="launchMeeting"
			@launchPointToPoint="launchPointToPoint"
			@openJoinMeetingDialog="openJoinMeetingDialog"
			@quickLaunch="quickLaunchMeeting"
		></MeetingEnterDialog>
		<!-- 会议准备弹窗 -->
		<ReadyMeetingDialog
			v-if="readyMeetingShow"
			:launchType="launchType"
			@closeDialog="readyMeetingShow = false"
			@meetingStart="startMeeting"
		></ReadyMeetingDialog>
		<!-- 多人会议最小化展示弹窗 -->
		<MinumVideoDialog
			v-if="miniumVideoDialogShow"
			:trackData="multiCurTrackData"
			:localCameraStatus="multiLocalCameraStatus"
			:localMicrophoneStatus="multiLocalMicroStatus"
			@resetMultiDialog="resetMultiDialog"
			@changeLocalMicrophone="changeLocalMicrophone"
			@changeLocalCamera="changeLocalCamera"
			v-drag
		></MinumVideoDialog>
		<!-- 加入会议弹窗 -->
		<JoinMeetingDialog
			v-if="joinMeetingShow"
			:joinMeetingData="joinMeetingData"
			@closeDialog="closeJoinDialog"
			@joinMeeting="joinMeetingByRoomNum"
		></JoinMeetingDialog>
	</div>
	<div v-else>
		<router-view />
	</div>
</template>

<script>
import { Message } from 'element-ui'
import HeaderMain from '@/components/leader/common/headerMain.vue'
import HeaderMain2 from '@/components/leader/common/headerMain2.vue'
import myRobotAuto from '@/components/robot/myRobotAuto.vue'
import { isMobile } from '@/utils/utils'
// import cesiumMap from '@/components/leader/cesiumMap/Cesium3dMap.vue'
// import leaderMiddle from '@/components/leader/common/leaderMiddle.vue'
// import HeaderMain from './components/leader/common/headerMain'
// import Map from './views/map/CvFanMap'
// import BottomMain from './components/leader/common/bottomMain'
// import rtc from '@/rhtx/core/index'
import { closeMeet, rhtxLogin } from '@/api/hs/hs.api.js'
import { queryOrgStreetTree } from '@/api/hs/hs.tsgz.js'
import { getToken, getToken1 } from '@/api/njzl/hs.api.js'
import { bjnjUrl } from '@/utils/leader/const'
import { cscpCurrentUserDetails, getDzToken, sendNotice, getRoom } from '@/api/bjnj/zhdd.js'
import autofit from '@/utils/autofit.js'
import LiveClient from './utils/livekit/live-client-esm'
import PointToPoint from '@/views/leader/components/service/pointToPoint.vue'
import InviteBoard from '@/views/leader/components/service/inviteBoard.vue'
import MultipleMeeting from '@/views/leader/components/service/multipleMeeting.vue'
// import AddressBook from '@/views/leader/components/service/addressBook.vue'
import MeetingEnterDialog from './views/leader/components/service/meetingEnterDialog.vue'
import ReadyMeetingDialog from '@/views/leader/components/service/readyMeetingDialog.vue'
import MinumVideoDialog from '@/views/leader/components/service/miniumVideoDialog.vue'
import JoinMeetingDialog from '@/views/leader/components/service/joinMeetingDialog.vue'
import { WebSocketManager } from '@/views/leader/components/service/tool/index.js'
import rtc from '@/components/mixins/rtc.js'
import TRTC from 'trtc-sdk-v5'
import LibGenerateTestUserSig from '@/utils/lib-generate-test-usersig.min.js'
export default {
	name: 'App',
	// mixins: [rtc],
	components: {
		HeaderMain,
		HeaderMain2,
		myRobotAuto,
		PointToPoint,
		InviteBoard,
		MultipleMeeting,
		ReadyMeetingDialog,
		MinumVideoDialog,
		JoinMeetingDialog,
		MeetingEnterDialog,
	},
	data() {
		return {
			url: '',
			path: '',
			rhtxLoginName: 'njqhsjd_3000',
			rhtxLoginPsd: 'Jnq@zhzx',
			offline: false,
			tokenInterval: null,
			bgShow: true,
			bgShow4: false,
			// 视频会议相关
			liveClient: null,
			liveClient2: null,
			// userId: '',
			org: '1478649882954985474',
			// baseUrl: 'https://**************:7443',
			// baseUrl: 'https://dpzs.camtc.cn',
			baseUrl: 'http://*************/api/notice-websocket',
			// baseUrl: 'http://***********:8888',
			// 点对点通话组件
			pointMeetingShow: false,
			invitedMobile: '',
			pointInviteWay: 'mobile',
			// 被邀请弹窗
			inviteBoardShow: false,
			inviteBoardData: {
				roomNum: '',
				fromIdentity: '',
				fromOrg: '',
				fName: '',
			},
			// 多人视频会议弹窗
			multipleMeetingShow: false,
			// meetingRole: 'host',
			multipleMeetingData: {},
			launchInviteList: [],
			// 通讯录弹窗
			addressBookShow: false,
			// 指挥调度地图areaId
			areaId: '',
			allTracks: null,
			// 发起会议准备弹窗
			readyMeetingShow: false,
			launchType: 'launch',
			cameraStatus: true,
			microPhoneStatus: true,
			// 多人会议最小化弹窗
			miniumVideoDialogShow: false,
			multiLocalCameraStatus: true,
			multiLocalMicroStatus: true,
			multiCurTrackData: null,
			// 加入会议弹窗
			joinMeetingShow: false,
			joinMeetingData: {},
			roomId: 14251,
			sdkAppId: 1600089532,
			sdkSecretKey: '3a6edb36029177424289091eb1d729befde4297a6426c3d6f9e8df97ada4cb0b',
			userId: 'user_651265',
			userSig: null,
			wwww: false,
			roomName1: '',
			previewTimeInterval: null,
			isPCmeeting: false,
			hasOpenMeetingWindow: false,
		}
	},
	created() {
		// this.rhtxLoginFn()
		// this.queryOrgStreetTreeFn() // 区视频会商人员
		// 初始化总线间事件监听
		this.isPCmeeting = location.href.indexOf('PCmeeting') != -1 ? true : false
		window.addEventListener('storage', (event) => {
			if (event.key === 'PCmeetingClosed') {
				console.log('窗口已关闭')
				this.hasOpenMeetingWindow = false
				localStorage.removeItem('PCmeetingClosed')
				this.initLiveClient()
			}
		})
	},
	mounted() {
		this.wwww = isMobile
		if (!isMobile && window.location.href.indexOf('invite') == -1) {
			autofit.init(
				{
					designHeight: 1080,
					designWidth: 1920,
					renderDom: '#mengCheng',
					resize: true,
					ignore: ['.map'],
					// 是否等比例适配大屏
					isEqal: false,
				},
				true,
			)
			document.querySelector('body').setAttribute('style', 'background: #000;')
			// console.log('$route.path', this.$route.path)
			// 页面刷新，关闭机器人连接
			/* window.addEventListener('beforeunload', (e) => {
			// this.$refs.myRobotAutoRef.closeYy()
			this.closeMeet()
			this.removeAllListener()
			this.resetLiveClient()
		}) */
			if (this.liveClient) {
				this.liveClient.close()
			}
			if (this.liveClient2) {
				this.liveClient2.close()
			}
			this.timingRefreshToken()
			this.refreshToken()
			let url = window.location.href
			// 获取参数部分
			let params = url.split('?')[1]
			// 将参数部分转换为对象
			let paramsObj = {}
			if (params) {
				let paramsArr = params.split('&')
				for (let i = 0; i < paramsArr.length; i++) {
					let param = paramsArr[i].split('=')
					paramsObj[param[0]] = param[1]
				}
			}
			// 获取指定参数的值
			let paramValue = paramsObj['token']
			if (paramValue) {
				localStorage.setItem('token', decodeURI(paramValue))
			}
			console.log('token2222', localStorage.token)
			if (!localStorage.token) {
				// window.open('https://portal.camtc.cn/home/<USER>')
				// window.location.href = bjnjUrl + '/api/oauth/jumpSceenOauth' // 跳转至登录页，发布时解掉注释
			} else {
				this.getScreenUserData().then((res) => {
					console.log('大屏用户信息', res.data)
					this.userId = res.data.mobile
					this.$store.commit('setScreenUsername', {
						username: res.data.name,
					})
					this.$store.commit('setScreenUserMobile', {
						mobile: res.data.mobile,
					})
					this.initLiveClient()
					// console.log('liveClient', this.liveClient);
				})
			}
			this.initBusEvent()
		} else {
			// document.querySelector('body').setAttribute('style', 'height: auto;')
			document.querySelector('body').setAttribute('style', 'width: auto;height: auto;')
		}
		this.$aegis?.reportEvent({
			name: 'loaded',
			ext1: 'loaded-success',
			ext2: this.$DEMOKEY,
		})
	},
	destroyed() {
		// 页面刷新，关闭机器人连接
		/* window.removeEventListener('beforeunload', (e) => {
			// this.$refs.myRobotAutoRef.closeYy()
			this.closeMeet()
		})
		this.removeAllListener()
		this.resetLiveClient() */
		clearInterval(this.tokenInterval)
	},
	methods: {
		// 视频会议相关
		// liveClient初始化
		async initLiveClient() {
			// this.liveClient = new WebSocket('wss://dpzs.camtc.cn/ws/notice-websocket/' + this.$store.state.mobile)
			// this.liveClient = new WebSocket('ws://*************:9019/notice-websocket/' + this.$store.state.mobile)
			// this.initClientEvent()

			// this.previewTimeInterval = setInterval(() => {
			// 	console.log('662626')
			// 	this.liveClient.send('')
			// }, 20000)
			// 使用示例
			this.liveClient = new WebSocketManager('wss://dpzs.camtc.cn/ws/notice-websocket/' + this.$store.state.mobile, {
				onOpen: (event) => {
					console.log('连接已建立，实现监控别人邀请')
					// 连接成功后可以发送初始消息
					// wsManager.send('Hello Server!')
				},
				onMessage: (event) => {
					console.log('收到消息:', event)
					if (event.data) {
						let message = JSON.parse(event.data).message
						let id = message.split('+-')[0]
						let name = message.split('+-')[1]
						let Fname = message.split('+-')[2]
						this.inviteBoardData.roomNum = id
						this.inviteBoardData.fromIdentity = name
						this.inviteBoardData.fName = Fname
						this.inviteBoardShow = true
					}
				},
				onClose: (event) => {
					console.log('连接已断开，实现监控别人邀请')
				},
				onError: (error) => {
					console.error('WebSocket error:', error)
				},
				reconnectInterval: 1000,
				maxReconnectAttempts: 10,
				heartbeatInterval: 20000,
				heartbeatMsg: '',
			})
		},
		sendNotice(data) {
			this.liveClient.send(data)
		},
		// 初始化liveClient事件监听
		initClientEvent() {
			if (!this.liveClient) {
				console.log('客户端未初始化')
				return
			}
			// // ws链接建立成功后触发
			// this.liveClient.on('socketOpen', () => {
			// 	console.log('链接建立成功')
			// })
			// // 接收到其他用户的会议邀请
			// this.liveClient.on('receiveInviteSuccess', (e) => {
			// 	// 来源identity不为自己
			// 	if (e.fromIdentity !== this.userId) {
			// 		this.inviteBoardData.roomNum = e.roomNum
			// 		this.inviteBoardData.fromIdentity = e.fromIdentity
			// 		this.inviteBoardData.fromOrg = e.fromOrg
			// 		this.inviteBoardShow = true
			// 	}
			// })
			// 连接打开时触发
			this.liveClient.onopen = (event) => {
				console.log('连接已建立，实现监控别人邀请')
				// 发送消息
			}
			// 接收消息时触发
			this.liveClient.onmessage = (event) => {
				console.log('收到消息:', event)
				if (event.data) {
					let message = JSON.parse(event.data).message
					let id = message.split('+-')[0]
					let name = message.split('+-')[1]
					let Fname = message.split('+-')[2]
					this.inviteBoardData.roomNum = id
					this.inviteBoardData.fromIdentity = name
					this.inviteBoardData.fName = Fname
					this.inviteBoardShow = true
				}
			}
			this.liveClient.onclose = (event) => {
				console.log('连接已断开，实现监控别人邀请')
				this.liveClient = null
			}
		},
		// 移除全部事件监听
		removeAllListener() {
			if (!this.liveClient) {
				console.log('客户端未初始化')
				return
			}
			this.liveClient.removeAllListeners()
		},
		// 重置liveClient
		resetLiveClient() {
			this.liveClient.close()
			window['liveClient'] = this.liveClient = null
			this.liveClient2 = null
		},
		// 大屏用户发起会议
		launchMeeting(e) {
			this.launchInviteList = []
			this.launchInviteList = e
			this.lauchType = 'launch'
			console.log('发起会议', this.launchInviteList)
			this.readyMeetingShow = true
		},
		// 大屏用户快速发起
		quickLaunchMeeting(e) {
			this.launchInviteList = []
			this.launchInviteList = e
			this.launchType = 'quickLaunch'
			console.log('快速发起会议', this.launchInviteList)
			this.readyMeetingShow = true
		},
		// reportSuccessEvent(name) {
		// 	const ext3 = name === 'enterRoom' ? this.sdkAppId : 0
		// 	this.$aegis?.reportEvent({
		// 		name,
		// 		ext1: `${name}-success`,
		// 		ext2: this.$DEMOKEY,
		// 		ext3,
		// 	})
		// },
		// reportFailedEvent(name, error, type = 'rtc') {
		// 	this.$aegis?.reportEvent({
		// 		name,
		// 		ext1: `${name}-failed#${this.roomId}*${type === 'share' ? this.shareUserId : this.userId}*${error.message}`,
		// 		ext2: this.$DEMOKEY,
		// 		ext3: 0,
		// 	})
		// },
		// 发起会议弹窗发起会议
		async startMeeting(e) {
			console.log('开始快速会议的麦克风和视频参数：', e, 'eeeeeeeeeeeeeeee', isMobile)

			this.$confirm('是否选择新窗口进入会议以增强体验?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'primary',
			})
				.then(() => {
					try {
						if (this.hasOpenMeetingWindow) {
							this.$message.warning('请先关闭已打开的会议室浏览器窗口！')
							return
						}
						sessionStorage.setItem('launchInviteList', JSON.stringify(this.launchInviteList))
						let { cameraStatus, microphoneStatus, meetingName, allTracks } = e
						window.open(
							location.origin +
								'/#/PCmeeting?c=' +
								cameraStatus +
								'&m=' +
								microphoneStatus +
								'&N=' +
								meetingName +
								'&I=0&J=0' +
								'&U=' +
								this.$store.state.username +
								'&T=' +
								this.$store.state.mobile,
						)

						this.hasOpenMeetingWindow = true
						if (this.liveClient) {
							this.liveClient.close()
							this.liveClient = null
							clearInterval(this.previewTimeInterval)
						}
						this.addressBookShow = false
					} catch {
						console.log('出现了错误情况')
					}
				})
				.catch((err) => {
					let { cameraStatus, microphoneStatus, meetingName, allTracks } = e
					console.log('取消',meetingName)
					this.allTracks = allTracks
					this.cameraStatus = cameraStatus
					this.microPhoneStatus = microphoneStatus
					this.roomName1 = meetingName
					this.multipleMeetingData.roomName = meetingName
					this.addressBookShow = false
					this.$bus.emit('LaunchMeetingDialogClose')
					this.multipleMeetingShow = true
				})
		},
		closeJoinDialog() {
			this.joinMeetingShow = false
			if (this.liveClient2) {
				console.log('链接主动断开')
				this.liveClient2.close()
			}
		},
		// 加入会议弹窗点击加入
		joinMeetingByRoomNum(e) {
			let { cameraStatus, microphoneStatus, meetingNum } = e
			this.cameraStatus = cameraStatus
			this.microPhoneStatus = microphoneStatus
			this.multipleMeetingData.roomNum = meetingNum
			this.multipleMeetingData.isJoin = true
			this.addressBookShow = false
			this.$bus.emit('LaunchMeetingDialogClose')
			this.launchInviteList = []
			// this.meetingRole = 'user'
			if (this.liveClient2) {
				this.$message({
					message: '请等待管理员同意入会...',
					type: 'success',
				})
			} else
				getRoom(meetingNum).then((res) => {
					this.roomName1 = res.data.roomName

					if (res.code != 0 || !res.data || res.data.roomStatus == 2) {
						this.$message({
							message: '会议不存在或已结束！请确认会议号是否正确！',
							type: 'error',
						})
						return
					}

					this.managerMobile = res.data.mobile
					// this.liveClient2 = new WebSocket(
					// 	'wss://dpzs.camtc.cn/ws/meeting-websocket/' + meetingNum + '?phone=' + this.$store.state.mobile,
					// 	// 'ws://*************:9019/meeting-websocket/' + meetingNum + '?phone=' + this.$store.state.mobile,
					// )
					// this.initClientEvent2()
					this.liveClient2 = new WebSocketManager(
						'wss://dpzs.camtc.cn/ws/meeting-websocket/' + meetingNum + '?phone=' + this.$store.state.mobile,
						{
							onOpen: (event) => {
								console.log('会议室连接已建立')
								let data = {
									type: '1', //1申请入会，2被解散
									message: this.$store.state.mobile + '_' + this.$store.state.username,
									sendUser: this.$store.state.mobile,
									acceptUser: this.managerMobile,
								}
								this.liveClient2.send(JSON.stringify(data))
							},
							onMessage: (event) => {
								console.log('收到消息:', event)
								if (event.data.indexOf('disband') > -1) {
									this.$refs.multipleMeeting.outroom()
									this.$message({
										message: '房主解散了会议',
										type: 'warning',
									})
								} else if (event.data == 'agree') {
									this.$confirm('是否选择新窗口进入会议以增强体验?', '提示', {
										confirmButtonText: '确定',
										cancelButtonText: '取消',
										type: 'primary',
									})
										.then(() => {
											if (this.hasOpenMeetingWindow) {
												this.$message.warning('请先关闭已打开的会议室浏览器窗口！')
												return
											}
											this.joinMeetingShow = false
											window.open(
												location.origin +
													'/#/PCmeeting?c=' +
													this.cameraStatus +
													'&m=' +
													this.microphoneStatus +
													'&I=0&J=1' +
													'&Id=' +
													this.multipleMeetingData.roomNum +
													'&U=' +
													this.$store.state.username +
													'&T=' +
													this.$store.state.mobile,
											)
										})
										.catch((e) => {
											this.joinMeetingShow = false
											this.multipleMeetingShow = true
										})
								} else if (event.data == 'refuse') {
									this.$message({
										message: '管理员拒绝了您的入会请求！',
										type: 'warning',
									})
									this.joinMeetingShow = false
									this.liveClient2.close()
								}
							},
							onClose: (event) => {
								console.log('会议室连接已断开')
							},
							onError: (error) => {
								console.error('WebSocket error:', error)
							},
							reconnectInterval: 1000,
							maxReconnectAttempts: 10,
							heartbeatInterval: 20000,
							heartbeatMsg: '',
						},
					)

					this.$message({
						message: '请等待管理员同意入会...',
						type: 'success',
					})
				})
			// this.multipleMeetingShow = true
		},
		initClientEvent2() {
			if (!this.liveClient2) {
				console.log('客户端未初始化')
				return
			}
			// 连接打开时触发
			this.liveClient2.onopen = (event) => {
				console.log('连接已建立')
				let data = {
					type: '1', //1申请入会，2被解散
					message: this.$store.state.mobile + '_' + this.$store.state.username,
					sendUser: this.$store.state.mobile,
					acceptUser: this.managerMobile,
				}
				this.liveClient2.send(JSON.stringify(data))
			}
			// 接收消息时触发
			this.liveClient2.onmessage = (event) => {
				console.log('收到消息:', event)
				if (event.data.indexOf('disband') > -1) {
					this.$refs.multipleMeeting.outroom()
					this.$message({
						message: '房主解散了会议',
						type: 'warning',
					})
				} else if (event.data == 'agree') {
					// this.$confirm('是否选择新窗口进入会议以增强体验?', '提示', {
					// 	confirmButtonText: '确定',
					// 	cancelButtonText: '取消',
					// 	type: 'primary',
					// })
					// 	.then(() => {
					// 		if (this.hasOpenMeetingWindow) {
					// 			this.$message.warning('请先关闭已打开的会议室浏览器窗口！')
					// 			return
					// 		}
					// 		this.joinMeetingShow = false
					// 		window.open(
					// 			location.origin +
					// 				'/#/PCmeeting?c=' +
					// 				this.cameraStatus +
					// 				'&m=' +
					// 				this.microphoneStatus +
					// 				'&I=0&J=1' +
					// 				'&Id=' +
					// 				this.multipleMeetingData.roomNum +
					// 				'&U=' +
					// 				this.$store.state.username +
					// 				'&T=' +
					// 				this.$store.state.mobile,
					// 		)
					// 	})
					// 	.catch((e) => {
					this.joinMeetingShow = false
					this.multipleMeetingShow = true
					// })
				} else if (event.data == 'refuse') {
					this.$message({
						message: '管理员拒绝了您的入会请求！',
						type: 'warning',
					})
					this.joinMeetingShow = false
					this.liveClient2.close()
				}
			}
			this.liveClient2.onclose = (event) => {
				this.liveClient2 = null
			}
		},
		launchPointToPoint(e) {
			this.addressBookShow = false
			this.$bus.emit('LaunchMeetingDialogClose')
			this.invitedMobile = e.mobile
			this.pointInviteWay = e.type
			this.pointMeetingShow = true
		},
		minumMultiMeeting(e) {
			console.log('最小化多人会议窗口', e)
			this.multiCurTrackData = e
			this.multiLocalCameraStatus = e.cameraStatus
			this.multiLocalMicroStatus = e.microphoneStatus
			this.miniumVideoDialogShow = true
		},
		changeLocalMicrophone(val) {
			this.multiLocalMicroStatus = val
			this.$refs.multipleMeeting.changeLocalMicroStatus()
		},
		changeLocalCamera(val) {
			this.multiLocalCameraStatus = val
			this.$refs.multipleMeeting.changeLocalCameraStatus()
		},
		multiMeetingClose() {
			this.launchInviteList = []
			if (this.liveClient2) {
				console.log('链接主动断开')
				this.liveClient2.close()
			}
			this.miniumVideoDialogShow = false
		},
		resetMultiDialog() {
			this.miniumVideoDialogShow = false
			this.$refs['multipleMeeting'].resetDialog()
		},
		// 初始化总线事件监听
		initBusEvent() {
			// 点对点会议邀请发送
			this.$bus.on('launchPointMeeting', (e) => {
				console.log('点对点会议邀请发送', e)
				this.invitedMobile = e
				this.pointInviteWay = 'mobile'
				this.pointMeetingShow = true
			})
			this.$bus.on('PCActiveInvite', (e) => {
				this.liveClient.send(e)
			})
			// 打开发起会议弹窗事件
			this.$bus.on('LaunchMeetingDialogOpen', (e) => {
				if (this.hasOpenMeetingWindow) {
					this.$message.warning('请先关闭已打开的会议室浏览器窗口！')
					return
				}
				TRTC.isSupported().then((checkResult) => {
					const { isBrowserSupported, isWebRTCSupported } = checkResult.detail
					if (!checkResult.result || !isBrowserSupported || !isWebRTCSupported) {
						// SDK 不支持当前浏览器，引导用户使用最新版的 Chrome 浏览器。
						this.$confirm(
							'当前浏览器不支持视频会商功能，建议使用最新版的 Chrome 浏览器！下载地址：https://www.google.cn/intl/zh-CN/chrome/',
							'提示',
							{
								// confirmButtonText: '确定',
								showConfirmButton: false,
								cancelButtonText: '好的',
								type: 'warning',
							},
						).then(() => {})
					} else {
						this.areaId = e
						this.$store.commit('setAreaId', {
							areaId: this.areaId,
						})
						console.log('指挥调度areaId', this.areaId)
						this.addressBookShow = true
					}
				})
			})
		},
		openJoinMeetingDialog(e) {
			if (e) {
				this.joinMeetingData = e
			} else {
				this.joinMeetingData = {}
			}
			this.joinMeetingShow = true
		},
		// 获取大屏用户信息
		getScreenUserData() {
			return cscpCurrentUserDetails()
		},
		refuseInvite(e) {
			// if (!this.liveClient) {
			// 	console.log('客户端未初始化')
			// 	return
			// }
			// this.liveClient.refuseInvite(e.roomNum, e.fromIdentity, e.fromOrg)
			this.inviteBoardShow = false
		},
		acceptInvite(e) {
			if (!this.liveClient) {
				console.log('客户端未初始化')
				return
			}
			this.multipleMeetingData.isInvite = true
			this.multipleMeetingData.roomId = e.roomNum
			this.multipleMeetingData.roomName = e.fName

			// this.$confirm('是否选择新窗口进入会议以增强体验?', '提示', {
			// 	confirmButtonText: '确定',
			// 	cancelButtonText: '取消',
			// 	type: 'primary',
			// })
			// 	.then(() => {
			// 		if (this.hasOpenMeetingWindow) {
			// 			this.$message.warning('请先关闭已打开的会议室浏览器窗口！')
			// 			return
			// 		}
			// 		this.inviteBoardShow = false
			// 		window.open(
			// 			location.origin +
			// 				'/#/PCmeeting?c=false&m=false' +
			// 				'&N=' +
			// 				e.fName +
			// 				'&I=1&J=0' +
			// 				'&Id=' +
			// 				e.roomNum +
			// 				'&U=' +
			// 				this.$store.state.username +
			// 				'&T=' +
			// 				this.$store.state.mobile,
			// 		)
			// 	})
			// 	.catch((e) => {
			this.inviteBoardShow = false
			this.multipleMeetingShow = true
			// })
		},
		// 非视频会议相关
		rightYc(e) {
			console.log(e)
		},
		timingRefreshToken() {
			let sessionStorage = window.sessionStorage
			// 定时每1min刷新token，如果时间间隔为两小时，停止刷新token
			if (!sessionStorage.lastHttpRequestTime) {
				sessionStorage.lastHttpRequestTime = +new Date()
			}
			this.tokenInterval = setInterval(() => {
				// if ((+new Date()) - sessionStorage.lastHttpRequestTime > 30 * 60 * 1000) {
				//   clearInterval(this.tokenInterval)
				//   // window.open(loginUrl, '_self')
				// } else {
				//   this.refreshToken()
				// }
				this.refreshToken()
			}, 30 * 60000)
		},
		refreshToken() {
			getDzToken().then((res) => {
				let token = res.data.access_token
				window.sessionStorage.setItem('token', 'Bearer ' + token)
			})
		},
		// refreshToken () {
		//   getToken1().then(res => {
		//     let token = res.access_token
		//     window.sessionStorage.setItem('token', 'Bearer ' + token)
		//     // window.localStorage['token'] = String(token)
		//     let url = window.location.href
		//     // 获取参数部分
		//     let params = url.split('?')[1]
		//     // 将参数部分转换为对象
		//     let paramsObj = {}
		//     if (params) {
		//       let paramsArr = params.split('&')
		//       for (let i = 0; i < paramsArr.length; i++) {
		//         let param = paramsArr[i].split('=')
		//         paramsObj[param[0]] = param[1]
		//       }
		//     }
		//     // 获取指定参数的值
		//     let paramValue = paramsObj['token']
		//     if (paramValue) {
		//       localStorage.setItem("token", decodeURI(paramValue))
		//     }
		//     console.log(localStorage.token)
		//   })
		// },
		// async rhtxLoginFn () {
		//   let res = await rhtxLogin()
		//   // console.log('rhtxLogin', res)
		//   if (res.code == '200') {
		//     this.rhtxLoginName = res.result[0].syskey
		//     this.rhtxLoginPsd = res.result[0].sysvalue

		//     const errorCodeMap = {
		//       EXIST_LOGIN: 'b0007',
		//     }

		//     rtc
		//       .regiest({
		//         account: this.rhtxLoginName,
		//         password: this.rhtxLoginPsd,
		//       })
		//       .catch(({ res_code, data }) => {
		//         switch (res_code) {
		//           case errorCodeMap.EXIST_LOGIN:
		//             this.$confirm('融合通信的账号已在其他地方登陆，确认强制登陆吗？')
		//               .then(() => {
		//                 rtc.regiest({
		//                   account: this.rhtxLoginName,
		//                   password: this.rhtxLoginPsd,
		//                   last_token: data.config.last_token,
		//                 })
		//               })
		//               .catch(() => {
		//                 console.log('取消')
		//               })
		//             break
		//         }
		//       })
		//     // rtc.ws.on('*', {
		//     //   offline: (data) => {
		//     //     console.log('rhtx设备不在线', data)
		//     //     console.log('rhtx账号在其他地方登陆')
		//     //     Message({
		//     //       showClose: true,
		//     //       message: '融合通信在其他地方登陆！',
		//     //       type: 'error',
		//     //       duration: 0,
		//     //     })
		//     //     // this.offline = true
		//     //   },
		//     // })
		//   }
		// },
		async closeMeet() {
			window.sessionStorage.setItem('oldUers', [])
			if (window.sessionStorage.getItem('tempMeetVoiceNum')) {
				let voiceRes = await closeMeet(window.sessionStorage.getItem('tempMeetVoiceNum'))
				if (voiceRes.res_code == '0') {
					window.sessionStorage.setItem('tempMeetVoiceNum', '')
				}
			}
			if (window.sessionStorage.getItem('tempMeetVideoNum')) {
				let videoRes = await closeMeet(window.sessionStorage.getItem('tempMeetVideoNum'))
				if (videoRes.res_code == '0') {
					window.sessionStorage.setItem('tempMeetVideoNum', '')
				}
			}
		},
		removePoints_Gj() {
			this.$refs.CesiumMapRef.removeAllLayer() //移除所有点位
			this.$refs.CesiumMapRef.removeTrack() //移除轨迹
		},
		bubble(data) {
			console.log('data', data)
			this.$store.commit('invokeSHZLClickPoint', data)
		},
		async queryOrgStreetTreeFn() {
			const res = await queryOrgStreetTree()
			if (res) {
				this.$store.commit('setQueryOrgStreetTreeData', res)
				// window.localStorage.setItem('queryOrgStreetTreeData',JSON.stringify(res))
			}
		},
		clearTracks() {
			this.allTracks.forEach((track) => {
				track.stop()
			})
		},
		getUrlParam(key) {
			const url = decodeURI(window.location.href.replace(/^[^?]*\?/, ''))
			const regexp = new RegExp(`(^|&)${key}=([^&#]*)(&|$|)`, 'i')
			const paramMatch = url.match(regexp)

			return paramMatch ? paramMatch[2] : null
		},
	},
	computed: {
		invokerightShow() {
			return this.$store.state.rightShow
		},
		invokerightShow4() {
			return this.$store.state.rightShow4
		},
		headerCurrentIndex() {
			return this.$store.state.currentIndex
		},
		// 【社会治理-人员力量】打点方法 全局触发器
		SHZLRyllAddPoints() {
			return this.$store.state.SHZLRyllAddPointsTrigger
		},
		// 【社会治理-轨迹】全局触发器
		SHZLRyllGj() {
			return this.$store.state.SHZLRyllSHZLGjTrigger
		},
		// 【社会治理-雪亮工程】打点方法 全局触发器
		SHZLXlgcAddPoints() {
			return this.$store.state.SHZLXlgcAddPointsTrigger
		},
		// 【社会治理-城市事件】打点方法 全局触发器
		SHZLCssjAddPoints() {
			return this.$store.state.SHZLCssjAddPointsTrigger
		},
		// 【社会治理-事件信息】打点方法 全局触发器
		SHZLSjxx1AddPoints() {
			return this.$store.state.SHZLSjxx1AddPointsTrigger
		},
		// 【社会治理-ai】打点方法 全局触发器
		SHZLaiAddPoints() {
			return this.$store.state.SHZLaiAddPointsTrigger
		},
		// 【点位轨迹移除】全局触发器
		invokeRemovePoints() {
			return this.$store.state.removePoints_GjTrigger
		},
	},
	watch: {
		$route(to, from) {
			console.log('to.path', to.path)
			this.url = to.path
			console.log('localStorage.token', this.$route.path !== '/zt' && !localStorage.token)
			// if (this.$route.path !== '/zt' && !localStorage.token) {
			//   // window.open('https://portal.camtc.cn/home/<USER>')
			//   window.location.href = bjnjUrl + '/api/oauth/jumpSceenOauth';
			// }
		},
		invokerightShow(newValue) {
			if (this.$store.state.currentIndex == 1) {
				this.bgShow = false
			} else {
				this.bgShow = newValue
			}
		},
		invokerightShow4(newValue) {
			this.bgShow4 = newValue
			//关闭右侧的内容
			// this.bgShow = false
			if (this.$store.state.currentIndex == 1) {
				// this.$store.commit('invokerightShow', false)
				this.bgShow = false
			} else {
				this.bgShow = !newValue
			}
		},

		headerCurrentIndex(newValue) {
			if (newValue == 1) {
				this.bgShow = false
			} else if (newValue == 0 || newValue == 2) {
				this.$store.commit('invokerightShow', true)
				// this.bgShow = true
			}
		},

		SHZLRyllAddPoints() {
			// console.log('this.$store.getters.getSHZLRyllAddParam', this.$store.getters.getSHZLRyllAddParam)
			this.removePoints_Gj()
			this.$refs.CesiumMapRef.loadPoints1(this.$store.getters.getSHZLRyllAddParam.data, this.$store.getters.getSHZLRyllAddParam.marker)
		},
		SHZLRyllGj() {
			this.$refs.CesiumMapRef.loadTrack()
		},
		SHZLXlgcAddPoints() {
			this.removePoints_Gj()
			this.$refs.CesiumMapRef.loadPoints1(this.$store.getters.getSHZLXlgcAddParam.data, this.$store.getters.getSHZLXlgcAddParam.marker)
		},
		SHZLCssjAddPoints() {
			this.removePoints_Gj()
			this.$refs.CesiumMapRef.loadPoints1(this.$store.getters.getSHZLCssjAddParam.data, this.$store.getters.getSHZLCssjAddParam.marker)
		},
		SHZLSjxx1AddPoints() {
			this.removePoints_Gj()
			this.$refs.CesiumMapRef.loadPoints1(
				this.$store.getters.getSHZLSjxx1AddParam.data,
				this.$store.getters.getSHZLSjxx1AddParam.marker,
			)
		},
		SHZLaiAddPoints() {
			this.removePoints_Gj()
			this.$refs.CesiumMapRef.loadPoints1(this.$store.getters.getSHZLaiAddParam.data, this.$store.getters.getSHZLaiAddParam.marker)
		},
		invokeRemovePoints() {
			this.removePoints_Gj()
		},
		multipleMeetingShow(newVal) {
			if (!newVal) {
				this.multipleMeetingData = {}
				this.launchInviteList = []
			}
		},
	},
}
</script>

<style lang="scss">
#mengCheng {
	width: 1920px;
	height: 1080px;
	position: relative;
	transform-origin: 0 0;
	// height: 67.5rem;
	font-family: Avenir, Helvetica, Arial, sans-serif;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	text-align: center;
	color: #2c3e50;
	// margin-top: 540px;
	overflow: hidden;

	.fade-enter,
	.fade-leave-to {
		opacity: 0;
	}

	.fade-enter-active,
	.fade-leave-active {
		transition: all 0.5s linear;
	}

	.left_bg1 {
		// width: 1071px;
		width: 538px;
		height: 1080px;
		position: absolute;
		left: 0;
		top: 0;
		background: url(~@/assets/bjnj/panel-bg-left.png) no-repeat;
		background-size: 100% 100%;
		z-index: 1001;
	}
	.right_bg1 {
		// width: 1071px;
		width: 538px;
		height: 1080px;
		position: absolute;
		right: 0;
		top: 0;
		background: url(~@/assets/bjnj/panel-bg-right.png) no-repeat;
		background-size: 100% 100%;
		z-index: 1001;
	}
	// .map_box {
	//   position: absolute;
	//   width: 1920px;
	//   height: 928px;
	//   top: 154px;
	//   left: 0;
	//   z-index: 100;
	// }
}
</style>
