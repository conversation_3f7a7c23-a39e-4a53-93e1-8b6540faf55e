import { RTCPublisher, RTCPlayer } from './rtc.base';
import { getCallDeviceType } from '../util/index.js';
import { DEFALT_BIT_RANGE } from '../dict/index';
import { useStreamAction } from './stream';

//推流
function startPublish({ url, type, mediaConstraints, waterMarker }) {
  return new Promise((resolve, reject) => {
    const pcInfo = new RTCPublisher();
    pcInfo.pc.onicegatheringstatechange = (event) => {
      if (pcInfo.pc.iceGatheringState === 'complete') {
        console.log('ok');
      }
    };
    //推流地址
    pcInfo
      .publish({ url, type, mediaConstraints, waterMarker })
      .catch((reason) => {
        reject(reason);
        console.error(reason);
        pcInfo.close();
      });

    //推流成功后获取本地流
    pcInfo.onaddstream = ({ stream }) => {
      //获取流之后在播流
      const streamAction = useStreamAction(stream, pcInfo);
      streamAction.changeBW(DEFALT_BIT_RANGE); //改变视频码率
      Object.assign(pcInfo, { stream }, streamAction);
      resolve(pcInfo);
    };
    // return pcInfo;
  });
}

//播流
function startPlay({ url, call_type }) {
  return new Promise((resolve, reject) => {
    const callOption = getCallDeviceType(call_type);
    const pcInfo = new RTCPlayer();
    pcInfo.play({ url, ...callOption }).catch(function (reason) {
      console.error(reason);
      pcInfo.close();
      reject(reason);
    });

    //推流成功后获取本地流
    pcInfo.onaddstream = ({ stream }) => {
      //获取流之后在播流
      const streamAction = useStreamAction(stream, pcInfo);
      Object.assign(pcInfo, { stream }, streamAction);
      resolve(pcInfo);
    };
  });
}

export { startPublish, startPlay };
