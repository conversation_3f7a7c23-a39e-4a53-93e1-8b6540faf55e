/*
 * @Description: 雷达扫描效果封装
 * @Author: likang
 * @LastEditors: likang
 */

export default class RadarScanner {
  constructor(map, gcoord) {
    this.map = map
    this.gcoord = gcoord
    this.RadarLineLayer = null
    this.offAngle = 1
    this.offAngTotal = 45
    this.stareAngle = 0
    this.endAngle = 0
    this.Timer = null
    this.radius = 1
    this.circle = null
  }

  initRadarLayer(point, radius) {
    this.radius = radius
    this.RadarLineLayer = new L.LayerGroup().addTo(this.map)
    this.addRadarLayer(point)
    let p0 = this.gcoord.transform(point, this.gcoord.WGS84, this.gcoord.WebMercator)
    this.scan(p0)
  }

  scan(p0) {
    this.stareAngle += 10
    if (this.stareAngle - this.endAngle > this.offAngTotal) {
      this.endAngle = this.stareAngle - this.offAngTotal
    }
    clearTimeout(this.Timer)
    if (this.RadarLineLayer) this.RadarLineLayer.clearLayers()

    for (let i = this.endAngle; i < this.stareAngle; i += this.offAngle) {
      let p = this.getPoint(p0, i)
      let line = this.getLine(p0, p)
      this.RadarLineLayer.addLayer(line)
    }
    this.Timer = setTimeout(() => {
      this.scan(p0)
    }, 250)
  }

  getPoint(center, angle) {
    let radius_ = this.radius * 1.18
    let sin = Math.sin((angle * Math.PI) / 180)
    let cos = Math.cos((angle * Math.PI) / 180)
    let x = center[0] + radius_ * sin
    let y = center[1] + radius_ * cos
    return [x, y]
  }

  getLine(p1, p2) {
    let p1_ = this.gcoord.transform(p1, this.gcoord.WebMercator, this.gcoord.WGS84)
    let p2_ = this.gcoord.transform(p2, this.gcoord.WebMercator, this.gcoord.WGS84)
    const polyline = L.polyline([p1_.reverse(), p2_.reverse()], {
      weight: 2,
      color: '#33ff33',
      fillOpacity: 0.5,
      opacity: 0.5
    })
    return polyline
  }

  addRadarLayer(p0) {
    this.circle = L.circle([p0[1], p0[0]], {
      color: 'green',
      fillColor: 'green',
      fillOpacity: 0.2,
      radius: this.radius
    }).addTo(this.map)
  }

  removeRadarLayer() {
    if (this.RadarLineLayer) {
      this.map.removeLayer(this.RadarLineLayer)
      this.RadarLineLayer = null
      clearTimeout(this.Timer)
    }
    if (this.circle) {
      this.map.removeLayer(this.circle)
      this.circle = null
    }
  }
}
