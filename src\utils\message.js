import { Message } from "element-ui";

export default class ShowMessage {
  constructor() {}
  success(options) {
    this.message('success', options)
  }
  error(options) {
    this.message('error', options)
  }
  warning(options) {
    this.message('warning', options)
  }
  info(options) {
    this.message('info', options)
  }
  message(type, options) {
    const messageDom = document.getElementsByClassName('el-message')[0]
    if(!messageDom) {
      Message[type](options)
    }
  }
}