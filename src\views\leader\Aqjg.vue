<template>
  <div class="leader_box">
    <div class="leader_sjts">
      <!-- <div class="left_bg"></div>
      <div class="right_bg"></div>-->
      <div class="left">
        <div class="left1">
          <BlockBox
            title="企业风险报告"
            subtitle="Enterprise Risk Reporting"
            class="box"
            :isListBtns="false"
            :blockHeight="299"
          >
            <!-- <EnterpriseRiskReporting :data="data1" @change="locate" /> -->
            <EnterpriseRiskReporting @change="locate" />
          </BlockBox>
          <BlockBox
            title="社会面小场所"
            subtitle="Social Small Places"
            class="box box1"
            :isListBtns="false"
            :blockHeight="300"
            :showMore="true"
            @handleShowMore="showMoreFn1(2)"
          >
            <PieChart3D v-if="chartData" type="1" :data="chartData" :options="chartDataOptions" />
          </BlockBox>
          <BlockBox
            title="文件通知"
            subtitle="File Notification"
            class="box"
            :isListBtns="false"
            :blockHeight="308"
            :showMore="true"
            @handleShowMore="showMoreFn1(3)"
          >
            <FileNotification @change="view" />
          </BlockBox>
        </div>
      </div>
      <div class="right">
        <div class="right1">
          <BlockBox
            title="视频监控"
            subtitle="Video surveillance"
            class="box box3"
            :showMore="true"
            :isListBtns="false"
            :blockHeight="310"
            @handleShowMore="showMoreFn1(4)"
          >
            <div class="cont">
              <VideoSurveillance :cameraId="cameraId1" v-if="cameraId1" class="video_" />
              <VideoSurveillance :cameraId="cameraId2" v-if="cameraId2" class="video_" />
              <VideoSurveillance :cameraId="cameraId3" v-if="cameraId3" class="video_" />
              <VideoSurveillance :cameraId="cameraId4" v-if="cameraId4" class="video_" />
            </div>
          </BlockBox>
          <BlockBox
            title="天气预警"
            subtitle="Monitoring and warning"
            class="box"
            :isListBtns="false"
            :blockHeight="330"
          >
            <MonitoringWarning />
          </BlockBox>
          <BlockBox
            title="政府预案统计"
            subtitle="Government contingency plan statistics"
            class="box"
            :isListBtns="false"
            :blockHeight="261"
          >
            <PieChart3D type="2" :data="chartData2" :options="chartData2Options" />
          </BlockBox>
        </div>
      </div>
    </div>
    <div class="map_box">
      <LeafletMap ref="leafletMap" @poiClick="showDialog" />
    </div>
    <!-- <leaderMiddle /> -->
    <LeaderFooter :btns="[]" />
    <!-- <EventDetail v-if="isShowEventDetail" @close="isShowEventDetail = false" /> -->
    <CountryPart :isShow="isShowCountryPart" @close="closeCountryPart" @check="checkTree" />
    <!-- <spjkPop v-if="isXlgcShow" @closeEmit="isXlgcShow = false" @moreXl="isXlgcShow1 = true" /> -->
    <!-- <szyyPop v-if="isXlgcShow1" @closeEmit="isXlgcShow1 = false" /> -->
    <wgyPop v-if="isWgllShow" @closeEmit="isWgllShow = false" />
    <dzzPop v-if="isdzzShow" @closeEmit="isdzzShow = false" />
    <xzqyPop v-if="isxzqyShow" @closeEmit="isxzqyShow = false" />
    <zwzxPop v-if="iszwzxShow" @closeEmit="iszwzxShow = false" />
    <zhzxPop v-if="iszhzxShow" @closeEmit="iszhzxShow = false" />
    <csglgdPop v-if="iscsglgdShow" @closeEmit="iscsglgdShow = false" />
    <shaqPop v-if="isshaqShow" @closeEmit="isshaqShow = false" />
    <sthbPop v-if="issthbShow" @closeEmit="issthbShow = false" />
    <whlyPop v-if="iswhlyShow" @closeEmit="iswhlyShow = false" />
    <yjfkPop v-if="isyjfkShow" @closeEmit="isyjfkShow = false" />
    <ggjtPop v-if="isggjtShow" @closeEmit="isggjtShow = false" />
    <zdqyPop v-if="iszdqyShow" @closeEmit="iszdqyShow = false" />
    <xxPop v-if="isxxShow" @closeEmit="isxxShow = false" />
    <wbPop v-if="iswbShow" @closeEmit="iswbShow = false" />
    <ylcsPop v-if="isylcsShow" @closeEmit="isylcsShow = false" />
    <hczPop v-if="ishczShow" @closeEmit="ishczShow = false" />
    <dyyPop v-if="isdyyShow" @closeEmit="isdyyShow = false" />
    <djylPop v-if="djylShow" @closeEmit="djylShow = false" />
    <bjxqPop v-if="bjxqShow" @closeEmit="bjxqShow = false" />
    <Area ref="area" v-show="showArea" :pop-area-info="popAreaInfo"></Area>
    <!-- 弹窗 -->
    <!-- 企业风险报告详情 -->
    <EnterpriseRiskReportDetails v-model="enterpriseRiskReportDetailsShow" />
    <!-- 应急预案 -->
    <EmergencyPlanDialog v-model="emergencyPlanDialogShow" />
    <!-- 社会面小场所更多 -->
    <SocialSmallPlace v-model="detailDialogShow" />
    <!-- 文件通知查看 -->
    <DocumentNotificationDetails v-model="documentNotificationDetailsShow" />
    <!-- 文件通知更多弹窗 -->
    <FileNotificationDialog v-model="fileNotificationDialogShow" />
    <!-- 基础信息 -->
    <BasicInformation
      v-model="basicInfomationShow"
      :title="basicInfomationTitle"
      :list="basicInfomationList"
      :btnShow="basicInfomationBtnShow"
      :btns="basicInfomationBtns"
      @handle="clickBasicInformationBtn"
    />
    <!-- 事件信息 -->
    <EventInfo v-model="eventDialogShow" @handle="eventInfoBtnClick" />
    <!-- 事件详情 -->
    <EventDetail v-model="eventDetailShow" />
    <!-- 事件统计 -->
    <EventStatistic v-model="eventStatisticShow" />
    <!-- 应急资源 -->
    <EmergencyResourcesDialog v-model="emergencyResourcesDialogShow" />
    <!-- 值班人员 -->
    <OfficeronDuty v-model="officeronDutyShow" />
    <!-- 网格员登录情况 -->
    <GridMemberLoginStatusDialog v-model="gridMemberLoginStatusDialogShow" />

    <!-- 侧边菜单 -->
    <ZhddAside :list="asideList" @marker="handleMenuClick" />
    <!-- 侧边二级菜单 -->
    <SurroundingResourcesMap
      v-model="showYjzyList"
      left="736px"
      bottom="470px"
      :list="asideLeveltwoMenu"
      @surEmit="chooseSource"
    />
    <!-- 视频会商 -->
    <sphsPop
      v-if="isXlgcShow1"
      @closeEmit="isXlgcShow1 = false"
      :leftTreeData="sphsData"
      :initCheckedPeo="initCheckedPeo"
    >
      <template v-slot:title1>视频会商</template>
      <template v-slot:title2>会议接入</template>
      <template v-slot:title3>重置</template>
      <template v-slot:title4>加入会议</template>
    </sphsPop>
    <!-- 视频监控 -->
    <spjkPop
      v-if="spjkShow"
      @closeEmit="spjkShow = false"
      @operate="taskDistributionShow = true"
    ></spjkPop>
    <!-- 通讯调度 -->
    <txddPop
      v-if="txddShow"
      @closeEmit="txddShow = false"
      :leftTreeData="sphsData"
      :initCheckedPeo="initCheckedPeo"
    ></txddPop>
  </div>
</template>

<script>
import testData from '@/assets/json/cyjjPoint'
import BlockBox from '@/components/leader/common/blockBox.vue'
import HeaderMain from '@/components/leader/common/headerMains'
import effectRankBarSwiper from './components/effectRankBar-swiper.vue'
import particle from './components/particle.vue'
// import EventDetail from './components/EventDetail.vue'
import CountryPart from './components/CountryPart.vue'
import leaderMiddle from '@/components/leader/common/leaderMiddle.vue'
import gdMap from '@/components/leader/gdMap/gdMap.vue'
import cesiumMap from '@/components/leader/cesiumMap/Cesium3dMap.vue'
import cssjPoint from '@/assets/json/csts/cssjPoint.json'
import spjkPoint from '@/assets/json/csts/spjkPoint.json'
import csbjPoint from '@/assets/json/csts/csbjPoint.json'
import ryllPoint from '@/assets/json/csts/ryllPoint.json'
import xzqhPoint from '@/assets/json/csts/xzqhPoint.json'
import dzzPoint from '@/assets/json/csts/dzzPoint.json'
import zwzxPoint from '@/assets/json/csts/zwzxPoint.json'
import zzzxPoint from '@/assets/json/csts/zzzxPoint.json'
import zdqyPoint from '@/assets/json/csts/zdqyPoint.json'
import xxPoint from '@/assets/json/csts/xxPoint.json'
import wbPoint from '@/assets/json/csts/wbPoint.json'
import ylcsPoint from '@/assets/json/csts/ylcsPoint.json'
import hczPoint from '@/assets/json/csts/hczPoint.json'
import dyyPoint from '@/assets/json/csts/dyyPoint.json'
import LeaderFooter from '@/components/leader/common/leaderFooter.vue'
import myRobot from '@/components/robot/myRobot.vue'
import NumScroll from '@/components/leader/num_scrolls'
import Area from '@/components/leader/leader_csts/area.vue'
import LeafletMap from '@/components/map/LeafletMap2.vue'
import { LandUse, FullFactorGridEvents, StreetSurvey } from './components/hszl'
// 左侧菜单
import { ZhddAside } from './components/zhdd'
import { SurroundingResourcesMap, Tools } from './components/map'
import { getCameraMakers, getCameraXq } from '@/api/hs/hs.api.js'

// 各模块
import {
  // 视频监控
  VideoSurveillance
} from './components/zhdd'
import {
  EnterpriseRiskReporting,
  FileNotification,
  MonitoringWarning
  // AqjgFooter
} from './components/aqjg'

// 弹窗
import {
  // 事件信息
  EventInfo,
  // 事件详情
  EventDetail,
  // 事件统计
  EventStatistic,
  // 应急资源
  EmergencyResourcesDialog,
  // 值班人员
  OfficeronDuty,
  // 网格员登录情况
  GridMemberLoginStatusDialog,
  // 基本信息
  BasicInformation
} from './components/zhdd/dialog'
// 弹窗
import {
  SocialSmallPlace,
  EnterpriseRiskReportDetails,
  DocumentNotificationDetails,
  FileNotificationDialog,
  EmergencyPlanDialog
} from './components/aqjg/dialog'

import {
  enterpriseSinglePoint,
  enterpriseEmergencyTeamPoint,
  gasStationPoint,
  enterprisePoint,
  protectiveTargetPoint,
  externalDefibrillatorPoint,
  medicalInstitutionPoint,
  substationPoint,
  fireFightingUnitPoint,
  videoSurveillancePoint,
  publicecurityTeamPoint,
  schoolPoint
} from './components/aqjg/mock/point.js'

import { aqjgGetQyxx, aqjgGetXcs, aqjgGetYjzy } from '@/api/hs/hs_aqjg.api.js'

import 'swiper/css/swiper.css'
export default {
  name: 'Aqjg',
  components: {
    BlockBox,
    HeaderMain,
    effectRankBarSwiper,
    particle,
    leaderMiddle,
    gdMap,
    cesiumMap,
    LeaderFooter,
    CountryPart,
    myRobot,
    Area,
    NumScroll,
    LandUse,
    FullFactorGridEvents,
    StreetSurvey,
    VideoSurveillance,
    LeafletMap,
    // ZhddFooter,
    EventInfo,
    EventDetail,
    EventStatistic,
    EmergencyResourcesDialog,
    OfficeronDuty,
    GridMemberLoginStatusDialog,
    EnterpriseRiskReporting,
    FileNotification,
    MonitoringWarning,
    // AqjgFooter,
    ZhddAside,
    SurroundingResourcesMap,
    SocialSmallPlace,
    EnterpriseRiskReportDetails,
    DocumentNotificationDetails,
    FileNotificationDialog,
    BasicInformation,
    EmergencyPlanDialog
  },
  data() {
    return {
      data1: [],
      // 控制企业风险报告详情展示
      enterpriseRiskReportDetailsShow: false,
      // 应急预案弹窗
      emergencyPlanDialogShow: false,
      // 控制详情弹窗展示
      detailDialogShow: false,
      // 文件通知查看
      documentNotificationDetailsShow: false,
      // 文件通知更多弹窗
      fileNotificationDialogShow: false,
      // 详情弹窗标题
      detailTitle: '',
      man_active: require('@/assets/hszl/man_active.png'),
      man_normal: require('@/assets/hszl/man_normal.png'),
      woman_normal: require('@/assets/hszl/woman_normal.png'),
      woman_active: require('@/assets/hszl/woman_active.png'),
      // 控制事件信息显示
      eventDialogShow: false,
      // 控制事件详情显示
      eventDetailShow: false,
      // 控制事件统计显示
      eventStatisticShow: false,
      // 控制应急资源显示
      emergencyResourcesDialogShow: false,
      // 控制值班人员显示
      officeronDutyShow: false,
      // 控制网格员登录情况显示
      gridMemberLoginStatusDialogShow: false,
      // 控制视频监控显示
      spjkShow: false,
      // 控制通讯调度显示
      txddShow: false,
      // 控制基本信息弹窗显示
      basicInfomationShow: false,
      // 基础信息标题
      basicInfomationTitle: '',
      // 基础信息内容
      basicInfomationList: [],
      // 控制基础信息按钮显示
      basicInfomationBtnShow: false,
      // 基础信息按钮
      basicInfomationBtns: [],
      sphsData: [
        { id: '4000', label: '河南社区网格员' },
        { id: '4001', label: '湖熟社区网格员' },
        { id: '4002', label: '和进社区网格员' },
        { id: '4003', label: '龙都社区网格员' },
        { id: '4004', label: '周岗社区网格员' },
        { id: '4005', label: '金桥社区网格员' }
      ],
      initCheckedPeo: [],
      eventActive: false,
      jgzzShow: false,
      zdcsShow: false,
      djylShow: false,
      bjxqShow: false,
      showArea: false,
      popAreaInfo: {},
      areaPointList: [],
      jgzzActive: 0,
      zdcsActive: 0,
      showCesiumMap: false,
      // chartData: null,
      chartData: [
        ['product', '社会面小场所'],
        ['小生产加工厂', 100],
        ['小餐饮', 200],
        ['小美容美发', 150],
        ['小汽修', 100],
        ['其他', 200],
        ['其他种类', 150],
        ['小洗浴', 150],
        ['家庭农场', 150],
        ['堆场', 150],
        ['小商超', 150]
      ],

      chartData2: [
        ['product', '政府预案'],
        ['生产安全', 1245],
        ['生态环境', 1245],
        ['自然灾害', 1245],
        ['公共安全', 1245],
        ['食品安全', 1245]
      ],
      chartDataOptions: {
        // colors: [
        //   '#23949a',
        //   '#4b6590',
        //   '#4e9349',
        //   '#1f6993',
        //   '#867f5d',
        //   '#855952',
        //   '#865090',
        //   '#0280DF',
        //   '#CDCF72'
        // ],
        legend: {
          top: 2,
          // left: 100,
          // orient: 'vertical',
          itemWidth: 10,
          itemHeight: 10,
          itemRadius: 2,
          itemPadding: 5,
          itemDistance: 0,
          itemMarginTop: 12,
          textColor: 'rgba(255, 255, 255, 0.85)',
          fontWeight: '400',
          fontSize: '12px',
          fontFamily: 'DINPro-Medium',
          letterSpacing: '0'
        },
        position: ['50%', '10%'],
        bgImg: {
          width: '60%',
          height: '60%',
          top: '42%',
          left: '50%'
        },
        unit: '件',
        title: {
          fontSize: '16px',
          top: 60
        },
        subtitle: {
          fontSize: '14px',
          top: 80
        }
      },
      chartData2Options: {
        // colors: ['#80CF83', '#CDCF72', '#0280DF', '#13A4C0'],
        legend: {
          top: 2,
          // left: 100,
          // orient: 'vertical',
          itemWidth: 10,
          itemHeight: 10,
          itemRadius: 2,
          itemPadding: 5,
          itemDistance: 12,
          itemMarginTop: 12,
          textColor: 'rgba(255, 255, 255, 0.85)',
          fontWeight: '400',
          fontSize: '12px',
          fontFamily: 'DINPro-Medium',
          letterSpacing: '3px'
        },
        position: ['50%', '-46%'],
        bgImg: {
          width: '60%',
          height: '60%',
          top: '42%',
          left: '50%'
        },
        unit: '件',
        title: {
          fontSize: '16px',
          top: 60
        },
        subtitle: {
          fontSize: '14px',
          top: 80
        }
      },
      data2: [
        {
          tit: '参保人数',
          img: require('@/assets/csts/icon7.png'),
          cont: [
            {
              num: 49883,
              unit: '人'
            }
          ]
        },
        {
          tit: '参保缴费补贴',
          img: require('@/assets/csts/icon8.png'),
          cont: [
            {
              num: 13467,
              unit: '人'
            },
            {
              num: 2346.45,
              unit: '万'
            }
          ]
        },
        {
          tit: '养老金发放',
          img: require('@/assets/csts/icon9.png'),
          cont: [
            {
              num: 13467,
              unit: '人'
            },
            {
              num: 1277.75,
              unit: '万'
            }
          ]
        }
      ],
      activaIdx: -1,
      isShowEventDetail: false,
      isShowCountryPart: false,
      isXlgcShow: false,
      isXlgcShow1: false,
      isWgllShow: false,
      isdzzShow: false,
      isxzqyShow: false,
      iszwzxShow: false,
      iszhzxShow: false,
      iscsglgdShow: false,
      isshaqShow: false,
      issthbShow: false,
      iswhlyShow: false,
      isyjfkShow: false,
      isggjtShow: false,
      iszdqyShow: false,
      isxxShow: false,
      iswbShow: false,
      isylcsShow: false,
      ishczShow: false,
      isdyyShow: false,
      asideList: [
        {
          bg: require('@/assets/aside/bg1.png'),
          bgActive: require('@/assets/aside/bg2.png'),
          icon: require('@/assets/aqjg/map/icon1.png'),
          iconActive: require('@/assets/aqjg/map/icon1_active.png'),
          label: '重点场所',
          active: false
        },
        {
          bg: require('@/assets/aside/bg1.png'),
          bgActive: require('@/assets/aside/bg2.png'),
          icon: require('@/assets/aqjg/map/icon2.png'),
          iconActive: require('@/assets/aqjg/map/icon2_active.png'),
          label: '应急预案',
          active: false
        },
        {
          bg: require('@/assets/aside/bg1.png'),
          bgActive: require('@/assets/aside/bg2.png'),
          icon: require('@/assets/aqjg/map/icon3.png'),
          iconActive: require('@/assets/aqjg/map/icon3_active.png'),
          label: '应急资源',
          active: false
        },
        {
          bg: require('@/assets/aside/bg1.png'),
          bgActive: require('@/assets/aside/bg2.png'),
          icon: require('@/assets/aqjg/map/icon4.png'),
          iconActive: require('@/assets/aqjg/map/icon4_active.png'),
          label: '视频监控',
          active: false
        },
        {
          bg: require('@/assets/aside/bg1.png'),
          bgActive: require('@/assets/aside/bg2.png'),
          icon: require('@/assets/aqjg/map/icon5.png'),
          iconActive: require('@/assets/aqjg/map/icon5_active.png'),
          label: '视频会商',
          active: false
        }
      ],
      showYjzyList: false,
      cameraId1: null,
      cameraId2: null,
      cameraId3: null,
      cameraId4: null,
      asideLeveltwoMenu: [
        {
          iconNormal: require('@/assets/foot/icon10.png'),
          iconActive: require('@/assets/foot/icon21.png'),
          label: '企业应急队伍',
          layerId: 'enterpriseEmergencyTeam',
          count: 3,
          checked: false
        },
        {
          iconNormal: require('@/assets/foot/icon11.png'),
          iconActive: require('@/assets/foot/icon22.png'),
          label: '加油站',
          layerId: 'gasStation',
          count: 2,
          checked: false
        },
        {
          iconNormal: require('@/assets/foot/icon12.png'),
          iconActive: require('@/assets/foot/icon23.png'),
          label: '企业',
          layerId: 'enterprise',
          count: 162,
          checked: false
        },
        {
          iconNormal: require('@/assets/foot/icon13.png'),
          iconActive: require('@/assets/foot/icon24.png'),
          label: '防护目标',
          layerId: 'protectiveTarget',
          count: 1,
          checked: false
        },
        {
          iconNormal: require('@/assets/foot/icon14.png'),
          iconActive: require('@/assets/foot/icon25.png'),
          label: '体外除颤仪',
          layerId: 'externalDefibrillator',
          count: 7,
          checked: false
        },
        {
          iconNormal: require('@/assets/foot/icon15.png'),
          iconActive: require('@/assets/foot/icon26.png'),
          label: '医疗机构',
          layerId: 'medicalInstitution',
          count: 1,
          checked: false
        },
        {
          iconNormal: require('@/assets/foot/icon16.png'),
          iconActive: require('@/assets/foot/icon27.png'),
          label: '变电站',
          layerId: 'substation',
          count: 4,
          checked: false
        },
        {
          iconNormal: require('@/assets/foot/icon17.png'),
          iconActive: require('@/assets/foot/icon28.png'),
          label: '二级消防单位',
          layerId: 'fireFightingUnit',
          count: 13,
          checked: false
        },
        {
          iconNormal: require('@/assets/foot/icon18.png'),
          iconActive: require('@/assets/foot/icon29.png'),
          label: '视频监控',
          layerId: 'videoSurveillance',
          count: 72,
          checked: false
        },
        {
          iconNormal: require('@/assets/foot/icon19.png'),
          iconActive: require('@/assets/foot/icon30.png'),
          label: '公安队伍',
          layerId: 'publicecurityTeam',
          count: 1,
          checked: false
        },
        {
          iconNormal: require('@/assets/foot/icon20.png'),
          iconActive: require('@/assets/foot/icon31.png'),
          label: '学校',
          layerId: 'school',
          count: 4,
          checked: false
        }
      ]
    }
  },
  created() {
    this.watchFlag()
    this.token = localStorage.getItem('aqjgToken')
  },
  mounted() {
    this.cameraMarker()

    // this.aqjgGetQyxxFn()
    // this.aqjgGetXcsFn()

    // this.aqjgGetYjzyFn()
  },
  watch: {
    '$route.params'(newval, oldval) {
      this.watchFlag()
    },
    isXlgcShow1(newval) {
      this.asideList[4].active = newval
      if (!newval) {
        this.sphsData = [
          { id:'4000', label: '河南社区网格员' },
          { id:'4001', label: '湖熟社区网格员' },
          { id:'4002', label: '和进社区网格员' },
          { id:'4003', label: '龙都社区网格员' },
          { id:'4004', label: '周岗社区网格员' },
          { id:'4005', label: '金桥社区网格员' }
        ]
        this.initCheckedPeo = []
      }
    },
    spjkShow(newval) {
      this.asideList[3].active = newval
    },
    txddShow(newval) {
      if (this.zhddType === 1) {
        this.asideList[0].active = newval
      }
      if (!newval) {
        this.sphsData = [
          { id: '4000', label: '河南社区网格员' },
          { id: '4001', label: '湖熟社区网格员' },
          { id: '4002', label: '和进社区网格员' },
          { id: '4003', label: '龙都社区网格员' },
          { id: '4004', label: '周岗社区网格员' },
          { id: '4005', label: '金桥社区网格员' }
        ]
        this.initCheckedPeo = []
      }
    },
    emergencyPlanDialogShow(newVal) {
      if (!newVal) {
        this.asideList[1].active = false
      }
    },
    enterpriseRiskReportDetailsShow(newVal) {
      if (!newVal) {
        this.asideList[0].active = false
      }
    }
  },
  computed: {
    formatNumber() {
      return function(param) {
        console.log(param)
        if (this.isEmpty(param) || param.length < 4 || isNaN(param)) {
          return param
        }

        let small = ''
        let isSmall = false
        if (param.split('.').length === 2) {
          // 有小数
          isSmall = true
          small = param.split('.')[1]
          param = param.split('.')[0]
        }

        let result = ''

        while (param.length > 3) {
          result = ',' + param.slice(-3) + result
          param = param.slice(0, param.length - 3)
        }

        if (param) {
          result = param + result
        }

        if (isSmall) {
          result = result + '.' + small
        }
        console.log(result)
        return result
      }
    }
  },
  methods: {
    // 获取企业信息
    async aqjgGetQyxxFn() {
      const res = await aqjgGetQyxx(this.token)
      if (res?.data) {
        this.data1 = res.data.list?.map(it => [it.title])
      }
    },
    // 获取社会面小场所
    async aqjgGetXcsFn() {
      const res = await aqjgGetXcs(this.token)
      console.log(res)
      if (res?.data) {
        this.chartData = [
          ['product', '社会面小场所'],
          ...res.data.slice(0, 9).map(it => [it.typeName, it.num])
        ]
      }
    },
    // 获取应急资源
    async aqjgGetYjzyFn(type, layerId) {
      const res = await aqjgGetYjzy(
        {
          pageNum: 1,
          pageSize: 10,
          resourceType: type
        },
        this.token
      )
      if (res?.data) {
        console.log(res.data.list)
        if (type === '变电站') {
          const points = res.data.list.map(it => {
            const [lat, lng] = it.coordEs.split(',')
            const infoObj = JSON.parse(it.jsonStr)
            return {
              latlng: [lat, lng],
              iconUrl: require('@/assets/map/point/point15.png'),
              id: it.id,
              info: [
                {
                  label: '名称：',
                  value: infoObj.electric_name
                },
                {
                  label: '地址：',
                  value: infoObj.electric_dept
                },
                {
                  label: '运维单位：',
                  value: infoObj.electric_dept
                },
                {
                  label: '值班电话：',
                  value: infoObj.electric_tel
                },
                {
                  label: '负责人：',
                  value: infoObj.electric_liable
                },
                {
                  label: '设备参数：',
                  value: infoObj.electric_parameter
                }
              ]
            }
          })
          const data = this.encapsulateArrayofPoint(points, layerId)
          this.marker(data, layerId)
        }
      }
    },
    // 企业风险报告定位
    locate(it) {
      console.log(it)
      const layerId = 'enterprise'
      const data = this.encapsulateArrayofPoint(enterpriseSinglePoint, layerId)
      this.marker(data, layerId)
    },
    // 文件通知查看
    view(it) {
      console.log(it)
      this.documentNotificationDetailsShow = true
    },
    // 处理地图菜单点击事件
    handleMenuClick(active, i) {
      console.log(active, i)
      if (i === 0) {
        this.enterpriseRiskReportDetailsShow = active
      } else if (i === 1) {
        this.emergencyPlanDialogShow = active
      } else if (i === 2) {
        this.showYjzyList = active
      } else if (i === 3) {
        this.spjkShow = active
      } else if (i === 4) {
        this.isXlgcShow1 = true
      }
    },
    // 地图打点
    marker(data, layerId) {
      this.$refs.leafletMap.drawPoiMarker(data, layerId, true)
    },
    // 点击标记点位显示弹窗
    showDialog(layerId, it) {
      const id = it.props.id
      const info = it.info

      console.log(layerId, it)
      if (layerId === 'enterprise') {
        this.enterpriseRiskReportDetailsShow = true
        return
      }
      if (layerId === 'enterpriseEmergencyTeam') {
        this.basicInfomationTitle = '企业应急队伍'
        this.basicInfomationList = [
          {
            label: '队伍名：',
            value: '江宁滨江专职队'
          },
          {
            label: '队伍地址：',
            value: '江宁区滨江开发区春阳路7号'
          },
          {
            label: '所属单位：',
            value: '湖熟街道'
          },
          {
            label: '值班电话：',
            value: '18205086401'
          },
          {
            label: '人数：',
            value: '26'
          },
          {
            label: '联系人：',
            value: '涂文康'
          },
          {
            label: '联系电话：',
            value: '15950493959'
          },
          {
            label: '专业类别：',
            value: '防火'
          },
          {
            label: '单位性质：',
            value: '企业'
          }
        ]
        this.basicInfomationShow = true
      } else if (layerId === 'gasStation') {
        this.basicInfomationTitle = '加油站'
        this.basicInfomationList = [
          {
            label: '加油站名称：',
            value: '中国石化销售有限公司江苏南京江宁景明大街加油站'
          },
          {
            label: '地址：',
            value: '南京市江宁区滨江开发区景明大街和锦文路'
          },
          {
            label: '类型：',
            value: '加油站'
          },
          {
            label: '值班电话：',
            value: '18205086401'
          },
          {
            label: '联系人：',
            value: '张文明'
          },
          {
            label: '联系人电话：',
            value: '15950493959'
          },
          {
            label: '辖区：',
            value: '江宁区'
          },
          {
            label: '管理部门：',
            value: '市应急管理局'
          }
        ]
        this.basicInfomationShow = true
      } else if (layerId === 'protectiveTarget') {
        this.basicInfomationTitle = '防护目标'
        this.basicInfomationList = [
          {
            label: '名称：',
            value: '南京市江宁区江宁中心小学'
          },
          {
            label: '地址：',
            value: '江宁区江宁街道如练路99号'
          },
          {
            label: '安全管理人：',
            value: '郑松龙'
          },
          {
            label: '电话：',
            value: '18205086401'
          },
          {
            label: '安全责任人：',
            value: '汤明'
          },
          {
            label: '电话：',
            value: '13913862942'
          }
        ]
        this.basicInfomationShow = true
      } else if (layerId === 'externalDefibrillator') {
        this.basicInfomationTitle = '体外除颤仪'
        this.basicInfomationList = [
          {
            label: '所属类别：',
            value: '器材工具类'
          },
          {
            label: '装备名称：',
            value: 'AED'
          },
          {
            label: '装备型号：',
            value: '无'
          },
          {
            label: '数量：',
            value: '1'
          },
          {
            label: '所在单位：',
            value: '滨江开发区管委会'
          },
          {
            label: '所在地址：',
            value: '天成路18号'
          },
          {
            label: '存放地点：',
            value: '门卫室'
          },
          {
            label: '责任部门：',
            value: '门卫'
          },
          {
            label: '责任人：',
            value: '门卫'
          },
          {
            label: '联系电话：',
            value: '13806756753'
          }
        ]
        this.basicInfomationShow = true
      } else if (layerId === 'medicalInstitution') {
        this.basicInfomationTitle = '医疗机构'
        this.basicInfomationList = [
          {
            label: '名称：',
            value: '南京市江宁医院'
          },
          {
            label: '地址：',
            value: '宝象路58号'
          },
          {
            label: '级别：',
            value: '三甲'
          },
          {
            label: '性质：',
            value: '公有制'
          }
        ]
        this.basicInfomationShow = true
      } else if (layerId === 'substation') {
        this.basicInfomationTitle = '变电站'
        this.basicInfomationList = info
        this.basicInfomationShow = true
      } else if (layerId === 'fireFightingUnit') {
        this.basicInfomationTitle = '二级消防单位'
        this.basicInfomationList = [
          {
            label: '名称：',
            value: '林德梅山（南京）气体有限公司'
          },
          {
            label: '地址：',
            value: '南京市江宁区江宁街道工业园区8号'
          },
          {
            label: '工地电话：',
            value: '025-86102356'
          },
          {
            label: '站长',
            value: '耿建东'
          },
          {
            label: '站长电话：',
            value: '17366213501'
          },
          {
            label: '副站长：',
            value: '无'
          }
        ]
        this.basicInfomationShow = true
      } else if (layerId === 'videoSurveillance') {
        this.spjkShow = true
      } else if (layerId === 'publicecurityTeam') {
        this.basicInfomationTitle = '公安队伍'
        this.basicInfomationList = [
          {
            label: '名称：',
            value: '滨江派出所'
          },
          {
            label: '地址：',
            value: '南京市江宁开发区盛安大道700号'
          },
          {
            label: '值班电话：',
            value: '84950110'
          }
        ]
        this.basicInfomationShow = true
      } else if (layerId === 'school') {
        this.basicInfomationTitle = '学校'
        this.basicInfomationList = [
          {
            label: '名称：',
            value: '南京传媒学院（滨江校区）'
          },
          {
            label: '地址：',
            value: '南京市江宁区滨江开发区翔凤路168号'
          },
          {
            label: '联系人：',
            value: '刘浩'
          },
          {
            label: '联系电话：',
            value: '18112925097'
          }
        ]
        this.basicInfomationShow = true
      }
    },
    // 标记点基础信息弹窗内按钮点击事件
    clickBasicInformationBtn(i, name) {
      console.log(i, name)
    },
    // 显示更多
    showMoreFn1(i) {
      if (i === 1) {
        // this.detailDialogShow = true
      } else if (i === 2) {
        this.detailDialogShow = true
      } else if (i === 3) {
        this.fileNotificationDialogShow = true
      }
    },
    // 事件信息弹窗上点击按钮触发事件
    eventInfoBtnClick(i) {
      if (i === 0) {
        this.eventDetailShow = true
      }
    },
    chooseSource(checked, i, item) {
      console.log(checked, i, item)
      const layerId = item.layerId
      if (checked) {
        if (item.label === '无人机') {
          this.droneShow = checked
        } else if (item.label === '企业应急队伍') {
          const data = this.encapsulateArrayofPoint(enterpriseEmergencyTeamPoint, layerId)
          this.marker(data, layerId)
        } else if (item.label === '加油站') {
          const data = this.encapsulateArrayofPoint(gasStationPoint, layerId)
          this.marker(data, layerId)
        } else if (item.label === '企业') {
          const data = this.encapsulateArrayofPoint(enterprisePoint, layerId)
          this.marker(data, layerId)
        } else if (item.label === '防护目标') {
          const data = this.encapsulateArrayofPoint(protectiveTargetPoint, layerId)
          this.marker(data, layerId)
        } else if (item.label === '体外除颤仪') {
          const data = this.encapsulateArrayofPoint(externalDefibrillatorPoint, layerId)
          this.marker(data, layerId)
        } else if (item.label === '医疗机构') {
          const data = this.encapsulateArrayofPoint(medicalInstitutionPoint, layerId)
          this.marker(data, layerId)
        } else if (item.label === '变电站') {
          this.aqjgGetYjzyFn(item.label, layerId)
        } else if (item.label === '二级消防单位') {
          const data = this.encapsulateArrayofPoint(fireFightingUnitPoint, layerId)
          this.marker(data, layerId)
        } else if (item.label === '视频监控') {
          const data = this.encapsulateArrayofPoint(videoSurveillancePoint, layerId)
          this.marker(data, layerId)
        } else if (item.label === '公安队伍') {
          const data = this.encapsulateArrayofPoint(publicecurityTeamPoint, layerId)
          this.marker(data, layerId)
        } else if (item.label === '学校') {
          const data = this.encapsulateArrayofPoint(schoolPoint, layerId)
          this.marker(data, layerId)
        }
      } else {
        if (item.label === '无人机') {
          this.droneShow = checked
        } else {
          this.$refs.leafletMap.removeLayer(layerId)
        }
      }
    },
    encapsulateArrayofPoint(points, layerId) {
      const data = points.map(it => ({
        latlng: it.latlng,
        icon: {
          iconUrl: it.iconUrl,
          iconSize: [32, 42],
          iconAnchor: [16, 42]
        },
        props: {
          id: it.id,
          type: layerId
        },
        info: it.info
      }))
      return data
    },
    watchFlag() {
      let flag = this.$route.query.flag?.substr(0, 4)
      this.djylShow = false
      this.iscsglgdShow = false
      switch (flag) {
        case 'djyl':
          this.djylShow = true
          break
        case 'csgl':
          this.iscsglgdShow = true
          break
        default:
          break
      }
    },
    closeCountryPart() {
      this.isShowCountryPart = false
      if (this.activaIdx === 3) this.activaIdx = -1
    },
    checkTree(e) {
      console.log(e)
      // 地图打点
      let data = csbjPoint.features.map(e => {
        return {
          position: e.geometry.coordinates.concat(100),
          properties: e.properties,
          img: e.img
        }
      })
      this.$refs.CesiumMapRef.loadPoints1(data, 'csbjPoint')
    },
    async cameraMarker() {
      const res = await getCameraMakers()
      if (res && res.length > 0) {
        let filerRes = res.filter(item => item.id)
        console.log('filerRes', filerRes)
        this.cameraId1 = filerRes[4].id
        this.cameraId2 = filerRes[5].id
        this.cameraId3 = filerRes[7].id
        this.cameraId4 = filerRes[9].id
      }
    }
  }
}
</script>

<style lang="less" scoped>
@font-face {
  font-family: QuartzRegular;
  src: url(~@/assets/leader/font/QuartzRegular.ttf);
}
@keyframes rotateS {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes rotateN {
  0% {
    transform: rotate(360deg);
  }
  100% {
    transform: rotate(0);
  }
}
@keyframes rotateY {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotateY(360deg);
  }
}

.leader_sjts {
  width: 1920px;
  height: 1080px;
  position: absolute;
  top: 0;
  display: flex;
  justify-content: space-between;
  .leader_city_box {
    width: 450px;
    .leader_zt_contain {
      height: 100%;
    }
  }
  .box {
    .cont {
      width: 100%;
      height: 100%;
    }
    .wrapper1 {
      width: 100%;
      height: 100%;
      padding: 14px 0 22px;
      .introduce {
        width: 100%;
        height: 100%;
        padding: 18px 26px;
        background: url(~@/assets/hszl/bg1.png) no-repeat;
        font-size: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #4fddff;
        line-height: 22px;
        letter-spacing: 2px;
        text-align: left;
      }
    }
    .wrapper4 {
      width: 100%;
      height: 100%;
      padding: 20px 0;
      ul {
        width: 100%;
        height: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;
        li {
          display: flex;
          height: 49px;
          gap: 10px;
          .icon {
            width: 46px;
            height: 49px;
          }
          .info {
            margin-top: -6px;
            .label {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
              text-align: left;
              padding-left: 5px;
            }
            .line {
              width: 87px;
              height: 6px;
              background: url('~@/assets/hszl/line1.png') no-repeat;
            }
            .count {
              text-align: left;
              margin-top: 4px;
              width: 87px;
              height: 22px;
              line-height: 22px;
              background: linear-gradient(96deg, #1d4975 0%, rgba(19, 56, 87, 0.24) 100%);
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #a8b1b9;
              padding-left: 5px;
              span {
                font-size: 18px;
                font-family: PingFangSC, PingFang SC;
                font-weight: normal;
                color: #ffffff;
                line-height: 22px;
                margin-right: 3px;
              }
            }
          }
        }
      }
    }
    .wrapper5 {
      width: 100%;
      height: 100%;
      padding-top: 14px;
      .info {
        width: 100%;
        height: 71px;
        padding-left: 8px;
        display: flex;
        justify-content: space-between;
        .total {
          width: 130px;
          height: 100%;
          background: url('~@/assets/hszl/bg4.png') no-repeat;
          display: grid;
          place-items: center;
          .cont {
            padding-top: 13px;
            .label {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
            }
            .value {
              font-size: 18px;
              font-family: PingFangSC, PingFang SC;
              font-weight: normal;
              color: #ffffff;
              line-height: 22px;
              background: linear-gradient(180deg, #ffffff 0%, #00fffa 67%, #04a1ff 100%);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              span {
                font-size: 12px;
              }
            }
          }
        }
        .counts {
          width: 312px;
          height: 100%;
          background: url('~@/assets/hszl/bg5.png') no-repeat;
          display: flex;
          padding: 0 9px;
          .men {
            text-align: left;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            margin-right: 4px;
            .count {
              height: 21px;
              font-size: 16px;
              font-family: PingFangSC, PingFang SC;
              font-weight: normal;
              line-height: 19px;
              background: linear-gradient(180deg, #ffffff 0%, #00ceff 67%, #04a1ff 100%);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              span {
                font-size: 14px;
              }
            }
            .chart {
              display: flex;
              gap: 4px;
              span {
                width: 10px;
                height: 25px;
                background-repeat: no-repeat;
              }
            }
          }
          .women {
            text-align: right;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            .count {
              height: 21px;
              font-size: 16px;
              font-family: PingFangSC, PingFang SC;
              font-weight: normal;
              line-height: 19px;
              background: linear-gradient(180deg, #ffffff 0%, #f076ff 66%, #ff04ff 100%);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              span {
                font-size: 14px;
              }
            }
            .chart {
              display: flex;
              gap: 4px;
              span {
                width: 12px;
                height: 25px;
                background-repeat: no-repeat;
              }
            }
          }
        }
      }
      .chart_wrap {
        position: relative;
        width: 100%;
        height: 198px;
        .sign {
          position: absolute;
          left: 105px;
          top: 25px;
          .woman {
            position: absolute;
            width: 27px;
            height: 57px;
            left: 0;
            top: 0;
            background: url('~@/assets/hszl/woman.png') no-repeat;
          }
          .man {
            position: absolute;
            width: 23px;
            height: 57px;
            left: 95px;
            top: 47px;
            background: url('~@/assets/hszl/man.png') no-repeat;
          }
          .man_count {
            position: absolute;
            left: 10px;
            top: 65px;
            width: 109px;
            height: 31px;
            font-size: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 24px;
          }
          .woman_count {
            position: absolute;
            left: 10px;
            top: 10px;
            width: 109px;
            height: 31px;
            font-size: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 24px;
          }
        }
      }
    }
  }
  .box1 {
    .cont {
      padding: 13px 0 23px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: space-between;
      li {
        position: relative;
        width: 222px;
        height: 61px;
        padding: 10px 7px;
        display: flex;
        background: url(~@/assets/hszl/bg2.png) no-repeat;
        .icon {
          width: 41px;
          height: 41px;
          background: url(~@/assets/hszl/bg3.png) no-repeat;
          font-size: 17px;
          img {
            animation: move infinite 3s ease-in-out;
            @keyframes move {
              0% {
                transform: rotateY(0);
              }
              50% {
                transform: rotateY(180deg);
              }
              100% {
                transform: rotateY(360deg);
              }
            }
          }
          .name {
            font-size: 10px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 11px;
            text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
          }
        }
        .line {
          position: absolute;
          left: 50px;
          top: 5px;
          width: 1px;
          height: 55px;
          border: 1px solid;
          border-image: linear-gradient(
              180deg,
              rgba(0, 83, 171, 0),
              rgba(0, 140, 213, 0.6),
              rgba(0, 83, 171, 0)
            )
            1 1;
        }
        .desc {
          position: relative;
          margin-left: 15px;
          text-align: left;
          padding-left: 18px;
          .xsj {
            position: absolute;
            width: 10px;
            height: 10px;
            left: 0;
            top: 7px;
            background: url(~@/assets/csts/xsj1.png) no-repeat;
          }
          .num {
            font-size: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 24px;
          }
          .tit {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 20px;
            white-space: nowrap;
          }
        }
      }
    }
  }
  .box2 {
    .cont {
      padding: 14px 28px 24px;
      display: flex;
      gap: 5px;
      .cont_item {
        position: relative;
        width: 128px;
        height: 176px;
        padding: 7px 15px 0 16px;
        box-shadow: inset 0px 1px 12px 2px rgba(44, 165, 235, 0.2);
        .icon {
          width: 97px;
          height: 99px;
        }
        .lzdx {
          width: 97px;
          height: 99px;
          position: absolute;
          top: 7px;
          left: 16px;
        }
        .tit {
          position: absolute;
          top: 97px;
          left: 50%;
          width: 100%;
          transform: translateX(-50%);
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #caecff;
          line-height: 20px;
          text-shadow: 0px 0px 1px #00132e;
        }
        ol {
          width: 100%;
          height: 40px;
          margin-top: 21px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          padding-left: 20px;
          & > li {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 22px;
            text-align: left;
            list-style: disc;
            &::marker {
              color: #38deff;
            }
            .unit {
              font-size: 12px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #caecff;
              line-height: 17px;
              text-shadow: 0px 0px 1px #00132e;
            }
          }
        }
      }
    }
  }
  .box3 {
    .cont {
      padding: 14px 22px 22px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: space-between;
      .video_ {
        flex-shrink: 0;
        width: 200px;
        height: 112px;
      }
    }
  }
  .box4 {
    .cont {
      padding: 15px 0;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-evenly;
      li {
        position: relative;
        width: 200px;
        height: 66px;
        background: url(~@/assets/csts/bg2.png) no-repeat;
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: left;
        padding-left: 92px;
        .icon {
          position: absolute;
          width: 26px;
          height: 26px;
          top: 10px;
          left: 31px;
          animation: move infinite 3s ease-in-out;
          @keyframes move {
            0% {
              transform: translateY(0px) rotateY(0);
            }
            50% {
              transform: translateY(-10px) rotateY(180deg);
            }
            100% {
              transform: translateY(0px) rotateY(360deg);
            }
          }
        }
        &:nth-child(2) {
          span {
            animation-delay: 0.3s;
          }
        }
        &:nth-child(3) {
          span {
            animation-delay: 0.6s;
          }
        }
        &:nth-child(4) {
          span {
            animation-delay: 0.9s;
          }
        }
        &:nth-child(5) {
          span {
            animation-delay: 1.2s;
          }
        }
        &:nth-child(6) {
          span {
            animation-delay: 1.5s;
          }
        }
        .tit {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 20px;
        }
        .num {
          font-size: 18px;
          font-family: PingFangSC, PingFang SC;
          font-weight: normal;
          color: #ffffff;
          line-height: 22px;
        }
      }
    }
  }
  .box5 {
    .cont {
      display: flex;
      flex-direction: column;
      .bar_chart {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        li {
          position: relative;
          display: flex;
          align-items: center;
          padding: 0 30px 0 23px;
          .icon {
            width: 20px;
            height: 22px;
            margin-right: 7px;
          }
          .icon2 {
            width: 10px;
            height: 12px;
            position: absolute;
            left: 28px;
            top: 5px;
            animation: move infinite 3s ease-in-out;
            @keyframes move {
              0% {
                transform: translateY(0px) rotateY(0);
              }
              50% {
                transform: translateY(0px) rotateY(180deg);
              }
              100% {
                transform: translateY(0px) rotateY(360deg);
              }
            }
          }
          .lab {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            margin-right: 8px;
          }
          .progress {
            display: block;
            flex: 1;
            height: 100%;
            background: rgba(0, 201, 255, 0.14);
            .cur {
              width: 100%;
              border: 1px solid;
              height: 100%;
              overflow: hidden;
              transition: width 0.5s;
            }
          }
          .num {
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            margin-left: 9px;
          }
          .percent {
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            margin-left: 14px;
          }
        }
      }
      .pie_chart {
        height: 123px;
        display: flex;
        justify-content: space-evenly;
        .warp {
          width: 107px;
          height: 107px;
          padding-top: 0;
          /deep/.ring {
            width: 100% !important;
            height: 100% !important;
          }
          /deep/.label {
            margin-top: -65px;
            .name {
              font-size: 13px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
            }
          }
        }
      }
    }
  }
  .box6 {
    .cont {
      padding: 14px 28px 0;
      .swiper-slide {
        height: 100%;
      }
      .swiper-pagination {
        text-align: right;
        bottom: 3px;
      }
      /deep/.swiper-pagination-bullet-active {
        background-color: rgba(255, 255, 255, 0.8);
      }
      .content {
        width: 100%;
        height: 100%;
        position: relative;
        img {
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
        }
        .cont {
          width: 100%;
          height: 30px;
          padding: 5px 10px;
          position: absolute;
          bottom: 0;
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
          text-align: left;
          background-color: rgba(0, 0, 0, 0.35);
        }
      }
    }
  }
  .box7 {
    .cont {
      display: grid;
      place-items: center;
      position: relative;
      .fy_out {
        position: absolute;
        top: 3%;
        left: 27%;
        z-index: 99;
        transform: translate(-50%, -50%);
        animation: rotateS infinite 12s linear;
      }
      .fy_in {
        position: absolute;
        top: 44%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
      .wrap {
        position: relative;
        width: 393px;
        height: 205px;
        background: url(~@/assets/csts/bg3.png) no-repeat;
        li {
          position: absolute;
          text-align: left;
          .num {
            font-size: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 24px;
          }
          .tit {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 17px;
          }
          &:nth-child(1) {
            top: 28px;
            left: 24px;
          }
          &:nth-child(2) {
            top: 28px;
            left: 295px;
          }
          &:nth-child(3) {
            bottom: 32px;
            left: 24px;
          }
          &:nth-child(4) {
            bottom: 32px;
            left: 295px;
          }
        }
      }
    }
  }
  .box9 {
    .cont {
      padding: 13px 23px 0;
      .title {
        width: 100%;
        height: 44px;
        line-height: 44px;
        display: flex;
        justify-content: space-between;
        padding: 0 24px;
        span {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
        }
      }
      .detail {
        position: relative;
        width: 404px;
        height: 156px;
        background: url(~@/assets/csts/bg4.png) no-repeat center;
        display: flex;
        justify-content: space-between;
        .center_out {
          position: absolute;
          left: 29%;
          top: -2%;
          transform: translate(-50%, -50%);
          z-index: 99;
          animation: rotateS infinite 12s linear;
        }
        .center_in {
          position: absolute;
          left: 29%;
          top: 2px;
          transform: translate(-50%, -50%);
          z-index: 99;
          animation: rotateN infinite 12s linear;
        }
        .fs {
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          gap: 9px;
          li {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .icon {
              width: 13px;
              height: 16px;
            }
            .lab {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
              padding-left: 6px;
              padding-right: 22px;
            }
            .num {
              font-size: 10px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              span {
                font-size: 18px;
                font-family: PingFangSC, PingFang SC;
                font-weight: normal;
                color: #ffffff;
                line-height: 22px;
              }
            }
          }
        }
        .fq {
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          gap: 9px;
          li {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .icon {
              width: 14px;
              height: 14px;
            }
            .lab {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
              padding-left: 22px;
              padding-right: 6px;
            }
            .num {
              font-size: 10px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              span {
                font-size: 18px;
                font-family: PingFangSC, PingFang SC;
                font-weight: normal;
                color: #ffffff;
                line-height: 22px;
              }
            }
          }
        }
        .zdqy {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          .num {
            font-size: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 24px;
          }
          .lab {
            font-size: 18px;
            font-family: PingFangSC, PingFang SC;
            color: #ffffff;
            line-height: 21px;
            letter-spacing: 1px;
          }
        }
      }
    }
  }
  .box10 {
    .cont {
      padding: 10px 35px 0;
      .desc {
        height: 53px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        li {
          display: flex;
          .icon {
            width: 41px;
            height: 41px;
            margin-right: 11px;
          }
          .info {
            text-align: left;
            .num {
              font-size: 18px;
              font-family: PingFangSC, PingFang SC;
              font-weight: normal;
              color: #ffffff;
              line-height: 22px;
            }
            .lab {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
            }
          }
        }
      }
      .chart {
        height: calc(100% - 53px);
        margin-top: -20px;
      }
    }
  }
  .box12 {
    position: relative;
    .gajq_lz {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .left_bg {
    width: 460px;
    height: 1033px;
    background: url(~@/assets/leader/img/component/left_bg.png) no-repeat center / 100% 100%;
    position: absolute;
    left: 50px;
    top: 25px;
    z-index: 102;
  }
  .right_bg {
    width: 520px;
    height: 1033px;
    background: url(~@/assets/leader/img/component/right_bg.png) no-repeat center / 100% 100%;
    position: absolute;
    right: 50px;
    top: 25px;
    z-index: 102;
  }
  .left {
    width: 538px;
    height: 1080px;
    display: flex;
    justify-content: space-between;
    padding-top: 123px;
    padding-left: 50px;
    // margin-top: 123px;
    // margin-left: 50px;
    position: relative;
    z-index: 1001;
    background: linear-gradient(270deg, rgba(0, 0, 0, 0.1) 0%, #060c10 100%);
  }
  .right {
    width: 538px;
    display: flex;
    justify-content: space-between;
    padding-top: 123px;
    padding-left: 50px;
    position: relative;
    z-index: 1001;
    background: linear-gradient(270deg, #060c10 0%, rgba(0, 0, 0, 0.1) 100%);
  }
  .map_box {
    position: absolute;
    width: 1920px;
    height: 970px;
    top: 100px;
    left: 0;
  }
}
.jgzzBox {
  width: 109px;
  height: 170px;
  display: flex;
  flex-direction: column;
  position: absolute;
  left: 525px;
  bottom: 93px;
  z-index: 999;
  background: url(~@/assets/csts/btnBg11.png) no-repeat center / 100% 100%;
  .jgzzItem {
    width: 103px;
    height: 35px;
    margin: 2px 3px 2px 3px;
    background: url(~@/assets/csts/btn11.png) no-repeat;
    img {
      float: left;
      margin: 7px 0 0 10px;
    }
    span {
      float: left;
      font-size: 17px;
      font-family: PingFangSC, PingFang SC;
      color: #e9fffe;
      line-height: 35px;
      background: linear-gradient(180deg, #ffffff 0%, #2f8af4 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .jgzzActive {
    width: 105px;
    height: 37px;
    margin: 1px 2px 1px 2px;
    background: url(~@/assets/csts/btn12.png) no-repeat;
  }
}
.zdcsBox {
  width: 109px;
  height: 248px;
  display: flex;
  flex-direction: column;
  position: absolute;
  left: 1197px;
  bottom: 93px;
  z-index: 999;
  background: url(~@/assets/csts/btnBg12.png) no-repeat center / 100% 100%;
  .jgzzItem {
    width: 103px;
    height: 35px;
    margin: 2px 3px 2px 3px;
    background: url(~@/assets/csts/btn11.png) no-repeat;
    img {
      float: left;
      margin: 7px 0 0 10px;
    }
    span {
      float: left;
      font-size: 17px;
      font-family: PingFangSC, PingFang SC;
      color: #e9fffe;
      line-height: 35px;
      background: linear-gradient(180deg, #ffffff 0%, #2f8af4 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .jgzzActive {
    width: 105px;
    height: 37px;
    margin: 1px 2px 1px 2px;
    background: url(~@/assets/csts/btn12.png) no-repeat;
  }
}
.left,
.right {
  ::v-deep {
    .el-carousel {
      width: 450px;
      height: 920px;
      .el-carousel__indicators {
        .el-carousel__indicator {
          &.is-active {
            .el-carousel__button {
              height: 4px;
              background-color: #49e6ff;
            }
          }
          .el-carousel__button {
            height: 4px;
            background-color: rgba(255, 255, 255, 0.4);
            border-radius: 2px;
          }
        }
      }
    }
  }
}
.right {
  ::v-deep {
    .el-carousel {
      margin-left: auto;
    }
  }
}
::v-deep .el-carousel--horizontal {
  width: 450px;
  overflow-x: hidden;
}
/* 3D饼图（type=2）背景图片高度 */
:deep(.pie-warrper) {
  .bg-img {
    height: 65%;
  }
}
</style>
