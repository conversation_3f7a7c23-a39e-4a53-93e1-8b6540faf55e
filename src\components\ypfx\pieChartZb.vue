<template>
  <chart class='charts' :autoresize='true' :option='options' :loading="loading"></chart>
</template>

<script setup>
import {ref} from "vue"
// import { graphic } from 'echarts/lib/export/api.js'
const loading = ref(true)
const props = defineProps({
    title:{
        type:String,
        default:'视频AI'
    },
    subTitle:{
        type:String,
        default:'分析'
    },
    data:{
        type:Array,
        default:()=>[
            {
                value: 520,
                name: "人群聚集",
            },
            {
                value: 280,
                name: "人员打架",
            },
            {
                value: 100,
                name: "违章停车",
            },
            {
                value: 100,
                name: "汽车追尾",
            },
        ]
    }
})
let pieData=[]
for (let i = 0; i < props.data.length; i++) {
  pieData.push({
    ...props.data[i],
    itemStyle: {
      borderRadius: 10,
    },
  });
}

const colorList = [
  'rgba(44, 186, 175, 1)',
  'rgba(209, 81, 81, 1)',
  'rgba(58, 145, 65, 1)',
  'rgba(70, 82, 216, 1)',
  'rgba(174, 157, 50, 1)',
]
const options = ref({
  title: [
    {
      text: props.title,
      top: "38%",
      left: "39%",
      textAlign: "center",
      textStyle: {
        fontSize: "14",
        fontWeight: "500",
        color: "#98b5d2",
        textAlign: "center",
      },
    },
    {
      text: props.subTitle,
      left: "39%",
      top: "50%",
      textAlign: "center",
      textStyle: {
        fontSize: "14",
        fontWeight: "500",
        color: "#98b5d2",
        textAlign: "center",
      },
    },
  ],
  legend: {
    right: "8%",
    top: "25%",
    align: "left",
    itemGap: 10,
    itemWidth: 11,
    itemHeight: 8,
    orient:'vertical',
    textStyle: {
      color: "rgba(157, 226, 255, 1)",
      rich: {
        name: {
          verticalAlign: "right",
          align: "left",
          fontSize: 14,
          color: "rgba(157, 226, 255, 1)",
        }
      },
    },
    formatter: (name) => {
      return "{name|" + name + "}";
    },
  },
  color: colorList,
  series: [
    {
      type: "pie",
      z: 2,
      radius: ["50%", "65%"],
      center: ["40%", "50%"],
      label: {
        show: true,
        formatter:(data)=>{
          let richName=`item${data.dataIndex}`
          return `{${richName}|${data.percent}%}`
        },
        rich:{
          item0:{
            color:colorList[0]
          },
          item1:{
            color:colorList[1]
          },
          item2:{
            color:colorList[2]
          },
          item3:{
            color:colorList[3]
          }
        }
      },
      labelLayout:{
        align:'center'
      },
      labelLine: {
        show: false
      },
      silent: true,
      data: pieData,
    },
    {
      name: "外边框",
      type: "pie",
      clockwise: false, //顺时加载
      hoverAnimation: false, //鼠标移入变大
      center: ["40%", "50%"],
      radius: '38%',
      label: {
        normal: {
          show: false,
        },
      },
      itemStyle: {
            color: {
            type: "radial",
            x: 0.5,
            y: 0.5,
            r: 0.5,
            colorStops: [
              {
                offset: 0,
                color: "rgba(17,25,37,0.02)", // 0% 处的颜色
              },
              {
                offset: 0.5,
                color: "rgba(17,25,37,0.2)", // 50% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(0,239,255,0.3)", // 100% 处的颜色
              },
            ],
            globalCoord: false, // 缺省为 false
          }
      },
      data: [
        {
          value: 100
        },
      ],
    }
  ],
})
setTimeout(() => {
  loading.value = false
//   options.value.series[0].data = props.yData
}, 300);
</script>

<style lang="scss" scoped>
.charts {
  width: 100%;
  height: 100%;
}
</style>