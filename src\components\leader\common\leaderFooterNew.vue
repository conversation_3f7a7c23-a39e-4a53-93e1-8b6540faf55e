<template>
  <footer>
    <ul>
      <li
        v-for="(it, i) of btns"
        :key="i"
        :style="{
          background:
            activaIdx === i
              ? `url(${it.activeBg}) no-repeat center`
              : `url(${it.normalBg}) no-repeat center`,
        }"
        :class="{ active: activaIdx === i }"
      >
        <img v-if="activaIdx !== i" class="img_" :src="it.normalIcon" alt="" />
        <img v-else class="img_" :src="it.activeIcon" alt="" />
        <span @click="handleClick(i)">{{ it.name }}</span>
        <!-- <div class="popups" v-show="false"></div> -->
      </li>
    </ul>
  </footer>
</template>

<script>
export default {
  name: 'LeaderFooter',
  props: {
    btns: {
      type: Array,
      default() {
        return [
          {
            normalIcon: require('@/assets/shzl/footer/n_ryll_icon.png'),
            activeIcon: require('@/assets/shzl/footer/a_ryll_icon.png'),
            normalBg: require('@/assets/shzl/footer/n_btn_bg.png'),
            activeBg: require('@/assets/shzl/footer/a_btn_bg.png'),
            name: '人员力量',
            children: [{}],
          },
          {
            normalIcon: require('@/assets/shzl/footer/n_spjk_icon.png'),
            activeIcon: require('@/assets/shzl/footer/a_spjk_icon.png'),
            normalBg: require('@/assets/shzl/footer/n_btn_bg.png'),
            activeBg: require('@/assets/shzl/footer/a_btn_bg.png'),
            name: '视频监控',
          },
          // {
          //   normalIcon: require('@/assets/shzl/footer/n_zdcs_icon.png'),
          //   activeIcon: require('@/assets/shzl/footer/a_zdcs_icon.png'),
          //   normalBg: require('@/assets/shzl/footer/n_btn_bg.png'),
          //   activeBg: require('@/assets/shzl/footer/a_btn_bg.png'),
          //   name: '网格力量',
          // },
          {
            normalIcon: require('@/assets/shzl/footer/n_cssx_icon.png'),
            activeIcon: require('@/assets/shzl/footer/a_cssx_icon.png'),
            normalBg: require('@/assets/shzl/footer/n_btn_bg.png'),
            activeBg: require('@/assets/shzl/footer/a_btn_bg.png'),
            name: '城市事件',
          },
        ]
      },
    },
    activaIdx: {
      type: Number,
      default: -1,
    },
  },
  methods: {
    handleClick(i) {
      this.$emit('mark', i)
    },
  },
}
</script>

<style lang="less" scoped>
footer {
  display: grid;
  place-items: center;
  width: 3840px;
  height: 124px;
  position: absolute;
  left: 0;
  bottom: 0;
  background: url(~@/assets/csts/bg5.png) no-repeat;
  z-index: 999;
  ul {
    display: flex;
    gap: 46px;
    li {
      position: relative;
      width: 157px;
      height: 76px;
      // line-height: 59px;
      margin-top: 17px;
      display: flex;
      // align-items: center;
      span {
        font-size: 24px;
        font-family: YouSheBiaoTiHei;
        color: #e9fffe;
        // line-height: 31px;
        background: linear-gradient(180deg, #ffffff 0%, #2f8af4 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        cursor: pointer;
        line-height: 50px;
        // margin-left: 20px;
      }
      &.active {
        span {
          font-size: 24px;
          font-family: YouSheBiaoTiHei;
          color: #edfffd;
          line-height: 50px;
          background: linear-gradient(180deg, #fdfeff 0%, #cbedff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        // &::after {
        //   content: '';
        //   position: absolute;
        //   width: 93px;
        //   height: 28px;
        //   left: 50%;
        //   transform: translateX(-50%);
        //   bottom: -15px;
        //   background: url(~@/assets/csts/btn_light.png) no-repeat;
        // }
      }
      .img_ {
        width: 29px;
        height: 30px;
        margin: 10px 0 0 20px;; 
      }
      // .popups {
      //   position: absolute;
      //   left: 50%;
      //   top: -264px;
      //   transform: translateX(-50%);
      //   width: 156px;
      //   height: 243px;
      //   background: url(~@/assets/csts/popups.png) no-repeat;
      //   transition: all 2s;
      // }
    }
  }
}
</style>
