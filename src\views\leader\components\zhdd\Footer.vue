<template>
  <div class="wrap">
    <ul>
      <li v-for="(it, i) of list" :key="i" @click="marker(i)">
        <div class="icon" :style="{ backgroundImage: `url(${it.icon})` }"></div>
        <div class="base"></div>
        <div class="effect"></div>
        <div class="cont">
          <div class="label">{{ it.label }}</div>
          <div class="count" v-if="it.count">{{ it.count }}</div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'ZhddFooter',
  data() {
    return {
      list: [
        {
          icon: require('@/assets/foot/icon1.png'),
          label: '事件',
          count: 136
        },
        {
          icon: require('@/assets/foot/icon2.png'),
          label: '周边资源',
          count: 0
        },
        {
          icon: require('@/assets/foot/icon3.png'),
          label: '视频会商',
          count: 0
        },
        {
          icon: require('@/assets/foot/icon4.png'),
          label: '视频监控',
          count: 1200
        }
      ]
    }
  },
  methods: {
    marker(i) {
      this.$emit('marker', i)
    }
  }
}
</script>

<style lang="less" scoped>
.wrap {
  position: absolute;
  bottom: 10px;
  left: 0;
  width: 1920px;
  height: 141px;
  display: grid;
  place-items: center;
  z-index: 1000;
  ul {
    display: flex;
    gap: 64px;
    height: 100%;
    li {
      position: relative;
      width: 133px;
      cursor: pointer;
      .icon {
        position: absolute;
        width: 38px;
        height: 39px;
        left: 47px;
        top: 13px;
        background-repeat: no-repeat;
      }
      .base {
        position: absolute;
        width: 104px;
        height: 97px;
        left: 16px;
        bottom: 0;
        background: url('~@/assets/foot/bg1.png') no-repeat;
      }
      .effect {
        position: absolute;
        width: 57px;
        height: 80px;
        left: 39px;
        top: 0;
        background: url('~@/assets/foot/bg2.png') no-repeat;
      }
      .cont {
        position: absolute;
        width: 133px;
        height: 31px;
        left: 0;
        bottom: 8px;
        background: url('~@/assets/foot/bg3.png') no-repeat;
        display: flex;
        align-items: center;
        justify-content: space-evenly;
        .label,
        .count {
          height: 18px;
          font-size: 18px;
          font-family: PingFangSC, PingFang SC;
          line-height: 21px;
          background: linear-gradient(180deg, #daefff 0%, #b3daff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
      &:hover {
        .icon {
          animation: move infinite 3s ease-in-out;
          @keyframes move {
            0% {
              transform: rotateY(0);
            }
            50% {
              transform: rotateY(180deg);
            }
            100% {
              transform: rotateY(360deg);
            }
          }
        }
      }
    }
  }
}
</style>
