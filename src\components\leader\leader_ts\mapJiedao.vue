<template>
  <div class="camera_box">
    <div class="title">
      <p>庄周街道</p>
      <div class="del" @click="close"></div>
    </div>
    <div class="content">
      <div class="item_">
        <p>123,456<span>人</span></p>
        <img src="@/assets/leader/img/leader_ts/map/line.png" alt="" />
        <p>人口数量</p>
      </div>
      <div class="item_">
        <p>123,456<span>万元</span></p>
        <img src="@/assets/leader/img/leader_ts/map/line.png" alt="" />
        <p>地区生产总值</p>
      </div>
      <div class="item_">
        <p>127<span>人</span></p>
        <img src="@/assets/leader/img/leader_ts/map/line.png" alt="" />
        <p>党员数量</p>
      </div>
      <div class="item_">
        <p>124<span>家</span></p>
        <img src="@/assets/leader/img/leader_ts/map/line.png" alt="" />
        <p>企业数量</p>
      </div>
    </div>
  </div>
</template>

<script type="text/ecmascript-6">
import hlsVideo from '@/components/leader/hlsVideo'

export default {
  name: 'mapCamera',
  components: {
    hlsVideo,
  },
  data() {
    return {}
  },
  created() {},
  mounted() {},
  methods: {
    close() {
      this.$emit('close')
    },
  },
}
</script>

<style lang="scss" scoped>
@font-face {
  font-family: FZSKJWGB1;
  src: url(~@/assets/leader/font/FZSKJWGB1.ttf);
}
.camera_box {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 101;
  width: 351px;
  height: 255px;
  background: url(~@/assets/leader/img/leader_ldzh/map/map_ry_bg.png) no-repeat;
  background-size: 100% 100%;
  padding: 0 0 28px 0;
  // border: 1px solid red;
  .title {
    margin: 20px 29px 0px 31px;
    background: url(~@/assets/leader/img/leader_ldzh/map/map_sj_title.png) no-repeat;
    background-size: 100% 100%;
    width: 291px;
    height: 33px;
    position: relative;
    p {
      font-size: 18px;
      font-family: FZSKJWGB1;
      font-weight: normal;
      color: #22fcff;
      height: 33px;
      letter-spacing: 1px;
      text-shadow: 0px 0px 4px #00353a;
      background: linear-gradient(180deg, #ffffff 0%, #01c3ff 100%);
      -webkit-background-clip: text;
      // -webkit-text-fill-color: transparent;
    }
    .del {
      background: url(~@/assets/leader/img/leader_ldzh/map/map_sj_del.png) no-repeat;
      background-size: 100% 100%;
      width: 29px;
      height: 36px;
      position: absolute;
      right: -12px;
      bottom: 0;
    }
  }
  .content {
    // width: 310px;
    height: 174px;
    // margin: 7px 0 0 21px;
    display: flex;
    justify-content: space-between;
    // align-items: center;
    flex-wrap: wrap;
    border: 1px solid red($color: #000000);
    padding: 26px 48px 0px 45px;
    .item_ {
      display: flex;
      flex-direction: column;
      & p:first-of-type {
        font-size: 22px;
        font-family: DINAlternate-Bold, DINAlternate;
        font-weight: bold;
        color: #ffffff;
        line-height: 26px;
        text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.5);
        background: linear-gradient(180deg, #ffffff 0%, #ffcb00 67%, #ff8704 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        span {
          font-size: 14px;
        }
      }
      & p:last-of-type {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
      }
      &:nth-of-type(2) p:first-of-type,
      &:nth-of-type(4) p:first-of-type {
        font-size: 22px;
        font-family: DINAlternate-Bold, DINAlternate;
        font-weight: bold;
        color: #ffffff;
        line-height: 26px;
        text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.5);
        background: linear-gradient(180deg, #ffffff 0%, #00ff1b 67%, #04ff32 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }
}
</style>
