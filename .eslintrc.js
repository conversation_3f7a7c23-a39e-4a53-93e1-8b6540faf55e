module.exports = {
  root: true,
  env: {
    node: true
  },
  extends: ['plugin:vue/essential', 'eslint:recommended', '@vue/prettier'],
  parserOptions: {
    parser: 'babel-eslint'
  },
  rules: {
    // "prettier/prettier": [
    //   "error",
    //   {
    //     endOfLine: "auto", // 兼容CRLF与LF换行
    //   },
    // ],
    'prettier/prettier': 'off',
    'no-console': process.env.NODE_ENV === 'production' ? 'off' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-unused-vars': 'off',
    'no-empty': 'off',
    'no-undef': 'off',
    'vue/no-unused-vars': 'off',
    'vue/no-unused-components': 'off',
    'vue/require-v-for-key': 'off',
    'vue/valid-v-for': 'off',
    'no-async-promise-executor' : 'off',
    // 'no-misleading-character-class' : 'off',
    // 'no-useless-catch' : 'off'
  },
  globals: {
    AgoraRTC: true,
    client: true,
    appId: true,
    channelKey: true,
    videoSource: true,
    audioSource: true,
    localStream: true,
    L: true,
  }
};
