<template>
  <div class="ai_waring">
    <div class="title">
      <span>AI监测预警</span>
      <img @click="closeEmitai" class="close_btn" src="@/assets/shzl/map/close_btn.png" alt="" />
    </div>
    <div class="content">
      <div><span>预警场景：</span><span>暴雨倾盆，道路积水严重</span></div>
      <div><span>预警地址：</span><span>鼓楼区汉中路与牌楼巷交叉口</span></div>
      <div><span>预警时间：</span><span>2022-12-31 15:34</span></div>
      <div><span>所属网格：</span><span>鼓楼区网格四</span></div>
      <div class="pic">
        <!-- <video muted controls autoplay loop="loop">
          <source :src="videoUrl" type="video/mp4" />
        </video> -->
        <LivePlayer class="video_" :videoUrl="videoUrl" fluent autoplay live stretch></LivePlayer>
      </div>
      <div class="btns">
        <div @click="rwMethod">任务指派</div>
        <div @click="spthMethod">连线网格员</div>
      </div>
    </div>
  </div>
</template>

<script>
import hlsVideo from '@/components/leader/hlsVideo'
import LivePlayer from '@liveqing/liveplayer'
export default {
  components: {
    hlsVideo,
    LivePlayer
  },
  data() {
    return {
      // testUrl1: 'http://**********:8087/pulllive/pulllive_32010199001329000011/hls.m3u8',
      testUrl1: 'http://**********:8088/firstfloor/stream1/hls.m3u8',
      videoUrl: require('@/assets/leader/video/video1.mp4')
    }
  },
  methods: {
    closeEmitai() {
      this.$emit('closeEmitai')
    },
    rwMethod() {
      this.$emit('rwMethod')
    },
    spthMethod() {
      this.$emit('spthMethod')
    },
    step() {
      window.open('http://**********:8090/zhddcp-dist/#/zsdd', '_blank')
    }
  }
}
</script>

<style lang="less" scoped>
.ai_waring {
  width: 413px;
  position: relative;
  background: url(~@/assets/shzl/map/ai_popBg.png) no-repeat center / 100% 100%;
  background-size: 100% 100%;
  z-index: 1000;
  padding-top: 15px;
  .title {
    background: url(~@/assets/shzl/map/title_bg.png) no-repeat center;
    background-size: 100% 100%;
    width: 375px;
    height: 47px;
    margin: 0 auto 0;
    line-height: 47px;
    text-align: left;
    text-indent: 27px;
    font-size: 18px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #ffffff;
    position: relative;
    span {
      background: linear-gradient(180deg, #ffffff 0%, #2aa6ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .close_btn {
      position: absolute;
      top: 11px;
      right: 25px;
      cursor: pointer;
    }
  }
  .content {
    padding: 20px 50px 46px 48px;
    // border: 1px solid red;
    & > div {
      display: flex;
      justify-content: space-between;
      &:not(:last-of-type) {
        margin-bottom: 14px;
      }
      & span:first-of-type {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
      }
      & span:last-of-type {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
      }
    }
    .pic {
      margin: 18px auto 0;
      video {
        width: 100%;
        height: 100%;
        object-fit: cover;
        &:not(:root):fullscreen {
          object-fit: contain;
        }
      }
      .video_ {
        width: 100%;
        height: 100%;
      }
    }
    .btns {
      margin: 15px auto 0;
      display: flex;
      justify-content: space-between;
      & > div {
        width: 150px;
        height: 33px;
        // border: 1px solid red;
        background: url(~@/assets/shzl/map/btn_bg.png) no-repeat center / 100% 100%;
        line-height: 33px;
        text-align: center;
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        cursor: pointer;
      }
    }
  }
}
</style>
