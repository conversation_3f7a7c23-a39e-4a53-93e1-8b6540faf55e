<!--
 * @Author: fanjialong <EMAIL>
 * @Date: 2023-04-18 16:04:46
 * @LastEditors: fanjialong <EMAIL>
 * @LastEditTime: 2023-05-25 09:33:10
 * @FilePath: \hs_dp\src\components\shzl\sphsPop.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="txdd_box">
    <div class="szyyPop">
      <img src="@/assets/img/video_close.png" alt class="close" @click="close" />
      <div class="title">
        <div class="title_name">
          <slot name="title1">通讯调度</slot>
        </div>
      </div>
      <div class="center">
        <div class="center_left">
          <div class="center_left1">
            <div class="center_item1">
              <slot name="title2">语音接入</slot>
            </div>
            <div class="center_item2">
              <el-input v-model="cameraValue" placeholder="搜索"></el-input>
              <img src="@/assets/shzl/map/camera/ss.png" alt />
            </div>
            <div class="center_item3">
              <el-tree
                :data="leftTreeData"
                :check-on-click-node="true"
                show-checkbox
                node-key="id"
                :props="defaultProps"
                ref="videoTreeRef"
                default-expand-all
                :filter-node-method="filterNode"
                :default-checked-keys="checkedArr"
              ></el-tree>
            </div>
            <div class="center_item4">
              <div @click="resetPeople">
                <slot name="title3">重置</slot>
              </div>
              <div @click="choosePeople">
                <slot name="title4">加入会议</slot>
              </div>
            </div>
          </div>
        </div>
        <div class="center_right">
          <div class="center_right1">{{dayjs(new Date).format('YYYY-MM-DD HH:mm:ss')}}</div>
          <div class="center_right2" id="rhtx_BoxId" v-if="rhtxBoxShow"></div>
        </div>
      </div>
    </div>
    <transition name="fade">
      <div class="bg-header"></div>
    </transition>
  </div>
</template>

<script>
import hlsVideo from '@/components/leader/hlsVideo'
import rtc from '@/rhtx/core/index'
// api
import { closeMeet } from '@/api/hs/hs.api.js'
export default {
  props: {
    leftTreeData: {
      type: Array,
      default: () => [
        // {
        //   id: 1,
        //   label: '建邺区',
        //   children: [
        //     {
        //       id: 4,
        //       label: '江心洲街道',
        //       children: [
        //         {
        //           id: 9,
        //           label: '视频设备1'
        //         },
        //         {
        //           id: 10,
        //           label: '视频设备2'
        //         }
        //       ]
        //     },
        //     {
        //       id: 5,
        //       label: '沙洲街道',
        //       children: [
        //         {
        //           id: 11,
        //           label: '视频设备1'
        //         },
        //         {
        //           id: 12,
        //           label: '视频设备2'
        //         }
        //       ]
        //     },
        //     {
        //       id: 6,
        //       label: '和谐社区',
        //       children: []
        //     }
        //   ]
        // }
      ]
    },
    initCheckedPeo: {
      type: Array,
      default: () => []
    }
  },
  components: {
    hlsVideo
  },
  name: 'txddPop',
  data() {
    return {
      title: 1,

      defaultProps: {
        children: 'children',
        label: 'label'
      },
      company2: 2,
      optionsList1: [
        {
          value: 1,
          label: '标清'
        },
        {
          value: 2,
          label: '高清'
        },
        {
          value: 3,
          label: '超清'
        }
      ],
      cameraValue: '',
      testUrl1: 'http://10.2.32.45:8088/firstfloor/stream1/hls.m3u8',
      testUrl2: 'http://10.2.32.45:8088/sixthfloor/stream1_01/hls.m3u8',
      testUrl3: 'http://10.2.32.45:8088/sixthfloor/stream1_01/hls.m3u8',
      testUrl4: 'http://10.2.32.45:8088/firstfloor/stream2/hls.m3u8',
      rhtxBoxShow: false,
      tempVideoHy: null,
      checkedArr: []
    }
  },
  created() {
    window.sessionStorage.setItem('oldUers', [])
  },
  mounted() {
    // console.log('leftTreeData', this.leftTreeData)
    // console.log('initCheckedPeo', this.initCheckedPeo)
    this.$nextTick(() => {
      // 默认有选中的人进行视频通话
      if (this.initCheckedPeo.length > 0) {
        this.checkedArr = this.initCheckedPeo //左侧tree默认选中传入的人员
        // 默认有选中的人保存到sessionStorage，再次点击tree加入会议，与sessionStorage的人员筛选下
        let oldUers = this.initCheckedPeo
        window.sessionStorage.setItem('oldUers', JSON.parse(JSON.stringify(oldUers)))
        this.testBtnHySp(oldUers)
      }
    })
  },
  methods: {
    async close() {
      try {
        if (this.tempVideoHy && window.sessionStorage.getItem('tempMeetVoiceNum')) {
          // 关闭之前调用挂断
          this.tempVideoHy.terminate()
          window.sessionStorage.setItem('tempMeetVoiceNum', '')
          window.sessionStorage.setItem('oldUers', [])
          this.$emit('closeEmit')
          // const res = await closeMeet(window.sessionStorage.getItem('tempMeetVoiceNum'))
          // if (res.res_code == '0') {
          //   window.sessionStorage.setItem('tempMeetVoiceNum', '')
          //   window.sessionStorage.setItem('oldUers', [])
          //   this.$emit('closeEmit')
          // } else {
          //       window.sessionStorage.setItem('tempMeetVoiceNum', '')
          //   window.sessionStorage.setItem('oldUers', [])
          //   this.$emit('closeEmit')
          // }
        } else {
          window.sessionStorage.setItem('tempMeetVoiceNum', '')
          window.sessionStorage.setItem('oldUers', [])
          this.$emit('closeEmit')
        }
      } catch (error) {
        window.sessionStorage.setItem('tempMeetVoiceNum', '')
        window.sessionStorage.setItem('oldUers', [])
        this.$emit('closeEmit')
      }
    },
    titleShow(index) {
      this.title = index
    },
    changeSelect1() {},
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    resetPeople() {
      this.$refs.videoTreeRef.setCheckedKeys([])
      window.sessionStorage.setItem('oldUers', [])
    },
    choosePeople() {
      // console.log(222)
      // console.log(this.$refs.videoTreeRef.getCheckedKeys(true))
      if (!this.tempVideoHy) {
        let oldUers = this.$refs.videoTreeRef.getCheckedKeys(true)
        window.sessionStorage.setItem('oldUers', JSON.parse(JSON.stringify(oldUers)))
        this.testBtnHySp(oldUers)
      } else if (this.tempVideoHy) {
        let newUser = this.filterArr(
          window.sessionStorage.getItem('oldUers').split(','),
          this.$refs.videoTreeRef.getCheckedKeys(true)
        )
        console.log('newUser', newUser)
        this.addUsers(newUser)
      }
    },
    async testBtnHySp(users) {
      this.rhtxBoxShow = true
      rtc.meet.mounted('rhtx_BoxId')
      this.tempVideoHy = await rtc.meet.callPopTempVoice({ users: users })
      this.tempVideoHy.on('meetOff', () => {
        console.log('会议关闭')
        this.rhtxBoxShow = false
        this.tempVideoHy = null
        this.resetPeople()
      })
    },
    addUsers(newUser) {
      console.log('addUsers', newUser)
      this.tempVideoHy.addUser(newUser)
      let allUsers = [...window.sessionStorage.getItem('oldUers').split(','), ...newUser]
      window.sessionStorage.setItem('oldUers', JSON.parse(JSON.stringify(allUsers)))
    },
    // js找出两个数组中不同元素
    filterArr(arr1, arr2) {
      return arr1.concat(arr2).filter((t, i, arr) => {
        return arr.indexOf(t) === arr.lastIndexOf(t)
      })
    }
  },
  watch: {
    cameraValue(val) {
      this.$refs.jiedaoTree.filter(val)
    }
  }
}
</script>
<style lang="scss" scoped>
@import url('~@/assets/rhtx/txdd.less');
.bg-header {
  position: absolute;
  width: 100%;
  height: 1080px;
  background: rgba(2, 14, 50, 0.75);
  z-index: 999999;
  top: 0;
  left: 0;
}
.szyyPop {
  width: 1242px;
  height: 749px;
  background: rgba(9, 19, 34, 0.9);
  box-shadow: 0px 0px 22px 0px rgba(11, 54, 138, 0.35), 0px 2px 8px 0px rgba(0, 0, 0, 0.7),
    inset 0px 0px 8px 0px rgba(17, 146, 214, 0.35);
  border-radius: 11px;
  border: 1px solid #015c8c;
  position: absolute;
  z-index: 101;
  left: 50%;
  top: 38%;
  transform: translate(-50%, -40%);
  z-index: 99999999;
  .close {
    position: absolute;
    width: 23px;
    height: 23px;
    top: 24px;
    right: 24px;
    cursor: pointer;
  }
  .title {
    width: 634px;
    height: 76px;
    line-height: 32px;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
    background-image: url(~@/assets/img/video_title.png);
    .title_name {
      background-clip: text;
      font-size: 36px;
      font-family: PangMenZhengDao;
      color: #ffffff;
      line-height: 76px;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .center {
    width: 1200px;
    height: 600px;
    padding: 28px 0 0 0;
    .type_ {
      display: flex;
      align-items: center;
      margin: 17px 0 8px 14px;
      & > div {
        width: 76px;
        height: 30px;
        line-height: 30px;
        font-size: 13px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ccf4ff;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        background-position: center;
        background-image: url(~@/assets/shzl/map/camera/type_bg.png);
        &.active {
          background-image: url(~@/assets/shzl/map/camera/type_bg_active.png);
        }
        &:first-of-type {
          margin-right: 10px;
        }
      }
    }
    .center_left {
      width: 300px;
      float: left;
      margin-left: 15px;
      .center_left1 {
        width: 300px;
        height: 595px;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        background-position: center;
        background-image: url(~@/assets/img/video_leftk.png);
        margin: 8px 0 0 0;
        padding: 12px 0 0 0;
        .center_item1 {
          margin: 0 0 13px 25px;
          font-size: 20px;
          font-family: YouSheBiaoTiHei;
          color: #ffffff;
          line-height: 26px;
          // text-shadow: 0px 0px 4px #00575d;
          background: linear-gradient(180deg, #eeeeee 0%, #43f4ff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          text-align: left;
        }
        .center_item2 {
          width: 256px;
          height: 34px;
          line-height: 34px;
          color: #fff;
          text-align: left;
          background: rgba(0, 74, 143, 0.4)
            linear-gradient(
              360deg,
              rgba(181, 223, 248, 0) 0%,
              rgba(29, 172, 255, 0.29) 100%,
              #ffffff 100%
            );
          border-radius: 4px;
          border: 1px solid rgba(0, 162, 255, 0.6);
          position: relative;
          margin: 0 auto;
          img {
            position: absolute;
            right: 9px;
            top: 10px;
          }
        }
        .center_item3 {
          margin: 20px auto 0;
          height: 387px;
          overflow-y: auto;
          width: 203px;
          &::-webkit-scrollbar {
            /*滚动条整体样式*/
            // width: 2px;
            background: transparent;
          }
          // &::-webkit-scrollbar-thumb {
          //   /*滚动条里面小方块*/
          //   border-radius: 2px;
          //   box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
          //   background: rgb(45, 124, 228);
          //   height: 20px;
          // }
        }
        .center_item4 {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 203px;
          font-size: 16px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 33px;
          margin: 42px auto 0;
          // position: relative;
          // z-index: 999;
          cursor: pointer;
          & div:first-of-type {
            width: 69px;
            height: 33px;
            background: url(~@/assets/img/video_reset.png) no-repeat;
          }
          & div:last-of-type {
            width: 99px;
            height: 33px;
            background: url(~@/assets/img/video_confirm.png) no-repeat;
          }
        }
      }
    }
    .center_right {
      width: 860px;
      height: 580px;
      float: left;
      margin-left: 15px;
      .center_right1 {
        text-align: left;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 20px;
        letter-spacing: 1px;
      }
      .center_right2 {
        width: 860px;
        height: 595px;
        padding: 3px 0;
        // border: 1px solid #eee;
      }
    }
  }
}
::v-deep .el-tree {
  background: transparent;
  color: #f6feff;
  .el-tree-node__content:hover {
    background: transparent !important;
  }
  .el-checkbox__inner {
    background: transparent !important;
    width: 17px;
    height: 17px;
  }
}
::v-deep .el-tree-node__label {
  font-size: 16px;
}
::v-deep .el-tree-node__content {
  margin-bottom: 10px;
}
::v-deep .el-tree-node:focus > .el-tree-node__content {
  background-color: transparent !important;
}

::v-deep .el-tree-node__content:hover {
  background: transparent !important;
}
::v-deep .el-select {
  width: 71px;
  height: 30px;
  .el-input__inner {
    font-size: 13px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ccf4ff;
    line-height: 18px;
    background: rgba(0, 74, 143, 0.4)
      linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(0, 162, 255, 0.08) 100%, #ffffff 100%);
    border-radius: 15px;
    border: 1px solid rgba(0, 162, 255, 0.6);
    padding: 0 20px 0 12px;
  }
}
::v-deep .el-input__inner {
  background-color: transparent !important;
  border: none;
  color: #ccf4ff;
  height: 34px;
}
</style>
