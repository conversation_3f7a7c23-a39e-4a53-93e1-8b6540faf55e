import axios from 'axios'
import qs from 'qs'
import { bjnjUrl, messageGroupUrl, appServiceInitAddressUrl } from './const'
import store from '@/store/leader/index.js'

//添加请求拦截器
axios.interceptors.request.use(
	(config) => {
		// let url = window.location.href
		// // 获取参数部分
		// let params = url.split('?')[1]
		// // 将参数部分转换为对象
		// let paramsObj = {}
		// if (params) {
		//   let paramsArr = params.split('&')
		//   for (let i = 0; i < paramsArr.length; i++) {
		//     let param = paramsArr[i].split('=')
		//     paramsObj[param[0]] = param[1]
		//   }
		// }
		// // 获取指定参数的值
		// let paramValue = paramsObj['token']
		// if (paramValue) {
		//   localStorage.setItem("token", decodeURI(paramValue))
		// }
		// console.log('localStorage.token',localStorage.token)
		// console.log('localStorage.token0',paramValue)
		// console.log('localStorage.token1',localStorage.token)
		// if (config.url.indexOf('http://njzhddqd.devops.com/sjczcq') != -1) {

		console.log('useIdaasToken--==', config.headers.useIdaasToken)
		if (config.url.indexOf(bjnjUrl) != -1) {
			if (config.headers.useIdaasToken) {
				config.headers.Authorization = 'Bearer ' + store.state.messageToken
			} else {
				config.headers.Authorization = localStorage.token
			}
		} else if (config.url.indexOf(messageGroupUrl) != -1 || config.url.indexOf(appServiceInitAddressUrl) != -1) {
			config.headers.Authorization = 'Bearer ' + store.state.messageToken
		} else {
			config.headers.Authorization = window.sessionStorage.getItem('token')
		}
		// console.log('localStorage.token3',config.headers.Authorization)
		return config
	},
	(error) => {
		return Promise.reject(error)
	},
)
//添加响应拦截器
axios.interceptors.response.use(
	(response) => {
		return response
	},
	(error) => {
		return Promise.resolve(error.response)
	},
)
// axios.defaults.baseURL = process.env.VUE_APP_BASE_URL;
axios.defaults.headers.post['Content-Type'] = 'application/json'
axios.defaults.headers.post['X-Requested-With'] = 'XMLHttpRequest'
axios.defaults.timeout = 120000

function checkStatus(response) {
	return new Promise((resolve, reject) => {
		if (response && (response.status === 200 || response.status === 304 || response.status === 400)) {
			resolve(response.data)
		} else {
			reject({
				state: '0',
				message: '请求服务出现问题',
			})
		}
	})
}
export default {
	post(url, params, headers) {
		if (headers) {
			for (const key in headers) {
				console.log('headers key', key)
				console.log('headers value', headers[key])
				axios.defaults.headers.post[key] = headers[key]
			}
		}
		return axios({
			method: 'post',
			url,
			data: params,
		}).then((response) => {
			return checkStatus(response)
		})
	},
	get(url, params, headers) {
		return axios({
			method: 'get',
			url,
			params,
			headers,
		}).then((response) => {
			return checkStatus(response)
		})
	},
	delete(url, params, headers) {
		// params = qs.stringify(params);
		return axios({
			method: 'delete',
			url,
			params,
			headers,
		}).then((response) => {
			return checkStatus(response)
		})
	},
	postForm(url, params, headers) {
		// axios.defaults.headers.post['Content-Type'] = 'x-www-form-urlencoded;charset=UTF-8';
		return axios({
			headers: {
				'Content-Type': 'application/x-www-form-urlencoded',
			},
			method: 'post',
			url,
			data: qs.stringify(params),
		}).then((response) => {
			return checkStatus(response)
		})
	},
}
