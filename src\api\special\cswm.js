import http from '@/utils/special/request';
import { finaUrl } from '@/utils/special/const';

// 归口
export const guikou = () => {
  return http.get(
    finaUrl + '/api/emergencyController/orderClassify?type=2'+'&level=2'
  );
};
// 区域排名
export const areaRank = () => {
  return http.get(
    finaUrl + '/api/civilizedCityController/civilizedCityAreaSort'
  );
};
//  事件数量趋势变化
export const sjqsSjs = () => {
  return http.get(
    finaUrl + '/api/civilizedCityController/civilizedCityTimeStatistics?type=1'
  );
};
// 事件办结率趋势变化
export const sjqsBjl = () => {
  return http.get(
    finaUrl + '/api/civilizedCityController/civilizedCityFinishPreTimeStatistics?type=1'
  );
};
// 标题
export const titleJk = () => {
  return http.get(
    finaUrl + '/api/civilizedCityController/civilizedCityStatistics'
  );
};

// 事件类型区域占比 柱状图
export const sjlxAreaBar = (classifyId) => {
  return http.get(
    finaUrl + '/api/civilizedCityController/civilizedCityAreaStatistics?type=2'+'&level=2'+'&classifyId='+classifyId
  );
};

// 事件类型占比 饼图（类型）
export const sjlxTypePie = (classifyId) => {
  return http.get(
    finaUrl + '/api/civilizedCityController/civilizedCityTypeStatistics?type=1'+'&level=2'+'&classifyId='+classifyId
  );
};

// 事件类型占比 饼图（区域）
export const sjlxAreaPie = (classifyId) => {
  return http.get(
    finaUrl + '/api/civilizedCityController/civilizedCityAreaStatistics?type=1'+'&level=2'+'&classifyId='+classifyId
  );
};

// 地图打点
export const mapPoint = (classifyId) => {
  return http.get(
    finaUrl + '/api/civilizedCityController/civilizedCityList?level=2'+'&dateType=2'+'&classifyId='+classifyId
  );
};

// 地图打点详情
export const mapPointXq = (id) => {
  return http.get(
    finaUrl + '/api/externalOrder/getOrderInfo?id='+id
  );
};

export const sendMessage = (data) => {
  return http.post(
    finaUrl + '/api/sms/sendSms',data
  );
};

// 地图打点 城市文明-城市部件
export const mapPointCity = () => {
  return http.get(
    finaUrl + '/api/civilizedCityController/civilizedCityList?dateType=2'
  );
};


