<template>
  <div class="charts">
    <swiper class="swiper" :options="swiperOption" ref="myBotSwiper">
      <swiper-slide
        v-for="(item, index) in resultData.sort(
          (a, b) => parseFloat(b.value) - parseFloat(a.value)
        )"
        :key="index"
        class="item"
        @click="clickBar(item, index)"
      >
        <div class="row">
          <div class="name" :style="{ marginBottom: OPTIONS.nameGap + 'px' }">
            <div
              class="rank"
              :style="{
                background: `url(${OPTIONS.img[index]}) no-repeat`
              }"
            ></div>
            <div class="info">
              <div class="label" v-text="item.name" />
              <div class="data">
                <countTo
                  ref="countTo"
                  :startVal="$countTo.startVal"
                  :decimals="$countTo.decimals(item.value)"
                  :endVal="item.value"
                  :duration="$countTo.duration"
                />{{ options.unit[index] }}
              </div>
            </div>
            <div class="percent">
              日环比:
              <countTo
                ref="countTo"
                :startVal="$countTo.startVal"
                :decimals="$countTo.decimals(item.rateVal)"
                :endVal="Number(item.rateVal)"
                :duration="$countTo.duration"
              />%
            </div>
          </div>
          <div
            class="progress"
            :class="'p' + index"
            :style="{
              height: parseFloat(OPTIONS.barHeight) + 'px'
            }"
          >
            <div
              class="cur"
              :style="{
                borderColor: OPTIONS.color[index],
                background: `linear-gradient(to right, #000 , ${OPTIONS.color[index]})`,
                width: item.rate
              }"
            >
              <particle :color="OPTIONS.color[index]" :dot-num="OPTIONS.dotNum" />
            </div>
          </div>
        </div>
      </swiper-slide>
    </swiper>
  </div>
</template>

<script>
import particle from './particle.vue'
import { cloneDeep } from 'lodash'
export default {
  name: 'EffectBar-swiper',
  components: {
    particle
  },
  props: {
    // 图表数据
    data: {
      type: Array,
      default: () => {
        const data = []
        for (let i = 0; i < 10; i++) {
          data.push(['系列' + (i + 1), 10 + Math.floor(Math.random() * 80)])
        }
        return [['product', '系列名'], ...data]
      }
    },
    // 配置项
    options: {
      type: Object,
      default: () => {
        return {
          unit: ['车次', '条', '起', '起']
        }
      }
    }
  },
  data() {
    let color = ['#00C6FA', '#3ADCD6', '#E8BB60', '#EF5C39']
    while (color.length !== this.data.length - 1) {
      color.push(color[color.length - 1])
    }
    return {
      defaultOptions: {
        color: color,
        img: [
          require('@/assets/csts/icon25.png'),
          require('@/assets/csts/icon26.png'),
          require('@/assets/csts/icon27.png'),
          require('@/assets/csts/icon28.png')
        ],
        barHeight: 10,
        dotNum: 50,
        fontSize: 18,
        nameGap: 6
      },
      swiperOption: {
        autoHeight: true,
        direction: 'vertical',
        spaceBetween: 0,
        // autoplay: {
        //   delay: 2500,
        //   disableOnInteraction: false,
        //   autoplayDisableOnInteraction: false
        // },
        autoplay: false,
        slidesPerView: 'auto',
        grabCursor: true,
        autoplayDisableOnInteraction: false,
        mousewheelControl: true
      }
    }
  },
  computed: {
    OPTIONS() {
      return {
        ...this.defaultOptions,
        ...this.options
      }
    },
    resultData() {
      const data = cloneDeep(this.data)
        .filter((item, index) => index > 0)
        .map(item => {
          return {
            name: item[0],
            value: item[1]
          }
        })
      const max =
        data.reduce((pre, item) => {
          return parseFloat(pre.value) > parseFloat(item.value) ? pre : item
        }).value * 1.1
      data.forEach(item => {
        item.rate = ((parseFloat(item.value) / max) * 100).toFixed(2) + '%'
        item.rateVal = ((parseFloat(item.value) / max) * 100).toFixed(0)
      })
      return data
    },
    myBotSwiper() {
      return this.$refs.myBotSwiper.$swiper
    }
  },
  watch: {
    options: {
      immediate: true,
      deep: true,
      handler(newVal) {
        const { color } = newVal
        if (color) {
          while (color.length !== this.data.length - 1) {
            color.push(color[color.length - 1])
          }
        }
      }
    },
    data: {
      immediate: false,
      deep: true,
      handler() {
        const { color } = this.OPTIONS
        if (color) {
          while (color.length !== this.data.length - 1) {
            color.push(color[color.length - 1])
          }
        }
      }
    }
  },
  methods: {
    clickBar(item, index) {
      this.$emit('clickBar', {
        current: item,
        currentIndex: index,
        data: this.data
      })
    },
    swiperStop() {
      this.myBotSwiper.autoplay.stop()
    },
    swiperStart() {
      this.myBotSwiper.autoplay.start()
    }
  }
}
</script>

<style lang="scss" scoped>
.charts {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: space-evenly;
  align-content: space-between;
  .swiper {
    width: 100%;
    height: 100%;
    padding: 8px 23px;
  }
  .item {
    display: flex;
    margin-bottom: 5px;
    justify-content: center;
    width: 100%;
    .row {
      width: 100%;
      .name {
        display: flex;
        align-items: center;
        height: 28px;
        .info {
          width: 200px;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .rank {
          // font-size: 13px;
          // font-family: SourceHanSansCN-Medium, SourceHanSansCN;
          // font-weight: 500;
          // color: #ffffff;
          // line-height: 21px;
          width: 22px;
          height: 22px;
          margin-right: 8px;
        }
        .label {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 24px;
        }
        .data {
          // margin-left: auto;
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          span {
            font-size: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #ffffff;
            line-height: 28px;
            text-shadow: 0px 0px 1px #00132e;
          }
        }
        .percent {
          margin-left: auto;
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          span {
            font-size: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #ffffff;
            line-height: 28px;
            text-shadow: 0px 0px 1px #00132e;
          }
        }
      }

      .progress {
        display: block;
        flex: 1;
        height: 100%;
        background: rgba(0, 201, 255, 0.14);
        .cur {
          width: 100%;
          border: 1px solid;
          height: 100%;
          overflow: hidden;
          transition: width 0.5s;
        }
      }
    }
  }
}
</style>
