<template>
  <div class="wrap">
    <div class="chart_wrap">
      <ring-per-chart2 :data="chartData"></ring-per-chart2>
    </div>
    <div class="info">
      <div class="icon"></div>
      <ul>
        <li v-for="(it, i) of list" :key="i">
          <div class="label">{{ it.label }}</div>
          <countTo
            ref="countTo"
            :startVal="$countTo.startVal"
            :decimals="$countTo.decimals(it.count)"
            :endVal="it.count"
            :duration="$countTo.duration"
          />
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GridMemberLoginStatus',
  data() {
    return {
      chartData: {
        name: '登录率',
        value: 90
      },
      list: [
        {
          label: '已登录人数',
          count: 1800
        },
        {
          label: '未登录人数',
          count: 200
        }
      ]
    }
  }
}
</script>

<style lang="less" scoped>
.wrap {
  width: 100%;
  height: 100%;
  display: flex;
  .chart_wrap {
    width: 229px;
    height: 100%;
    padding: 0 20px 20px;
  }
  .info {
    flex: 1;
    padding: 29px 0 55px;
    display: flex;
    align-items: center;
    .icon {
      width: 34px;
      height: 102px;
      background: url('~@/assets/zhdd/bg3.png') no-repeat;
      margin-right: 4px;
    }
    ul {
      display: flex;
      flex-direction: column;
      gap: 9px;
      li {
        width: 158px;
        height: 75px;
        padding: 17px 0 0 25px;
        text-align: left;
        background: url('~@/assets/zhdd/bg4.png') no-repeat;
        &:nth-child(2) {
          background: url('~@/assets/zhdd/bg5.png') no-repeat;
        }
        .label {
          height: 20px;
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #8bc7ff;
          line-height: 20px;
        }
        span {
          height: 30px;
          font-size: 26px;
          font-family: DINAlternate-Bold, DINAlternate;
          font-weight: bold;
          color: #ffffff;
          line-height: 30px;
        }
      }
    }
  }
}
</style>
