<template>
  <div class="container">
    <div class="box">
      <p>{{ name1 }}：</p>
      <div class="select">
        <Select v-model="value1" clearable filterable @on-change="handleOption1">
          <Option v-for="item in typeOptions1" :value="item.itemCode" :key="item.itemCode">
            {{
            item.itemValue
            }}
          </Option>
        </Select>
      </div>
    </div>
    <div class="box">
      <p>{{ name2 }}：</p>
      <div class="select">
        <Select v-model="value2" clearable filterable @on-change="handleOption2">
          <Option v-for="item in typeOptions2" :value="item.itemCode" :key="item.itemCode">
            {{
            item.itemValue
            }}
          </Option>
        </Select>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    name1: {
      type: String,
      default: '农机类型'
    },
    name2: {
      type: String,
      default: '基础农机'
    },
    value1: {
      type: String,
      default: ''
    },
    typeOptions1: {
      type: Array,
      default: () => [
        {
          value: '0',
          label: '已处置'
        },
        {
          value: '1',
          label: '未处置'
        }
      ]
    },
    value2: {
      type: String,
      default: ''
    },
    typeOptions2: {
      type: Array,
      default: () => [
        {
          value: '0',
          label: '已处置'
        },
        {
          value: '1',
          label: '未处置'
        }
      ]
    }
  },
  data () {
    return {
      proityValue: '',
      eventTypeOptions: [
        {
          value: '0',
          label: '已处置'
        },
        {
          value: '1',
          label: '未处置'
        }
      ]
    }
  },
  created () { },
  mounted () { },
  watch: {},
  computed: {},
  methods: {
    handleOption1 (e) {
     this.$emit('change',{
      value:e,
      type:'weatherAlarmType'
     })
    },
    handleOption2 (e) {
      this.$emit('change',{
      value:e,
      type:'weatherAlarmLevel'
     })
    }
  },
};
</script>
<style lang="scss" scoped>
.container {
  position: absolute;
  top: 138px;
  left: 529px;
  z-index: 1001;
  width: 255px;
  height: 240px;
  background: url(~@/assets/mskx/bg_tooltip.png) no-repeat;
  background-size: 100% 100%;
  padding: 45px 41px;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  :deep(.ivu-select-input){
    color:#fff!important;
  }
  p {
    // padding-left: 20px;
    height: 20px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 20px;
    text-align: left;
    font-style: normal;
    margin-bottom: 5px;
  }
  .box {
    width: 100%;
    .select {
      // padding-left: 20px;
    }
  }
}
</style>