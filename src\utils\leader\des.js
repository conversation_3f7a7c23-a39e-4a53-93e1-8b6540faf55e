import cryptoJs from 'crypto-js';

// DES加密
export const encryptDes = (message, key) => {
  const KeyHex = cryptoJs.enc.Utf8.parse(key);
  const encrypted = cryptoJs.TripleDES.encrypt(message, KeyHex, {
    mode: cryptoJs.mode.ECB, // ecb模式不需要偏移量
    padding: cryptoJs.pad.Pkcs7
  });

  let hexstr = encrypted.toString(); // 此方式返回base64格式
  //  encrypted.ciphertext.toString() 如果是这么写 那就返回hex格式的密文了
  return hexstr;
};
// DES解密
export const decryptDes = (message, key, ivstr) => {
  const KeyHex = cryptoJs.enc.Utf8.parse(key);
  //第一步把16进制字符串转为WordArray格式
  const WordArray = cryptoJs.enc.Hex.parse(message);
  //第二步把WordArray再转为base64的字符串
  const base64str = cryptoJs.enc.Base64.stringify(WordArray);
  //第三步再进行解密
  const decrypted = cryptoJs.TripleDES.decrypt(base64str, KeyHex, {
    mode: cryptoJs.mode.ECB,
    padding: cryptoJs.pad.Pkcs7,
    iv: cryptoJs.enc.Utf8.parse(ivstr)
  });
  return decrypted.toString(cryptoJs.enc.Utf8);
};
