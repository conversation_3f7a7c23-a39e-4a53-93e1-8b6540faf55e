<template>
  <div class="wrap">
    <div class="table-wrapper" :style="{
      height: `calc(${contentHeight})`
    }">
      <div class="menu">
        <div class="menu-item" :class="{ active: activeIndex === i }" v-for="(it, i) in menuList" :key="i"
          @click="handleMenuClick(i)">{{ it }}kg/s</div>
      </div>

      <div class="table-content" :style="{ height: contentHeight }">
        <swiper class="swiper" :options="swiperOption" ref="myBotSwiper">
          <swiper-slide v-for="(it, i) in dataList" :key="i">
            <div class="container">
              <div class="header">
                <div :class="[i <= 2 ? `num${i}` : 'nums', 'num']">
                  <!-- <div class="num"> -->
                  NO.{{ i + 1 }}
                </div>
                <div class="track" @click="getTrackHistroy(it)">
                  <img src="@/assets/mskx/icon_track.png" />
                  <a>查看轨迹</a>
                </div>
              </div>
              <div class="info">
                <ul>
                  <li>
                    <div>
                      <span class="name">机手姓名：</span>
                      <span class="value" :title="it.ownerName">{{ it.ownerName||'' }}</span>
                    </div>
                    <div>
                      <span class="name">日期：</span>
                      <span class="value">{{ it.date }}</span>
                    </div>
                  </li>
                  <li>
                    <div>
                      <span class="name">作业面积：</span>
                      <span class="value">{{ it.workArea }}亩</span>
                    </div>
                    <div>
                      <span class="name">作业时间：</span>
                      <span class="value">{{ it.workTime }}小时</span>
                    </div>
                  </li>
                  <li :title="it.proEntName">
                    <span class="name">机器厂家名称：</span>
                    <span class="value">{{ it.proEntName }}</span>
                  </li>
                  <li :title="it.phone">
                    <span class="name">手机号：</span>
                    <span class="value">{{ it.phone }}</span>
                  </li>
                </ul>
              </div>
            </div>
          </swiper-slide>
        </swiper>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RealTimeEventQgd',
  props: {
    data: {
      type: Array,
      default: () => [
          // {
          //   "feedRate": 9,
          //   "proEntName": "江苏沃得农业机械股份有限公司(原:江苏沃得农业机械有限公司)",
          //   "agmachId": "ba1e5691a9394606a665993bdf6510c6",
          //   "ownerName": "李文合",
          //   "workArea": 0.95,
          //   "unitTimeWorkload": 3.83,
          //   "calc_date": "2024-03-27",
          //   "ownerId": "6d78e3df261c46cabe203c5613c65c35",
          //   "workTime": 0.25,
          //   "proEntId": "1921cfce997541eab6576f734bae9e40"
          // },
          // {
          //   "feedRate": 9,
          //   "proEntName": "中国一拖集团有限公司",
          //   "agmachId": "c76a524816b34572a2fbb7a54889e9c8",
          //   "ownerName": "靳瑞平",
          //   "workArea": 0,
          //   "unitTimeWorkload": 0,
          //   "calc_date": "2024-03-27",
          //   "ownerId": "d6526dfdbb5443f69614e7cc3e558f58",
          //   "workTime": 0,
          //   "proEntId": "2a4a6a2bfbf44e64b5636737d134bcb1"
          // },
          // {
          //   "feedRate": 9,
          //   "proEntName": "中国一拖集团有限公司",
          //   "agmachId": "824ef7b7f10e4d92847ecbdd9736e0bb",
          //   "ownerName": "王玉山",
          //   "workArea": 0,
          //   "unitTimeWorkload": 0,
          //   "calc_date": "2024-03-27",
          //   "ownerId": "aa096cd5533d422ebcd26ee86a33959a",
          //   "workTime": 0,
          //   "proEntId": "2a4a6a2bfbf44e64b5636737d134bcb1"
          // },
          // {
          //   "feedRate": 9,
          //   "proEntName": "江苏沃得农业机械股份有限公司(原:江苏沃得农业机械有限公司)",
          //   "agmachId": "857ce658a7e04ff89f562176618e1a23",
          //   "ownerName": "褚付科",
          //   "workArea": 0,
          //   "unitTimeWorkload": 0,
          //   "calc_date": "2024-03-27",
          //   "ownerId": "c9c7f74e846846f89eb2ede0b85f0eb0",
          //   "workTime": 0,
          //   "proEntId": "1921cfce997541eab6576f734bae9e40"
          // },
          // {
          //   "feedRate": 9,
          //   "proEntName": "江苏沃得农业机械股份有限公司(原:江苏沃得农业机械有限公司)",
          //   "agmachId": "beed59710d594a6dbca7048b35c3ba2c",
          //   "ownerName": "邱玉英",
          //   "workArea": 0,
          //   "unitTimeWorkload": 0,
          //   "calc_date": "2024-03-27",
          //   "ownerId": "552f2a4d4ecd431aaee854f4d9c0efce",
          //   "workTime": 0,
          //   "proEntId": "1921cfce997541eab6576f734bae9e40"
          // }
      ]
    },
    titles: {
      type: Array,
      default: () => ['事件标题', '创建时间', '信息来源', '分类', '状态', '上报人']
    },
    widths: {
      type: Array,
      default: () => ['246px', '348px', '224px', '214px', '214px', '214px']
    },
    contentHeight: {
      type: String,
      default: '220px'
    },
    showHeader: {
      type: Boolean,
      default: true
    },
    tabelHeight: {
      type: String,
      default: '105px'
    },
    spanPadding: {
      type: String,
      default: '0 15px'
    },
    headerSpanPadding: {
      type: String,
      default: '0 15px'
    },
    headerHeight: {
      type: String,
      default: '44px'
    },
    rowFontSize: {
      type: String,
      default: '14px'
    }
  },
  computed: {
    // 将手机号的中间四位隐藏
    dataList() {
      return this.data.map(it => {
        // let [name, mobile, time, totals, bfb, qwer] = it;
        // const reg = /^(\d{3})\d{4}(\d{4})$/;
        // mobile = mobile.replace(reg, '$1****$2');

        // 不需要隐藏手机号码就隐去上面的代码
        let qwer = it
        return qwer
      })
    },
    myBotSwiper() {
      return this.$refs.myBotSwiper.$swiper
    }
  },
  data() {
    return {
      defaultImg: require('@/assets/zhdd/image.png'),
      currentActiveRow: 999,
      swiperOption: {
        autoHeight: true,
        direction: 'vertical',
        spaceBetween: 0,
        autoplay: {
          delay: 3500,
          disableOnInteraction: false,
          autoplayDisableOnInteraction: false
        },
        slidesPerView: 'auto',
        grabCursor: true,
        autoplayDisableOnInteraction: false,
        mousewheelControl: true,
      },
      menuList: [
        10, 9, 8, 7, 6, 5
      ],
      activeIndex: 0
    }
  },
  methods: {
    getTrackHistroy(e){
      console.log("e1",e)
      this.$emit('getTrackHistroy',e)
    },
    showDetail(it) {
    },
    handleMenuClick(i) {
      this.activeIndex = i
      this.$emit('clickRealtimeEvent', this.menuList[i])
    },
    nextHandler(){
      if(this.activeIndex==5){
        this.activeIndex=0
      }else{
        this.activeIndex++
      }
      this.$emit('clickRealtimeEvent', this.menuList[this.activeIndex])
    },
  },
  watch: {
    data() {
      this.currentActiveRow = 0
    }
  }
}
</script>

<style lang="scss" scoped>

@font-face {
  /*给字体命名*/
  font-family: 'YouSheBiaoTiHei';
  /*引入字体文件*/
  src: url(~@/assets/font/youshebiaotihei.ttf);
  font-weight: normal;
  font-style: normal;
}
.wrap {
  width: 100%;
  height: calc(100% + 20px);

  // padding: 9px 0 19px 33px;
  :deep(.swiper-slide) {
    height: 150px;
    box-sizing: border-box;
    // padding-top: 14px;
    margin-bottom: 20px;

    &:last-child {
      border-bottom: none;
      margin: none;
    }
  }

  .container {
    width: 100%;
    height: 100%;

    .header {
      height: 42px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .num {
        font-family: YouSheBiaoTiHei;
        font-size: 32px;
        color: #ffffff;
        line-height: 42px;
        text-align: left;
        font-style: normal;

        background: linear-gradient(180deg, #00caff 0%, #0093ff 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;

        // &:nth-of-type(1) {
        //   color: red;
        //   // color: transparent;
        //   // -webkit-background-clip: text;
        //   // background-clip: text;
        //   // background: linear-gradient(92deg, #ffca00 0%, #ff7e00 100%);
        // }
        // &:nth-of-type(2) {
        //   background-clip: text;
        //   -webkit-background-clip: text;
        //   -webkit-text-fill-color: transparent;
        //   background: linear-gradient(180deg, #c1f9ff 0%, #71f6ff 100%);
        // }
        // &:nth-of-type(3) {
        //   background-clip: text;
        //   -webkit-background-clip: text;
        //   -webkit-text-fill-color: transparent;
        //   background: linear-gradient(180deg, #ffa66c 0%, #7f2400 100%);
        // }
      }

      .num0 {
        background: linear-gradient(92deg, #ffca00 0%, #ff7e00 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .num1 {
        background: linear-gradient(180deg, #c1f9ff 0%, #71f6ff 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .num2 {
        background: linear-gradient(180deg, #ffa66c 0%, #7f2400 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .nums {
        background: linear-gradient(180deg, #00caff 0%, #0093ff 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .track {
        width: 103px;
        height: 28px;
        background: url(~@/assets/mskx/bg_track.png) no-repeat;
        background-size: 100% 100%;
        padding: 4px 0 4px 15px;

        img {
          width: 20px;
          height: 20px;
        }

        a {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #00b6ff;
          text-align: center;
          font-style: normal;
          text-decoration-line: underline;
        }
      }
    }

    .info {
      // width: 410px;
      width: 100%;
      height: 127px;
      box-shadow: inset 0px 1px 6px 2px rgba(19, 97, 191, 0.4);
      border: 1px solid;
      padding: 10px;

      ul {
        li {
          display: flex;

          &:not(:last-child) {
            border-bottom: 1px solid rgba(158, 216, 255, 0.2);
          }

          div {
            width: 50%;
            height: 24px;
            display: flex;
            justify-content: flex-start;
          }

          .name {
            font-family: PingFangSC, PingFang SC;
            font-weight: 300;
            font-size: 15px;
            color: rgba(255, 255, 255, 0.7);
            line-height: 28px;
            height: 28px;
            text-align: left;
            font-style: normal;
          }

          .value {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 15px;
            color: #dbe8ff;
            line-height: 28px;
            height: 28px;
            text-align: left;
            font-style: normal;
            flex: 1;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }
}

.table-wrapper {
  width: 100%;

  span {
    box-sizing: border-box;
  }

  // .table-header {
  //   $height: 60px;
  //   width: 100%;
  //   height: $height;
  //   display: flex;
  //   background: rgba(3, 135, 255, 0.15);

  //   span {
  //     padding: 0px !important;
  //     // background: rgba(255, 255, 255, 0.12);
  //     font-size: 14px;
  //     font-family: PingFangSC, PingFang SC;
  //     line-height: $height;
  //     font-weight: 400;
  //     color: #37c1ff;
  //     line-height: 20px;

  //     .row-item {
  //       position: absolute;
  //       padding: 0 15px;
  //       padding: 0px !important;
  //       top: 50%;
  //       transform: translateY(-50%);
  //       overflow: hidden;
  //       text-overflow: ellipsis; /* 文本溢出时显示省略号来代表被修剪的文本 */
  //       white-space: normal;
  //       display: -webkit-box; /* 必须结合的属性 ，将对象作为弹性伸缩盒子模型显示 */
  //       -webkit-box-orient: vertical; /* 必须结合的属性 ，设置或检索伸缩盒对象的子元素的排列方式 */
  //       -webkit-line-clamp: 2; /* 文本需要显示多少行 */
  //       word-break: break-all;
  //     }

  //     &:nth-child(2) {
  //       text-align: center;
  //     }
  //   }

  //   span + span {
  //     margin-left: 2px;
  //   }
  // }

  .menu {
    height: 28px;
    // width: 70px;
    width: 100%;
    display: flex;

    .menu-item {
      background: url(~@/assets/mskx/pic_area.png) no-repeat;
      background-size: 100% 100%;
      height: 28px;
      width: 70px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      text-align: right;
      font-style: normal;
      line-height: 28px;
      text-align: center;
      color: #88adff;
      cursor: pointer;
    }

    .active {
      background: url(~@/assets/mskx/pic_area_a.png) no-repeat;
      background-size: 100% 100%;
      color: #ffffff;
    }
  }

  .table-content {
    width: 100%;
    height: 900px;

    .swiper {
      width: 100%;
      height: 100%;

      .table-row {
        width: 100%;
        height: 44px;
        line-height: 44px;
        cursor: pointer;
        display: flex;

        &.active {
          span {
            color: rgba(131, 194, 238, 1);
            font-size: 14px;
            font-weight: bold;
          }
        }

        &.stripe span {
          background: rgba(255, 255, 255, 0.06);
        }

        span {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: rgba(255, 255, 255, 0.85);

          &>div {
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
          }
        }

        span+span {
          margin-left: 2px;
        }

        .swiper-item {
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  // span {
  //   height: 100%;
  //   // line-height: 100%;
  //   // display: inline-block;
  //   text-align: center;
  //   white-space: nowrap;
  //   overflow: hidden;
  //   text-overflow: ellipsis;
  //   padding: 0 15px;
  //   display: flex;
  //   justify-content: center;
  //   position: relative;

  //   .row-point {
  //     display: flex;
  //     align-items: center;
  //     justify-content: center;

  //     .red-point {
  //       color: #08bf8a;
  //     }

  //     .green-point {
  //       color: #ff9090;
  //     }
  //   }

  //   .row-item {
  //     position: absolute;
  //     padding: 0 15px;
  //     top: 50%;
  //     transform: translateY(-50%);
  //     overflow: hidden;
  //     text-overflow: ellipsis; /* 文本溢出时显示省略号来代表被修剪的文本 */
  //     white-space: normal;
  //     display: -webkit-box; /* 必须结合的属性 ，将对象作为弹性伸缩盒子模型显示 */
  //     -webkit-box-orient: vertical; /* 必须结合的属性 ，设置或检索伸缩盒对象的子元素的排列方式 */
  //     -webkit-line-clamp: 2; /* 文本需要显示多少行 */
  //     word-break: break-all;
  //   }
  // }
  .icon_ {
    display: flex;
    margin: 0 0 0 12px;
    display: flex;
    align-items: center;

    img {
      width: 12px;
      height: 16px;
      margin-left: 2px;
    }
  }
}
</style>
