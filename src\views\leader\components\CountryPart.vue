<template>
  <div class="country_part" v-if="isShow">
    <div class="title"><span>城市部件</span></div>
    <span class="close" @click="$emit('close')"></span>
    <div class="tree">
      <el-tree
        :data="data"
        show-checkbox
        node-key="id"
        :default-expanded-keys="[2]"
        :default-checked-keys="[1, 3, 4, 5]"
        :props="defaultProps"
        @check="handleNodeClick"
      >
      </el-tree>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CountryPart',
  props: {
    isShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      data: [
        {
          id: 1,
          label: '01-公用设施'
        },
        {
          id: 2,
          label: '02-道路交通设施',
          children: [
            {
              id: 6,
              label: '停车场'
            },
            {
              id: 7,
              label: '公交站台'
            },
            {
              id: 8,
              label: '跨河桥'
            },
            {
              id: 9,
              label: '交通标志牌'
            },
            {
              id: 10,
              label: '限高架标志'
            },
            {
              id: 11,
              label: '路名牌'
            },
            {
              id: 12,
              label: '地名牌'
            },
            {
              id: 13,
              label: '交通信号灯'
            },
            {
              id: 14,
              label: '铁道口设施'
            }
          ]
        },
        {
          id: 3,
          label: '03-市容环境设施'
        },
        {
          id: 4,
          label: '04-园林绿化设施'
        },
        {
          id: 5,
          label: '05-其他部件'
        }
      ],
      defaultProps: {
        children: 'children',
        label: 'label'
      }
    }
  },
  methods: {
    handleNodeClick(data) {
      console.log(data)
      this.$emit('check', data)
    }
  }
}
</script>

<style lang="less" scoped>
.country_part {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 500px;
  width: 303px;
  height: 571px;
  background: url(~@/assets/csts/bg9.png) no-repeat;
  z-index: 1000;
  transition: all 1.5s;
  &.disappear {
    left: 797px;
    opacity: 0;
  }
  .title {
    width: 259px;
    height: 47px;
    text-align: left;
    padding-left: 27px;
    padding-top: 3px;
    line-height: 47px;
    margin: 20px auto 0;
    background: url(~@/assets/csts/title3.png) no-repeat;
    span {
      font-size: 18px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 26px;
      background: linear-gradient(180deg, #ffffff 0%, #2aa6ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .close {
    position: absolute;
    width: 24px;
    height: 24px;
    top: 31px;
    right: 33px;
    background: url(~@/assets/csts/close1.png) no-repeat;
    background-size: contain;
    cursor: pointer;
  }
  .tree {
    width: 243px;
    height: calc(100% - 120px);
    margin: 10px auto 0;
    overflow-y: scroll;
    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 6px;
      background: transparent;
      // border: 1px solid #999;
      /*高宽分别对应横竖滚动条的尺寸*/
      // height: 1px;
    }
    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 2px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: rgb(45, 124, 228);
      height: 100px;
    }
  }
  /deep/.el-tree {
    background: transparent;
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #ffffff;
  }
  // hover时背景色
  /deep/.el-tree-node__content {
    &:hover {
      background-color: transparent;
    }
  }
  // 选中时背景色
  /deep/.el-tree-node.is-current > .el-tree-node__content {
    background-color: transparent;
  }
  // 即使没有子元素也显示箭头
  /deep/.el-tree-node__expand-icon.is-leaf {
    color: #c0c4cc;
  }
  /deep/.el-checkbox__inner {
    background-color: rgba(0, 74, 143, 0.4);
    border-color: rgba(195, 202, 208, 0.4);
  }
  /deep/.el-tree-node {
    margin-bottom: 5px;
  }
}
</style>
