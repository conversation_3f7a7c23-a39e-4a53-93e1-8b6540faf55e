Index: src/views/leader/components/service/multipleMeeting.vue
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><template>\r\n\t<div :class=\"['multi-dialog', isFull ? 'multi-dialog-full' : isExpand ? 'multi-dialog-expand' : 'multi-dialog-other']\" v-if=\"value\">\r\n\t\t<div class=\"dialog-head\">\r\n\t\t\t<div class=\"dialog-head-left\">\r\n\t\t\t\t<div class=\"theme\">主题</div>\r\n\t\t\t\t<div class=\"invited\" @mouseover=\"meetingBoardShow = true\" @mouseleave=\"meetingBoardShow = false\">\r\n\t\t\t\t\t<span>{{ meetingData.isInvite ? meetingData.roomName : roomName1 }}</span>\r\n\t\t\t\t\t<div class=\"meeting-show-board\" v-show=\"meetingBoardShow\">\r\n\t\t\t\t\t\t<div class=\"meeting-show-board-title\">{{ roomName1 }}</div>\r\n\t\t\t\t\t\t<div class=\"meeting-show-board-item\">\r\n\t\t\t\t\t\t\t<div class=\"meeting-show-board-item-title\">会议ID</div>\r\n\t\t\t\t\t\t\t<div class=\"meeting-show-board-item-value\">\r\n\t\t\t\t\t\t\t\t<span id=\"roomNum\">{{ roomId }}</span>\r\n\t\t\t\t\t\t\t\t<i class=\"icon clip-icon\" @click=\"clipTextToBoard\"></i>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"meeting-show-board-item\">\r\n\t\t\t\t\t\t\t<div class=\"meeting-show-board-item-title\">组织者</div>\r\n\t\t\t\t\t\t\t<div class=\"meeting-show-board-item-value\">\r\n\t\t\t\t\t\t\t\t<span>{{ mobile }}</span>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"call-duration\">\r\n\t\t\t\t\t[通话时长<span style=\"color: #1EFF00;\">{{ meetingDuration }}</span\r\n\t\t\t\t\t>]\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t<div class=\"full-btns\">\r\n\t\t\t\t<div class=\"full-btn\" @click=\"minumDialog\"></div>\r\n\t\t\t\t<div class=\"full-btn\" v-show=\"false\"></div>\r\n\t\t\t\t<div class=\"full-btn\" @click=\"hangOff\"></div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t<div class=\"dialog-contain\">\r\n\t\t\t<div id=\"room-contain\" :class=\"['meeting-contain', layout]\">\r\n\t\t\t\t<!-- <div v-show=\"camStatus === 'started'\" class=\"local-stream-container participant\">\r\n\t\t\t\t</div> -->\r\n\t\t\t\t<div\r\n\t\t\t\t\tv-show=\"remoteScreenViews.length != 0\"\r\n\t\t\t\t\tv-for=\"item in remoteScreenViews\"\r\n\t\t\t\t\t:key=\"item\"\r\n\t\t\t\t\t:id=\"item\"\r\n\t\t\t\t\tclass=\"remote-stream-container participant\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<div class=\"screen-share-name\">{{ item.split('_')[2] }}正在共享屏幕</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div id=\"local\" class=\"local-stream-content participant\" v-show=\"remoteScreenViews.length == 0\">\r\n\t\t\t\t\t<div v-if=\"!localCameraShow\" class=\"board\">\r\n\t\t\t\t\t\t<div class=\"board-img\"></div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div :id=\"`describe-${tel}`\" class=\"describe\">\r\n\t\t\t\t\t\t<div :id=\"`microphone-${tel}`\" :class=\"['microphone', microphonePic]\"></div>\r\n\t\t\t\t\t\t<div :id=\"`${tel}`\" class=\"identity\">{{ username }}(我)</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<!-- <div class=\"remote-container participant\" v-if=\"remoteUsersViews && remoteUsersViews.length != 0\"> -->\r\n\t\t\t\t<div\r\n\t\t\t\t\tv-show=\"remoteScreenViews.length == 0\"\r\n\t\t\t\t\tv-for=\"item in remoteUsersViews\"\r\n\t\t\t\t\t:key=\"item\"\r\n\t\t\t\t\t:id=\"item\"\r\n\t\t\t\t\tclass=\"remote-stream-container participant\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<div v-if=\"remoteStopVideoUserIdList.has(item.split('_main')[0])\" class=\"board\">\r\n\t\t\t\t\t\t<div class=\"board-img\"></div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div :id=\"`describe-${item.split('_')[1]}`\" class=\"describe\">\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t:id=\"`microphone-${item.split('_')[1]}`\"\r\n\t\t\t\t\t\t\t:class=\"[\r\n\t\t\t\t\t\t\t\t'microphone',\r\n\t\t\t\t\t\t\t\tremoteStartAudioUserIdList.has(item.split('_main')[0]) ? 'microphone-active' : 'microphone-inactive',\r\n\t\t\t\t\t\t\t]\"\r\n\t\t\t\t\t\t></div>\r\n\t\t\t\t\t\t<div :id=\"`${item.split('_')[1]}`\" class=\"identity\">{{ item.split('_')[2] }}</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<!-- </div> -->\r\n\t\t\t</div>\r\n\t\t\t<div class=\"member-manage\" v-if=\"isExpand\">\r\n\t\t\t\t<div class=\"member-manage-top\">\r\n\t\t\t\t\t<el-input v-model=\"filterText\" placeholder=\"请输入名字进行搜索\" @change=\"filterParticipantList\">\r\n\t\t\t\t\t\t<i slot=\"suffix\" class=\"el-input__icon el-icon-search\"></i>\r\n\t\t\t\t\t</el-input>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"member-manage-contain\">\r\n\t\t\t\t\t<div class=\"member-list\">\r\n\t\t\t\t\t\t<div class=\"member-list-group\">\r\n\t\t\t\t\t\t\t<div class=\"avatar\"></div>\r\n\t\t\t\t\t\t\t<div class=\"name\" :title=\"username\">{{ username }}</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"member-list-group\">\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t:class=\"['member-icon', { 'mic-on': localMicrophoneShow }, { 'mic-off': !localMicrophoneShow }]\"\r\n\t\t\t\t\t\t\t\t@click=\"changeLocalMicroStatus\"\r\n\t\t\t\t\t\t\t></div>\r\n\r\n\t\t\t\t\t\t\t<div class=\"member-icon\"></div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div class=\"member-list\" v-for=\"(item, index) of tongxunluList\" :key=\"'m' + index\">\r\n\t\t\t\t\t\t<div class=\"member-list-group\">\r\n\t\t\t\t\t\t\t<div class=\"avatar\"></div>\r\n\t\t\t\t\t\t\t<div class=\"name\" :title=\"item.split('_')[2]\">{{ item.split('_')[2] }}</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"member-list-group\" v-if=\"role == 'host'\">\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t:class=\"[\r\n\t\t\t\t\t\t\t\t\t'member-icon',\r\n\t\t\t\t\t\t\t\t\t{ 'mic-on': remoteStartAudioUserIdList.has(item.split('_main')[0]) },\r\n\t\t\t\t\t\t\t\t\t{ 'mic-off': !remoteStartAudioUserIdList.has(item.split('_main')[0]) },\r\n\t\t\t\t\t\t\t\t]\"\r\n\t\t\t\t\t\t\t\t@click=\"changeParticipantMicrophone(item)\"\r\n\t\t\t\t\t\t\t></div>\r\n\t\t\t\t\t\t\t<div class=\"member-icon more\" @click.stop=\"openMoreDialog(item, $event)\"></div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<!-- <div class=\"member-manage-contain\">\r\n\t\t\t\t\t<div class=\"member-list\" v-for=\"(item, index) of memberList\" :key=\"'m' + index\">\r\n\t\t\t\t\t\t<div class=\"member-list-group\">\r\n\t\t\t\t\t\t\t<div class=\"avatar\"></div>\r\n\t\t\t\t\t\t\t<div class=\"name\" :title=\"item.name\">{{ item.name }}</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"member-list-group\">\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t:class=\"['member-icon', { 'mic-on': item.isMicrophoneEnabled }, { 'mic-off': !item.isMicrophoneEnabled }]\"\r\n\t\t\t\t\t\t\t\t@click=\"changeParticipantMicrophone(item)\"\r\n\t\t\t\t\t\t\t></div>\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\tclass=\"member-icon more\"\r\n\t\t\t\t\t\t\t\tv-if=\"isMoreOptionShow(item.identity)\"\r\n\t\t\t\t\t\t\t\<EMAIL>=\"openMoreDialog(item.identity, $event)\"\r\n\t\t\t\t\t\t\t></div>\r\n\t\t\t\t\t\t\t<div class=\"member-icon\" v-else></div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div> -->\r\n\t\t\t\t<div class=\"member-manage-bottom\" v-if=\"role === 'host'\">\r\n\t\t\t\t\t<div class=\"member-manage-btn\" @click=\"muteAllMicrophone\">全员静音</div>\r\n\t\t\t\t\t<div class=\"member-manage-btn\" @click=\"closeAllCamera\">全员关闭摄像头</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<moreOptionDialog\r\n\t\t\t\t\tv-if=\"moreDialogShow\"\r\n\t\t\t\t\t:optionList=\"moreDialogOptionList\"\r\n\t\t\t\t\t:identity=\"optionIdentity\"\r\n\t\t\t\t\t:offset=\"optionDialogOffset\"\r\n\t\t\t\t\t@closeCamera=\"closeParticipantCamera\"\r\n\t\t\t\t\t@changeParticipantName=\"changeParticipantName\"\r\n\t\t\t\t\t@removeParticipant=\"removeParticipant\"\r\n\t\t\t\t></moreOptionDialog>\r\n\t\t\t</div>\r\n\t\t\t<InstantMessage\r\n\t\t\t\tv-if=\"isInstantMessageShow\"\r\n\t\t\t\tref=\"chatComponent\"\r\n\t\t\t\t:messageList=\"messageList\"\r\n\t\t\t\t:chatStyle=\"chatStyle\"\r\n\t\t\t\t:isMonitorIn=\"true\"\r\n\t\t\t\t@messageSend=\"sendMessage\"\r\n\t\t\t></InstantMessage>\r\n\t\t</div>\r\n\t\t<div class=\"dialog-foot\">\r\n\t\t\t<div class=\"point-foot-group\">\r\n\t\t\t\t<div class=\"foot-btn\" v-if=\"localMicrophoneShow\" @click.stop=\"changeLocalMicroStatus\">\r\n\t\t\t\t\t<div class=\"foot-btn-icon mic-on\"></div>\r\n\t\t\t\t\t<div>麦克风</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"foot-btn\" v-else @click.stop=\"changeLocalMicroStatus\">\r\n\t\t\t\t\t<div class=\"foot-btn-icon mic-off\"></div>\r\n\t\t\t\t\t<div>麦克风</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"foot-btn\" v-if=\"localCameraShow\" @click.stop=\"changeLocalCameraStatus\">\r\n\t\t\t\t\t<div class=\"foot-btn-icon cam-on\"></div>\r\n\t\t\t\t\t<div>摄像头</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"foot-btn\" v-else @click.stop=\"changeLocalCameraStatus\">\r\n\t\t\t\t\t<div class=\"foot-btn-icon cam-off\"></div>\r\n\t\t\t\t\t<div>摄像头</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"foot-btn\" @click.stop=\"isExpand = !isExpand\">\r\n\t\t\t\t\t<div class=\"foot-btn-icon member\"></div>\r\n\t\t\t\t\t<div>成员管理</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"foot-btn\" @click.stop=\"chooseInviteWay($event)\">\r\n\t\t\t\t\t<div class=\"foot-btn-icon invite\"></div>\r\n\t\t\t\t\t<div>邀请</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"foot-btn\" v-if=\"!localScreenShow\" @click.stop=\"handleStartScreenShare\">\r\n\t\t\t\t\t<div class=\"foot-btn-icon screen-on\"></div>\r\n\t\t\t\t\t<div>屏幕共享</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"foot-btn\" v-else @click.stop=\"handleStartScreenShare\">\r\n\t\t\t\t\t<div class=\"foot-btn-icon screen-off\"></div>\r\n\t\t\t\t\t<div>屏幕共享</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t<div class=\"point-foot-group\">\r\n\t\t\t\t<div class=\"foot-btn\" @click=\"hangOff\">\r\n\t\t\t\t\t<div class=\"foot-btn-icon hang-off\"></div>\r\n\t\t\t\t\t<div>挂断</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t<inviteOptionDialog\r\n\t\t\t\tv-if=\"inviteOptionShow\"\r\n\t\t\t\t:isFull=\"isFull\"\r\n\t\t\t\t@openAddressBook=\"openAddressBook\"\r\n\t\t\t\t@openMessageBoard=\"openMessageBoard\"\r\n\t\t\t></inviteOptionDialog>\r\n\t\t</div>\r\n\t\t<changeNameDialog\r\n\t\t\tv-if=\"changeNameShow\"\r\n\t\t\t:identity=\"changingIdentity\"\r\n\t\t\t@updateParticipantName=\"updateParticipantName(arguments)\"\r\n\t\t\t@changeNameDialogClose=\"changeNameShow = false\"\r\n\t\t></changeNameDialog>\r\n\t\t<messageInviteDialog\r\n\t\t\tv-if=\"messageInviteShow\"\r\n\t\t\t@sendMessageInvite=\"sendMessageInvite\"\r\n\t\t\t@messageInviteClose=\"messageInviteShow = false\"\r\n\t\t></messageInviteDialog>\r\n\t\t<addressBook v-model=\"addressBookShow\" type=\"invite\" @inviteToJoin=\"inviteToJoin\"></addressBook>\r\n\t\t<!-- 请求入会弹窗 -->\r\n\t\t<AgreeBoard v-model=\"AgreeBoardShow\" :inviteBoardData=\"AgreeInfo\" @refuse=\"refuseAgree\" @accept=\"acceptAgree\" v-drag></AgreeBoard>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport { sendInviteMessage, generateWxLink, getSig, joinRoom, saveRoom, getRoom, endRoom, quitRoom } from '@/api/bjnj/zhdd.js'\r\nimport util from '@/libs/util.js'\r\nimport rtc from '@/components/mixins/rtc.js'\r\nimport TRTC from 'trtc-sdk-v5'\r\nimport LibGenerateTestUserSig from '@/utils/lib-generate-test-usersig.min.js'\r\nimport AgreeBoard from '@/views/leader/components/service/agreeBoard.vue'\r\nimport { isMobile } from '@/utils/utils'\r\nlet meetingInterval = null\r\nexport default {\r\n\tname: 'multipleMeeting',\r\n\tmixins: [rtc],\r\n\tcomponents: {\r\n\t\tmoreOptionDialog: () => import('./moreOptionDialog.vue'),\r\n\t\tinviteOptionDialog: () => import('./inviteOptionDialog.vue'),\r\n\t\tchangeNameDialog: () => import('./changeNameDialog.vue'),\r\n\t\tmessageInviteDialog: () => import('./messageInviteDialog.vue'),\r\n\t\taddressBook: () => import('./addressBook.vue'),\r\n\t\tInstantMessage: () => import('./instantMessage.vue'),\r\n\t\tAgreeBoard,\r\n\t},\r\n\tprops: {\r\n\t\tvalue: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false,\r\n\t\t},\r\n\t\tallTracks: {\r\n\t\t\ttype: Array,\r\n\t\t\tdefault: () => [],\r\n\t\t},\r\n\t\troomName1: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '快速会议室',\r\n\t\t},\r\n\t\tmeetingData: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault: () => ({}),\r\n\t\t},\r\n\t\t// 发起会议邀请人列表\r\n\t\tlaunchInviteList: {\r\n\t\t\ttype: Array,\r\n\t\t\tdefault: () => [],\r\n\t\t},\r\n\t\t// 用户角色 host-会议创建者 user-普通入会者\r\n\t\t// role: {\r\n\t\t//   type: String,\r\n\t\t//   default: 'host'\r\n\t\t// },\r\n\t\tmicrophoneStatus: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true,\r\n\t\t},\r\n\t\tcameraStatus: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true,\r\n\t\t},\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\t// 用户角色 host-会议创建者 user-普通入会者\r\n\t\t\trole: 'user',\r\n\t\t\tisFull: false,\r\n\t\t\tisExpand: false,\r\n\t\t\tlocalScreenShow: false,\r\n\t\t\t// 通话时长\r\n\t\t\tduration: 0,\r\n\t\t\t// 房间号\r\n\t\t\troomNum: '',\r\n\t\t\t// 房间名\r\n\t\t\troomName: '',\r\n\t\t\t// 房主identity\r\n\t\t\troomHost: '',\r\n\t\t\t// 组件内client实例\r\n\t\t\tliveClient: null,\r\n\t\t\tliveClient2: null,\r\n\t\t\t// 本地与会者identity\r\n\t\t\tlocalIdentity: '',\r\n\t\t\t// 保存当前页面触发的全部事件\r\n\t\t\tpageEventList: [],\r\n\t\t\t// 保存会议中绑定的音视频轨道\r\n\t\t\ttrackList: [],\r\n\t\t\t// 房间与会者列表\r\n\t\t\tparticipants: [],\r\n\t\t\t// 成员管理列表\r\n\t\t\tmemberList: [],\r\n\t\t\t// 本地用户设备状态\r\n\t\t\tlocalMicrophoneShow: true,\r\n\t\t\tlocalCameraShow: true,\r\n\t\t\t// 成员管理\r\n\t\t\tfilterText: '',\r\n\t\t\tmoreDialogShow: false,\r\n\t\t\toptionDialogOffset: {},\r\n\t\t\toptionIdentity: '',\r\n\t\t\t// 邀请\r\n\t\t\tinviteOptionShow: false,\r\n\t\t\tinviteOptionDialogOffset: {},\r\n\t\t\t// 修改用户名称\r\n\t\t\tchangeNameShow: false,\r\n\t\t\tchangingIdentity: '',\r\n\t\t\t// 发送短信邀请\r\n\t\t\tmessageInviteShow: false,\r\n\t\t\t// 打开通讯录\r\n\t\t\taddressBookShow: false,\r\n\t\t\t// 即时消息\r\n\t\t\t// messageList: [\r\n\t\t\t// \t{\r\n\t\t\t// \t\tfromName: '系统消息',\r\n\t\t\t// \t\tcontent: '会议内文本聊天已启用',\r\n\t\t\t// \t},\r\n\t\t\t// ],\r\n\t\t\tchatStyle: {\r\n\t\t\t\twidth: '295px',\r\n\t\t\t\theight: '184px',\r\n\t\t\t},\r\n\t\t\tisInstantMessageShow: false,\r\n\t\t\tmeetingBoardShow: false,\r\n\t\t\tmessageReceivedHandlerBinder: null,\r\n\t\t\t// roomId: 14251,\r\n\t\t\tsdkAppId: 1600089532,\r\n\t\t\tsdkSecretKey: '3a6edb36029177424289091eb1d729befde4297a6426c3d6f9e8df97ada4cb0b',\r\n\t\t\t// userId: 'user_651265',\r\n\t\t\tuserSig: null,\r\n\t\t\tAgreeInfo: '',\r\n\t\t\tAgreeBoardShow: false,\r\n\t\t\trealRoomId: NaN,\r\n\t\t\tpreviewTimeInterval: null,\r\n\t\t\tpreviewTimeInterval2: null,\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\tthis.trtc = TRTC.create()\r\n\t},\r\n\tcomputed: {\r\n\t\tmicrophonePic() {\r\n\t\t\treturn this.localMicrophoneShow ? 'microphone-active' : 'microphone-inactive'\r\n\t\t},\r\n\t\ttel() {\r\n\t\t\treturn this.$store.state.mobile\r\n\t\t},\r\n\t\tuserId() {\r\n\t\t\treturn 'user_' + this.$store.state.mobile + '_' + this.$store.state.username\r\n\t\t},\r\n\t\troomId() {\r\n\t\t\treturn this.meetingData.isInvite\r\n\t\t\t\t? Number(this.meetingData.roomId)\r\n\t\t\t\t: this.meetingData.isJoin\r\n\t\t\t\t? Number(this.meetingData.roomNum)\r\n\t\t\t\t: parseInt(Math.random() * 1000000, 10)\r\n\t\t},\r\n\t\tmeetingDuration() {\r\n\t\t\tlet minute, second\r\n\t\t\tif (Math.floor(this.duration / 60) > 0) {\r\n\t\t\t\tminute = Math.floor(this.duration / 60) <= 9 ? '0' + Math.floor(this.duration / 60) : Math.floor(this.duration / 60)\r\n\t\t\t} else {\r\n\t\t\t\tminute = '00'\r\n\t\t\t}\r\n\t\t\tif (Math.floor(this.duration % 60) > 0) {\r\n\t\t\t\tsecond = Math.floor(this.duration % 60) <= 9 ? '0' + Math.floor(this.duration % 60) : Math.floor(this.duration % 60)\r\n\t\t\t} else {\r\n\t\t\t\tsecond = '00'\r\n\t\t\t}\r\n\t\t\treturn `${minute}分${second}秒`\r\n\t\t},\r\n\t\tparticipantLength() {\r\n\t\t\treturn this.participants.length\r\n\t\t},\r\n\t\tlayout() {\r\n\t\t\tconsole.log(this.remoteUsersViews.length, '来了几个人？？', this.remoteUsersViews)\r\n\t\t\tif (this.remoteUsersViews.length <= 1) {\r\n\t\t\t\treturn 'layout1'\r\n\t\t\t} /* else if (this.remoteUsersViews.length <= 2) {\r\n\t\t\t\treturn 'layout2'\r\n\t\t\t} */ else if (this.remoteUsersViews.length <= 3) {\r\n\t\t\t\treturn 'layout3'\r\n\t\t\t} else if (this.remoteUsersViews.length <= 5) {\r\n\t\t\t\treturn 'layout4'\r\n\t\t\t} else {\r\n\t\t\t\treturn 'layout5'\r\n\t\t\t}\r\n\t\t\t// if (this.participants.length <= 1) {\r\n\t\t\t//   return 'layout1'\r\n\t\t\t// } else if (this.participants.length <= 2) {\r\n\t\t\t//   return 'layout2'\r\n\t\t\t// } else if (this.participants.length <= 4) {\r\n\t\t\t//   return 'layout3'\r\n\t\t\t// } else if (this.participants.length <= 6) {\r\n\t\t\t//   return 'layout4'\r\n\t\t\t// } else {\r\n\t\t\t//   return 'layout5'\r\n\t\t\t// }\r\n\t\t},\r\n\t\tmoreDialogOptionList() {\r\n\t\t\tif (this.role === 'host') {\r\n\t\t\t\t// return ['关闭摄像头', '修改名称', '移出会议'] //修改名称的功能暂时不做\r\n\t\t\t\treturn ['关闭摄像头', '移出会议']\r\n\t\t\t} else {\r\n\t\t\t\t// return ['修改名称']\r\n\t\t\t\treturn []\r\n\t\t\t}\r\n\t\t},\r\n\t\tusername() {\r\n\t\t\treturn this.$store.state.username\r\n\t\t},\r\n\t\tmobile() {\r\n\t\t\treturn this.$store.state.mobile\r\n\t\t},\r\n\t\ttongxunluList() {\r\n\t\t\treturn this.remoteUsersViews.filter((d) => {\r\n\t\t\t\treturn d.indexOf(this.filterText) > -1\r\n\t\t\t})\r\n\t\t},\r\n\t},\r\n\twatch: {\r\n\t\tasync value(newVal) {\r\n\t\t\tif (newVal) {\r\n\t\t\t\tthis.duration = 0\r\n\t\t\t\tthis.roomNum = ''\r\n\t\t\t\tthis.roomName = ''\r\n\t\t\t\tthis.pageEventList = []\r\n\t\t\t\tthis.trackList = []\r\n\t\t\t\tthis.participants = []\r\n\t\t\t\tthis.memberList = []\r\n\t\t\t\tthis.messageList = []\r\n\t\t\t\tthis.filterText = ''\r\n\t\t\t\tthis.optionIdentity = ''\r\n\t\t\t\tthis.messageReceivedHandlerBinder = null\r\n\r\n\t\t\t\tthis.startMeeting()\r\n\t\t\t}\r\n\t\t},\r\n\t\tcamStatus(newVal) {\r\n\t\t\tif (newVal === 'stopped') this.localCameraShow = false\r\n\t\t},\r\n\t\tmicStatus(newVal) {\r\n\t\t\tif (newVal === 'stopped') this.localMicrophoneShow = false\r\n\t\t},\r\n\t\troomStatus(newVal) {\r\n\t\t\tif (newVal === 'exited') {\r\n\t\t\t\tthis.closeDialog()\r\n\t\t\t\tthis.isInstantMessageShow = false\r\n\t\t\t}\r\n\t\t\tif (this.isBeiTi) {\r\n\t\t\t\tquitRoom({ roomNumber: this.realRoomId, mobile: this.$store.state.mobile }).then(async (res) => {\r\n\t\t\t\t\tthis.isBeiTi = false\r\n\t\t\t\t\tthis.closeDialog()\r\n\t\t\t\t\tthis.isInstantMessageShow = false\r\n\t\t\t\t\tif (this.allTracks && this.allTracks.length != 0) {\r\n\t\t\t\t\t\tthis.$emit('clearTracks')\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (this.previewTimeInterval) {\r\n\t\t\t\t\t\tawait clearInterval(this.previewTimeInterval)\r\n\t\t\t\t\t\tthis.previewTimeInterval = null\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (this.previewTimeInterval2) {\r\n\t\t\t\t\t\tawait clearInterval(this.previewTimeInterval2)\r\n\t\t\t\t\t\tthis.previewTimeInterval2 = null\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\tshareStatus(newVal) {\r\n\t\t\tif (newVal === 'stopped') {\r\n\t\t\t\tthis.localScreenShow = false\r\n\t\t\t}\r\n\t\t},\r\n\t},\r\n\t// beforeDestroy() {\r\n\t//   if(this.liveClient) {\r\n\t//     this.clearAllInterval()\r\n\t//     this.clearTrack();\r\n\t//     this.dispatchLiveClientEvent()\r\n\t//     this.leaveRoom()\r\n\t//   }\r\n\t// },\r\n\tmethods: {\r\n\t\tasync startMeeting(e) {\r\n\t\t\tconsole.log(this.roomId, 'RoomId', this.launchInviteList)\r\n\t\t\tthis.realRoomId = this.roomId\r\n\t\t\t// const userSigGenerator = new LibGenerateTestUserSig(\r\n\t\t\t// \t1600089532,\r\n\t\t\t// \t'3a6edb36029177424289091eb1d729befde4297a6426c3d6f9e8df97ada4cb0b',\r\n\t\t\t// \t604800,\r\n\t\t\t// )\r\n\t\t\tif (!this.meetingData.isInvite) {\r\n\t\t\t\t// this.userSig = userSigGenerator.genTestUserSig(this.userId)\r\n\t\t\t\tgetSig({\r\n\t\t\t\t\tuserid: this.userId,\r\n\t\t\t\t\texpire: 1800, //链接失效时间单位s\r\n\t\t\t\t\troomid: this.roomId,\r\n\t\t\t\t\tprivilegeMap: 255,\r\n\t\t\t\t}).then(async (res) => {\r\n\t\t\t\t\tconsole.log('您您你你你你你你你你你那那那', this.roomId, res)\r\n\t\t\t\t\tthis.userSig = res.data\r\n\t\t\t\t\tconsole.log(this.cameraStatus, this.microphoneStatus, 'this.cameraStatus')\r\n\t\t\t\t\tthis.localCameraShow = this.cameraStatus\r\n\t\t\t\t\tthis.localMicrophoneShow = this.microphoneStatus\r\n\r\n\t\t\t\t\tawait this.enterRoom()\r\n\t\t\t\t\tif (this.cameraStatus) this.handleStartLocalVideo()\r\n\t\t\t\t\tif (this.microphoneStatus) this.handleStartLocalAudio()\r\n\t\t\t\t\tif (!this.meetingData.isJoin) {\r\n\t\t\t\t\t\tthis.liveClient = new WebSocket(\r\n\t\t\t\t\t\t\t'wss://dpzs.camtc.cn/ws/meeting-websocket/' + this.roomId + '?phone=' + this.$store.state.mobile,\r\n\t\t\t\t\t\t\t// 'ws://172.16.50.211:9019/meeting-websocket/' + this.roomId + '?phone=' + this.$store.state.mobile,\r\n\t\t\t\t\t\t)\r\n\t\t\t\t\t\tthis.initClientEvent()\r\n\t\t\t\t\t\tif (this.previewTimeInterval) {\r\n\t\t\t\t\t\t\tclearInterval(this.previewTimeInterval)\r\n\t\t\t\t\t\t\tthis.previewTimeInterval = null\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.previewTimeInterval = setInterval(() => {\r\n\t\t\t\t\t\t\tconsole.log('9885')\r\n\t\t\t\t\t\t\tlet data = { acceptUser: 'root', type: 0 }\r\n\t\t\t\t\t\t\tthis.liveClient.send(JSON.stringify(data))\r\n\t\t\t\t\t\t}, 20000)\r\n\t\t\t\t\t\tsaveRoom({\r\n\t\t\t\t\t\t\troomNumber: this.roomId,\r\n\t\t\t\t\t\t\troomName: this.roomName1,\r\n\t\t\t\t\t\t\tmobile: this.$store.state.mobile,\r\n\t\t\t\t\t\t}).then((res) => {\r\n\t\t\t\t\t\t\tthis.role = 'host'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.role = 'user'\r\n\t\t\t\t\t\tgetRoom(this.roomId).then((res) => {\r\n\t\t\t\t\t\t\tthis.roomName1 = res.data.roomName\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tjoinRoom({\r\n\t\t\t\t\t\t\troomNumber: this.roomId,\r\n\t\t\t\t\t\t\tmobile: this.$store.state.mobile,\r\n\t\t\t\t\t\t\tname: this.$store.state.username,\r\n\t\t\t\t\t\t}).then((res) => {})\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.generateInviteLink()\r\n\t\t\t\t\tmeetingInterval = setInterval(() => {\r\n\t\t\t\t\t\tthis.duration++\r\n\t\t\t\t\t}, 1000)\r\n\t\t\t\t\tif (this.launchInviteList.length != 0) {\r\n\t\t\t\t\t\t//e是电话号码\r\n\t\t\t\t\t\tconsole.log('16565454564')\r\n\t\t\t\t\t\tthis.launchInviteList.forEach((d) => {\r\n\t\t\t\t\t\t\tif (d.mobile) {\r\n\t\t\t\t\t\t\t\t// const useSig = userSigGenerator.genTestUserSig('user_' + d.mobile)\r\n\t\t\t\t\t\t\t\tlet params = {\r\n\t\t\t\t\t\t\t\t\ttoUser: d.mobile,\r\n\t\t\t\t\t\t\t\t\t// toUser: '17634406498',\r\n\t\t\t\t\t\t\t\t\tmessage: encodeURI(\r\n\t\t\t\t\t\t\t\t\t\t`${location.origin}${location.pathname}#/invite?roomId=${this.roomId}&userId=user_${d.mobile}&roomName=${this.roomName1}`,\r\n\t\t\t\t\t\t\t\t\t),\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tsendInviteMessage(params).then((res) => {})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t// sendNotice({\r\n\t\t\t\t\t\t// \tuserIds: this.launchInviteList.map((d) => {\r\n\t\t\t\t\t\t// \t\treturn d.mobile\r\n\t\t\t\t\t\t// \t}),\r\n\t\t\t\t\t\t// \tmessage: this.roomId + '+-' + this.username + '+-' + this.roomName1,\r\n\t\t\t\t\t\t// }).then(async (res) => {\r\n\t\t\t\t\t\t// \tconsole.log('成功调用通知接口')\r\n\t\t\t\t\t\t// })\r\n\t\t\t\t\t\tlet data = {\r\n\t\t\t\t\t\t\tuserIds: this.launchInviteList.map((d) => {\r\n\t\t\t\t\t\t\t\treturn d.mobile\r\n\t\t\t\t\t\t\t}),\r\n\t\t\t\t\t\t\tmessage: this.roomId + '+-' + this.username + '+-' + this.roomName1,\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.$emit('sendNotice', JSON.stringify(data))\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t} else {\r\n\t\t\t\t// this.userSig = userSigGenerator.genTestUserSig(this.userId)\r\n\t\t\t\tgetSig({\r\n\t\t\t\t\tuserid: this.userId,\r\n\t\t\t\t\texpire: 1800,\r\n\t\t\t\t\troomid: this.roomId,\r\n\t\t\t\t\tprivilegeMap: 255,\r\n\t\t\t\t}).then(async (res) => {\r\n\t\t\t\t\tconsole.log('您您你你你你你你你你你那那那', this.roomId, res)\r\n\t\t\t\t\tthis.userSig = res.data\r\n\t\t\t\t\tconsole.log(this.userId, this.userSig, this.roomId, this.roomName1, 'this.cameraStatus')\r\n\r\n\t\t\t\t\tthis.role = 'user'\r\n\t\t\t\t\tthis.localCameraShow = this.cameraStatus\r\n\t\t\t\t\tthis.localMicrophoneShow = this.microphoneStatus\r\n\t\t\t\t\tawait joinRoom({\r\n\t\t\t\t\t\troomNumber: this.roomId,\r\n\t\t\t\t\t\tmobile: this.$store.state.mobile,\r\n\t\t\t\t\t\tname: this.$store.state.username,\r\n\t\t\t\t\t}).then((res) => {\r\n\t\t\t\t\t\tthis.liveClient2 = new WebSocket(\r\n\t\t\t\t\t\t\t'wss://dpzs.camtc.cn/ws/meeting-websocket/' + this.roomId + '?phone=' + this.$store.state.mobile,\r\n\t\t\t\t\t\t\t// 'ws://172.16.50.211:9019/meeting-websocket/' + this.roomId + '?phone=' + this.$store.state.mobile,\r\n\t\t\t\t\t\t)\r\n\t\t\t\t\t\tthis.initClientEvent2()\r\n\t\t\t\t\t\tif (this.previewTimeInterval2) {\r\n\t\t\t\t\t\t\tclearInterval(this.previewTimeInterval2)\r\n\t\t\t\t\t\t\tthis.previewTimeInterval2 = null\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.previewTimeInterval2 = setInterval(() => {\r\n\t\t\t\t\t\t\tconsole.log('9885')\r\n\t\t\t\t\t\t\tlet data = { acceptUser: 'root', type: 0 }\r\n\t\t\t\t\t\t\tthis.liveClient2.send(JSON.stringify(data))\r\n\t\t\t\t\t\t}, 20000)\r\n\t\t\t\t\t})\r\n\t\t\t\t\tawait this.enterRoom()\r\n\t\t\t\t\tif (this.cameraStatus) this.handleStartLocalVideo()\r\n\t\t\t\t\tif (this.microphoneStatus) this.handleStartLocalAudio()\r\n\t\t\t\t\tmeetingInterval = setInterval(() => {\r\n\t\t\t\t\t\tthis.duration++\r\n\t\t\t\t\t}, 1000)\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tthis.isInstantMessageShow = true\r\n\t\t},\r\n\t\tinitClientEvent2() {\r\n\t\t\tif (!this.liveClient2) {\r\n\t\t\t\tconsole.log('客户端未初始化')\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\t// 连接打开时触发\r\n\t\t\tthis.liveClient2.onopen = (event) => {\r\n\t\t\t\tconsole.log('连接已建立')\r\n\t\t\t}\r\n\t\t\t// 接收消息时触发\r\n\t\t\tthis.liveClient2.onmessage = (event) => {\r\n\t\t\t\tconsole.log('收到消息:', event)\r\n\t\t\t\tif (event.data.indexOf('disband') > -1) {\r\n\t\t\t\t\tthis.outroom()\r\n\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\tmessage: '房主解散了会议',\r\n\t\t\t\t\t\ttype: 'warning',\r\n\t\t\t\t\t})\r\n\t\t\t\t} else if (event.data == 'agree') {\r\n\t\t\t\t\tthis.joinMeetingShow = false\r\n\t\t\t\t\tthis.multipleMeetingShow = true\r\n\t\t\t\t} else if (event.data == 'refuse') {\r\n\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\tmessage: '管理员拒绝了您的入会请求！',\r\n\t\t\t\t\t\ttype: 'warning',\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.joinMeetingShow = false\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthis.liveClient2.onclose = (event) => {\r\n\t\t\t\tthis.liveClient2 = null\r\n\t\t\t}\r\n\t\t},\r\n\t\tgenerateInviteLink() {\r\n\t\t\tlet sdkAppId = 1600089532\r\n\t\t\tlet sdkSecretKey = '3a6edb36029177424289091eb1d729befde4297a6426c3d6f9e8df97ada4cb0b'\r\n\t\t\tconst inviteUserId2 = `user_17634406496`\r\n\t\t\tconst inviteUserId3 = `user_17634406495`\r\n\t\t\tconst inviteUserId4 = `user_17634406494`\r\n\t\t\tconst userSigGenerator = new LibGenerateTestUserSig(sdkAppId, sdkSecretKey, 604800)\r\n\t\t\tconst inviteUserSig2 = userSigGenerator.genTestUserSig(inviteUserId2)\r\n\t\t\tconst inviteUserSig3 = userSigGenerator.genTestUserSig(inviteUserId3)\r\n\t\t\tconst inviteUserSig4 = userSigGenerator.genTestUserSig(inviteUserId4)\r\n\t\t\tconsole.log(\r\n\t\t\t\tencodeURI(\r\n\t\t\t\t\t`${location.origin}${location.pathname}#/invite?sdkAppId=${sdkAppId}&userSig=${inviteUserSig2}&roomId=${this.roomId}&userId=${inviteUserId2}&roomName=${this.roomName1}`,\r\n\t\t\t\t),\r\n\t\t\t\tencodeURI(\r\n\t\t\t\t\t`${location.origin}${location.pathname}#/invite?sdkAppId=${sdkAppId}&userSig=${inviteUserSig3}&roomId=${this.roomId}&userId=${inviteUserId3}&roomName=${this.roomName1}`,\r\n\t\t\t\t),\r\n\t\t\t\tencodeURI(\r\n\t\t\t\t\t`${location.origin}${location.pathname}#/invite?sdkAppId=${sdkAppId}&userSig=${inviteUserSig4}&roomId=${this.roomId}&userId=${inviteUserId4}&roomName=${this.roomName1}`,\r\n\t\t\t\t),\r\n\t\t\t\t'ttttttttttttt',\r\n\t\t\t)\r\n\t\t},\r\n\t\tinitClientEvent() {\r\n\t\t\tif (!this.liveClient) {\r\n\t\t\t\tconsole.log('客户端未初始化')\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\t// 连接打开时触发\r\n\t\t\tthis.liveClient.onopen = (event) => {\r\n\t\t\t\tconsole.log('连接已建立')\r\n\t\t\t\t// 发送消息\r\n\t\t\t\t// this.liveClient.send('Hello Server!')\r\n\t\t\t}\r\n\t\t\t// 接收消息时触发\r\n\t\t\tthis.liveClient.onmessage = (event) => {\r\n\t\t\t\tconsole.log('收到消息:', event)\r\n\t\t\t\tthis.AgreeInfo = event.data\r\n\t\t\t\tthis.AgreeBoardShow = true\r\n\t\t\t}\r\n\t\t\tthis.liveClient.onclose = (event) => {\r\n\t\t\t\tthis.liveClient = null\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 最小化窗口\r\n\t\tminumDialog() {\r\n\t\t\tconsole.log('el', this.$el, this.$el.style)\r\n\t\t\t// if (!this.liveClient?.room) {\r\n\t\t\t// \tthis.$messageNew.message('error', {\r\n\t\t\t// \t\tmessage: '当前尚未加入会议',\r\n\t\t\t// \t})\r\n\t\t\t// } else {\r\n\t\t\t// \tlet curIdentity = this.liveClient?.room.localParticipant.identity\r\n\t\t\t// \tlet index = this.participants.findIndex((item) => {\r\n\t\t\t// \t\treturn item.identity === curIdentity\r\n\t\t\t// \t})\r\n\t\t\t// \tif (index > -1) {\r\n\t\t\tthis.$emit('multiMeetingMinum', {\r\n\t\t\t\t// identity: curIdentity,\r\n\t\t\t\t// name: this.participants[index].name,\r\n\t\t\t\t// track: this.participants[index].videoTrack,\r\n\t\t\t\t// cameraStatus: this.participants[index].isCameraEnabled,\r\n\t\t\t\t// microphoneStatus: this.participants[index].isMicrophoneEnabled,\r\n\t\t\t\tduration: this.duration,\r\n\t\t\t\tcameraStatus: this.localCameraShow,\r\n\t\t\t\tmicrophoneStatus: this.localMicrophoneShow,\r\n\t\t\t})\r\n\t\t\tthis.$el.style.visibility = 'hidden'\r\n\t\t\t// \t} else {\r\n\t\t\t// \t\tthis.$messageNew.message('error', {\r\n\t\t\t// \t\t\tmessage: '无法获取本地与会者视频流',\r\n\t\t\t// \t\t})\r\n\t\t\t// \t}\r\n\t\t\t// }\r\n\t\t},\r\n\t\t// 还原窗口\r\n\t\tresetDialog() {\r\n\t\t\tthis.$el.style.visibility = 'visible'\r\n\t\t},\r\n\t\tfilterParticipantList(text) {\r\n\t\t\t// this.memberList = this.participants.map(item => {\r\n\t\t\t//   if(item.name.includes(text)) {\r\n\t\t\t//     console.log('匹配的用户', item)\r\n\t\t\t//     return {\r\n\t\t\t//       identity: item?.identity,\r\n\t\t\t//       name: item.name,\r\n\t\t\t//       audioLevel: item.audioLevel,\r\n\t\t\t//       isCameraEnabled: item.isCameraEnabled,\r\n\t\t\t//       isLocal: item.isLocal,\r\n\t\t\t//       isMicrophoneEnabled: item.isMicrophoneEnabled,\r\n\t\t\t//       isScreenShareEnabled: item.isScreenShareEnabled,\r\n\t\t\t//       isSpeaking: item.isSpeaking,\r\n\t\t\t//       sid: item.sid,\r\n\t\t\t//       metadata: item.metadata\r\n\t\t\t//     }\r\n\t\t\t//   }\r\n\t\t\t// })\r\n\t\t\tthis.memberList = []\r\n\t\t\t// this.participants.forEach((item) => {\r\n\t\t\t// \tif (item.name.includes(text)) {\r\n\t\t\t// \t\tthis.memberList.push({\r\n\t\t\t// \t\t\tidentity: item?.identity,\r\n\t\t\t// \t\t\tname: item.name,\r\n\t\t\t// \t\t\taudioLevel: item.audioLevel,\r\n\t\t\t// \t\t\tisCameraEnabled: item.isCameraEnabled,\r\n\t\t\t// \t\t\tisLocal: item.isLocal,\r\n\t\t\t// \t\t\tisMicrophoneEnabled: item.isMicrophoneEnabled,\r\n\t\t\t// \t\t\tisScreenShareEnabled: item.isScreenShareEnabled,\r\n\t\t\t// \t\t\tisSpeaking: item.isSpeaking,\r\n\t\t\t// \t\t\tsid: item.sid,\r\n\t\t\t// \t\t\tmetadata: item.metadata,\r\n\t\t\t// \t\t})\r\n\t\t\t// \t}\r\n\t\t\t// })\r\n\t\t\t// console.log('memberList', this.memberList)\r\n\t\t},\r\n\t\tsendMessage(e) {\r\n\t\t\t// this.liveClient.sendMessage(e, this.username)\r\n\t\t\tthis.messageList.push({\r\n\t\t\t\t// sender: 'me',\r\n\t\t\t\t// content: this.newMessage,\r\n\t\t\t\t// timestamp: new Date(),\r\n\t\t\t\tcmdId: 1,\r\n\t\t\t\tdata: e,\r\n\t\t\t\tseq: 11,\r\n\t\t\t\tuserId: this.userId,\r\n\t\t\t\ttimestamp: new Date(),\r\n\t\t\t})\r\n\t\t\tthis.trtc.sendCustomMessage({\r\n\t\t\t\tcmdId: 1,\r\n\t\t\t\tdata: new TextEncoder().encode(e).buffer,\r\n\t\t\t\ttimestamp: new Date(),\r\n\t\t\t})\r\n\t\t},\r\n\t\tisMoreOptionShow(identity) {\r\n\t\t\tif (this.role === 'host') {\r\n\t\t\t\treturn true\r\n\t\t\t} else {\r\n\t\t\t\tif (this.localIdentity === identity) {\r\n\t\t\t\t\treturn true\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 创建会议房间\r\n\t\tasync createRoom() {\r\n\t\t\tawait this.liveClient.createRoom()\r\n\t\t},\r\n\t\t// 加入会议房间\r\n\t\tasync joinMeeting() {\r\n\t\t\tlet joinRoomParams = {\r\n\t\t\t\troomNum: this.roomNum,\r\n\t\t\t\tcameraStatus: this.cameraStatus,\r\n\t\t\t\tmicrophoneStatus: this.microphoneStatus,\r\n\t\t\t\techoCancellation: true,\r\n\t\t\t\tnoiseSuppression: true,\r\n\t\t\t}\r\n\t\t\tconsole.log('加入会议')\r\n\t\t\tawait this.liveClient.joinRoom(joinRoomParams)\r\n\t\t},\r\n\t\t// 修改本地与会者状态\r\n\t\tasync changeLocalMicroStatus() {\r\n\t\t\t// await this.liveClient.changeMicrophoneStatus()\r\n\r\n\t\t\tconst microPhoneList = await TRTC.getMicrophoneList()\r\n\t\t\tif (microPhoneList[0]) {\r\n\t\t\t\tif (this.localMicrophoneShow) this.handleStopLocalAudio()\r\n\t\t\t\telse this.handleStartLocalAudio()\r\n\t\t\t\tthis.localMicrophoneShow = !this.localMicrophoneShow\r\n\t\t\t} else {\r\n\t\t\t\tthis.$messageNew.message('warning', {\r\n\t\t\t\t\tmessage: '未检测到设备存在麦克风',\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\tasync changeLocalCameraStatus() {\r\n\t\t\t// await this.liveClient.changeCameraStatus()\r\n\t\t\tconst cameraList = await TRTC.getCameraList()\r\n\t\t\tif (cameraList[0]) {\r\n\t\t\t\tif (this.localCameraShow) this.handleStopLocalVideo()\r\n\t\t\t\telse this.handleStartLocalVideo()\r\n\t\t\t\tthis.localCameraShow = !this.localCameraShow\r\n\t\t\t} else {\r\n\t\t\t\tthis.$messageNew.message('warning', {\r\n\t\t\t\t\tmessage: '未检测到设备存在摄像头',\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 保存liveClient监听的事件到数组中，方便离开页面时解除绑定\r\n\t\taddEventToList(eventName) {\r\n\t\t\tif (this.pageEventList.indexOf(eventName) < 0) {\r\n\t\t\t\tthis.pageEventList.push(eventName)\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 判断与会者浏览器是否有对应权限\r\n\t\tasync hasRelatedPermissions() {\r\n\t\t\tif (navigator?.permissions) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tlet cameraPermissionStatus = null,\r\n\t\t\t\t\t\tmicrophonePermissionStatus = null\r\n\t\t\t\t\tcameraPermissionStatus = await navigator.permissions.query({\r\n\t\t\t\t\t\tname: 'camera',\r\n\t\t\t\t\t})\r\n\t\t\t\t\tmicrophonePermissionStatus = await navigator.permissions.query({\r\n\t\t\t\t\t\tname: 'microphone',\r\n\t\t\t\t\t})\r\n\t\t\t\t\tconsole.log('cameraPermission', cameraPermissionStatus.state)\r\n\t\t\t\t\tconsole.log('microphonePermission', microphonePermissionStatus.state)\r\n\t\t\t\t\tif (cameraPermissionStatus.state === 'granted' && microphonePermissionStatus.state === 'granted') {\r\n\t\t\t\t\t\tconsole.log('关键权限已授权')\r\n\t\t\t\t\t\treturn true\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log('关键权限未授权')\r\n\t\t\t\t\t\treturn false\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.log('当前浏览器不支持permissionAPI')\r\n\t\t\t\t\treturn true\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\treturn true\r\n\t\t\t}\r\n\t\t},\r\n\t\tinitLiveClientEvent() {\r\n\t\t\tif (!this.liveClient) {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tthis.messageReceivedHandlerBinder = this.messageReceivedHandler.bind(this)\r\n\t\t\tthis.liveClient.on('errorPush', (e) => {\r\n\t\t\t\tthis.addEventToList('errorPush')\r\n\t\t\t\tconsole.log('出错位置' + e.type + ':', e.message)\r\n\t\t\t})\r\n\t\t\tthis.liveClient.on('roomCreatedSuccess', async (e) => {\r\n\t\t\t\tthis.addEventToList('roomCreatedSuccess')\r\n\t\t\t\tthis.roomNum = e\r\n\t\t\t\tconsole.log('房间创建成功', this.roomNum)\r\n\t\t\t\tawait this.joinMeeting()\r\n\t\t\t})\r\n\t\t\tthis.liveClient.on('roomCreatedFail', (e) => {\r\n\t\t\t\tthis.addEventToList('roomCreatedFail')\r\n\t\t\t\t// 房间创建失败\r\n\t\t\t\tconsole.log('房间创建失败', e)\r\n\t\t\t})\r\n\t\t\tthis.liveClient.on('joinRoomError', (e) => {\r\n\t\t\t\tthis.addEventToList('joinRoomError')\r\n\t\t\t\tthis.$messageNew.message('error', {\r\n\t\t\t\t\tmessage: '加入会议失败，请检查房间是否存在',\r\n\t\t\t\t})\r\n\t\t\t\tthis.closeDialog()\r\n\t\t\t})\r\n\t\t\tthis.liveClient.on('roomConnected', async () => {\r\n\t\t\t\tthis.addEventToList('roomConnected')\r\n\t\t\t\t// 会议计时开始\r\n\t\t\t\tmeetingInterval = setInterval(() => {\r\n\t\t\t\t\tthis.duration++\r\n\t\t\t\t}, 1000)\r\n\t\t\t\tconsole.log('会议链接成功', this.liveClient.room)\r\n\t\t\t\tlet roomMetadata = JSON.parse(this.liveClient.room.metadata)\r\n\t\t\t\t// let [org, roomName, roomHost] = JSON.parse(this.liveClient.room.metadata)\r\n\t\t\t\t// this.roomName = roomName\r\n\t\t\t\t// this.roomHost = roomHost\r\n\t\t\t\t// org = null\r\n\t\t\t\tthis.roomName = roomMetadata?.roomAlias\r\n\t\t\t\tthis.roomHost = roomMetadata?.owner[0]\r\n\t\t\t\tthis.role = this.liveClient.userId === this.roomHost ? 'host' : 'user'\r\n\t\t\t\tthis.isInstantMessageShow = true\r\n\t\t\t\tthis.inviteLaunchParticipant()\r\n\t\t\t\ttry {\r\n\t\t\t\t\tawait this.updateParticipantName([this.username, this.liveClient.room?.localParticipant.identity])\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tconsole.error('修改用户名称失败', e)\r\n\t\t\t\t}\r\n\t\t\t\t// this.hasRelatedPermissions().then(res => {\r\n\t\t\t\t//   console.log('权限结果', res)\r\n\t\t\t\t//   if (!res) {\r\n\t\t\t\t//     this.$messageNew.message('error', {\r\n\t\t\t\t//       message:\r\n\t\t\t\t//         '系统检测到您的摄像头和麦克风异常，可能会导致系统异常，请检查浏览器权限或是否存在设备'\r\n\t\t\t\t//     })\r\n\t\t\t\t//   }\r\n\t\t\t\t// })\r\n\t\t\t\t// if (this.liveClient.room?.localParticipant) {\r\n\t\t\t\t//   this.tempRenderAbnormalParticipant(this.liveClient.room?.localParticipant)\r\n\t\t\t\t// }\r\n\t\t\t})\r\n\t\t\tthis.liveClient.on('requestDeviceOpen', (e) => {\r\n\t\t\t\tthis.addEventToList('requestDeviceOpen')\r\n\t\t\t\tif (e.input == '1') {\r\n\t\t\t\t\tthis.$messageNew.success({\r\n\t\t\t\t\t\tmessage: '房主请求与会者打开麦克风',\r\n\t\t\t\t\t})\r\n\t\t\t\t} else if (e.input == '2') {\r\n\t\t\t\t\tthis.$messageNew.success({\r\n\t\t\t\t\t\tmessage: '房主请求与会者打开摄像头',\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\tthis.liveClient.on('roomDisconnected', (e) => {\r\n\t\t\t\tthis.addEventToList('roomDisconnected')\r\n\t\t\t\tconsole.log('房间链接已关闭', e)\r\n\t\t\t\tthis.isInstantMessageShow = false\r\n\t\t\t\tthis.clearAllInterval()\r\n\t\t\t\t// 结束会议\r\n\t\t\t\tthis.closeDialog()\r\n\t\t\t})\r\n\t\t\tthis.liveClient.on('sendTextFailed', (e) => {\r\n\t\t\t\tthis.addEventToList('sendTextFailed')\r\n\t\t\t\t// this.$message({\r\n\t\t\t\t//   message: \"即时消息发送失败\" + e,\r\n\t\t\t\t//   type: \"error\",\r\n\t\t\t\t// });\r\n\t\t\t})\r\n\t\t\tthis.liveClient.on('sendTextSuccess', (e) => {\r\n\t\t\t\tthis.addEventToList('sendTextSuccess')\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.$refs['chatComponent'].clearInput()\r\n\t\t\t\t})\r\n\t\t\t})\r\n\t\t\tthis.liveClient.on('messageReceived', this.messageReceivedHandlerBinder)\r\n\t\t\tthis.liveClient.on('localCameraChange', (e) => {\r\n\t\t\t\tthis.addEventToList('localCameraChange')\r\n\t\t\t\tthis.$emit('localCameraChange', e)\r\n\t\t\t\tlet message = ''\r\n\t\t\t\tthis.localCameraShow = e\r\n\t\t\t\tif (e) {\r\n\t\t\t\t\tmessage = '摄像头已打开'\r\n\t\t\t\t} else {\r\n\t\t\t\t\tmessage = '摄像头已关闭'\r\n\t\t\t\t}\r\n\t\t\t\tthis.$messageNew.message('success', {\r\n\t\t\t\t\tmessage: message,\r\n\t\t\t\t})\r\n\t\t\t})\r\n\t\t\tthis.liveClient.on('localMicrophoneChange', (e) => {\r\n\t\t\t\tthis.addEventToList('localMicrophoneChange')\r\n\t\t\t\tthis.$emit('localMicrophoneChange', e)\r\n\t\t\t\tlet message = ''\r\n\t\t\t\tthis.localMicrophoneShow = e\r\n\t\t\t\tif (e) {\r\n\t\t\t\t\tmessage = '麦克风已打开'\r\n\t\t\t\t} else {\r\n\t\t\t\t\tmessage = '麦克风已关闭'\r\n\t\t\t\t}\r\n\t\t\t\tthis.$messageNew.message('success', {\r\n\t\t\t\t\tmessage: message,\r\n\t\t\t\t})\r\n\t\t\t})\r\n\t\t\tthis.liveClient.on('videoCallRender', (e) => {\r\n\t\t\t\tthis.addEventToList('videoCallRender')\r\n\t\t\t\tif (this.liveClient) {\r\n\t\t\t\t\tthis.localCameraShow = this.liveClient.isCameraEnable\r\n\t\t\t\t\tthis.localMicrophoneShow = this.liveClient.isMicrophoneEnable\r\n\t\t\t\t}\r\n\t\t\t\tthis.renderVideoItem(e)\r\n\t\t\t})\r\n\t\t\t// this.liveClient.on('participantConnected', e => {\r\n\t\t\t//  this.addEventToList('participantConnected')\r\n\t\t\t//  console.log('与会者链接到会议中', e)\r\n\t\t\t//  this.tempRenderAbnormalParticipant(e)\r\n\t\t\t// })\r\n\t\t},\r\n\t\treportSuccessEvent(name) {\r\n\t\t\t// const ext3 = name === 'enterRoom' ? this.sdkAppId : 0\r\n\t\t\t// this.$aegis?.reportEvent({\r\n\t\t\t// \tname,\r\n\t\t\t// \text1: `${name}-success`,\r\n\t\t\t// \text2: this.$DEMOKEY,\r\n\t\t\t// \text3,\r\n\t\t\t// })\r\n\t\t},\r\n\t\treportFailedEvent(name, error, type = 'rtc') {\r\n\t\t\t// this.$aegis?.reportEvent({\r\n\t\t\t// \tname,\r\n\t\t\t// \text1: `${name}-failed#${this.roomId}*${type === 'share' ? this.shareUserId : this.userId}*${error.message}`,\r\n\t\t\t// \text2: this.$DEMOKEY,\r\n\t\t\t// \text3: 0,\r\n\t\t\t// })\r\n\t\t},\r\n\t\ttempRenderAbnormalParticipant(participant) {\r\n\t\t\tif (participant && participant.identity !== 'appointment_monitor') {\r\n\t\t\t\tconst renderItem = {}\r\n\t\t\t\tconst {\r\n\t\t\t\t\tname,\r\n\t\t\t\t\tidentity,\r\n\t\t\t\t\taudioLevel,\r\n\t\t\t\t\tisSpeaking,\r\n\t\t\t\t\tmetadata,\r\n\t\t\t\t\tsid,\r\n\t\t\t\t\tisCameraEnabled,\r\n\t\t\t\t\tisLocal,\r\n\t\t\t\t\tisMicrophoneEnabled,\r\n\t\t\t\t\tisScreenShareEnabled,\r\n\t\t\t\t} = participant\r\n\t\t\t\trenderItem.remove = false\r\n\t\t\t\trenderItem.name = name ? name : identity\r\n\t\t\t\trenderItem.identity = identity\r\n\t\t\t\trenderItem.audioLevel = audioLevel\r\n\t\t\t\trenderItem.isSpeaking = isSpeaking\r\n\t\t\t\trenderItem.metadata = metadata\r\n\t\t\t\trenderItem.sid = sid\r\n\t\t\t\trenderItem.isCameraEnabled = isCameraEnabled\r\n\t\t\t\trenderItem.isLocal = isLocal\r\n\t\t\t\trenderItem.isMicrophoneEnabled = isMicrophoneEnabled\r\n\t\t\t\trenderItem.isScreenShareEnabled = isScreenShareEnabled\r\n\t\t\t\tlet cameraPub = participant.getTrack('camera')\r\n\t\t\t\tlet micPub = participant.getTrack('microphone')\r\n\t\t\t\tif (isScreenShareEnabled) {\r\n\t\t\t\t\tcameraPub = participant.getTrack('screen_share')\r\n\t\t\t\t\tmicPub = participant.getTrack('screen_share_audio')\r\n\t\t\t\t}\r\n\t\t\t\trenderItem.videoTrack = cameraPub ? cameraPub.videoTrack : undefined\r\n\t\t\t\trenderItem.audioTrack = micPub ? micPub.audioTrack : undefined\r\n\t\t\t\tif (this.liveClient) {\r\n\t\t\t\t\tthis.localCameraShow = this.liveClient.isCameraEnable\r\n\t\t\t\t\tthis.localMicrophoneShow = this.liveClient.isMicrophoneEnable\r\n\t\t\t\t}\r\n\t\t\t\tthis.renderVideoItem(renderItem)\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 即时消息队列处理函数\r\n\t\tmessageReceivedHandler(e) {\r\n\t\t\t// this.addEventToList('messageReceived')\r\n\t\t\tthis.messageList.push(e)\r\n\t\t},\r\n\t\t// 邀请发起会议时选择的与会者\r\n\t\tinviteLaunchParticipant() {\r\n\t\t\tif (this.launchInviteList.length > 0) {\r\n\t\t\t\tthis.launchInviteList.forEach((item) => {\r\n\t\t\t\t\t// 发送短信邀请,PC邀请\r\n\t\t\t\t\tif (item.mobile) {\r\n\t\t\t\t\t\tthis.sendMessageInvite2(item.mobile)\r\n\t\t\t\t\t\tthis.sendPcInvite(item.mobile)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 通讯录邀请\r\n\t\tinviteToJoin(e) {\r\n\t\t\t// if (e.length > 0) {\r\n\t\t\t// \te.forEach((item) => {\r\n\t\t\t// \t\t// 发送短信邀请,PC邀请\r\n\t\t\t// \t\tif (item.mobile) {\r\n\t\t\t// \t\t\tthis.sendMessageInvite2(item.mobile)\r\n\t\t\t// \t\t\tthis.sendPcInvite(item.mobile)\r\n\t\t\t// \t\t}\r\n\t\t\t// \t})\r\n\t\t\t// }\r\n\t\t\tif (e.length > 0) {\r\n\t\t\t\t//e是电话号码\r\n\t\t\t\tconsole.log('16565454564', e)\r\n\t\t\t\te.forEach((d) => {\r\n\t\t\t\t\tif (d.mobile) {\r\n\t\t\t\t\t\tlet params = {\r\n\t\t\t\t\t\t\ttoUser: d.mobile,\r\n\t\t\t\t\t\t\t// toUser: '17634406498',\r\n\t\t\t\t\t\t\tmessage: encodeURI(\r\n\t\t\t\t\t\t\t\t`${location.origin}${location.pathname}#/invite?roomId=${this.roomId}&userId=user_${d.mobile}&roomName=${this.roomName1}`,\r\n\t\t\t\t\t\t\t),\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tsendInviteMessage(params).then((res) => {\r\n\t\t\t\t\t\t\t// this.$messageNew.message('success', {\r\n\t\t\t\t\t\t\t// \tmessage: '短信发送成功',\r\n\t\t\t\t\t\t\t// })\r\n\t\t\t\t\t\t\t// this.messageInviteShow = false\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t// sendNotice({\r\n\t\t\t\t// \tuserIds: this.launchInviteList.map((d) => {\r\n\t\t\t\t// \t\treturn d.mobile\r\n\t\t\t\t// \t}),\r\n\t\t\t\t// \tmessage: this.roomId + '+-' + this.username + '+-' + this.roomName1,\r\n\t\t\t\t// }).then(async (res) => {\r\n\t\t\t\t// \tconsole.log('成功调用通知接口')\r\n\t\t\t\t// })\r\n\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tuserIds: e.map((d) => {\r\n\t\t\t\t\t\treturn d.mobile\r\n\t\t\t\t\t}),\r\n\t\t\t\t\tmessage: this.roomId + '+-' + this.username + '+-' + this.roomName1,\r\n\t\t\t\t}\r\n\t\t\t\tthis.$emit('sendNotice', JSON.stringify(data))\r\n\t\t\t}\r\n\t\t\tthis.addressBookShow = false\r\n\t\t},\r\n\t\trenderVideoItem(videoItem) {\r\n\t\t\tconsole.log('视频与会者通话渲染, 用户identity：' + videoItem.identity, videoItem)\r\n\t\t\t// 验证用户姓名, 若用户姓名为'Phone 0'+手机号，则去掉前面的0\r\n\t\t\tlet reg = /^(Phone 0)+?/\r\n\t\t\tvideoItem.name = videoItem.name.replace(reg, '')\r\n\t\t\t// 会议室dom\r\n\t\t\tconst container = document.getElementById('room-contain')\r\n\t\t\tif (!container) {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tlet div = document.getElementById(`participant-${videoItem.identity}`)\r\n\t\t\tif (!div && !videoItem.remove) {\r\n\t\t\t\tif (videoItem.isLocal) {\r\n\t\t\t\t\t// 缓存本地与会者的identity\r\n\t\t\t\t\tthis.localIdentity = videoItem.identity\r\n\t\t\t\t}\r\n\t\t\t\t// 成员渲染到页面\r\n\t\t\t\tdiv = document.createElement('div')\r\n\t\t\t\tdiv.id = `participant-${videoItem.identity}`\r\n\t\t\t\tdiv.className = videoItem.isLocal ? 'participant local' : 'participant'\r\n\t\t\t\tif (videoItem.isCameraEnabled || videoItem.isScreenShareEnabled) {\r\n\t\t\t\t\tdiv.innerHTML = `\r\n              <video id=\"video-${videoItem.identity}\" class=\"p-video\" autoplay></video>\r\n              <div id=\"describe-${videoItem.identity}\" class=\"describe\">\r\n                <div id=\"microphone-${videoItem.identity}\" class=\"microphone\"></div>\r\n                <div id=\"${videoItem.identity}\" class=\"identity\">${videoItem.isLocal ? videoItem.name + '(我)' : videoItem.name}</div>\r\n              </div>\r\n            `\r\n\t\t\t\t} else {\r\n\t\t\t\t\tdiv.innerHTML = `\r\n            <video id=\"video-${videoItem.identity}\" class=\"p-video\" autoplay></video>\r\n            <div id=\"board-${videoItem.identity}\" class=\"board\">\r\n              <div class=\"board-img\"></div>\r\n              <div id=\"describe-${videoItem.identity}\" class=\"describe\">\r\n                  <div id=\"microphone-${videoItem.identity}\" class=\"microphone\"></div>\r\n                  <div id=\"${videoItem.identity}\" class=\"identity\">${\r\n\t\t\t\t\t\tvideoItem.isLocal ? videoItem.name + '(我)' : videoItem.name\r\n\t\t\t\t\t}</div>\r\n              </div>\r\n            </div>\r\n            `\r\n\t\t\t\t}\r\n\t\t\t\tif (div.className.includes('local')) {\r\n\t\t\t\t\t// 将主机节点插入到最前面\r\n\t\t\t\t\tcontainer.insertBefore(div, container.firstChild ?? null)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tcontainer.appendChild(div)\r\n\t\t\t\t}\r\n\t\t\t\t// 将与会者添加到与会者列表中\r\n\t\t\t\tthis.participants.push(videoItem)\r\n\t\t\t}\r\n\t\t\t// 摄像头关闭状态下展板dom元素\r\n\t\t\tlet boardEle = document.getElementById(`board-${videoItem.identity}`)\r\n\t\t\t// 视频元素\r\n\t\t\tlet videoElm = document.getElementById(`video-${videoItem.identity}`)\r\n\t\t\t// 静音用户\r\n\t\t\tconst unableMicrophone = () => {\r\n\t\t\t\tthis.liveClient.changeParticipantMicrophoneStatus(videoItem.identity, true)\r\n\t\t\t}\r\n\t\t\t// 解除静音用户\r\n\t\t\tconst enableMicrophone = () => {\r\n\t\t\t\tthis.liveClient.changeParticipantMicrophoneStatus(videoItem.identity, false)\r\n\t\t\t}\r\n\t\t\t// 删除与会者\r\n\t\t\tif (videoItem.remove) {\r\n\t\t\t\tif (videoElm) {\r\n\t\t\t\t\tvideoElm.srcObject = null\r\n\t\t\t\t\tvideoElm.src = ''\r\n\t\t\t\t\tvideoElm?.remove()\r\n\t\t\t\t}\r\n\t\t\t\tif (div) {\r\n\t\t\t\t\tdiv?.remove()\r\n\t\t\t\t}\r\n\t\t\t\tif (this.participants.length > 0) {\r\n\t\t\t\t\tlet index = this.participants.findIndex((item) => {\r\n\t\t\t\t\t\treturn item.identity === videoItem.identity\r\n\t\t\t\t\t})\r\n\t\t\t\t\tif (index > -1) {\r\n\t\t\t\t\t\tthis.participants.splice(index, 1)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.filterParticipantList(this.filterText)\r\n\t\t\t\t}\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\t// 以下为与会者状态变更渲染代码\r\n\t\t\tif (div) {\r\n\t\t\t\tif (videoItem.isCameraEnabled || videoItem.isScreenShareEnabled) {\r\n\t\t\t\t\tif (boardEle) {\r\n\t\t\t\t\t\tdiv.removeChild(boardEle)\r\n\t\t\t\t\t\tboardEle = null\r\n\t\t\t\t\t}\r\n\t\t\t\t\tlet describeElm = document.getElementById(`describe-${videoItem.identity}`)\r\n\t\t\t\t\tif (!describeElm) {\r\n\t\t\t\t\t\tdescribeElm = document.createElement('div')\r\n\t\t\t\t\t\tdescribeElm.id = 'describe-' + videoItem.identity\r\n\t\t\t\t\t\tdescribeElm.className = 'describe'\r\n\t\t\t\t\t\tdescribeElm.innerHTML = `\r\n              <div id=\"microphone-${videoItem.identity}\" class=\"microphone\"></div>\r\n              <div id=\"${videoItem.identity}\" class=\"identity\">${videoItem.isLocal ? videoItem.name + '(我)' : videoItem.name}</div>\r\n            `\r\n\t\t\t\t\t\tdiv.appendChild(describeElm)\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (!boardEle) {\r\n\t\t\t\t\t\tlet describeElm = document.getElementById(`describe-${videoItem.identity}`)\r\n\t\t\t\t\t\tif (describeElm) {\r\n\t\t\t\t\t\t\tdescribeElm.remove()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tlet boardEle = document.createElement('div')\r\n\t\t\t\t\t\tboardEle.id = `board-${videoItem.identity}`\r\n\t\t\t\t\t\tboardEle.className = 'board'\r\n\t\t\t\t\t\tboardEle.innerHTML = `\r\n                <div class=\"board-img\"></div>\r\n                <div class=\"describe\">\r\n                  <div id=\"microphone-${videoItem.identity}\" class=\"microphone\"></div>\r\n                  <div id=\"${videoItem.identity}\" class=\"identity\">${\r\n\t\t\t\t\t\t\tvideoItem.isLocal ? videoItem.name + '(我)' : videoItem.name\r\n\t\t\t\t\t\t}</div>\r\n                </div>\r\n              `\r\n\t\t\t\t\t\tdiv.appendChild(boardEle)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// 麦克风图标dom\r\n\t\t\t\tlet microphoneElm = document.getElementById(`microphone-${videoItem.identity}`)\r\n\t\t\t\tif (microphoneElm && videoItem.isMicrophoneEnabled) {\r\n\t\t\t\t\tmicrophoneElm.className = 'microphone microphone-active'\r\n\t\t\t\t\tmicrophoneElm.onclick = unableMicrophone\r\n\t\t\t\t} else if (microphoneElm && !videoItem.isMicrophoneEnabled) {\r\n\t\t\t\t\tmicrophoneElm.className = 'microphone microphone-inactive'\r\n\t\t\t\t\tmicrophoneElm.onclick = enableMicrophone\r\n\t\t\t\t}\r\n\t\t\t\t// 视频轨道绑定\r\n\t\t\t\tif (videoItem.videoTrack) {\r\n\t\t\t\t\tvideoItem.videoTrack.attach(videoElm)\r\n\t\t\t\t\tif (\r\n\t\t\t\t\t\tthis.trackList.findIndex((item) => {\r\n\t\t\t\t\t\t\treturn item.sid === videoItem.videoTrack.sid\r\n\t\t\t\t\t\t}) < 0\r\n\t\t\t\t\t) {\r\n\t\t\t\t\t\tthis.trackList.push(videoItem.videoTrack)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// 音频轨道绑定,消除回音\r\n\t\t\t\tif (!videoItem.isLocal) {\r\n\t\t\t\t\tif (videoItem.audioTrack) {\r\n\t\t\t\t\t\tvideoItem.audioTrack.attach(videoElm)\r\n\t\t\t\t\t\tif (\r\n\t\t\t\t\t\t\tthis.trackList.findIndex((item) => {\r\n\t\t\t\t\t\t\t\treturn item.sid === videoItem.audioTrack.sid\r\n\t\t\t\t\t\t\t}) < 0\r\n\t\t\t\t\t\t) {\r\n\t\t\t\t\t\t\tthis.trackList.push(videoItem.audioTrack)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// 更新与会者名称\r\n\t\t\t\tlet nameDom = document.getElementById(videoItem.identity)\r\n\t\t\t\tif (nameDom) {\r\n\t\t\t\t\tnameDom.innerHTML = videoItem.isLocal ? videoItem.name + '(我)' : videoItem.name\r\n\t\t\t\t}\r\n\t\t\t\t// 更新与会者数组\r\n\t\t\t\tif (this.participants.length > 0) {\r\n\t\t\t\t\tlet index = this.participants.findIndex((item) => {\r\n\t\t\t\t\t\treturn item.identity === videoItem.identity\r\n\t\t\t\t\t})\r\n\t\t\t\t\tif (index > -1) {\r\n\t\t\t\t\t\tthis.participants.splice(index, 1, videoItem)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.filterParticipantList(this.filterText)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 清除组件中的定时器\r\n\t\tclearAllInterval() {\r\n\t\t\tif (meetingInterval) {\r\n\t\t\t\tclearInterval(meetingInterval)\r\n\t\t\t\tmeetingInterval = null\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 清除组件中绑定的事件监听\r\n\t\tdispatchLiveClientEvent() {\r\n\t\t\tconsole.log('当前组件中绑定的事件监听', this.pageEventList)\r\n\t\t\tif (this.liveClient) {\r\n\t\t\t\tif (this.messageReceivedHandlerBinder) {\r\n\t\t\t\t\tthis.liveClient.off('messageReceived', this.messageReceivedHandlerBinder)\r\n\t\t\t\t}\r\n\t\t\t\tif (this.pageEventList.length > 0) {\r\n\t\t\t\t\tthis.pageEventList.forEach((eventName) => {\r\n\t\t\t\t\t\tconsole.log('解除绑定事件:', eventName)\r\n\t\t\t\t\t\tthis.liveClient.off(eventName)\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 清除组件中绑定的音视频轨道\r\n\t\tclearTrack() {\r\n\t\t\tif (this.trackList.length > 0) {\r\n\t\t\t\tthis.trackList.forEach((track) => {\r\n\t\t\t\t\ttrack.detach()\r\n\t\t\t\t\ttrack.stop()\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 退出会议\r\n\t\tleaveRoom() {\r\n\t\t\tif (this.liveClient) {\r\n\t\t\t\tthis.liveClient.leaveRoom()\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 用户点击挂断\r\n\t\tasync hangOff() {\r\n\t\t\t// this.leaveRoom()\r\n\t\t\tif (this.localScreenShow) this.localScreenShow = !this.localScreenShow\r\n\t\t\tif (this.shareStatus !== 'shared') {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tthis.shareStatus = 'stopping'\r\n\t\t\ttry {\r\n\t\t\t\tawait this.trtc.stopScreenShare()\r\n\t\t\t\tthis.shareStatus = 'stopped'\r\n\t\t\t} catch (error) {\r\n\t\t\t\tthis.shareStatus = 'shared'\r\n\t\t\t}\r\n\t\t\tif (this.role == 'host')\r\n\t\t\t\tendRoom(this.realRoomId + '/1').then(async (res) => {\r\n\t\t\t\t\tlet data = {\r\n\t\t\t\t\t\ttype: '2', //0心跳 1申请入会，2被解散\r\n\t\t\t\t\t\tmessage: 'disband',\r\n\t\t\t\t\t\tsendUser: this.$store.state.mobile,\r\n\t\t\t\t\t\tacceptUser: '',\r\n\t\t\t\t\t}\r\n\t\t\t\t\tconsole.log(data)\r\n\t\t\t\t\tawait this.liveClient.send(JSON.stringify(data))\r\n\r\n\t\t\t\t\tthis.closeDialog()\r\n\t\t\t\t\tthis.isInstantMessageShow = false\r\n\t\t\t\t\tif (this.allTracks && this.allTracks.length != 0) {\r\n\t\t\t\t\t\tthis.$emit('clearTracks')\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (this.previewTimeInterval) {\r\n\t\t\t\t\t\tclearInterval(this.previewTimeInterval)\r\n\t\t\t\t\t\tthis.previewTimeInterval = null\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (this.previewTimeInterval2) {\r\n\t\t\t\t\t\tclearInterval(this.previewTimeInterval2)\r\n\t\t\t\t\t\tthis.previewTimeInterval2 = null\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.exitRoom()\r\n\t\t\t\t})\r\n\t\t\telse {\r\n\t\t\t\tquitRoom({ roomNumber: this.realRoomId, mobile: this.$store.state.mobile }).then((res) => {\r\n\t\t\t\t\tthis.closeDialog()\r\n\t\t\t\t\tthis.isInstantMessageShow = false\r\n\t\t\t\t\tif (this.allTracks && this.allTracks.length != 0) {\r\n\t\t\t\t\t\tthis.$emit('clearTracks')\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (this.previewTimeInterval) {\r\n\t\t\t\t\t\tclearInterval(this.previewTimeInterval)\r\n\t\t\t\t\t\tthis.previewTimeInterval = null\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (this.previewTimeInterval2) {\r\n\t\t\t\t\t\tclearInterval(this.previewTimeInterval2)\r\n\t\t\t\t\t\tthis.previewTimeInterval2 = null\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.exitRoom()\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\toutroom() {\r\n\t\t\tthis.closeDialog()\r\n\t\t\tthis.isInstantMessageShow = false\r\n\t\t\tif (this.allTracks && this.allTracks.length != 0) {\r\n\t\t\t\tthis.$emit('clearTracks')\r\n\t\t\t}\r\n\t\t\tif (this.previewTimeInterval) {\r\n\t\t\t\tclearInterval(this.previewTimeInterval)\r\n\t\t\t\tthis.previewTimeInterval = null\r\n\t\t\t}\r\n\t\t\tif (this.previewTimeInterval2) {\r\n\t\t\t\tclearInterval(this.previewTimeInterval2)\r\n\t\t\t\tthis.previewTimeInterval2 = null\r\n\t\t\t}\r\n\t\t\tthis.exitRoom()\r\n\t\t},\r\n\t\t// 用户管理\r\n\t\t// 控制指定用户麦克风\r\n\t\tchangeParticipantMicrophone(e) {\r\n\t\t\t// if (participant.isMicrophoneEnabled) {\r\n\t\t\t// \tthis.liveClient.changeParticipantMicrophoneStatus(participant.identity, true)\r\n\t\t\t// } else {\r\n\t\t\t// \tthis.liveClient.changeParticipantMicrophoneStatus(participant.identity, false)\r\n\t\t\t// }\r\n\t\t\tif (this.remoteStartAudioUserIdList.has(e.split('_main')[0])) {\r\n\t\t\t\tthis.trtc.sendCustomMessage({\r\n\t\t\t\t\tcmdId: 5, // 1是聊天内容  5关麦克风\r\n\t\t\t\t\tdata: new TextEncoder().encode(e).buffer,\r\n\t\t\t\t})\r\n\t\t\t} else {\r\n\t\t\t\tthis.trtc.sendCustomMessage({\r\n\t\t\t\t\tcmdId: 6, // 1是聊天内容  6开麦克风\r\n\t\t\t\t\tdata: new TextEncoder().encode(e).buffer,\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 点击更多按钮\r\n\t\topenMoreDialog(identity, e) {\r\n\t\t\tconsole.log('更多按钮点击', e)\r\n\t\t\tthis.optionDialogOffset = {\r\n\t\t\t\tleft: e.target.offsetLeft - 32 + 'px',\r\n\t\t\t\ttop: e.target.offsetTop + 16 + 'px',\r\n\t\t\t}\r\n\t\t\tif (this.optionIdentity === identity && this.moreDialogShow) {\r\n\t\t\t\tthis.moreDialogShow = false\r\n\t\t\t} else {\r\n\t\t\t\tthis.moreDialogShow = false\r\n\t\t\t\tthis.optionIdentity = identity\r\n\t\t\t\tthis.moreDialogShow = true\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 修改与会者名称\r\n\t\tchangeParticipantName(e) {\r\n\t\t\tthis.moreDialogShow = false\r\n\t\t\tthis.changingIdentity = e\r\n\t\t\tthis.changeNameShow = true\r\n\t\t},\r\n\t\tasync updateParticipantName(arg) {\r\n\t\t\t// console.log('修改与会者名称', arg);\r\n\t\t\tlet [name, identity] = arg\r\n\t\t\t// await this.liveClient.updateParticipantName(identity, name)\r\n\t\t\tthis.trtc.sendCustomMessage({\r\n\t\t\t\tcmdId: 3, // 1是聊天内容  3改名字\r\n\t\t\t\tdata: new TextEncoder().encode(identity + '_' + name).buffer,\r\n\t\t\t})\r\n\t\t\tthis.changeNameShow = false\r\n\t\t},\r\n\t\t// 移除与会者\r\n\t\tasync removeParticipant(e) {\r\n\t\t\t// if (e === this.liveClient.userId) {\r\n\t\t\t// \tthis.$messageNew.warning({\r\n\t\t\t// \t\tmessage: '无法移出自己，若想退出会议，请点击挂断',\r\n\t\t\t// \t})\r\n\t\t\t// } else {\r\n\t\t\t// \t// 移除与会者\r\n\t\t\t// \tawait this.liveClient.moveoutParticipant(e)\r\n\t\t\t// }\r\n\t\t\tthis.trtc.sendCustomMessage({\r\n\t\t\t\tcmdId: 4, // 1是聊天内容  4踢人\r\n\t\t\t\tdata: new TextEncoder().encode(e).buffer,\r\n\t\t\t})\r\n\t\t\tthis.moreDialogShow = false\r\n\t\t},\r\n\t\t// 关闭与会者摄像头\r\n\t\tcloseParticipantCamera(e) {\r\n\t\t\t// if (this.participants && this.participants.length > 0) {\r\n\t\t\t// \tlet index = this.participants.findIndex((item) => {\r\n\t\t\t// \t\treturn item.identity === e\r\n\t\t\t// \t})\r\n\t\t\t// \tif (index > -1 && this.participants[index].isCameraEnabled) {\r\n\t\t\t// \t\tthis.liveClient.changeParticipantCameraStatus(e, true)\r\n\t\t\t// \t}\r\n\t\t\t// }\r\n\t\t\t// userId = user_手机号_main_参会名\r\n\t\t\tif (this.remoteStartVideoUserIdList.has(e.split('_main')[0])) {\r\n\t\t\t\tthis.trtc.sendCustomMessage({\r\n\t\t\t\t\tcmdId: 2, // 1是聊天内容  其余都是指令 2关闭摄像头\r\n\t\t\t\t\tdata: new TextEncoder().encode(e).buffer,\r\n\t\t\t\t})\r\n\t\t\t} else {\r\n\t\t\t\tthis.trtc.sendCustomMessage({\r\n\t\t\t\t\tcmdId: 7, // 1是聊天内容  其余都是指令 7开摄像头\r\n\t\t\t\t\tdata: new TextEncoder().encode(e).buffer,\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 全员禁言\r\n\t\tmuteAllMicrophone() {\r\n\t\t\t// this.remoteStopAudioUserIdList.clear()\r\n\t\t\tthis.remoteStartAudioUserIdList.clear()\r\n\t\t\tthis.remoteUsersViews.length > 0 &&\r\n\t\t\t\tthis.remoteUsersViews.forEach((d) => {\r\n\t\t\t\t\tthis.remoteStopAudioUserIdList.add(d.split('_main')[0])\r\n\t\t\t\t\tthis.trtc.sendCustomMessage({\r\n\t\t\t\t\t\tcmdId: 5, // 1是聊天内容  5关麦克风\r\n\t\t\t\t\t\tdata: new TextEncoder().encode(d).buffer,\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t},\r\n\t\tdisableMemberMicrophone(e) {\r\n\t\t\tif (this.liveClient) {\r\n\t\t\t\tthis.liveClient.changeParticipantMicrophoneStatus(e, true)\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 全员关闭摄像头\r\n\t\tcloseAllCamera() {\r\n\t\t\t// this.remoteStopVideoUserIdList.clear()\r\n\t\t\tthis.remoteStartVideoUserIdList.clear()\r\n\t\t\tthis.remoteUsersViews.length > 0 &&\r\n\t\t\t\tthis.remoteUsersViews.forEach((d) => {\r\n\t\t\t\t\tthis.remoteStopAudioUserIdList.add(d.split('_main')[0])\r\n\t\t\t\t\tthis.trtc.sendCustomMessage({\r\n\t\t\t\t\t\tcmdId: 2, // 1是聊天内容  5关麦克风\r\n\t\t\t\t\t\tdata: new TextEncoder().encode(d).buffer,\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t},\r\n\t\t// 邀请\r\n\t\t// 点击邀请按钮\r\n\t\tchooseInviteWay() {\r\n\t\t\tthis.inviteOptionShow = !this.inviteOptionShow\r\n\t\t},\r\n\t\t// 打开通讯录\r\n\t\topenAddressBook() {\r\n\t\t\tthis.inviteOptionShow = false\r\n\t\t\tthis.addressBookShow = true\r\n\t\t},\r\n\t\t// 打开短信邀请弹窗\r\n\t\topenMessageBoard() {\r\n\t\t\tthis.inviteOptionShow = false\r\n\t\t\tthis.messageInviteShow = true\r\n\t\t},\r\n\t\t// 发送短信邀请\r\n\t\tsendMessageInvite(e) {\r\n\t\t\t//e是电话号码\r\n\t\t\tlet params = {\r\n\t\t\t\ttoUser: e,\r\n\t\t\t\tmessage: encodeURI(\r\n\t\t\t\t\t`${location.origin}${location.pathname}#/invite?roomId=${this.roomId}&userId=user_${e}&roomName=${this.roomName1}`,\r\n\t\t\t\t),\r\n\t\t\t}\r\n\t\t\t/* let params2 = {\r\n        query: encodeURI(`roomNum=${this.roomNum}&tenantId=1478649882954985474&identity=${e}`)\r\n         \r\n      }\r\n      let header = {\r\n        signature: util.encryptWxLink(\r\n          `roomNum=${this.roomNum}&tenantId=1478649882954985474&identity=${e}`\r\n        )\r\n      }\r\n      generateWxLink(params2, header).then(res => {\r\n        console.log('链接地址', res)\r\n        if (res.code == 0) {\r\n          params.message = res.data['url_link']\r\n          sendInviteMessage(params).then(res => {\r\n            this.$messageNew.message('success', {\r\n              message: '短信发送成功'\r\n            })\r\n            this.messageInviteShow = false\r\n          })\r\n        } else {\r\n          this.$messageNew.message('error', {\r\n            message: '生成小程序链接失败'\r\n          })\r\n        }\r\n      }) */\r\n\t\t\tsendInviteMessage(params).then((res) => {\r\n\t\t\t\tthis.$messageNew.message('success', {\r\n\t\t\t\t\tmessage: '短信发送成功',\r\n\t\t\t\t})\r\n\t\t\t\tthis.messageInviteShow = false\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 对被发起人发送短信邀请\r\n\t\tsendMessageInvite2(mobile) {\r\n\t\t\tlet params = {\r\n\t\t\t\ttoUser: mobile,\r\n\t\t\t\tmessage: ``,\r\n\t\t\t}\r\n\t\t\tlet params2 = {\r\n\t\t\t\tquery: encodeURI(`roomNum=${this.roomNum}&tenantId=1478649882954985474&identity=${mobile}`),\r\n\t\t\t}\r\n\t\t\tlet header = {\r\n\t\t\t\tsignature: util.encryptWxLink(`roomNum=${this.roomNum}&tenantId=1478649882954985474&identity=${mobile}`),\r\n\t\t\t}\r\n\t\t\tgenerateWxLink(params2, header).then((res) => {\r\n\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\tparams.message = res.data['url_link']\r\n\t\t\t\t\tsendInviteMessage(params).then((res) => {\r\n\t\t\t\t\t\tthis.$messageNew.message('success', {\r\n\t\t\t\t\t\t\tmessage: '短信发送成功',\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$messageNew.message('error', {\r\n\t\t\t\t\t\tmessage: '生成小程序链接失败',\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 向PC登录用户发送邀请 identity-被邀请人姓名\r\n\t\tsendPcInvite(identity) {\r\n\t\t\tthis.liveClient.inviteThirdParty(identity)\r\n\t\t},\r\n\t\tasync closeDialog() {\r\n\t\t\tconsole.log('关闭多人会议弹窗')\r\n\t\t\tif (this.liveClient) {\r\n\t\t\t\tawait this.liveClient.close()\r\n\t\t\t}\r\n\t\t\tif (this.liveClient2) {\r\n\t\t\t\tawait this.liveClient2.close()\r\n\t\t\t}\r\n\t\t\tthis.clearAllInterval()\r\n\t\t\tthis.clearTrack()\r\n\t\t\tthis.dispatchLiveClientEvent()\r\n\r\n\t\t\tthis.$emit('multiMeetingClose')\r\n\t\t\tthis.$emit('input', false)\r\n\t\t},\r\n\t\tclipTextToBoard() {\r\n\t\t\tlet dom = document.getElementById('roomNum')\r\n\t\t\tif (dom) {\r\n\t\t\t\tif (navigator.clipboard) {\r\n\t\t\t\t\tlet content = dom.innerHTML\r\n\t\t\t\t\tnavigator.clipboard\r\n\t\t\t\t\t\t.writeText(content)\r\n\t\t\t\t\t\t.then(() => {\r\n\t\t\t\t\t\t\tthis.$messageNew.message('success', {\r\n\t\t\t\t\t\t\t\tmessage: '已复制到剪贴板',\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\t\tconsole.error('复制失败', err)\r\n\t\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$messageNew.message('warning', {\r\n\t\t\t\t\t\tmessage: '当前浏览器不支持复制内容',\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\trefuseAgree() {\r\n\t\t\tlet data = {\r\n\t\t\t\ttype: '1', //1申请入会，2被解散\r\n\t\t\t\tmessage: 'refuse',\r\n\t\t\t\tsendUser: this.$store.state.mobile,\r\n\t\t\t\tacceptUser: this.AgreeInfo.split('_')[0],\r\n\t\t\t}\r\n\t\t\tthis.liveClient.send(JSON.stringify(data))\r\n\t\t\tthis.AgreeBoardShow = false\r\n\t\t},\r\n\t\tacceptAgree() {\r\n\t\t\tlet data = {\r\n\t\t\t\ttype: '1', //1申请入会，2被解散\r\n\t\t\t\tmessage: 'agree',\r\n\t\t\t\tsendUser: this.$store.state.mobile,\r\n\t\t\t\tacceptUser: this.AgreeInfo.split('_')[0],\r\n\t\t\t}\r\n\t\t\tthis.liveClient.send(JSON.stringify(data))\r\n\t\t\tthis.AgreeBoardShow = false\r\n\t\t},\r\n\t\tasync handleStartScreenShare() {\r\n\t\t\tif (this.remoteScreenViews.length != 0) {\r\n\t\t\t\tthis.$message.warning('有人正在共享屏幕！请稍后再试...')\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tif (!this.localScreenShow) {\r\n\t\t\t\tthis.localScreenShow = !this.localScreenShow\r\n\t\t\t\tthis.shareStatus = 'sharing'\r\n\t\t\t\ttry {\r\n\t\t\t\t\tawait this.trtc.startScreenShare()\r\n\t\t\t\t\tthis.shareStatus = 'shared'\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tthis.shareStatus = 'stopped'\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tthis.localScreenShow = !this.localScreenShow\r\n\t\t\t\tif (this.shareStatus !== 'shared') {\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis.shareStatus = 'stopping'\r\n\t\t\t\ttry {\r\n\t\t\t\t\tawait this.trtc.stopScreenShare()\r\n\t\t\t\t\tthis.shareStatus = 'stopped'\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tthis.shareStatus = 'shared'\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t},\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.multi-dialog {\r\n\twidth: 1236px;\r\n\theight: 822px;\r\n\tposition: absolute;\r\n\tz-index: 3000;\r\n\tleft: 339px;\r\n\ttop: 128px;\r\n\tbackground: url('~@/assets/service/multi1.png') no-repeat center / 100% 100%;\r\n\t&-expand {\r\n\t\twidth: 1522px !important;\r\n\t\tbackground: url('~@/assets/service/multi2.png') no-repeat center / 100% 100%;\r\n\t}\r\n\t.dialog-head {\r\n\t\theight: 55px;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 0 40px 0 38px;\r\n\t\t&-left {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\t.theme {\r\n\t\t\t\twidth: 72px;\r\n\t\t\t\tline-height: 32px;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tbackground: #59a1ff;\r\n\t\t\t\tborder-radius: 16px;\r\n\t\t\t\tfont-family: Source Han Sans CN;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tfont-size: 20px;\r\n\t\t\t\tcolor: #ffffff;\r\n\t\t\t\tmargin-right: 16px;\r\n\t\t\t}\r\n\t\t\t.invited {\r\n\t\t\t\tfont-family: YouSheBiaoTiHei;\r\n\t\t\t\tfont-size: 28px;\r\n\t\t\t\tcolor: #ffffff;\r\n\t\t\t\tline-height: 36px;\r\n\t\t\t\tmargin-right: 38px;\r\n\t\t\t\tpadding-right: 21px;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\t&::after {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tright: 0;\r\n\t\t\t\t\ttop: 17px;\r\n\t\t\t\t\twidth: 12px;\r\n\t\t\t\t\theight: 6px;\r\n\t\t\t\t\tbackground: url('~@/assets/service/to-bottom.png') no-repeat center / 100% 100%;\r\n\t\t\t\t}\r\n\t\t\t\t.meeting-show-board {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tz-index: 5000;\r\n\t\t\t\t\tleft: 0;\r\n\t\t\t\t\ttop: 36px;\r\n\t\t\t\t\twidth: 392px;\r\n\t\t\t\t\tborder-radius: 8px;\r\n\t\t\t\t\tbackground: linear-gradient(to bottom, rgba(66, 130, 208, 0.9) 0%, rgba(17, 48, 88, 0.9) 100%);\r\n\t\t\t\t\tpadding: 21px 31px;\r\n\t\t\t\t\t&-title {\r\n\t\t\t\t\t\tfont-family: YouSheBiaoTiHei;\r\n\t\t\t\t\t\tfont-size: 28px;\r\n\t\t\t\t\t\tcolor: #ffffff;\r\n\t\t\t\t\t\tline-height: 36px;\r\n\t\t\t\t\t\ttext-align: left;\r\n\t\t\t\t\t\tmargin-bottom: 18px;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t&-item {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: flex-start;\r\n\t\t\t\t\t\tflex-wrap: nowrap;\r\n\t\t\t\t\t\tfont-family: Source Han Sans CN;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tfont-size: 14px;\r\n\t\t\t\t\t\tcolor: #ffffff;\r\n\t\t\t\t\t\tline-height: 21px;\r\n\t\t\t\t\t\tmargin-bottom: 30px;\r\n\t\t\t\t\t\t&-title {\r\n\t\t\t\t\t\t\tflex-shrink: 0;\r\n\t\t\t\t\t\t\twidth: 72px;\r\n\t\t\t\t\t\t\ttext-align: left;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t&-value {\r\n\t\t\t\t\t\t\tflex-shrink: 0;\r\n\t\t\t\t\t\t\twidth: calc(100% - 72px);\r\n\t\t\t\t\t\t\ttext-align: left;\r\n\t\t\t\t\t\t\t.icon {\r\n\t\t\t\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\t\t\t\twidth: 12px;\r\n\t\t\t\t\t\t\t\theight: 12px;\r\n\t\t\t\t\t\t\t\tcursor: pointer;\r\n\t\t\t\t\t\t\t\tmargin-left: 10px;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t.clip-icon {\r\n\t\t\t\t\t\t\t\tbackground: url('~@/assets/service/clip-icon.png') no-repeat center / 100% 100%;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.call-duration {\r\n\t\t\t\tfont-family: Source Han Sans CN;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tcolor: #cbe5ff;\r\n\t\t\t\tline-height: 21px;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.full-btns {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\t.full-btn {\r\n\t\t\t\t&:not(:last-child) {\r\n\t\t\t\t\tmargin-right: 10px;\r\n\t\t\t\t}\r\n\t\t\t\twidth: 32px;\r\n\t\t\t\theight: 32px;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\t&:nth-child(1) {\r\n\t\t\t\t\tbackground: url('~@/assets/service/slide.png') no-repeat center / 100% 100%;\r\n\t\t\t\t}\r\n\t\t\t\t&:nth-child(2) {\r\n\t\t\t\t\tbackground: url('~@/assets/service/full1.png') no-repeat center / 100% 100%;\r\n\t\t\t\t}\r\n\t\t\t\t&:nth-child(3) {\r\n\t\t\t\t\tbackground: url('~@/assets/service/close.png') no-repeat center / 100% 100%;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.dialog-contain {\r\n\t\theight: calc(100% - 110px);\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: nowrap;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t\t.meeting-contain {\r\n\t\t\twidth: 1236px;\r\n\t\t\theight: 100%;\r\n\t\t\tpadding: 8px;\r\n\t\t\t-ms-overflow-style: none;\r\n\t\t\tscrollbar-width: none;\r\n\t\t\t&::-webkit-scrollbar {\r\n\t\t\t\tdisplay: none; /* Chrome Safari */\r\n\t\t\t}\r\n\t\t}\r\n\t\t.member-manage {\r\n\t\t\tmargin-right: 13px;\r\n\t\t\twidth: calc(100% - 1249px);\r\n\t\t\theight: 100%;\r\n\t\t\tbackground: #0a3368;\r\n\t\t\tborder-radius: 5px;\r\n\t\t\tpadding: 20px;\r\n\t\t\t&-top {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tmargin-bottom: 16px;\r\n\t\t\t\t::v-deep {\r\n\t\t\t\t\t.el-input {\r\n\t\t\t\t\t\twidth: 237px !important;\r\n\t\t\t\t\t\t.el-input__inner {\r\n\t\t\t\t\t\t\theight: 36px;\r\n\t\t\t\t\t\t\tline-height: 36px;\r\n\t\t\t\t\t\t\tborder: 1px solid #cbe5ff !important;\r\n\t\t\t\t\t\t\tbackground-color: transparent !important;\r\n\t\t\t\t\t\t\tfont-size: 16px !important;\r\n\t\t\t\t\t\t\tcolor: #ffffff !important;\r\n\t\t\t\t\t\t\tfont-family: Source Han Sans CN;\r\n\t\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t.el-input__icon {\r\n\t\t\t\t\t\t\tline-height: 36px;\r\n\t\t\t\t\t\t\tcolor: #00aaff;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t&-contain {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: calc(100% - 100px);\r\n\t\t\t\toverflow-y: auto;\r\n\t\t\t\t&::-webkit-scrollbar {\r\n\t\t\t\t\t/*滚动条整体样式*/\r\n\t\t\t\t\twidth: 0px;\r\n\t\t\t\t\tbackground: transparent;\r\n\t\t\t\t}\r\n\t\t\t\t&::-webkit-scrollbar-thumb {\r\n\t\t\t\t\t/*滚动条里面小方块*/\r\n\t\t\t\t\tborder-radius: 2px;\r\n\t\t\t\t\tbox-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);\r\n\t\t\t\t\tbackground: rgb(45, 124, 228);\r\n\t\t\t\t\theight: 100px;\r\n\t\t\t\t}\r\n\t\t\t\t.member-list {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\t&-group {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tflex-wrap: nowrap;\r\n\t\t\t\t\t\t.avatar {\r\n\t\t\t\t\t\t\twidth: 32px;\r\n\t\t\t\t\t\t\theight: 32px;\r\n\t\t\t\t\t\t\tbackground: url('~@/assets/service/avatar.png') no-repeat center / 100% 100%;\r\n\t\t\t\t\t\t\tmargin-right: 10px;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t.name {\r\n\t\t\t\t\t\t\tfont-family: Source Han Sans CN;\r\n\t\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\t\tfont-size: 16px;\r\n\t\t\t\t\t\t\tcolor: #ffffff;\r\n\t\t\t\t\t\t\tline-height: 24px;\r\n\t\t\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\t\t\twidth: 140px;\r\n\t\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\t\ttext-align: left;\r\n\t\t\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t.member-icon {\r\n\t\t\t\t\t\t\twidth: 16px;\r\n\t\t\t\t\t\t\theight: 16px;\r\n\t\t\t\t\t\t\tcursor: pointer;\r\n\t\t\t\t\t\t\t&:not(:last-child) {\r\n\t\t\t\t\t\t\t\tmargin-right: 8px;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t.mic-on {\r\n\t\t\t\t\t\t\tbackground: url('~@/assets/service/mic-on.png') no-repeat center / 100% 100%;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t.mic-off {\r\n\t\t\t\t\t\t\tbackground: url('~@/assets/service/mic-off.png') no-repeat center / 100% 100%;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t.more {\r\n\t\t\t\t\t\t\tbackground: url('~@/assets/service/more.png') no-repeat center / 100% 100%;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t&:not(:last-of-type) {\r\n\t\t\t\t\t\tmargin-bottom: 20px;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t&-bottom {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 50px;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t.member-manage-btn {\r\n\t\t\t\t\tline-height: 36px;\r\n\t\t\t\t\tpadding: 0 10px;\r\n\t\t\t\t\tbackground: url('~@/assets/service/board-btn-bg.png') no-repeat center / 100% 100%;\r\n\t\t\t\t\tcursor: pointer;\r\n\t\t\t\t\tfont-family: Source Han Sans CN;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tfont-size: 16px;\r\n\t\t\t\t\tcolor: #ffffff;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.dialog-foot {\r\n\t\theight: 50px;\r\n\t\tmargin-bottom: 5px;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 0 43px 0 20px;\r\n\t\tposition: relative;\r\n\t\t.point-foot-group {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tflex-wrap: nowrap;\r\n\t\t\t.foot-btn {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tpadding: 0 32px;\r\n\t\t\t\tfont-family: Source Han Sans CN;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tcolor: #ffffff;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\t&:not(:last-of-type) {\r\n\t\t\t\t\tborder-right: 1px solid;\r\n\t\t\t\t\tborder-image: linear-gradient(180deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.1)) 1 1;\r\n\t\t\t\t}\r\n\t\t\t\t&-icon {\r\n\t\t\t\t\twidth: 28px;\r\n\t\t\t\t\theight: 28px;\r\n\t\t\t\t\tmargin-right: 6px;\r\n\t\t\t\t}\r\n\t\t\t\t.mic-on {\r\n\t\t\t\t\tbackground: url('~@/assets/service/mic-on.png') no-repeat center / 100% 100%;\r\n\t\t\t\t}\r\n\t\t\t\t.mic-off {\r\n\t\t\t\t\tbackground: url('~@/assets/service/mic-off.png') no-repeat center / 100% 100%;\r\n\t\t\t\t}\r\n\t\t\t\t.cam-on {\r\n\t\t\t\t\tbackground: url('~@/assets/service/cam-on.png') no-repeat center / 100% 100%;\r\n\t\t\t\t}\r\n\t\t\t\t.cam-off {\r\n\t\t\t\t\tbackground: url('~@/assets/service/cam-off.png') no-repeat center / 100% 100%;\r\n\t\t\t\t}\r\n\t\t\t\t.member {\r\n\t\t\t\t\tbackground: url('~@/assets/service/member.png') no-repeat center / 100% 100%;\r\n\t\t\t\t}\r\n\t\t\t\t.invite {\r\n\t\t\t\t\tbackground: url('~@/assets/service/invite.png') no-repeat center / 100% 100%;\r\n\t\t\t\t}\r\n\t\t\t\t.screen-on {\r\n\t\t\t\t\tbackground: url('~@/assets/service/screen-on.png') no-repeat center / 100% 100%;\r\n\t\t\t\t}\r\n\t\t\t\t.screen-off {\r\n\t\t\t\t\tbackground: url('~@/assets/service/screen-off.png') no-repeat center / 100% 100%;\r\n\t\t\t\t}\r\n\t\t\t\t.hang-off {\r\n\t\t\t\t\tbackground: url('~@/assets/service/hang-off.png') no-repeat center / 100% 100%;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n.local-stream-content {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n}\r\n.remote-stream-container {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\t// width: 320px;\r\n\t// height: 240px;\r\n\t// margin: 0 10px 10px 0;\r\n\t.screen-share-name {\r\n\t\tposition: absolute;\r\n\t\twidth: 100%;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 17px;\r\n\t\tfont-weight: bold;\r\n\t}\r\n}\r\n</style>\r\n<style lang=\"scss\">\r\n@import './style/multipleMeetingStyle.scss';\r\n</style>\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/views/leader/components/service/multipleMeeting.vue b/src/views/leader/components/service/multipleMeeting.vue
--- a/src/views/leader/components/service/multipleMeeting.vue	(revision a72b0702f0419dde89e3780235f8d0a4d356a708)
+++ b/src/views/leader/components/service/multipleMeeting.vue	(date 1758002345848)
@@ -1375,7 +1375,7 @@
 		async hangOff() {
 			// this.leaveRoom()
 			if (this.localScreenShow) this.localScreenShow = !this.localScreenShow
-			if (this.shareStatus !== 'shared') {
+			if (this.shareStatus === 'shared') {
 				return
 			}
 			this.shareStatus = 'stopping'
