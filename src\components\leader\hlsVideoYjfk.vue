<template>
 <div>
  <video
      ref="myVideo"
      class="video-js vjs-default-skin vjs-big-play-centered vjs-fluid vjs-16-9"
      controls
      :playsinline="true"
      preload="auto"
    ></video>
 </div>
</template>

<script>
import Video from 'video.js';
import 'video.js/dist/video-js.css';
// import { getVideo } from '@/api/common/common';
// import {slfhCamera} from '@/api/beihai/yingjifangkong.js';
// import { monitorDetail } from '@/api/beihai/quanjingtaishi.js';

export default {
  name: 'hls-video',
  props: {
    // 请求路径
    src: {
      type: String,
      default: ''
    },
    // 视频再次请求id
    cameraCode: {
      type: String,
      default: ''
    },
    // 摄像流来源
    isSource: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isLoading: false,
      times: null,
      timespace: 15000,
    };
  },
  beforeDestroy() {
    if (this.player) {
      this.player.off('waiting');
      this.player.off('playing');
      this.player.off('error');
      // this.player.stop();
      this.player.dispose();
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initVideo();
    });
  },
  watch: {
    src(newSrc) {
      console.log('newSrc',newSrc);
      this.isUrlShow = false
      this.playFromSrc(newSrc);
    }
  },
  methods: {
    initVideo() {
      //初始化视频方法
      this.player = Video(this.$refs.myVideo, {
        //确定播放器是否具有用户可以与之交互的控件。没有控件，启动视频播放的唯一方法是使用autoplay属性或通过Player API。
        controls: true,
        //自动播放属性,muted:静音播放
        autoplay: 'muted',
        //建议浏览器是否应在<video>加载元素后立即开始下载视频数据。
        preload: 'auto'
      });
      this.player.on('error', this.onError);
      this.player.on('waiting', this.onWaitting);
      this.player.on('playing', this.onOpen);
      console.log('this.src',this.src);
      if(this.src){
        this.playFromSrc(this.src);
      }
      // this.player.duration();
    },
    // 播放
    playFromSrc(src) {
      console.log('src',src);
      this.player.src({
        src,
        type: 'application/x-mpegURL'
      });
    },
    // 重新获取在线地址
    // getHlsUrl() {
    //   if (!this.cameraCode) {
    //     return '';
    //   }
    //   return getVideo({
    //     cameraIndexCode: this.cameraCode
    //   });
    //   //   return this.$http.get(
    //   //     baseUrl + '/hd/video/CamerasQuery/regetCamerasURl?cameraIndexCode=' + this.cameraCode
    //   //   );
    // },
    async onError() {
      // console.log('加载错误', this.src);
      // let arr = await this.getHlsUrl();
      // let { url } = arr[0];
      // console.log('新地址', url);
      console.log('isSource',this.isSource);
      if(this.isSource=='linhuo'){
        slfhCamera(this.cameraCode).then(res => {
          if(res.code=='0'){
            console.log(res.data.url2);
            this.playFromSrc(res.data.url2);
          }
        })
      }else if(this.isSource=='xueliang'){
          monitorDetail(this.cameraCode).then(res => {
            if (res.code == '0') {
            console.log(res.data.url);
            this.playFromSrc(res.data.url);
          }
        })
      }
    },
    onWaitting() {
      // console.log("等待数据");
      if (!this.isLoading) {
        this.isLoading = true;
        this.times = setTimeout(() => {
          this.onError();
        }, this.timespace);
      }
    },
    onOpen() {
      // console.log('视频播放中');
      // console.log('this.times', this.times);
      this.times && clearTimeout(this.times);
      this.isLoading = false;
    }
  }
};
</script>

<style lang="scss" scoped>
video {
  width: 100%;
  height: 100%;
  object-fit: fill;
}
.video-js.vjs-16-9:not(.vjs-audio-only-mode) {
  padding-top: 54.25%;
}
</style>
