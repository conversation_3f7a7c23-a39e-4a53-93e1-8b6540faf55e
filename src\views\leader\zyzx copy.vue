<template>
	<div class="leader_box">
		<div class="leader_sjts">
			<div class="left">
				<div class="condition">
					<div class="conditionLeft">
						<el-date-picker
							v-model="dtDay"
							type="date"
							value-format="yyyy-MM-dd"
							placeholder="选择时间"
							@change="dateSwitch"
						></el-date-picker>
					</div>
					<div class="conditionRight">
						<el-select v-model="curSeason" placeholder="请选择" @change="selectSwitch">
							<el-option v-for="item in options" :key="item.type" :label="item.name" :value="item.type"> </el-option>
						</el-select>
					</div>
				</div>
				<div class="content">
					<div class="contentList" :class="navigation == 1 ? 'contentList2' : ''">
						<div class="contentTitle" @click="navigationChage(1)">
							<div class="contentName">作业进度</div>
							<img src="@/assets/bjnj/zyjdTitleBg3.png" v-if="navigation == 1" alt="" />
							<img src="@/assets/bjnj/zyjdTitleBg2.png" v-else alt="" />
						</div>
						<div class="contentData" v-if="navigation == 1">
							<div class="contentSwitch">
								<div class="contentSwitchLeft" :class="contentSwitch == 1 ? 'contentSelect1' : ''" @click="contentChange(1)">
									早稻
								</div>
								<div class="contentSwitchRight" :class="contentSwitch == 2 ? 'contentSelect2' : ''" @click="contentChange(2)">
									晚稻
								</div>
							</div>
							<div class="contentProportion">
								<div class="contentProportion1">整体进度</div>
								<div class="contentProportion2">
									<div
										class="contentRatio"
										:style="{
											width: `${contentSwitch == 1 ? zyjdData.harvest_pgs : zyjdData.sowing_pgs}%`,
										}"
										v-if="zyjdData.harvest_pgs"
									>
										<div class="contentItem" v-for="(item, index) in 16" :key="index"></div>
									</div>
								</div>
								<div class="contentProportion3">{{ contentSwitch == 1 ? zyjdData.harvest_pgs : zyjdData.sowing_pgs }}%</div>
							</div>
							<div class="contentPiechart">
								<PieChart3D2 :data="data" :options="Options2" :init-option="initOption" v-if="PieChartType" />
								<div class="tbmc">{{ contentSwitch == 1 ? '机收占比' : '机种占比' }}</div>
							</div>
							<div class="contentClassify">
								<div class="contentClassifyList">
									<img src="@/assets/bjnj/zyxlImg1.png" alt="" />
									<div class="contentClassifyArea">
										<div class="contentClassifyLeft">
											{{ contentSwitch == 1 ? '总面积(万亩)' : '计划面积(万亩)' }}
										</div>
										<div class="contentClassifyRight">
											{{ contentSwitch == 1 ? zyjdData.total_area : zyjdData.planned_area }}
										</div>
									</div>
								</div>
								<div class="contentClassifyList">
									<img src="@/assets/bjnj/zyxlImg2.png" alt="" />
									<div class="contentClassifyArea">
										<div class="contentClassifyLeft">
											{{ contentSwitch == 1 ? '已收面积(万亩)' : '已种面积(万亩)' }}
										</div>
										<div class="contentClassifyRight">
											{{ contentSwitch == 1 ? zyjdData.collected_area : zyjdData.planted_area }}
										</div>
									</div>
								</div>
								<div class="contentClassifyList">
									<img src="@/assets/bjnj/zyxlImg3.png" alt="" />
									<div class="contentClassifyArea">
										<div class="contentClassifyLeft">
											{{ contentSwitch == 1 ? '机收面积(万亩)' : '机种面积(万亩)' }}
										</div>
										<div class="contentClassifyRight">
											{{ contentSwitch == 1 ? zyjdData.machn_coll_area : zyjdData.machn_sowed_area }}
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="contentList" :class="navigation == 2 ? 'contentList2' : ''">
						<div class="contentTitle" @click="navigationChage(2)">
							<div class="contentName">灾害影响</div>
							<img src="@/assets/bjnj/zyjdTitleBg3.png" v-if="navigation == 2" alt="" />
							<img src="@/assets/bjnj/zyjdTitleBg2.png" v-else alt="" />
						</div>
						<div class="contentData contentData2" v-if="navigation == 2">
							<div class="contentDataTitle">
								<div class="contentDataLeft">省份</div>
								<div class="contentDataRight">影响详情</div>
							</div>
							<div class="contentDataBox">
								<div class="contentDataList" v-for="(item, index) in dataList1" :key="index" @click="zhyxxqJumpto(item)">
									<div class="contentDataLeft">{{ item.area_name }}</div>
									<div class="contentDataRight">{{ item.disaster_impact }}</div>
								</div>
							</div>
						</div>
					</div>
					<div class="contentList" :class="navigation == 3 ? 'contentList2' : ''">
						<div class="contentTitle" @click="navigationChage(3)">
							<div class="contentName">天气形势</div>
							<img src="@/assets/bjnj/zyjdTitleBg3.png" v-if="navigation == 3" alt="" />
							<img src="@/assets/bjnj/zyjdTitleBg2.png" v-else alt="" />
						</div>
						<div class="contentData contentData2" v-if="navigation == 3">
							<div class="contentDataTitle">
								<div class="contentDataLeft">省份</div>
								<div class="contentDataRight">天气形势</div>
							</div>
							<div class="contentDataBox">
								<div class="contentDataList" v-for="(item, index) in dataList2" :key="index" @click="tqxsxqJumpto(item)">
									<div class="contentDataLeft">{{ item.area }}</div>
									<div class="contentDataRight">{{ item.detail }}</div>
								</div>
							</div>
						</div>
					</div>
					<div class="contentList" :class="navigation == 4 ? 'contentList2' : ''">
						<div class="contentTitle" @click="navigationChage(4)">
							<div class="contentName">应对举措</div>
							<img src="@/assets/bjnj/zyjdTitleBg3.png" v-if="navigation == 4" alt="" />
							<img src="@/assets/bjnj/zyjdTitleBg2.png" v-else alt="" />
						</div>
						<div class="contentData contentData2" v-if="navigation == 4">
							<div class="contentDataTitle">
								<div class="contentDataLeft">省份</div>
								<div class="contentDataRight">应对措施</div>
							</div>
							<div class="contentDataBox">
								<div class="contentDataList" v-for="(item, index) in dataList3" :key="index" @click="ydjcxqJumpto(item)">
									<div class="contentDataLeft">{{ item.area_name }}</div>
									<div class="contentDataRight">{{ item.current_ctmss }}</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="right">
				<SvgMap ref="SvgMap" :contentSwitch="contentSwitch" @warningEvent="warningEvent" />
			</div>
		</div>
		<div class="btnBox" v-if="navigation == 3">
			<div
				class="btnItem"
				:class="btnShow == index ? 'btnItem2' : ''"
				v-for="(item, index) in btnList"
				:key="index"
				@click="btnActive(index)"
			>
				<span>{{ item.name }}</span>
			</div>
		</div>
		<LeaderFooter :btns="btns2" />
		<jcyjList ref="jcyjList" v-model="isjcyjListShow" :areaId="areaId" :dtDay="dtDay" @operate="eventInfoBtnClick10" />
		<yjxqPop v-model="isyjxqShow" :list="yjxqList" @handle="eventInfoBtnClick5" />
		<zhyxxqPop ref="zhyxxqPop" v-model="zhyxxqShow" :list="zhyxxqCenter" />
		<tqxsxqPop ref="tqxsxqPop" v-model="tqxsxqShow" :list="tqxsxqCenter" />
		<ydjcxqPop ref="ydjcxqPop" v-model="ydjcxqShow" :list="ydjcxqCenter" />
	</div>
</template>

<script>
import dayjs from 'dayjs'
import LeaderFooter from '@/components/leader/common/leaderFooter2.vue'
import SvgMap from '@/components/map/svgMap/index.vue'
import svg_data from '@/components/map/svgMap/data.json'
import jcyjList from '@/components/bjnj/jcyjList.vue' //监测预警
import yjxqPop from '@/components/bjnj/yjxqPop.vue' //预警详情
import zhyxxqPop from '@/components/bjnj/zhyxxqPop.vue' //实况信息详情
import tqxsxqPop from '@/components/bjnj/tqxsxqPop.vue' //天气形势详情
import ydjcxqPop from '@/components/bjnj/ydjcxqPop.vue' //应对举措详情
import { bkcgobjTDK, xnrEpNKvpj, dhACIovrQh, getAlarmByProvince, getAlarmStasticsByProvince } from '@/api/njzl/zyzx.js'
export default {
	components: {
		SvgMap,
		jcyjList,
		yjxqPop,
		zhyxxqPop,
		tqxsxqPop,
		ydjcxqPop,
		LeaderFooter,
	},
	data() {
		return {
			zyjdList: [],
			tqyjList: [],
			zhyxxqCenter: {},
			tqxsxqCenter: {},
			ydjcxqCenter: {},
			PieChartType: false,
			dtDay: dayjs()
				.subtract(1, 'day')
				.format('YYYY-MM-DD'),
			areaId: '',
			areaFlag: 0,
			btns2: [],
			zyjdData: {},
			data: [
				['product', '占比'],
				['机收面积', 20],
				['其余面积', 80],
			],
			Options2: {
				color: ['rgba(240, 159, 66, 1)', 'rgba(0, 168, 229, 1)'],
			},
			navigation: 1,
			contentSwitch: 1,
			curSeason: 'shuangqiang',
			value3: '2024',
			isjcyjListShow: false,
			isyjxqShow: false,
			yjxqList: [],
			zhyxxqShow: false,
			tqxsxqShow: false,
			ydjcxqShow: false,
			btnShow: 0,
			btnList: [
				{
					name: '农业气象预报',
				},
				{
					name: '农业气象专报',
				},
				{
					name: '关键农时农事',
				},
			],
			options: [
				{
					name: '春耕',
					type: 'cg',
				},
				{
					name: '三夏',
					type: 'sanx',
				},
				{
					name: '三秋',
					type: 'sq',
				},
				{
					name: '双抢',
					type: 'shuangqiang',
				},
			],
			dataList1: [
				{
					name: '湖北',
					content:
						'6月16日以来，全省平均降水量324.8毫米，偏多近1.6倍，在全国各省排第1位。持续的强降雨导致农作物累计受灾面积469.89万亩，其中成灾225.6万亩，绝收38.17万亩。粮食作物受灾371.21万亩，其中早稻185.6万亩，中稻148.5万亩，旱杂粮37.11万亩。',
				},
				{
					name: '北京',
					content:
						'6月16日以来，全省平均降水量324.8毫米，偏多近1.6倍，在全国各省排第1位。持续的强降雨导致农作物累计受灾面积469.89万亩，其中成灾225.6万亩，绝收38.17万亩。粮食作物受灾371.21万亩，其中早稻185.6万亩，中稻148.5万亩，旱杂粮37.11万亩。',
				},
				{
					name: '上海',
					content:
						'6月16日以来，全省平均降水量324.8毫米，偏多近1.6倍，在全国各省排第1位。持续的强降雨导致农作物累计受灾面积469.89万亩，其中成灾225.6万亩，绝收38.17万亩。粮食作物受灾371.21万亩，其中早稻185.6万亩，中稻148.5万亩，旱杂粮37.11万亩。',
				},
				{
					name: '海南',
					content:
						'6月16日以来，全省平均降水量324.8毫米，偏多近1.6倍，在全国各省排第1位。持续的强降雨导致农作物累计受灾面积469.89万亩，其中成灾225.6万亩，绝收38.17万亩。粮食作物受灾371.21万亩，其中早稻185.6万亩，中稻148.5万亩，旱杂粮37.11万亩。',
				},
				{
					name: '安徽',
					content:
						'6月16日以来，全省平均降水量324.8毫米，偏多近1.6倍，在全国各省排第1位。持续的强降雨导致农作物累计受灾面积469.89万亩，其中成灾225.6万亩，绝收38.17万亩。粮食作物受灾371.21万亩，其中早稻185.6万亩，中稻148.5万亩，旱杂粮37.11万亩。',
				},
				{
					name: '江苏',
					content:
						'6月16日以来，全省平均降水量324.8毫米，偏多近1.6倍，在全国各省排第1位。持续的强降雨导致农作物累计受灾面积469.89万亩，其中成灾225.6万亩，绝收38.17万亩。粮食作物受灾371.21万亩，其中早稻185.6万亩，中稻148.5万亩，旱杂粮37.11万亩。',
				},
			],
			dataList2: [
				{
					name: '湖北',
					content: '未来3——5天全省总体天气晴好',
				},
				{
					name: '北京',
					content:
						'6月16日以来，全省平均降水量324.8毫米，偏多近1.6倍，在全国各省排第1位。持续的强降雨导致农作物累计受灾面积469.89万亩，其中成灾225.6万亩，绝收38.17万亩。粮食作物受灾371.21万亩，其中早稻185.6万亩，中稻148.5万亩，旱杂粮37.11万亩。',
				},
				{
					name: '上海',
					content: '未来3——5天全省总体天气晴好',
				},
				{
					name: '海南',
					content: '未来3——5天全省总体天气晴好',
				},
				{
					name: '安徽',
					content:
						'6月16日以来，全省平均降水量324.8毫米，偏多近1.6倍，在全国各省排第1位。持续的强降雨导致农作物累计受灾面积469.89万亩，其中成灾225.6万亩，绝收38.17万亩。粮食作物受灾371.21万亩，其中早稻185.6万亩，中稻148.5万亩，旱杂粮37.11万亩。',
				},
				{
					name: '江苏',
					content:
						'6月16日以来，全省平均降水量324.8毫米，偏多近1.6倍，在全国各省排第1位。持续的强降雨导致农作物累计受灾面积469.89万亩，其中成灾225.6万亩，绝收38.17万亩。粮食作物受灾371.21万亩，其中早稻185.6万亩，中稻148.5万亩，旱杂粮37.11万亩。',
				},
			],
			dataList3: [
				{
					name: '湖北',
					content:
						'此次汛期的受灾作物主要为水稻、蔬菜等。以水稻为例，受强降雨影响，我省早稻易发生“雨洗禾花”危害，处于分蘖期的中稻有淹涝风险。针对此情况，农业部门提出，及时排水补肥，并针对不同天气和早稻生长期通过不同举措及肥料配比；注意高温高湿加剧病虫害，强化病虫害的监测、预警和预报，抓住无雨时段及时施药，并推行专业化统防统治；对受灾严重的中稻田及时补种，难以补种的改种晚稻或用上年早稻品种进行翻秋或改种生育期短的甜糯玉米、蔬菜等其他作物；密切关注天气，提前准备物资，合理调配农机，适时抢晴收获早稻。',
				},
				{
					name: '北京',
					content:
						'此次汛期的受灾作物主要为水稻、蔬菜等。以水稻为例，受强降雨影响，我省早稻易发生“雨洗禾花”危害，处于分蘖期的中稻有淹涝风险。针对此情况，农业部门提出，及时排水补肥，并针对不同天气和早稻生长期通过不同举措及肥料配比；注意高温高湿加剧病虫害，强化病虫害的监测、预警和预报，抓住无雨时段及时施药，并推行专业化统防统治；对受灾严重的中稻田及时补种，难以补种的改种晚稻或用上年早稻品种进行翻秋或改种生育期短的甜糯玉米、蔬菜等其他作物；密切关注天气，提前准备物资，合理调配农机，适时抢晴收获早稻。',
				},
				{
					name: '上海',
					content:
						'此次汛期的受灾作物主要为水稻、蔬菜等。以水稻为例，受强降雨影响，我省早稻易发生“雨洗禾花”危害，处于分蘖期的中稻有淹涝风险。针对此情况，农业部门提出，及时排水补肥，并针对不同天气和早稻生长期通过不同举措及肥料配比；注意高温高湿加剧病虫害，强化病虫害的监测、预警和预报，抓住无雨时段及时施药，并推行专业化统防统治；对受灾严重的中稻田及时补种，难以补种的改种晚稻或用上年早稻品种进行翻秋或改种生育期短的甜糯玉米、蔬菜等其他作物；密切关注天气，提前准备物资，合理调配农机，适时抢晴收获早稻。',
				},
				{
					name: '海南',
					content:
						'此次汛期的受灾作物主要为水稻、蔬菜等。以水稻为例，受强降雨影响，我省早稻易发生“雨洗禾花”危害，处于分蘖期的中稻有淹涝风险。针对此情况，农业部门提出，及时排水补肥，并针对不同天气和早稻生长期通过不同举措及肥料配比；注意高温高湿加剧病虫害，强化病虫害的监测、预警和预报，抓住无雨时段及时施药，并推行专业化统防统治；对受灾严重的中稻田及时补种，难以补种的改种晚稻或用上年早稻品种进行翻秋或改种生育期短的甜糯玉米、蔬菜等其他作物；密切关注天气，提前准备物资，合理调配农机，适时抢晴收获早稻。',
				},
				{
					name: '安徽',
					content:
						'此次汛期的受灾作物主要为水稻、蔬菜等。以水稻为例，受强降雨影响，我省早稻易发生“雨洗禾花”危害，处于分蘖期的中稻有淹涝风险。针对此情况，农业部门提出，及时排水补肥，并针对不同天气和早稻生长期通过不同举措及肥料配比；注意高温高湿加剧病虫害，强化病虫害的监测、预警和预报，抓住无雨时段及时施药，并推行专业化统防统治；对受灾严重的中稻田及时补种，难以补种的改种晚稻或用上年早稻品种进行翻秋或改种生育期短的甜糯玉米、蔬菜等其他作物；密切关注天气，提前准备物资，合理调配农机，适时抢晴收获早稻。',
				},
				{
					name: '江苏',
					content:
						'此次汛期的受灾作物主要为水稻、蔬菜等。以水稻为例，受强降雨影响，我省早稻易发生“雨洗禾花”危害，处于分蘖期的中稻有淹涝风险。针对此情况，农业部门提出，及时排水补肥，并针对不同天气和早稻生长期通过不同举措及肥料配比；注意高温高湿加剧病虫害，强化病虫害的监测、预警和预报，抓住无雨时段及时施药，并推行专业化统防统治；对受灾严重的中稻田及时补种，难以补种的改种晚稻或用上年早稻品种进行翻秋或改种生育期短的甜糯玉米、蔬菜等其他作物；密切关注天气，提前准备物资，合理调配农机，适时抢晴收获早稻。',
				},
			],
		}
	},
	computed: {
		initOption() {
			return {
				legend: {
					show: false,
				},
			}
		},
	},
	async mounted() {
		this.$store.commit('invokerightShow', false)
		this.bkcgobjTDK()
		this.bkcgobjTDK2()
		this.xnrEpNKvpj()
		this.dhACIovrQh()
		this.getAlarmByProvince()
	},
	methods: {
		dateSwitch() {
			this.bkcgobjTDK()
			this.xnrEpNKvpj()
			this.dhACIovrQh()
			this.getAlarmByProvince()
			if (this.navigation == 1) {
				//作业进度
				this.bkcgobjTDK()
				this.bkcgobjTDK2()
				this.$refs.SvgMap.loadPointLayer('', false)
				this.$refs.SvgMap.highlightMultiArea([], false)
			}
			if (this.navigation == 2) {
				//灾害影响
				this.getAlarmStasticsByProvince()
				this.$refs.SvgMap.loadAreaColorLayer([], false)
				this.$refs.SvgMap.highlightMultiArea([], false)
			}
			if (this.navigation == 3) {
				//天气形势
				this.$refs.SvgMap.loadAreaColorLayer([], false)
				this.$refs.SvgMap.loadPointLayer('', false)
				this.$refs.SvgMap.highlightMultiArea([], false)
			}
			if (this.navigation == 4) {
				//应对举措
				this.$refs.SvgMap.loadAreaColorLayer([], false)
				this.$refs.SvgMap.loadPointLayer('', false)
				this.$refs.SvgMap.highlightMultiArea(
					['浙江省', '安徽省', '福建省', '江西省', '湖北省', '湖南省', '广东省', '广西壮族自治区', '海南省', '云南省'],
					true,
				)
			}
		},
		selectSwitch() {
			this.bkcgobjTDK()
			this.bkcgobjTDK2()
			this.xnrEpNKvpj() //灾害影响
			this.dhACIovrQh()
			this.getAlarmByProvince()
			this.getAlarmStasticsByProvince()
			if (this.navigation == 1) {
				this.bkcgobjTDK2()
				this.bkcgobjTDK()
				this.$refs.SvgMap.loadPointLayer('', false)
				this.$refs.SvgMap.highlightMultiArea([], false)
			}
			if (this.navigation == 2) {
				this.getAlarmStasticsByProvince()
				this.$refs.SvgMap.loadAreaColorLayer([], false)
				this.$refs.SvgMap.highlightMultiArea([], false)
			}
			if (this.navigation == 3) {
				this.$refs.SvgMap.loadAreaColorLayer([], false)
				this.$refs.SvgMap.loadPointLayer('', false)
				this.$refs.SvgMap.highlightMultiArea([], false)
			}
			if (this.navigation == 4) {
				this.$refs.SvgMap.loadAreaColorLayer([], false)
				this.$refs.SvgMap.loadPointLayer('', false)
				this.$refs.SvgMap.highlightMultiArea(
					['浙江省', '安徽省', '福建省', '江西省', '湖北省', '湖南省', '广东省', '广西壮族自治区', '海南省', '云南省'],
					true,
				)
			}
		},
		navigationChage(index) {
			this.navigation = index
			if (this.navigation == 1) {
				this.bkcgobjTDK2()
				this.bkcgobjTDK()
				this.$refs.SvgMap.loadPointLayer('', false)
				this.$refs.SvgMap.highlightMultiArea([], false)
			}
			if (this.navigation == 2) {
				this.getAlarmStasticsByProvince()
				this.$refs.SvgMap.loadAreaColorLayer([], false)
				this.$refs.SvgMap.highlightMultiArea([], false)
			}
			if (this.navigation == 3) {
				this.$refs.SvgMap.loadAreaColorLayer([], false)
				this.$refs.SvgMap.loadPointLayer('', false)
				this.$refs.SvgMap.highlightMultiArea([], false)
			}
			if (this.navigation == 4) {
				this.$refs.SvgMap.loadAreaColorLayer([], false)
				this.$refs.SvgMap.loadPointLayer('', false)
				this.$refs.SvgMap.highlightMultiArea(
					['浙江省', '安徽省', '福建省', '江西省', '湖北省', '湖南省', '广东省', '广西壮族自治区', '海南省', '云南省'],
					true,
				)
			}
		},
		async bkcgobjTDK() {
			this.PieChartType = false
			let params = {
				farmingSeason: this.curSeason,
				dtDay: this.dtDay,
				areaId: this.areaId,
				areaFlag: this.areaFlag,
			}
			let res = await bkcgobjTDK(params)
			if (res.data.length > 0) {
				this.zyjdData = res.data[0]
				this.data[1][1] = Number(this.contentSwitch == 1 ? res.data[0].ppot_of_machn_coll : res.data[0].ppot_of_machn_sowed)
				this.data[2][1] = Number(
					this.contentSwitch == 1 ? 100 - res.data[0].ppot_of_machn_coll : 100 - res.data[0].ppot_of_machn_sowed,
				)
				console.log('this.data123', this.data)
				console.log('this.zyjdData123', this.zyjdData)
				this.PieChartType = true
			} else {
				this.zyjdData = {
					harvest_pgs: 0,
					sowing_pgs: 0,
					total_area: 0,
					planned_area: 0,
					collected_area: 0,
					planted_area: 0,
					machn_coll_area: 0,
					machn_sowed_area: 0,
				}
				this.data[1][1] = 0
				this.data[2][1] = 0
				this.PieChartType = true
			}
		},
    //加载区域分区
		async bkcgobjTDK2() {
			this.PieChartType = false
			let params = {
				farmingSeason: this.curSeason,
				dtDay: this.dtDay,
				areaFlag: 1,
			}
			let res = await bkcgobjTDK(params)
			if (res.data.length > 0) {
			  this.zyjdList = res.data
			  await this.$refs.SvgMap.init()
			  this.$refs.SvgMap.loadAreaColorLayer(this.zyjdList, true, '作业进度(%)')
			} else {
			  this.zyjdList = []
			  this.$refs.SvgMap.loadAreaColorLayer([], false)
			}
			//测试
			// const data_ = [
			// 	{
			// 		area_name: '浙江省',
			// 		total_area: '200.6',
			// 		harvest_pgs: '86.14',
			// 		ppot_of_machn_coll: '97.51',
			// 		area_code: '330000',
			// 		early_rice_hvt_sutn: 'null',
			// 		ppot_of_machn_sowed: '75.53',
			// 		sowing_pgs: '70.17',
			// 		machn_coll_area: '168.5',
			// 		machn_sowed_area: '85.8',
			// 		late_rice_rush_pltg_sutn: 'null',
			// 		planted_area: '113.6',
			// 		pgs_cpin: '与去年同期相比，偏快9.1个百分点',
			// 		planned_area: '161.9',
			// 		collected_area: '172.8',
			// 	},
			// 	{
			// 		area_name: '湖南省',
			// 		total_area: '1818',
			// 		harvest_pgs: '94.00',
			// 		ppot_of_machn_coll: '97.13',
			// 		area_code: '430000',
			// 		early_rice_hvt_sutn: 'null',
			// 		ppot_of_machn_sowed: '60.93',
			// 		sowing_pgs: '86.02',
			// 		machn_coll_area: '1660',
			// 		machn_sowed_area: '1001',
			// 		late_rice_rush_pltg_sutn: 'null',
			// 		planted_area: '1643',
			// 		pgs_cpin: '与去年同期相比，收种进度均偏慢。',
			// 		planned_area: '1910',
			// 		collected_area: '1709',
			// 	},
			// 	{
			// 		area_name: '广西壮族自治区',
			// 		total_area: '1226.62',
			// 		harvest_pgs: '94.81',
			// 		ppot_of_machn_coll: '97.59',
			// 		area_code: '450000',
			// 		early_rice_hvt_sutn: 'null',
			// 		ppot_of_machn_sowed: '51.49',
			// 		sowing_pgs: '68.09',
			// 		machn_coll_area: '1135',
			// 		machn_sowed_area: '431',
			// 		late_rice_rush_pltg_sutn: 'null',
			// 		planted_area: '837',
			// 		pgs_cpin: '与去年同期相比基本持平',
			// 		planned_area: '1229.29',
			// 		collected_area: '1163',
			// 	},
			// 	{
			// 		area_name: '江西省',
			// 		total_area: '1815',
			// 		harvest_pgs: '100.00',
			// 		ppot_of_machn_coll: '98.68',
			// 		area_code: '360000',
			// 		early_rice_hvt_sutn: 'null',
			// 		ppot_of_machn_sowed: '53.59',
			// 		sowing_pgs: '99.89',
			// 		machn_coll_area: '1791',
			// 		machn_sowed_area: '993',
			// 		late_rice_rush_pltg_sutn: 'null',
			// 		planted_area: '1853',
			// 		pgs_cpin: '与去年同期相比基本持平。',
			// 		planned_area: '1855',
			// 		collected_area: '1815',
			// 	},
			// ]
			// await this.$refs.SvgMap.init()
			// this.$refs.SvgMap.loadAreaColorLayer(data_, true, '作业进度(%)')
		},
		//灾害影响
		async xnrEpNKvpj() {
			let params = {
				farmingSeason: this.curSeason,
				dtDay: this.dtDay,
			}
			let res = await xnrEpNKvpj(params)
			if (res.data.length > 0) {
				this.dataList1 = res.data
			} else {
				this.dataList1 = []
			}
		},
    //应对举措
		async dhACIovrQh() {
			let params = {
				farmingSeason: this.curSeason,
				dtDay: this.dtDay,
			}
			let res = await dhACIovrQh(params)
			if (res.data.length > 0) {
				this.dataList3 = res.data
			} else {
				this.dataList3 = []
			}
		},
		async getAlarmByProvince() {
			let params = {
				releaseTime: this.dtDay,
			}
			let res = await getAlarmByProvince(params)
			console.log('res123', res)
			if (res.data.alarm.length > 0) {
				this.dataList2 = res.data.alarm
			} else {
				this.dataList2 = []
			}
		},
		async getAlarmStasticsByProvince() {
			let params = {
				releaseTime: this.dtDay,
			}
			let res = await getAlarmStasticsByProvince(params)
			console.log('res12345', res)
			if (res.data.length > 0) {
				this.tqyjList = res.data
				this.$refs.SvgMap.loadPointLayer(this.tqyjList, true)
				
			} else {
			}
		},
		warningEvent(params) {
			this.areaId = params.data.areaId
			this.isjcyjListShow = true
		},
		contentChange(index) {
			this.bkcgobjTDK()
			this.bkcgobjTDK2()
			this.contentSwitch = index
		},
		eventInfoBtnClick10(i, it) {
			this.yjxqList = it
			this.isyjxqShow = true
		},
		eventInfoBtnClick5(i) {
			console.log(i)
		},
		zhyxxqJumpto(item) {
			this.zhyxxqCenter = item
			this.zhyxxqShow = true
		},
		tqxsxqJumpto(item) {
			this.tqxsxqCenter = item
			this.tqxsxqShow = true
		},
		ydjcxqJumpto(item) {
			this.ydjcxqCenter = item
			this.ydjcxqShow = true
		},
		btnActive(index) {
			this.btnShow = index
		},
	},
}
</script>
<style lang="less" scoped>
.leader_box {
	position: relative;
	background: url(~@/assets/bjnj/zyjdBg.png) no-repeat center / 100% 100%;
	.leader_sjts {
		width: 1920px;
		height: 1080px;
		position: absolute;
		top: 0;
		display: flex;
		//  justify-content: space-between;
		.left {
			width: 450px;
			margin-top: 87px;
			margin-left: 32px;
			position: relative;
			left: 0;
			opacity: 1;
			z-index: 1003;
			-webkit-transition: all 0.5s ease-in;
			-moz-transition: all 0.5s ease-in;
			transition: all 0.5s ease-in;
			.condition {
				width: 450px;
				height: 36px;
				.conditionLeft {
					width: 294px;
					height: 36px;
					float: left;
					/deep/ .el-input {
						width: 294px;
						height: 36px;
					}
				}
				.conditionRight {
					width: 144px;
					height: 36px;
					float: right;
					/deep/ .el-select {
						width: 144px;
						height: 36px;
					}
				}
			}
			.content {
				.contentList {
					width: 450px;
					height: 48px;
					margin-bottom: 20px;
					.contentTitle {
						width: 450px;
						height: 48px;
						background: url(~@/assets/bjnj/zyjdTitleBg.png) no-repeat center / 100% 100%;
						position: relative;
						padding-top: 4px;
						.contentName {
							font-family: YouSheBiaoTiHei;
							font-size: 22px;
							color: #ffffff;
							line-height: 29px;
							text-shadow: 0px 2px 8px rgba(0, 21, 46, 0.8);
							text-align: left;
							font-style: normal;
							margin-left: 30px;
						}
						img {
							position: absolute;
							top: 23px;
							right: 7px;
						}
					}
					.contentData {
						width: 450px;
						height: 570px;
						background: rgba(0, 14, 25, 0.4);
						border: 1px solid rgba(56, 99, 176, 0.75);
						margin-top: 6px;
						.contentSwitch {
							width: 145px;
							height: 26px;
							border-radius: 5px;
							border: 1px solid rgba(19, 89, 194, 1);
							// border-image: linear-gradient(230deg, rgba(99, 173, 235, 1), rgba(19, 89, 194, 1), rgba(19, 89, 194, 1), rgba(99, 173, 235, 0.57)) 1 1;
							margin: 10px 0 0 153px;
							.contentSwitchLeft {
								width: 71px;
								height: 24px;
								line-height: 24px;
								font-family: YouSheBiaoTiHei;
								font-size: 16px;
								color: #b2d6e9;
								text-align: center;
								font-style: normal;
								float: left;
							}
							.contentSwitchRight {
								width: 71px;
								height: 24px;
								line-height: 24px;
								font-family: YouSheBiaoTiHei;
								font-size: 16px;
								color: #b2d6e9;
								text-align: center;
								font-style: normal;
								float: left;
							}
							.contentSelect1 {
								background: linear-gradient(180deg, rgba(36, 132, 212, 0.79) 0%, #114da8 100%);
								box-shadow: inset 0 0 2px 0 #57afff;
								border-radius: 5px 0 0 5px;
								border-right: 1px solid;
								border-image: linear-gradient(
										230deg,
										rgba(99, 173, 235, 1),
										rgba(19, 89, 194, 1),
										rgba(19, 89, 194, 1),
										rgba(99, 173, 235, 0.57)
									)
									1 1;
							}
							.contentSelect2 {
								background: linear-gradient(180deg, rgba(36, 132, 212, 0.79) 0%, #114da8 100%);
								box-shadow: inset 0 0 2px 0 #57afff;
								border-radius: 0 5px 5px 0;
								border-right: 1px solid;
								border-image: linear-gradient(
										230deg,
										rgba(99, 173, 235, 1),
										rgba(19, 89, 194, 1),
										rgba(19, 89, 194, 1),
										rgba(99, 173, 235, 0.57)
									)
									1 1;
							}
						}
						.contentProportion {
							width: 100%;
							height: 26px;
							margin-top: 15px;
							.contentProportion1 {
								float: left;
								width: 68px;
								height: 23px;
								font-family: YouSheBiaoTiHei;
								font-size: 18px;
								color: #ffffff;
								line-height: 23px;
								text-align: center;
								font-style: normal;
								background: linear-gradient(180deg, #b1f7ff 0%, #01b8ec 100%);
								background-clip: text;
								-webkit-background-clip: text;
								-webkit-text-fill-color: transparent;
								margin-left: 10px;
							}
							.contentProportion2 {
								float: left;
								width: 260px;
								height: 23px;
								background: linear-gradient(180deg, rgba(0, 33, 55, 0.8) 0%, rgba(0, 14, 25, 0.9) 100%);
								border-radius: 2px;
								border: 2px solid rgba(56, 99, 176, 0.75);
								margin-left: 8px;
								margin-top: 1px;
								.contentRatio {
									margin: 5px 6px;
									height: 10px;
									background: linear-gradient(90deg, #0054ff 0%, #00ff9d 100%);
									.contentItem {
										width: 2.5px;
										height: 10px;
										margin-left: 13px;
										float: left;
										background: rgba(0, 14, 25, 0.9);
									}
								}
							}
							.contentProportion3 {
								float: left;
								height: 26px;
								font-family: DINAlternate, DINAlternate;
								font-weight: bold;
								font-size: 22px;
								color: #78defb;
								line-height: 26px;
								letter-spacing: 1px;
								text-align: left;
								font-style: normal;
								background: linear-gradient(180deg, #46b9ff 0%, #ade0ff 100%);
								background-clip: text;
								-webkit-background-clip: text;
								-webkit-text-fill-color: transparent;
								margin-left: 13px;
							}
						}
						.contentPiechart {
							width: 326px;
							height: 213px;
							margin: 0 auto;
							margin-top: 9px;
							position: relative;
							.tbmc {
								font-size: 20px;
								color: #fff;
								position: absolute;
								top: 16px;
								left: 50%;
								transform: translateX(-50%);
								font-family: DINOT-Bold;
							}
						}
						.contentClassify {
							width: 100%;
							height: 210px;
							margin-top: 24px;
							.contentClassifyList {
								width: 100%;
								height: 52px;
								margin-bottom: 18px;
								img {
									width: 52px;
									height: 52px;
									float: left;
									margin-left: 23px;
								}
								.contentClassifyArea {
									width: 341px;
									height: 36px;
									line-height: 36px;
									background: linear-gradient(90deg, rgba(0, 123, 255, 0.6) 0%, rgba(0, 197, 255, 0) 100%);
									float: left;
									margin-left: 19px;
									margin-top: 7px;
									.contentClassifyLeft {
										font-family: PingFangSC, PingFang SC;
										font-weight: 500;
										font-size: 16px;
										color: #ffffff;
										text-align: left;
										font-style: normal;
										float: left;
										margin-left: 30px;
									}
									.contentClassifyRight {
										height: 28px;
										line-height: 28px;
										font-family: DINAlternate, DINAlternate;
										font-weight: bold;
										font-size: 24px;
										color: #78defb;
										letter-spacing: 2px;
										text-align: left;
										font-style: normal;
										background: linear-gradient(180deg, #46b9ff 0%, #ade0ff 100%);
										background-clip: text;
										-webkit-background-clip: text;
										-webkit-text-fill-color: transparent;
										float: right;
										margin-right: 42px;
										margin-top: 4px;
									}
								}
							}
						}
						.contentDataTitle {
							width: 426px;
							height: 36px;
							line-height: 36px;
							background: rgba(77, 159, 252, 0.3);
							font-family: SourceHanSansCN, SourceHanSansCN;
							font-weight: 400;
							font-size: 14px;
							color: rgba(255, 255, 255, 0.9);
							text-align: center;
							font-style: normal;
							.contentDataLeft {
								width: 75px;
								text-align: center;
								float: left;
							}
							.contentDataRight {
								width: 351px;
								text-align: center;
								float: left;
							}
						}
						.contentDataBox {
							width: 426px;
							height: 500px;
							overflow-y: auto;
							margin-top: 6px;
							.contentDataList {
								overflow: hidden;
								width: 426px;
								padding: 25px 26px 25px 21px;
								font-family: SourceHanSansCN, SourceHanSansCN;
								font-weight: 400;
								font-size: 14px;
								color: #ffffff;
								text-align: center;
								font-style: normal;
								// position: relative;
								display: flex;
								justify-content: space-between;
								box-sizing: border-box;
								align-items: center;
								.contentDataLeft {
									width: 64px;
									padding-right: 10px;
									text-align: center;
									// float: left;
									text-align: left;
									// position: absolute;
									// left: 21px;
									// top: 50%;
									// transform: translateY(-50%);
								}
								.contentDataRight {
									width: 315px;
									line-height: 20px;
									// float: right;
									text-align: left;
									display: -webkit-box;
									-webkit-box-orient: vertical;
									-webkit-line-clamp: 3; /* 根据业务需求设置第几行显示省略号 */
									overflow: hidden;
								}
								&:hover {
									background: rgba(7, 113, 246, 0.4);
								}
							}
							.contentDataList:nth-child(odd) {
								background: rgba(77, 149, 252, 0.1);
								&:hover {
									background: rgba(7, 113, 246, 0.4);
								}
							}
						}
						.contentDataBox::-webkit-scrollbar {
							display: none;
						}
					}
					.contentData2 {
						padding: 14px 13px;
					}
				}
				.contentList2 {
					height: 624px;
				}
			}
		}
		.right {
			width: 100%;
		}
	}
	.btnBox {
		width: 618px;
		height: 46px;
		position: absolute;
		left: 530px;
		bottom: 111px;
		.btnItem {
			width: 156px;
			height: 46px;
			line-height: 46px;
			float: left;
			margin-right: 50px;
			background: url(~@/assets/bjnj/btn1.png) no-repeat center / 100% 100%;
			span {
				font-family: YouSheBiaoTiHei;
				font-size: 20px;
				color: #ffffff;
				letter-spacing: 2px;
				text-align: center;
				font-style: normal;
				background: linear-gradient(180deg, #ffffff 0%, #009fff 100%);
				background-clip: text;
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
			}
		}
		.btnItem2 {
			background: url(~@/assets/bjnj/btn2.png) no-repeat center / 100% 100%;
			span {
				font-family: YouSheBiaoTiHei;
				font-size: 20px;
				color: #ffffff;
				letter-spacing: 2px;
				text-align: left;
				font-style: normal;
				background: linear-gradient(180deg, #ffffff 0%, #ffca00 100%);
				background-clip: text;
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
			}
		}
	}
}
/deep/ .el-input__inner {
	background: #002450;
	color: #fff;
	border: 1px solid #1359c2;
}
/deep/ .pieWorkOrder {
	width: 100%;
	height: 100%;
}
/deep/ .charts {
	width: 100%;
	height: 100%;
	position: absolute;
	left: 0;
	top: 0;
}
</style>
