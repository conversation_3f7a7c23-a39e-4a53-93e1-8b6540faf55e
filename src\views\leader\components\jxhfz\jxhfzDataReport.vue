<template>
	<div class="yjsj-dialog" v-if="show">
		<div class="ai_waring">
			<div class="title">
				<span>机具进度</span>
				<i class="el-icon-close close_btn" @click="closeEmitai"></i>
			</div>
			<div class="content">
				<div class="basic-info">
					<div class="title-box">
						<div class="tag-box"></div>
						<div class="text-box">农机总动力</div>
					</div>
					<div class="form-body">
						<div class="form-content-box">
							<div class="form-item-box" v-for="(item, index) in totalPowerByMachine" :key="index">
								<div class="item-label-box">
									{{ item.label }}
								</div>
								<el-input
									v-model="item.value"
									min="0"
									type="number"
									placeholder="请输入数量"
									:disabled="isDisabled"
									maxlength="30"
									@change="changeFormData($event, 'njzdl', item)"
								></el-input>
								<div class="unit-text">{{ item.unit || '' }}</div>
							</div>
						</div>
					</div>
				</div>
				<div class="basic-info">
					<div class="title-box">
						<div class="tag-box"></div>
						<div class="text-box">农机拥有量</div>
					</div>
					<div class="form-body">
						<div class="form-content-box">
							<div class="form-item-box" v-for="(item, index) in ownershipByMachinery" :key="index">
								<div class="item-label-box">
									{{ item.label }}
								</div>
								<!--  -->
								<el-input
									v-model="item.value"
									min="0"
									type="number"
									placeholder="请输入数量"
									:disabled="isDisabled"
									maxlength="30"
									@change="changeFormData($event, 'njyyl', item)"
								></el-input>
								<div class="unit-text">{{ item.unit || '' }}</div>
							</div>
						</div>
					</div>
				</div>
				<div class="basic-info">
					<div class="title-box">
						<div class="tag-box"></div>
						<div class="text-box">农机作业面积</div>
					</div>
					<div class="form-body">
						<div class="form-content-box">
							<div class="form-item-box" v-for="(item, index) in njWorkFace" :key="index">
								<div class="item-label-box">
									{{ item.label }}
								</div>
								<el-input
									v-model="item.value"
									min="0"
									type="number"
									placeholder="请输入数量"
									:disabled="isDisabled"
									maxlength="30"
									@change="changeFormData($event, 'nyzymj', item)"
								></el-input>
								<div class="unit-text">{{ item.unit || '' }}</div>
							</div>
						</div>
					</div>
				</div>
				<div class="basic-info">
					<div class="title-box">
						<div class="tag-box"></div>
						<div class="text-box">机械化率</div>
					</div>
					<div class="form-body">
						<div class="form-content-box">
							<div class="form-item-box" v-for="(item, index) in mechanizationRate" :key="index">
								<div class="item-label-box">
									{{ item.label }}
								</div>
								<el-input
									v-model="item.value"
									min="0"
									type="number"
									placeholder="请输入数量"
									:disabled="isDisabled"
									maxlength="30"
									@change="changeFormData($event, 'jxhl', item)"
								></el-input>
								<div class="unit-text">{{ item.unit || '' }}</div>
							</div>
						</div>
					</div>
				</div>
				<div class="basic-info">
					<div class="title-box">
						<div class="tag-box"></div>
						<div class="text-box">农业服务产业</div>
					</div>
					<div class="form-body">
						<div class="form-content-box">
							<div class="form-item-box" v-for="(item, index) in agriculturalServiceIndustry" :key="index">
								<div class="item-label-box">
									{{ item.label }}
								</div>
								<el-input
									v-model="item.value"
									min="0"
									type="number"
									placeholder="请输入数量"
									:disabled="isDisabled"
									maxlength="30"
									@change="changeFormData($event, 'nyfwcy', item)"
								></el-input>

								<div class="unit-text">{{ item.unit || '' }}</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="footer-buttons-box">
				<div class="cancel-button-box" @click="closeEmitai">取消</div>
				<div class="confirm-button-box" @click="handleConfirm">保存</div>
			</div>
		</div>
	</div>
</template>

<script>
import { getCscpBasicHxItemCode, cscpCurrentUserDetails } from '@/api/bjnj/zhdd.js'
import { api_getJxhfzKeyFormData, api_setJxhfzKeyFormData } from '@/api/njzl/hs.api.js'

export default {
	name: 'jxhfzDataReport',
	components: {
		// tiandiShow,
	},
	props: {
		value: {
			type: Boolean,
			default: false,
		},
		year: {
			type: String,
			default: '',
		},
	},
	data() {
		return {
			action: 'edit',
			mapShow: false,
			mapInitAddress: {
				name: '',
				lng: '',
				lat: '',
			},
			actionType: 'edit',
			isDisabled: false,
			operateCenterForm: {},

			levelList: [
				{
					itemValue: '否',
					itemCode: '0',
				},
				{
					itemValue: '是',
					itemCode: '1',
				},
			],
			accountType: '',
			totalPowerByMachine: [],
			ownershipByMachinery: [],
			njWorkFace: [],
			mechanizationRate: [],
			agriculturalServiceIndustry: [],
			usePointNumber: true,
			currentReportId: '',
			currentYear: '',
			currentRoleLevel: '',
			currentAreaId: '100000',
		}
	},
	mounted() {},
	created() {
		this.getCscpBasicHxItemCodeFun()
		this.getCscpBasicHxItemCodeCenter()
	},
	watch: {
		value(val) {
			if (val) {
				this.initFormData()
			}
		},
	},
	computed: {
		token() {
			return localStorage.getItem('token')
		},

		show() {
			return this.value
		},
	},
	methods: {
		closeEmitai() {
			this.totalPowerByMachine = []
			this.ownershipByMachinery = []
			this.njWorkFace = []
			this.mechanizationRate = []
			this.agriculturalServiceIndustry = []
			this.$emit('closeEmitai')
		},
		confirmEdit() {},
		async initFormData() {
			if (this.value) {
				// const res = await api_getJxhfzReportId()
				// this.currentReportId = '1931944576734482433'
				this.currentReportId = ''

				this.currentYear = this.year
				this.isDisabled = false
				this.getFormData()
			}
		},
		changeJobType(event) {},

		handleCancel() {
			// this.actionType = 'check'
			// this.isDisabled = true
			// this.getFormData()

			const route = {
				name: 'machinery-development-list',
				params: {},
				meta: {
					// title: vm.$t(name)
					title: 'machinery-development-list',
				},
			}
			this.$router.push(route)
		},

		changeFormData(ev, type, item) {
			console.log('ev--==', ev)
			switch (type) {
				case 'njzdl':
					this.totalPowerByMachine.forEach((item_) => {
						if (item_.code == item.code) {
							if (Number(ev) < 0) {
								item_.value = 0
							} else {
								if (this.usePointNumber) {
									item_.value = Number(Number(ev).toFixed(6))
								} else {
									item_.value = Number(Number(ev).toFixed(2))
								}
							}
						}
					})
					break
				case 'njyyl':
					this.ownershipByMachinery.forEach((item_) => {
						if (item_.code == item.code) {
							if (Number(ev) < 0) {
								item_.value = 0
							} else {
								if (item.code == '14') {
									if (this.usePointNumber) {
										item_.value = Number(Number(ev).toFixed(6))
									} else {
										item_.value = Number(Number(ev).toFixed(2))
									}
								} else {
									if (this.usePointNumber) {
										item_.value = Number(Number(ev).toFixed(4))
									} else {
										item_.value = Number(Number(ev).toFixed(0))
									}
								}
							}
						}
					})
					break
				case 'nyzymj':
					this.njWorkFace.forEach((item_) => {
						if (item_.code == item.code) {
							if (Number(ev) < 0) {
								item_.value = 0
							} else {
								if (this.usePointNumber) {
									item_.value = Number(Number(ev).toFixed(6))
								} else {
									item_.value = Number(Number(ev).toFixed(2))
								}
							}
						}
					})
					break
				case 'jxhl':
					this.mechanizationRate.forEach((item_) => {
						if (item_.code == item.code) {
							if (Number(ev) < 0) {
								item_.value = 0
							} else {
								if (Number(ev) >= 100) {
									item_.value = 100
								} else {
									item_.value = Number(Number(ev).toFixed(2))
								}
							}
						}
					})
					break
				case 'nyfwcy':
					this.agriculturalServiceIndustry.forEach((item_) => {
						if (item_.code == item.code) {
							if (Number(ev) < 0) {
								item_.value = 0
							} else {
								if (item.code == '32') {
									if (this.usePointNumber) {
										item_.value = Number(Number(ev).toFixed(6))
									} else {
										item_.value = Number(Number(ev).toFixed(6))
									}
								} else {
									if (this.usePointNumber) {
										item_.value = Number(Number(ev).toFixed(4))
									} else {
										item_.value = Number(Number(ev).toFixed(0))
									}
								}
							}
						}
					})
					break
			}
		},

		handleConfirm() {
			console.log('handleConfirmthis.currentRoleLevel--==', this.currentRoleLevel);
			// ...this.mechanizationRate,
			let allData = [
				...this.totalPowerByMachine,
				...this.ownershipByMachinery,
				...this.njWorkFace,
				...this.agriculturalServiceIndustry,
			]

			const allData_ = allData.map((item) => {
				if (item.code == '32') {
					return item
				} else {
					const { value, ...others } = item
					return {
						value: this.currentRoleLevel == 0 || this.currentRoleLevel == 1 ? Number(Number(value) * 10000) : Number(value),
						...others,
					}
				}
			})

			const allData__ = [...allData_, ...this.mechanizationRate]
			console.log('allData__--==', allData__);
			api_setJxhfzKeyFormData(allData__).then((res) => {
				if (res.data) {
					// this.isDisabled = true
					this.$messageNew.message('success', {
						message: '保存成功',
					})
					this.$emit('updateJxhfzData')
				}
				// this.getJxhfzKeyFormData()
			})
		},

		getCscpBasicHxItemCodeFun() {
			getCscpBasicHxItemCode('jobType')
				.then((res) => {
					if (res.data.code === 0) {
					}
				})
				.finally(() => {
					// this.getTableData()
				})
		},
		getCscpBasicHxItemCodeCenter() {
			getCscpBasicHxItemCode('teamLevel').then((res) => {
				if (res.data.code === 0) {
				}
			})
		},

		async getFormData() {
			try {
				let roleName = ''
				const roleRes = await cscpCurrentUserDetails()
				console.log('roleRes--==', roleRes)
				if (roleRes.code === 0) {
					// areaLevel：[0]部 [1]省  [2]市 [3]区县
					this.currentRoleLevel = roleRes.data.areaLevel
					roleName = roleRes.data.roleNames
					this.currentAreaId = roleRes.data.areaId || '100000'
				}

				if (this.currentRoleLevel == 0 || this.currentRoleLevel == 1) {
					this.usePointNumber = true
				} else {
					this.usePointNumber = false
				}

				this.totalPowerByMachine = []
				this.ownershipByMachinery = []
				this.njWorkFace = []
				this.mechanizationRate = []
				this.agriculturalServiceIndustry = []

				const dictRes = await getCscpBasicHxItemCode('mechanize')
				let dictData = []
				dictRes.data.forEach((item) => {
					if (!item.itemCode) {
					} else {
						dictData.push(item)
					}
				})
				const dictData_ = dictData.length > 0 ? dictData.sort((a, b) => Number(a.itemCode) - Number(b.itemCode)) : []
				dictData_.forEach((item) => {
					if (item.itemCode == '01') {
						this.totalPowerByMachine.push({
							label: item.itemValue,
							indexName: item.itemValue,
							code: item.itemCode,
							value: 0,
							unit: this.getUnit(this.currentRoleLevel, roleName, item.itemCode),
							year: this.currentYear,
							// year: 2024,
							reportId: this.currentReportId,
							areaId: this.currentAreaId,
						})
					} else if (Number(item.itemCode) >= 2 && Number(item.itemCode) <= 18) {
						this.ownershipByMachinery.push({
							label: `（${this.numberToChinese(Number(item.itemCode) - 1)}）${item.itemValue}`,
							indexName: item.itemValue,
							code: item.itemCode,
							value: 0,
							unit: this.getUnit(this.currentRoleLevel, roleName, item.itemCode),
							year: this.currentYear,
							// year: 2024,
							reportId: this.currentReportId,
							areaId: this.currentAreaId,
						})
					} else if (item.itemCode == '19') {
						this.njWorkFace.push({
							label: item.itemValue,
							indexName: item.itemValue,
							code: item.itemCode,
							value: 0,
							unit: this.getUnit(this.currentRoleLevel, roleName, item.itemCode),
							year: this.currentYear,
							// year: 2024,
							reportId: this.currentReportId,
							areaId: this.currentAreaId,
						})
					} else if (Number(item.itemCode) >= 20 && Number(item.itemCode) <= 31) {
						this.mechanizationRate.push({
							label: `（${this.numberToChinese(Number(item.itemCode) - 19)}）${item.itemValue}`,
							indexName: item.itemValue,
							code: item.itemCode,
							value: 0,
							unit: this.getUnit(this.currentRoleLevel, roleName, item.itemCode),
							year: this.currentYear,
							// year: 2024,
							reportId: this.currentReportId,
							areaId: this.currentAreaId,
						})
					} else if (Number(item.itemCode) >= 32 && Number(item.itemCode) <= 37) {
						this.agriculturalServiceIndustry.push({
							label: `（${this.numberToChinese(Number(item.itemCode) - 31)}）${item.itemValue}`,
							indexName: item.itemValue,
							code: item.itemCode,
							value: 0,
							unit: this.getUnit(this.currentRoleLevel, roleName, item.itemCode),
							year: this.currentYear,
							// year: 2024,
							reportId: this.currentReportId,
							areaId: this.currentAreaId,
						})
					}
				})

				this.$nextTick(() => {
					this.getJxhfzKeyFormData(this.currentRoleLevel, this.currentAreaId)
				}, 500)
			} catch (error) {
				const data_ = []
				this.usePointNumber = false
			}
		},

		async getJxhfzKeyFormData(roleLevel, areaId) {
			const formDataRes = await api_getJxhfzKeyFormData({ year: this.currentYear, areaId: !areaId ? '' : areaId })
			const data_ = formDataRes.data
			if (data_.length > 0) {
				for (let i = 0; i < data_.length; i++) {
					const item = data_[i]
					// 						totalPowerByMachine
					// ownershipByMachinery
					// njWorkFace
					// mechanizationRate
					// agriculturalServiceIndustry

					this.totalPowerByMachine.forEach((item_) => {
						if (item_.code == item.code) {
							item_.value = Number(
								roleLevel == 0 || roleLevel == 1
									? Number(Number(item.value) / 10000).toFixed(6)
									: Number(Number(item.value).toFixed(2)),
							)
							item_.id = item.id
						}
					})

					this.ownershipByMachinery.forEach((item_) => {
						if (item_.code == item.code) {
							item_.value = Number(
								item.code == '14'
									? roleLevel == 0 || roleLevel == 1
										? Number(Number(item.value) / 10000).toFixed(6)
										: Number(item.value).toFixed(2)
									: roleLevel == 0 || roleLevel == 1
									? Number(Number(item.value) / 10000).toFixed(4)
									: Number(item.value).toFixed(0),
							)

							item_.id = item.id
						}
					})
					this.njWorkFace.forEach((item_) => {
						if (item_.code == item.code) {
							item_.value = Number(
								roleLevel == 0 || roleLevel == 1
									? Number(Number(item.value) / 10000).toFixed(6)
									: Number(Number(item.value).toFixed(2)),
							)
							item_.id = item.id
						}
					})
					this.mechanizationRate.forEach((item_) => {
						if (item_.code == item.code) {
							item_.value = Number(Number(item.value).toFixed(2))
							item_.id = item.id
						}
					})
					this.agriculturalServiceIndustry.forEach((item_) => {
						if (item_.code == item.code) {
							item_.value = Number(
								item.code == '32'
									? Number(item.value).toFixed(6)
									: roleLevel == 0 || roleLevel == 1
									? Number(Number(item.value) / 10000).toFixed(4)
									: Number(item.value).toFixed(0),
							)
							item_.id = item.id
						}
					})
				}
			}
		},

		//将阿拉伯数字转为大写数字
		numberToChinese(num) {
			const chineseNumbers = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
			const chineseUnits = ['', '十', '百', '千', '万']

			if (num === 0) return chineseNumbers[0]
			if (num === 10) return '十' // 特殊处理10

			let result = ''
			let strNum = num.toString()
			let length = strNum.length

			for (let i = 0; i < length; i++) {
				const digit = parseInt(strNum[i])
				const unit = length - i - 1

				if (digit !== 0) {
					// 处理"一十X"的情况，简化为"十X"
					if (num > 10 && num < 20 && i === 0) {
						result += chineseUnits[unit]
					} else {
						result += chineseNumbers[digit] + chineseUnits[unit]
					}
				} else {
					// 处理连续的零，只添加一个零
					if (result[result.length - 1] !== chineseNumbers[0]) {
						result += chineseNumbers[digit]
					}
				}
			}

			// 去除末尾的零
			if (result.endsWith(chineseNumbers[0])) {
				result = result.slice(0, -1)
			}

			return result
		},

		getUnit(roleLevel, roleName, code) {
			if (code == '01') {
				if (roleLevel == 0 || roleLevel == 1) {
					return '万千瓦'
				} else {
					return '千瓦'
				}
			} else if (
				code == '02' ||
				code == '03' ||
				code == '04' ||
				code == '05' ||
				code == '06' ||
				code == '07' ||
				code == '08' ||
				code == '09' ||
				code == '10' ||
				code == '11' ||
				code == '12' ||
				code == '13' ||
				code == '15' ||
				code == '16' ||
				code == '17'
			) {
				if (roleLevel == 0 || roleLevel == 1) {
					return '万台'
				} else {
					return '台'
				}
			} else if (code == '14') {
				if (roleLevel == 0 || roleLevel == 1) {
					return '万平方米'
				} else {
					return '平方米'
				}
			} else if (code == '18') {
				if (roleLevel == 0 || roleLevel == 1) {
					return '万架'
				} else {
					return '架'
				}
			} else if (code == '19') {
				if (roleLevel == 0 || roleLevel == 1) {
					return '万亩'
				} else {
					return '亩'
				}
			} else if (
				code == '20' ||
				code == '21' ||
				code == '22' ||
				code == '23' ||
				code == '24' ||
				code == '25' ||
				code == '26' ||
				code == '27' ||
				code == '28' ||
				code == '29' ||
				code == '30' ||
				code == '31'
			) {
				return '%'
			} else if (code == '32') {
				// if (roleLevel == 0 || roleLevel == 1) {
				// 	return '万元'
				// } else {
				// 	return '元'
				// }
				return '万元'
			} else if (code == '33' || code == '34' || code == '37') {
				if (roleLevel == 0 || roleLevel == 1) {
					return '万个'
				} else {
					return '个'
				}
			} else if (code == '35' || code == '36') {
				if (roleLevel == 0 || roleLevel == 1) {
					return '万人'
				} else {
					return '人'
				}
			} else {
				return ''
			}
		},

		emitAdressMethod(e) {
			this.operateCenterForm.address = e.name
			this.operateCenterForm.longitude = e.lng
			this.operateCenterForm.latitude = e.lat
			this.closeMapPop()
		},
		closeMapPop() {
			this.mapShow = false
		},
	},
}
</script>

<style lang="less">
.elDatePicker.el-picker-panel {
	color: #fff; //设置当前面板的月份的字体为白色，记为1
	background: #002450; //定义整体面板的颜色
	border: 1px solid #1384b4; //定义整体面板的轮廓
	.el-input__inner {
		background: #002450;
		color: #fff;
	}
	.el-picker-panel__icon-btn {
		//设置年份月份调节按钮颜色，记为2
		color: #ffffff;
	}
	.el-date-picker__header-label {
		//设置年月显示颜色，记为3
		color: #ffffff;
	}
	.el-date-table th {
		//设置星期颜色，记为4
		color: #ffffff;
	}
	.el-picker-panel__footer {
		background: #002450;
	}
}
</style>
<style lang="less" scoped>
.zwsjImg {
	width: 250px;
	margin: 20px 0;
}

.alert-empty {
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	font-family: PangMenZhengDao-3, PangMenZhengDao-3;
	font-weight: 500;
	font-size: 20px;
	color: #dbf1ff;
	text-shadow: 0px 0px 10px #bddcf1;
	text-align: center;
	font-style: normal;
	text-transform: none;
}
.bg-header {
	position: absolute;
	width: 100%;
	height: 1080px;
	background: rgba(2, 14, 50, 0.75);
	z-index: 999999;
	top: 0;
	left: 0;
}

/deep/ .ivu-select {
	width: 189px;
}
/deep/ .ivu-select-selection {
	width: 189px;
	height: 34px;
	background: rgba(0, 74, 143, 0.4) linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
	border: 1px solid rgba(0, 162, 255, 0.6);
}
/deep/ .ivu-select-selection .ivu-select-selected-value {
	font-size: 13px;
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	color: #ccf4ff;
	line-height: 34px;
}

/deep/ .ivu-input {
	background: rgba(0, 74, 143, 0.4) linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
	border: 1px solid rgba(0, 162, 255, 0.6);
	font-size: 16px;
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	color: #ccf4ff;
	line-height: 22px;
}
/deep/ .ivu-select-placeholder {
	height: 34px !important;
	font-size: 13px !important;
	line-height: 34px !important;
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	color: #ccf4ff !important;
}
/deep/ .ivu-page-item {
	background: transparent;
	color: #fff;
}
/deep/ .ivu-page-item a {
	color: #fff;
}
/deep/ .ivu-page-prev,
/deep/ .ivu-page-next {
	background: transparent;
}
/deep/ .el-input__inner {
	color: #ccf4ff;
}

/deep/.basic-info .el-form-item__content {
	margin-left: 160px !important;
}

.basic-info {
	width: 100%;
	// background: #fff;
}
.title-box {
	height: 40px;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	margin-bottom: 20px;
	.tag-box {
		width: 6px;
		height: 21px;
		border-radius: 3px;
		background: #159aff;
		margin-right: 10px;
	}
	.text-box {
		line-height: 100%;
		font-weight: bold;
		font-size: 16px;
		color: #fff;
	}
}

.form-body {
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: center;
	width: 100%;
	overflow-y: auto;
	.form-content-box {
		width: 100%;
		padding-left: 50px;
		display: flex;
		justify-content: flex-start;
		align-items: flex-start;
		flex-wrap: wrap;
	}

	.form-item-box {
		width: 48%;
		// height: 60px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		margin-right: 15px;
		margin-bottom: 20px;
		color: #fff;

		.item-label-box {
			min-width: 25%;
			max-width: 25%;
			height: 100%;
			font-size: 14px;
			margin-right: 5px;
			display: flex;
			justify-content: flex-start;
			align-items: center;
		}

		/deep/ .el-input {
			width: 65%;
			&.is-disabled {
				.el-input__inner {
					background-color: rgb(245, 247, 250);
					color: rgb(192, 196, 204);
					border-color: rgb(228, 231, 237);
				}
			}
		}

		.unit-text {
			height: 100%;
			font-size: 14px;
			margin-left: 5px;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			white-space: nowrap;
			color: #fff;
		}
	}
}

.yjsj-dialog {
	width: 1920px;
	height: 1080px;
	position: absolute;
	left: 0;
	top: 0;
	background-color: rgba(0, 0, 0, 0.3);
	z-index: 1104;
	display: flex;
	justify-content: center;
	align-items: center;
}

.ai_waring {
	width: 1367px;
	height: 652px;
	background: #001c40;

	border: 1px solid #023164;
	z-index: 1100;
	.title {
		width: 100%;
		height: 48px;
		line-height: 48px;
		// background: url(~@/assets/map/dialog/title7.png) no-repeat;

		padding: 0 16px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background: #104993;
		span {
			font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
			font-weight: normal;
			font-size: 16px;
			color: #d0deee;
			line-height: 16px;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}
		.close_btn {
			font-size: 14px;
			color: #80caff;
			cursor: pointer;
		}
	}

	.content {
		width: 100%;
		height: calc(100% - 94px);
		overflow: auto;
		padding: 26px 16px 0 16px;

		&::-webkit-scrollbar {
			width: 4px; /* 滚动条宽度 */
		}

		//轨道
		&::-webkit-scrollbar-track {
			background: #04244e; /* 滚动条轨道背景 */
			border-radius: 10px;
			padding: 5px 0;
		}

		//滑块
		&::-webkit-scrollbar-thumb {
			background: #82aed0; /* 滑块背景颜色 */
			border-radius: 10px;
			height: 15px;
		}

		//悬停滑块颜色
		&::-webkit-scrollbar-thumb:hover {
			background: #ff823b; /* 鼠标悬停时滑块颜色 */
		}

		//箭头
		&::-webkit-scrollbar-button {
			display: none; /* 隐藏滚动条的箭头按钮 */
		}
	}

	.footer-buttons-box {
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 10px 0;
		border-top: 1px solid rgba(255, 255, 255, 0.3);

		.cancel-button-box {
			// width: 100px;
			padding: 3px 15px;
			border: 1px solid #159aff;
			border-radius: 5px;
			font-size: 14px;
			color: #ffffff;
			display: block;
			justify-content: center;
			align-items: center;
			cursor: pointer;
			margin: 0 10px;

			&:hover {
				background-color: #159aff;
			}
		}
		.confirm-button-box {
			padding: 3px 15px;
			border: 1px solid #159aff;
			border-radius: 5px;
			font-size: 14px;
			color: #ffffff;
			display: block;
			justify-content: center;
			align-items: center;
			cursor: pointer;
			background-color: #159aff;
		}

		&:last-child {
			margin: 0 !important;
		}
	}

	.check_types {
		text-align: left;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 0 16px 0 !important;
		border-bottom: 1px solid #104993;

		.filter-box {
			text-align: left;
			display: flex;
			justify-content: flex-start;
			align-items: center;
		}

		.jjcd {
			width: 251px;
			height: 32px;
			display: flex;
			align-items: center;
			margin-right: 24px;
			span {
				width: 70px;
				font-size: 14px;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				line-height: 14px;
				color: #ffffff;
			}

			/deep/ .el-input {
				width: 181px;
				height: 32px;
				.el-input__inner {
					width: 181px !important;
					height: 32px !important;
					// background: linear-gradient(180deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%),
					// 	rgba(0, 74, 143, 0.4);
					background-color: #001c40 !important;
					border: 1px solid #d9d9d9 !important;
					border-radius: 4px 4px 4px 4px !important;

					font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
					font-weight: normal;
					font-size: 14px !important;
					color: #6c8097 !important;
					line-height: 14px;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}
			}
		}

		/deep/ .el-select {
			width: 181px !important;
			height: 32px !important;
		}

		.report_time {
			margin-left: 26px;
			display: flex;
			align-items: center;
			span {
				font-size: 16px;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				color: #ffffff;
				line-height: 22px;
			}
		}
		.istime_out {
			margin-left: 26px;
			align-items: center;
			display: flex;
			span {
				font-size: 16px;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				color: #ffffff;
				line-height: 22px;
			}
		}
		.type_btns {
			display: flex;
			align-items: center;

			& div {
				width: 60px;
				height: 32px;
				font-size: 16px;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				color: #ffffff;
				line-height: 32px;
				border-radius: 2px 2px 2px 2px;
				border: 1px solid #d0deee;
				display: flex;
				justify-content: center;
				align-items: center;
				font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
				font-weight: normal;
				font-size: 14px;
				line-height: 14px;
				text-align: center;
				font-style: normal;
				text-transform: none;
				margin: 0 4px;
				cursor: pointer;
			}

			& div:first-child {
				background-color: #001c40;
				color: #d0deee;
			}

			& div:last-child {
				background-color: #159aff;
				color: #d0deee;
			}
		}
	}
}
/deep/ .el-table .cell {
	color: rgba(255, 255, 255, 0.85);
}
/deep/ .el-button--text {
	color: #ff6600;
}
/deep/ .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
	background-color: rgba(255, 255, 255, 0);
}
// /deep/ .ivu-page-total {
//   font-size: 18px;
//   color: #fff;
// }
</style>
