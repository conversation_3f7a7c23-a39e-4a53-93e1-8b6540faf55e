<template>
	<div v-if="value" class="zhddxz-dialog">
		<div class="ai_waring">
			<div class="title">
				<span>指挥调度小组</span>
				<i class="el-icon-close close_btn" @click="closeEmitai"></i>
			</div>
			<div class="content">
				<!-- :appId="appId"
					:appServiceInitAddress="appServiceInitAddress"
					:appServiceBaseUrl="appServiceBaseUrl"
					:userId="userId"
					:title="title"
					:token="token" -->
				<iframe id="iframeId" ref="iframeRef" class="message-group-iframe-box" :src="iframeUrl" frameborder="0"></iframe>
			</div>
		</div>
	</div>
</template>

<script>
import { getOrgTree, api_getMessageGroupToken } from '@/api/bjnj/zhdd.js'
import { debounce } from './tool/index'
import { delPreviewedMeeting } from '@/api/bjnj/preview.js'

export default {
	name: 'jgmlList',
	components: {},
	props: {
		value: {
			type: Boolean,
			default: false,
		},
		userName: {
			type: String,
			default: '',
		},
		areaId: {
			type: String | Number,
			default: '',
		},
		zhjgDataList: {
			type: Array,
			default: () => {
				return []
			},
		},
		groupInfo: {
			type: Object,
			default: () => {
				return { uuid: '', name: '' }
			},
		},
	},
	data() {
		return {
			iframeUrl: 'http://***************:8811/hybrid/zhdd-wscp-im/', //消息测试iframe地址
			appId: '',
			appServiceInitAddress: '',
			appServiceBaseUrl: '',
			userId: '',
			title: '',
			token: '',
		}
	},
	mounted() {
		window.addEventListener('message', this.refreshIdaasToken)
	},
	beforeDestroy() {
		window.removeEventListener('message', this.refreshIdaasToken)
	},
	watch: {
		value(val) {
			if (val) {
				this.getMessageGroupInfo()
			}
		},
	},
	computed: {},
	methods: {
		refreshIdaasToken(event) {
			console.log('event.--==', event)
			// // 安全检查：验证来源
			// if (event.origin !== '') return;
			// 检查是否是期望的消息格式
			if (event.data.action === 'refreshIdaasToken') {
				// 执行父组件方法
				const result = this.$store.state.messageToken

				console.log('tokenResult--==', result)
				// 向 iframe 发送回结果

				// 向 iframe 发送回结果
				const iframe = document.getElementById('iframeId')
				iframe.contentWindow.postMessage(
					{
						action: 'response',
						callId: event.data.callId,
						result: result,
					},
					'*',
				)
			} else if (event.data.action === 'getOrgTree') {
				// 执行父组件方法
				const result = this.zhjgDataList
				// this.getOrgTree().then((res) => {
				// 	result = res.data
				// })
				console.log('treeResult--==', result)

				// 向 iframe 发送回结果
				const iframe = document.getElementById('iframeId')
				iframe.contentWindow.postMessage(
					{
						action: 'response',
						callId: event.data.callId,
						result: result,
					},
					'*',
				)
			}
		},
		async getMessageToken() {},

		async getOrgTree() {
			let treeData = []
			const params = { areaId: this.areaId }
			return getOrgTree(params)
		},
		getMessageGroupInfo() {
			let sessionStorage = window.sessionStorage
			const messageGroupParams = JSON.parse(sessionStorage.getItem('messageGroupParams') || '')

			this.$nextTick(() => {
				this.$refs.iframeRef.onload = () => {
					const iframeWindow = this.$refs.iframeRef.contentWindow
					const { messageToken, ...others } = messageGroupParams
					let config = null
					if (!this.groupInfo.uuid) {
						config = { token: this.$store.state.messageToken, ...others }
					} else {
						config = { token: this.$store.state.messageToken, groupInfo: this.groupInfo, ...others }
					}
					console.log('config--==', config)
					iframeWindow.postMessage(
						{
							type: 'init',
							config: config,
						},
						'*',
					)
				}
			}, 500)
		},

		closeEmitai() {
			this.$emit('closeEmitai')
		},
	},
}
</script>

<style lang="scss" scoped>
.zhddxz-dialog {
	width: 1920px;
	height: 1080px;
	position: absolute;
	left: 0;
	top: 0;
	background-color: rgba(0, 0, 0, 0.3);
	z-index: 1104;
	display: flex;
	justify-content: center;
	align-items: center;
}

.ai_waring {
	width: 1260px;
	height: 708px;
	background: #001c40;

	border: 1px solid #023164;
	z-index: 1100;

	.title {
		width: 100%;
		height: 48px;
		line-height: 48px;
		// background: url(~@/assets/map/dialog/title7.png) no-repeat;

		padding: 0 16px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background: #104993;
		span {
			font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
			font-weight: normal;
			font-size: 16px;
			color: #d0deee;
			line-height: 16px;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}
		.close_btn {
			font-size: 14px;
			color: #80caff;
			cursor: pointer;
		}
	}

	.content {
		height: calc(100% - 48px);
		// padding: 0 16px 24px 16px;

		.message-group-iframe-box {
			width: 100%;
			height: 100%;
		}
	}
}

::v-deep .tree-content {
	.el-tree-node:focus > .el-tree-node__content {
		background-color: #1e539a !important;
	}
}

// 父组件 mounted 中
</style>
