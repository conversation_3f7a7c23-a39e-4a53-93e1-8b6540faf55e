<template>
	<div class="minium-dialog">
		<div class="dialog-expand-content" v-show="isExpanded">
			<video id="minium-video" class="minium-video" muted></video>
			<div class="board" v-if="!curCameraStatus">
				<div class="board-img"></div>
			</div>
		</div>
		<div class="dialog-slide-content" v-show="!isExpanded">
			<div class="dialog-slide-content-inner">
				<!-- <span class="inner-title">正在讲话：</span>
        <span class="inner-value">{{ activeSpeakers }}</span> -->
				<span class="inner-title">通话时长：</span>
				<span class="inner-value">{{ meetingDuration }}</span>
			</div>
		</div>
		<div class="dialog-foot">
			<div :class="['dialog-foot-btn', localMicrophoneStatus ? 'mic-on' : 'mic-off']" @click="changeLocalMicrophone"></div>
			<div :class="['dialog-foot-btn', localCameraStatus ? 'cam-on' : 'cam-off']" @click="changeLocalCamera"></div>
			<div :class="['dialog-foot-btn', 'reset']" @click="resetMultiDialog"></div>
			<!-- <div :class="['dialog-foot-btn', isExpanded ? 'expand' : 'slide']" @click="changeExpandedStatus"></div> -->
		</div>
	</div>
</template>

<script>
export default {
	name: 'miniumVideoDialog',
	props: {
		trackData: {
			type: Object,
		},
		localCameraStatus: {
			type: Boolean,
		},
		localMicrophoneStatus: {
			type: Boolean,
		},
	},
	data() {
		return {
			liveClient: null,
			isExpanded: false,
			curIdentity: null,
			curTrack: null,
			curCameraStatus: false,
			renderCallback: null,
			beginDuration: 0,
			meetingInterval: null,
			meetingDuration: '',
		}
	},
	computed: {
		activeSpeakers() {
			let arr = []
			// console.log('activeSpeakers', this.liveClient?.room.activeSpeakers);
			if (this.liveClient?.room.activeSpeakers && this.liveClient?.room.activeSpeakers.length > 0) {
				this.liveClient?.room.activeSpeakers.forEach((participant) => {
					arr.push(participant.name)
				})
			}
			if (arr.length > 0) {
				return arr.join(',')
			} else {
				return ''
			}
		},
		activeSpeakerIdentity() {
			if (this.liveClient?.room.activeSpeakers && this.liveClient?.room.activeSpeakers.length > 0) {
				return this.liveClient?.room.activeSpeakers[0].identity
			} else {
				return ''
			}
		},
	},
	watch: {
		activeSpeakerIdentity(newIdentity) {
			if (newIdentity && this.curIdentity !== newIdentity) {
				console.log('newIdentity', newIdentity)
				this.curIdentity = newIdentity
			}
		},
		beginDuration(n, o) {
			let minute, second
			if (Math.floor(n / 60) > 0) {
				minute = Math.floor(n / 60) <= 9 ? '0' + Math.floor(n / 60) : Math.floor(n / 60)
			} else {
				minute = '00'
			}
			if (Math.floor(n % 60) > 0) {
				second = Math.floor(n % 60) <= 9 ? '0' + Math.floor(n % 60) : Math.floor(n % 60)
			} else {
				second = '00'
			}
			this.meetingDuration = `${minute}分${second}秒`
		},
		curTrack(newTrack, oldTrack) {
			let videoEle = document.getElementById('minium-video')
			console.log('newTrack', this.newTrack)
			console.log('oldTrack', this.oldTrack)
			if (oldTrack) {
				if (videoEle) {
					oldTrack.detach(videoEle)
				}
			}
			if (newTrack) {
				if (videoEle) {
					newTrack.attach(videoEle)
				}
			}
		},
	},
	mounted() {
		console.log('组件渲染111')
		// this.initLiveClient()
		this.$nextTick(() => {
			// this.initVideoAttach()
		})
		this.beginDuration = this.trackData.duration
		this.meetingInterval = setInterval(() => {
			this.beginDuration++
		}, 1000)
	},
	beforeDestroy() {
		console.log('组件销毁111')
		clearInterval(this.meetingInterval)
		// this.clearEventHandler()
		// this.clearTrack()
	},
	methods: {
		initLiveClient() {
			this.liveClient = window['liveClient']
			this.renderCallback = this.videoCallRenderHandler.bind(this)
			this.liveClient.on('videoCallRender', this.renderCallback)
		},
		videoCallRenderHandler(e) {
			console.log('videoCallRender2233', e)
			if (e.identity === this.curIdentity) {
				this.curCameraStatus = e.isCameraEnabled
				this.curTrack = e.videoTrack
			}
		},
		initVideoAttach() {
			let videoEle = document.getElementById('minium-video')
			if (this.trackData?.track) {
				this.curCameraStatus = this.trackData.cameraStatus
				this.curIdentity = this.trackData.identity
				this.curTrack = this.trackData.track
				if (videoEle) {
					this.curTrack.attach(videoEle)
				}
			}
			videoEle = null
		},
		async changeLocalMicrophone() {
			// await this.liveClient.changeMicrophoneStatus()
			this.$emit('changeLocalMicrophone',!this.localMicrophoneStatus)
		},
		async changeLocalCamera() {
			// await this.liveClient.changeCameraStatus()
			this.$emit('changeLocalCamera',!this.localCameraStatus)
		},
		resetMultiDialog() {
			this.$emit('resetMultiDialog')
		},
		changeExpandedStatus() {
			this.isExpanded = !this.isExpanded
		},
		clearTrack() {
			let videoEle = document.getElementById('minium-video')
			if (this.curTrack) {
				if (videoEle) {
					this.curTrack.detach(videoEle)
					videoEle.src = ''
					videoEle.srcObject = null
				}
			}
			videoEle = null
		},
		clearEventHandler() {
			this.liveClient.off('videoCallRender', this.renderCallback)
		},
	},
}
</script>

<style lang="scss" scoped>
.minium-dialog {
	width: 320px;
	height: auto;
	min-height: 40px;
	position: absolute;
	top: 40px;
	right: 40px;
	z-index: 6000;
	cursor: grab;

	.dialog-foot {
		width: 100%;
		height: 40px;
		position: absolute;
		bottom: 0;
		left: 0;
		display: flex;
		align-items: center;
		padding: 0 24px;
		justify-content: space-between;
		background: rgba(41, 44, 48, 0.7);
		&-btn {
			cursor: pointer;
			width: 20px;
			height: 20px;
			transition: transform 0.2s ease;
		}
		.mic-on {
			background: url('~@/assets/service/mic-on2.png') no-repeat center / 100% 100%;
		}
		.mic-off {
			background: url('~@/assets/service/mic-off2.png') no-repeat center / 100% 100%;
		}
		.cam-on {
			background: url('~@/assets/service/cam-on2.png') no-repeat center / 100% 100%;
		}
		.cam-off {
			background: url('~@/assets/service/cam-off2.png') no-repeat center / 100% 100%;
		}
		.reset {
			background: url('~@/assets/service/reset_icon.png') no-repeat center / 100% 100%;
		}
		.expand {
			background: url('~@/assets/service/expand_icon.png') no-repeat center / 100% 100%;
		}
		.slide {
			background: url('~@/assets/service/expand_icon.png') no-repeat center / 100% 100%;
			transform: rotate(180deg);
		}
	}

	.dialog-expand-content {
		position: relative;
		width: 100%;
		height: 180px;
		border-radius: 6px;
		.minium-video {
			width: 100%;
			height: 100%;
			object-fit: contain;
			border-radius: 6px;
		}
		.board {
			width: 100%;
			height: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			position: absolute;
			left: 0;
			top: 0;
			background: #f1f6fc;
			border-radius: 6px;
			// border-radius: 8px;
			// border: 1px solid #316abe;
			&-img {
				width: 22%;
				height: auto;
				border-radius: 50%;
				aspect-ratio: 1/1;
				background: url('~@/assets/service/bohao.png') no-repeat center / 100% 100%;
			}
		}
	}
	.dialog-slide-content {
		width: 100%;
		height: 80px;
		padding-bottom: 40px;
		background: rgba(41, 44, 48, 0.9);
		&-inner {
			width: 100%;
			height: 100%;
			padding: 0 10px;
			display: flex;
			align-items: center;
			flex-wrap: nowrap;
			font-family: Source Han Sans CN;
			font-weight: 400;
			font-size: 16px;
			color: #ffffff;
			.inner-title {
				flex-shrink: 0;
				white-space: nowrap;
			}
			.inner-value {
				width: fit-content;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}
	}
}
</style>
