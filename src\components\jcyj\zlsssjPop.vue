<template>
  <div class="ai_waring" v-if="show">
    <div class="title">
      <span>实时告警</span>
    </div>
    <div class="close_btn" @click="closeEmitai"></div>
    <div class="content">
      <!-- <div class="check_types">
        <div class="jjcd">
          <span>告警来源：</span>
          <el-input v-model="gjcs.csName" placeholder="请输入告警场所" clearable></el-input>
        </div>
        <div class="jjcd">
          <span>告警状态：</span>
          <Select v-model="proityValue" clearable>
            <Option v-for="item in eventTypeOptions" :value="item.value" :key="item.itemId">
              {{
              item.label
              }}
            </Option>
          </Select>
        </div>
        <div class="type_btns">
          <div @click="searchBtn">查询</div>
          <div @click="resetBtn">重置</div>
          <div @click="generateAlert(1)">生成烟感预警</div>
          <div @click="generateAlert(2)">生成燃气预警</div>
        </div>
      </div>-->
      <div class="table_box">
        <SwiperTableCall
          :titles="['序号', '告警内容', '告警类型', '具体位置','告警时间','告警状态','操作' ]"
          :widths="['6%', '12%', '13%','23%','18%','10%','18%']"
          :data="sjtjData"
          :contentHeight="'510px'"
          :isNeedOperate="true"
          :operateBtn="['转工单']"
          :dataAll="dataAll"
          v-on="$listeners"
        ></SwiperTableCall>
        <!-- , '忽略', '短信任务', '转工单' -->
      </div>
    </div>
    <!-- <SocialSmallPlaceDetail v-model="socialSmallPlaceDetailShow" /> -->
    <Page class="page_box" :total="total" @on-change="pageNumChange" show-total></Page>
  </div>
</template>

<script>
import SwiperTableCall from '@/components/jcyj/SwiperTableCall.vue'
import { queryByPageSsyj } from '@/api/hs/hs.tsgz.js'

export default {
  name: 'zlsssjPop',
  components: {
    SwiperTableCall
    // SocialSmallPlaceDetail
  },
  props: {
    // value: {
    //   type: Boolean,
    //   default: false
    // },
    title: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      gjcs: {
        csName: ''
      },
      socialSmallPlaceDetailShow: false,
      inputValue: '',
      tabActive: 0,
      tabs: ['应急队伍', '机具保有量', '应急车辆', '医疗机构', '避难场所'],
      btnsArr: [
        {
          name: '未办结',
          normalBg: require('@/assets/shzl/map/left_n.png'),
          activeBg: require('@/assets/shzl/map/left_a.png')
        },
        {
          name: '已办结',
          normalBg: require('@/assets/shzl/map/right_n.png'),
          activeBg: require('@/assets/shzl/map/right_a.png')
        }
      ],
      urgentOptions: [
        {
          value: '0',
          label: '普通'
        },
        {
          value: '1',
          label: '突发'
        },
        {
          value: '2',
          label: '重要'
        },
        {
          value: '3',
          label: '紧急'
        }
      ],
      gjcsOptions: [
        {
          value: '0',
          label: '小餐饮类'
        },
        {
          value: '1',
          label: '小商品类'
        }
      ],
      eventTypeOptions: [
        {
          value: '0',
          label: '已处置'
        },
        {
          value: '1',
          label: '未处置'
        }
      ],
      eventTypeOptions1: [],
      sjtjData: [
        [
          '1',
          '烟感YG-001',
          '餐饮类',
          '出现大量烟雾',
          '汉中门大街68号',
          '2023-04-20 12:23:12',
          '淳化社区001网格',
          '望江',
          '13812345678',
          '待处理'
        ],
        [
          '2',
          '燃气RQ-002',
          '网吧类',
          '出现大量烟雾',
          '汉中门大街68号',
          '2023-04-20 12:23:12',
          '淳化社区001网格',
          '望江',
          '13812345678',
          '待处理'
        ]
      ],
      proityOptions: [
        {
          value: '0',
          label: '一般'
        },
        {
          value: '1',
          label: '紧急'
        }
        //  {
        //     value: '2',
        //     label: '紧急',
        //   },
      ],
      timeOutOptions: [
        {
          value: '0',
          label: '是'
        },
        {
          value: '1',
          label: '否'
        }
      ],
      gjcsValue: '',
      proityValue: '',
      timeOutValue: '',
      settled: false,
      pageNum: 1,
      pageSize: 10,
      total: 0,
      dataAll: []
    }
  },
  computed: {
    show () {
      // return this.value
      return true
    }
  },
  mounted () {
    // this.jxcsGjxx()
    this.queryByPageSsyj()
    // this.getCscpBasicHxItemCode()
  },
  methods: {
    generateAlert (val) {
      this.searchBtn()
      generateAlarm({ type: val })
    },
    closeEmitai () {
      this.$emit('closeEmitai', false)
    },
    dealMethod () {
      console.log(1111)
      this.$parent.dealMethod()
    },
    dwMethod () {
      this.$parent.dwMethod()
    },
    changeTab (i) {
      this.tabActive = i
    },
    handleOperate (i, it) {
      console.log(i, it)
      this.socialSmallPlaceDetailShow = true
    },
    // async jxcsGjxx () {
    //   this.sjtjData = []
    //   let res = await jxcsGjxx({ descr: this.gjcs.csName, page: this.pageNum, size: this.pageSize })
    //   if (res.code == '200') {
    //     this.sjtjData = res.result.data.map((item, index) => [
    //       this.pageSize * this.pageNum + index + 1,
    //       item.facilitiesModel || '—',
    //       item.descr,
    //       item.valStr,
    //       item.address || '—',
    //       item.alarmTime,
    //       item.tgridInfo && item.tgridInfo.gridName ? item.tgridInfo.gridName : '—',
    //       item.tgridInfo && item.tgridInfo.gridMan ? item.tgridInfo.gridMan : '—',
    //       item.tgridInfo && item.tgridInfo.phone ? item.tgridInfo.phone : '',
    //       '—',
    //       item.id
    //     ])
    //     this.total = res.result.recordsTotal
    //   }
    // },
    pageNumChange (val) {
      this.pageNum = val
      this.queryByPageSsyj()
    },
    searchBtn () {
      this.pageNum = 0
      this.queryByPageSsyj()
    },
    resetBtn () {
      this.gjcs = {}
      this.pageNum = 0
      this.proityValue = ''
      this.queryByPageSsyj()
    },
    async getCscpBasicHxItemCode () {
      const res = await getCscpBasicHxItemStatus()
      this.eventTypeOptions = res.data.map(item => {
        return {
          value: item.itemCode,
          itemId: item.itemId,
          itemSort: item.itemSort,
          label: item.itemValue,
        }
      })
    },
    async queryByPageSsyj () {
      this.sjtjData = []
      const res = await queryByPageSsyj({ page: this.pageNum, size: this.pageSize })
      console.log(res, 9)
      this.sjtjData = res.data.map((it, index) => {
        return [
          this.pageSize * (this.pageNum - 1) + index + 1,
          it.content,
          it.alarmType,
          it.incidentLocation,
          it.createTime,
          it.alarmStatus === '0' ? '未处理' : it.status === '1' ? '已转任务' : it.status === '2' ? '已转工单' : '已忽略',
        ]
      })
      this.dataAll = res.data
      console.log('this.sjtjData', this.sjtjData)
      this.total = res.recordsTotal
    },
  }
}
</script>

<style lang="less" scoped>
.bg-header {
  position: absolute;
  width: 100%;
  height: 1080px;
  background: rgba(2, 14, 50, 0.75);
  z-index: 999999;
  top: 0;
  left: 0;
}
/deep/ .ivu-select {
  width: 168px;
}
/deep/ .ivu-select-selection {
  width: 168px;
  height: 34px;
  background: rgba(0, 74, 143, 0.4)
    linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
}
/deep/ .ivu-select-selection .ivu-select-selected-value {
  font-size: 13px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 34px;
}

/deep/ .ivu-input {
  background: rgba(0, 74, 143, 0.4)
    linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
  font-size: 16px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 22px;
}
/deep/ .ivu-select-placeholder {
  font-size: 13px !important;
  line-height: 34px !important;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #ccf4ff !important;
}
/deep/ .ivu-page-item {
  background: transparent;
  color: #fff;
}
/deep/ .ivu-page-item a {
  color: #fff;
}
/deep/ .ivu-page-prev,
/deep/ .ivu-page-next {
  background: transparent;
}
.ai_waring {
  width: 1435px;
  height: 759px;
  background: rgba(9, 19, 34, 0.95);
  box-shadow: 0px 0px 22px 0px rgba(11, 54, 138, 0.35), 0px 2px 8px 0px rgba(0, 0, 0, 0.7),
    inset 0px 0px 8px 0px rgba(17, 146, 214, 0.35);
  border-radius: 11px;
  border: 1px solid #015c8c;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1003;
  .title {
    margin: 0 auto 23px;
    width: 635px;
    height: 76px;
    line-height: 76px;
    background: url(~@/assets/map/dialog/title2.png) no-repeat;
    display: grid;
    place-items: center;
    span {
      font-size: 36px;
      font-family: PangMenZhengDao;
      line-height: 36px;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .tabs {
    display: flex;
    gap: 13px;
    margin: 28px 0 42px 28px;
    .tab {
      width: 191px;
      height: 41px;
      line-height: 41px;
      background: url('~@/assets/map/dialog/bg5.png') no-repeat center / 100% 100%;
      cursor: pointer;
      span {
        font-size: 24px;
        font-family: PangMenZhengDao;
        color: #ffffff;
      }
      &.active {
        background: url('~@/assets/map/dialog/bg4.png') no-repeat center / 100% 100%;
        span {
          font-size: 30px;
          font-family: PangMenZhengDao;
          background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
  }
  .close_btn {
    position: absolute;
    width: 23px;
    height: 23px;
    top: 25px;
    right: 40px;
    background: url(~@/assets/map/dialog/btn3.png) no-repeat;
    cursor: pointer;
  }
  .content {
    padding: 0 30px;
    .btns {
      // border: 1px solid red;
      margin: 0 auto;
      display: flex;
      justify-content: center;
      & > div {
        width: 154px;
        height: 53px;
        span {
          font-size: 24px;
          font-family: YouSheBiaoTiHei;
          color: #ffffff;
          line-height: 53px;
          background: linear-gradient(180deg, #ffffff 0%, #ffeba0 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
    .check_types {
      text-align: left;
      display: flex;
      .jjcd {
        display: flex;
        align-items: center;
        margin-right: 24px;
        span {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #ffffff;
        }
      }
      .report_time {
        margin-left: 26px;
        display: flex;
        align-items: center;
        span {
          font-size: 16px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
      }
      .istime_out {
        margin-left: 26px;
        align-items: center;
        display: flex;
        span {
          font-size: 16px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
      }
      .type_btns {
        margin-left: 27px;
        display: flex;
        align-items: center;
        & div {
          font-size: 16px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 43px;
          background: url(~@/assets/shzl/map/type_btn.png) no-repeat center / 100% 100%;
          background-size: 100% 100%;
          text-align: center;
          cursor: pointer;
          &:first-of-type {
            width: 98px;
            height: 43px;
          }
          &:nth-of-type(2) {
            margin-left: 20px;
            width: 98px;
            height: 43px;
          }
          &:nth-of-type(3) {
            margin-left: 20px;
            width: 125px;
            height: 43px;
          }
          &:nth-of-type(4) {
            margin-left: 20px;
            width: 125px;
            height: 43px;
          }
        }
      }
    }
    .table_box {
      margin-top: 31px;
      overflow: hidden;
    }
    .fy_page {
      margin-top: 20px;
    }
  }
}
/deep/ .el-input {
  width: 200px;
  height: 34px;
  .el-input__inner {
    height: 34px;
    background: rgba(0, 74, 143, 0.4)
      linear-gradient(
        360deg,
        rgba(181, 223, 248, 0) 0%,
        rgba(29, 172, 255, 0.29) 100%,
        #ffffff 100%
      );
    border: 1px solid rgba(0, 162, 255, 0.6);
    color: #fff;
    padding: 0 30px 0 15px;
  }
}
</style>
