<template>
  <div class="leader_box">
    <div class="leader_sjts" v-if="zhddType === 0">
      <div class="left">
        <div class="left1">
          <BlockBox
            title="值班体系"
            subtitle="Duty system"
            class="box"
            :isListBtns="false"
            :showMore="true"
            :blockHeight="268"
            @handleShowMore="showMoreFn1(0)"
          >
            <DutySystem />
          </BlockBox>
          <BlockBox
            title="未办结工单"
            subtitle="Work order not completed"
            class="box box1"
            :isListBtns="false"
            :showMore="true"
            :blockHeight="229"
            @handleShowMore="showMoreFn1(1)"
          >
            <!-- <WorkOrderNotCompl /> -->
            <WorkOrderNotCompl :chartData1="notCompl1" :chartData2="notCompl2" />
          </BlockBox>
          <BlockBox
            title="实时事件"
            subtitle="Real-time event"
            class="box"
            :isListBtns="false"
            :showMore="true"
            :blockHeight="405"
            @handleShowMore="showMoreFn1(5)"
          >
            <!-- <RealTimeEvent
              contentHeight="360px"
              :data="realTimeEventList"
              @clickRealtimeEvent="clickRealtimeEvent"
            /> -->
            <RealTimeEventQgd
              contentHeight="358px"
              :data="realTimeEventList"
              @clickRealtimeEvent="clickRealtimeEventQgd"
            />
          </BlockBox>
        </div>
      </div>
      <div class="right">
        <div class="right1">
          <BlockBox
            title="周边资源"
            subtitle="Surrounding Resources"
            class="box"
            :isListBtns="false"
            :showMore="true"
            :blockHeight="256"
            @handleShowMore="showMoreFn1(2)"
          >
            <EmergencyResources />
          </BlockBox>
          <BlockBox
            title="AI智能事件分析"
            subtitle="AI intelligent event analysis"
            class="box"
            :showMore="true"
            :isListBtns="false"
            :blockHeight="308"
            @handleShowMore="showMoreFn1(3)"
          >
            <!-- <GridMemberLoginStatus /> -->
            <AIEventAnalysis />
          </BlockBox>
          <BlockBox
            title="视频监控"
            subtitle="Video surveillance"
            class="box box6"
            :showMore="true"
            :isListBtns="false"
            :blockHeight="296"
            @handleShowMore="showMoreFn1(4)"
          >
            <div class="cont">
              <VideoSurveillance :cameraId="cameraId1" v-if="cameraId1" class="video_" />
              <VideoSurveillance :cameraId="cameraId2" v-if="cameraId2" class="video_" />
              <VideoSurveillance :cameraId="cameraId3" v-if="cameraId3" class="video_" />
              <VideoSurveillance :cameraId="cameraId4" v-if="cameraId4" class="video_" />
            </div>
          </BlockBox>
        </div>
      </div>
    </div>
    <!-- <GeneralDisposal
      v-if="zhddType === 1"
      @close="closeGeneralDisposal"
      :infos="eventDetail"
      :imgList="eventImgs"
      :processList="processList"
      v-model="showVideo"
    /> -->
    <!-- 周边资源左边区工单 -->
    <GeneralDisposalQgd
      v-if="zhddType === 1"
      @close="closeGeneralDisposal"
      :infos="eventDetail"
      :imgList="eventImgs"
      :processList="processList"
      v-model="showVideo"
    />
    <SurroundingResources
      v-if="zhddType === 1 && !showVideo"
      :lists="surroundingResourcesList"
      :tabs="surroundingResourceTabsProp"
      :currentAddress="currentAddress"
      @changeDistance="changeDistance"
      @closeEmit="showVideo = true"
      @voiceCall="voiceCall"
      @videoCall="videoCall"
      @checkTab="checkTab"
      @changeSlider="changeSlider"
    />
    <div class="map_box">
      <LeafletMap ref="leafletMap" @poiClick="showDialog" />
    </div>
    <!-- <leaderMiddle /> -->
    <LeaderFooter :btns="[]" :activaIdx="activaIdx" />
    <CountryPart :isShow="isShowCountryPart" @close="closeCountryPart" @check="checkTree" />
    <wgyPop v-if="isWgllShow" @closeEmit="isWgllShow = false" />
    <dzzPop v-if="isdzzShow" @closeEmit="isdzzShow = false" />
    <xzqyPop v-if="isxzqyShow" @closeEmit="isxzqyShow = false" />
    <zwzxPop v-if="iszwzxShow" @closeEmit="iszwzxShow = false" />
    <zhzxPop v-if="iszhzxShow" @closeEmit="iszhzxShow = false" />
    <csglgdPop v-if="iscsglgdShow" @closeEmit="iscsglgdShow = false" />
    <shaqPop v-if="isshaqShow" @closeEmit="isshaqShow = false" />
    <sthbPop v-if="issthbShow" @closeEmit="issthbShow = false" />
    <whlyPop v-if="iswhlyShow" @closeEmit="iswhlyShow = false" />
    <yjfkPop v-if="isyjfkShow" @closeEmit="isyjfkShow = false" />
    <ggjtPop v-if="isggjtShow" @closeEmit="isggjtShow = false" />
    <zdqyPop v-if="iszdqyShow" @closeEmit="iszdqyShow = false" />
    <xxPop v-if="isxxShow" @closeEmit="isxxShow = false" />
    <wbPop v-if="iswbShow" @closeEmit="iswbShow = false" />
    <ylcsPop v-if="isylcsShow" @closeEmit="isylcsShow = false" />
    <hczPop v-if="ishczShow" @closeEmit="ishczShow = false" />
    <dyyPop v-if="isdyyShow" @closeEmit="isdyyShow = false" />
    <djylPop v-if="djylShow" @closeEmit="djylShow = false" />
    <bjxqPop v-if="bjxqShow" @closeEmit="bjxqShow = false" />
    <Area ref="area" v-show="showArea" :pop-area-info="popAreaInfo"></Area>
    <!-- 滚动 -->
    <div class="warning_tips">
      <SwiperTableWaring :data="warnMessage" @markerMessage="markerMessage" />
    </div>
    <!-- 侧边菜单 -->
    <ZhddAside :list="asideList" @marker="handleMenuClick" />
    <!-- 侧边二级菜单 -->
    <SurroundingResourcesMap
      v-model="showYjzyList"
      left="740px"
      bottom="518px"
      :list="asideLeveltwoMenu"
      @surEmit="chooseSource"
    />
    <!-- 侧边工具栏 -->
    <Tools :btns="tools" @handleClick="clickTool" />
    <!-- 弹窗 ------------------------------------------------------------------------------------------------->
    <!-- 事件信息 -->
    <EventInfo v-model="eventDialogShow" :list="eventInfo" @handle="eventInfoBtnClick" />
    <!-- 基础信息 -->
    <BasicInformation
      v-model="basicInfomationShow"
      :title="basicInfomationTitle"
      :list="basicInfomationList"
      :btnShow="basicInfomationBtnShow"
      :btns="basicInfomationBtns"
      @handle="clickBasicInformationBtn"
    />
    <!-- 事件详情 -->
    <!-- <EventDetail
      v-model="eventDetailShow"
      :detail="eventDetailObj"
      :imgList="eventImgs"
      :processList="processList"
      :is-done="isDone"
      @handle="eventHandle"
    /> -->
    <EventDetailQgd
      v-model="eventDetailShow"
      :detail="eventDetailObj"
      :imgList="eventImgs"
      :processList="processList"
      :is-done="isDone"
      @handle="eventHandle"
    />
    <!-- 事件统计 -->
    <!-- <EventStatistic v-model="eventStatisticShow" @operate="eventStatisticOperate" /> -->
    <EventStatisticQgd v-model="eventStatisticShow" @operate="eventStatisticOperateQgd" />
    <!-- 实时事件 -->
    <!-- <RealTimeEventDialog
      v-model="realTimeEventDialogShow"
      :list="realTimeEventList"
      @operate="realTimeEventOperate"
    /> -->
    <RealTimeEventDialogQgd v-model="realTimeEventDialogShow" @operate="realTimeEventOperateQgd" />
    <!-- 应急资源 -->
    <EmergencyResourcesDialog v-model="emergencyResourcesDialogShow" />
    <!-- 值班人员 -->
    <OfficeronDuty v-model="officeronDutyShow" @voice="officeDutyVoice" @video="officeDutyVideo" />
    <!-- 网格员登录情况 -->
    <GridMemberLoginStatusDialog v-model="gridMemberLoginStatusDialogShow" />
    <!-- 任务派发 -->
    <TaskDistribution v-model="taskDistributionShow" />
    <!-- AI智能事件分析 -->
    <AIEventAnalysisDialog v-model="AIEventAnalysisDialogShow" />
    <!-- 告警详情 -->
    <AlarmDetails v-model="alarmDetailsShow" />
    <!-- 单个视频监控弹窗 -->
    <VideoSingle v-model="videoSingleShow" :video-url="videoSingleUrl" :cameraCode="cameraCode" />
    <!-- 无人机 -->
    <Drone
      v-model="droneShow"
      @markDrone="markDrone"
      @pushDroneCount="getDroneCount"
      @openWrj="openWrj"
    />
    <!-- 无人机监控 -->
    <DroneVideo v-model="droneVideoShow" :video-url="videoUrl" />
    <!-- 视频会商 -->
    <sphsPop
      v-if="isXlgcShow1"
      @closeEmit="isXlgcShow1 = false"
      :leftTreeData="sphsData"
      :initCheckedPeo="initCheckedPeo"
    >
      <template v-slot:title1>视频会商</template>
      <template v-slot:title2>会议接入</template>
      <template v-slot:title3>重置</template>
      <template v-slot:title4>加入会议</template>
    </sphsPop>
    <!-- 视频监控 -->
    <spjkPop v-if="spjkShow" @closeEmit="spjkShow = false" @operate="handleSsgjOperate"></spjkPop>
    <!-- 通讯调度 -->
    <txddPop
      v-if="txddShow"
      @closeEmit="txddShow = false"
      :leftTreeData="sphsData"
      :initCheckedPeo="initCheckedPeo"
    ></txddPop>
    <!-- 消息推送 -->
    <WebSocket @pushMessage="getMessage" />
  </div>
</template>

<script>
import testData from '@/assets/json/cyjjPoint'
import BlockBox from '@/components/leader/common/blockBox.vue'
import HeaderMain from '@/components/leader/common/headerMains'
import effectRankBarSwiper from './components/effectRankBar-swiper.vue'
import particle from './components/particle.vue'
// import EventDetail from './components/EventDetail.vue'
import CountryPart from './components/CountryPart.vue'
import leaderMiddle from '@/components/leader/common/leaderMiddle.vue'
import gdMap from '@/components/leader/gdMap/gdMap.vue'
import cesiumMap from '@/components/leader/cesiumMap/Cesium3dMap.vue'
import cssjPoint from '@/assets/json/csts/cssjPoint.json'
import spjkPoint from '@/assets/json/csts/spjkPoint.json'
import csbjPoint from '@/assets/json/csts/csbjPoint.json'
import ryllPoint from '@/assets/json/csts/ryllPoint.json'
import xzqhPoint from '@/assets/json/csts/xzqhPoint.json'
import dzzPoint from '@/assets/json/csts/dzzPoint.json'
import zwzxPoint from '@/assets/json/csts/zwzxPoint.json'
import zzzxPoint from '@/assets/json/csts/zzzxPoint.json'
import zdqyPoint from '@/assets/json/csts/zdqyPoint.json'
import xxPoint from '@/assets/json/csts/xxPoint.json'
import wbPoint from '@/assets/json/csts/wbPoint.json'
import ylcsPoint from '@/assets/json/csts/ylcsPoint.json'
import hczPoint from '@/assets/json/csts/hczPoint.json'
import dyyPoint from '@/assets/json/csts/dyyPoint.json'
import LeaderFooter from '@/components/leader/common/leaderFooter.vue'
import myRobot from '@/components/robot/myRobot.vue'
import NumScroll from '@/components/leader/num_scrolls'
import Area from '@/components/leader/leader_csts/area.vue'
import LeafletMap from '@/components/map/LeafletMap2.vue'
import SwiperTableWaring from '@/components/shzl/SwiperTableWaring.vue'
import WebSocket from '@/components/WebSocket/WebSocket.vue'
import { LandUse, FullFactorGridEvents, StreetSurvey } from './components/hszl'
import {
  getCameraMakers,
  getCameraXq,
  getQgdList,
  getQgdSssj,
  getQgdWbjTj,
  getQgdDetailJk,
  getQgdCodejjcd,
  getQgdCodesfgk,
  getQgdCodesqlx,
  getQgdCodesjly,
} from '@/api/hs/hs.api.js'
// 周边资源标签
import { surroundingResourceTabs } from '@/views/leader/components/zhdd/index.d.js'

// 各模块
import {
  // 值班体系
  DutySystem,
  // 未办结工单
  WorkOrderNotCompl,
  // 实时事件
  RealTimeEvent,
  //实时事件区工单
  RealTimeEventQgd,
  // 应急资源
  EmergencyResources,
  // 网格员登录情况
  GridMemberLoginStatus,
  // 视频监控
  VideoSurveillance,
  // 底部菜单
  ZhddFooter,
  // 侧边菜单
  ZhddAside,
  // 事件 - 一般处置
  GeneralDisposal,
  // 事件 - 区一般处置
  GeneralDisposalQgd,
  // 事件 - 一般处置 - 周边资源
  SurroundingResources,
  // AI智能事件分析
  AIEventAnalysis,
} from './components/zhdd'

// 弹窗
import {
  // 事件信息
  EventInfo,
  // 事件详情
  EventDetail,
  // 区事件详情
  EventDetailQgd,
  // 事件统计
  EventStatistic,
  // 区级事件统计
  EventStatisticQgd,
  // 应急资源
  EmergencyResourcesDialog,
  // 值班人员
  OfficeronDuty,
  // 网格员登录情况
  GridMemberLoginStatusDialog,
  // 任务派发
  TaskDistribution,
  // AI智能事件分析
  AIEventAnalysisDialog,
  // 无人机
  Drone,
  // 无人机监控
  DroneVideo,
  // 实时事件
  RealTimeEventDialog,
  // 区实时事件
  RealTimeEventDialogQgd,
  // 基本信息
  BasicInformation,
  // 告警详情
  AlarmDetails,
  // 单个视频监控
  VideoSingle,
} from './components/zhdd/dialog'

import { SurroundingResourcesMap, Tools } from './components/map'

// api
import {
  getEventInfo,
  getEventDetail,
  getSurroundingResourcesPersonList,
  getPersonnelTrack,
} from '@/api/hs/hs.api.js'

import {
  enterpriseSinglePoint,
  enterpriseEmergencyTeamPoint,
  gasStationPoint,
  enterprisePoint,
  protectiveTargetPoint,
  externalDefibrillatorPoint,
  medicalInstitutionPoint,
  substationPoint,
  fireFightingUnitPoint,
  videoSurveillancePoint,
  publicecurityTeamPoint,
  schoolPoint,
  gridOperatorPoint,
} from './components/aqjg/mock/point.js'

import 'swiper/css/swiper.css'
export default {
  name: 'Zhdd',
  components: {
    BlockBox,
    HeaderMain,
    effectRankBarSwiper,
    particle,
    leaderMiddle,
    gdMap,
    cesiumMap,
    LeaderFooter,
    CountryPart,
    myRobot,
    Area,
    NumScroll,
    LandUse,
    FullFactorGridEvents,
    StreetSurvey,
    DutySystem,
    WorkOrderNotCompl,
    RealTimeEvent,
    RealTimeEventQgd,
    EmergencyResources,
    GridMemberLoginStatus,
    VideoSurveillance,
    LeafletMap,
    ZhddFooter,
    EventInfo,
    EventDetail,
    EventDetailQgd,
    EventStatistic,
    EventStatisticQgd,
    EmergencyResourcesDialog,
    OfficeronDuty,
    Drone,
    GridMemberLoginStatusDialog,
    SwiperTableWaring,
    ZhddAside,
    GeneralDisposal,
    GeneralDisposalQgd,
    SurroundingResources,
    Tools,
    AIEventAnalysis,
    SurroundingResourcesMap,
    TaskDistribution,
    AIEventAnalysisDialog,
    WebSocket,
    DroneVideo,
    RealTimeEventDialog,
    RealTimeEventDialogQgd,
    BasicInformation,
    AlarmDetails,
    VideoSingle,
  },
  data() {
    return {
      videoSingleUrl: '',
      cameraCode: '',
      videoUrl: '',
      // 控制事件详情弹窗中下方按钮的显示
      isDone: true,
      asideLeveltwoMenu: [
        {
          iconNormal: require('@/assets/foot/icon10.png'),
          iconActive: require('@/assets/foot/icon21.png'),
          label: '企业应急队伍',
          layerId: 'enterpriseEmergencyTeam',
          count: 2,
          checked: false,
        },
        {
          iconNormal: require('@/assets/foot/icon34.png'),
          iconActive: require('@/assets/foot/icon35.png'),
          label: '网格员',
          layerId: 'gridOperator',
          count: 2,
          checked: false,
        },
        {
          iconNormal: require('@/assets/foot/icon11.png'),
          iconActive: require('@/assets/foot/icon22.png'),
          label: '加油站',
          layerId: 'gasStation',
          count: 2,
          checked: false,
        },
        {
          iconNormal: require('@/assets/foot/icon12.png'),
          iconActive: require('@/assets/foot/icon23.png'),
          label: '企业',
          layerId: 'enterprise',
          count: 2,
          checked: false,
        },
        {
          iconNormal: require('@/assets/foot/icon13.png'),
          iconActive: require('@/assets/foot/icon24.png'),
          label: '防护目标',
          layerId: 'protectiveTarget',
          count: 2,
          checked: false,
        },
        {
          iconNormal: require('@/assets/foot/icon14.png'),
          iconActive: require('@/assets/foot/icon25.png'),
          label: '体外除颤仪',
          layerId: 'externalDefibrillator',
          count: 2,
          checked: false,
        },
        {
          iconNormal: require('@/assets/foot/icon15.png'),
          iconActive: require('@/assets/foot/icon26.png'),
          label: '医疗机构',
          layerId: 'medicalInstitution',
          count: 2,
          checked: false,
        },
        {
          iconNormal: require('@/assets/foot/icon16.png'),
          iconActive: require('@/assets/foot/icon27.png'),
          label: '变电站',
          layerId: 'substation',
          count: 2,
          checked: false,
        },
        {
          iconNormal: require('@/assets/foot/icon17.png'),
          iconActive: require('@/assets/foot/icon28.png'),
          label: '二级消防单位',
          layerId: 'fireFightingUnit',
          count: 2,
          checked: false,
        },
        {
          iconNormal: require('@/assets/foot/icon18.png'),
          iconActive: require('@/assets/foot/icon29.png'),
          label: '视频监控',
          layerId: 'videoSurveillance',
          count: 2,
          checked: false,
        },
        {
          iconNormal: require('@/assets/foot/icon19.png'),
          iconActive: require('@/assets/foot/icon30.png'),
          label: '公安队伍',
          layerId: 'publicecurityTeam',
          count: 2,
          checked: false,
        },
        {
          iconNormal: require('@/assets/foot/icon20.png'),
          iconActive: require('@/assets/foot/icon31.png'),
          label: '学校',
          layerId: 'school',
          count: 2,
          checked: false,
        },
        {
          iconNormal: require('@/assets/foot/icon32.png'),
          iconActive: require('@/assets/foot/icon33.png'),
          label: '无人机',
          layerId: 'drone',
          count: 1,
          checked: false,
        },
      ],
      tools: [
        {
          icon: require('@/assets/map/icon2.png'),
          disabled: false,
        },
        {
          icon: require('@/assets/map/icon1.png'),
          disabled: false,
        },
        {
          icon: require('@/assets/map/icon3.png'),
          disabled: true,
        },
      ],
      warnMessage: [],
      asideList: [
        {
          bg: require('@/assets/aside/bg1.png'),
          bgActive: require('@/assets/aside/bg2.png'),
          icon: require('@/assets/aside/icon1.png'),
          iconActive: require('@/assets/aside/icon6.png'),
          label: '事件信息',
          active: true,
        },
        {
          bg: require('@/assets/aside/bg1.png'),
          bgActive: require('@/assets/aside/bg2.png'),
          icon: require('@/assets/aside/icon2.png'),
          iconActive: require('@/assets/aside/icon7.png'),
          label: '周边资源',
          active: false,
        },
        {
          bg: require('@/assets/aside/bg1.png'),
          bgActive: require('@/assets/aside/bg2.png'),
          icon: require('@/assets/aside/icon3.png'),
          iconActive: require('@/assets/aside/icon8.png'),
          label: '视频会商',
          active: false,
        },
        {
          bg: require('@/assets/aside/bg1.png'),
          bgActive: require('@/assets/aside/bg2.png'),
          icon: require('@/assets/aside/icon4.png'),
          iconActive: require('@/assets/aside/icon9.png'),
          label: '任务下发',
          active: false,
        },
        {
          bg: require('@/assets/aside/bg1.png'),
          bgActive: require('@/assets/aside/bg2.png'),
          icon: require('@/assets/aside/icon5.png'),
          iconActive: require('@/assets/aside/icon10.png'),
          label: '视频监控',
          active: false,
        },
      ],
      showYjzyList: false,
      zhddType: 0,
      eventDetail: [
        {
          label: '姓名',
          value: '重大交通事故',
        },
        {
          label: '事件类型',
          value: '城市管理-突发事件',
        },
        {
          label: '事件地址',
          value: '中国江苏省南京市湖熟街道',
        },
        {
          label: '上报时间',
          value: '2022-05-05 08:38:22',
        },
        {
          label: '事件类型',
          value: '城市管理',
        },
        {
          label: '事件来源',
          value: '12345',
        },
        {
          label: '事件编号',
          value: 'DH6101002205050065902',
        },
        {
          label: '事件状态',
          value: '待区指挥中心签收',
        },
        {
          label: '事件时间',
          value: '2022-05-05 08:38:22',
        },
        {
          label: '公开信息',
          value: '是',
        },
        {
          label: '紧急程度',
          value: '紧急',
        },
        {
          label: '归属地',
          value: '是',
        },
        {
          label: '事件内容',
          value: '',
        },
        {
          label: '附件',
          value: '',
        },
      ],
      processList: [],
      eventDetailObj: {},
      eventImgs: [],
      man_active: require('@/assets/hszl/man_active.png'),
      man_normal: require('@/assets/hszl/man_normal.png'),
      woman_normal: require('@/assets/hszl/woman_normal.png'),
      woman_active: require('@/assets/hszl/woman_active.png'),
      currentAddress: '',
      // 实时事件列表
      realTimeEventList: [],
      // 控制事件信息显示
      eventDialogShow: false,
      // 控制基本信息弹窗显示
      basicInfomationShow: false,
      // 基础信息标题
      basicInfomationTitle: '',
      // 基础信息内容
      basicInfomationList: [],
      // 控制基础信息按钮显示
      basicInfomationBtnShow: false,
      // 基础信息按钮
      basicInfomationBtns: [],
      // 事件信息
      eventInfo: {
        title: '',
        type: '',
        time: '',
        address: '',
        describe: '',
      },
      // 控制事件详情显示
      eventDetailShow: false,
      // 控制事件统计显示
      eventStatisticShow: false,
      // 控制实时事件显示
      realTimeEventDialogShow: false,
      // 控制应急资源显示
      emergencyResourcesDialogShow: false,
      // 控制值班人员显示
      officeronDutyShow: false,
      // 控制网格员登录情况显示
      gridMemberLoginStatusDialogShow: false,
      // 控制任务派发显示
      taskDistributionShow: false,
      // 控制AI智能事件分析弹窗显示
      AIEventAnalysisDialogShow: false,
      // 告警详情弹窗显示
      alarmDetailsShow: false,
      // 单个视频监控弹窗显示
      videoSingleShow: false,
      // 控制一般处置界面右侧视频监控的展示 地图圈选时会隐藏，显示周边资源
      showVideo: true,
      // 控制视频会商显示
      isXlgcShow1: false,
      // 控制视频监控显示
      spjkShow: false,
      // 控制通讯调度显示
      txddShow: false,
      // 控制无人机显示
      droneShow: false,
      // 控制无人机监控显示
      droneVideoShow: false,
      // 周边资源 - 人员信息列表
      surroundingResourcesList: [],
      // 周边资源 - 标签列表
      surroundingResourceTabsProp: surroundingResourceTabs,
      sphsData: [
        { id: '4000', label: '河南社区网格员' },
        { id: '4001', label: '湖熟社区网格员' },
        { id: '4002', label: '和进社区网格员' },
        { id: '4003', label: '龙都社区网格员' },
        { id: '4004', label: '周岗社区网格员' },
        { id: '4005', label: '金桥社区网格员' },
      ],
      initCheckedPeo: [],
      messageArr: [],
      eventActive: false,
      jgzzShow: false,
      zdcsShow: false,
      djylShow: false,
      bjxqShow: false,
      showArea: false,
      popAreaInfo: {},
      areaPointList: [],
      jgzzActive: 0,
      zdcsActive: 0,
      showCesiumMap: false,

      data2: [
        {
          tit: '参保人数',
          img: require('@/assets/csts/icon7.png'),
          cont: [
            {
              num: 49883,
              unit: '人',
            },
          ],
        },
        {
          tit: '参保缴费补贴',
          img: require('@/assets/csts/icon8.png'),
          cont: [
            {
              num: 13467,
              unit: '人',
            },
            {
              num: 2346.45,
              unit: '万',
            },
          ],
        },
        {
          tit: '养老金发放',
          img: require('@/assets/csts/icon9.png'),
          cont: [
            {
              num: 13467,
              unit: '人',
            },
            {
              num: 1277.75,
              unit: '万',
            },
          ],
        },
      ],
      activaIdx: -1,
      isShowEventDetail: false,
      isShowCountryPart: false,
      isXlgcShow: false,
      isWgllShow: false,
      isdzzShow: false,
      isxzqyShow: false,
      iszwzxShow: false,
      iszhzxShow: false,
      iscsglgdShow: false,
      isshaqShow: false,
      issthbShow: false,
      iswhlyShow: false,
      isyjfkShow: false,
      isggjtShow: false,
      iszdqyShow: false,
      isxxShow: false,
      iswbShow: false,
      isylcsShow: false,
      ishczShow: false,
      isdyyShow: false,
      cameraId1: null,
      cameraId2: null,
      cameraId3: null,
      cameraId4: null,
      notCompl1: {
        value: 23,
      },
      notCompl2: [
        ['product', '占比'],
        ['紧急', 71],
        ['一般', 29],
      ],
    }
  },
  created() {
    this.watchFlag()
  },
  mounted() {
    this.cameraMarker()
    // this.initRealtimeEventList()
    // this.getEventInfoFn()

    this.getQgdList() // 工单全量
    this.getQgdWbjTj() // 未办结工单
  },
  watch: {
    '$route.params'(newval, oldval) {
      this.watchFlag()
    },
    zhddType(newval) {
      // 0 - 默认页面 | 1 - 一般处置
      if (newval === 0) {
        this.tools = [
          {
            icon: require('@/assets/map/icon2.png'),
            disabled: false,
          },
          {
            icon: require('@/assets/map/icon1.png'),
            disabled: false,
          },
          {
            icon: require('@/assets/map/icon3.png'),
            disabled: true,
          },
        ]
        this.asideList = [
          {
            bg: require('@/assets/aside/bg1.png'),
            bgActive: require('@/assets/aside/bg2.png'),
            icon: require('@/assets/aside/icon1.png'),
            iconActive: require('@/assets/aside/icon6.png'),
            label: '事件信息',
            active: true,
          },
          {
            bg: require('@/assets/aside/bg1.png'),
            bgActive: require('@/assets/aside/bg2.png'),
            icon: require('@/assets/aside/icon2.png'),
            iconActive: require('@/assets/aside/icon7.png'),
            label: '周边资源',
            active: false,
          },
          {
            bg: require('@/assets/aside/bg1.png'),
            bgActive: require('@/assets/aside/bg2.png'),
            icon: require('@/assets/aside/icon3.png'),
            iconActive: require('@/assets/aside/icon8.png'),
            label: '视频会商',
            active: false,
          },
          {
            bg: require('@/assets/aside/bg1.png'),
            bgActive: require('@/assets/aside/bg2.png'),
            icon: require('@/assets/aside/icon4.png'),
            iconActive: require('@/assets/aside/icon9.png'),
            label: '任务下发',
            active: false,
          },
          {
            bg: require('@/assets/aside/bg1.png'),
            bgActive: require('@/assets/aside/bg2.png'),
            icon: require('@/assets/aside/icon5.png'),
            iconActive: require('@/assets/aside/icon10.png'),
            label: '视频监控',
            active: false,
          },
        ]
        this.$refs.leafletMap.removeCircle()
      } else {
        this.tools = [
          {
            icon: require('@/assets/map/icon2.png'),
            disabled: true,
          },
          {
            icon: require('@/assets/map/icon1.png'),
            disabled: true,
          },
          {
            icon: require('@/assets/map/icon3.png'),
            disabled: true,
          },
        ]
        this.asideList = [
          {
            bg: require('@/assets/aside/bg1.png'),
            bgActive: require('@/assets/aside/bg2.png'),
            icon: require('@/assets/aside/icon11.png'),
            iconActive: require('@/assets/aside/icon12.png'),
            label: '通讯调度',
            active: false,
          },
          {
            bg: require('@/assets/aside/bg1.png'),
            bgActive: require('@/assets/aside/bg2.png'),
            icon: require('@/assets/aside/icon2.png'),
            iconActive: require('@/assets/aside/icon7.png'),
            label: '周边资源',
            active: false,
          },
          {
            bg: require('@/assets/aside/bg1.png'),
            bgActive: require('@/assets/aside/bg2.png'),
            icon: require('@/assets/aside/icon3.png'),
            iconActive: require('@/assets/aside/icon8.png'),
            label: '视频会商',
            active: false,
          },
          {
            bg: require('@/assets/aside/bg1.png'),
            bgActive: require('@/assets/aside/bg2.png'),
            icon: require('@/assets/aside/icon4.png'),
            iconActive: require('@/assets/aside/icon9.png'),
            label: '任务下发',
            active: false,
          },
          {
            bg: require('@/assets/aside/bg1.png'),
            bgActive: require('@/assets/aside/bg2.png'),
            icon: require('@/assets/aside/icon5.png'),
            iconActive: require('@/assets/aside/icon10.png'),
            label: '视频监控',
            active: false,
          },
        ]
      }
    },
    taskDistributionShow(newval) {
      this.asideList[3].active = newval
    },
    isXlgcShow1(newval) {
      this.asideList[2].active = newval
      if (!newval) {
        this.sphsData = [
          { id: '4000', label: '河南社区网格员' },
          { id: '4001', label: '湖熟社区网格员' },
          { id: '4002', label: '和进社区网格员' },
          { id: '4003', label: '龙都社区网格员' },
          { id: '4004', label: '周岗社区网格员' },
          { id: '4005', label: '金桥社区网格员' },
        ]
        this.initCheckedPeo = []
      }
    },
    spjkShow(newval) {
      this.asideList[4].active = newval
    },
    txddShow(newval) {
      if (this.zhddType === 1) {
        this.asideList[0].active = newval
      }
      if (!newval) {
        this.sphsData = [
          { id: '4000', label: '河南社区网格员' },
          { id: '4001', label: '湖熟社区网格员' },
          { id: '4002', label: '和进社区网格员' },
          { id: '4003', label: '龙都社区网格员' },
          { id: '4004', label: '周岗社区网格员' },
          { id: '4005', label: '金桥社区网格员' },
        ]
        this.initCheckedPeo = []
      }
    },
    eventStatisticShow(newVal) {
      if (!newVal) {
        this.isDone = true
      }
    },
  },
  methods: {
    handleSsgjOperate(item, i) {
      console.log(item)
      if (i === 0) {
        this.alarmDetailsShow = true
      } else if (i === 1) {
        this.taskDistributionShow = true
      }
    },
    // 地图打点
    handleMenuClick(active, i) {
      if (i === 0) {
        this.eventActive = active
        if (this.zhddType === 0) {
          if (this.eventActive) {
            // this.getEventInfoFn() // 原事件处置
            this.getQgdList()
          } else {
            // 关闭弹窗
            this.eventDialogShow = false
            // 清除标记点
            this.removeAll()
          }
        } else {
          this.txddShow = this.eventActive
        }
      } else if (i === 1) {
        this.showYjzyList = active
      } else if (i === 2) {
        this.isXlgcShow1 = true
      } else if (i === 3) {
        this.taskDistributionShow = active
      } else if (i === 4) {
        this.spjkShow = active
      }
    },
    marker(data, layerId) {
      this.$refs.leafletMap.drawPoiMarker(data, layerId, true)
    },
    // 点击地图标记点显示点位基础信息弹窗
    showDialog(layerId, it) {
      console.log(layerId, it)
      const id = it.props.id

      if (layerId === 'eventInformation' || layerId === 'message') {
        this.currentCheckedMarker = it.latlng
      } else if (layerId === 'personnelInformation' || layerId === 'gridOperator') {
        // 如果选择的是网格员或者人员，将其经纬度存入currentCheckedPersonMarker变量，用于绘制轨迹
        this.currentCheckedPersonMarker = it.latlng
      }

      // 弹窗标题
      this.basicInfomationTitle = surroundingResourceTabs.find((it) => it.layerId === layerId)?.name
      // 默认弹窗内不展示按钮（没有按钮的弹窗多，需要按钮的单独设置）
      this.basicInfomationBtnShow = false

      if (layerId === 'eventInformation') {
        this.eventId = id
        // this.getEventDetailFn(id)
        this.getQgdListDetail(it.info)
      } else if (layerId === 'message') {
        this.eventId = id
        // this.getEventDetailFn(id)
        this.getQgdListDetail(it.info)
      } else if (layerId === 'personnelInformation') {
        this.basicInfomationList = [
          {
            label: '姓名：',
            value: '和进网格员1',
          },
          {
            label: '所属部门：',
            value: '和进社区-第四网格',
          },
          {
            label: '人员类型：',
            value: '网格员',
          },
          {
            label: '联系方式：',
            value: '18951675336',
          },
          {
            label: '所在地址：',
            value: '湖熟街道',
          },
        ]
        this.basicInfomationBtns = ['语音通话', '视频通话', '人员轨迹']
        this.basicInfomationBtnShow = true
        this.basicInfomationShow = true
      } else if (layerId === 'materialInformation') {
        this.basicInfomationList = [
          {
            label: '物资名称：',
            value: '照明灯',
          },
          {
            label: '所属区域：',
            value: '江宁区',
          },
          {
            label: '地址：',
            value: '湖熟街道尚桥社区',
          },
        ]
        this.basicInfomationShow = true
      } else if (layerId === 'vehicleInformation') {
        this.basicInfomationList = [
          {
            label: '车辆类型：',
            value: '灭火消防车',
          },
          {
            label: '所属单位：',
            value: '南京市消防局',
          },
          {
            label: '联系方式：',
            value: '16788885366',
          },
          {
            label: '地址：',
            value: '湖熟街道金桥社区',
          },
        ]
        this.basicInfomationBtns = ['车辆轨迹']
        this.basicInfomationBtnShow = true
        this.basicInfomationShow = true
      } else if (layerId === 'medicalInstitution') {
        this.basicInfomationList = [
          {
            label: '机构名称：',
            value: '南京市儿童医院',
          },
          {
            label: '所属区域：',
            value: '江宁区',
          },
          {
            label: '地址：',
            value: '湖熟街道尚桥社区',
          },
        ]
        this.basicInfomationShow = true
      } else if (layerId === 'shelter') {
        this.basicInfomationList = [
          {
            label: '场所名称：',
            value: '国防区中心应急避难场所',
          },
          {
            label: '联系人：',
            value: '王文',
          },
          {
            label: '联系方式：',
            value: '16788885366',
          },
          {
            label: '地址：',
            value: '湖熟街道新农社区',
          },
        ]
        this.basicInfomationShow = true
      } else if (layerId === 'surveillanceCamera') {
        this.spjkShow = true
      } else if (layerId === 'drone') {
        this.videoUrl = 'http://10.2.32.45:8088/firstfloor/stream1/hls.m3u8'
        this.droneVideoShow = true
      } else if (layerId === 'enterpriseEmergencyTeam') {
        this.basicInfomationTitle = '企业应急队伍'
        this.basicInfomationList = [
          {
            label: '队伍名：',
            value: '江宁滨江专职队',
          },
          {
            label: '队伍地址：',
            value: '江宁区滨江开发区春阳路7号',
          },
          {
            label: '所属单位：',
            value: '湖熟街道',
          },
          {
            label: '值班电话：',
            value: '18205086401',
          },
          {
            label: '人数：',
            value: '26',
          },
          {
            label: '联系人：',
            value: '涂文康',
          },
          {
            label: '联系电话：',
            value: '15950493959',
          },
          {
            label: '专业类别：',
            value: '防火',
          },
          {
            label: '单位性质：',
            value: '企业',
          },
        ]
        this.basicInfomationShow = true
      } else if (layerId === 'gasStation') {
        this.basicInfomationTitle = '加油站'
        this.basicInfomationList = [
          {
            label: '加油站名称：',
            value: '中国石化销售有限公司江苏南京江宁景明大街加油站',
          },
          {
            label: '地址：',
            value: '南京市江宁区滨江开发区景明大街和锦文路',
          },
          {
            label: '类型：',
            value: '加油站',
          },
          {
            label: '值班电话：',
            value: '18205086401',
          },
          {
            label: '联系人：',
            value: '张文明',
          },
          {
            label: '联系人电话：',
            value: '15950493959',
          },
          {
            label: '辖区：',
            value: '江宁区',
          },
          {
            label: '管理部门：',
            value: '市应急管理局',
          },
        ]
        this.basicInfomationShow = true
      } else if (layerId === 'protectiveTarget') {
        this.basicInfomationTitle = '防护目标'
        this.basicInfomationList = [
          {
            label: '名称：',
            value: '南京市江宁区江宁中心小学',
          },
          {
            label: '地址：',
            value: '江宁区江宁街道如练路99号',
          },
          {
            label: '安全管理人：',
            value: '郑松龙',
          },
          {
            label: '电话：',
            value: '18205086401',
          },
          {
            label: '安全责任人：',
            value: '汤明',
          },
          {
            label: '电话：',
            value: '13913862942',
          },
        ]
        this.basicInfomationShow = true
      } else if (layerId === 'externalDefibrillator') {
        this.basicInfomationTitle = '体外除颤仪'
        this.basicInfomationList = [
          {
            label: '所属类别：',
            value: '器材工具类',
          },
          {
            label: '装备名称：',
            value: 'AED',
          },
          {
            label: '装备型号：',
            value: '无',
          },
          {
            label: '数量：',
            value: '1',
          },
          {
            label: '所在单位：',
            value: '滨江开发区管委会',
          },
          {
            label: '所在地址：',
            value: '天成路18号',
          },
          {
            label: '存放地点：',
            value: '门卫室',
          },
          {
            label: '责任部门：',
            value: '门卫',
          },
          {
            label: '责任人：',
            value: '门卫',
          },
          {
            label: '联系电话：',
            value: '13806756753',
          },
        ]
        this.basicInfomationShow = true
      } else if (layerId == 'medicalInstitution') {
        this.basicInfomationTitle = '医疗机构'
        this.basicInfomationList = [
          {
            label: '名称：',
            value: '南京市江宁医院',
          },
          {
            label: '地址：',
            value: '宝象路58号',
          },
          {
            label: '级别：',
            value: '三甲',
          },
          {
            label: '性质：',
            value: '公有制',
          },
        ]
        this.basicInfomationShow = true
      } else if (layerId === 'substation') {
        this.basicInfomationTitle = '变电站'
        this.basicInfomationList = [
          {
            label: '名称：',
            value: '亭村变',
          },
          {
            label: '地址：',
            value: '弘利路',
          },
          {
            label: '运维单位：',
            value: '无',
          },
          {
            label: '值班电话：',
            value: '无',
          },
          {
            label: '负责人：',
            value: '无',
          },
          {
            label: '设备参数：',
            value: '110kV',
          },
        ]

        this.basicInfomationShow = true
      } else if (layerId === 'fireFightingUnit') {
        this.basicInfomationTitle = '二级消防单位'
        this.basicInfomationList = [
          {
            label: '名称：',
            value: '林德梅山（南京）气体有限公司',
          },
          {
            label: '地址：',
            value: '南京市江宁区江宁街道工业园区8号',
          },
          {
            label: '工地电话：',
            value: '025-86102356',
          },
          {
            label: '站长',
            value: '耿建东',
          },
          {
            label: '站长电话：',
            value: '17366213501',
          },
          {
            label: '副站长：',
            value: '无',
          },
        ]
        this.basicInfomationShow = true
      } else if (layerId === 'videoSurveillance') {
        // this.spjkShow = true
        this.cameraXq1(id)
      } else if (layerId === 'publicecurityTeam') {
        this.basicInfomationTitle = '公安队伍'
        this.basicInfomationList = [
          {
            label: '名称：',
            value: '滨江派出所',
          },
          {
            label: '地址：',
            value: '南京市江宁开发区盛安大道700号',
          },
          {
            label: '值班电话：',
            value: '84950110',
          },
        ]
        this.basicInfomationShow = true
      } else if (layerId === 'school') {
        this.basicInfomationTitle = '学校'
        this.basicInfomationList = [
          {
            label: '名称：',
            value: '南京传媒学院（滨江校区）',
          },
          {
            label: '地址：',
            value: '南京市江宁区滨江开发区翔凤路168号',
          },
          {
            label: '联系人：',
            value: '刘浩',
          },
          {
            label: '联系电话：',
            value: '18112925097',
          },
        ]
        this.basicInfomationShow = true
      } else if (layerId === 'enterprise') {
        this.basicInfomationTitle = '企业'
        this.basicInfomationList = [
          {
            label: '企业名称：',
            value: '南京宇创石化设备有限公司',
          },
          {
            label: '企业规模：',
            value: '规上企业（小型）',
          },
          {
            label: '风险源数量：',
            value: '9',
          },
          {
            label: '统一社会信用代码：',
            value: '91320115780687607L',
          },
          {
            label: '法人代表：',
            value: '吴先怡',
          },
          {
            label: '主营范围：',
            value: '非标设备、钢结构设计、制造、销售等',
          },
          {
            label: '注册地址：',
            value: '南京市江宁滨江经济开发区丽水大街',
          },
        ]
        this.basicInfomationShow = true
      } else if (layerId === 'gridOperator') {
        this.basicInfomationTitle = '网格员'
        this.basicInfomationList = [
          {
            label: '姓名：',
            value: '和进网格员1',
          },
          {
            label: '所属部门：',
            value: '和进社区-第四网格',
          },
          {
            label: '人员类型：',
            value: '网格员',
          },
          {
            label: '联系方式：',
            value: '18951675336',
          },
          {
            label: '所在地址：',
            value: '湖熟街道',
          },
        ]
        this.basicInfomationBtns = ['语音通话', '视频通话', '人员轨迹']
        this.basicInfomationBtnShow = true
        this.basicInfomationShow = true
      }
    },
    async initRealtimeEventList() {
      const res = await getEventInfo()
      if (res?.code === 200) {
        this.eventMarkers = res.result.map((it) => ({
          latlng: [it.latitude, it.longitude],
          icon: {
            iconUrl: require('@/assets/map/point/point1.png'),
            iconSize: [32, 42],
            iconAnchor: [16, 42],
          },
          props: {
            id: it.id,
            type: 'eventInformation',
          },
        }))
        this.realTimeEventList = res.result.map((it) => ({
          ...it,
        }))
      }
    },
    // 新推送预警事件后，再重新获取一次实时事件和未办结工单，以保证事件的实时性
    async reloadEvent() {
      const res = await getEventInfo()
      if (res?.code === 200) {
        this.realTimeEventList = res.result.map((it) => ({
          ...it,
        }))
      }
    },
    getDroneCount(i) {
      this.asideLeveltwoMenu[11].count = i
    },
    eventHandle(i) {
      if (i === 0) {
        this.eventDetailShow = false
        this.eventDialogShow = false
        // 关闭未办结工单弹窗
        this.eventStatisticShow = false
        // 关闭实时事件弹窗
        this.realTimeEventDialogShow = false
        this.zhddType = 1
      } else if (i === 1) {
        this.eventDetailShow = false
        this.eventStatisticShow = false
        this.realTimeEventDialogShow = false
        this.$refs.leafletMap.flyToPoint(this.currentCheckedMarker, 15)
      }
    },
    voiceCall(data, type, item) {
      if (type === 1) {
        this.sphsData = data.map((it) => ({
          id: it.code,
          label: it.userName,
        }))
        this.initCheckedPeo = data.map((it) => it.code)
      } else if (type === 0) {
        this.sphsData = [{ id: item.code, label: item.userName }]
        this.initCheckedPeo = [item.code]
      }
      this.txddShow = true
    },
    videoCall(data, type, item) {
      if (type === 1) {
        this.sphsData = data.map((it) => ({
          id: it.code,
          label: it.userName,
        }))
        this.initCheckedPeo = data.map((it) => it.code)
      } else if (type === 0) {
        this.sphsData = [{ id: item.code, label: item.userName }]
        this.initCheckedPeo = [item.code]
        console.log(this.sphsData)
      }
      this.isXlgcShow1 = true
    },
    officeDutyVoice(data) {
      console.log(data)
      this.sphsData = [
        { id: '4001', label: '王建国' },
        { id: '4002', label: '李进' },
      ]
      if (data[1] === '王建国') {
        this.initCheckedPeo = ['4001']
      } else {
        this.initCheckedPeo = ['4002']
      }

      this.$nextTick(() => {
        this.txddShow = true
      })
    },
    officeDutyVideo(data) {
      console.log(data)
      this.sphsData = [
        { id: '4001', label: '王建国' },
        { id: '4002', label: '李进' },
      ]
      if (data[1] === '王建国') {
        this.initCheckedPeo = ['4001']
      } else {
        this.initCheckedPeo = ['4002']
      }

      this.$nextTick(() => {
        this.isXlgcShow1 = true
      })
    },
    // 未办结工单下钻按钮操作
    async eventStatisticOperate(i, id, it, tabIdx) {
      console.log(i, id, tabIdx)
      if (i === 0) {
        const res = await getEventDetail(id)
        const {
          title, // 标题
          type, // 事件来源
          addressDetail, // 地址
          orderContent, // 事件内容
          reporterName, // 姓名
          eventCategoryName, // 事件类型
          reportTime, // 事件时间 | 发生时间
          insertTime, // 上报时间
          orderNo, // 事件编号
          state, // 事件状态
          istemporary, // 公开信息：0 是
          priority, // 紧急程度
          areaNo, // 归属地
          appendixList, // 附件
          phone, // 手机号
          tbOrderRecordList, // 处理流程
          latitude,
          longitude,
        } = res.result
        this.eventDetailObj = {
          name: phone ? `${reporterName}（${phone}）` : reporterName,
          address: addressDetail,
          gsd: areaNo,
          reportTime: insertTime,
          type: eventCategoryName,
          origin: type,
          content: orderContent,
          eventNo: orderNo,
          state,
          eventTime: reportTime,
          info: istemporary === '0' ? '是' : '否',
          urgent: priority,
        }
        this.processList = tbOrderRecordList.map((it, i) => ({
          ...it,
        }))
        tabIdx === 0 ? (this.isDone = true) : (this.isDone = false)

        this.eventMarkers = [
          {
            latlng: [latitude, longitude],
            icon: {
              iconUrl: require('@/assets/map/point/point1.png'),
              iconSize: [32, 42],
              iconAnchor: [16, 42],
            },
            props: {
              id: id,
              type: 'eventInformation',
            },
          },
        ]
        this.$refs.leafletMap.drawPoiMarker(this.eventMarkers, 'eventInformation', true)
        this.currentCheckedMarker = [latitude, longitude]

        this.eventDetailShow = true
      }
    },
    // 未办结工单下钻按钮操作
    async eventStatisticOperateQgd(i, id, it, tabIdx) {
      console.log(i, id, it, tabIdx)
      if (i === 0) {
        const info = it[9]
        console.log(info)
        const res = await getQgdDetailJk({ orderId: info.id })
        const {
          appealTitle: title, // 标题
          appealType: type, // 诉求类型
          eventPlace: addressDetail, // 地址
          appealContent: orderContent, // 事件内容
          serveName: reporterName, // 姓名
          orderSource: eventCategoryName, // 事件类型
          eventDate: reportTime, // 事件时间 | 发生时间
          createDate: insertTime, // 上报时间
          orderNum: orderNo, // 事件编号
          formStatus: state, // 事件状态
          showFlag: istemporary, // 公开信息：0 是
          emeLevel: priority, // 紧急程度
          area: areaNo, // 归属地
          torderAttachments: appendixList, // 附件
          mobile: phone, // 手机号
          community: gsd, //归属地
          lng: longitude, //归属地
          lat: latitude, //归属地
          // tbOrderRecordList // 处理流程
        } = info || {}

        const tbOrderRecordList = res.result // 处理流程

        this.eventDetailObj = {
          name: phone ? `${reporterName}（${phone}）` : reporterName,
          address: addressDetail,
          gsd: gsd,
          reportTime: insertTime,
          type: type,
          origin: eventCategoryName,
          content: orderContent,
          eventNo: orderNo,
          state,
          eventTime: reportTime,
          info: istemporary === '0' ? '是' : '否',
          urgent: priority,
        }

        this.processList = tbOrderRecordList.map((it, i) => ({
          ...it,
        }))
        tabIdx === 0 ? (this.isDone = true) : (this.isDone = false)

        this.eventMarkers = [
          {
            latlng: [latitude, longitude],
            icon: {
              iconUrl: require('@/assets/map/point/point1.png'),
              iconSize: [32, 42],
              iconAnchor: [16, 42],
            },
            props: {
              id: info.id,
              type: 'eventInformation',
            },
            info: info,
          },
        ]
        this.$refs.leafletMap.drawPoiMarker(this.eventMarkers, 'eventInformation', true)
        this.currentCheckedMarker = [latitude, longitude]
        this.eventDetailShow = true
      }
    },
    // 实时事件下钻按钮操作
    async realTimeEventOperate(i, id, it) {
      console.log(id)
      if (i === 0) {
        const res = await getEventDetail(id)
        const {
          title, // 标题
          type, // 事件来源
          addressDetail, // 地址
          orderContent, // 事件内容
          reporterName, // 姓名
          eventCategoryName, // 事件类型
          reportTime, // 事件时间 | 发生时间
          insertTime, // 上报时间
          orderNo, // 事件编号
          state, // 事件状态
          istemporary, // 公开信息：0 是
          priority, // 紧急程度
          areaNo, // 归属地
          appendixList, // 附件
          phone, // 手机号
          tbOrderRecordList, // 处理流程
          latitude,
          longitude,
        } = res.result
        this.eventDetailObj = {
          name: phone ? `${reporterName}（${phone}）` : reporterName,
          address: addressDetail,
          gsd: areaNo,
          reportTime: insertTime,
          type: eventCategoryName,
          origin: type,
          content: orderContent,
          eventNo: orderNo,
          state,
          eventTime: reportTime,
          info: istemporary === '0' ? '是' : '否',
          urgent: priority,
        }
        this.processList = tbOrderRecordList.map((it, i) => ({
          ...it,
        }))
        this.eventMarkers = [
          {
            latlng: [latitude, longitude],
            icon: {
              iconUrl: require('@/assets/map/point/point1.png'),
              iconSize: [32, 42],
              iconAnchor: [16, 42],
            },
            props: {
              id: id,
              type: 'eventInformation',
            },
          },
        ]
        this.$refs.leafletMap.drawPoiMarker(this.eventMarkers, 'eventInformation', true)
        this.currentCheckedMarker = [latitude, longitude]
        this.eventDetailShow = true
      } else if (i === 1) {
        this.realTimeEventDialogShow = false
        console.log(it)
        // 如果地图上已经打点了事件信息，直接聚焦
        if (this.asideList[0].active) {
          this.$refs.leafletMap.flyToPoint([it[9], it[10]], 15)
        } else {
          // 将该点位单独标记在地图上
          this.$refs.leafletMap.drawPoiMarker(
            this.eventMarkers.filter((it) => it.props.id === id),
            'eventInformation',
            true
          )
        }
      }
    },
    // 实时事件下钻按钮操作
    async realTimeEventOperateQgd(i, id, it) {
      console.log(it)
      const info = it[10]
      if (i === 0) {
        const res = await getQgdDetailJk({ orderId: info.id })
        const {
          appealTitle: title, // 标题
          appealType: type, // 诉求类型
          eventPlace: addressDetail, // 地址
          appealContent: orderContent, // 事件内容
          serveName: reporterName, // 姓名
          orderSource: eventCategoryName, // 事件类型
          eventDate: reportTime, // 事件时间 | 发生时间
          createDate: insertTime, // 上报时间
          orderNum: orderNo, // 事件编号
          formStatus: state, // 事件状态
          showFlag: istemporary, // 公开信息：0 是
          emeLevel: priority, // 紧急程度
          area: areaNo, // 归属地
          torderAttachments: appendixList, // 附件
          mobile: phone, // 手机号
          community: gsd, //归属地
          lng: longitude, //归属地
          lat: latitude, //归属地
          // tbOrderRecordList // 处理流程
        } = info || {}

        const tbOrderRecordList = res.result // 处理流程
        this.eventDetailObj = {
          name: phone ? `${reporterName}（${phone}）` : reporterName,
          address: addressDetail,
          gsd: gsd,
          reportTime: insertTime,
          type: type,
          origin: eventCategoryName,
          content: orderContent,
          eventNo: orderNo,
          state,
          eventTime: reportTime,
          info: istemporary === '0' ? '是' : '否',
          urgent: priority,
        }

        this.processList = tbOrderRecordList.map((it, i) => ({
          ...it,
        }))
        this.eventMarkers = [
          {
            latlng: [latitude, longitude],
            icon: {
              iconUrl: require('@/assets/map/point/point1.png'),
              iconSize: [32, 42],
              iconAnchor: [16, 42],
            },
            props: {
              id: id,
              type: 'eventInformation',
            },
            info: info,
          },
        ]
        this.$refs.leafletMap.drawPoiMarker(this.eventMarkers, 'eventInformation', true)
        this.currentCheckedMarker = [latitude, longitude]
        this.eventDetailShow = true
      } else if (i === 1) {
        this.realTimeEventDialogShow = false
        console.log(it, info)
        // 如果地图上已经打点了事件信息，直接聚焦
        if (this.asideList[0].active) {
          this.$refs.leafletMap.flyToPoint([info.lat, info.lng], 18)
        } else {
          // 将该点位单独标记在地图上
          console.log(this.eventMarkers)
          this.eventMarkers = [
            {
              latlng: [info.lat, info.lng],
              icon: {
                iconUrl: require('@/assets/map/point/point1.png'),
                iconSize: [32, 42],
                iconAnchor: [16, 42],
              },
              props: {
                id: info.id,
                type: 'eventInformation',
              },
              info: info,
            },
          ]
          this.$refs.leafletMap.drawPoiMarker(this.eventMarkers, 'eventInformation', true)
          this.$refs.leafletMap.flyToPoint([info.lat, info.lng], 18)
        }
      }
    },
    // 获取预警信息
    getMessage(message) {
      console.log(message)
      this.messageArr.push({
        orderId: message.orderId,
        category: message.category,
        orderno: message.orderno,
        message: message.message,
        system: message.system,
      })
      this.warnMessage = this.messageArr

      this.reloadEvent()
      // console.log(this.warnMessage)
    },
    async getEventInfoFn() {
      // if (this.eventMarkers) {
      //   this.$refs.leafletMap.drawPoiMarker(this.eventMarkers, 'eventInformation', true)
      //   return
      // }
      const res = await getEventInfo()
      if (res?.code === 200) {
        this.eventMarkers = res.result.map((it) => ({
          latlng: [it.latitude, it.longitude],
          icon: {
            iconUrl: require('@/assets/map/point/point1.png'),
            iconSize: [32, 42],
            iconAnchor: [16, 42],
          },
          props: {
            id: it.id,
            type: 'eventInformation',
          },
        }))
        this.$refs.leafletMap.drawPoiMarker(this.eventMarkers, 'eventInformation', true)
      } else {
        this.$message({
          message: '未查询到事件信息',
          type: 'warning',
        })
      }
    },
    async getEventDetailFn(id) {
      const res = await getEventDetail(id)
      if (res?.code === 200) {
        const {
          title, // 标题
          type, // 事件来源
          addressDetail, // 地址
          orderContent, // 事件内容
          reporterName, // 姓名
          eventCategoryName, // 事件类型
          reportTime, // 事件时间 | 发生时间
          insertTime, // 上报时间
          orderNo, // 事件编号
          state, // 事件状态
          istemporary, // 公开信息：0 是
          priority, // 紧急程度
          areaNo, // 归属地
          appendixList, // 附件
          phone, // 手机号
          tbOrderRecordList, // 处理流程
        } = res.result
        this.currentAddress = addressDetail
        console.log(this.currentAddress)
        this.eventInfo = {
          title,
          type: eventCategoryName,
          time: reportTime,
          address: addressDetail,
          describe: orderContent,
        }
        // eventDetail - 用于事件信息点击一般处置展示的左侧的事件详情
        this.eventDetail = [
          {
            label: '姓名',
            value: reporterName,
          },
          {
            label: '事件类型',
            value: eventCategoryName,
          },
          {
            label: '事件地址',
            value: addressDetail,
          },
          {
            label: '上报时间',
            value: insertTime,
          },
          {
            label: '事件来源',
            value: type,
          },
          {
            label: '事件编号',
            value: orderNo,
          },
          {
            label: '事件状态',
            value: state,
          },
          {
            label: '事发时间',
            value: reportTime,
          },
          {
            label: '公开信息',
            value: istemporary === '0' ? '是' : '否',
          },
          {
            label: '紧急程度',
            value: priority,
          },
          {
            label: '归属地',
            value: areaNo,
          },
          {
            label: '事件内容',
            value: orderContent,
          },
          {
            label: '附件',
            value: appendixList?.length > 0 ? '' : '无',
          },
        ]
        // eventDetailObj - 用于事件信息点击事件详情
        this.eventDetailObj = {
          name: phone ? `${reporterName}（${phone}）` : reporterName,
          address: addressDetail,
          gsd: areaNo,
          reportTime: insertTime,
          type: eventCategoryName,
          origin: type,
          content: orderContent,
          eventNo: orderNo,
          state,
          eventTime: reportTime,
          info: istemporary === '0' ? '是' : '否',
          urgent: priority,
        }
        // 计划完成时间expectTime，签收时间claimTime，附件appendixList，处置意见dealAdvice，操作动作nodeName+operateName
        this.processList = tbOrderRecordList.map((it, i) => ({
          ...it,
        }))
        this.eventImgs = []
        appendixList?.forEach((it) => {
          this.eventImgs.push(it.url)
        })
        this.eventDialogShow = true
      } else {
        this.$message({
          message: '未查询到事件详情',
          type: 'warning',
        })
      }
    },
    async getSurroundingResourcesPersonListFn() {
      const res = await getSurroundingResourcesPersonList()
      if (res?.code === 200) {
        const codes = ['4000', '4002', '4003', '4004', '4005', '4006', '4007']
        this.surroundingResourcesList = res.result.map((it, i) => ({
          checked: false,
          icon: require('@/assets/zhdd/icon13.png'),
          userName: it.userName,
          deptName: it.deptName,
          code: codes[i] ? codes[i] : 0,
        }))
        this.surroundingResourcesList.splice(
          1,
          0,
          {
            checked: false,
            icon: require('@/assets/zhdd/icon13.png'),
            userName: '和进网格员1',
            deptName: '湖熟街道',
            code: '4001',
          },
          {
            checked: false,
            icon: require('@/assets/zhdd/icon13.png'),
            userName: '执法仪',
            deptName: '湖熟街道',
            code: '34021000001320000006',
          }
        )
        this.surroundingResourcesList[3].userName = '湖熟街道城管局一队管理员'
        this.temporaryResource = this.surroundingResourcesList
        this.surroundingResourcesList = this.temporaryResource.slice(1, 6)
        this.surroundingResourceTabsProp[0].count = res.result.length
        this.surroundingResourceTabsProp[1].count = 30
        this.surroundingResourceTabsProp[2].count = 25
        this.surroundingResourceTabsProp[3].count = 32
        this.surroundingResourceTabsProp[4].count = 43
        this.surroundingResourceTabsProp[5].count = 10
        this.surroundingResourceTabsProp[6].count = 12
      }
    },
    changeSlider(val) {
      this.surroundingResourceTabsProp.forEach((it, i) => {
        if (i === 0) {
          it.count =
            27 + Math.floor((val - 50) / 10) * 3 < 0 ? 0 : 27 + Math.floor((val - 50) / 10) * 3
        } else if (i === 1) {
          it.count =
            30 + Math.floor((val - 50) / 10) * 3 < 0 ? 0 : 30 + Math.floor((val - 50) / 10) * 3
        } else if (i === 2) {
          it.count =
            25 + Math.floor((val - 50) / 10) * 3 < 0 ? 0 : 25 + Math.floor((val - 50) / 10) * 3
        } else if (i === 3) {
          it.count =
            32 + Math.floor((val - 50) / 10) * 3 < 0 ? 0 : 32 + Math.floor((val - 50) / 10) * 3
        } else if (i === 4) {
          it.count =
            43 + Math.floor((val - 50) / 10) * 3 < 0 ? 0 : 43 + Math.floor((val - 50) / 10) * 3
        } else if (i === 5) {
          it.count =
            10 + Math.floor((val - 50) / 10) * 3 < 0 ? 0 : 10 + Math.floor((val - 50) / 10) * 3
        } else if (i === 6) {
          it.count =
            12 + Math.floor((val - 50) / 10) * 3 < 0 ? 0 : 12 + Math.floor((val - 50) / 10) * 3
        }
      })
      if (val >= 0 && val < 25) {
        this.surroundingResourcesList = this.temporaryResource.slice(1, 4)
      } else if (val >= 25 && val < 50) {
        this.surroundingResourcesList = this.temporaryResource.slice(1, 5)
      } else if (val >= 50 && val < 75) {
        this.surroundingResourcesList = this.temporaryResource.slice(1, 6)
      } else if (val >= 75 && val < 100) {
        this.surroundingResourcesList = this.temporaryResource.slice(1, 7)
      }
    },
    // 点击地图小工具
    clickTool(i) {
      // 在非事件处置时，仅第三个删除工具可使用
      if (this.zhddType === 0) {
        if (i === 2) {
          // 关闭事件弹窗
          this.eventDialogShow = false
          // 清除事件标记点
          this.removeAll()
          // 清除图标高亮效果
          this.asideList[0].active = false
        }
        return
      }
      // 在事件处置时
      if (i === 0) {
        if (this.currentCheckedMarker) {
          // 在当前已选择的事件位置绘制一个圆
          this.$refs.leafletMap.drawCircle(this.currentCheckedMarker, 750)
          // 在圆形范围内随机落点（暂时写死，默认首次落点为人员）
          this.markerRandom(
            this.currentCheckedMarker,
            'personnelInformation',
            require(`@/assets/map/point/point2.png`)
          )
          // 获取周边人员（默认展示人员列表）
          this.getSurroundingResourcesPersonListFn()
          // 关闭视频监控
          this.showVideo = false
        } else {
          this.$message({
            message: '请先选择绘制中心点',
            type: 'info',
          })
        }
      } else if (i === 2) {
        this.$refs.leafletMap.removeLine()
        this.$refs.leafletMap.removeCircle()
      }
    },
    checkTab(it, i) {
      console.log(it, i)
      this.removeAllBesideEvent()
      this.checkedResource = i
      this.markerRandom(this.currentCheckedMarker, it.layerId, it.markerIcon)
    },
    markerRandom(center, layerId, iconUrl) {
      const min = -0.000001
      const max = 0.005555
      const marker = [
        {
          latlng: [
            Number(center[0]) + Math.random() * (max - min) + min,
            Number(center[1]) + Math.random() * (max - min) + min,
          ],
          icon: {
            iconUrl,
            iconSize: [32, 42],
            iconAnchor: [16, 42],
          },
          props: {
            id: `${layerId}0`,
            type: layerId,
          },
        },
        {
          latlng: [
            Number(center[0]) + Math.random() * (max - min) + min,
            Number(center[1]) + Math.random() * (max - min) + min,
          ],
          icon: {
            iconUrl,
            iconSize: [32, 42],
            iconAnchor: [16, 42],
          },
          props: {
            id: `${layerId}1`,
            type: layerId,
          },
        },
      ]
      this.$refs.leafletMap.drawPoiMarker(marker, layerId, true)
    },
    // 改变周边距离
    changeDistance(r) {
      this.$refs.leafletMap.changeCircle(500 * (1 + r / 100))
    },
    setAiMapPoint() {
      this.removeAllMarker()
      this.spjk1PointList = jcyjData.aiPoint.map((item) => ({
        ...item,
        popup: this.$refs.spjk1.$el,
        onclick: () => {
          this.popSjypInfo = {
            ...item.properties,
          }
          this.showSpjk1 = true
        },
      }))
      this.$refs.map.addMarkers(
        this.spjk1PointList,
        require('@/assets/jcyj/marker1.png'),
        [61, 113],
        'spjk1'
      )
    },
    // 显示更多
    showMoreFn1(i) {
      if (i === 0) {
        this.officeronDutyShow = true
      } else if (i === 1) {
        this.eventStatisticShow = true
      } else if (i === 2) {
        this.emergencyResourcesDialogShow = true
      } else if (i === 3) {
        // this.gridMemberLoginStatusDialogShow = true
        this.AIEventAnalysisDialogShow = true
      } else if (i === 4) {
        this.spjkShow = true
      } else if (i === 5) {
        this.realTimeEventDialogShow = true
      }
    },
    async markerMessage(it) {
      console.log(it)
      const _idx = this.warnMessage.findIndex((item) => item.orderId === it.orderId)
      this.warnMessage.splice(_idx, 1)
      const res = await getEventDetail(it.orderId)
      console.log(res)
      this.$refs.leafletMap.removeLayer('message')
      this.messageMarkers = [
        {
          latlng: [res.result.latitude, res.result.longitude],
          icon: {
            iconUrl: require('@/assets/map/point/point.png'),
            iconSize: [68, 68],
            iconAnchor: [34, 68],
          },
          props: {
            id: it.orderId,
            type: 'message',
          },
        },
      ]
      this.$refs.leafletMap.drawPoiMarker(this.messageMarkers, 'message', true)
      setTimeout(() => {
        this.$refs.leafletMap.flyToPoint([res.result.latitude, res.result.longitude], 15)
      }, 200)
    },
    markDrone(i, it) {
      console.log(i, it)
    },
    openWrj(i, it) {
      console.log(i, it)
      this.videoUrl = it[it.length - 1]
      this.droneVideoShow = true
    },
    // 点击实时事件的每项
    clickRealtimeEvent(item) {
      console.log(item)
      // 如果地图上已经打点了事件信息，直接聚焦
      if (this.asideList[0].active) {
        this.$refs.leafletMap.flyToPoint([item.latitude, item.longitude], 15)
      } else {
        // 将该点位单独标记在地图上
        const singleEventMarker = this.eventMarkers.filter((it) => it.props.id === item.id)
        this.$refs.leafletMap.drawPoiMarker(singleEventMarker, 'eventInformation', true)
        this.$refs.leafletMap.flyToPoint(singleEventMarker[0].latlng, 15)
      }
    },
    clickRealtimeEventQgd(item) {
      console.log(item)
      // 如果地图上已经打点了事件信息，直接聚焦
      if (this.asideList[0].active) {
        this.$refs.leafletMap.flyToPoint([item.lat, item.lng], 18)
      } else {
        // 将该点位单独标记在地图上
        // console.log('this.eventMarkers', this.eventMarkers)
        const singleEventMarker = this.eventMarkers.filter((it) => it.props.id === item.id)
        this.$refs.leafletMap.drawPoiMarker(singleEventMarker, 'eventInformation', true)
        console.log('singleEventMarker', singleEventMarker)
        this.$refs.leafletMap.flyToPoint(
          [singleEventMarker[0].latlng[0], singleEventMarker[0].latlng[1]],
          18
        )
      }
    },
    chooseSource(checked, i, item) {
      console.log(checked, i, item)
      const layerId = item.layerId
      if (checked) {
        if (item.label === '无人机') {
          this.droneShow = checked
        } else if (item.label === '企业应急队伍') {
          const data = this.encapsulateArrayofPoint(enterpriseEmergencyTeamPoint, layerId)
          this.marker(data, layerId)
        } else if (item.label === '加油站') {
          const data = this.encapsulateArrayofPoint(gasStationPoint, layerId)
          this.marker(data, layerId)
        } else if (item.label === '企业') {
          const data = this.encapsulateArrayofPoint(enterprisePoint, layerId)
          this.marker(data, layerId)
        } else if (item.label === '防护目标') {
          const data = this.encapsulateArrayofPoint(protectiveTargetPoint, layerId)
          this.marker(data, layerId)
        } else if (item.label === '体外除颤仪') {
          const data = this.encapsulateArrayofPoint(externalDefibrillatorPoint, layerId)
          this.marker(data, layerId)
        } else if (item.label === '医疗机构') {
          const data = this.encapsulateArrayofPoint(medicalInstitutionPoint, layerId)
          this.marker(data, layerId)
        } else if (item.label === '变电站') {
          const data = this.encapsulateArrayofPoint(substationPoint, layerId)
          this.marker(data, layerId)
          // this.aqjgGetYjzyFn(item.label, layerId)
        } else if (item.label === '二级消防单位') {
          const data = this.encapsulateArrayofPoint(fireFightingUnitPoint, layerId)
          this.marker(data, layerId)
        } else if (item.label === '视频监控') {
          // const data = this.encapsulateArrayofPoint(videoSurveillancePoint, layerId)
          const data = this.encapsulateArrayofPoint(
            this.spjkMarkerList.map((it) => ({
              latlng: [it.latitude, it.longitude],
              iconUrl: require('@/assets/map/point/point19.png'),
              id: it.id,
            })),
            layerId
          )
          this.marker(data, layerId)
        } else if (item.label === '公安队伍') {
          const data = this.encapsulateArrayofPoint(publicecurityTeamPoint, layerId)
          this.marker(data, layerId)
        } else if (item.label === '学校') {
          const data = this.encapsulateArrayofPoint(schoolPoint, layerId)
          this.marker(data, layerId)
        } else if (item.label === '网格员') {
          const data = this.encapsulateArrayofPoint(gridOperatorPoint, layerId)
          this.marker(data, layerId)
        }
      } else {
        if (item.label === '无人机') {
          this.droneShow = checked
        } else {
          this.$refs.leafletMap.removeLayer(layerId)
        }
      }
    },
    encapsulateArrayofPoint(points, layerId) {
      console.log(points)
      const data = points.map((it) => ({
        latlng: it.latlng,
        icon: {
          iconUrl: it.iconUrl,
          iconSize: [32, 42],
          iconAnchor: [16, 42],
        },
        props: {
          id: it.id,
          type: layerId,
        },
        info: it.info,
      }))
      return data
    },
    // 标记点基础信息弹窗内按钮点击事件
    clickBasicInformationBtn(i, name) {
      console.log(i, name)
      if (name === '人员信息' || name === '网格员') {
        this.sphsData = [{ id: '4001', label: '和进网格员1' }]
        this.initCheckedPeo = ['4001']
        if (i === 0) {
          this.txddShow = true
        } else if (i === 1) {
          this.isXlgcShow1 = true
        } else if (i === 2) {
          this.basicInfomationShow = false
          this.$refs.leafletMap.drawLins([
            this.currentCheckedPersonMarker,
            [31.84224153277144, 118.97117531288804],
            [31.84264153277144, 118.97197531288804],
            this.currentCheckedPersonMarker,
          ])
          // this.showPersonalTrack()
        }
      }
    },
    async showPersonalTrack() {
      const res = await getPersonnelTrack()
      console.log(res)
    },
    // 事件信息弹窗上点击按钮触发事件
    eventInfoBtnClick(i) {
      if (i === 0) {
        this.eventDetailShow = true
      } else if (i === 1) {
        // 点击一般处置后，出现一般处置界面，同时隐藏事件信息弹窗，地图上只出现当前选中的这个事件点位
        this.zhddType = 1
        this.eventDialogShow = false
        // 清除标记点
        this.$refs.leafletMap.removeLayer('eventInformation')
        // 将该点位单独标记在地图上
        this.$refs.leafletMap.drawPoiMarker(
          this.eventMarkers.filter((it) => it.props.id === this.eventId),
          'eventInformation',
          true
        )
      } else if (i === 2) {
        window.open('https://jnyj.honganshuke.com:10082/front/hushu-map/#/', '_blank')
      }
    },
    // 关闭一般处置界面
    closeGeneralDisposal() {
      this.zhddType = 0
      // 清除标记点
      this.$refs.leafletMap.removeLayer('eventInformation')
      // 将所有事件点位重绘到地图上
      this.$refs.leafletMap.drawPoiMarker(this.eventMarkers, 'eventInformation', true)
    },
    removeAll() {
      this.$refs.leafletMap.removeLayer('eventInformation')
      this.$refs.leafletMap.removeLayer('message')
      this.$refs.leafletMap.removeLine()
      surroundingResourceTabs.forEach((it) => {
        this.$refs.leafletMap.removeLayer(it.layerId)
      })
    },
    removeAllBesideEvent() {
      this.$refs.leafletMap.removeLine()
      surroundingResourceTabs.forEach((it) => {
        this.$refs.leafletMap.removeLayer(it.layerId)
      })
    },
    watchFlag() {
      let flag = this.$route.query.flag?.substr(0, 4)
      this.djylShow = false
      this.iscsglgdShow = false
      switch (flag) {
        case 'djyl':
          this.djylShow = true
          break
        case 'csgl':
          this.iscsglgdShow = true
          break
        default:
          break
      }
    },
    jgzz(index) {
      this.isWgllShow = false
      this.isdzzShow = false
      this.isxzqyShow = false
      this.iszwzxShow = false
      this.iszhzxShow = false
      this.isShowEventDetail = false
      this.isXlgcShow = false
      this.iszdqyShow = false
      this.isxxShow = false
      this.iswbShow = false
      this.isylcsShow = false
      this.ishczShow = false
      this.isdyyShow = false
      this.bjxqShow = false
      this.jgzzActive = index
      this.$refs.CesiumMapRef.removeAllLayer() //移除所有点位
      if (index == 0) {
        // 地图打点
        let data = xzqhPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'xzqhPoint')
      } else if (index == 1) {
        // 地图打点
        let data = dzzPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'dzzPoint')
      } else if (index == 2) {
        // 地图打点
        let data = zwzxPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'zwzxPoint')
      } else if (index == 3) {
        // 地图打点
        let data = zzzxPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'zzzxPoint')
      }
    },
    zdcs(index) {
      this.isWgllShow = false
      this.isdzzShow = false
      this.isxzqyShow = false
      this.iszwzxShow = false
      this.iszhzxShow = false
      this.isShowEventDetail = false
      this.isXlgcShow = false
      this.iszdqyShow = false
      this.isxxShow = false
      this.iswbShow = false
      this.isylcsShow = false
      this.ishczShow = false
      this.isdyyShow = false
      this.bjxqShow = false
      this.zdcsActive = index
      this.$refs.CesiumMapRef.removeAllLayer() //移除所有点位
      if (index == 0) {
        // 地图打点
        let data = zdqyPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'zdqyPoint')
      } else if (index == 1) {
        // 地图打点
        let data = xxPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'xxPoint')
      } else if (index == 2) {
        // 地图打点
        let data = wbPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'wbPoint')
      } else if (index == 3) {
        // 地图打点
        let data = ylcsPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'ylcsPoint')
      } else if (index == 4) {
        // 地图打点
        let data = hczPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'hczPoint')
      } else if (index == 5) {
        // 地图打点
        let data = dyyPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'dyyPoint')
      }
    },
    closeCountryPart() {
      this.isShowCountryPart = false
      if (this.activaIdx === 3) this.activaIdx = -1
    },
    checkTree(e) {
      // 地图打点
      let data = csbjPoint.features.map((e) => {
        return {
          position: e.geometry.coordinates.concat(100),
          properties: e.properties,
          img: e.img,
        }
      })
      this.$refs.CesiumMapRef.loadPoints1(data, 'csbjPoint')
    },
    async cameraMarker() {
      const res = await getCameraMakers()
      if (res && res.length > 0) {
        let filerRes = res.filter((item) => item.id)
        this.cameraId1 = filerRes[4].id
        this.cameraId2 = filerRes[5].id
        this.cameraId3 = filerRes[7].id
        this.cameraId4 = filerRes[9].id
        this.spjkMarkerList = res
        this.asideLeveltwoMenu[9].count = this.spjkMarkerList.length
      }
    },
    async cameraXq1(id) {
      const res = await getCameraXq(id)
      if (res && res.body && res.body.data && res.body.data.length > 0 && res.body.data[0].url) {
        this.videoSingleUrl = res.body.data[0].url
        this.cameraCode = id
        this.videoSingleShow = true
      }
    },
    async getQgdList() {
      let res1 = await getQgdList() // 地图落点
      // console.log('res1', res1)
      if (res1.code == '200') {
        this.eventMarkers = res1.result.map((it) => ({
          latlng: [it.lat, it.lng],
          icon: {
            iconUrl: require('@/assets/map/point/point1.png'),
            iconSize: [32, 42],
            iconAnchor: [16, 42],
          },
          props: {
            id: it.id,
            type: 'eventInformation',
          },
          info: it,
        }))
        this.$refs.leafletMap.drawPoiMarker(this.eventMarkers, 'eventInformation', true)
        //  let res2 = await getQgdSssj({ street: '1649962979288023040', size: 10 }) // 实时事件
        // console.log('res2.data', res2)
        this.realTimeEventList = res1.result.slice(0, 10).map((it) => ({
          ...it,
        })) // 实时事件
      } else {
        this.$message({
          message: '未查询到事件信息',
          type: 'warning',
        })
      }
    },
    async getQgdWbjTj() {
      let res3 = await getQgdWbjTj() // 未办结工单
      this.notCompl1.value = res3.result.unDoneCount
      this.notCompl2 = [
        ['product', '占比'],
        ['紧急', res3.result.isEmeLevel],
        ['一般', res3.result.unEmeLevel],
      ]
      console.log('this.notCompl2',this.notCompl2);
    },
    async getQgdListDetail(info) {
      console.log('info', info)
      const res = await getQgdDetailJk({ orderId: info.id })
      if (res?.code == '200') {
        const {
          appealTitle: title, // 标题
          appealType: type, // 诉求类型
          eventPlace: addressDetail, // 地址
          appealContent: orderContent, // 事件内容
          serveName: reporterName, // 姓名
          orderSource: eventCategoryName, // 事件类型
          eventDate: reportTime, // 事件时间 | 发生时间
          createDate: insertTime, // 上报时间
          orderNum: orderNo, // 事件编号
          formStatus: state, // 事件状态
          showFlag: istemporary, // 公开信息：0 是
          emeLevel: priority, // 紧急程度
          area: areaNo, // 归属地
          torderAttachments: appendixList, // 附件
          mobile: phone, // 手机号
          community: gsd, //归属地
          grid: grid,
          // tbOrderRecordList // 处理流程
        } = info || {}

        // const eventCategoryName = info.orderSource
        //   ? this.qgdCodesjlyOptions.find(item => item.dicValue == info.orderSource).dicLabel
        //   : '' // 事件来源
        // const priority = info.emeLevel
        //   ? this.qgdCodejjcdOptions.find(item => item.dicValue == info.emeLevel).dicLabel
        //   : '' // 优先级

        // console.log('res', res)
        const tbOrderRecordList = res.result // 处理流程
        this.currentAddress = addressDetail
        // console.log(this.currentAddress)
        this.eventInfo = {
          title: title,
          type: type,
          time: reportTime,
          address: addressDetail,
          describe: orderContent,
        }
        // eventDetail - 用于事件信息点击一般处置展示的左侧的事件详情
        console.log('eventCategoryName', eventCategoryName)
        this.eventDetail = [
          {
            label: '姓名',
            value: reporterName,
          },
          {
            label: '事件类型',
            value: type,
          },
          {
            label: '事件地址',
            value: addressDetail,
          },
          {
            label: '上报时间',
            value: insertTime,
          },
          {
            label: '事件来源',
            value: eventCategoryName,
          },
          {
            label: '事件编号',
            value: orderNo,
          },
          {
            label: '事件状态',
            value: state,
          },
          {
            label: '事发时间',
            value: reportTime,
          },
          {
            label: '公开信息',
            value: istemporary === '0' ? '是' : '否',
          },
          {
            label: '紧急程度',
            value: priority,
          },
          {
            label: '归属地',
            value: areaNo,
          },
          {
            label: '事件内容',
            value: orderContent,
          },
          // {
          //   label: '附件',
          //   value: appendixList?.length > 0 ? '' : '无'
          // }
        ]
        // eventDetailObj - 用于事件信息点击事件详情
        this.eventDetailObj = {
          name: phone ? `${reporterName}（${phone}）` : reporterName,
          address: addressDetail,
          gsd: gsd,
          reportTime: insertTime,
          type: type,
          origin: eventCategoryName,
          content: orderContent,
          eventNo: orderNo,
          state,
          eventTime: reportTime,
          info: istemporary === '0' ? '是' : '否',
          urgent: priority,
          grid: grid,
        }
        console.log('this.eventDetailObj', this.eventDetailObj)

        // 计划完成时间expectTime，签收时间claimTime，附件appendixList，处置意见dealAdvice，操作动作nodeName+operateName
        this.processList = tbOrderRecordList.map((it, i) => ({
          ...it,
        }))
        // this.eventImgs = []
        // appendixList?.forEach(it => {
        //   this.eventImgs.push(it.url)
        // })
        this.eventDialogShow = true
      } else {
        this.$message({
          message: '未查询到事件详情',
          type: 'warning',
        })
      }
    },
  },
}
</script>

<style lang="less" scoped>
@font-face {
  font-family: QuartzRegular;
  src: url(~@/assets/leader/font/QuartzRegular.ttf);
}
@keyframes rotateS {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes rotateN {
  0% {
    transform: rotate(360deg);
  }
  100% {
    transform: rotate(0);
  }
}
@keyframes rotateY {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotateY(360deg);
  }
}
.warning_tips {
  height: 41px;
  position: absolute;
  left: 660px;
  top: 136px;
  z-index: 1000;
  overflow: hidden;
}

.leader_sjts {
  width: 1920px;
  height: 1080px;
  position: absolute;
  top: 0;
  display: flex;
  justify-content: space-between;
  .leader_city_box {
    width: 450px;
    .leader_zt_contain {
      height: 100%;
    }
  }
  .box {
    .cont {
      width: 100%;
      height: 100%;
    }
    .wrapper1 {
      width: 100%;
      height: 100%;
      padding: 14px 0 22px;
      .introduce {
        width: 100%;
        height: 100%;
        padding: 18px 26px;
        background: url(~@/assets/hszl/bg1.png) no-repeat;
        font-size: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #4fddff;
        line-height: 22px;
        letter-spacing: 2px;
        text-align: left;
      }
    }
    .wrapper4 {
      width: 100%;
      height: 100%;
      padding: 20px 0;
      ul {
        width: 100%;
        height: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;
        li {
          display: flex;
          height: 49px;
          gap: 10px;
          .icon {
            width: 46px;
            height: 49px;
          }
          .info {
            margin-top: -6px;
            .label {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
              text-align: left;
              padding-left: 5px;
            }
            .line {
              width: 87px;
              height: 6px;
              background: url('~@/assets/hszl/line1.png') no-repeat;
            }
            .count {
              text-align: left;
              margin-top: 4px;
              width: 87px;
              height: 22px;
              line-height: 22px;
              background: linear-gradient(96deg, #1d4975 0%, rgba(19, 56, 87, 0.24) 100%);
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #a8b1b9;
              padding-left: 5px;
              span {
                font-size: 18px;
                font-family: PingFangSC, PingFang SC;
                font-weight: normal;
                color: #ffffff;
                line-height: 22px;
                margin-right: 3px;
              }
            }
          }
        }
      }
    }
    .wrapper5 {
      width: 100%;
      height: 100%;
      padding-top: 14px;
      .info {
        width: 100%;
        height: 71px;
        padding-left: 8px;
        display: flex;
        justify-content: space-between;
        .total {
          width: 130px;
          height: 100%;
          background: url('~@/assets/hszl/bg4.png') no-repeat;
          display: grid;
          place-items: center;
          .cont {
            padding-top: 13px;
            .label {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
            }
            .value {
              font-size: 18px;
              font-family: PingFangSC, PingFang SC;
              font-weight: normal;
              color: #ffffff;
              line-height: 22px;
              background: linear-gradient(180deg, #ffffff 0%, #00fffa 67%, #04a1ff 100%);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              span {
                font-size: 12px;
              }
            }
          }
        }
        .counts {
          width: 312px;
          height: 100%;
          background: url('~@/assets/hszl/bg5.png') no-repeat;
          display: flex;
          padding: 0 9px;
          .men {
            text-align: left;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            margin-right: 4px;
            .count {
              height: 21px;
              font-size: 16px;
              font-family: PingFangSC, PingFang SC;
              font-weight: normal;
              line-height: 19px;
              background: linear-gradient(180deg, #ffffff 0%, #00ceff 67%, #04a1ff 100%);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              span {
                font-size: 14px;
              }
            }
            .chart {
              display: flex;
              gap: 4px;
              span {
                width: 10px;
                height: 25px;
                background-repeat: no-repeat;
              }
            }
          }
          .women {
            text-align: right;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            .count {
              height: 21px;
              font-size: 16px;
              font-family: PingFangSC, PingFang SC;
              font-weight: normal;
              line-height: 19px;
              background: linear-gradient(180deg, #ffffff 0%, #f076ff 66%, #ff04ff 100%);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              span {
                font-size: 14px;
              }
            }
            .chart {
              display: flex;
              gap: 4px;
              span {
                width: 12px;
                height: 25px;
                background-repeat: no-repeat;
              }
            }
          }
        }
      }
      .chart_wrap {
        position: relative;
        width: 100%;
        height: 198px;
        .sign {
          position: absolute;
          left: 105px;
          top: 25px;
          .woman {
            position: absolute;
            width: 27px;
            height: 57px;
            left: 0;
            top: 0;
            background: url('~@/assets/hszl/woman.png') no-repeat;
          }
          .man {
            position: absolute;
            width: 23px;
            height: 57px;
            left: 95px;
            top: 47px;
            background: url('~@/assets/hszl/man.png') no-repeat;
          }
          .man_count {
            position: absolute;
            left: 10px;
            top: 65px;
            width: 109px;
            height: 31px;
            font-size: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 24px;
          }
          .woman_count {
            position: absolute;
            left: 10px;
            top: 10px;
            width: 109px;
            height: 31px;
            font-size: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 24px;
          }
        }
      }
    }
  }
  .box1 {
    .cont {
      padding: 13px 0 23px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: space-between;
      li {
        position: relative;
        width: 222px;
        height: 61px;
        padding: 10px 7px;
        display: flex;
        background: url(~@/assets/hszl/bg2.png) no-repeat;
        .icon {
          width: 41px;
          height: 41px;
          background: url(~@/assets/hszl/bg3.png) no-repeat;
          font-size: 17px;
          img {
            animation: move infinite 3s ease-in-out;
            @keyframes move {
              0% {
                transform: rotateY(0);
              }
              50% {
                transform: rotateY(180deg);
              }
              100% {
                transform: rotateY(360deg);
              }
            }
          }
          .name {
            font-size: 10px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 11px;
            text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
          }
        }
        .line {
          position: absolute;
          left: 50px;
          top: 5px;
          width: 1px;
          height: 55px;
          border: 1px solid;
          border-image: linear-gradient(
              180deg,
              rgba(0, 83, 171, 0),
              rgba(0, 140, 213, 0.6),
              rgba(0, 83, 171, 0)
            )
            1 1;
        }
        .desc {
          position: relative;
          margin-left: 15px;
          text-align: left;
          padding-left: 18px;
          .xsj {
            position: absolute;
            width: 10px;
            height: 10px;
            left: 0;
            top: 7px;
            background: url(~@/assets/csts/xsj1.png) no-repeat;
          }
          .num {
            font-size: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 24px;
          }
          .tit {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 20px;
            white-space: nowrap;
          }
        }
      }
    }
  }
  .box2 {
    .cont {
      padding: 14px 28px 24px;
      display: flex;
      gap: 5px;
      .cont_item {
        position: relative;
        width: 128px;
        height: 176px;
        padding: 7px 15px 0 16px;
        box-shadow: inset 0px 1px 12px 2px rgba(44, 165, 235, 0.2);
        .icon {
          width: 97px;
          height: 99px;
        }
        .lzdx {
          width: 97px;
          height: 99px;
          position: absolute;
          top: 7px;
          left: 16px;
        }
        .tit {
          position: absolute;
          top: 97px;
          left: 50%;
          width: 100%;
          transform: translateX(-50%);
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #caecff;
          line-height: 20px;
          text-shadow: 0px 0px 1px #00132e;
        }
        ol {
          width: 100%;
          height: 40px;
          margin-top: 21px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          padding-left: 20px;
          & > li {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 22px;
            text-align: left;
            list-style: disc;
            &::marker {
              color: #38deff;
            }
            .unit {
              font-size: 12px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #caecff;
              line-height: 17px;
              text-shadow: 0px 0px 1px #00132e;
            }
          }
        }
      }
    }
  }
  .box3 {
    .cont {
      padding: 14px 28px 0;
      video {
        width: 100%;
        height: 100%;
        object-fit: cover;
        &:not(:root):fullscreen {
          object-fit: contain;
        }
      }
    }
  }
  .box4 {
    .cont {
      padding: 15px 0;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-evenly;
      li {
        position: relative;
        width: 200px;
        height: 66px;
        background: url(~@/assets/csts/bg2.png) no-repeat;
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: left;
        padding-left: 92px;
        .icon {
          position: absolute;
          width: 26px;
          height: 26px;
          top: 10px;
          left: 31px;
          animation: move infinite 3s ease-in-out;
          @keyframes move {
            0% {
              transform: translateY(0px) rotateY(0);
            }
            50% {
              transform: translateY(-10px) rotateY(180deg);
            }
            100% {
              transform: translateY(0px) rotateY(360deg);
            }
          }
        }
        &:nth-child(2) {
          span {
            animation-delay: 0.3s;
          }
        }
        &:nth-child(3) {
          span {
            animation-delay: 0.6s;
          }
        }
        &:nth-child(4) {
          span {
            animation-delay: 0.9s;
          }
        }
        &:nth-child(5) {
          span {
            animation-delay: 1.2s;
          }
        }
        &:nth-child(6) {
          span {
            animation-delay: 1.5s;
          }
        }
        .tit {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 20px;
        }
        .num {
          font-size: 18px;
          font-family: PingFangSC, PingFang SC;
          font-weight: normal;
          color: #ffffff;
          line-height: 22px;
        }
      }
    }
  }
  .box5 {
    .cont {
      display: flex;
      flex-direction: column;
      .bar_chart {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        li {
          position: relative;
          display: flex;
          align-items: center;
          padding: 0 30px 0 23px;
          .icon {
            width: 20px;
            height: 22px;
            margin-right: 7px;
          }
          .icon2 {
            width: 10px;
            height: 12px;
            position: absolute;
            left: 28px;
            top: 5px;
            animation: move infinite 3s ease-in-out;
            @keyframes move {
              0% {
                transform: translateY(0px) rotateY(0);
              }
              50% {
                transform: translateY(0px) rotateY(180deg);
              }
              100% {
                transform: translateY(0px) rotateY(360deg);
              }
            }
          }
          .lab {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            margin-right: 8px;
          }
          .progress {
            display: block;
            flex: 1;
            height: 100%;
            background: rgba(0, 201, 255, 0.14);
            .cur {
              width: 100%;
              border: 1px solid;
              height: 100%;
              overflow: hidden;
              transition: width 0.5s;
            }
          }
          .num {
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            margin-left: 9px;
          }
          .percent {
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            margin-left: 14px;
          }
        }
      }
      .pie_chart {
        height: 123px;
        display: flex;
        justify-content: space-evenly;
        .warp {
          width: 107px;
          height: 107px;
          padding-top: 0;
          /deep/.ring {
            width: 100% !important;
            height: 100% !important;
          }
          /deep/.label {
            margin-top: -65px;
            .name {
              font-size: 13px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
            }
          }
        }
      }
    }
  }
  .box6 {
    .cont {
      padding: 23px 22px 0;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: space-between;
      .video_ {
        flex-shrink: 0;
        width: 200px;
        height: 112px;
      }
    }
  }
  .box7 {
    .cont {
      display: grid;
      place-items: center;
      position: relative;
      .fy_out {
        position: absolute;
        top: 3%;
        left: 27%;
        z-index: 99;
        transform: translate(-50%, -50%);
        animation: rotateS infinite 12s linear;
      }
      .fy_in {
        position: absolute;
        top: 44%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
      .wrap {
        position: relative;
        width: 393px;
        height: 205px;
        background: url(~@/assets/csts/bg3.png) no-repeat;
        li {
          position: absolute;
          text-align: left;
          .num {
            font-size: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 24px;
          }
          .tit {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 17px;
          }
          &:nth-child(1) {
            top: 28px;
            left: 24px;
          }
          &:nth-child(2) {
            top: 28px;
            left: 295px;
          }
          &:nth-child(3) {
            bottom: 32px;
            left: 24px;
          }
          &:nth-child(4) {
            bottom: 32px;
            left: 295px;
          }
        }
      }
    }
  }
  .box9 {
    .cont {
      padding: 13px 23px 0;
      .title {
        width: 100%;
        height: 44px;
        line-height: 44px;
        display: flex;
        justify-content: space-between;
        padding: 0 24px;
        span {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
        }
      }
      .detail {
        position: relative;
        width: 404px;
        height: 156px;
        background: url(~@/assets/csts/bg4.png) no-repeat center;
        display: flex;
        justify-content: space-between;
        .center_out {
          position: absolute;
          left: 29%;
          top: -2%;
          transform: translate(-50%, -50%);
          z-index: 99;
          animation: rotateS infinite 12s linear;
        }
        .center_in {
          position: absolute;
          left: 29%;
          top: 2px;
          transform: translate(-50%, -50%);
          z-index: 99;
          animation: rotateN infinite 12s linear;
        }
        .fs {
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          gap: 9px;
          li {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .icon {
              width: 13px;
              height: 16px;
            }
            .lab {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
              padding-left: 6px;
              padding-right: 22px;
            }
            .num {
              font-size: 10px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              span {
                font-size: 18px;
                font-family: PingFangSC, PingFang SC;
                font-weight: normal;
                color: #ffffff;
                line-height: 22px;
              }
            }
          }
        }
        .fq {
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          gap: 9px;
          li {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .icon {
              width: 14px;
              height: 14px;
            }
            .lab {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
              padding-left: 22px;
              padding-right: 6px;
            }
            .num {
              font-size: 10px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              span {
                font-size: 18px;
                font-family: PingFangSC, PingFang SC;
                font-weight: normal;
                color: #ffffff;
                line-height: 22px;
              }
            }
          }
        }
        .zdqy {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          .num {
            font-size: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 24px;
          }
          .lab {
            font-size: 18px;
            font-family: PingFangSC, PingFang SC;
            color: #ffffff;
            line-height: 21px;
            letter-spacing: 1px;
          }
        }
      }
    }
  }
  .box10 {
    .cont {
      padding: 10px 35px 0;
      .desc {
        height: 53px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        li {
          display: flex;
          .icon {
            width: 41px;
            height: 41px;
            margin-right: 11px;
          }
          .info {
            text-align: left;
            .num {
              font-size: 18px;
              font-family: PingFangSC, PingFang SC;
              font-weight: normal;
              color: #ffffff;
              line-height: 22px;
            }
            .lab {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
            }
          }
        }
      }
      .chart {
        height: calc(100% - 53px);
        margin-top: -20px;
      }
    }
  }
  .box12 {
    position: relative;
    .gajq_lz {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .left_bg {
    width: 460px;
    height: 1033px;
    background: url(~@/assets/leader/img/component/left_bg.png) no-repeat center / 100% 100%;
    position: absolute;
    left: 50px;
    top: 25px;
    z-index: 102;
  }
  .right_bg {
    width: 520px;
    height: 1033px;
    background: url(~@/assets/leader/img/component/right_bg.png) no-repeat center / 100% 100%;
    position: absolute;
    right: 50px;
    top: 25px;
    z-index: 102;
  }
  .left {
    width: 538px;
    height: 1080px;
    display: flex;
    justify-content: space-between;
    padding-top: 123px;
    padding-left: 50px;
    position: relative;
    z-index: 1001;
    background: linear-gradient(270deg, rgba(0, 0, 0, 0.1) 0%, #060c10 100%);
  }
  .right {
    width: 538px;
    display: flex;
    justify-content: space-between;
    padding-top: 123px;
    padding-left: 28px;
    position: relative;
    z-index: 1001;
    background: linear-gradient(270deg, #060c10 0%, rgba(0, 0, 0, 0.1) 100%);
  }
  .map_box {
    position: absolute;
    width: 1920px;
    height: 970px;
    top: 100px;
    left: 0;
  }
}
.jgzzBox {
  width: 109px;
  height: 170px;
  display: flex;
  flex-direction: column;
  position: absolute;
  left: 525px;
  bottom: 93px;
  z-index: 999;
  background: url(~@/assets/csts/btnBg11.png) no-repeat center / 100% 100%;
  .jgzzItem {
    width: 103px;
    height: 35px;
    margin: 2px 3px 2px 3px;
    background: url(~@/assets/csts/btn11.png) no-repeat;
    img {
      float: left;
      margin: 7px 0 0 10px;
    }
    span {
      float: left;
      font-size: 17px;
      font-family: PingFangSC, PingFang SC;
      color: #e9fffe;
      line-height: 35px;
      background: linear-gradient(180deg, #ffffff 0%, #2f8af4 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .jgzzActive {
    width: 105px;
    height: 37px;
    margin: 1px 2px 1px 2px;
    background: url(~@/assets/csts/btn12.png) no-repeat;
  }
}
.zdcsBox {
  width: 109px;
  height: 248px;
  display: flex;
  flex-direction: column;
  position: absolute;
  left: 1197px;
  bottom: 93px;
  z-index: 999;
  background: url(~@/assets/csts/btnBg12.png) no-repeat center / 100% 100%;
  .jgzzItem {
    width: 103px;
    height: 35px;
    margin: 2px 3px 2px 3px;
    background: url(~@/assets/csts/btn11.png) no-repeat;
    img {
      float: left;
      margin: 7px 0 0 10px;
    }
    span {
      float: left;
      font-size: 17px;
      font-family: PingFangSC, PingFang SC;
      color: #e9fffe;
      line-height: 35px;
      background: linear-gradient(180deg, #ffffff 0%, #2f8af4 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .jgzzActive {
    width: 105px;
    height: 37px;
    margin: 1px 2px 1px 2px;
    background: url(~@/assets/csts/btn12.png) no-repeat;
  }
}
.left,
.right {
  ::v-deep {
    .el-carousel {
      width: 450px;
      height: 920px;
      .el-carousel__indicators {
        .el-carousel__indicator {
          &.is-active {
            .el-carousel__button {
              height: 4px;
              background-color: #49e6ff;
            }
          }
          .el-carousel__button {
            height: 4px;
            background-color: rgba(255, 255, 255, 0.4);
            border-radius: 2px;
          }
        }
      }
    }
  }
}
.right {
  ::v-deep {
    .el-carousel {
      margin-left: auto;
    }
  }
}
::v-deep .el-carousel--horizontal {
  width: 450px;
  overflow-x: hidden;
}
</style>
