<template>
  <div :class="['invite-dialog', {'invite-dialog-full': isFull}]">
    <div class="option-list">
      <div :class="['option-item']" v-for="item of optionList" :key="item" @click="handleOption(item)">{{ item }}</div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'inviteOptionDialog',
    props: {
      isFull: {
        type: Boolean,
        default: false
      },
      optionList: {
        type: Array,
        default: () => {
          return [
            '通讯录邀请',
            '短信邀请',
          ]
        }
      }
    },
    methods: {
      handleOption(option) {
        if(option === '通讯录邀请') {
          this.$emit('openAddressBook')
        } else if(option === '短信邀请') {
          this.$emit('openMessageBoard')
        } else {
          void(0)
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .invite-dialog {
    min-width: 152px;
    max-height: 101px;
    overflow: hidden;
    padding: 21px 0;
    position: absolute;
    bottom: 50px;
    left: 430px;
    background: url('~@/assets/service/mini-bg2.png') no-repeat center / 100% 100%;
    .option-list {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      .option-item {
        white-space: nowrap;
        cursor: pointer;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #C2DDFC;
        line-height: 36px;
        &-warning {
          color: #D06C63 !important;
        }
      }
    }
    // &-full {

    // }
  }
</style>