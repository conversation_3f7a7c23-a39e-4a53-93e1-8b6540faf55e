<template>
  <div class="ai_waring">
    <div class="title">
      <span>行政区域</span>
      <img @click="closeEmit" class="close_btn" src="@/assets/shzl/map/close_btn.png" alt="" />
    </div>
    <div class="content">
      <div><span>简称：</span><span>第一党支部</span></div>
      <div><span>党组织代码：</span><span>0256398</span></div>
      <div><span>党组织全称：</span><span>中共南京市鼓楼区第一党支部</span></div>
      <div><span>党组织类型：</span><span>支部</span></div>
      <div><span>党组织关系：</span><span>街道</span></div>
      <div><span>人数：</span><span>632人</span></div>
      <div><span>联系电话：</span><span>(025)8989258</span></div>
    </div>
  </div>
</template>

<script>
export default {
  name:'wgyPop',
  methods: {
    closeEmit() {
      this.$emit('closeEmit')
    },
  },
}
</script>

<style lang='less' scoped>
.ai_waring {
  width: 413px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  // border: 1px solid red;
  background: url(~@/assets/shzl/map/ai_popBg.png) no-repeat center / 100% 100%;
  background-size: 100% 100%;
  z-index: 999;
  .title {
    background: url(~@/assets/shzl/map/title_bg.png) no-repeat center / 100% 100%;
    background-size: 100% 100%;
    width: 375px;
    height: 47px;
    margin: 20px auto 0;
    line-height: 47px;
    text-align: left;
    text-indent: 27px;
    font-size: 18px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #ffffff;
    position: relative;
    span {
      background: linear-gradient(180deg, #ffffff 0%, #2aa6ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .close_btn {
      position: absolute;
      top: 11px;
      right: 25px;
      cursor: pointer;
    }
  }
  .content {
    padding: 9px 50px 38px 48px;
    & > div {
      display: flex;
      justify-content: space-between;
      &:not(:last-of-type) {
        margin-bottom: 14px;
      }
      & span:first-of-type {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
      }
      & span:last-of-type {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
      }
    }
  }
}
</style>