<template>
  <div class="ai_waring" style="padding-top:5px">
    <div class="ai_wrapper" v-if="popZdqyInfo">
      <div class="title">
        <span>重点企业</span>
        <img @click="closeEmitai" class="close_btn" src="@/assets/shzl/map/close_btn.png" alt="" />
      </div>
      <div class="content">
        <div><span>企业名称:</span><span>{{ popZdqyInfo.name }}</span></div>
        <div><span>统一信用代码:</span><span>{{ popZdqyInfo.creditCode }}</span></div>
        <div><span>注册地址：</span><span>{{ popZdqyInfo.address }}</span></div>
        <div><span>属地：</span><span>{{ popZdqyInfo.territory }}</span></div>
        <div><span>法人代表：</span><span>{{ popZdqyInfo.legal }}</span></div>
        <div><span>成立时间：</span><span>{{ popZdqyInfo.establishment }}</span></div>
        <div><span>注册资本：</span><span>{{ popZdqyInfo.capital }}</span></div>
        <div class="btn">更多</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    popZdqyInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
    }
  },
  methods: {
    closeEmitai() {
      this.$emit('closeEmitai')
    },
  },
}
</script>

<style lang='less' scoped>
.ai_waring {
  width: 453px;
  // height:491px;
  // position: absolute;
  // left: 50%;
  // top: 50%;
  // transform: translate(-50%, -50%);
  // border: 1px solid red;
  background: url(~@/assets/shzl/map/ai_popBg.png) no-repeat center / 100% 100%;
  background-size: 100% 100%;
  position: relative;

  .title {
    background: url(~@/assets/shzl/map/title_bg.png) no-repeat center / 100% 100%;
    background-size: 100% 100%;
    width: 375px;
    height: 47px;
    margin: 20px auto 0;
    line-height: 47px;
    text-align: left;
    text-indent: 27px;
    font-size: 18px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #ffffff;
    position: relative;

    span {
      background: linear-gradient(180deg, #ffffff 0%, #2aa6ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .close_btn {
      position: absolute;
      top: 11px;
      right: 25px;
      cursor: pointer;
    }
  }

  .content {
    .btn {
      width: 146px;
      height: 38px;
      line-height: 38px;
      background: url(~@/assets/cyjj/btn.png) no-repeat center / 100% 100%;
      color: #fff;
      font-size: 14px;
      text-align: center;
      justify-content: center;
      margin: 22px 104px 0;
    }

    .desc {
      color: #fff;
      text-align: left;
    }

    padding: 20px 50px 46px 48px;

    // border: 1px solid red;
    &>div {
      display: flex;
      justify-content: space-between;

      &:not(:last-of-type) {
        margin-bottom: 14px;
      }

      & span:first-of-type {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
      }

      & span:last-of-type {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
      }
    }

    .pic {
      margin: 18px auto 0;
      width: 316px;
      height: 169px;
    }

    .btns {
      margin: 15px auto 0;
      display: flex;
      justify-content: space-between;

      &>div {
        width: 150px;
        height: 33px;
        // border: 1px solid red;
        background: url(~@/assets/shzl/map/btn_bg.png) no-repeat center / 100% 100%;
        background-size: 100% 100%;
        line-height: 33px;
        text-align: center;
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
      }
    }
  }
}
</style>