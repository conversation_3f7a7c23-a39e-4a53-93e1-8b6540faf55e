<template>
  <div>
    <div class="event_detail">
      <div class="title">
        <span>事件详情</span>
      </div>
      <div class="close" @click="close"></div>
      <div class="sub_tit">
        <span :class="{ active: activeBtn1 === i }" v-for="(it, i) of btns1" :key="i" @click="activeBtn1 = i">{{
          it
        }}</span>
      </div>
      <div class="cont1">
        <div class="info info1" v-if="activeBtn1 === 0">
          <ul>
            <li>
              <div class="left">
                <div class="lab">姓名：</div>
                <div class="val">小明（15163746723）</div>
              </div>
              <div class="right">
                <div class="lab">上报时间：</div>
                <div class="val">2022-05-05 08:38:22</div>
              </div>
            </li>
            <li>
              <div class="left">
                <div class="lab">事件类型：</div>
                <div class="val">城市管理</div>
              </div>
              <div class="right">
                <div class="lab">归属地：</div>
                <div class="val">朱中村网格一</div>
              </div>
            </li>
            <li>
              <div class="left">
                <div class="lab">事件编号：</div>
                <div class="val">DH6101002205050065902</div>
              </div>
              <div class="right">
                <div class="lab">事件地址：</div>
                <div class="val">中国江苏省南京市鼓楼区华侨路街道</div>
              </div>
            </li>
            <li>
              <div class="left">
                <div class="lab">事件来源：</div>
                <div class="val">12345</div>
              </div>
              <div class="right">
                <div class="lab">事件内容：</div>
                <div class="val"></div>
              </div>
            </li>
            <li>
              <div class="left">
                <div class="lab">事件状态：</div>
                <div class="val">待区指挥中心签收</div>
              </div>
              <div class="right">
                <div class="lab">信息公开：</div>
                <div class="val">是</div>
              </div>
            </li>
            <li>
              <div class="left">
                <div class="lab">紧急程度：</div>
                <div class="val">紧急</div>
              </div>
              <div class="right">
                <div class="lab">事发时间：</div>
                <div class="val">2022-05-05 08:38:22</div>
              </div>
            </li>
            <li>
              <div class="lab">附件：</div>
              <div class="pics">
                <el-image class="img_view" v-for="(item, index) in picArr" :key="index" :src="item"
                  :preview-src-list="srcList">
                </el-image>
                <!-- <img src="@/assets/csts/photo1.png" alt="" /> -->
                <!-- <img src="@/assets/csts/photo1.png" alt="" /> -->
              </div>
            </li>
          </ul>
        </div>
        <div class="info info2" v-if="activeBtn1 === 1">
          <div class="theader">
            <span v-for="(it, i) of label2" :key="i" :style="{ width: `${it.width}px` }">{{
              it.lab
            }}</span>
          </div>
          <div class="tbody">
            <span v-for="(it, i) of info2" :key="i" :style="{ width: `${label2[i].width}px` }">{{
              it
            }}</span>
          </div>
        </div>
        <div class="info info3" v-if="activeBtn1 === 2">
          <div class="theader">
            <span v-for="(it, i) of label3" :key="i" :style="{ width: `${it.width}px` }">{{
              it.lab
            }}</span>
          </div>
          <div class="tbody">
            <span v-for="(it, i) of info3" :key="i" :style="{ width: `${label3[i].width}px` }">{{
              it
            }}</span>
          </div>
        </div>
      </div>
      <div class="sub_tit">
        <span class="active">处理流程</span>
        <p>总处理时长为0时3分16秒</p>
      </div>
      <div class="cont2" :class="{ cont2_1: activeBtn1 === 0 }">
        <div class="info">
          <ul class="time_line">
            <li class="time_line_item">
              <div class="basic_info">
                <div class="time">08:10</div>
                <div class="name">小明(321281130019001)</div>
                <div class="grid">朱中村网格一</div>
                <div class="state">办结：2022-05-05 06:02:34</div>
              </div>
              <div class="detail_info">
                <ul>
                  <li>
                    <div class="lab">操作动作：</div>
                    <div class="val">上报社区指挥中心</div>
                  </li>
                  <li>
                    <div class="lab">签收时间：</div>
                    <div class="val">2022-05-05 06:02:34</div>
                  </li>
                  <li>
                    <div class="lab">计划完成时间：</div>
                    <div class="val">2022-05-05 14:00:34</div>
                  </li>
                  <li>
                    <div class="lab">附件：</div>
                    <div class="val">IMG2022050101.jpg</div>
                  </li>
                  <li>
                    <div class="lab">处理意见：</div>
                    <div class="val">上报社区指挥中心上报社区指挥中心上报社区指挥中心</div>
                  </li>
                </ul>
              </div>
            </li>
            <li class="time_line_item">
              <div class="basic_info">
                <div class="time">08:56</div>
                <div class="name">朱中村管理员(321281130019admin)</div>
                <div class="grid"></div>
                <div class="state"></div>
              </div>
              <div class="detail_info">
                <ul>
                  <li>
                    <div class="lab">操作动作：</div>
                    <div class="val">签收</div>
                  </li>
                  <li>
                    <div class="lab">签收时间：</div>
                    <div class="val">2022-05-05 06:02:34</div>
                  </li>
                  <li>
                    <div class="lab">计划完成时间：</div>
                    <div class="val">2022-05-05 14:00:34</div>
                  </li>
                  <li>
                    <div class="lab">附件：</div>
                    <div class="val">IMG2022050101.jpg</div>
                  </li>
                  <li>
                    <div class="lab">处理意见：</div>
                    <div class="val">上报社区指挥中心上报社区指挥中心上报社区指挥中心</div>
                  </li>
                </ul>
              </div>
            </li>
          </ul>
        </div>
      </div>
      <!-- <div class="btn_contain">
        <div class="btns">
          <span>确定</span>
          <span>定位</span>
        </div>
      </div> -->
    </div>
    <div>
      <transition name="fade">
        <div class="bg-header"></div>
      </transition>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EventDetail',
  data() {
    return {
      btns1: ['基本信息', '工单备注历史', '下一步处理人信息'],
      activeBtn1: 0,
      label2: [
        {
          lab: '创建人',
          width: 83,
        },
        {
          lab: '创建时间',
          width: 133,
        },
        {
          lab: '备注类型',
          width: 163,
        },
        {
          lab: '备注内容',
          width: 528,
        },
      ],
      info2: [
        '张三丰',
        '2022-05-23',
        '社区网格上报',
        '',
      ],
      label3: [
        {
          lab: '姓名',
          width: 273,
        },
        {
          lab: 'ID',
          width: 253,
        },
        {
          lab: '岗位',
          width: 163,
        },
        {
          lab: '联系电话',
          width: 319,
        },
      ],
      info3: ['朱中村管理员', '32128002202admin', '社区指挥中心', '15172638464'],
      picArr: [require('@/assets/csts/photo1.png'), require('@/assets/shzl/map/ai_bg.png')],
      url: require('@/assets/csts/photo1.png'),
      srcList: [require('@/assets/csts/photo1.png'), require('@/assets/shzl/map/ai_bg.png')],
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
  },
}
</script>

<style lang="less" scoped>
.bg-header {
  position: absolute;
  width: 100%;
  height: 1080px;
  background: rgba(2, 14, 50, 0.75);
  z-index: 999999;
  top: 0;
  left: 0;
}

.event_detail {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 1111px;
  height: 900px;
  padding: 0 62px;
  background: rgba(0, 23, 59, 1);
  box-shadow: 0px 0px 43px 0px rgba(11, 54, 138, 0.35), 0px 3px 16px 0px rgba(0, 0, 0, 0.7),
    inset 0px 0px 16px 0px rgba(17, 146, 214, 0.35);
  border-radius: 11px;
  border: 1px solid #015c8c;
  z-index: 99999999;

  .title {
    margin: 0 auto;
    width: 634px;
    height: 74px;
    background: url(~@/assets/csts/title1.png) no-repeat;
    text-align: center;
    margin-bottom: 37px;

    span {
      display: inline-block;
      line-height: 74px;
      font-size: 40px;
      font-family: PingFangSC, PingFang SC;
      color: #ffffff;
      background: linear-gradient(180deg, #ffffff 0%, #2aebff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .close {
    position: absolute;
    width: 44px;
    height: 44px;
    top: 16px;
    right: 28px;
    background: url(~@/assets/csts/close1.png) no-repeat;
    cursor: pointer;
  }

  .sub_tit {
    position: relative;
    width: 100%;
    height: 50px;
    line-height: 50px;
    padding-left: 64px;
    background: url(~@/assets/csts/title2.png) no-repeat;
    display: flex;
    align-items: center;
    gap: 15px;

    span {
      font-size: 20px;
      font-family: PingFangSC, PingFang SC;
      color: rgba(255, 255, 255, 0.33);
      line-height: 23px;
      letter-spacing: 2px;
      cursor: pointer;

      &.active {
        color: #fff;
      }
    }

    p {
      position: absolute;
      right: 80px;
      font-size: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #ffffff;
    }
  }

  .cont1 {
    width: 100%;
    min-height: 114px;
    max-height: 288px;
    overflow-y: scroll;
    margin: 20px 0 13px;

    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 6px;
      background: transparent;
      // border: 1px solid #999;
      /*高宽分别对应横竖滚动条的尺寸*/
      // height: 1px;
    }

    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 2px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: rgb(45, 124, 228);
      height: 100px;
    }

    .info {
      width: 100%;
      height: 100%;
    }

    .info1 {
      padding: 0 6px 0 9px;

      ul {
        li {
          display: flex;
          gap: 101px;
          width: 100%;
          height: 48px;
          line-height: 48px;
          padding: 0 102px 0 55px;

          &:nth-child(odd) {
            background: url(~@/assets/csts/bg7.png) no-repeat;
          }

          &:last-child {
            height: 108px;
            background: url(~@/assets/csts/bg8.png) no-repeat;

            .lab {
              font-size: 16px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              color: #9de2ff;
              text-align: left;
            }
          }

          .left,
          .right {
            display: flex;
            justify-content: space-between;
            width: 357px;

            .lab {
              min-width: 101px;
              font-size: 16px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              color: #9de2ff;
              text-align: left;
            }

            .val {
              font-size: 16px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              color: #ffffff;
              text-align: right;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }

          .pics {
            height: 81px;
            padding-top: 16px;
            margin-left: -80px;

            .img_view {
              width: 126px;
              height: 81px;
              margin-right: 25px;
            }
          }
        }
      }
    }

    .info2,
    .info3 {
      padding: 0 38px 0;

      .theader {
        display: flex;
        height: 46px;
        line-height: 46px;
        border: 1px solid #2968c7;

        span {
          display: inline-block;
          height: 100%;
          border-right: 1px solid #2968c7;
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #00eaff;

          &:last-child {
            border-right: none;
          }
        }
      }

      .tbody {
        display: flex;
        height: 68px;
        line-height: 68px;

        span {
          display: inline-block;
          height: 100%;
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
        }
      }
    }
  }

  .cont2 {
    width: 100%;
    height: 439px;
    padding-top: 9px;

    &.cont2_1 {
      height: 265px;
    }

    .info {
      width: 100%;
      height: 100%;
      overflow-y: scroll;

      &::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 6px;
        background: transparent;
        // border: 1px solid #999;
        /*高宽分别对应横竖滚动条的尺寸*/
        // height: 1px;
      }

      &::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius: 2px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        background: rgb(45, 124, 228);
        height: 100px;
      }

      .time_line {
        .time_line_item {
          position: relative;
          width: 100%;
          height: auto;
          padding: 14px 7px 0 19px;

          &::before {
            position: absolute;
            content: '';
            width: 8px;
            height: 8px;
            border-radius: 50%;
            left: 4.5px;
            top: 26px;
            background-color: #add3ff;
          }

          &::after {
            position: absolute;
            content: '';
            width: 1px;
            height: calc(100% - 26px);
            left: 8px;
            top: 26px;
            background-color: #add3ff;
          }

          .basic_info {
            display: flex;
            height: 40px;
            align-items: center;
            gap: 17px;

            .time {
              width: 42px;
              height: 20px;
              background: linear-gradient(180deg, #ffffff 0%, #73b3ff 100%);
              box-shadow: 0px 2px 4px 0px rgba(1, 19, 56, 0.5);
              border-radius: 10px;
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #000000;
              line-height: 20px;
            }

            .name {
              font-size: 16px;
              font-family: PingFangSC-Semibold, PingFang SC;
              font-weight: 600;
              color: #9de2ff;
              line-height: 16px;
            }

            .grid,
            .state {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
            }
          }

          .detail_info {
            width: 100%;
            height: auto;
            padding: 17px 108px 16px 45px;
            background: url(~@/assets/csts/bg6.png);

            ul {
              display: flex;
              flex-wrap: wrap;
              justify-content: space-between;
              gap: 10px;

              li {
                display: flex;
                justify-content: space-between;
                width: 357px;

                .lab {
                  min-width: 110px;
                  text-align: left;
                  font-size: 16px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: 500;
                  color: #9de2ff;
                }

                .val {
                  text-align: right;
                  font-size: 16px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: 500;
                  color: #ffffff;
                  line-height: 16px;
                  line-height: 27px;
                }
              }
            }
          }
        }
      }
    }
  }

  .btn_contain {
    width: 100%;
    height: 93px;
    display: grid;
    place-items: center;

    .btns {
      display: flex;
      gap: 66px;

      span {
        width: 170px;
        height: 33px;
        line-height: 33px;
        font-size: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        background: url(~@/assets/csts/btn_bg.png) no-repeat;
        cursor: pointer;
      }
    }
  }
}
</style>
