<template>
  <div class="leader_box">
    <div class="leader_sjts">
      <div class="left">
        <div class="left1">
          <BlockBox
            title="未办结工单"
            subtitle="Work order not completed"
            class="box box1"
            :isListBtns="false"
            :showMore="true"
            :blockHeight="229"
            @handleShowMore="showMoreFn(1)"
          >
            <WorkOrderNotCompl :chartData1="notCompl1" :chartData2="notCompl2" />
          </BlockBox>
          <BlockBox
            title="实时事件"
            subtitle="Real-time event"
            class="box"
            :isListBtns="false"
            :showMore="true"
            :blockHeight="605"
            @handleShowMore="showMoreFn(2)"
          >
            <RealTimeEventQgd
              contentHeight="558px"
              :data="realTimeEventList"
              @clickRealtimeEvent="clickRealtimeEventQgd"
            />
          </BlockBox>
          <!-- <BlockBox
            title="事件总览"
            subtitle="Event summary"
            class="box box2"
            :isListBtns="false"
            :blockHeight="323"
            :showMore="true"
            @handleShowMore="showMoreFn2"
          >
            <div class="cont">
              <div class="sjzl_box">
                <div class="sjzl_box_top">
                  <ul>
                    <li v-for="(item, index) in sjzlTopData" :key="index">
                      <countTo
                        ref="countTo"
                        :startVal="$countTo.startVal"
                        :decimals="$countTo.decimals(item.num)"
                        :endVal="item.num"
                        :duration="$countTo.duration"
                      />
                      <img src="@/assets/shzl/sjzl_top.png" alt />
                      <span>{{ item.tit }}</span>
                    </li>
                  </ul>
                </div>
                <div class="sjzl_box_bootom">
                  <div class="now_year">本年</div>
                  <img class="center" src="@/assets/shzl/sjzl_middle.png" alt />
                  <img class="center_out" src="@/assets/shzl/sjzl_out.png" alt />
                  <img class="center_in" src="@/assets/shzl/sjzl_in.png" alt />
                  <ul class="sjzl_box_bootom_l">
                    <li v-for="(item, index) in sjzlBootomL" :key="index">
                      <countTo
                        ref="countTo"
                        :startVal="$countTo.startVal"
                        :decimals="$countTo.decimals(item.num)"
                        :endVal="item.num"
                        :duration="$countTo.duration"
                      />
                      <span>{{ item.tit }}</span>
                    </li>
                  </ul>
                  <ul class="sjzl_box_bootom_r">
                    <li v-for="(item, index) in sjzlBootomR" :key="index">
                      <span>{{ item.tit }}</span>
                      <div>
                        <countTo
                          ref="countTo"
                          :startVal="$countTo.startVal"
                          :decimals="$countTo.decimals(item.num)"
                          :endVal="item.num"
                          :duration="$countTo.duration"
                        />%
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </BlockBox>
          <BlockBox
            title="实时事件"
            subtitle="Real-time event"
            class="box box3"
            :isListBtns="false"
            :showMore="true"
            :blockHeight="271"
            @handleShowMore="showMoreFn3"
          >
            <div class="cont">
              <swiper-table
                :data="sewageDataList1"
                :titles="['标题', '来源', '时间']"
                :widths="['200px', '75px', '152px']"
                content-height="260px"
              />
            </div>
          </BlockBox>-->
        </div>
      </div>
      <div class="right">
        <div class="right1">
          <BlockBox
            title="物联设备"
            subtitle="Iot equipment"
            class="box box4"
            :isListBtns="false"
            :blockHeight="285"
            :textArr="[]"
          >
            <div class="cont">
              <div class="box_top">
                <div class="box_top_item" v-for="(item, index) in box4TopData" :key="index">
                  <img src="@/assets/tsgz/wlsb1.png" alt />
                  <div>
                    <p>{{ item.num }}</p>
                    <p>{{ item.name }}</p>
                  </div>
                </div>
              </div>
              <div class="box_bottom">
                <PieChart3D type="2" :data="box4BottomData.data" :options="box4BottomData.options" />
              </div>
            </div>
          </BlockBox>
          <!-- <BlockBox
            title="预警分析"
            subtitle="Fire monitoring"
            class="box box5"
            :isListBtns="false"
            :blockHeight="308"
          >
            <div class="cont">
              <div class="box5_top">
                <div class="box5_top_item">
                  <img src="@/assets/tsgz/hqjc1.png" alt />
                  <div>
                    <p>总数</p>
                    <img src="@/assets/tsgz/hqjc_line.png" alt />
                    <p>
                      6
                      <span>个</span>
                    </p>
                  </div>
                </div>
                <div class="box5_top_item">
                  <img src="@/assets/tsgz/hqjc2.png" alt />
                  <div>
                    <p>已处置</p>
                    <img src="@/assets/tsgz/hqjc_line.png" alt />
                    <p>
                      5
                      <span>个</span>
                    </p>
                  </div>
                </div>
                <div class="box5_top_item">
                  <img src="@/assets/tsgz/hqjc3.png" alt />
                  <div>
                    <p>待处置</p>
                    <img src="@/assets/tsgz/hqjc_line.png" alt />
                    <p>
                      1
                      <span>个</span>
                    </p>
                  </div>
                </div>
              </div>
              <div class="box5_bottom">
                <TrendLineChart
                  :options="box5Options"
                  :data="box5Data"
                  :init-option="box5initOptions"
                />
              </div>
            </div>
          </BlockBox>-->
          <BlockBox
            title="视频监控"
            subtitle="Video surveillance"
            class="box box6"
            :showMore="true"
            :isListBtns="false"
            :blockHeight="296"
            @handleShowMore="showMoreFn(5)"
          >
            <div class="cont">
              <VideoSurveillance :cameraId="cameraId1" v-if="cameraId1" class="video_" />
              <VideoSurveillance :cameraId="cameraId2" v-if="cameraId2" class="video_" />
              <VideoSurveillance :cameraId="cameraId3" v-if="cameraId3" class="video_" />
              <VideoSurveillance :cameraId="cameraId4" v-if="cameraId4" class="video_" />
            </div>
          </BlockBox>
          <BlockBox
            title="实时预警"
            subtitle="Real time warning"
            class="box box1"
            :isListBtns="false"
            :blockHeight="295"
            :showMore="true"
            @handleShowMore="showMoreFn(6)"
          >
            <div class="cont">
              <swiper class="swiper" :options="swiperOptionZy" ref="myBotSwiper4">
                <swiper-slide v-for="item in yjData" :key="item">
                  <div class="line">
                    <div>
                      <div class="warningItem_">
                        <img src="@/assets/tsgz/yj_red.png" alt />
                        <div class="title red">{{ item.warningItem }}</div>
                      </div>
                      <div class="level">
                        {{ item.warningLevel }}
                        <!-- <span class="red">红</span> -->
                      </div>
                      <div class="time">{{ item.createTime }}</div>
                    </div>
                    <div>{{ item.title }}</div>
                  </div>
                  <!-- <div class="line">
                    <div>
                      <img src="@/assets/tsgz/yj_red.png" alt />
                      <div class="title blue">生态环境-空气质量预警</div>
                      <div class="level">
                        预警等级:
                        <span class="blue">蓝</span>
                      </div>
                      <div class="time">2022.03.04</div>
                    </div>
                    <div>根据###监测站点数值显示,##市##区空气AQI指数为134，严重超标，请注意</div>
                  </div>-->
                </swiper-slide>
              </swiper>
            </div>
          </BlockBox>

          <!-- <BlockBox
            title="生态监管"
            subtitle="Ecological supervisi"
            class="box box6"
            :isListBtns="true"
            :blockHeight="287"
            :textArr="['实时监测', '污染物']"
          >
            <div class="cont">
              <div class="box6_top">
                <img src="@/assets/tsgz/stjg.png" alt />
                <div>
                  <div class="box6_top1">
                    <span>101</span>
                    <span>轻度污染</span>
                    <span>PM10</span>
                  </div>
                  <div class="box6_top2">
                    <div>
                      <img src="@/assets/tsgz/stjg1.png" alt />
                      <span>今日空气AQI</span>
                    </div>
                    <div>
                      <img src="@/assets/tsgz/stjg2.png" alt />
                      <span>今日质量等级</span>
                    </div>
                    <div>
                      <img src="@/assets/tsgz/stjg3.png" alt />
                      <span>首要污染物</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="box6_bottom">
                <div class="box6_bottom_item" v-for="(item, index) in stjgData">
                  <div>
                    <span>{{ item.name }}</span>
                    <span>{{ item.val }}</span>
                  </div>
                  <div class="line">
                    <div
                      class="line_in"
                      :style="{ background: item.bgColor, width: item.percent + '%' }"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </BlockBox>-->
        </div>
      </div>
    </div>
    <div class="map_box">
      <LeafletMap ref="leafletMap" @poiClick="showDialog" />
    </div>
    <!-- <leaderMiddle /> -->

    <LeaderFooter :btns="[]" :activaIdx="activaIdx" />

    <EventDetail v-if="isShowEventDetail" @close="isShowEventDetail = false" />
    <CountryPart :isShow="isShowCountryPart" @close="closeCountryPart" @check="checkTree" />
    <!-- <szyyPop v-if="isXlgcShow1" @closeEmit="isXlgcShow1 = false" /> -->
    <wgyPop v-if="isWgllShow" @closeEmit="isWgllShow = false" />
    <dzzPop v-if="isdzzShow" @closeEmit="isdzzShow = false" />
    <xzqyPop v-if="isxzqyShow" @closeEmit="isxzqyShow = false" />
    <zwzxPop v-if="iszwzxShow" @closeEmit="iszwzxShow = false" />
    <zhzxPop v-if="iszhzxShow" @closeEmit="iszhzxShow = false" />
    <csglgdPop v-if="iscsglgdShow" @closeEmit="iscsglgdShow = false" />
    <shaqPop v-if="isshaqShow" @closeEmit="isshaqShow = false" />
    <sthbPop v-if="issthbShow" @closeEmit="issthbShow = false" />
    <whlyPop v-if="iswhlyShow" @closeEmit="iswhlyShow = false" />
    <yjfkPop v-if="isyjfkShow" @closeEmit="isyjfkShow = false" />
    <ggjtPop v-if="isggjtShow" @closeEmit="isggjtShow = false" />
    <zdqyPop v-if="iszdqyShow" @closeEmit="iszdqyShow = false" />
    <xxPop v-if="isxxShow" @closeEmit="isxxShow = false" />
    <wbPop v-if="iswbShow" @closeEmit="iswbShow = false" />
    <ylcsPop v-if="isylcsShow" @closeEmit="isylcsShow = false" />
    <hczPop v-if="ishczShow" @closeEmit="ishczShow = false" />
    <dyyPop v-if="isdyyShow" @closeEmit="isdyyShow = false" />
    <djylPop v-if="djylShow" @closeEmit="djylShow = false" />
    <bjxqPop v-if="bjxqShow" @closeEmit="bjxqShow = false" />
    <Area ref="area" v-show="showArea" :pop-area-info="popAreaInfo"></Area>

    <!-- 侧边菜单 -->
    <ZhddAside :list="asideList" @marker="marker" />
    <!-- 侧边二级菜单 -->
    <SurroundingResourcesMap
      v-model="showYjzyList"
      :list="surList"
      @surEmit="surEmit"
      left="736px"
      bottom="822px"
    />

    <!-- 烟感监测 -->
    <ygjcPop ref="ygjc1" :info="ygjcInfo" v-show="showYgjc1" @closeEmitai="showYgjc1 = false"></ygjcPop>
    <!-- 空气质量 -->
    <airJcPop ref="ygjc2" v-show="showYgjc2" @closeEmitai="showYgjc2 = false"></airJcPop>
    <!-- 燃气监控 -->
    <rqJcPop ref="ygjc3" v-show="showYgjc3" @closeEmitai="showYgjc3 = false"></rqJcPop>
    <!-- 视频监控 -->
    <spjkPop
      v-if="spjkShow"
      @closeEmit="spjkShow = false"
      @operate="taskDistributionShow = true"
      :leftTreeData="spjkLeftTree"
    ></spjkPop>
    <!-- 任务派发 -->
    <TaskDistribution v-model="taskDistributionShow" />
    <!-- 事件总览更多 -->
    <sjzlPop v-if="sjzlPopShow" @closeEmitai="sjzlPopShow = false"></sjzlPop>
    <!-- 实时事件更多 -->
    <SocialSmallPlace v-model="detailDialogShow" />
    <!-- 事件信息 -->
    <EventInfo
      v-model="eventDialogShow"
      :list="eventInfo"
      :btns="['事件详情']"
      @handle="eventInfoBtnClick"
    />
    <!-- 事件信息详情 -->
    <EventDetailQgd
      v-model="eventDetailShow"
      :detail="eventDetailObj"
      :imgList="eventImgs"
      :processList="processList"
      :is-done="isDone"
      @handle="eventHandle"
    />
    <!-- 实时事件 -->
    <RealTimeEventDialogQgd v-model="realTimeEventDialogShow" @operate="realTimeEventOperateQgd" />
    <!-- 未办结、已办结  -->
    <EventStatisticQgd v-model="eventStatisticShow" @operate="eventStatisticOperateQgd" />
    <!-- 单个视频监控弹窗 -->
    <VideoSingle v-model="videoSingleShow" :video-url="videoSingleUrl" :cameraCode="cameraCode" />

    <!-- 视频会商 -->
    <sphsPop v-if="isXlgcShow1" @closeEmit="isXlgcShow1 = false" :initCheckedPeo="initCheckedPeo">
      <template v-slot:title1>指挥调度</template>
      <template v-slot:title2>会议接入</template>
      <template v-slot:title3>重置</template>
      <template v-slot:title4>加入会议</template>
    </sphsPop>

    <!-- 实时告警 -->
    <zlsssjPop
      v-if="jxcsssgjPopShows"
      ref="zlsssjPop"
      @closeEmitai="jxcsssgjPopShows = false"
      @fatheroperate="getOperateSssj"
    ></zlsssjPop>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import testData from '@/assets/json/cyjjPoint'
import BlockBox from '@/components/leader/common/blockBox.vue'
import HeaderMain from '@/components/leader/common/headerMains'
import effectRankBarSwiper from './components/effectRankBar-swiper.vue'
import particle from './components/particle.vue'
import EventDetail from './components/EventDetail.vue'
import CountryPart from './components/CountryPart.vue'
import leaderMiddle from '@/components/leader/common/leaderMiddle.vue'
import gdMap from '@/components/leader/gdMap/gdMap.vue'
import cesiumMap from '@/components/leader/cesiumMap/Cesium3dMap.vue'
import cssjPoint from '@/assets/json/csts/cssjPoint.json'
import spjkPoint from '@/assets/json/csts/spjkPoint.json'
import csbjPoint from '@/assets/json/csts/csbjPoint.json'
import ryllPoint from '@/assets/json/csts/ryllPoint.json'
import xzqhPoint from '@/assets/json/csts/xzqhPoint.json'
import dzzPoint from '@/assets/json/csts/dzzPoint.json'
import zwzxPoint from '@/assets/json/csts/zwzxPoint.json'
import zzzxPoint from '@/assets/json/csts/zzzxPoint.json'
import zdqyPoint from '@/assets/json/csts/zdqyPoint.json'
import xxPoint from '@/assets/json/csts/xxPoint.json'
import wbPoint from '@/assets/json/csts/wbPoint.json'
import ylcsPoint from '@/assets/json/csts/ylcsPoint.json'
import hczPoint from '@/assets/json/csts/hczPoint.json'
import dyyPoint from '@/assets/json/csts/dyyPoint.json'
import LeaderFooter from '@/components/leader/common/leaderFooter.vue'
import myRobot from '@/components/robot/myRobot.vue'
import NumScroll from '@/components/leader/num_scrolls'
import Area from '@/components/leader/leader_csts/area.vue'
import { LandUse, FullFactorGridEvents } from './components/hszl'
import SwiperTable from '@/components/tsgz/SwiperTable.vue'
import LivePlayer from '@liveqing/liveplayer'
import LeafletMap from '@/components/map/LeafletMap2.vue'

import zlsssjPop from '@/components/jcyj/zlsssjPop.vue'

import { ZhddAside, VideoSurveillance, WorkOrderNotCompl } from './components/zhdd'
import { SurroundingResourcesMap, Tools } from './components/map'
// import {ygjcPop,airJcPop,rqJcPop} from '@/components/tsgz'

import ygjcPop from '@/components/tsgz/ygjcPop.vue'
import airJcPop from '@/components/tsgz/airJcPop.vue'
import rqJcPop from '@/components/tsgz/rqJcPop.vue'
import TaskDistribution from '@/views/leader/components/zhdd/dialog/TaskDistribution.vue'
import sjzlPop from './components/tsgz/sjzlPop.vue'
import SocialSmallPlace from '@/views/leader/components/tsgz/dialog/SocialSmallPlace.vue'

import {
  //实时事件区工单
  RealTimeEventQgd,
} from '@/views/leader/components/zhdd'

import {
  EventInfo,
  EventDetailQgd,
  RealTimeEventDialogQgd,
  EventStatisticQgd,
  VideoSingle,
} from '@/views/leader/components/zhdd/dialog'

import {
  getSmokeDeviceMakers,
  getCameraMakers,
  getCameraMakersNew,
  getRightCamera,
  getCameraXq,
  getQgdList,
  getQgdDetailJk,
  getQgdWbjTj,
} from '@/api/hs/hs.api.js'
import { getSsyj, wglListJk, queryOrgStreetTree, queryByPageSsyj, alarmToOrder } from '@/api/hs/hs.tsgz.js'

import 'swiper/css/swiper.css'
export default {
  name: 'Hszl',
  components: {
    BlockBox,
    HeaderMain,
    effectRankBarSwiper,
    particle,
    leaderMiddle,
    gdMap,
    cesiumMap,
    LeaderFooter,
    EventDetail,
    CountryPart,
    myRobot,
    Area,
    NumScroll,
    LandUse,
    FullFactorGridEvents,
    SwiperTable,
    LivePlayer,
    LeafletMap,
    ZhddAside,
    VideoSurveillance,
    SurroundingResourcesMap,
    ygjcPop,
    airJcPop,
    rqJcPop,
    TaskDistribution,
    sjzlPop,
    SocialSmallPlace,
    RealTimeEventQgd,
    EventInfo,
    EventDetailQgd,
    RealTimeEventDialogQgd,
    EventStatisticQgd,
    VideoSingle,
    WorkOrderNotCompl,
    zlsssjPop
  },
  data () {
    return {
      jxcsssgjPopShows: false,
      ygjcInfo: {},
      man_active: require('@/assets/hszl/man_active.png'),
      man_normal: require('@/assets/hszl/man_normal.png'),
      woman_normal: require('@/assets/hszl/woman_normal.png'),
      woman_active: require('@/assets/hszl/woman_active.png'),
      jgzzShow: false,
      zdcsShow: false,
      djylShow: false,
      bjxqShow: false,
      showArea: false,
      popAreaInfo: {},
      areaPointList: [],
      jgzzActive: 0,
      zdcsActive: 0,
      csglTitleBtns: ['事件', '部件'],
      shbzTitleBtns: ['社会保险', '住房保险'],
      showCesiumMap: false,
      videoUrl: require('@/assets/leader/video/bg1.mp4'),
      // videoUrl: require('@/assets/leader/video/bg.mp4'),
      ybssData: [
        {
          num: 100,
          tit: '党工委数(个)',
          img: require('@/assets/shzl/dzz1.png'),
        },
        {
          num: 18072,
          tit: '基层党委数(人)',
          img: require('@/assets/shzl/dzz2.png'),
        },
        {
          num: 624,
          tit: '党总支数(个)',
          img: require('@/assets/shzl/dzz3.png'),
        },
        {
          num: 142,
          tit: '党支部数(个)',
          img: require('@/assets/shzl/dzz4.png'),
        },
      ],
      data1: [
        {
          num: 55.08,
          tit: 'GDP地区生产总值',
          img: require('@/assets/csts/icon.png'),
          url: require('@/assets/hszl/icon1.png'),
          name: '亿元',
        },
        {
          num: 2.34,
          tit: '一般公共预算收入',
          img: require('@/assets/csts/icon.png'),
          url: require('@/assets/hszl/icon2.png'),
          name: '亿元',
        },
        {
          num: 11.09,
          tit: '全社会固定资产投资',
          img: require('@/assets/csts/icon.png'),
          url: require('@/assets/hszl/icon3.png'),
          name: '亿元',
        },
        {
          num: 40.62,
          tit: '规上工业生产总值',
          img: require('@/assets/csts/icon.png'),
          url: require('@/assets/hszl/icon4.png'),
          name: '亿元',
        },
        {
          num: 18.26,
          tit: '社会消费品零售总额',
          img: require('@/assets/csts/icon.png'),
          url: require('@/assets/hszl/icon5.png'),
          name: '元',
        },
        {
          num: 6.64,
          tit: '规上服务物业营业收入',
          img: require('@/assets/csts/icon.png'),
          url: require('@/assets/hszl/icon6.png'),
          name: '元',
        },
      ],

      data4: [
        {
          icon: require('@/assets/hszl/icon8.png'),
          label: '党委',
          count: 11,
        },
        {
          icon: require('@/assets/hszl/icon9.png'),
          label: '党员',
          count: 3810,
        },
        {
          icon: require('@/assets/hszl/icon10.png'),
          label: '党总支',
          count: 17,
        },
        {
          icon: require('@/assets/hszl/icon11.png'),
          label: '直属党组织',
          count: 13,
        },
        {
          icon: require('@/assets/hszl/icon12.png'),
          label: '党支部',
          count: 149,
        },
        {
          icon: require('@/assets/hszl/icon13.png'),
          label: '二级党支部',
          count: 92,
        },
      ],
      data5_1: [
        {
          img: require('@/assets/csts/icon29.png'),
          icon: require('@/assets/csts/icon31.png'),
          tit: '常驻人口',
          num: 64,
          pct: 82.7,
          color: '#00C6FA',
          dotNum: 50,
        },
        {
          img: require('@/assets/csts/icon30.png'),
          icon: require('@/assets/csts/icon32.png'),
          tit: '流动人口',
          num: 32,
          pct: 41.3,
          color: '#3ADCD6',
          dotNum: 50,
        },
      ],
      data5_2: [
        {
          data: {
            label: '人口就业率',
            value: 52,
          },
          options: {
            // 圆环颜色
            color: '#D7BC2F',
            // 圆环背景颜色
            bgColor: 'rgba(0, 52, 100, 1)',
            // 是否顺时针 旋转
            clockwise: true,
            // 圆环端点的形状 ['round', 'square', 'butt']
            strokeLinecap: 'round',
            // 圆环大小 单位是 px
            size: 120,
            // 圆环的宽度 相对单位
            barWidth: 80,
            // 是否开启 轮播动画
            showMotion: true,
            // 动画的 间隔时间
            times: 10000,
            // 圆环类型
            type: 2,
          },
        },
        {
          data: {
            label: '人口老龄化',
            value: 54,
          },
          options: {
            // 圆环颜色
            color: '#28C7FF',
            // 圆环背景颜色
            bgColor: 'rgba(0, 52, 100, 1)',
            // 是否顺时针 旋转
            clockwise: true,
            // 圆环端点的形状 ['round', 'square', 'butt']
            strokeLinecap: 'round',
            // 圆环大小 单位是 px
            size: 140,
            // 圆环的宽度 相对单位
            barWidth: 80,
            // 是否开启 轮播动画
            showMotion: true,
            // 动画的 间隔时间
            times: 10000,
            // 圆环类型
            type: 0,
          },
        },
        {
          data: {
            label: '贫困人口率',
            value: 68,
          },
          options: {
            // 圆环颜色
            color: '#FF8943',
            // 圆环背景颜色
            bgColor: 'rgba(0, 52, 100, 1)',
            // 是否顺时针 旋转
            clockwise: true,
            // 圆环端点的形状 ['round', 'square', 'butt']
            strokeLinecap: 'round',
            // 圆环大小 单位是 px
            size: 140,
            // 圆环的宽度 相对单位
            barWidth: 80,
            // 是否开启 轮播动画
            showMotion: true,
            // 动画的 间隔时间
            times: 10000,
            // 圆环类型
            type: 4,
          },
        },
      ],
      data6: [
        {
          url: require('@/assets/csts/photo1.png'),
          title: '南京鸡鸣寺',
        },
        {
          url: require('@/assets/csts/photo1.png'),
          title: '南京鸡鸣寺',
        },
        {
          url: require('@/assets/csts/photo1.png'),
          title: '南京鸡鸣寺',
        },
      ],
      data7: [
        {
          num: 8,
          tit: '党工委 (个)',
        },
        {
          num: 10,
          tit: '党总支 (个)',
        },
        {
          num: 12,
          tit: '党支部 (个)',
        },
        {
          num: 2468,
          tit: '党员总数 (人)',
        },
      ],
      data2: {
        data: [
          ['product', ''],
          ['30-40年  21.94%', 2456],
          ['20-30年  28.74%', 1200],
          ['10-20年  21.94%', 2000],
          ['10年 7.56%', 1800],
          ['40-50年  9.08%', 2211],
        ],
        options: {
          colors: ['#2EF6FF', '#E6F973', '#6D5AE2', '#5AE29D', '#FFBA4F'],
          alpha: 65,
          pieSize: 220,
          pieInnerSize: 160,
          position: ['50%', '35%'],
          bgImg: {
            top: '40%',
            left: '50%',
          },
          unit: '人',
          title: {
            fontSize: '16px',
            top: 70,
          },
          subtitle: {
            fontSize: '14px',
            top: 90,
          },
          legend: {
            // orient: 'vertical',
            // align: 'center',
            // verticalAlign: 'bottom',
            top: 20,
            left: -20,
          },
        },
      },
      data8: {
        data: [
          ['product', ''],
          ['30-40年  21.94%', 2456],
          ['20-30年  28.74%', 1200],
          ['10-20年  21.94%', 2000],
          ['10年 7.56%', 1800],
          ['40-50年  9.08%', 2211],
        ],
        // options: {
        //   colors: ['#2EF6FF', '#E6F973', '#6D5AE2', '#5AE29D', '#FFBA4F', '#E05165'],
        //   alpha: 65,
        //   pieSize: 220,
        //   pieInnerSize: 160,
        //   position: ['35%', '-50%'],
        //   bgImg: {
        //     top: '60%',
        //     left: '37%'
        //   },
        //   unit: '',
        //   title: {
        //     fontSize: '16px',
        //     top: 70,
        //     textColor: 'rgba(255, 255, 255, 0)'
        //   },
        //   subtitle: {
        //     fontSize: '14px',
        //     top: 90,
        //     textColor: 'rgba(255, 255, 255, 0)'
        //   },
        //   legend: {
        //     orient: 'vertical',
        //     align: 'center',
        //     verticalAlign: 'bottom',
        //     top: -10,
        //     left: 160
        //   }
        // }
        options: {
          colors: ['#2EF6FF', '#E6F973', '#6D5AE2', '#5AE29D', '#FFBA4F'],
          alpha: 65,
          pieSize: 220,
          pieInnerSize: 160,
          position: ['50%', '35%'],
          bgImg: {
            top: '40%',
            left: '50%',
          },
          unit: '人',
          title: {
            fontSize: '16px',
            top: 70,
          },
          subtitle: {
            fontSize: '14px',
            top: 90,
          },
        },
      },
      swiperOption: {
        observer: true,
        observeParents: true,
        autoHeight: true,
        spaceBetween: 0,
        autoplay: {
          delay: 3500,
          disableOnInteraction: false,
        },
        slidesPerView: 'auto',
        grabCursor: true,
        autoplayDisableOnInteraction: false,
        mousewheelControl: true,
        pagination: {
          el: '.swiper-pagination',
        },
      },
      data9_1: [
        {
          lab: 'PM2.5',
          num: 22,
          img: require('@/assets/csts/icon16.png'),
        },
        {
          lab: 'SO2',
          num: 2,
          img: require('@/assets/csts/icon17.png'),
        },
        {
          lab: 'CO',
          num: 0.7,
          img: require('@/assets/csts/icon18.png'),
        },
      ],
      data9_2: [
        {
          lab: 'NO2',
          num: 28,
          img: require('@/assets/csts/icon19.png'),
        },
        {
          lab: 'PM10',
          num: 30,
          img: require('@/assets/csts/icon20.png'),
        },
        {
          lab: 'O3',
          num: 64,
          img: require('@/assets/csts/icon21.png'),
        },
      ],
      data10_1: [
        {
          lab: '警员(人)',
          num: 51,
          img: require('@/assets/csts/icon22.png'),
        },
        {
          lab: '辅警(人)',
          num: 549,
          img: require('@/assets/csts/icon23.png'),
        },
        {
          lab: '警车',
          num: 368,
          img: require('@/assets/csts/icon24.png'),
        },
      ],
      data11: [
        ['product', '系列名'],
        ['当前车流量', 23],
        ['当前拥堵路段', 9],
        ['当前违章数', 18],
        ['当前警情', 11],
      ],
      data12: {
        data: [
          ['product', '救援成功', '隐患整改'],
          ['2017', 500, 310],
          ['2018', 310, 102],
          ['2019', 320, 190],
          ['2020', 350, 176],
          ['2021', 360, 290],
          ['2022', 250, 161],
        ],
        options: {
          // 颜色数据
          color: ['#ffd11a', 'rgba(46,200,207,1)'],
          // 柱子的宽度
          barWidth: 20,
          // 柱子X轴位置
          symbolOffsetX: ['-30%', '30%'],
          // 顶部的大小
          symbolSize: [20, 11],
        },
      },
      activaIdx: -1,
      isShowEventDetail: false,
      isShowCountryPart: false,
      isXlgcShow: false,
      isXlgcShow1: false,
      isWgllShow: false,
      isdzzShow: false,
      isxzqyShow: false,
      iszwzxShow: false,
      iszhzxShow: false,
      iscsglgdShow: false,
      isshaqShow: false,
      issthbShow: false,
      iswhlyShow: false,
      isyjfkShow: false,
      isggjtShow: false,
      iszdqyShow: false,
      isxxShow: false,
      iswbShow: false,
      isylcsShow: false,
      ishczShow: false,
      isdyyShow: false,
      sewageDataList1: [
        ['江宁区湖熟街道雨润大家…', '监测预警', '23/03/12 14:39:45'],
        ['江宁区湖熟街道雨润大家…', '12345热线', '23/03/12 14:39:45'],
        ['江宁区湖熟街道雨润大家…', '领导交办', '23/03/12 14:39:45'],
        ['江宁区湖熟街道雨润大家…', '数字城管', '23/03/12 14:39:45'],
        ['江宁区湖熟街道雨润大家…', '监测预警', '23/03/12 14:39:45'],
        ['江宁区湖熟街道雨润大家…', '12345热线', '23/03/12 14:39:45'],
        ['江宁区湖熟街道雨润大家…', '领导交办', '23/03/12 14:39:45'],
        ['江宁区湖熟街道雨润大家…', '数字城管', '23/03/12 14:39:45'],
        ['江宁区湖熟街道雨润大家…', '监测预警', '23/03/12 14:39:45'],
        ['江宁区湖熟街道雨润大家…', '12345热线', '23/03/12 14:39:45'],
        ['江宁区湖熟街道雨润大家…', '领导交办', '23/03/12 14:39:45'],
      ],
      swiperOption1: {
        loop: true,
        // slidesPerView:"2",
        autoplay: {
          // 这个参数很重要,当外力干扰轮播图时，它能够继续实现自我滚动
          disableOnInteraction: false,
          delay: 15000, //5秒切换一次
        },
        pagination: {
          el: '.swiper-pagination',
          clickable: true,
          clickableClass: 'my-pagination-clickable',
        },
        // 设置点击箭头
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
        },
      },
      video1: require('@/assets/videos/video1.mp4'),
      video2: require('@/assets/videos/video2.mp4'),
      video8: require('@/assets/videos/video8.mp4'),
      box5Data: [
        ['product', ''],
        ['1月', 75],
        ['2月', 100],
        ['3月', 125],
        ['4月', 100],
        ['5月', 200],
        ['6月', 200],
        ['7月', 75],
        ['8月', 100],
        ['9月', 125],
        ['10月', 100],
        ['11月', 200],
        ['12月', 200],
        // ['product', '党员支部大会', '支部委员会', '党小组会', '党课'],
        // ['鼓楼', 200, 160, 140, 160],
        // ['六合', 140, 90, 180, 90],
        // ['建邺', 102, 105, 120, 105],
        // ['江宁', 200, 90, 130, 90],
        // ['雨花', 150, 200, 150, 200],
        // ['浦口', 200, 90, 130, 90],
      ],
      box5Options: {
        color: ['#01FF6C', '#8300D7', '#FF9201', '#00A3D7FF'],
        stack: true,
        isGradient: true,
        barMaxWidth: 10,
        gradientColors: [
          ['#01FF6C', '#00ED8B'],
          ['#8300D7', '#7800ED'],
          ['#FF9201', '#EDB000'],
        ],
        isHor: false,
        barGap: '30%',
        tooltip: {
          color: '#fff',
          show: true,
          type: 'shadow',
        },
      },
      sjzlTopData: [
        {
          num: 6000,
          tit: '本月新增事件量',
        },
        {
          num: 2345,
          tit: '本月新增办理事件量',
        },
        {
          num: 3655,
          tit: '本月新增办结事件量',
        },
      ],
      sjzlBootomL: [
        {
          num: 1235,
          tit: '新增',
        },
        {
          num: 2209,
          tit: '办理',
        },
        {
          num: 10652,
          tit: '办结',
        },
      ],
      sjzlBootomR: [
        {
          num: 100,
          tit: '签收率',
        },
        {
          num: 99,
          tit: '办理率',
        },
        {
          num: 93,
          tit: '办结率',
        },
      ],
      // swiperOptionZy: {
      //   loop: true,
      //   direction: 'vertical',
      //   autoplay: {
      //     // 这个参数很重要,当外力干扰轮播图时，它能够继续实现自我滚动
      //     disableOnInteraction: false,
      //     delay: 5000, //5秒切换一次
      //   },
      // },
      swiperOptionZy: {
        loop: true,
        direction: 'vertical',
        autoplay: {
          // 这个参数很重要,当外力干扰轮播图时，它能够继续实现自我滚动
          disableOnInteraction: false,
          delay: 15000, //5秒切换一次
        },
        //  loop: false,
        // direction: 'vertical',
        slidesPerView: '2',
        // height: 715,
        // autoplay: {
        //   disableOnInteraction: false,
        //   delay: 5000 //5秒切换一次
        // },
      },
      box4TopData: [
        { num: '698', name: '设备总数' },
        { num: '698', name: '在线数量' },
        { num: '0', name: '离线数量' },
      ],
      box4BottomData: {
        data: [
          ['product', '事件总数'],
          ['烟感监测', 698],
          ['燃气监测', 0],
          ['水质监测', 0],
          ['环境监测', 0],
        ],
        options: {
          colors: ['#80CF83', '#CDCF72', '#0280DF', '#13A4C0'],
          legend: {
            top: 2,
            // left: 100,
            // orient: 'vertical',
            itemWidth: 10,
            itemHeight: 10,
            itemRadius: 2,
            itemPadding: 5,
            itemDistance: 12,
            itemMarginTop: 12,
            textColor: 'rgba(255, 255, 255, 0.85)',
            fontWeight: '400',
            fontSize: '12px',
            fontFamily: 'DINPro-Medium',
            letterSpacing: '3px',
          },
          position: ['50%', '-60%'],
          bgImg: {
            width: '80%',
            height: '80%',
            top: '42%',
            left: '50%',
          },
          unit: '个',
          title: {
            fontSize: '16px',
            top: 50,
          },
          subtitle: {
            fontSize: '14px',
            top: 70,
          },
        },
      },
      box5BottomList: [
        ['热成像设备01', '树木失火', '江宁区湖熟街道西', '23/03/12 14:39:45'],
        ['热成像设备02', '人员吸烟', '江宁区湖熟街道西', '23/03/12 14:39:45'],
        ['热成像设备01', '树木失火', '江宁区湖熟街道西', '23/03/12 14:39:45'],
        ['热成像设备02', '人员吸烟', '江宁区湖熟街道西', '23/03/12 14:39:45'],
        ['热成像设备01', '树木失火', '江宁区湖熟街道西', '23/03/12 14:39:45'],
        ['热成像设备02', '人员吸烟', '江宁区湖熟街道西', '23/03/12 14:39:45'],
        ['热成像设备01', '树木失火', '江宁区湖熟街道西', '23/03/12 14:39:45'],
        ['热成像设备02', '人员吸烟', '江宁区湖熟街道西', '23/03/12 14:39:45'],
        ['热成像设备01', '树木失火', '江宁区湖熟街道西', '23/03/12 14:39:45'],
        ['热成像设备02', '人员吸烟', '江宁区湖熟街道西', '23/03/12 14:39:45'],
      ],
      stjgData: [
        {
          bgColor: 'linear-gradient(270deg, #ffc3bf 0%, #e94125 100%)',
          name: 'M2.5μg/m3',
          val: '48',
          percent: 80,
        },
        {
          bgColor: 'linear-gradient(270deg, #FFECBF 0%, #FFB000 100%)',
          name: 'PM10μg/m3',
          val: '310',
          percent: 50,
        },
        {
          bgColor: 'linear-gradient(270deg, #BFFFFF 0%, #00FFE0 100%)',
          name: 'O3μg/m3',
          val: '93',
          percent: 30,
        },
        {
          bgColor: 'linear-gradient(270deg, #BFFFFF 0%, #00FFE0 100%)',
          name: 'SO2μg/m3',
          val: '5',
          percent: 20,
        },
        {
          bgColor: 'linear-gradient(270deg, #BFFFFF 0%, #00FFE0 100%)',
          name: 'NO2μg/m3',
          val: '8',
          percent: 30,
        },
        {
          bgColor: 'linear-gradient(270deg, #BFFFFF 0%, #00FFE0 100%)',
          name: 'COmg/m3',
          val: '0.3',
          percent: 20,
        },
      ],
      asideList: [
        {
          bg: require('@/assets/aside/bg1.png'),
          bgActive: require('@/assets/aside/bg2.png'),
          icon: require('@/assets/tsgz/map/icon1.png'),
          iconActive: require('@/assets/tsgz/map/icon1_active.png'),
          label: '烟感监测',
          active: false,
        },
        {
          bg: require('@/assets/aside/bg1.png'),
          bgActive: require('@/assets/aside/bg2.png'),
          icon: require('@/assets/tsgz/map/icon2.png'),
          iconActive: require('@/assets/tsgz/map/icon2_active.png'),
          label: '视频监测',
          active: false,
        },
        // {
        //   bg: require('@/assets/aside/bg1.png'),
        //   bgActive: require('@/assets/aside/bg2.png'),
        //   icon: require('@/assets/aside/icon3.png'),
        //   iconActive: require('@/assets/aside/icon8.png'),
        //   label: '指挥调度',
        //   active: false,
        // },
      ],
      surList: [
        // {
        //   iconNormal: require('@/assets/tsgz/map/icon3.png'),
        //   iconActive: require('@/assets/tsgz/map/icon3_active.png'),
        //   label: '烟感监测',
        //   count: 698,
        //   checked: false,
        // },
        // {
        //   iconNormal: require('@/assets/tsgz/map/icon4.png'),
        //   iconActive: require('@/assets/tsgz/map/icon4_active.png'),
        //   label: '空气质量',
        //   count: 2,
        //   checked: false,
        // },
        // {
        //   iconNormal: require('@/assets/tsgz/map/icon5.png'),
        //   iconActive: require('@/assets/tsgz/map/icon5_active.png'),
        //   label: '燃气监控',
        //   count: 162,
        //   checked: false,
        // },
      ],
      surInitList: [
        {
          iconNormal: require('@/assets/tsgz/map/icon3.png'),
          iconActive: require('@/assets/tsgz/map/icon3_active.png'),
          label: '烟感监测',
          count: 698,
          checked: false,
        },
        {
          iconNormal: require('@/assets/tsgz/map/icon4.png'),
          iconActive: require('@/assets/tsgz/map/icon4_active.png'),
          label: '空气质量',
          count: 2,
          checked: false,
        },
        {
          iconNormal: require('@/assets/tsgz/map/icon5.png'),
          iconActive: require('@/assets/tsgz/map/icon5_active.png'),
          label: '燃气监控',
          count: 162,
          checked: false,
        },
      ],
      showYjzyList: false,
      showYgjc1: false,
      showYgjc2: false,
      showYgjc3: false,
      spjkShow: false,
      wlsbMarks1: [],
      wlsbMarks2: [],
      wlsbMarks3: [],
      taskDistributionShow: false,
      sjzlPopShow: false,
      detailDialogShow: false,
      spjkLeftTree: [],
      eventMarkers: [],
      realTimeEventList: [],
      eventId: '',
      eventDialogShow: false,
      eventInfo: {},
      eventDetailShow: false,
      eventDetailObj: {},
      eventImgs: [],
      processList: [],
      isDone: false,
      realTimeEventDialogShow: false,
      eventStatisticShow: false,
      cameraId1: '',
      cameraId2: '',
      cameraId3: '',
      cameraId4: '',
      videoSingleShow: false,
      videoSingleUrl: '',
      cameraCode: '',
      notCompl1: {
        value: 23,
      },
      notCompl2: [
        ['product', '占比'],
        ['紧急', 71],
        ['一般', 29],
      ],
      yjData: [],
      sphsData: [],
      initCheckedPeo: [],
    }
  },
  created () {
    // this.watchFlag()
  },
  mounted () {
    this.cameraMarkerFn()
    this.getQgdListFn()
    this.getSmokeDeviceMakersFn()
    this.getQgdWbjTjFn()
    // this.getSsyjFn()

    this.queryByPageSsyj()
    // this.wglListJkFn()
    this.getRightCameraFn()
  },
  watch: {
    // '$route.params'(newval, oldval) {
    //   this.watchFlag()
    // },
  },
  computed: {
    formatNumber () {
      return function (param) {
        console.log(param)
        if (this.isEmpty(param) || param.length < 4 || isNaN(param)) {
          return param
        }

        let small = ''
        let isSmall = false
        if (param.split('.').length === 2) {
          // 有小数
          isSmall = true
          small = param.split('.')[1]
          param = param.split('.')[0]
        }

        let result = ''

        while (param.length > 3) {
          result = ',' + param.slice(-3) + result
          param = param.slice(0, param.length - 3)
        }

        if (param) {
          result = param + result
        }

        if (isSmall) {
          result = result + '.' + small
        }
        console.log(result)
        return result
      }
    },
    box5initOptions () {
      return {
        yAxis: {
          name: '件',
        },
        tooltip: {
          formatter: (params) => {
            var relVal = params[0].name
            for (var i = 0, l = params.length; i < l; i++) {
              var unit = '件'
              relVal += '<br/>' + params[i].marker + params[i].value + unit
            }
            return relVal
          },
        },
      }
    },
  },
  methods: {
    getOperateSssj (val, it, result) {
      console.log(222, val, it, result)
      this.operateData = result
      if (val == 0) {
        this.alarmToOrder(result)
      }
    },
    alarmToOrder (result) {
      let params = {
        alarmId: result.id,
        appealContent: result.content,
        appealTitle: result.title,
        eventDate: result.createTime,
        eventLocation: result.longitude + ',' + result.latitude,
        eventPlace: result.incidentLocation,
      }
      alarmToOrder(params).then(res => {
        if (res?.code == 200) {
          this.$message.success('转工单成功！')
        }
      })
    },
    async getSmokeDeviceMakersFn () {
      const res = await getSmokeDeviceMakers()
      this.ygjcPoint = res
      this.surInitList[0].count = res.length
      // console.log('烟杆 ---------- ', res)
    },
    watchFlag () {
      let flag = this.$route.query.flag?.substr(0, 4)
      this.djylShow = false
      this.iscsglgdShow = false
      switch (flag) {
        case 'djyl':
          this.djylShow = true
          break
        case 'csgl':
          this.iscsglgdShow = true
          break
        default:
          break
      }
    },
    // csglBulletFrame(val) {
    //   this.iscsglgdShow = val
    // },
    // shaqBulletFrame(val) {
    //   this.isshaqShow = val
    // },
    // sthbBulletFrame(val) {
    //   this.issthbShow = val
    // },
    // djylBulletFrame(val) {
    //   this.djylShow = val
    // },
    // whlyBulletFrame(val) {
    //   this.iswhlyShow = val
    // },
    // yjfkBulletFrame(val) {
    //   this.isyjfkShow = val
    // },
    // ggjtBulletFrame(val) {
    //   this.isggjtShow = val
    // },
    closeCountryPart () {
      this.isShowCountryPart = false
      if (this.activaIdx === 3) this.activaIdx = -1
    },
    checkTree (e) {
      console.log(e)
      // 地图打点
      let data = csbjPoint.features.map((e) => {
        return {
          position: e.geometry.coordinates.concat(100),
          properties: e.properties,
          img: e.img,
        }
      })
      this.$refs.CesiumMapRef.loadPoints1(data, 'csbjPoint')
    },
    marker (active, i) {
      console.log(active, i)
      if (i == 0 && active) {
        this.mapMarker1()
      } else if (i == 0 && !active) {
        this.showYgjc1 = false
        this.cleaerMapMarker('wlsbMark1Type')
        // this.showYjzyList = false
        // this.surList = JSON.parse(JSON.stringify(this.surInitList))
        // this.cleaerMapMarker('wlsbMark1Type')
        // this.cleaerMapMarker('wlsbMark2Type')
        // this.cleaerMapMarker('wlsbMark3Type')
      }
      if (i == 1 && active) {
        this.getCameraMakersFn()
      } else if (i == 1 && !active) {
        this.cleaerMapMarker('videoSurveillance')
      }
      if (i == 2 && active) {
        this.isXlgcShow1 = true
      } else if (i == 2 && !active) {
        this.isXlgcShow1 = false
      }
    },
    surEmit (active, val) {
      console.log(active, val)
      if (val == 0 && active) {
        this.mapMarker1()
      } else if (val == 0 && !active) {
        this.showYgjc1 = false
        this.cleaerMapMarker('wlsbMark1Type')
      }
      // if (val == 1 && active) {
      //   this.mapMarker2()
      // } else if (val == 1 && !active) {
      //   this.showYgjc2 = false
      //   this.cleaerMapMarker('wlsbMark2Type')
      // }
      // if (val == 2 && active) {
      //   this.mapMarker3()
      // } else if (val == 2 && !active) {
      //   this.showYgjc3 = false
      //   this.cleaerMapMarker('wlsbMark3Type')
      // }
    },
    mapMarker1 () {
      this.$refs.leafletMap.drawPoiMarker(
        this.ygjcPoint.map((it) => ({
          latlng: [it.lnglat.split(',')[0], it.lnglat.split(',')[1]],
          icon: {
            iconUrl: require('@/assets/jcyj/map/map1_icon.png'),
            iconSize: [32, 42],
            iconAnchor: [16, 42],
          },
          props: {
            id: it.id,
            type: 'wlsbMark1Type',
          },
          info: {
            deviceName: it.deviceName,
            detail: JSON.parse(it.info),
            updateTime: dayjs(it.update_time).format('YYYY-MM-DD HH:mm:ss'),
          },
        })),
        'wlsbMark1Type',
        true
      )
    },
    mapMarker2 () {
      this.wlsbMarks2 = [
        {
          latlng: [31.862144, 118.971976],
          icon: {
            iconUrl: require('@/assets/jcyj/map/map2_icon.png'),
            iconSize: [32, 42],
            iconAnchor: [16, 42],
          },
          props: {
            id: 'wlsbMark2Id',
            type: 'wlsbMark2Type',
          },
        },
        {
          latlng: [31.865986, 118.980902],
          icon: {
            iconUrl: require('@/assets/jcyj/map/map2_icon.png'),
            iconSize: [32, 42],
            iconAnchor: [16, 42],
          },
          props: {
            id: 'wlsbMark2Id',
            type: 'wlsbMark2Type',
          },
        },
        {
          latlng: [31.855427, 118.985752],
          icon: {
            iconUrl: require('@/assets/jcyj/map/map2_icon.png'),
            iconSize: [32, 42],
            iconAnchor: [16, 42],
          },
          props: {
            id: 'wlsbMark2Id',
            type: 'wlsbMark2Type',
          },
        },
      ]
      this.$refs.leafletMap.drawPoiMarker(this.wlsbMarks2, 'wlsbMark2Type', true)
    },
    mapMarker3 () {
      this.wlsbMarks3 = [
        {
          latlng: [31.873373, 118.973006],
          icon: {
            iconUrl: require('@/assets/jcyj/map/map3_icon.png'),
            iconSize: [32, 42],
            iconAnchor: [16, 42],
          },
          props: {
            id: 'wlsbMarks3Id',
            type: 'wlsbMark3Type',
          },
        },
        {
          latlng: [31.883741, 119.016546],
          icon: {
            iconUrl: require('@/assets/jcyj/map/map3_icon.png'),
            iconSize: [32, 42],
            iconAnchor: [16, 42],
          },
          props: {
            id: 'wlsbMarks3Id',
            type: 'wlsbMark3Type',
          },
        },
        {
          latlng: [31.895522, 118.975544],
          icon: {
            iconUrl: require('@/assets/jcyj/map/map3_icon.png'),
            iconSize: [32, 42],
            iconAnchor: [16, 42],
          },
          props: {
            id: 'wlsbMarks3Id',
            type: 'wlsbMark3Type',
          },
        },
      ]
      this.$refs.leafletMap.drawPoiMarker(this.wlsbMarks3, 'wlsbMark3Type', true)
    },
    showDialog (layerId, it) {
      const id = it.props.id
      console.log(layerId, it)
      if (layerId == 'wlsbMark1Type') {
        this.ygjcInfo = it.info
        this.showYgjc1 = true
      } else if (layerId == 'wlsbMark2Type') {
        this.showYgjc2 = true
      } else if (layerId == 'wlsbMark3Type') {
        this.showYgjc3 = true
      } else if (layerId === 'eventInformation') {
        this.eventId = id
        this.getQgdListDetail(it.info)
      } else if (layerId === 'videoSurveillance') {
        this.cameraXq1(id)
      }
    },
    cleaerMapMarker (type) {
      // 清除标记点
      this.$refs.leafletMap.removeLayer(type)
    },
    showMoreFn2 () {
      this.sjzlPopShow = true
    },
    showMoreFn3 () {
      console.log(2222)
      this.detailDialogShow = true
    },

    clickRealtimeEventQgd (item) {
      console.log(item)
      // 将该点位单独标记在地图上
      // console.log('this.eventMarkers', this.eventMarkers)
      const singleEventMarker = this.eventMarkers.filter((it) => it.props.id === item.id)
      this.$refs.leafletMap.drawPoiMarker(singleEventMarker, 'eventInformation', true, true)
      // console.log('singleEventMarker', singleEventMarker)
      // this.$refs.leafletMap.flyToPoint(
      //   [singleEventMarker[0].latlng[0], singleEventMarker[0].latlng[1]],
      //   18
      // )
    },
    async getQgdListFn () {
      let res1 = await getQgdList() // 地图落点
      // console.log('res1', res1)
      this.eventMarkers = res1.result.map((it) => ({
        latlng: [it.lat, it.lng],
        icon: {
          iconUrl: require('@/assets/map/point/point1.png'),
          iconSize: [32, 42],
          iconAnchor: [16, 42],
        },
        props: {
          id: it.id,
          type: 'eventInformation',
        },
        info: it,
      }))
      if (res1.code == '200') {
        this.realTimeEventList = res1.result.slice(0, 10).map((it) => ({
          ...it,
        })) // 实时事件
      } else {
        this.$message({
          message: '未查询到事件信息',
          type: 'warning',
        })
      }
    },
    async getQgdListDetail (info) {
      console.log('info', info)
      const res = await getQgdDetailJk({ orderId: info.id })
      if (res?.code == '200') {
        const {
          appealTitle: title, // 标题
          appealType: type, // 诉求类型
          eventPlace: addressDetail, // 地址
          appealContent: orderContent, // 事件内容
          serveName: reporterName, // 姓名
          orderSource: eventCategoryName, // 事件类型
          eventDate: reportTime, // 事件时间 | 发生时间
          createDate: insertTime, // 上报时间
          orderNum: orderNo, // 事件编号
          formStatus: state, // 事件状态
          showFlag: istemporary, // 公开信息：0 是
          emeLevel: priority, // 紧急程度
          area: areaNo, // 归属地
          torderAttachments: appendixList, // 附件
          mobile: phone, // 手机号
          community: gsd, //归属地
          grid: grid,
          // tbOrderRecordList // 处理流程
        } = info || {}

        // const eventCategoryName = info.orderSource
        //   ? this.qgdCodesjlyOptions.find(item => item.dicValue == info.orderSource).dicLabel
        //   : '' // 事件来源
        // const priority = info.emeLevel
        //   ? this.qgdCodejjcdOptions.find(item => item.dicValue == info.emeLevel).dicLabel
        //   : '' // 优先级

        // console.log('res', res)
        const tbOrderRecordList = res.result // 处理流程
        this.currentAddress = addressDetail
        // console.log(this.currentAddress)
        this.eventInfo = {
          title: title,
          type: type,
          time: reportTime,
          address: addressDetail,
          describe: orderContent,
        }
        // eventDetail - 用于事件信息点击一般处置展示的左侧的事件详情
        console.log('eventCategoryName', eventCategoryName)
        this.eventDetail = [
          {
            label: '姓名',
            value: reporterName,
          },
          {
            label: '事件类型',
            value: type,
          },
          {
            label: '事件地址',
            value: addressDetail,
          },
          {
            label: '上报时间',
            value: insertTime,
          },
          {
            label: '事件来源',
            value: eventCategoryName,
          },
          {
            label: '事件编号',
            value: orderNo,
          },
          {
            label: '事件状态',
            value: state,
          },
          {
            label: '事发时间',
            value: reportTime,
          },
          {
            label: '公开信息',
            value: istemporary === '0' ? '是' : '否',
          },
          {
            label: '紧急程度',
            value: priority,
          },
          {
            label: '归属地',
            value: areaNo,
          },
          {
            label: '事件内容',
            value: orderContent,
          },
          // {
          //   label: '附件',
          //   value: appendixList?.length > 0 ? '' : '无'
          // }
        ]
        // eventDetailObj - 用于事件信息点击事件详情
        this.eventDetailObj = {
          name: phone ? `${reporterName}（${phone}）` : reporterName,
          address: addressDetail,
          gsd: gsd,
          reportTime: insertTime,
          type: type,
          origin: eventCategoryName,
          content: orderContent,
          eventNo: orderNo,
          state,
          eventTime: reportTime,
          info: istemporary === '0' ? '是' : '否',
          urgent: priority,
          grid: grid,
        }
        console.log('this.eventDetailObj', this.eventDetailObj)

        // 计划完成时间expectTime，签收时间claimTime，附件appendixList，处置意见dealAdvice，操作动作nodeName+operateName
        this.processList = tbOrderRecordList.map((it, i) => ({
          ...it,
        }))
        // this.eventImgs = []
        // appendixList?.forEach(it => {
        //   this.eventImgs.push(it.url)
        // })
        this.eventDialogShow = true
      } else {
        this.$message({
          message: '未查询到事件详情',
          type: 'warning',
        })
      }
    },
    // 事件信息弹窗上点击按钮触发事件
    eventInfoBtnClick (i) {
      if (i === 0) {
        this.eventDetailShow = true
      }
    },
    eventHandle (i) {
      if (i === 0) {
        this.eventDetailShow = false
        this.eventDialogShow = false
        // 关闭未办结工单弹窗
        this.eventStatisticShow = false
        // 关闭实时事件弹窗
        this.realTimeEventDialogShow = false
      } else if (i === 1) {
        this.eventDetailShow = false
        this.eventStatisticShow = false
        this.realTimeEventDialogShow = false
        this.$refs.leafletMap.flyToPoint(this.currentCheckedMarker, 15)
      }
    },
    // 实时事件下钻按钮操作
    async realTimeEventOperateQgd (i, id, it) {
      console.log(it)
      const info = it[10]
      if (i === 0) {
        const res = await getQgdDetailJk({ orderId: info.id })
        const {
          appealTitle: title, // 标题
          appealType: type, // 诉求类型
          eventPlace: addressDetail, // 地址
          appealContent: orderContent, // 事件内容
          serveName: reporterName, // 姓名
          orderSource: eventCategoryName, // 事件类型
          eventDate: reportTime, // 事件时间 | 发生时间
          createDate: insertTime, // 上报时间
          orderNum: orderNo, // 事件编号
          formStatus: state, // 事件状态
          showFlag: istemporary, // 公开信息：0 是
          emeLevel: priority, // 紧急程度
          area: areaNo, // 归属地
          torderAttachments: appendixList, // 附件
          mobile: phone, // 手机号
          community: gsd, //归属地
          lng: longitude, //归属地
          lat: latitude, //归属地
          // tbOrderRecordList // 处理流程
        } = info || {}

        const tbOrderRecordList = res.result // 处理流程
        this.eventDetailObj = {
          name: phone ? `${reporterName}（${phone}）` : reporterName,
          address: addressDetail,
          gsd: gsd,
          reportTime: insertTime,
          type: type,
          origin: eventCategoryName,
          content: orderContent,
          eventNo: orderNo,
          state,
          eventTime: reportTime,
          info: istemporary === '0' ? '是' : '否',
          urgent: priority,
        }

        this.processList = tbOrderRecordList.map((it, i) => ({
          ...it,
        }))
        this.eventMarkers = [
          {
            latlng: [latitude, longitude],
            icon: {
              iconUrl: require('@/assets/map/point/point1.png'),
              iconSize: [32, 42],
              iconAnchor: [16, 42],
            },
            props: {
              id: id,
              type: 'eventInformation',
            },
            info: info,
          },
        ]
        this.$refs.leafletMap.drawPoiMarker(this.eventMarkers, 'eventInformation', true)
        this.currentCheckedMarker = [latitude, longitude]
        this.eventDetailShow = true
      } else if (i === 1) {
        this.realTimeEventDialogShow = false
        console.log(it, info)
        // 如果地图上已经打点了事件信息，直接聚焦
        if (this.asideList[0].active) {
          this.$refs.leafletMap.flyToPoint([info.lat, info.lng], 18)
        } else {
          this.eventDialogShow = false
          // 将该点位单独标记在地图上
          console.log(this.eventMarkers)
          this.eventMarkers = [
            {
              latlng: [info.lat, info.lng],
              icon: {
                iconUrl: require('@/assets/map/point/point1.png'),
                iconSize: [32, 42],
                iconAnchor: [16, 42],
              },
              props: {
                id: info.id,
                type: 'eventInformation',
              },
              info: info,
            },
          ]
          this.$refs.leafletMap.drawPoiMarker(this.eventMarkers, 'eventInformation', true)
          this.$refs.leafletMap.flyToPoint([info.lat, info.lng], 18)
        }
      }
    },
    // 未办结工单下钻按钮操作
    async eventStatisticOperateQgd (i, id, it, tabIdx) {
      console.log(i, id, it, tabIdx)
      if (i === 0) {
        const info = it[9]
        console.log(info)
        const res = await getQgdDetailJk({ orderId: info.id })
        const {
          appealTitle: title, // 标题
          appealType: type, // 诉求类型
          eventPlace: addressDetail, // 地址
          appealContent: orderContent, // 事件内容
          serveName: reporterName, // 姓名
          orderSource: eventCategoryName, // 事件类型
          eventDate: reportTime, // 事件时间 | 发生时间
          createDate: insertTime, // 上报时间
          orderNum: orderNo, // 事件编号
          formStatus: state, // 事件状态
          showFlag: istemporary, // 公开信息：0 是
          emeLevel: priority, // 紧急程度
          area: areaNo, // 归属地
          torderAttachments: appendixList, // 附件
          mobile: phone, // 手机号
          community: gsd, //归属地
          lng: longitude, //归属地
          lat: latitude, //归属地
          // tbOrderRecordList // 处理流程
        } = info || {}

        const tbOrderRecordList = res.result // 处理流程

        this.eventDetailObj = {
          name: phone ? `${reporterName}（${phone}）` : reporterName,
          address: addressDetail,
          gsd: gsd,
          reportTime: insertTime,
          type: type,
          origin: eventCategoryName,
          content: orderContent,
          eventNo: orderNo,
          state,
          eventTime: reportTime,
          info: istemporary === '0' ? '是' : '否',
          urgent: priority,
        }

        this.processList = tbOrderRecordList.map((it, i) => ({
          ...it,
        }))
        tabIdx === 0 ? (this.isDone = true) : (this.isDone = false)

        this.eventMarkers = [
          {
            latlng: [latitude, longitude],
            icon: {
              iconUrl: require('@/assets/map/point/point1.png'),
              iconSize: [32, 42],
              iconAnchor: [16, 42],
            },
            props: {
              id: info.id,
              type: 'eventInformation',
            },
            info: info,
          },
        ]
        this.$refs.leafletMap.drawPoiMarker(this.eventMarkers, 'eventInformation', true)
        this.currentCheckedMarker = [latitude, longitude]
        this.eventDetailShow = true
      }
    },
    // 显示更多
    showMoreFn (i) {
      switch (i) {
        case 1:
          this.eventStatisticShow = true
          break
        case 2:
          this.realTimeEventDialogShow = true
          break
        case 5:
          this.spjkShow = true
          break
        case 6:
          this.jxcsssgjPopShows = true
          break
        default:
          break
      }
    },
    async cameraMarkerFn () {
      const res = await getCameraMakersNew()
      if (res?.code == '200') {
        // let filerRes = res.result[0].children.filter((item) => item.id)
        // this.cameraId1 = filerRes[5].id
        // this.cameraId2 = filerRes[3].id
        // this.cameraId3 = filerRes[9].id
        // this.cameraId4 = filerRes[10].id

        this.spjkLeftTree = []
        this.spjkLeftTree = res.result
      }
    },
    async getRightCameraFn () {
      const res = await getRightCamera()
      if (res?.code == '200') {
        this.cameraId1 = res.result[0].sysvalue
        this.cameraId2 = res.result[1].sysvalue
        this.cameraId3 = res.result[2].sysvalue
        this.cameraId4 = res.result[3].sysvalue
      }
    },
    async getCameraMakersFn () {
      const res = await getCameraMakersNew()
      console.log('getCameraMakersNew', res)
      if (res?.code == '200') {
        const data = this.encapsulateArrayofPoint(
          res.result[0].children.map((it) => ({
            latlng: [it.latitude, it.longitude],
            iconUrl: require('@/assets/map/point/point19.png'),
            id: it.id,
          })),
          'videoSurveillance'
        )
        this.$refs.leafletMap.drawPoiMarker(data, 'videoSurveillance', true)
      }
    },
    encapsulateArrayofPoint (points, layerId) {
      // console.log(points)
      const data = points.map((it) => ({
        latlng: it.latlng,
        icon: {
          iconUrl: it.iconUrl,
          iconSize: [32, 42],
          iconAnchor: [16, 42],
        },
        props: {
          id: it.id,
          type: layerId,
        },
        info: it.info,
      }))
      return data
    },
    async cameraXq1 (id) {
      const res = await getCameraXq(id)
      if (res && res.body && res.body.data && res.body.data.length > 0 && res.body.data[0].url) {
        this.videoSingleUrl = res.body.data[0].url
        this.cameraCode = id
        this.videoSingleShow = true
      }
    },
    async getQgdWbjTjFn () {
      let res = await getQgdWbjTj() // 未办结工单
      this.notCompl1.value = res.result.unDoneCount
      this.notCompl2 = [
        ['product', '占比'],
        ['紧急', res.result.isEmeLevel],
        ['一般', res.result.unEmeLevel],
      ]
      console.log('this.notCompl2', this.notCompl2)
    },
    async getSsyjFn () {
      let res = await getSsyj()
      // if (res.data) {
      //   this.yjData = []
      //   this.yjData = res.data
      // }
    },
    async queryByPageSsyj () {
      let params = {
        page: 1,
        size: 10
      }
      let res = await queryByPageSsyj(params)
      this.yjData = []
      this.yjData = res.data
    },
    async wglListJkFn () {
      console.log(8888)
      // const res = await wglListJk()
      const res = await queryOrgStreetTree()
      if (res) {
        this.sphsData = []
        this.sphsData = res
        // console.log('this.sphsData', this.sphsData)
      }
    },
  },
}
</script>

<style lang="less" scoped>
@font-face {
  font-family: QuartzRegular;
  src: url(~@/assets/leader/font/QuartzRegular.ttf);
}
@keyframes rotateS {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes rotateN {
  0% {
    transform: rotate(360deg);
  }
  100% {
    transform: rotate(0);
  }
}
@keyframes rotateY {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotateY(360deg);
  }
}

.leader_sjts {
  width: 1920px;
  height: 1080px;
  position: absolute;
  top: 0;
  display: flex;
  justify-content: space-between;
  .leader_city_box {
    width: 450px;
    .leader_zt_contain {
      height: 100%;
    }
  }
  .box {
    .cont {
      width: 100%;
      height: 100%;
    }

    .ybsscont {
      height: 100%;
      padding: 14px 44px 24px 37px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: space-between;
      li {
        position: relative;
        width: 165px;
        height: 66px;
        background: url(~@/assets/shzl/ybss_bg.png) no-repeat;
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: left;
        padding-left: 92px;
        .icon {
          position: absolute;
          width: 26px;
          height: 26px;
          top: 10px;
          left: 31px;
          animation: move infinite 3s ease-in-out;
          @keyframes move {
            0% {
              transform: translateY(0px) rotateY(0);
            }
            50% {
              transform: translateY(-10px) rotateY(180deg);
            }
            100% {
              transform: translateY(0px) rotateY(360deg);
            }
          }
        }
        &:nth-child(2) {
          span {
            animation-delay: 0.3s;
          }
        }
        &:nth-child(3) {
          span {
            animation-delay: 0.6s;
          }
        }
        &:nth-child(4) {
          span {
            animation-delay: 0.9s;
          }
        }
        &:nth-child(5) {
          span {
            animation-delay: 1.2s;
          }
        }
        &:nth-child(6) {
          span {
            animation-delay: 1.5s;
          }
        }
        .tit {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 20px;
          white-space: nowrap;
        }
        .num {
          font-size: 18px;
          font-family: PingFangSC, PingFang SC;
          font-weight: normal;
          color: #ffffff;
          line-height: 22px;
        }
      }
    }
    .wrapper4 {
      width: 100%;
      height: 100%;
      padding: 20px 0;
      ul {
        width: 100%;
        height: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;
        li {
          display: flex;
          height: 49px;
          gap: 10px;
          .icon {
            width: 46px;
            height: 49px;
          }
          .info {
            margin-top: -6px;
            .label {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
              text-align: left;
              padding-left: 5px;
            }
            .line {
              width: 87px;
              height: 6px;
              background: url('~@/assets/hszl/line1.png') no-repeat;
            }
            .count {
              text-align: left;
              margin-top: 4px;
              width: 87px;
              height: 22px;
              line-height: 22px;
              background: linear-gradient(96deg, #1d4975 0%, rgba(19, 56, 87, 0.24) 100%);
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #a8b1b9;
              padding-left: 5px;
              span {
                font-size: 18px;
                font-family: PingFangSC, PingFang SC;
                font-weight: normal;
                color: #ffffff;
                line-height: 22px;
                margin-right: 3px;
              }
            }
          }
        }
      }
    }
  }
  .box1 {
    .cont {
      // border: 1px solid red;
      padding: 12px 35px 0 29px;
      height: 100%;
      .swiper {
        width: 100%;
        height: 100%;
      }
      .box1_top {
        display: flex;
        justify-content: space-between;
        & > div {
          display: flex;
          justify-content: flex-start;
          img {
            width: 55px;
            height: 52px;
          }
          & > div {
            & p:first-of-type {
              font-size: 22px;
              font-family: PingFangSC, PingFang SC;
              font-weight: normal;
              color: #ffbb00;
              line-height: 26px;
              text-shadow: 0px 0px 1px rgba(0, 43, 133, 0.84);
            }
            & p:last-of-type {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffbb00;
              line-height: 20px;
            }
          }
          &:nth-of-type(2) {
            & > div p:first-of-type {
              font-size: 22px;
              font-family: PingFangSC, PingFang SC;
              font-weight: normal;
              color: #00ea47;
              line-height: 26px;
              text-shadow: 0px 0px 1px rgba(0, 43, 133, 0.84);
            }
            & > div p:last-of-type {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #00ea47;
              line-height: 20px;
            }
          }
          &:nth-of-type(3) {
            & > div p:first-of-type {
              font-size: 22px;
              font-family: PingFangSC, PingFang SC;
              font-weight: normal;
              color: #fc683a;
              line-height: 26px;
              text-shadow: 0px 0px 1px rgba(0, 43, 133, 0.84);
            }
            & > div p:last-of-type {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #fc683a;
              line-height: 20px;
            }
          }
        }
      }
      .line {
        padding: 13px 0;
        border-bottom: 1px dashed rgba(255, 255, 255, 0.3);
        text-align: left;
        .warningItem_ {
          display: flex;
          img {
            width: 18px;
            height: 18px;
          }
          .title {
            font-size: 16px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            width: 120px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            margin-left: 5px;
            &.red {
              color: #fc683aff;
            }
            &.blue {
              color: #009fecff;
            }
          }
        }
        & > div:first-of-type {
          display: flex;
          // align-items: center;
          justify-content: space-between;
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          line-height: 21px;
          color: #fff;
          margin-bottom: 6px;

          .level {
            // margin-left: 28px;
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 20px;
            span {
              &.red {
                color: #fc683aff;
              }
              &.blue {
                color: #009fecff;
              }
            }
          }
          .time {
            // margin-left: 42px;
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #b6dcff;
            line-height: 17px;
          }
        }
        & > div:last-of-type {
          margin-top: 18px;
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 20px;
        }
      }
    }
  }
  .box2 {
    .cont {
      // border: 1px solid red;
      .sjzl_box {
        padding: 14px 22px 23px;
        .sjzl_box_top {
          margin-bottom: 23px;
          ul {
            display: flex;
            justify-content: space-between;
            li {
              display: flex;
              flex-direction: column;
              &:first-of-type {
                & span:first-of-type {
                  font-size: 20px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: normal;
                  color: #ffffff;
                  line-height: 24px;
                  // text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.5);
                  background: linear-gradient(180deg, #ffffff 0%, #00fffa 67%, #04a1ff 100%);
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                }
                & span:last-of-type {
                  font-size: 14px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: 400;
                  color: #ffffff;
                  line-height: 20px;
                }
              }
              &:nth-of-type(2) {
                & span:first-of-type {
                  font-size: 20px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: normal;
                  color: #ffffff;
                  line-height: 24px;
                  // text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.5);
                  background: linear-gradient(180deg, #ffffff 0%, #ffd500 67%, #ffb904 100%);
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                }
                & span:last-of-type {
                  font-size: 14px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: 400;
                  color: #ffffff;
                  line-height: 20px;
                }
              }
              &:nth-of-type(3) {
                & span:first-of-type {
                  font-size: 20px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: normal;
                  color: #ffffff;
                  line-height: 24px;
                  // text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.5);
                  background: linear-gradient(180deg, #ffffff 0%, #86ff00 67%, #1cff04 100%);
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                }
                & span:last-of-type {
                  font-size: 14px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: 400;
                  color: #ffffff;
                  line-height: 20px;
                }
              }
            }
          }
        }
        .sjzl_box_bootom {
          width: 406px;
          height: 156px;
          background: url(~@/assets/shzl/sjzl_bottom.png) no-repeat;
          background-size: 100% 100%;
          position: relative;
          display: flex;
          justify-content: space-between;
          padding: 20px 18px 17px 14px;
          .now_year {
            position: absolute;
            left: 50%;
            top: 76%;
            transform: translate(-50%, -50%);
            font-size: 18px;
            font-family: PingFangSC, PingFang SC;
            color: #ffffff;
            line-height: 21px;
            letter-spacing: 1px;
            z-index: 99;
          }
          .center {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
          }
          .center_out {
            position: absolute;
            left: 29%;
            top: 0%;
            transform: translate(-50%, -50%);
            z-index: 99;
            animation: rotateS infinite 12s linear;
          }
          .center_in {
            position: absolute;
            left: 29.5%;
            top: 1.7%;
            transform: translate(-50%, -50%);
            z-index: 99;
            animation: rotateN infinite 12s linear;
          }
          .sjzl_box_bootom_l {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-content: space-between;
            width: 94px;
            li {
              display: flex;
              justify-content: space-between;
              & span:first-of-type {
                font-size: 16px;
                font-family: PingFangSC, PingFang SC;
                font-weight: normal;
                color: #ffffff;
                line-height: 19px;
              }
              & span:last-of-type {
                // margin-left: 16px;
                font-size: 14px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                color: #ffffff;
                line-height: 20px;
              }
            }
          }
          .sjzl_box_bootom_r {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-content: space-between;
            width: 89px;
            li {
              display: flex;
              justify-content: space-between;
              & > span:first-of-type {
                font-size: 14px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                color: #ffffff;
                line-height: 20px;
              }
              & > div {
                display: flex;
                font-size: 16px;
                font-family: PingFangSC, PingFang SC;
                font-weight: normal;
                color: #ffffff;
                line-height: 19px;
                span {
                  font-size: 16px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: normal;
                  color: #ffffff;
                  line-height: 19px;
                }
              }
            }
          }
        }
      }
    }
  }
  .box3 {
    .cont {
      // border: 1px solid red;
      padding: 16px 15px 0 7px;
      overflow: hidden;
    }
  }
  .box4 {
    .cont {
      // border: 1px solid red;
      padding: 14px 31px 23px 31px;
      .box_top {
        height: 41px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .box_top_item {
          display: flex;
          align-items: center;
          img {
            width: 40px;
            height: 41px;
          }
          & > div {
            margin-left: 6px;
            text-align: left;
            & p:first-of-type {
              font-size: 18px;
              font-family: PingFangSC, PingFang SC;
              font-weight: normal;
              color: #ffffff;
              line-height: 22px;
              text-shadow: 0px 0px 1px rgba(0, 43, 133, 0.84);
            }
            & p:last-of-type {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
            }
          }
        }
      }
      .box_bottom {
        // margin-top: 33px;
        // border: 1px solid red;
        height: 192px;
      }
    }
  }

  .box5 {
    .cont {
      padding: 18px 5px 19px 9px;
      // border: 1px solid red;
      .box5_top {
        height: 49px;
        // border: 1px solid red;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .box5_top_item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 100%;
          & > img {
            width: 46px;
            height: 49px;
          }
          &:first-of-type > div {
            height: 100%;
            text-align: left;
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #6ee4ff;
            line-height: 20px;
            margin-left: 10px;
            display: flex;
            flex-direction: column;
            & p:first-of-type {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #6ee4ff;
              line-height: 20px;
              margin-top: -4px;
              text-indent: 8px;
            }
            & img {
              width: 87px;
              height: 6px;
            }
            & p:last-of-type {
              margin-top: 2px;
              width: 87px;
              height: 20px;
              background: linear-gradient(96deg, #1d4975 0%, rgba(19, 56, 87, 0.24) 100%);
              font-size: 18px;
              font-family: PingFangSC, PingFang SC;
              font-weight: normal;
              color: #6ee4ff;
              line-height: 20px;
              text-shadow: 0px 0px 1px rgba(0, 43, 133, 0.84);
              text-indent: 10px;
              // -webkit-background-clip: text;
              // -webkit-text-fill-color: transparent;
              span {
                font-size: 14px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                color: #a8b1b9;
                line-height: 20px;
              }
            }
          }
          &:nth-of-type(2) > div {
            height: 100%;
            text-align: left;
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #6ee4ff;
            line-height: 20px;
            margin-left: 10px;
            display: flex;
            flex-direction: column;
            & p:first-of-type {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #00ea47;
              line-height: 20px;
              margin-top: -4px;
              text-indent: 8px;
            }
            & img {
              width: 87px;
              height: 6px;
            }
            & p:last-of-type {
              margin-top: 2px;
              width: 87px;
              height: 20px;
              background: linear-gradient(96deg, #1d4975 0%, rgba(19, 56, 87, 0.24) 100%);
              font-size: 18px;
              font-family: PingFangSC, PingFang SC;
              font-weight: normal;
              color: #00ea47;
              line-height: 20px;
              text-shadow: 0px 0px 1px rgba(0, 43, 133, 0.84);
              text-indent: 10px;
              // -webkit-background-clip: text;
              // -webkit-text-fill-color: transparent;
              span {
                font-size: 14px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                color: #a8b1b9;
                line-height: 20px;
              }
            }
          }
          &:last-of-type > div {
            height: 100%;
            text-align: left;
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #6ee4ff;
            line-height: 20px;
            margin-left: 10px;
            display: flex;
            flex-direction: column;
            & p:first-of-type {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #fc683a;
              line-height: 20px;
              text-indent: 8px;
            }
            & img {
              width: 87px;
              height: 6px;
            }
            & p:last-of-type {
              margin-top: 2px;
              width: 87px;
              height: 20px;
              background: linear-gradient(96deg, #1d4975 0%, rgba(19, 56, 87, 0.24) 100%);
              font-size: 18px;
              font-family: PingFangSC, PingFang SC;
              font-weight: normal;
              color: #ffffff;
              line-height: 20px;
              text-shadow: 0px 0px 1px rgba(0, 43, 133, 0.84);
              text-indent: 10px;
              // -webkit-background-clip: text;
              // -webkit-text-fill-color: transparent;
              span {
                font-size: 14px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                color: #a8b1b9;
                line-height: 20px;
              }
            }
          }
        }
      }
      .box5_bottom {
        height: 173px;
        // border: 1px solid red;
        margin-top: 13px;
        overflow: hidden;
      }
    }
  }
  .box6 {
    .cont {
      padding: 16px 4px 0 14px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .video_ {
        flex-shrink: 0;
        width: 200px;
        height: 112px;
      }
      .box6_top {
        display: flex;
        // justify-content: space-between;
        & > img {
          width: 59px;
          height: 44px;
          margin-right: 17px;
        }
        & .box6_top1 {
          display: flex;
          // justify-content: space-between;
          & span:first-of-type {
            font-size: 20px;
            font-family: Arial-BoldMT, Arial;
            font-weight: normal;
            color: #6fdcff;
            line-height: 23px;
          }
          & span:nth-of-type(2) {
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #86daf7;
            line-height: 22px;
            margin-left: 111px;
          }
          & span:last-of-type {
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #86daf7;
            line-height: 22px;
            margin-left: 63px;
          }
        }
        & .box6_top2 {
          display: flex;
          justify-content: space-between;
          & div:first-of-type {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: rgba(255, 255, 255, 0.7);
            line-height: 16px;
          }
          & div:nth-of-type(2) {
            margin-left: 26px;
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: rgba(255, 255, 255, 0.7);
            line-height: 16px;
          }
          & div:last-of-type {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: rgba(255, 255, 255, 0.7);
            line-height: 16px;
            margin-left: 30px;
          }
        }
      }
      .box6_bottom {
        margin-top: 29px;
        // border: 1px solid red;
        height: 156px;
        display: flex;
        align-content: space-between;
        justify-content: space-between;
        flex-wrap: wrap;
        .box6_bottom_item {
          width: 211px;
          height: 46px;
          background: url(~@/assets/tsgz/stjg_k.png) no-repeat;
          background-size: 100% 100%;
          // border: 1px solid red;
          padding: 9px;
          box-sizing: border-box;
          & > div:first-of-type {
            display: flex;
            justify-content: space-between;
            span:first-of-type {
              font-size: 12px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 17px;
            }
            span:last-of-type {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              color: #fff;
              line-height: 17px;
            }
          }
          & .line {
            width: 194px;
            height: 5px;
            background: rgba(34, 73, 125, 0.5);
            margin-top: 4px;
            border-radius: 10px;
            .line_in {
              // width: 139px;
              height: 5px;
              // background: linear-gradient(270deg, #ffc3bf 0%, #e94125 100%);
              box-shadow: 0px 0px 2px 0px #f8553a;
              opacity: 0.8;
              border-radius: 34px;
            }
          }
        }
      }
    }
  }
  .left_bg {
    width: 460px;
    height: 1033px;
    background: url(~@/assets/leader/img/component/left_bg.png) no-repeat center / 100% 100%;
    position: absolute;
    left: 50px;
    top: 25px;
    z-index: 102;
  }
  .right_bg {
    width: 520px;
    height: 1033px;
    background: url(~@/assets/leader/img/component/right_bg.png) no-repeat center / 100% 100%;
    position: absolute;
    right: 50px;
    top: 25px;
    z-index: 102;
  }
  .left {
    width: 450px;
    display: flex;
    justify-content: space-between;
    margin-top: 123px;
    margin-left: 50px;
    position: relative;
    z-index: 1003;
  }
  .right {
    width: 450px;
    margin-right: 60px;
    // border: 1px solid red;
    display: flex;
    justify-content: space-between;
    margin-top: 136px;
    position: relative;
    z-index: 1003;
  }
  // .left1 , .right1{
  //   position: relative;
  //   z-index: 1100;
  // }
  .map_box {
    position: absolute;
    width: 1920px;
    height: 970px;
    top: 100px;
    left: 0;
  }
}
.jgzzBox {
  width: 109px;
  height: 170px;
  display: flex;
  flex-direction: column;
  position: absolute;
  left: 525px;
  bottom: 93px;
  z-index: 999;
  background: url(~@/assets/csts/btnBg11.png) no-repeat center / 100% 100%;
  .jgzzItem {
    width: 103px;
    height: 35px;
    margin: 2px 3px 2px 3px;
    background: url(~@/assets/csts/btn11.png) no-repeat;
    img {
      float: left;
      margin: 7px 0 0 10px;
    }
    span {
      float: left;
      font-size: 17px;
      font-family: PingFangSC, PingFang SC;
      color: #e9fffe;
      line-height: 35px;
      background: linear-gradient(180deg, #ffffff 0%, #2f8af4 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .jgzzActive {
    width: 105px;
    height: 37px;
    margin: 1px 2px 1px 2px;
    background: url(~@/assets/csts/btn12.png) no-repeat;
  }
}
.zdcsBox {
  width: 109px;
  height: 248px;
  display: flex;
  flex-direction: column;
  position: absolute;
  left: 1197px;
  bottom: 93px;
  z-index: 999;
  background: url(~@/assets/csts/btnBg12.png) no-repeat center / 100% 100%;
  .jgzzItem {
    width: 103px;
    height: 35px;
    margin: 2px 3px 2px 3px;
    background: url(~@/assets/csts/btn11.png) no-repeat;
    img {
      float: left;
      margin: 7px 0 0 10px;
    }
    span {
      float: left;
      font-size: 17px;
      font-family: PingFangSC, PingFang SC;
      color: #e9fffe;
      line-height: 35px;
      background: linear-gradient(180deg, #ffffff 0%, #2f8af4 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .jgzzActive {
    width: 105px;
    height: 37px;
    margin: 1px 2px 1px 2px;
    background: url(~@/assets/csts/btn12.png) no-repeat;
  }
}
.left,
.right {
  ::v-deep {
    .el-carousel {
      width: 450px;
      height: 920px;
      .el-carousel__indicators {
        .el-carousel__indicator {
          &.is-active {
            .el-carousel__button {
              height: 4px;
              background-color: #49e6ff;
            }
          }
          .el-carousel__button {
            height: 4px;
            background-color: rgba(255, 255, 255, 0.4);
            border-radius: 2px;
          }
        }
      }
    }
  }
}
.right {
  ::v-deep {
    .el-carousel {
      margin-left: auto;
    }
  }
}
::v-deep .el-carousel--horizontal {
  width: 450px;
  overflow-x: hidden;
}

/*先去掉默认样式*/
.swiper-button-prev:after {
  display: none;
}
.swiper-button-next:after {
  display: none;
}

/*再自定义样式*/
.swiper-button-prev {
  display: inline-block;
  width: 26px;
  height: 32px;
  background: url(~@/assets/djyl/btn-pre.png) no-repeat;
  bottom: 15px;
}
.swiper-button-next {
  display: inline-block;
  width: 26px;
  height: 32px;
  background: url(~@/assets/djyl/btn-next.png) no-repeat;
  bottom: 15px;
}

/deep/ .video-js .vjs-control {
  width: 2em;
}
/deep/ .video-js .vjs-volume-panel.vjs-volume-panel-vertical {
  width: 0em;
}
</style>
