// 时长处理
export const calculateTime = (time) => {
	let hour, minute, second
	let rest = Math.floor(time % 3600)
	if (Math.floor(time / 3600) > 0) {
		hour = Math.floor(time / 3600) <= 9 ? '0' + Math.floor(time / 3600) : Math.floor(time / 3600)
	} else {
		hour = '00'
	}
	if (Math.floor(rest / 60) > 0) {
		minute = Math.floor(rest / 60) <= 9 ? '0' + Math.floor(rest / 60) : Math.floor(rest / 60)
	} else {
		minute = '00'
	}
	if (Math.floor(rest % 60) > 0) {
		second = Math.floor(rest % 60) <= 9 ? '0' + Math.floor(rest % 60) : Math.floor(rest % 60)
	} else {
		second = '00'
	}
	return `${hour}:${minute}:${second}`
}

export const timeToMinute = (time) => {
	let minute, second
	if (Math.floor(time / 60) > 0) {
		minute = Math.floor(time / 60) <= 9 ? '0' + Math.floor(time / 60) : Math.floor(time / 60)
	} else {
		minute = '00'
	}
	if (Math.floor(time % 60) > 0) {
		second = Math.floor(time % 60) <= 9 ? '0' + Math.floor(time % 60) : Math.floor(time % 60)
	} else {
		second = '0'
	}
	if (time >= 60) {
		return `${minute}分${second}秒`
	} else {
		return `${second}秒`
	}
}

export const timeToHtml = (time) => {
	let minute, second
	if (Math.floor(time / 60) > 0) {
		minute = Math.floor(time / 60) <= 9 ? '0' + Math.floor(time / 60) : Math.floor(time / 60)
	} else {
		minute = '00'
	}
	if (Math.floor(time % 60) > 0) {
		second = Math.floor(time % 60) <= 9 ? '0' + Math.floor(time % 60) : Math.floor(time % 60)
	} else {
		second = '00'
	}
	if (time >= 60) {
		return `${minute}<span class="unit">分</span>${second}<span class="unit">秒</span>`
	} else {
		return `${second}<span class="unit">秒</span>`
	}
}
// 隐藏手机号中间4位
export const hideMobile = (mobile) => {
	return mobile.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')
}
// 校验手机号
export const validateCallNum = (identity) => {
	// let reg = /^Phone\s[0-9]+/
	let reg = /^1[345789]\d{9}$/
	return reg.test(identity)
}
// 验证用户来源
export const validateCallName = (identity) => {
	let reg = /^01[345789]\d{9}$/
	return reg.test(identity)
}

let timer = null
export function debounce(fun, wait = 200) {
	return function() {
		const argu = arguments
		if (timer) {
			clearTimeout(timer)
			timer = null
		}
		timer = setTimeout(function() {
			fun.apply(this, argu)
		}, wait)
	}
}
export class WebSocketManager {
	constructor(url, options = {}) {
		// 配置参数
		this.url = url
		this.reconnectInterval = options.reconnectInterval || 1000 // 重连基础间隔(ms)
		this.maxReconnectAttempts = options.maxReconnectAttempts || 5 // 最大重连次数
		this.heartbeatInterval = options.heartbeatInterval || 30000 // 心跳间隔(ms)
		this.heartbeatMsg = options.heartbeatMsg || '' // 心跳消息内容

		// 内部状态
		this.ws = null
		this.reconnectAttempts = 0
		this.heartbeatTimer = null
		this.reconnectTimer = null
		this.isManualClose = false // 是否手动关闭

		// 事件回调
		this.onOpen = options.onOpen || (() => {})
		this.onMessage = options.onMessage || (() => {})
		this.onClose = options.onClose || (() => {})
		this.onError = options.onError || (() => {})

		// 初始化连接
		this.connect()

		// 监听网络状态变化
		window.addEventListener('online', this.handleNetworkOnline.bind(this))
	}

	connect() {
		this.isManualClose = false
		this.ws = new WebSocket(this.url)

		this.ws.onopen = (event) => {
			this.reconnectAttempts = 0 // 重置重连计数器
			this.startHeartbeat()
			this.onOpen(event)
		}

		this.ws.onmessage = (event) => {
			// 可以在这里处理心跳响应
			this.onMessage(event)
		}

		this.ws.onclose = (event) => {
			this.stopHeartbeat()
			this.onClose(event)

			if (!this.isManualClose) {
				this.scheduleReconnect()
			}
		}

		this.ws.onerror = (error) => {
			this.onError(error)
			this.ws.close() // 触发onclose进行重连
		}
	}

	// 发送消息
	send(data) {
		if (this.ws && this.ws.readyState === WebSocket.OPEN) {
			this.ws.send(data)
		} else {
			console.error('WebSocket is not connected')
		}
	}

	// 手动关闭连接
	close() {
		this.isManualClose = true
		this.stopHeartbeat()
		if (this.ws) {
			this.ws.close()
		}
	}

	// 心跳机制
	startHeartbeat() {
		this.stopHeartbeat()

		this.heartbeatTimer = setInterval(() => {
			if (this.ws && this.ws.readyState === WebSocket.OPEN) {
				this.send(this.heartbeatMsg)
			}
		}, this.heartbeatInterval)
	}

	stopHeartbeat() {
		if (this.heartbeatTimer) {
			clearInterval(this.heartbeatTimer)
			this.heartbeatTimer = null
		}
	}

	// 重连机制
	scheduleReconnect() {
		if (this.reconnectAttempts >= this.maxReconnectAttempts) {
			console.error('Max reconnection attempts reached')
			return
		}

		if (this.reconnectTimer) {
			clearTimeout(this.reconnectTimer)
		}

		// 指数退避算法
		const delay = Math.min(
			this.reconnectInterval * Math.pow(2, this.reconnectAttempts),
			30000, // 最大延迟30秒
		)

		this.reconnectTimer = setTimeout(() => {
			this.reconnectAttempts++
			console.log(`Reconnecting (attempt ${this.reconnectAttempts})...`)
			this.connect()
		}, delay)
	}

	// 网络恢复时尝试重连
	handleNetworkOnline() {
		if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
			this.connect()
		}
	}

	// 获取当前状态
	getState() {
		return this.ws ? this.ws.readyState : WebSocket.CLOSED
	}
}
