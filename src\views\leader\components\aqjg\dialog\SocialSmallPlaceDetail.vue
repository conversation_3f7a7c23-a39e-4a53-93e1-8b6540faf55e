<template>
  <div class="dialog" v-if="show">
    <div class="title">
      <span>社会面小场所详情</span>
    </div>
    <div class="close_btn" @click="$emit('input', false)"></div>
    <div class="content"><twoColumnList :list="data1" labelWidth="150px" /></div>
  </div>
</template>

<script>
import { twoColumnList } from '../list'
export default {
  name: 'SocialSmallPlaceDetail',
  components: { twoColumnList },
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    show() {
      return this.value
    }
  },
  data() {
    return {
      data1: [
        {
          label: '单位名称: ',
          value: '南京市江宁区俏厨小吃店'
        },
        {
          label: '法人证件号码: ',
          value: '无'
        },
        {
          label: '单位地址: ',
          value: '江苏南京市江宁区江宁街道地秀路901号'
        },
        {
          label: '单位常用名称: ',
          value: '无'
        },
        {
          label: '单位联系人: ',
          value: '严强'
        },
        {
          label: '单位类型: ',
          value: '无'
        },
        {
          label: '单位联系人电话: ',
          value: '158********'
        },
        {
          label: '注册地址: ',
          value: '无'
        },
        {
          label: '统一社会信用代码: ',
          value: '92320115MA1Y081C6Y'
        },
        {
          label: '从业人数: ',
          value: '无'
        },
        {
          label: '是否无证单位: ',
          value: '否'
        },
        {
          label: '党建单位类型:',
          value: '无'
        },
        {
          label: '是否非法窝点: ',
          value: '否'
        },
        {
          label: '是否独立组建党组织: ',
          value: '否'
        },
        {
          label: '法人: ',
          value: '无'
        },
        {
          label: '审核状态: ',
          value: '已审核'
        },
        {
          label: '管理级别: ',
          value: '002'
        },
        {
          label: '重大危险源（安监）: ',
          value: '无'
        },
        {
          label: '九小场所类型: ',
          value: '小餐饮'
        },
        {
          label: '是否九小场所: ',
          value: '是'
        },
        {
          label: '单位值班电话: ',
          value: '无'
        },
        {
          label: '数据来源: ',
          value: '无'
        }
      ]
    }
  }
}
</script>

<style lang="less" scoped>
.dialog {
  width: 1035px;
  height: 600px;
  background: rgba(9, 19, 34, 0.95);
  box-shadow: 0px 0px 22px 0px rgba(11, 54, 138, 0.35), 0px 2px 8px 0px rgba(0, 0, 0, 0.7),
    inset 0px 0px 8px 0px rgba(17, 146, 214, 0.35);
  border-radius: 11px;
  border: 1px solid #015c8c;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1003;
  .title {
    margin: 0 auto 23px;
    width: 635px;
    height: 76px;
    line-height: 76px;
    background: url(~@/assets/map/dialog/title2.png) no-repeat;
    display: grid;
    place-items: center;
    span {
      font-size: 36px;
      font-family: PingFangSC, PingFang SC;
      line-height: 36px;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .close_btn {
    position: absolute;
    width: 23px;
    height: 23px;
    top: 25px;
    right: 40px;
    background: url(~@/assets/map/dialog/btn3.png) no-repeat;
    cursor: pointer;
  }
}
</style>
