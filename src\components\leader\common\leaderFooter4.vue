<template>
  <footer>
    <ul>
      <li
        v-for="(it, i) of btns"
        :key="i"
        :class="{ active: activeList.findIndex((item) => item === i) !== -1 }"
      >
        <img
          :src="activeList.findIndex((item) => item === i) !== -1 ? it.activeBg : it.normalBg"
          alt
        />
        <span style="font-family: YouSheBiaoTiHei;" @click="handleClick(i)">{{ it.name }}</span>
        <div class="popups" v-show="false"></div>
      </li>
    </ul>
  </footer>
</template>
  
  <script>
export default {
  name: 'LeaderFooter',
  props: {
    btns: {
      type: Array,
      default() {
        return [
          {
            normalBg: require('@/assets/csts/n_btn11.png'),
            activeBg: require('@/assets/csts/a_btn11.png'),
            name: '机构组织',
            children: [{}],
          },
          {
            normalBg: require('@/assets/csts/n_btn12.png'),
            activeBg: require('@/assets/csts/a_btn12.png'),
            name: '人员力量',
          },
          {
            normalBg: require('@/assets/csts/n_btn13.png'),
            activeBg: require('@/assets/csts/a_btn13.png'),
            name: '视频监控',
          },
          {
            normalBg: require('@/assets/csts/n_btn14.png'),
            activeBg: require('@/assets/csts/a_btn14.png'),
            name: '城市部件',
          },
          {
            normalBg: require('@/assets/csts/n_btn15.png'),
            activeBg: require('@/assets/csts/a_btn15.png'),
            name: '城市事件',
          },
          {
            normalBg: require('@/assets/csts/n_btn16.png'),
            activeBg: require('@/assets/csts/a_btn16.png'),
            name: '重点场所',
          },
        ]
      },
    },
  },
  data() {
    return {
      activaIdx: 0,
      activeList: [],
    }
  },
  methods: {
    handleClick(i) {
      const markIndex = this.activeList.findIndex((item) => item === i)
      console.log(markIndex)
      if (markIndex == -1) {
        this.activeList.push(i)
      } else {
        this.activeList.splice(markIndex, 1)
      }
      console.log(this.activeList)
      this.$emit('mark', i, this.activeList)
      // if (i === this.activaIdx) {
      //   this.activaIdx = -1
      //   this.$emit('mark', -1)
      // } else {
      //   this.$emit('mark', i)
      //   this.activaIdx = i
      // }
    },
  },
}
</script>
  
  <style lang="less" scoped>
footer {
  width: 160px;
  position: absolute;
  top: 201px;
  left: 504px;
  z-index: 1003;
  ul {
    display: flex;
    flex-direction: column;
    li {
      width: 160px;
      height: 40px;
      margin: 4px 0;
      background: url(~@/assets/bjnj/btn1.png) no-repeat center / 100% 100%;
      cursor: pointer;
      span {
        margin-left: 4px;
        font-family: PingFangSC, PingFang SC;
        font-size: 20px;
        color: #ffffff;
        line-height: 40px;
        text-align: left;
        font-style: normal;
        background: linear-gradient(180deg, #ffffff 0%, #009fff 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        cursor: pointer;
      }
      img {
        vertical-align: middle;
        margin-top: -10px;
      }
      &.active {
        width: 160px;
        height: 40px;
        margin: 4px 0;
        background: url(~@/assets/bjnj/btn2.png) no-repeat center / 100% 100%;
        span {
          margin-left: 4px;
          font-size: 20px;
          color: #ffffff;
          line-height: 40px;
          text-align: left;
          font-style: normal;
          background: linear-gradient(180deg, #ffffff 0%, #ffca00 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          cursor: pointer;
        }
        img {
          vertical-align: middle;
          margin-top: -10px;
        }
      }
      .popups {
        position: absolute;
        left: 50%;
        top: -264px;
        transform: translateX(-50%);
        width: 156px;
        height: 243px;
        background: url(~@/assets/csts/popups.png) no-repeat;
        transition: all 2s;
      }
    }
  }
  .footerImg {
    width: 1920px;
    height: 24px;
    background: url(~@/assets/img/footer_bg2.png) no-repeat center / cover;
    position: absolute;
    bottom: 0;
  }
}
</style>
  