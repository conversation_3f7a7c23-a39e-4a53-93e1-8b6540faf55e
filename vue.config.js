var CopyWebpackPlugin = require('copy-webpack-plugin')

module.exports = {
	outputDir: 'ywtghs-dist',
	publicPath: './',
	lintOnSave: false,
	devServer: {
		overlay: {
			warnings: false,
			errors: false,
		},
		host: '0.0.0.0', // 设置为0.0.0.0则所有的地址均能访问
		port: 8080,
		https: true,
		hotOnly: false,
		proxy: {
			'/api': {
				target: 'http://*************/sjczcq', //API服务器的地址
				changeOrigin: true, // 是否跨域，虚拟的站点需要更管origin
			},
		},
	},
	productionSourceMap: false,
	configureWebpack: {
		plugins: [
			new CopyWebpackPlugin(
				// {
				// patterns: [
				//   { from: 'node_modules/@liveqing/liveplayer/dist/component/crossdomain.xml' },
				//   { from: 'node_modules/@liveqing/liveplayer/dist/component/liveplayer.swf' },
				//   { from: 'node_modules/@liveqing/liveplayer/dist/component/liveplayer-lib.min.js', to: 'js/' }
				// ],
				// }
				[
					{ from: 'node_modules/@liveqing/liveplayer/dist/component/crossdomain.xml' },
					{ from: 'node_modules/@liveqing/liveplayer/dist/component/liveplayer.swf' },
					{
						from: 'node_modules/@liveqing/liveplayer/dist/component/liveplayer-lib.min.js',
						to: 'js/',
					},
				],
			),
		],
		externals: {
			AMap: 'AMap',
			'mars3d-cesium': 'Cesium',
		},
	},
	chainWebpack(config) {
		config.externals({ './cptable': 'var cptable' })
	},
	// './cptable': 'var cptable',
}
