<template>
  <div class="ai_waring">
    <div class="title">
      <span>南京市政务服务中心</span>
      <img @click="closeEmit" class="close_btn" src="@/assets/shzl/map/close_btn.png" alt="" />
    </div>
    <div class="content">
      <div><span>部件名称：</span><span>空气质量监测站（莫愁湖街道）</span></div>
      <div><span>主管部门：</span><span>南京市生态环境局</span></div>
      <div><span>部件类型：</span><span>市容环境设施</span></div>
      <div><span>状态：</span><span>在线</span></div>
    </div>
    <div class="tb">
      <div class="table">
        <div class="tableItem" :class="tableActive == 1 ? 'tableActive' : ''" @click="table(1)">
          <div class="tableName">PM2.5</div>
          <div class="tableNum">19</div>
        </div>
        <div class="tableItem" :class="tableActive == 2 ? 'tableActive' : ''" @click="table(2)">
          <div class="tableName">PM10</div>
          <div class="tableNum">23</div>
        </div>
      </div>
      <div class="sbjajyj2content">
        <GroupColChart :data="qyeqData2" :options="qyeqOptions2"></GroupColChart>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name:'wgyPop',
  data() {
    return {
      tableActive: 1,
      qyeqData2: [
        ['product', ''],
        ['09:00', 200],
        ['12:00', 140],
        ['15:00', 102],
        ['18:00', 200],
        ['19:00', 150]
      ],
      qyeqOptions2: {
        color: ['#00A3D7'],
        stack: true,
        isGradient: true,
        barMaxWidth: 10,
        gradientColors: [
          ['#00A3D7', '#0080ED'],
        ],
        isHor: false,
        barGap: '30%',
        tooltip: {
          color: '#fff',
          show: true,
          type: 'shadow'
        },
        yAxis: {
          name: '收入:千元',
        },
      },
    }
  },
  methods: {
    closeEmit() {
      this.$emit('closeEmit')
    },
    table(index) {
      this.tableActive = index
    }
  },
}
</script>

<style lang='less' scoped>
.ai_waring {
  width: 413px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  // border: 1px solid red;
  background: url(~@/assets/shzl/map/ai_popBg.png) no-repeat center / 100% 100%;
  background-size: 100% 100%;
  z-index: 999;
  .title {
    background: url(~@/assets/shzl/map/title_bg.png) no-repeat center / 100% 100%;
    background-size: 100% 100%;
    width: 375px;
    height: 47px;
    margin: 20px auto 0;
    line-height: 47px;
    text-align: left;
    text-indent: 27px;
    font-size: 18px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #ffffff;
    position: relative;
    span {
      background: linear-gradient(180deg, #ffffff 0%, #2aa6ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .close_btn {
      position: absolute;
      top: 11px;
      right: 25px;
      cursor: pointer;
    }
  }
  .content {
    padding: 9px 50px 0 48px;
    & > div {
      display: flex;
      justify-content: space-between;
      &:not(:last-of-type) {
        margin-bottom: 14px;
      }
      & span:first-of-type {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
      }
      & span:last-of-type {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
      }
    }
  }
  .tb {
    padding: 19px 50px 38px 48px;
    .table {
      width: 200px;
      height: 58px;
      margin: 0 auto;
      .tableItem {
        width: 92px;
        height: 50px;
        margin: 0 3.5px;
        float: left;
        background: url(~@/assets/csts/tableBg1.png) no-repeat center / 100% 100%;
        .tableName {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 20px;
          margin-top: 3px;
        }
        .tableNum {
          font-size: 20px;
          font-family: DIN-BlackItalic, DIN;
          font-weight: normal;
          color: #FFFFFF;
          line-height: 24px;
          margin-top: -3px;
        }
      }
      .tableActive {
        height: 58px;
        background: url(~@/assets/csts/tableBg2.png) no-repeat center / 100% 100%;
      }
    }
    .sbjajyj2content {
      width: 100%;
      height: 200px;
    }
  }
}
</style>