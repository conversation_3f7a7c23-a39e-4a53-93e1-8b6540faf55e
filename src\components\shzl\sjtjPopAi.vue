<template>
<div>
  <div class="ai_waring">
    <div class="title">
      <div><span>AI预警事件</span></div>
      <img @click="closeEmitai" class="close_btn" src="@/assets/shzl/map/close_btn.png" alt="" />
    </div>
    <div class="content">
      <div class="btns">
        <div
          @click="activaIdx = index"
          v-for="(item, index) in btnsArr"
          :key="index"
          :style="{
            background:
              activaIdx === index
                ? `url(${item.activeBg}) no-repeat center`
                : `url(${item.normalBg}) no-repeat center`,
          }"
        >
          <span>{{ item.name }}</span>
        </div>
      </div>
      <div class="check_types">
        <div class="jjcd">
          <span>紧急程度：</span>
          <Select v-model="proityValue" style="width: 200px">
            <Option v-for="item in proityOptions" :value="item.value" :key="item">{{
              item.label
            }}</Option>
          </Select>
        </div>
        <div class="report_time">
          <span>上报时间：</span>
          <Date-picker
            type="daterange"
            placement="bottom-end"
            placeholder="选择日期"
            style="width: 200px"
          ></Date-picker>
        </div>
        <div class="istime_out">
          <span>是否超时：</span>
          <Select v-model="timeOutValue" style="width: 200px">
            <Option v-for="item in timeOutOptions" :value="item.value" :key="item">{{
              item.label
            }}</Option>
          </Select>
        </div>
        <div class="type_btns">
          <div>重置</div>
          <div>搜索</div>
        </div>
      </div>
      <div class="table_box">
        <SwiperTableAi
          v-if="activaIdx == 0"
          style="margin: 20px 0 0 0"
          :titles="['序号', '识别场景', '设备名称', '设备位置', '所属网格', '识别时间', '操作']"
          :widths="['10%', '10%', '12%', '16%', '16%', '16%', '26%']"
          :data="sjtjData"
          :contentHeight="'538px'"
          :tabelHeight="'59px'"
        ></SwiperTableAi>
        <SwiperTableAiFinish
          v-else
          style="margin: 20px 0 0 0"
          :titles="['序号', '识别场景', '设备名称', '设备位置', '所属网格', '识别时间', '处理时间']"
          :widths="['10%', '10%', '12%', '16%', '20%', '16%', '26%']"
          :data="sjtjData1"
          :contentHeight="'538px'"
          :tabelHeight="'59px'"
        ></SwiperTableAiFinish>
      </div>
      <div class="fy_page">
        <Page :total="100"></Page>
      </div>
    </div>
  </div>
  <transition name="fade">
    <div
      class="bg-header"
    ></div>
  </transition>
</div>
</template>

<script>
import SwiperTableAi from '@/components/shzl/SwiperTableAi.vue'
import SwiperTableAiFinish from '@/components/shzl/SwiperTableAiFinish.vue'

export default {
  name: 'sjtjPop',
  components: { SwiperTableAi, SwiperTableAiFinish },
  data() {
    return {
      btnsArr: [
        {
          name: '未处理',
          normalBg: require('@/assets/shzl/map/left_n.png'),
          activeBg: require('@/assets/shzl/map/left_a.png'),
        },
        {
          name: '已处理',
          normalBg: require('@/assets/shzl/map/right_n.png'),
          activeBg: require('@/assets/shzl/map/right_a.png'),
        },
      ],
      activaIdx: 0,
      sjtjData: [
        [
          '1',
          '人群聚集',
          'JKSB-8394845-01',
          '鼓楼区汉中路与牌楼巷交叉口',
          '鼓楼区网格一',
          '2022-08-08 07:57:49',
        ],
        [
          '2',
          '人员打架',
          'JKSB-8394845-01',
          '鼓楼区汉中门广场',
          '鼓楼区网格四',
          '2022-08-10 07:57:49',
        ],
      ],
      sjtjData1: [
        [
          '1',
          '人群聚集',
          'JKSB-8394845-01',
          '鼓楼区汉中路与牌楼巷交叉口',
          '鼓楼区网格一',
          '2022-08-08 07:57:49',
          '2022-08-09 07:57:49',
        ],
        [
          '2',
          '人员打架',
          'JKSB-8394845-01',
          '鼓楼区汉中门广场',
          '鼓楼区网格四',
          '2022-08-10 07:57:49',
          '2022-08-11 07:57:49',
        ],
      ],
      proityOptions: [
        {
          value: '0',
          label: '一般',
        },
        {
          value: '1',
          label: '紧急',
        },
        //  {
        //     value: '2',
        //     label: '紧急',
        //   },
      ],
      timeOutOptions: [
        {
          value: '0',
          label: '是',
        },
        {
          value: '1',
          label: '否',
        },
      ],
      proityValue: '',
      timeOutValue: '',
    }
  },
  methods: {
    closeEmitai() {
      this.$emit('closeEmitai')
    },
    AidealMethod() {
      this.$parent.AidealMethod()
    },
    AispthMethod() {
      this.$parent.AispthMethod()
    },
    AirwzpMethod() {
      this.$parent.AirwzpMethod()
    },
  },
}
</script>

<style lang='less' scoped>
 .bg-header {
    position: absolute;
    width: 100%;
    height: 1080px;
    background: rgba(2, 14, 50, 0.75);
    z-index: 999999;
    top:0;
    left:0;
  }
/deep/ .ivu-select-selection {
  background: rgba(0, 74, 143, 0.4)
    linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
}
/deep/ .ivu-select-selection .ivu-select-selected-value {
  font-size: 16px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 22px;
}

/deep/ .ivu-input {
  background: rgba(0, 74, 143, 0.4)
    linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
  font-size: 16px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 22px;
}
/deep/ .ivu-select-placeholder {
  font-size: 16px !important;
}
/deep/ .ivu-page-item {
  background: transparent;
  color: #fff;
}
/deep/ .ivu-page-item a {
  color: #fff;
}
/deep/ .ivu-page-prev,
/deep/ .ivu-page-next {
  background: transparent;
}
.ai_waring {
  width: 1408px;
  height: 900px;
  background: rgba(0, 23, 59, 1);
  box-shadow: 0px 0px 43px 0px rgba(11, 54, 138, 0.35), 0px 3px 16px 0px rgba(0, 0, 0, 0.7),
    inset 0px 0px 16px 0px rgba(17, 146, 214, 0.35);
  border-radius: 11px;
  border: 1px solid #015c8c;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  // border: 1px solid red;
  z-index: 99999999;
  .title {
    & > div {
      margin-left: 387px;
      width: 634px;
      height: 74px;
      background: url(~@/assets/shzl/sjtj_titlebg.png) no-repeat center / 100% 100%;
      background-size: 100% 100%;
    }
    span {
      font-size: 40px;
      font-family: YouSheBiaoTiHei;
      color: #ffffff;
      line-height: 74px;
      background: linear-gradient(180deg, #ffffff 0%, #2aebff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .close_btn {
      width: 44px;
      height: 44px;
      position: absolute;
      top: 16px;
      right: 25px;
      cursor: pointer;
    }
  }
  .content {
    // border: 1px solid red;
    padding: 27px 51px 36px 63px;
    .btns {
      // border: 1px solid red;
      margin: 0 auto;
      display: flex;
      justify-content: center;
      & > div {
        width: 154px;
        height: 53px;
        span {
          font-size: 24px;
          font-family: YouSheBiaoTiHei;
          color: #ffffff;
          line-height: 53px;
          background: linear-gradient(180deg, #ffffff 0%, #ffeba0 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
    .check_types {
      margin-top: 32px;
      text-align: left;
      display: flex;
      .jjcd {
        display: flex;
        align-items: center;
        span {
          font-size: 16px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
      }
      .report_time {
        margin-left: 26px;
        display: flex;
        align-items: center;
        span {
          font-size: 16px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
      }
      .istime_out {
        margin-left: 26px;
        align-items: center;
        display: flex;
        span {
          font-size: 16px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
      }
      .type_btns {
        margin-left: 46px;
        display: flex;
        align-items: center;
        & div {
          font-size: 16px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 43px;
          background: url(~@/assets/shzl/map/type_btn.png) no-repeat center / 100% 100%;
          background-size: 100% 100%;
          text-align: center;
          &:first-of-type {
            width: 98px;
            height: 43px;
          }
          &:nth-of-type(2) {
            margin-left: 20px;
            width: 98px;
            height: 43px;
          }
          &:nth-of-type(3) {
            margin-left: 20px;
            width: 98px;
            height: 43px;
          }
        }
      }
    }
    .table_box {
      margin-top: 30px;
      overflow: hidden;
    }
    .fy_page {
      margin-top: 30px;
    }
  }
}
</style>