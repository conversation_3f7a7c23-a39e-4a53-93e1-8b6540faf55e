<template>
  <div class="middle_box">
    <div class="line">
      <div
        class="line_box"
        v-for="(item, index) in middleList"
        :key="index"
        :style="{ backgroundImage: 'url(' + bgArr[index] + ')' }"
      >
        <span class="num_" :style="{ color: colorArr[index] }">{{ item.num }}</span>
        <div class="info_">
          <div v-if="!item.percent" class="no_percent">
            <img class="font_icon" v-if="iconArr[index]" :src="iconArr[index]" alt="" />
            <p>
              {{ item.name }}
              <span>{{ item.percent }}</span>
            </p>
          </div>
          <div v-if="item.percent" class="has_percent">
            <p>
              {{ item.name }}
              <span>{{ item.percent }}</span>
              <img
                v-if="item.sign == 'down'"
                class="b_icon"
                src="@/assets/leader/img/leader_ts/down.png"
                alt=""
              />
              <img
                v-if="item.sign == 'up'"
                class="b_icon"
                src="@/assets/leader/img/leader_ts/up.png"
                alt=""
              />
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    colorArr: {
      type: Array,
      default: () => ['#00A0FF', '#00DEFF', '#FF6600', '#00C726'],
    },
    iconArr: {
      type: Array,
      default: () => [
        require('@/assets/leader/img/component/middle/leader_middle_icon1.png'),
        require('@/assets/leader/img/component/middle/leader_middle_icon2.png'),
        require('@/assets/leader/img/component/middle/leader_middle_icon3.png'),
        require('@/assets/leader/img/component/middle/leader_middle_icon4.png'),
      ],
    },
    middleList: {
      type: Array,
      default: () => [
        {
          name: '事件上报数量',
          unit: '件',
          num: 138,
        },
        {
          name: '事件办结数量',
          unit: '件',
          num: 112,
        },
        {
          name: '未办结数量',
          unit: '件',
          num: 2,
        },
        {
          name: '按时完成率',
          unit: '%',
          num: '91.4%',
        },
      ],
    },
    bgArr: {
      type: Array,
      default: () => [
        require('@/assets/leader/img/component/middle/middle_item_bg1.png'),
        require('@/assets/leader/img/component/middle/middle_item_bg2.png'),
        require('@/assets/leader/img/component/middle/middle_item_bg3.png'),
        require('@/assets/leader/img/component/middle/middle_item_bg4.png'),
      ],
    },
  },
  data() {
    return {}
  },
}
</script>

<style lang="scss" scoped>
.middle_box {
  position: absolute;
  top: 116px;
  // left: 533px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
  .line {
    // width: 858px;
    display: flex;
    // border: 1px solid red;
    justify-content: space-between;
    .line_box {
      width: 165px;
      height: 99px;
      background-size: 100% 100%;
      &:not(:last-of-type) {
        margin-right: 10px;
      }
      .num_ {
        font-size: 36px;
        font-family: DINCond-Black, DINCond;
        font-weight: 900;
        color: #00a0ff;
        text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
        line-height: 62px;
      }
      .info_ {
        display: flex;
        // padding: 0 0 0 28px;
        align-items: center;
        justify-content: center;
        & > div {
          display: flex;
          align-items: center;
          .font_icon {
            width: 22px;
            height: 22px;
          }
          p {
            font-size: 16px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            margin-left: 10px;
            display: flex;
            align-items: center;
            .b_icon {
              width: 12px;
              height: 16px;
            }
          }
        }
        .has_percent {
          width: 100%;
          text-align: center;
          padding: 0 6px;
          p {
            margin: 0 auto;
            span {
              margin-left: 12px;
            }
          }
        }
      }
    }
  }
}
</style>