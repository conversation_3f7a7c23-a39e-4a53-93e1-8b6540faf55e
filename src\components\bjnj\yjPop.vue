<template>
  <div class="container">
    <div class="head">
      <span class="text">预警详情</span>
      <img @click="close" src="@/assets/mskx/icon_close.png" alt />
    </div>
    <div class="content">
      <div class="title">
        <p>山东省荷泽市巨野县气象台发布暴雨黄色预警</p>
      </div>
      <div class="yj_type">
        <div class="type_item" v-for="(item,index) in typeData" :key="index">
          <span class="name">{{ item.name }}</span>
          <span class="value">{{ item.value }}</span>
        </div>
      </div>
      <div class="text_box">
        <p>12日上午我市将出现一次较强降水天气过程，主要降受高空槽及低层切变线共同影响，预计11-1水、雷暴大风等强对流天气。过程累计降水量为水时段出现在11日傍晚至夜间，伴有短时强队15~35毫米，南部大于北部，局部地区可能达到 50 毫米以上，最大小时雨量 20~40 毫米。本次降水不利于大田作物玉米、大豆、小麦作物收割工作。但降水伴有短时强降水、雷暴大为涝、山洪、地质灾害等次生灾害，要提前做好灾风等强对流天气，局部地区雨量大，易引发害。</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  props: {},
  data () {
    return {
      typeData: [
        {
          name: '预警类型：',
          value: '暴雨预警'
        },
        {
          name: '预警时间：',
          value: '2023-12-23'
        },
        {
          name: '预警区域：',
          value: '江苏省南京市浦口区'
        },
        {
          name: '预警等级：',
          value: '黄色预警'
        }
      ]
    }
  },
  created () { },
  mounted () { },
  watch: {},
  computed: {},
  methods: {
    close () {
      this.$emit('close')
    }
  },
};
</script>
<style lang="less" scoped>
.container {
  width: 683px;
  height: 412px;
  background: url(~@/assets/mskx/bg_yj.png) no-repeat;
  background-size: 100% 100%;
  z-index: 1006;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 20px;
  .head {
    width: 643px;
    height: 47px;
    background: url(~@/assets/mskx/bg_title.png) no-repeat;
    background-size: 100% 100%;
    color: #ffffff;
    .text {
      margin-left: 30px;
      float: left;
      font-size: 22px;
      font-family: PingFangSC, PingFang SC;
      line-height: 47px;
      background: linear-gradient(180deg, #ffffff 0%, #01c3ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    img {
      float: right;
      width: 24px;
      height: 24px;
      margin: 12px 12px 0 0;
      cursor: pointer;
    }
  }
  .content {
    padding: 20px;

    .title {
      margin-bottom: 13px;
      width: 100%;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 20px;
      color: #ffffff;
      line-height: 28px;
      text-align: left;
      font-style: normal;
      background: linear-gradient(180deg, #ffffff 0%, #00d4ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-align: center;
    }
    .yj_type {
      width: 60%;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin: 0 auto;
      .type_item {
        width: 50%;
        text-align: left;
        .name {
          height: 17px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #ffffff;
          line-height: 17px;
          text-align: center;
          font-style: normal;
        }
        .value {
          height: 17px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #ffffff;
          line-height: 17px;
          text-align: center;
          font-style: normal;
        }
      }
    }

    .text_box {
      margin-top: 23px;
      p {
        font-family: PingFangSC, PingFang SC;
        font-weight: 300;
        font-size: 14px;
        color: #ffffff;
        line-height: 26px;
        text-align: left;
        font-style: normal;
      }
    }
  }
}
</style>