body .left {
	/* padding: 0 0 0 39px;
  width: 520px;
  position: relative;
  top: 0;
  left: 0;
  z-index: 2;
  background: url(~@/assets/leader/img/component/leftBg.png) center/100% 100%;
  background-size: 100% 100%; */
	/* border: 1px solid red; */
}
body .right {
	/* padding: 0 40px 0 ;
  width: 520px;
  position: relative;
  top: 0;
  right: 0;
  z-index: 2;
  background: url(~@/assets/leader/img/component/rightBg.png) center/100% 100%;
  background-size: 100% 100%; */
	/* border: 1px solid red; */
}
body .leader_box {
	/* width: 3840px; */
	width: 1920px;
	height: 1080px;
	position: relative;
	/* background: RGBA(0, 3, 35, 1); */
	background-image: url('~@/assets/img/map-theme1-bg-1.png');
	/* 背景不重复 */
	background-repeat: no-repeat;
	/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
	background-size: cover;
	/* 背景居中显示（可选） */
	background-position: center;
}

.textMarker {
	width: 80px;
	height: 80px;
	text-align: center;
	line-height: 30px;
	color: aliceblue;
	background: url('~@/assets/map/qy-bg.png') no-repeat;
	background-size: 100%;
	padding: 0px 0px 0px 0px;
}
.textName {
	font-weight: bold;
}
.el-message {
	z-index: 10000 !important;
}
.el-image-viewer__wrapper {
	z-index: 10000 !important;
}
.el-image-viewer__close {
	top: 130px !important;
	right: 80px !important;
}

.number-marker-out {
	/* width: 50px;
  height: 50px;
  line-height: 50px !important; 
	border-radius: 25px; 
  */
	/* width: 88px;
	height: 155px; */
	width: 58px;
	height: 53px;
	font: 12px 'Helvetica Neue', Arial, Helvetica, sans-serif;
	/* background: url('~@/assets/img/map/number-marker-bg.png') no-repeat; */
	/* background: url('~@/assets/img/map/number-marker-bg1.png') no-repeat; */
	background: url('~@/assets/img/map/number-marker-bg2.png') no-repeat;


	/* 背景不重复 */
	background-repeat: no-repeat;
	/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
	background-size: cover;
	/* 背景居中显示（可选） */
	background-position: center;
	/* background-color: rgba(128, 197, 88, 0.48); */
	display: flex;
	align-items: flex-start;
	justify-content: flex-start;
}

.number-marker-in {
	/* width: 40px;
	height: 40px;
	line-height: 40px !important; */

	width: 100%;
	height: 25px;
	line-height: 25px !important;
	padding-left: 10px;
	

	font-family: DINPro, DINPro;
	font-weight: bold;
	font-size: 12px;
	color: #ffffff;
	font-style: normal;

	display: flex;
	justify-content: flex-start;
	align-items: center;
}

.marker-cluster-large {
	width: 50px !important;
	height: 50px !important;
	border-radius: 25px;
}
.marker-cluster-large div {
	width: 40px !important;
	height: 40px !important;
	border-radius: 20px;
}
.marker-cluster-large div span {
	display: inline-block !important;
	width: 40px !important;
	height: 40px !important;
	line-height: 40px !important;
}

.custom-cluster {
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
}
.custom-cluster-large {
	line-height: 50px;
}

.custom-cluster-medium {
	line-height: 40px;
}

.custom-cluster-small {
	line-height: 30px;
}

.marker-zyzx {
	width: 100%;
	height: 100%;
	color: #fff;
	text-align: left;
	font-size: 12px;
	font-weight: bolder;
	padding: 10px 0 0 65px;
}
