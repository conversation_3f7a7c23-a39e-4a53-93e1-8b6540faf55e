<template>
  <div class="leader_box">
    <div class="leader_sjts">
      <!-- <div class="left_bg"></div>
      <div class="right_bg"></div>-->
      <div class="left">
        <div class="left1">
          <BlockBox
            title="湖熟简介"
            subtitle="Introduction to Hushu"
            class="box"
            :isListBtns="false"
            :blockHeight="396"
          >
            <div class="wrapper1">
              <div class="introduce_video">
                <LivePlayer
                  class="video_"
                  :videoUrl="introUrl"
                  fluent
                  autoplay
                  live
                  stretch
                ></LivePlayer>
              </div>
              <div class="introduce">
                <p>
                  {{ introWord }}
                </p>
              </div>
            </div>
          </BlockBox>
          <BlockBox
            title="经济概览"
            subtitle="Economic Overview"
            class="box box1"
            :isListBtns="false"
            :blockHeight="272"
          >
            <ul class="cont">
              <li v-for="(it, i) of data1" :key="i">
                <div class="icon">
                  <img :src="it.url" alt />
                </div>
                <div class="line"></div>
                <div class="desc">
                  <span class="xsj"></span>
                  <div class="num">
                    <countTo
                      ref="countTo"
                      :startVal="$countTo.startVal"
                      :decimals="$countTo.decimals(it.num)"
                      :endVal="it.num"
                      :duration="$countTo.duration"
                    />
                  </div>
                  <div class="tit">{{ it.tit }}</div>
                </div>
              </li>
            </ul>
          </BlockBox>
          <BlockBox
            title="空气质量"
            subtitle="Air quality"
            class="box box9"
            :isListBtns="false"
            :blockHeight="216"
          >
            <div class="cont">
              <div class="detail">
                <img class="center_out" src="@/assets/shzl/sjzl_out.png" alt />
                <img class="center_in" src="@/assets/shzl/sjzl_in.png" alt />
                <ul class="fs">
                  <li v-for="(it, i) of data9_1" :key="i">
                    <div class="num">
                      <countTo
                        ref="countTo"
                        :startVal="$countTo.startVal"
                        :decimals="$countTo.decimals(it.num)"
                        :endVal="it.num"
                        :duration="$countTo.duration"
                      />ug/m³
                    </div>
                    <div class="lab">{{ it.lab }}</div>
                  </li>
                </ul>
                <ul class="fq">
                  <li v-for="(it, i) of data9_2" :key="i">
                    <div class="lab">{{ it.lab }}</div>
                    <div class="num">
                      <countTo
                        ref="countTo"
                        :startVal="$countTo.startVal"
                        :decimals="$countTo.decimals(it.num)"
                        :endVal="it.num"
                        :duration="$countTo.duration"
                      />ug/m³
                    </div>
                  </li>
                </ul>
                <div class="zdqy">
                  <img src="@/assets/hszl/icon7.png" alt />
                  <div class="lab">空气质量</div>
                </div>
              </div>
            </div>
          </BlockBox>
          <!-- <BlockBox
            title="土地使用"
            subtitle="Land use"
            class="box"
            :isListBtns="false"
            :blockHeight="213"
          >
            <LandUse />
          </BlockBox>-->
        </div>
      </div>
      <div class="right">
        <div class="right1">
          <BlockBox
            title="党建信息"
            subtitle="Party Building Information"
            class="box box7"
            :isListBtns="false"
            :blockHeight="243"
          >
            <div class="wrapper4">
              <ul>
                <li v-for="(it, i) of data4" :key="i">
                  <div class="icon" :style="{ background: `url(${it.icon}) no-repeat` }"></div>
                  <div class="info">
                    <div class="label">{{ it.label }}</div>
                    <div class="line"></div>
                    <div class="count">
                      <span>{{ it.count }}</span
                      >个
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </BlockBox>
          <BlockBox
            title="人口概览"
            subtitle="Population Survey"
            class="box box8"
            :isListBtns="false"
            :blockHeight="409"
          >
            <div class="wrapper5">
              <div class="info">
                <div class="total">
                  <div class="cont">
                    <div class="label">人口总数</div>
                    <div class="value">
                      {{ peoTotal }}
                      <span>人</span>
                    </div>
                  </div>
                </div>
                <div class="counts">
                  <div class="men">
                    <div class="count">
                      {{ maleNum }}
                      <span>人</span>
                    </div>
                    <div class="chart">
                      <span
                        v-for="(it, i) of 10"
                        :key="i"
                        :style="{
                          backgroundImage:
                            i <= (maleNumRate/10)-1 ? `url(${man_normal})` : `url(${man_active})`,
                        }"
                      ></span>
                    </div>
                    <div class="count">
                      {{ maleNumRate }}
                      <span>%</span>
                    </div>
                  </div>
                  <div class="women">
                    <div class="count">
                      {{ femaleNum }}
                      <span>人</span>
                    </div>
                    <div class="chart">
                      <span
                        v-for="(it, i) of 10"
                        :key="i"
                        :style="{
                          backgroundImage:
                            i >= femaleNumRate / 10
                              ? `url(${woman_normal})`
                              : `url(${woman_active})`,
                        }"
                      ></span>
                    </div>
                    <div class="count">
                      {{ femaleNumRate }} 
                      <span>%</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="chart_wrap">
                <PieChart3D
                  type="1"
                  :data="data8.data"
                  :options="data8.options"
                  :init-option="{
                    title: {
                      x: -60,
                    },
                    subtitle: {
                      x: -60,
                    },
                  }"
                />
                <!-- <div class="sign">
                  <div class="man"></div>
                  <div class="woman"></div>
                  <div class="man_count">{{ maleNum }}</div>
                  <div class="woman_count">{{ femaleNum }}</div>
                </div> -->
              </div>
            </div>
          </BlockBox>
          <BlockBox
            title="全要素网格事件"
            subtitle="Full Factor Grid Events"
            class="box box6"
            :isListBtns="false"
            :blockHeight="230"
          >
            <div class="cont">
              <div class="select_month">
                <!-- <el-dropdown > -->
                <el-button class="select_month" type="primary">
                  {{ wgcurrentMonth }}月
                </el-button>
                <!-- <el-dropdown-menu class="select_month" slot="dropdown">
                <el-dropdown-item v-for="item in wgsjmonthList" :key="item" :command="item"
                  >{{ item }}月</el-dropdown-item
                >
                </el-dropdown-menu>
                </el-dropdown> -->
              </div>
              <FullFactorGridEvents :total="qyswgTotal" :chartData="chartData" />
            </div>
          </BlockBox>
          <!-- <BlockBox
            title="街道概览"
            subtitle="Street Survey"
            class="box"
            :isListBtns="false"
            :blockHeight="181"
          >
            <StreetSurvey />
          </BlockBox>-->
        </div>
      </div>
    </div>
    <div class="map_box">
      <zlBg @emitMenu="emitMenu" />
      <svgMap />
    </div>
    <!-- <leaderMiddle /> -->
    <LeaderFooter :btns="[]" :activaIdx="activaIdx" @mark="mark" />
    <div class="jgzzBox" v-if="jgzzShow">
      <div class="jgzzItem" :class="jgzzActive == 0 ? 'jgzzActive' : ''" @click="jgzz(0)">
        <img src="@/assets/csts/img111.png" alt v-if="jgzzActive == 0" />
        <img src="@/assets/csts/img101.png" alt v-else />
        <span>行政区域</span>
      </div>
      <div class="jgzzItem" :class="jgzzActive == 1 ? 'jgzzActive' : ''" @click="jgzz(1)">
        <img src="@/assets/csts/img112.png" alt v-if="jgzzActive == 1" />
        <img src="@/assets/csts/img102.png" alt v-else />
        <span>党组织</span>
      </div>
      <div class="jgzzItem" :class="jgzzActive == 2 ? 'jgzzActive' : ''" @click="jgzz(2)">
        <img src="@/assets/csts/img113.png" alt v-if="jgzzActive == 2" />
        <img src="@/assets/csts/img103.png" alt v-else />
        <span>政务中心</span>
      </div>
      <div class="jgzzItem" :class="jgzzActive == 3 ? 'jgzzActive' : ''" @click="jgzz(3)">
        <img src="@/assets/csts/img114.png" alt v-if="jgzzActive == 3" />
        <img src="@/assets/csts/img104.png" alt v-else />
        <span>指挥中心</span>
      </div>
    </div>
    <div class="zdcsBox" v-if="zdcsShow">
      <div class="jgzzItem" :class="zdcsActive == 0 ? 'jgzzActive' : ''" @click="zdcs(0)">
        <img src="@/assets/csts/img131.png" alt v-if="zdcsActive == 0" />
        <img src="@/assets/csts/img121.png" alt v-else />
        <span>重点企业</span>
      </div>
      <div class="jgzzItem" :class="zdcsActive == 1 ? 'jgzzActive' : ''" @click="zdcs(1)">
        <img src="@/assets/csts/img132.png" alt v-if="zdcsActive == 1" />
        <img src="@/assets/csts/img122.png" alt v-else />
        <span>学校</span>
      </div>
      <div class="jgzzItem" :class="zdcsActive == 2 ? 'jgzzActive' : ''" @click="zdcs(2)">
        <img src="@/assets/csts/img133.png" alt v-if="zdcsActive == 2" />
        <img src="@/assets/csts/img123.png" alt v-else />
        <span>网吧</span>
      </div>
      <div class="jgzzItem" :class="zdcsActive == 3 ? 'jgzzActive' : ''" @click="zdcs(3)">
        <img src="@/assets/csts/img134.png" alt v-if="zdcsActive == 3" />
        <img src="@/assets/csts/img124.png" alt v-else />
        <span>娱乐场所</span>
      </div>
      <div class="jgzzItem" :class="zdcsActive == 4 ? 'jgzzActive' : ''" @click="zdcs(4)">
        <img src="@/assets/csts/img135.png" alt v-if="zdcsActive == 4" />
        <img src="@/assets/csts/img125.png" alt v-else />
        <span>火车站</span>
      </div>
      <div class="jgzzItem" :class="zdcsActive == 5 ? 'jgzzActive' : ''" @click="zdcs(5)">
        <img src="@/assets/csts/img136.png" alt v-if="zdcsActive == 5" />
        <img src="@/assets/csts/img126.png" alt v-else />
        <span>电影院</span>
      </div>
    </div>
    <EventDetail v-if="isShowEventDetail" @close="isShowEventDetail = false" />
    <CountryPart :isShow="isShowCountryPart" @close="closeCountryPart" @check="checkTree" />
    <spjkPop v-if="isXlgcShow" @closeEmit="isXlgcShow = false" @moreXl="isXlgcShow1 = true" />
    <szyyPop v-if="isXlgcShow1" @closeEmit="isXlgcShow1 = false" />
    <wgyPop v-if="isWgllShow" @closeEmit="isWgllShow = false" />
    <dzzPop v-if="isdzzShow" @closeEmit="isdzzShow = false" />
    <xzqyPop v-if="isxzqyShow" @closeEmit="isxzqyShow = false" />
    <zwzxPop v-if="iszwzxShow" @closeEmit="iszwzxShow = false" />
    <zhzxPop v-if="iszhzxShow" @closeEmit="iszhzxShow = false" />
    <csglgdPop v-if="iscsglgdShow" @closeEmit="iscsglgdShow = false" />
    <shaqPop v-if="isshaqShow" @closeEmit="isshaqShow = false" />
    <sthbPop v-if="issthbShow" @closeEmit="issthbShow = false" />
    <whlyPop v-if="iswhlyShow" @closeEmit="iswhlyShow = false" />
    <yjfkPop v-if="isyjfkShow" @closeEmit="isyjfkShow = false" />
    <ggjtPop v-if="isggjtShow" @closeEmit="isggjtShow = false" />
    <zdqyPop v-if="iszdqyShow" @closeEmit="iszdqyShow = false" />
    <xxPop v-if="isxxShow" @closeEmit="isxxShow = false" />
    <wbPop v-if="iswbShow" @closeEmit="iswbShow = false" />
    <ylcsPop v-if="isylcsShow" @closeEmit="isylcsShow = false" />
    <hczPop v-if="ishczShow" @closeEmit="ishczShow = false" />
    <dyyPop v-if="isdyyShow" @closeEmit="isdyyShow = false" />
    <djylPop v-if="djylShow" @closeEmit="djylShow = false" />
    <bjxqPop v-if="bjxqShow" @closeEmit="bjxqShow = false" />
    <Area ref="area" v-show="showArea" :pop-area-info="popAreaInfo"></Area>
    <jjfztk v-if="jjfztkShow" @closeEmitai="jjfztkShow = false"></jjfztk>
    <hswhtk v-if="hswhtkShow" @closeEmitai="hswhtkShow = false"></hswhtk>
    <stjstk v-if="stjstkShow" @closeEmitai="stjstkShow = false"></stjstk>
    <wgzltk v-if="wgzltkShow" @closeEmitai="wgzltkShow = false"></wgzltk>
  </div>
</template>

<script>
import testData from '@/assets/json/cyjjPoint'
import BlockBox from '@/components/leader/common/blockBox.vue'
import HeaderMain from '@/components/leader/common/headerMains'
import effectRankBarSwiper from './components/effectRankBar-swiper.vue'
import particle from './components/particle.vue'
import EventDetail from './components/EventDetail.vue'
import CountryPart from './components/CountryPart.vue'
import leaderMiddle from '@/components/leader/common/leaderMiddle.vue'
import gdMap from '@/components/leader/gdMap/gdMap.vue'
import cesiumMap from '@/components/leader/cesiumMap/Cesium3dMap.vue'
import cssjPoint from '@/assets/json/csts/cssjPoint.json'
import spjkPoint from '@/assets/json/csts/spjkPoint.json'
import csbjPoint from '@/assets/json/csts/csbjPoint.json'
import ryllPoint from '@/assets/json/csts/ryllPoint.json'
import xzqhPoint from '@/assets/json/csts/xzqhPoint.json'
import dzzPoint from '@/assets/json/csts/dzzPoint.json'
import zwzxPoint from '@/assets/json/csts/zwzxPoint.json'
import zzzxPoint from '@/assets/json/csts/zzzxPoint.json'
import zdqyPoint from '@/assets/json/csts/zdqyPoint.json'
import xxPoint from '@/assets/json/csts/xxPoint.json'
import wbPoint from '@/assets/json/csts/wbPoint.json'
import ylcsPoint from '@/assets/json/csts/ylcsPoint.json'
import hczPoint from '@/assets/json/csts/hczPoint.json'
import dyyPoint from '@/assets/json/csts/dyyPoint.json'
import LeaderFooter from '@/components/leader/common/leaderFooter.vue'
import myRobot from '@/components/robot/myRobot.vue'
import NumScroll from '@/components/leader/num_scrolls'
import Area from '@/components/leader/leader_csts/area.vue'
import svgMap from '@/components/common/svgMap.vue'
import zlBg from '@/components/common/zlBg.vue'
import LivePlayer from '@liveqing/liveplayer'

import {
  LandUse,
  FullFactorGridEvents,
  StreetSurvey,
  jjfztk,
  hswhtk,
  stjstk,
  wgzltk,
} from './components/hszl'
import rtc from '@/rhtx/core/index'
import { aqjgGetToken } from '@/api/hs/hs_aqjg.api.js'
import {
  getHsjj,
  getJjgl,
  getKqzl,
  getDjxx,
  getRkgl,
  getRkglAge,
  getQyswgsj,
  getHsjjVideo,
} from '@/api/hs/hs.hszl.js'

import 'swiper/css/swiper.css'
export default {
  name: 'Hszl',
  components: {
    BlockBox,
    HeaderMain,
    effectRankBarSwiper,
    particle,
    leaderMiddle,
    gdMap,
    cesiumMap,
    LeaderFooter,
    EventDetail,
    CountryPart,
    myRobot,
    Area,
    NumScroll,
    LandUse,
    FullFactorGridEvents,
    StreetSurvey,
    jjfztk,
    hswhtk,
    stjstk,
    wgzltk,
    svgMap,
    zlBg,
    LivePlayer,
  },
  data() {
    return {
      jjfztkShow: false,
      hswhtkShow: false,
      stjstkShow: false,
      wgzltkShow: false,
      man_active: require('@/assets/hszl/man_active.png'),
      man_normal: require('@/assets/hszl/man_normal.png'),
      woman_normal: require('@/assets/hszl/woman_normal.png'),
      woman_active: require('@/assets/hszl/woman_active.png'),
      jgzzShow: false,
      zdcsShow: false,
      djylShow: false,
      bjxqShow: false,
      showArea: false,
      popAreaInfo: {},
      areaPointList: [],
      jgzzActive: 0,
      zdcsActive: 0,
      csglTitleBtns: ['事件', '部件'],
      shbzTitleBtns: ['社会保险', '住房保险'],
      showCesiumMap: false,
      videoUrl: require('@/assets/leader/video/bg1.mp4'),
      // videoUrl: require('@/assets/leader/video/bg.mp4'),
      data1: [
        {
          num: 55.08,
          tit: 'GDP地区生产总值',
          img: require('@/assets/csts/icon.png'),
          url: require('@/assets/hszl/icon1.png'),
          name: '亿元',
        },
        {
          num: 2.34,
          tit: '一般公共预算收入',
          img: require('@/assets/csts/icon.png'),
          url: require('@/assets/hszl/icon2.png'),
          name: '亿元',
        },
        {
          num: 11.09,
          tit: '全社会固定资产投资',
          img: require('@/assets/csts/icon.png'),
          url: require('@/assets/hszl/icon3.png'),
          name: '亿元',
        },
        {
          num: 40.62,
          tit: '规上工业生产总值',
          img: require('@/assets/csts/icon.png'),
          url: require('@/assets/hszl/icon4.png'),
          name: '亿元',
        },
        {
          num: 18.26,
          tit: '社会消费品零售总额',
          img: require('@/assets/csts/icon.png'),
          url: require('@/assets/hszl/icon5.png'),
          name: '元',
        },
        {
          num: 6.64,
          tit: '规上服务物业营业收入',
          img: require('@/assets/csts/icon.png'),
          url: require('@/assets/hszl/icon6.png'),
          name: '元',
        },
      ],
      data1Url: {
        gdp: require('@/assets/hszl/icon1.png'),
        ybggys: require('@/assets/hszl/icon2.png'),
        gdzctz: require('@/assets/hszl/icon3.png'),
        gysczz: require('@/assets/hszl/icon4.png'),
        xflsze: require('@/assets/hszl/icon5.png'),
        fwyysr: require('@/assets/hszl/icon6.png'),
      },
      data2: [
        {
          tit: '参保人数',
          img: require('@/assets/csts/icon7.png'),
          cont: [
            {
              num: 49883,
              unit: '人',
            },
          ],
        },
        {
          tit: '参保缴费补贴',
          img: require('@/assets/csts/icon8.png'),
          cont: [
            {
              num: 13467,
              unit: '人',
            },
            {
              num: 2346.45,
              unit: '万',
            },
          ],
        },
        {
          tit: '养老金发放',
          img: require('@/assets/csts/icon9.png'),
          cont: [
            {
              num: 13467,
              unit: '人',
            },
            {
              num: 1277.75,
              unit: '万',
            },
          ],
        },
      ],
      data4: [
        {
          icon: require('@/assets/hszl/icon8.png'),
          label: '党委',
          count: 11,
        },
        {
          icon: require('@/assets/hszl/icon9.png'),
          label: '党员',
          count: 3810,
        },
        {
          icon: require('@/assets/hszl/icon10.png'),
          label: '党总支',
          count: 17,
        },
        {
          icon: require('@/assets/hszl/icon11.png'),
          label: '直属党组织',
          count: 13,
        },
        {
          icon: require('@/assets/hszl/icon12.png'),
          label: '党支部',
          count: 149,
        },
        {
          icon: require('@/assets/hszl/icon13.png'),
          label: '二级党支部',
          count: 92,
        },
      ],
      data4Url: {
        djxxdw: require('@/assets/hszl/icon8.png'),
        djxxdy: require('@/assets/hszl/icon9.png'),
        djxxdzz: require('@/assets/hszl/icon10.png'),
        zsdzz: require('@/assets/hszl/icon11.png'),
        djxxdzb: require('@/assets/hszl/icon12.png'),
        djxxejdzb: require('@/assets/hszl/icon13.png'),
      },
      data5_1: [
        {
          img: require('@/assets/csts/icon29.png'),
          icon: require('@/assets/csts/icon31.png'),
          tit: '常驻人口',
          num: 64,
          pct: 82.7,
          color: '#00C6FA',
          dotNum: 50,
        },
        {
          img: require('@/assets/csts/icon30.png'),
          icon: require('@/assets/csts/icon32.png'),
          tit: '流动人口',
          num: 32,
          pct: 41.3,
          color: '#3ADCD6',
          dotNum: 50,
        },
      ],
      data5_2: [
        {
          data: {
            label: '人口就业率',
            value: 52,
          },
          options: {
            // 圆环颜色
            color: '#D7BC2F',
            // 圆环背景颜色
            bgColor: 'rgba(0, 52, 100, 1)',
            // 是否顺时针 旋转
            clockwise: true,
            // 圆环端点的形状 ['round', 'square', 'butt']
            strokeLinecap: 'round',
            // 圆环大小 单位是 px
            size: 120,
            // 圆环的宽度 相对单位
            barWidth: 80,
            // 是否开启 轮播动画
            showMotion: true,
            // 动画的 间隔时间
            times: 10000,
            // 圆环类型
            type: 2,
          },
        },
        {
          data: {
            label: '人口老龄化',
            value: 54,
          },
          options: {
            // 圆环颜色
            color: '#28C7FF',
            // 圆环背景颜色
            bgColor: 'rgba(0, 52, 100, 1)',
            // 是否顺时针 旋转
            clockwise: true,
            // 圆环端点的形状 ['round', 'square', 'butt']
            strokeLinecap: 'round',
            // 圆环大小 单位是 px
            size: 140,
            // 圆环的宽度 相对单位
            barWidth: 80,
            // 是否开启 轮播动画
            showMotion: true,
            // 动画的 间隔时间
            times: 10000,
            // 圆环类型
            type: 0,
          },
        },
        {
          data: {
            label: '贫困人口率',
            value: 68,
          },
          options: {
            // 圆环颜色
            color: '#FF8943',
            // 圆环背景颜色
            bgColor: 'rgba(0, 52, 100, 1)',
            // 是否顺时针 旋转
            clockwise: true,
            // 圆环端点的形状 ['round', 'square', 'butt']
            strokeLinecap: 'round',
            // 圆环大小 单位是 px
            size: 140,
            // 圆环的宽度 相对单位
            barWidth: 80,
            // 是否开启 轮播动画
            showMotion: true,
            // 动画的 间隔时间
            times: 10000,
            // 圆环类型
            type: 4,
          },
        },
      ],
      data6: [
        {
          url: require('@/assets/csts/photo1.png'),
          title: '南京鸡鸣寺',
        },
        {
          url: require('@/assets/csts/photo1.png'),
          title: '南京鸡鸣寺',
        },
        {
          url: require('@/assets/csts/photo1.png'),
          title: '南京鸡鸣寺',
        },
      ],
      data7: [
        {
          num: 8,
          tit: '党工委 (个)',
        },
        {
          num: 10,
          tit: '党总支 (个)',
        },
        {
          num: 12,
          tit: '党支部 (个)',
        },
        {
          num: 2468,
          tit: '党员总数 (人)',
        },
      ],
      data8: {
        data: [
          ['product', '年总数'],
          ['0-18', 10000],
          ['19-25', 10000],
          ['25-40', 10000],
          ['41-60', 10000],
          ['60岁以上', 10000],
        ],
        options: {
          colors: ['#2EF6FF', '#6D5AE2', '#2B8EF3', '#F7B13F', '#F5616F'],
          alpha: 65,
          pieSize: 220,
          pieInnerSize: 160,
          position: ['35%', '-50%'],
          bgImg: {
            top: '60%',
            left: '37%',
          },
          unit: '',
          title: {
            fontSize: '16px',
            top: 70,
            left: -20,
            // textColor: 'rgba(255, 255, 255, 0)',
          },
          subtitle: {
            fontSize: '14px',
            top: 90,
            // textColor: 'rgba(255, 255, 255, 0)',
          },
          legend: {
            orient: 'vertical',
            align: 'center',
            verticalAlign: 'bottom',
            top: -10,
            left: 160,
          },
        },
      },
      swiperOption: {
        observer: true,
        observeParents: true,
        autoHeight: true,
        spaceBetween: 0,
        autoplay: {
          delay: 3500,
          disableOnInteraction: false,
        },
        slidesPerView: 'auto',
        grabCursor: true,
        autoplayDisableOnInteraction: false,
        mousewheelControl: true,
        pagination: {
          el: '.swiper-pagination',
        },
      },
      data9_1: [
        {
          lab: 'PM2.5',
          num: 22,
          img: require('@/assets/csts/icon16.png'),
        },
        {
          lab: 'SO2',
          num: 2,
          img: require('@/assets/csts/icon17.png'),
        },
        {
          lab: 'CO',
          num: 0.7,
          img: require('@/assets/csts/icon18.png'),
        },
      ],
      data9_2: [
        {
          lab: 'NO2',
          num: 28,
          img: require('@/assets/csts/icon19.png'),
        },
        {
          lab: 'PM10',
          num: 30,
          img: require('@/assets/csts/icon20.png'),
        },
        {
          lab: 'O3',
          num: 64,
          img: require('@/assets/csts/icon21.png'),
        },
      ],
      data10_1: [
        {
          lab: '警员(人)',
          num: 51,
          img: require('@/assets/csts/icon22.png'),
        },
        {
          lab: '辅警(人)',
          num: 549,
          img: require('@/assets/csts/icon23.png'),
        },
        {
          lab: '警车',
          num: 368,
          img: require('@/assets/csts/icon24.png'),
        },
      ],
      data10_2: {
        data: [
          ['product', '事件总数'],
          ['群体性事件', 1245],
          ['恐怖袭击事件', 1245],
          ['刑事案件', 1245],
          ['信息安全事件', 1245],
        ],
        options: {
          colors: ['#80CF83', '#CDCF72', '#0280DF', '#13A4C0'],
          legend: {
            top: 15,
          },
          bgImg: {
            width: '70%',
            height: '70%',
            top: '51%',
            left: '50%',
          },
          unit: '件',
          title: {
            fontSize: '16px',
            top: 70,
          },
          subtitle: {
            fontSize: '14px',
            top: 90,
          },
        },
      },
      data11: [
        ['product', '系列名'],
        ['当前车流量', 23],
        ['当前拥堵路段', 9],
        ['当前违章数', 18],
        ['当前警情', 11],
      ],
      data12: {
        data: [
          ['product', '救援成功', '隐患整改'],
          ['2017', 500, 310],
          ['2018', 310, 102],
          ['2019', 320, 190],
          ['2020', 350, 176],
          ['2021', 360, 290],
          ['2022', 250, 161],
        ],
        options: {
          // 颜色数据
          color: ['#ffd11a', 'rgba(46,200,207,1)'],
          // 柱子的宽度
          barWidth: 20,
          // 柱子X轴位置
          symbolOffsetX: ['-30%', '30%'],
          // 顶部的大小
          symbolSize: [20, 11],
        },
      },
      activaIdx: -1,
      isShowEventDetail: false,
      isShowCountryPart: false,
      isXlgcShow: false,
      isXlgcShow1: false,
      isWgllShow: false,
      isdzzShow: false,
      isxzqyShow: false,
      iszwzxShow: false,
      iszhzxShow: false,
      iscsglgdShow: false,
      isshaqShow: false,
      issthbShow: false,
      iswhlyShow: false,
      isyjfkShow: false,
      isggjtShow: false,
      iszdqyShow: false,
      isxxShow: false,
      iswbShow: false,
      isylcsShow: false,
      ishczShow: false,
      isdyyShow: false,
      introUrl: '',
      introWord: '',
      peoTotal: '',
      maleNum: '',
      femaleNum: '',
      maleNumRate: '',
      femaleNumRate: '',
      qyswgTotal: 0,
      chartData: [],
      wgcurrentMonth: 8,
      wgsjmonthList:[]
    }
  },
  created() {
    this.getHsjjVideoFn()
    this.getHsjjFn()
    this.getJjglFn()
    this.getKqzlFn()
    this.getDjxxFn()
    this.getRkglFn()
    this.getRkglAgeFn()
    this.getQyswgsjFn()
  },
  beforeDestroy() {
    window.clearInterval(this.timer)
  },
  mounted() {
    // 获取滚动容器和文本元素
    const scrollingText = document.querySelector('.introduce')
    const text = document.querySelector('.introduce p')
    // 获取文本元素的高度
    const textHeight = text.offsetHeight

    // 定义滚动函数
    function scroll() {
      // console.log('scrollingText.scrollTop',scrollingText.scrollTop);
      // 如果文本元素已经完全滚动出去，则将其重置到滚动容器的顶部
      if (scrollingText.scrollTop >= 96) {
        scrollingText.scrollTop = 0
      }
      // 否则，将滚动容器向上滚动一个像素的距离
      else {
        scrollingText.scrollTop += 1
      }
    }

    // 每隔20毫秒调用一次滚动函数
    this.timer = window.setInterval(scroll, 100)

    // 获取安全监管系统的token
    this.aqjgGetTokenFn()
  },
  watch: {},
  computed: {
    formatNumber() {
      return function (param) {
        console.log(param)
        if (this.isEmpty(param) || param.length < 4 || isNaN(param)) {
          return param
        }

        let small = ''
        let isSmall = false
        if (param.split('.').length === 2) {
          // 有小数
          isSmall = true
          small = param.split('.')[1]
          param = param.split('.')[0]
        }

        let result = ''

        while (param.length > 3) {
          result = ',' + param.slice(-3) + result
          param = param.slice(0, param.length - 3)
        }

        if (param) {
          result = param + result
        }

        if (isSmall) {
          result = result + '.' + small
        }
        console.log(result)
        return result
      }
    },
  },
  methods: {
    async aqjgGetTokenFn() {
      const res = await aqjgGetToken()
      if (res?.data) {
        localStorage.setItem('aqjgToken', res.data.access_token)
      }
    },
    watchFlag() {
      let flag = this.$route.query.flag?.substr(0, 4)
      this.djylShow = false
      this.iscsglgdShow = false
      switch (flag) {
        case 'djyl':
          this.djylShow = true
          break
        case 'csgl':
          this.iscsglgdShow = true
          break
        default:
          break
      }
    },
    csglBulletFrame(val) {
      this.iscsglgdShow = val
    },
    shaqBulletFrame(val) {
      this.isshaqShow = val
    },
    sthbBulletFrame(val) {
      this.issthbShow = val
    },
    djylBulletFrame(val) {
      this.djylShow = val
    },
    whlyBulletFrame(val) {
      this.iswhlyShow = val
    },
    yjfkBulletFrame(val) {
      this.isyjfkShow = val
    },
    ggjtBulletFrame(val) {
      this.isggjtShow = val
    },
    shbzBtnMehtod(val) {
      console.log(val)
      if (val == 0) {
        this.data2 = [
          {
            tit: '参保人数',
            img: require('@/assets/csts/icon7.png'),
            cont: [
              {
                num: 49883,
                unit: '人',
              },
            ],
          },
          {
            tit: '参保缴费补贴',
            img: require('@/assets/csts/icon8.png'),
            cont: [
              {
                num: 13467,
                unit: '人',
              },
              {
                num: 2346.45,
                unit: '万',
              },
            ],
          },
          {
            tit: '养老金发放',
            img: require('@/assets/csts/icon9.png'),
            cont: [
              {
                num: 13467,
                unit: '人',
              },
              {
                num: 1277.75,
                unit: '万',
              },
            ],
          },
        ]
      } else if (val == 1) {
        this.data2 = [
          {
            tit: '实施保障户',
            img: require('@/assets/csts/icon37.png'),
            cont: [
              {
                num: 13.467,
                unit: '户',
              },
              {
                num: 2346,
                unit: '人',
              },
            ],
          },
          {
            tit: '廉租房',
            img: require('@/assets/csts/icon38.png'),
            cont: [
              {
                num: 13.467,
                unit: '户',
              },
              {
                num: 2346,
                unit: '人',
              },
            ],
          },
          {
            tit: '公租房',
            img: require('@/assets/csts/icon39.png'),
            cont: [
              {
                num: 13.467,
                unit: '户',
              },
              {
                num: 2346,
                unit: '人',
              },
            ],
          },
        ]
      }
    },
    jgzz(index) {
      this.isWgllShow = false
      this.isdzzShow = false
      this.isxzqyShow = false
      this.iszwzxShow = false
      this.iszhzxShow = false
      this.isShowEventDetail = false
      this.isXlgcShow = false
      this.iszdqyShow = false
      this.isxxShow = false
      this.iswbShow = false
      this.isylcsShow = false
      this.ishczShow = false
      this.isdyyShow = false
      this.bjxqShow = false
      this.jgzzActive = index
      this.$refs.CesiumMapRef.removeAllLayer() //移除所有点位
      if (index == 0) {
        // 地图打点
        let data = xzqhPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'xzqhPoint')
      } else if (index == 1) {
        // 地图打点
        let data = dzzPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'dzzPoint')
      } else if (index == 2) {
        // 地图打点
        let data = zwzxPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'zwzxPoint')
      } else if (index == 3) {
        // 地图打点
        let data = zzzxPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'zzzxPoint')
      }
    },
    zdcs(index) {
      this.isWgllShow = false
      this.isdzzShow = false
      this.isxzqyShow = false
      this.iszwzxShow = false
      this.iszhzxShow = false
      this.isShowEventDetail = false
      this.isXlgcShow = false
      this.iszdqyShow = false
      this.isxxShow = false
      this.iswbShow = false
      this.isylcsShow = false
      this.ishczShow = false
      this.isdyyShow = false
      this.bjxqShow = false
      console.log(index)
      this.zdcsActive = index
      this.$refs.CesiumMapRef.removeAllLayer() //移除所有点位
      if (index == 0) {
        // 地图打点
        let data = zdqyPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'zdqyPoint')
      } else if (index == 1) {
        // 地图打点
        let data = xxPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'xxPoint')
      } else if (index == 2) {
        // 地图打点
        let data = wbPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'wbPoint')
      } else if (index == 3) {
        // 地图打点
        let data = ylcsPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'ylcsPoint')
      } else if (index == 4) {
        // 地图打点
        let data = hczPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'hczPoint')
      } else if (index == 5) {
        // 地图打点
        let data = dyyPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'dyyPoint')
      }
    },
    bubble(data) {
      this.isWgllShow = false
      this.isdzzShow = false
      this.isxzqyShow = false
      this.iszwzxShow = false
      this.iszhzxShow = false
      this.isShowEventDetail = false
      this.isXlgcShow = false
      this.iszdqyShow = false
      this.isxxShow = false
      this.iswbShow = false
      this.isylcsShow = false
      this.ishczShow = false
      this.isdyyShow = false
      this.bjxqShow = false
      if (data.id === 'ryll') {
        this.isWgllShow = true
      }
      if (data.id === 'dzz') {
        this.isdzzShow = true
      }
      if (data.id === 'xzqh') {
        this.isxzqyShow = true
      }
      if (data.id === 'zwzx') {
        this.iszwzxShow = true
      }
      if (data.id === 'zhzx') {
        this.iszhzxShow = true
      }
      if (data.id === 'cssj') {
        this.isShowEventDetail = true
      }
      if (data.id === 'spjk') {
        this.isXlgcShow = true
      }
      if (data.id === 'zdqy') {
        this.iszdqyShow = true
      }
      if (data.id === 'xx') {
        this.isxxShow = true
      }
      if (data.id === 'wb') {
        this.iswbShow = true
      }
      if (data.id === 'ylcs') {
        this.isylcsShow = true
      }
      if (data.id === 'hcz') {
        this.ishczShow = true
      }
      if (data.id === 'dyy') {
        this.isdyyShow = true
      }
      if (data.id === 'bjxq') {
        this.bjxqShow = true
      }
    },
    mark(i) {
      this.isWgllShow = false
      this.isdzzShow = false
      this.isxzqyShow = false
      this.iszwzxShow = false
      this.iszhzxShow = false
      this.isShowEventDetail = false
      this.isXlgcShow = false
      this.iszdqyShow = false
      this.isxxShow = false
      this.iswbShow = false
      this.isylcsShow = false
      this.ishczShow = false
      this.isdyyShow = false
      this.bjxqShow = false

      this.showCesiumMap = true
      this.activaIdx = i
      this.mapLoad()
      this.$refs.CesiumMapRef.removeAllLayer() //移除所有点位
      this.$refs.CesiumMapRef.removeTrack()
      this.$refs.map.removeLayer('area')
      if (i === 0) {
        this.jgzzShow = true
        this.zdcsShow = false
        this.isShowCountryPart = false
        // 地图打点
        let data = xzqhPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'xzqhPoint')
      } else if (i === 1) {
        this.jgzzShow = false
        this.zdcsShow = false
        this.isShowCountryPart = false
        // 地图打点
        let data = ryllPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'ryllPoint')
      } else if (i === 2) {
        this.jgzzShow = false
        this.zdcsShow = false
        this.isShowCountryPart = false
        // 地图打点
        let data = spjkPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'spjkPoint')
      } else if (i === 3) {
        this.jgzzShow = false
        this.zdcsShow = false
        this.isShowCountryPart = true
      } else if (i === 4) {
        this.jgzzShow = false
        this.zdcsShow = false
        this.isShowCountryPart = false
        // 地图打点
        let data = cssjPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'cssjPoint')
      } else if (i === 5) {
        this.jgzzShow = false
        this.zdcsShow = true
        this.isShowCountryPart = false
        // 地图打点
        let data = zdqyPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'zdqyPoint')
      }
    },
    mapLoad() {
      this.loadCarLayer('area')
      this.$refs.map.autoShowInfoWindow(this.areaPointList)
    },

    // 加载重点项目
    loadCarLayer(layerId) {
      if (layerId == 'area') {
        this.areaPointList = testData.markersArea.map((item) => ({
          ...item,
          popup: this.$refs.area.$el,
          onclick: () => {
            this.popAreaInfo = {
              ...item.properties,
            }
            this.showArea = true
          },
        }))
        this.$refs.map.addMarkers(
          this.areaPointList,
          require('@/assets/map/point/point2.png'),
          // [34, 84],
          [1, 1],
          layerId
        )
      }
    },
    closeCountryPart() {
      this.isShowCountryPart = false
      if (this.activaIdx === 3) this.activaIdx = -1
    },
    checkTree(e) {
      console.log(e)
      // 地图打点
      let data = csbjPoint.features.map((e) => {
        return {
          position: e.geometry.coordinates.concat(100),
          properties: e.properties,
          img: e.img,
        }
      })
      this.$refs.CesiumMapRef.loadPoints1(data, 'csbjPoint')
    },
    emitMenu(val) {
      console.log(val)
      switch (val) {
        case 1:
          this.stjstkShow = true
          break
        case 2:
          this.jjfztkShow = true
          break
        case 3:
          this.wgzltkShow = true
          break
        case 4:
          this.hswhtkShow = true
          break

        default:
          break
      }
    },
    async getHsjjVideoFn() {
      let res = await getHsjjVideo()
      if (res?.code == '200') {
        this.introUrl = res.result.tbAppendixList[0].url
      }
    },
    async getHsjjFn() {
      let res = await getHsjj()
      if (res?.code == '200') {
        this.introWord = res.result[0].sysvalue
      }
    },
    async getJjglFn() {
      let res = await getJjgl()
      if (res?.code == '200') {
        this.data1 = []
        this.data1 = res.result.map((item) => {
          return {
            num: Number(item.sysvalue),
            tit: item.name,
            img: require('@/assets/csts/icon.png'),
            url: this.data1Url[item.syskey],
            name: '亿元',
          }
        })
      }
    },
    async getKqzlFn() {
      let res = await getKqzl()
      if (res?.code == '200') {
        this.data9_1 = []
        this.data9_2 = []
        this.data9_1 = res.result
          .slice(0, 3)
          // .filter((element, index) => index % 2 !== 0)
          .map((it) => {
            return { lab: it.name, num: Number(it.value) }
          })
        this.data9_2 = res.result
          .slice(3)
          // .filter((element, index) => index % 2 == 0)
          .map((it) => {
            return { lab: it.name, num: Number(it.value) }
          })
      }
    },
    async getDjxxFn() {
      let res = await getDjxx()
      if (res?.code == '200') {
        this.data4 = []
        this.data4 = res.result.map((item) => {
          return {
            count: Number(item.sysvalue),
            label: item.name,
            icon: this.data4Url[item.syskey],
          }
        })
      }
    },
    async getRkglFn() {
      let res = await getRkgl()
      if (res?.code == '200') {
        this.peoTotal = res.result.find((item) => !item.syskey).sysvalue
        this.maleNum = res.result.find((item) => item.syskey == 'male').sysvalue
        this.femaleNum = res.result.find((item) => item.syskey == 'female').sysvalue

        this.maleNumRate = (this.maleNum / this.peoTotal).toFixed(2) * 100
        this.femaleNumRate = (this.femaleNum / this.peoTotal).toFixed(2) * 100
      }
    },
    async getRkglAgeFn() {
      let res = await getRkglAge()
      if (res?.code == '200') {
        this.data8.data = []
        this.data8.data = [
          ['product', '年总数'],
          ...res.result.map((item) => [item.name, Number(item.sysvalue)]),
        ]
      }
    },
    async getQyswgsjFn() {
      let res = await getQyswgsj()
      if (res?.code == '200') {
        this.qyswgTotal = res.result.reduce((total, obj) => total + Number(obj.sysvalue), 0)
        this.chartData = [
          ['product', '数量'],
          ...res.result.map((item) => [item.name, Number(item.sysvalue)]),
        ]
      }
    },
  },
}
</script>

<style lang="less" scoped>
@font-face {
  font-family: QuartzRegular;
  src: url(~@/assets/leader/font/QuartzRegular.ttf);
}
@keyframes rotateS {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes rotateN {
  0% {
    transform: rotate(360deg);
  }
  100% {
    transform: rotate(0);
  }
}
@keyframes rotateY {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotateY(360deg);
  }
}

.leader_sjts {
  width: 1920px;
  height: 1080px;
  position: absolute;
  top: 0;
  display: flex;
  justify-content: space-between;
  .leader_city_box {
    width: 450px;
    .leader_zt_contain {
      height: 100%;
    }
  }
  .box {
    .cont {
      width: 100%;
      height: 100%;
    }
    .wrapper1 {
      width: 100%;
      height: 100%;
      padding: 14px 0 22px;
      .introduce_video {
        width: 459px;
        height: 192px;
        ::v-deep .video-wrapper {
          padding-bottom: 41.25% !important;
        }
      }
      .introduce {
        margin-top: 11px;
        width: 100%;
        height: 120px;
        padding: 18px 26px;
        background: url(~@/assets/hszl/bg1.png) no-repeat;
        background-size: 100% 100%;
        font-size: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #4fddff;
        line-height: 22px;
        letter-spacing: 2px;
        text-align: left;
        // overflow-y: scroll;
        // &::-webkit-scrollbar {
        //   /*滚动条整体样式*/
        //   width: 6px;
        //   background: transparent;
        //   // border: 1px solid #999;
        //   /*高宽分别对应横竖滚动条的尺寸*/
        //   // height: 1px;
        // }

        // &::-webkit-scrollbar-thumb {
        //   /*滚动条里面小方块*/
        //   border-radius: 2px;
        //   box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        //   background: rgb(45, 124, 228);
        // }
      }
    }
    .wrapper4 {
      width: 100%;
      height: 100%;
      padding: 30px 0 30px 8px;
      ul {
        width: 100%;
        height: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;
        li {
          display: flex;
          height: 49px;
          gap: 10px;
          .icon {
            width: 46px;
            height: 49px;
          }
          .info {
            margin-top: -6px;
            .label {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
              text-align: left;
              padding-left: 5px;
            }
            .line {
              width: 87px;
              height: 6px;
              background: url('~@/assets/hszl/line1.png') no-repeat;
            }
            .count {
              text-align: left;
              margin-top: 4px;
              width: 87px;
              height: 22px;
              line-height: 22px;
              background: linear-gradient(96deg, #1d4975 0%, rgba(19, 56, 87, 0.24) 100%);
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #a8b1b9;
              padding-left: 5px;
              span {
                font-size: 18px;
                font-family: PingFangSC, PingFang SC;
                font-weight: normal;
                color: #ffffff;
                line-height: 22px;
                margin-right: 3px;
              }
            }
          }
        }
      }
    }
    .wrapper5 {
      width: 100%;
      height: 100%;
      padding-top: 31px;
      .info {
        width: 100%;
        height: 71px;
        padding-left: 8px;
        display: flex;
        justify-content: space-between;
        .total {
          width: 130px;
          height: 100%;
          background: url('~@/assets/hszl/bg4.png') no-repeat;
          display: grid;
          place-items: center;
          .cont {
            padding-top: 13px;
            .label {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
            }
            .value {
              font-size: 18px;
              font-family: PingFangSC, PingFang SC;
              font-weight: normal;
              color: #ffffff;
              line-height: 22px;
              background: linear-gradient(180deg, #ffffff 0%, #00fffa 67%, #04a1ff 100%);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              span {
                font-size: 12px;
              }
            }
          }
        }
        .counts {
          width: 312px;
          height: 100%;
          background: url('~@/assets/hszl/bg5.png') no-repeat;
          display: flex;
          padding: 0 9px;
          .men {
            text-align: left;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            margin-right: 4px;
            .count {
              height: 21px;
              font-size: 16px;
              font-family: PingFangSC, PingFang SC;
              font-weight: normal;
              line-height: 19px;
              background: linear-gradient(180deg, #ffffff 0%, #00ceff 67%, #04a1ff 100%);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              span {
                font-size: 14px;
              }
            }
            .chart {
              display: flex;
              gap: 4px;
              span {
                width: 10px;
                height: 25px;
                background-repeat: no-repeat;
              }
            }
          }
          .women {
            text-align: right;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            .count {
              height: 21px;
              font-size: 16px;
              font-family: PingFangSC, PingFang SC;
              font-weight: normal;
              line-height: 19px;
              background: linear-gradient(180deg, #ffffff 0%, #f076ff 66%, #ff04ff 100%);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              span {
                font-size: 14px;
              }
            }
            .chart {
              display: flex;
              gap: 4px;
              span {
                width: 12px;
                height: 25px;
                background-repeat: no-repeat;
              }
            }
          }
        }
      }
      .chart_wrap {
        position: relative;
        width: 100%;
        height: 198px;
        top: 41px;
        .sign {
          position: absolute;
          left: 105px;
          top: 25px;
          .woman {
            position: absolute;
            width: 27px;
            height: 57px;
            left: 0;
            top: 0;
            background: url('~@/assets/hszl/woman.png') no-repeat;
          }
          .man {
            position: absolute;
            width: 23px;
            height: 57px;
            left: 95px;
            top: 47px;
            background: url('~@/assets/hszl/man.png') no-repeat;
          }
          .man_count {
            position: absolute;
            left: 10px;
            top: 76px;
            width: 109px;
            height: 31px;
            font-size: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 24px;
          }
          .woman_count {
            position: absolute;
            left: 10px;
            top: 36px;
            width: 109px;
            height: 31px;
            font-size: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 24px;
          }
        }
      }
    }
  }
  .box1 {
    .cont {
      padding: 17px 0 21px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: space-between;
      li {
        position: relative;
        width: 222px;
        height: 61px;
        padding: 10px 7px;
        display: flex;
        background: url(~@/assets/hszl/bg2.png) no-repeat;
        .icon {
          width: 41px;
          height: 41px;
          background: url(~@/assets/hszl/bg3.png) no-repeat;
          font-size: 17px;
          img {
            animation: move infinite 3s ease-in-out;
            @keyframes move {
              0% {
                transform: rotateY(0);
              }
              50% {
                transform: rotateY(180deg);
              }
              100% {
                transform: rotateY(360deg);
              }
            }
          }
          .name {
            font-size: 10px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 11px;
            text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
          }
        }
        .line {
          position: absolute;
          left: 50px;
          top: 5px;
          width: 1px;
          height: 55px;
          border: 1px solid;
          border-image: linear-gradient(
              180deg,
              rgba(0, 83, 171, 0),
              rgba(0, 140, 213, 0.6),
              rgba(0, 83, 171, 0)
            )
            1 1;
        }
        .desc {
          position: relative;
          margin-left: 15px;
          text-align: left;
          padding-left: 18px;
          .xsj {
            position: absolute;
            width: 10px;
            height: 10px;
            left: 0;
            top: 7px;
            background: url(~@/assets/csts/xsj1.png) no-repeat;
          }
          .num {
            font-size: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 24px;
          }
          .tit {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 20px;
            white-space: nowrap;
          }
        }
      }
    }
  }
  .box2 {
    .cont {
      padding: 14px 28px 24px;
      display: flex;
      gap: 5px;
      .cont_item {
        position: relative;
        width: 128px;
        height: 176px;
        padding: 7px 15px 0 16px;
        box-shadow: inset 0px 1px 12px 2px rgba(44, 165, 235, 0.2);
        .icon {
          width: 97px;
          height: 99px;
        }
        .lzdx {
          width: 97px;
          height: 99px;
          position: absolute;
          top: 7px;
          left: 16px;
        }
        .tit {
          position: absolute;
          top: 97px;
          left: 50%;
          width: 100%;
          transform: translateX(-50%);
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #caecff;
          line-height: 20px;
          text-shadow: 0px 0px 1px #00132e;
        }
        ol {
          width: 100%;
          height: 40px;
          margin-top: 21px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          padding-left: 20px;
          & > li {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 22px;
            text-align: left;
            list-style: disc;
            &::marker {
              color: #38deff;
            }
            .unit {
              font-size: 12px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #caecff;
              line-height: 17px;
              text-shadow: 0px 0px 1px #00132e;
            }
          }
        }
      }
    }
  }
  .box3 {
    .cont {
      padding: 14px 28px 0;
      video {
        width: 100%;
        height: 100%;
        object-fit: cover;
        &:not(:root):fullscreen {
          object-fit: contain;
        }
      }
    }
  }
  .box4 {
    .cont {
      padding: 15px 0;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-evenly;
      li {
        position: relative;
        width: 200px;
        height: 66px;
        background: url(~@/assets/csts/bg2.png) no-repeat;
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: left;
        padding-left: 92px;
        .icon {
          position: absolute;
          width: 26px;
          height: 26px;
          top: 10px;
          left: 31px;
          animation: move infinite 3s ease-in-out;
          @keyframes move {
            0% {
              transform: translateY(0px) rotateY(0);
            }
            50% {
              transform: translateY(-10px) rotateY(180deg);
            }
            100% {
              transform: translateY(0px) rotateY(360deg);
            }
          }
        }
        &:nth-child(2) {
          span {
            animation-delay: 0.3s;
          }
        }
        &:nth-child(3) {
          span {
            animation-delay: 0.6s;
          }
        }
        &:nth-child(4) {
          span {
            animation-delay: 0.9s;
          }
        }
        &:nth-child(5) {
          span {
            animation-delay: 1.2s;
          }
        }
        &:nth-child(6) {
          span {
            animation-delay: 1.5s;
          }
        }
        .tit {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 20px;
        }
        .num {
          font-size: 18px;
          font-family: PingFangSC, PingFang SC;
          font-weight: normal;
          color: #ffffff;
          line-height: 22px;
        }
      }
    }
  }
  .box5 {
    .cont {
      display: flex;
      flex-direction: column;
      .bar_chart {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        li {
          position: relative;
          display: flex;
          align-items: center;
          padding: 0 30px 0 23px;
          .icon {
            width: 20px;
            height: 22px;
            margin-right: 7px;
          }
          .icon2 {
            width: 10px;
            height: 12px;
            position: absolute;
            left: 28px;
            top: 5px;
            animation: move infinite 3s ease-in-out;
            @keyframes move {
              0% {
                transform: translateY(0px) rotateY(0);
              }
              50% {
                transform: translateY(0px) rotateY(180deg);
              }
              100% {
                transform: translateY(0px) rotateY(360deg);
              }
            }
          }
          .lab {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            margin-right: 8px;
          }
          .progress {
            display: block;
            flex: 1;
            height: 100%;
            background: rgba(0, 201, 255, 0.14);
            .cur {
              width: 100%;
              border: 1px solid;
              height: 100%;
              overflow: hidden;
              transition: width 0.5s;
            }
          }
          .num {
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            margin-left: 9px;
          }
          .percent {
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            margin-left: 14px;
          }
        }
      }
      .pie_chart {
        height: 123px;
        display: flex;
        justify-content: space-evenly;
        .warp {
          width: 107px;
          height: 107px;
          padding-top: 0;
          /deep/.ring {
            width: 100% !important;
            height: 100% !important;
          }
          /deep/.label {
            margin-top: -65px;
            .name {
              font-size: 13px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
            }
          }
        }
      }
    }
  }
  .box6 {
    .cont {
      .select_month {
        position: absolute;
        right: 52px;
        top: -20px;
        ::v-deep  button {
          width: 56px;
          height: 24px;
          right: -46px;
          top: -28px;
          border-radius: 12px;
          background: url(~@/assets/shzl/select_bg.png) no-repeat center / 100% 100%;
          border: none;
          padding: 0;
          // background: linear-gradient(180deg, #0e63ac 0%, #253c69 100%);
          // border-radius: 2px;
          // border: 2px solid;
          // border-image: linear-gradient(180deg, rgba(63, 148, 224, 0.2), rgba(218, 250, 255, 0.3))
          //   2 2;
        }
      }
      // padding: 14px 28px 0;
      // .swiper-slide {
      //   height: 100%;
      // }
      // .swiper-pagination {
      //   text-align: right;
      //   bottom: 3px;
      // }
      // /deep/.swiper-pagination-bullet-active {
      //   background-color: rgba(255, 255, 255, 0.8);
      // }
      // .content {
      //   width: 100%;
      //   height: 100%;
      //   position: relative;
      //   img {
      //     position: absolute;
      //     width: 100%;
      //     height: 100%;
      //     top: 0;
      //     left: 0;
      //   }
      //   .cont {
      //     width: 100%;
      //     height: 30px;
      //     padding: 5px 10px;
      //     position: absolute;
      //     bottom: 0;
      //     font-size: 14px;
      //     font-family: PingFangSC, PingFang SC;
      //     font-weight: 500;
      //     color: #ffffff;
      //     line-height: 22px;
      //     text-align: left;
      //     background-color: rgba(0, 0, 0, 0.35);
      //   }
      // }
    }
  }
  .box7 {
    .cont {
      display: grid;
      place-items: center;
      position: relative;
      .fy_out {
        position: absolute;
        top: 3%;
        left: 27%;
        z-index: 99;
        transform: translate(-50%, -50%);
        animation: rotateS infinite 12s linear;
      }
      .fy_in {
        position: absolute;
        top: 44%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
      .wrap {
        position: relative;
        width: 393px;
        height: 205px;
        background: url(~@/assets/csts/bg3.png) no-repeat;
        li {
          position: absolute;
          text-align: left;
          .num {
            font-size: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 24px;
          }
          .tit {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 17px;
          }
          &:nth-child(1) {
            top: 28px;
            left: 24px;
          }
          &:nth-child(2) {
            top: 28px;
            left: 295px;
          }
          &:nth-child(3) {
            bottom: 32px;
            left: 24px;
          }
          &:nth-child(4) {
            bottom: 32px;
            left: 295px;
          }
        }
      }
    }
  }
  .box9 {
    .cont {
      padding: 16px 23px 0;
      .title {
        width: 100%;
        height: 44px;
        line-height: 44px;
        display: flex;
        justify-content: space-between;
        padding: 0 24px;
        span {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
        }
      }
      .detail {
        position: relative;
        width: 404px;
        height: 156px;
        background: url(~@/assets/csts/bg4.png) no-repeat center;
        display: flex;
        justify-content: space-between;
        .center_out {
          position: absolute;
          left: 29%;
          top: -2%;
          transform: translate(-50%, -50%);
          z-index: 99;
          animation: rotateS infinite 12s linear;
        }
        .center_in {
          position: absolute;
          left: 29%;
          top: 2px;
          transform: translate(-50%, -50%);
          z-index: 99;
          animation: rotateN infinite 12s linear;
        }
        .fs {
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          gap: 9px;
          li {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .icon {
              width: 13px;
              height: 16px;
            }
            .lab {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
              padding-left: 6px;
              padding-right: 22px;
            }
            .num {
              font-size: 10px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              span {
                font-size: 18px;
                font-family: PingFangSC, PingFang SC;
                font-weight: normal;
                color: #ffffff;
                line-height: 22px;
              }
            }
          }
        }
        .fq {
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          gap: 9px;
          li {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .icon {
              width: 14px;
              height: 14px;
            }
            .lab {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
              padding-left: 22px;
              padding-right: 6px;
            }
            .num {
              font-size: 10px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              span {
                font-size: 18px;
                font-family: PingFangSC, PingFang SC;
                font-weight: normal;
                color: #ffffff;
                line-height: 22px;
              }
            }
          }
        }
        .zdqy {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          .num {
            font-size: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 24px;
          }
          .lab {
            font-size: 18px;
            font-family: PingFangSC, PingFang SC;
            color: #ffffff;
            line-height: 21px;
            letter-spacing: 1px;
          }
        }
      }
    }
  }
  .box10 {
    .cont {
      padding: 10px 35px 0;
      .desc {
        height: 53px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        li {
          display: flex;
          .icon {
            width: 41px;
            height: 41px;
            margin-right: 11px;
          }
          .info {
            text-align: left;
            .num {
              font-size: 18px;
              font-family: PingFangSC, PingFang SC;
              font-weight: normal;
              color: #ffffff;
              line-height: 22px;
            }
            .lab {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
            }
          }
        }
      }
      .chart {
        height: calc(100% - 53px);
        margin-top: -20px;
      }
    }
  }
  .box12 {
    position: relative;
    .gajq_lz {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .left_bg {
    width: 460px;
    height: 1033px;
    background: url(~@/assets/leader/img/component/left_bg.png) no-repeat center / 100% 100%;
    position: absolute;
    left: 50px;
    top: 25px;
    z-index: 102;
  }
  .right_bg {
    width: 520px;
    height: 1033px;
    background: url(~@/assets/leader/img/component/right_bg.png) no-repeat center / 100% 100%;
    position: absolute;
    right: 50px;
    top: 25px;
    z-index: 102;
  }
  .left {
    width: 460px;
    display: flex;
    justify-content: space-between;
    margin-top: 123px;
    margin-left: 50px;
    position: relative;
    z-index: 1003;
  }
  .right {
    width: 460px;
    margin-right: 50px;
    // border: 1px solid red;
    display: flex;
    justify-content: space-between;
    margin-top: 123px;
    position: relative;
    z-index: 1003;
  }
}
.jgzzBox {
  width: 109px;
  height: 170px;
  display: flex;
  flex-direction: column;
  position: absolute;
  left: 525px;
  bottom: 93px;
  z-index: 999;
  background: url(~@/assets/csts/btnBg11.png) no-repeat center / 100% 100%;
  .jgzzItem {
    width: 103px;
    height: 35px;
    margin: 2px 3px 2px 3px;
    background: url(~@/assets/csts/btn11.png) no-repeat;
    img {
      float: left;
      margin: 7px 0 0 10px;
    }
    span {
      float: left;
      font-size: 17px;
      font-family: PingFangSC, PingFang SC;
      color: #e9fffe;
      line-height: 35px;
      background: linear-gradient(180deg, #ffffff 0%, #2f8af4 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .jgzzActive {
    width: 105px;
    height: 37px;
    margin: 1px 2px 1px 2px;
    background: url(~@/assets/csts/btn12.png) no-repeat;
  }
}
.zdcsBox {
  width: 109px;
  height: 248px;
  display: flex;
  flex-direction: column;
  position: absolute;
  left: 1197px;
  bottom: 93px;
  z-index: 999;
  background: url(~@/assets/csts/btnBg12.png) no-repeat center / 100% 100%;
  .jgzzItem {
    width: 103px;
    height: 35px;
    margin: 2px 3px 2px 3px;
    background: url(~@/assets/csts/btn11.png) no-repeat;
    img {
      float: left;
      margin: 7px 0 0 10px;
    }
    span {
      float: left;
      font-size: 17px;
      font-family: PingFangSC, PingFang SC;
      color: #e9fffe;
      line-height: 35px;
      background: linear-gradient(180deg, #ffffff 0%, #2f8af4 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .jgzzActive {
    width: 105px;
    height: 37px;
    margin: 1px 2px 1px 2px;
    background: url(~@/assets/csts/btn12.png) no-repeat;
  }
}
.left,
.right {
  ::v-deep {
    .el-carousel {
      width: 450px;
      height: 920px;
      .el-carousel__indicators {
        .el-carousel__indicator {
          &.is-active {
            .el-carousel__button {
              height: 4px;
              background-color: #49e6ff;
            }
          }
          .el-carousel__button {
            height: 4px;
            background-color: rgba(255, 255, 255, 0.4);
            border-radius: 2px;
          }
        }
      }
    }
  }
}
.right {
  ::v-deep {
    .el-carousel {
      margin-left: auto;
    }
  }
}
::v-deep .el-carousel--horizontal {
  width: 450px;
  overflow-x: hidden;
}
.map_box {
  position: absolute;
  width: 1920px;
  height: 1080px;
  top: 0px;
  left: 0;
  z-index: 999;
  // border: 1px solid red;
}

/* 样式用于控制滚动容器的高度、宽度、边框和滚动效果等 */
.introduce {
  // width: 500px;
  // height: 200px;
  overflow: hidden;
  position: relative;
}

/* 样式用于控制滚动文本的位置和颜色 */
.introduce p {
  position: absolute;
  top: 0;
  left: 0;
  margin: 0;
  padding: 10px;
  font-size: 16px;
}
</style>
