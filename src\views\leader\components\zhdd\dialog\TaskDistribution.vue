<template>
  <div class="task-distribution" v-if="show">
    <div class="close" @click="$emit('input', false)"></div>
    <div class="title">
      <span>任务派发</span>
    </div>
    <el-form ref="form" :model="form" label-width="80px">
      <div :style="{ display: 'flex', justifyContent: 'space-between' }">
        <el-form-item label="紧急程度:" class="custom">
          <el-select v-model="form.urgent" placeholder="请选择">
            <el-option label="紧急" value="3"></el-option>
            <el-option label="重要" value="2"></el-option>
            <el-option label="突发" value="1"></el-option>
            <el-option label="普通" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="事发时间:" class="custom">
          <el-time-picker
            v-model="form.time"
            :picker-options="{
              selectableRange: '00:00:00 - 23:59:59'
            }"
            placeholder="选择时间点"
          >
          </el-time-picker>
        </el-form-item>
      </div>

      <el-form-item label="事件来源:">
        <el-select v-model="form.origin" placeholder="请选择事件来源">
          <el-option
            :label="it.label"
            :value="it.value"
            v-for="(it, i) of eventSource"
            :key="it.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="事件标题:">
        <el-input v-model="form.title" placeholder="请输入事件标题"></el-input>
      </el-form-item>
      <el-form-item label="事件地址:">
        <el-input v-model="form.address" placeholder="请输入事件地址"></el-input>
      </el-form-item>
      <el-form-item label="事件内容:">
        <el-input
          type="textarea"
          :rows="4"
          v-model="form.desc"
          placeholder="请填写事件内容"
        ></el-input>
      </el-form-item>
      <el-form-item label="事件类型:">
        <!-- <el-select v-model="form.type" placeholder="请选择事件类型">
          <el-option
            :label="it.label"
            :value="it.value"
            v-for="(it, i) of eventType"
            :key="it.value"
          ></el-option>
        </el-select> -->
        <el-cascader v-model="form.type" :options="eventType" @change="handleChange"></el-cascader>
      </el-form-item>
      <el-form-item label="负责人:">
        <el-select v-model="form.person" placeholder="请选择负责人">
          <el-option
            :label="it.label"
            :value="it.value"
            v-for="(it, i) of personList"
            :key="it.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="上传附件:">
        <el-upload
          class="upload-demo"
          :action="
            `${finaUrl}/api/app/apendix/tbAppendixs/upload?annexNum=${new Date().getTime()}2`
          "
          :on-change="handleChange"
          :on-progress="handleProgress"
          :on-success="handleSuccess"
          :file-list="form.fileList"
        >
          <el-button size="small" type="primary" class="upload">选择文件</el-button>
        </el-upload>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit" class="submit">提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import {
  getEventType,
  getEventResource,
  getResponsiblePerson,
  createOrder
} from '@/api/hs/hs.api.js'
export default {
  name: 'TaskDistribution',
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    show() {
      return this.value
    }
  },
  data() {
    return {
      finaUrl: window.location.origin.includes('//172')
        ? 'http://*************:9008'
        : `http://**********:8090/sjcz`,
      form: {
        title: '',
        urgent: '',
        time: '',
        origin: '',
        address: '',
        type: '',
        person: '',
        desc: '',
        fileList: [],
        eventSource: [],
        eventType: [],
        personList: [],
        fileCode: ''
      }
    }
  },
  methods: {
    async onSubmit() {
      const { urgent, time, origin, title, address, desc, type, person } = this.form
      console.log(
        urgent,
        dayjs(time).format('YY-MM-DD HH:mm:ss'),
        origin,
        title,
        address,
        desc,
        type,
        person
      )
      if (!urgent) {
        this.$message({
          message: '请选择紧急程度',
          type: 'error'
        })
        return
      }
      if (!time) {
        this.$message({
          message: '请选择时间点',
          type: 'error'
        })
        return
      }
      if (!origin) {
        this.$message({
          message: '请选择事件来源',
          type: 'error'
        })
        return
      }
      if (!title) {
        this.$message({
          message: '请填写事件标题',
          type: 'error'
        })
        return
      }
      if (!address) {
        this.$message({
          message: '请填写事件地址',
          type: 'error'
        })
        return
      }
      if (!desc) {
        this.$message({
          message: '请填写事件内容',
          type: 'error'
        })
        return
      }
      if (!type) {
        this.$message({
          message: '请选择事件类型',
          type: 'error'
        })
        return
      }
      if (!person) {
        this.$message({
          message: '请选择负责人',
          type: 'error'
        })
        return
      }
      // if (!this.fileCode) {
      //   this.$message({
      //     message: '请上传附件',
      //     type: 'error'
      //   })
      //   return
      // }
      if (!this.distributeLoading) {
        this.distributeLoading = this.$message({
          message: '派发中...',
          type: 'info',
          duration: 0
        })
      }
      const res = await createOrder({
        priority: urgent,
        reportTime: dayjs(time).format('YYYY-MM-DD HH:mm:ss'),
        type: origin,
        title,
        orderContent: desc,
        addressDetail: address,
        categoryItemId: type[2],
        userId: person,
        annexNo: this.fileCode
      })
      if (res?.code === 200) {
        this.distributeLoading.close()
        this.$message({
          message: '派发成功',
          type: 'success'
        })
        this.form = {
          title: '',
          urgent: '',
          time: '',
          origin: '',
          address: '',
          type: '',
          person: '',
          desc: '',
          fileList: [],
          fileCode: ''
        }
        this.$emit('input', false)
      }
    },
    handleChange(file, fileList) {
      console.log(file, fileList)
    },
    handleProgress() {
      if (!this.updateLoading) {
        this.updateLoading = this.$message({
          message: '上传中...',
          type: 'info',
          duration: 0
        })
      }
    },
    handleSuccess(res) {
      this.updateLoading.close()
      console.log(res)
      if (res?.code === 200) {
        this.fileCode = res.result[0].annexNum
        this.$message({
          message: '上传成功',
          type: 'success'
        })
      }
    },
    async getEventTypeFn() {
      const res = await getEventType()
      this.eventType = this.recursionData(res.result.children)
    },
    recursionData(val) {
      let _data = val.map(it => {
        return it.children
          ? {
              value: it.id,
              label: it.categoryName,
              children: this.recursionData(it.children)
            }
          : {
              value: it.id,
              label: it.categoryName
            }
      })
      return _data
    },
    async getEventResourceFn() {
      const res = await getEventResource()
      if (res?.data) {
        this.eventSource = res.data.map(it => ({
          label: it.itemValue,
          value: it.itemCode,
          id: it.itemId
        }))
      }
    },
    async getResponsiblePersonFn() {
      const res = await getResponsiblePerson()
      console.log(res)
      if (res?.code === 200) {
        this.personList = res.result.map(it => ({
          label: it.name,
          value: it.userId
        }))
      }
    }
  },
  mounted() {
    this.getEventTypeFn()
    this.getEventResourceFn()
    this.getResponsiblePersonFn()
  }
}
</script>

<style lang="less" scoped>
.task-distribution {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1004;
  width: 532px;
  height: 720px;
  background: rgba(9, 19, 34, 0.9);
  box-shadow: 0px 0px 22px 0px rgba(11, 54, 138, 0.35), 0px 2px 8px 0px rgba(0, 0, 0, 0.7),
    inset 0px 0px 8px 0px rgba(17, 146, 214, 0.35);
  border-radius: 11px;
  border: 1px solid #015c8c;
  .close {
    position: absolute;
    top: 15px;
    right: 18px;
    width: 31px;
    height: 31px;
    background-image: url('~@/assets/zhdd/close.png');
    cursor: pointer;
  }
  .title {
    width: 299px;
    height: 60px;
    margin: -1px auto 0;
    background-image: url('~@/assets/zhdd/title1.png');
    span {
      height: 36px;
      font-size: 36px;
      font-family: PingFangSC, PingFang SC;
      color: #ffffff;
      line-height: 70px;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .el-form {
    margin-top: 24px;
    width: 100%;
    padding: 0 30px;
    text-align: left;
  }
  /deep/ .el-form-item__label {
    font-size: 14px;
    font-family: SourceHanSansCN-Normal, SourceHanSansCN;
    font-weight: 400;
    color: #ffffff;
  }
  /deep/ .el-input__inner {
    height: 34px;
    background: rgba(0, 74, 143, 0.4)
      linear-gradient(
        360deg,
        rgba(181, 223, 248, 0) 0%,
        rgba(29, 172, 255, 0.29) 100%,
        #ffffff 100%
      );
    border: 1px solid rgba(0, 162, 255, 0.6);
    font-size: 13px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #ccf4ff;
  }
  /deep/ .el-textarea__inner {
    background: rgba(0, 74, 143, 0.4)
      linear-gradient(
        360deg,
        rgba(181, 223, 248, 0) 0%,
        rgba(29, 172, 255, 0.29) 100%,
        #ffffff 100%
      );
    border: 1px solid rgba(0, 162, 255, 0.6);
    font-size: 13px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #ccf4ff;
  }
  /deep/ .el-upload-list__item-name {
    color: #fff;
  }
  .el-select {
    width: 100%;
  }
  .el-cascader {
    width: 100%;
  }
  .custom {
    .el-select {
      width: 90px;
    }
    .el-date-editor.el-input {
      width: 130px;
    }
  }
  .upload {
    width: 86px;
    height: 26px;
    padding: 0 0 0 20px;
    line-height: 26px;
    background: url('~@/assets/zhdd/icon27.png') no-repeat;
    border: none;
    font-size: 13px;
    font-family: SourceHanSansCN-Normal, SourceHanSansCN;
    font-weight: 400;
    color: #ccf4ff;
  }
  .submit {
    width: 169px;
    height: 32px;
    line-height: 32px;
    padding: 0;
    margin-left: 90px;
    border: none;
    background: url('~@/assets/zhdd/btn.png') no-repeat center / 100% 100%;
  }
}
</style>
