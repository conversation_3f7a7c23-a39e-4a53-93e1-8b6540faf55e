<template>
  <div class="block" :style="{ height: blockHeight + 'px', width:blockWidth+'px' }">
    <div class="block_title" :class="[ isLong ?'long_block_title':'' ]" @click="clickBulletFrame">
      <span class="title">{{ title }}</span>
      <span class="subtitle">{{ subtitle }}</span>
      <ul class="btns" v-if="isListBtns">
        <li
          :class="['btnList', currentIndex == index ? 'active' : '']"
          v-for="(item, index) in textArr"
          :key="index"
          @click.stop="clickchange(index)"
        >{{ item }}</li>
      </ul>
      <div class="more" v-if="showMore" @click="showMoreFn">更多</div>
    </div>
    <div class="content">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    subtitle: {
      type: String,
      default: ''
    },
    textArr: {
      type: Array,
      default: () => ['社会保险', '住房保险']
    },
    isListBtns: {
      type: Boolean,
      default: true
    },
    showMore: {
      type: Boolean,
      default: false
    },
    blockHeight: {
      type: Number,
      default: 277
    },
    blockWidth: {
      type: Number,
      default: 611
    },
    isLong: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      currentIndex: 0
    }
  },
  methods: {
    clickchange (index) {
      this.currentIndex = index
      this.$emit('updateChange', this.currentIndex)
    },
    clickBulletFrame () {
      this.$emit('updateChange2', true)
    },
    showMoreFn () {
      this.$emit('handleShowMore', true)
    }
  }
}
</script>

<style lang="scss" scoped>
.block {
  width: 611px;
  box-sizing: border-box;
  z-index: 101;
  position: relative;
  .block_title {
    position: relative;
    width: 100%;
    height: 33px;
    background: url(~@/assets/cyjj/bg_title7.png) no-repeat;
    background-size: 100% 100%;
    display: flex;
    // justify-content: space-between;
    padding-left: 34px;
    position: relative;
    &.title_bg_Btn {
      background: url(~@/assets/leader/img/component/title_bg1.png) no-repeat;
      background-size: 100% 100%;
    }
    .title {
      font-size: 22px;
      font-family: PangMenZhengDao;
      letter-spacing: 2px;
      background: linear-gradient(180deg, #ffffff 20%, #7ed1ff 50%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-top: -8px;
      margin-right: 6px;
    }
    .subtitle {
      font-size: 14px;
      font-family: DINCond-RegularAlternate;
      font-weight: normal;
      color: #b3daff;
    }

    .btns {
      display: flex;
      align-items: center;
      height: 16px;
      // margin-right: 22px;
      position: absolute;
      top: 1px;
      right: 15px;
      .btnList {
        // width: 50px;
        height: 26px;
        font-size: 16px;
        font-family: PangMenZhengDao;
        color: #caecff;
        text-shadow: 0px 0px 1px #00132e;
        background: url(~@/assets/leader/img/component/title_btn.png) no-repeat;
        background-size: 100% 100%;
        line-height: 26px;
        cursor: pointer;
        &.active {
          color: #45daff;
          text-shadow: 0px 0px 4px rgba(0, 175, 255, 0.5), 0px 0px 1px rgba(0, 30, 50, 0.5);
        }
        &:not(:last-of-type) {
          margin-right: 14px;
        }
      }
    }
    .more {
      position: absolute;
      right: 22px;
      top: 1px;
      height: 16px;
      font-size: 16px;
      font-family: PangMenZhengDao;
      color: #caecff;
      line-height: 18px;
      text-shadow: 0px 0px 1px #00132e;
      cursor: pointer;
    }
  }
  .long_block_title {
    background: url(~@/assets/cyjj/bg_title_long.png) no-repeat;
    background-size: 100% 100%;
  }
  .content {
    position: relative;
    height: calc(100% - 33px);
  }
}
</style>
