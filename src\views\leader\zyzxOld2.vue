<template>
  <div class="leader_box">
    <div class="leader_sjts">
      <div class="left" :class="mkdx ? 'mkdxLeft' : ''">
        <div class="left1">
          <BlockBox title="作业总览" subtitle="Introduction to Hushu" class="box box1" :isListBtns="false" :showMore="true"
            :blockHeight="642" @handleShowMore="showMoreFn1">
          <!-- <BlockBox title="作业总览" subtitle="Introduction to Hushu" class="box box1" :isListBtns="false"
            :blockHeight="642"> -->
            <div class="cont">
              <div class="cont1" ref="chart1"></div>
              <div class="cont2">
                <div class="cont2Item">
                  <img class="cont2Img" src="@/assets/bjnj/zyxlImg1.png" alt="">
                  <div class="cont2Right">
                    <div class="cont2Right1">总种植面积</div>
                    <div class="cont2Right2">
                      <span>{{zyzl.total_planted_area || 0}}万亩</span>
                    </div>
                  </div>
                </div>
                <div class="cont2Item">
                  <img class="cont2Img" src="@/assets/bjnj/zyxlImg2.png" alt="">
                  <div class="cont2Right">
                    <div class="cont2Right1">总收获面积</div>
                    <div class="cont2Right2">
                      <span>{{zyzl.total_harvested_area || 0}}万亩</span>
                    </div>
                  </div>
                </div>
                <div class="cont2Item">
                  <img class="cont2Img" src="@/assets/bjnj/zyxlImg3.png" alt="">
                  <div class="cont2Right">
                    <div class="cont2Right1">去年进度</div>
                    <div class="cont2Right2">
                      <span>{{zyzl.last_year_progress ? (Number(zyzl.last_year_progress) * 100).toFixed(2) : 0}}%</span>
                    </div>
                  </div>
                </div>
                <div class="cont2Item">
                  <img class="cont2Img" src="@/assets/bjnj/zyxlImg4.png" alt="">
                  <div class="cont2Right">
                    <div class="cont2Right1">进度差距</div>
                    <div class="cont2Right2">
                      <span>{{zyzl.progress_gap ? (Number(zyzl.progress_gap) * 100).toFixed(2) : 0}}%<img src="@/assets/bjnj/zyzlSjt.png" alt="" v-if="Number(zyzl.progress_gap) > 0"><img src="@/assets/bjnj/zyzlxjt.png" alt="" v-if="Number(zyzl.progress_gap) < 0"></span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </BlockBox>
        </div>
      </div>
      <div class="right" :class="mkdx ? 'mkdxRight' : ''">
        <div class="right1">
          <BlockBox
            title="种植面积统计"
            subtitle="Party Building Information"
            class="box box7"
            :isListBtns="false"
            :blockHeight="332">
            <!-- <div v-show="barChartDataTrue.length > 0" class="njfbDw">万亩</div>
            <hor-bar-chart v-show="barChartDataTrue.length > 0" :data="barChartDataTrue" :init-option="barInitOptions" :options="zyjdBarOptions"  />
            <div class="cout" v-show="barChartDataTrue.length == 0">
              <img class="zwsjImg" src="@/assets/bjnj/zwsj.png" alt="">
            </div> -->
            <div class="cont" v-show="sewageDataList3.length > 0">
              <img src="@/assets/bjnj/zgl.png" @click="sfy" alt="">
              <!-- <img src="@/assets/bjnj/zmr.png" alt=""> -->
              <div class="contTable">
                <swiper-table :data="sewageDataList3" :titles="['地址', '面积']"
                  :widths="['238px', '100px']" content-height="264px" />
              </div>
              <img src="@/assets/bjnj/ygl.png" @click="xfy" alt="">
              <!-- <img src="@/assets/bjnj/ymr.png" alt=""> -->
            </div>
            <div class="couts" v-show="sewageDataList3.length == 0">
              <img class="zwsjImg" src="@/assets/bjnj/zwsj.png" alt="">
            </div>
          </BlockBox>
          <BlockBox title="机收面积分析" subtitle="Full Factor Grid Events" class="box box8" :isListBtns="false"
            :blockHeight="298">
            <div class="cont">
              <BarLineChart v-show="sczzData1[0].data.length > 0" :data="sczzData1" :options="sczzOptions2" :init-option="initOption2" @click="njzytj"></BarLineChart>
              <div class="couts" v-show="sczzData1[0].data.length == 0">
                <img class="zwsjImg" src="@/assets/bjnj/zwsj.png" alt="">
              </div>
            </div>
          </BlockBox>
        </div>
      </div>
    </div>
    <div class="middle" :class="mkdx ? 'mkdxMiddle' : ''">
      <BlockBox8
        title="收获进度统计"
        subtitle="Introduction to Hushu"
        class="box box10"
        :showMore3="false"
        :textArr2="amachTypeListText2"
        :isListBtns="false"
        :blockHeight="290"
        @updateChange="updateChange">
        <TrendLineChart v-show="zczData.length > 0" :options="zczOptions" :data="zczData" :init-option="initOptions4" />
        <div class="cout" v-show="zczData.length == 0">
          <img class="zwsjImg" src="@/assets/bjnj/zwsj.png" alt="">
        </div>
      </BlockBox8>
    </div>
    <div class="btnBox">
      <div class="btn" :class="curSeason == 'cg' ? 'btnActive' : ''" @click="btnChoice('cg')">春耕</div>
      <div class="btn" :class="curSeason == 'sanxia' ? 'btnActive' : ''" @click="btnChoice('sanxia')">三夏</div>
      <div class="btn" :class="curSeason == 'sanqiu' ? 'btnActive' : ''" @click="btnChoice('sanqiu')">三秋</div>
      <div class="btn" :class="curSeason == 'shuangqiang' ? 'btnActive' : ''" @click="btnChoice('shuangqiang')">双抢</div>
      <div class="select">
        <div class="selectName">年份：</div>
        <el-select v-model="year" placeholder="请选择任务状态" @change="changeSelect1">
          <el-option v-for="item in qgdCodesqlxOptions" :key="Number(item.itemCode)" :label="item.itemValue"
            :value="Number(item.itemCode)"></el-option>
        </el-select>
      </div>
    </div>
    <div class="map_box">
      <!-- <zlBg @emitMenu="emitMenu" /> -->
      <!-- <svgMap /> -->
      <LeaMap ref="leafletMap" @gridClick="gridClick" @operate="operate" @qctc="qctc" />
    </div>
    <leaderFooter :btns="[]" :activaIdx="activaIdx" />
    <div class="mkss" :class="!mkdx ? 'mkss1' : ''" @click="mkssBtn">
      <div class="mkss2">收缩</div>
    </div>
    <div class="sxBox" @click="sxTk">
      <img src="@/assets/bjnj/sxan.png" alt="">
    </div>
    <zyjdskxxPop ref="zyjdskxxPop" v-model="iszyjdskxxShow" @handle="eventInfoBtnClick7" :AreaId="areaId" />
    <skxxxqPop ref="skxxxqPop" v-model="isskxxxqShow" />
  </div>
</template>

<script>
import BlockBox from '@/components/leader/common/blockBox6.vue'
import BlockBox8 from '@/components/leader/common/blockBox8.vue'
import leaderFooter from '@/components/leader/common/leaderFooter2.vue'
import LeaMap from '@/components/map/LeafletMapNj.vue'
import zyjdskxxPop from '@/components/bjnj/zyjdskxxPop.vue' //作业进度实况信息
import skxxxqPop from '@/components/bjnj/skxxxqPop.vue' //实况信息详情
import SwiperTable from '@/components/bjnj/SwiperTable.vue'
import {
  getWorkProgress,
  getOverviewNew,
  getPlantingArea,
  getReceiptArea,
  getWorkProgressNew,
  mapScheduleInfo,
} from '@/api/njzl/zyzx.js'
import {
  cscpCurrentUserDetails
} from '@/api/bjnj/zhdd.js'

export default {
  components: {
    BlockBox,
    BlockBox8,
    leaderFooter,
    LeaMap,
    zyjdskxxPop,
    skxxxqPop,
    SwiperTable,
  },
  props: {},
  data() {
    return {
      popupList: [],
      page: 1,
      maxpage: 1,
      sewageDataList3: [
        ['1', '光明农机公司'],
        ['2', '新希望农机公司'],
        ['3', '人本股份有限公司'],
        ['4', '新希望农机公司'],
        ['5', '新希望农机公司'],
      ],
      curSeason: 'sanxia',
      year: 2024,
      areaId: 100000,
      areaFlag: 0,
      zyzl: {},
      iszyjdskxxShow: false,
      isskxxxqShow: false,
      state: '2024',
      qgdCodesqlxOptions: [
        {
          itemCode: '2024',
          itemValue: '2024'
        },
        {
          itemCode: '2023',
          itemValue: '2023'
        },
        {
          itemCode: '2022',
          itemValue: '2022'
        },
        {
          itemCode: '2021',
          itemValue: '2021'
        },
        {
          itemCode: '2020',
          itemValue: '2020'
        },
        {
          itemCode: '2019',
          itemValue: '2019'
        },
        {
          itemCode: '2018',
          itemValue: '2018'
        },
        {
          itemCode: '2017',
          itemValue: '2017'
        },
        {
          itemCode: '2016',
          itemValue: '2016'
        },
        {
          itemCode: '2015',
          itemValue: '2015'
        },
      ],
      mkdx: false,
      amachTypeListText: [['', '机收作业']],
      amachTypeListText2: [['', '全国']],
      activaIdx: -1,
      time: 0,
      timer1: null,
      gdCommunityName: '机耕面积',
      trendList1: [
        {
          name: '机耕面积',
          type: 'farmland'
        },
        {
          name: '机播面积',
          type: 'sowing'
        },
        {
          name: '机收面积',
          type: 'harvest'
        }
      ],
      barChartDataTrue: [],
      zyjdBarOptions:{
        gradientColor: ['#00B5FF','#BFE3FF']
      },
      sczzData1: [
        {
          name: '机收面积',
          data: [
            {
              name: 2018,
              value: 163,
            },
            {
              name: 2019,
              value: 130,
            },
            {
              name: 2020,
              value: 125,
            },
            {
              name: 2021,
              value: 146,
            },
            {
              name: 2022,
              value: 130,
            },
          ],
          type: 'bar',
          barWidth: 5,
          yAxisIndex: 0,
          color: ['#4CECAA', '#2F3BED'],
        },
        {
          name: '占比',
          data: [
            {
              name: 2018,
              value: 83,
            },
            {
              name: 2019,
              value: 99,
            },
            {
              name: 2020,
              value: 81,
            },
            {
              name: 2021,
              value: 94,
            },
            {
              name: 2022,
              value: 92,
            },
          ],
          type: 'line',
          yAxisIndex: 1,
        },
      ],
      sczzOptions2: {
        grid: [
          {
            top: '25%',
            bottom: '30%',
            left: '15%',
            right: '10%',
          },
        ],
        yAxis: [
          {
            minInterval: 1,
          },
          {
            minInterval: 1
          }
        ],
        barWidth: 5,
        barColor: ['rgba(64, 172, 255, 0.80)', 'rgba(64, 172, 255, 0.80)'],
        areaColor: '#fff',
        lineColor: 'rgba(0, 255, 255, 0.90)',
        leftYaxisName: '作业面积：万亩',
        rightYaxisName: '占比：%',
        tooltip: {
          show: true,
          type: 'shadow',
        },
        // yAxis: {
        //   min: 0,
        //   splitNumber: 5,
        //   unit: '亿元',
        // },
        legend: {
          show: true,
          padding: [20,80,0,0],
        },
        "openDataZoom": true,
        // 是否开启轮播
        isSeriesScorll: false,
        // 轮播的间隔时间
        scorllTimes: 1000,
        // 超过多少的数量轮播
        dataZoomNum: 8,
      },
      njType1: 'farmland',
      headName2: '2023',
      zczData: [
        ['product', ''],
        ['2020', 75],
        ['2021', 100],
        ['2022', 125],
        ['2023', 100],
        ['2024', 200],
      ],
      zczOptions: {
        smooth: true,
        colors: [['#009BFF', '#0077FF']],
      },
      btnActive: 2,
      dataArr: 50,
      dataX: 100,
      height1: { value: 300 },
    }
  },
  created() { },
  beforeDestroy() {
    this.timer1 && clearInterval(this.timer1)
  },
  watch: {},
  computed: {
    barInitOptions() {
      return {
        yAxis: {
          nameTextStyle: {
            width: 200
          }
        },
        xAxis: [{
          show:false,
          axisTick: {
            show: false // 不显示坐标轴刻度线
          },
          axisLine: {
            show: false, // 不显示坐标轴线
          },
          axisLabel: {
            show: false, // 不显示坐标轴上的文字
          },
          // max: Math.max(...this.barChartData.map(item => { return item[1] } )),
          nameTextStyle: {
            width: 200
          }
        }],
        grid: {
          right: '20%',
          left: '5%'
        },
        tooltip: {
          show: true,
          triggerOn: 'mousemove',
          formatter: (params) => {
            var relVal = ''
            for (var i = 0, l = params.length; i < l; i++) {
              var unit = '万亩'
              relVal += params[i].marker + params[i].value + unit
            }
            return relVal
          },
        },
        // dataZoom: [
        //   {
        //     type: 'inside', // 内置型式的 dataZoom 组件
        //     yAxisIndex: [0], // 对应 x 轴的索引，默认为 0
        //     start: 0, // 起始位置的百分比
        //     end: 40, // 结束位置的百分比
        //     realtime: true,// 启用实时滚动
        //     zoomOnMouseWheel: false,  // 关闭滚轮缩放
        //     moveOnMouseWheel: true, // 开启滚轮平移
        //     moveOnMouseMove: true  // 鼠标移动能触发数据窗口平移
        //   },
        //   {
        //     type: 'slider',
        //     realtime: true,
        //     startValue: 0,
        //     endValue: 5,
        //     width: '2',
        //     height: '100%',
        //     yAxisIndex: [0], // 控制y轴滚动
        //     fillerColor: 'rgba(154, 181, 215, 1)', // 滚动条颜色
        //     borderColor: 'rgba(17, 100, 210, 0.12)',
        //     backgroundColor: '#cfcfcf', //两边未选中的滑动条区域的颜色
        //     handleSize: 0, // 两边手柄尺寸
        //     showDataShadow: false, //是否显示数据阴影 默认auto
        //     showDetail: false, // 拖拽时是否展示滚动条两侧的文字
        //     top: '1%',
        //     right: '5'
        //   }
        // ],
      }
    },
    initOption2() {
      return {
        yAxis: [
          {
            minInterval: 1,
            nameTextStyle: {
              padding: [30, 0, 0, 0]
            },
          },
          {
            minInterval: 1
          }
        ],
        tooltip: {
          formatter: (params) => {
            let relVal = params[0].name
            for (let i = 0, l = params.length; i < l; i++) {
              let seriesName = params[i].seriesName + ':'
              let unit = params[i].seriesType == 'bar' ? '万亩' : '%'
              relVal += '<br/>' + params[i].marker + seriesName + params[i].value + unit
            }
            return relVal
          },
        },
        series: [
          {
            barWidth: 20,
          },
        ],
      }
    },
    initOptions4() {
      return {
        yAxis: {
          name: '%',
        },
        xAxis: {
          axisLabel: {
            // interval: 2,
            // rotate: 40,
            textStyle: {
              fontSize: 12,
              // color: '#000',
            },
          },
        },
        tooltip: {
          formatter: params => {
            var relVal = params[0].name
            for (var i = 0, l = params.length; i < l; i++) {
              var unit = '%'
              relVal += '<br/>' + params[i].marker + params[i].value + unit
            }
            return relVal
          }
        }
      }
    },
  },
  mounted() {
    this.cscpCurrentUserDetails()
  },
  methods: {
    sfy() {
      if (this.page > 1) {
        this.page = this.page - 1
        this.getPlantingArea()
      }
    },
    xfy() {
      if (this.page < this.maxpage) {
        this.page = this.page + 1
        this.getPlantingArea()
      }
    },
    eventInfoBtnClick7(info) {
      this.$refs.skxxxqPop.workScheduleInfo(info)
      this.isskxxxqShow = true
    },
    // 近12月事件趋势
    drawLineChart(id, dataArr, dataX, height1) {
      let option = {
          /** 标题*/
          title: [
              {
                  text: '{val|' + dataArr + '%}\n{name|' + '整体进度' + '}',
                  bottom: '18%',
                  left: 'center',
                  textStyle: {
                      rich: {
                          val: {
                              fontSize: 24,
                              color: '#00FFFA',
                              padding: [10, 0],
                          },
                          name: {
                              fontSize: 14,
                              color: '#C2DDFC',
                          },
                      },
                  },
                  triggerEvent: true,
              },
          ],

          /** 关闭必图例*/
          legend: {
              show: false,
          },
          series: [
              {
                  name: '最外部进度条',
                  type: 'gauge',
                  radius: '93%',
                  splitNumber: 15,
                  axisLine: {
                      lineStyle: {
                          color: [
                              [
                                  1,
                                  {
                                      type: 'linear',
                                      x: 0,
                                      y: 1,
                                      x2: 0,
                                      y2: 0,
                                      colorStops: [
                                          {
                                              offset: 0,
                                              color: '#032347', // 0% 处的颜色
                                          },
                                          {
                                              offset: 0.5,
                                              color: '#0B8BAA', // 100% 处的颜色
                                          },
                                          {
                                              offset: 1,
                                              color: '#03FCF3', // 100% 处的颜色
                                          },
                                      ],
                                      global: false, // 缺省为 false
                                  },
                              ],
                          ],
                          width: 12,
                      },
                  },
                  axisLabel: {
                      show: false,
                  },
                  axisTick: {
                      show: false,
                  },
                  splitLine: {
                      show: true,
                      length: 30,
                      lineStyle: {
                          color: '#031f45',
                          width: 3,
                      },
                  },
                  itemStyle: {
                      show: false,
                  },
                  detail: {
                      show: false,
                  },
                  title: {
                      // 标题
                      show: false,
                  },
                  data: [
                      {
                          name: 'title',
                          value: dataArr,
                      },
                  ],
                  pointer: {
                      show: false,
                  },
                  animationDuration: 4000,
              },
              {
                  name: '外二红线',
                  type: 'gauge',
                  radius: '84.5%',
                  axisLine: {
                      lineStyle: {
                          color: [
                              [dataArr / dataX, '#031f45'],
                              [1, '#B74443'],
                          ],
                          width: 4,
                      },
                  },
                  axisLabel: {
                      show: false,
                  },
                  axisTick: {
                      show: false,
                  },
                  splitLine: {
                      show: false,
                  },
                  itemStyle: {
                      show: false,
                  },
                  detail: {
                      show: false,
                  },
                  title: {
                      // 标题
                      show: false,
                  },
                  data: [
                      {
                          name: 'title',
                          value: dataArr,
                      },
                  ],
                  pointer: {
                      show: false,
                  },
                  animationDuration: 4000,
              },
              {
                  name: '刻度尺',
                  type: 'gauge',
                  radius: '81%',
                  splitNumber: 10, // 刻度数量
                  min: 0, // 最小刻度
                  max: dataX, // 最大刻度
                  // 仪表盘轴线相关配置
                  axisLine: {
                      lineStyle: {
                          color: [
                              [
                                  1,
                                  {
                                      type: 'radial',
                                      x: 0.5,
                                      y: 0.6,
                                      r: 0.6,
                                      colorStops: [
                                          {
                                              offset: 0.85,
                                              color: '#031F46', // 0% 处的颜色
                                          },
                                          {
                                              offset: 0.93,
                                              color: '#086989', // 100% 处的颜色
                                          },
                                          {
                                              offset: 1,
                                              color: '#12D7EF', // 100% 处的颜色
                                          },
                                      ],
                                  },
                              ],
                          ],
                          width: 500,
                      },
                  },
                  /** 分隔线样式*/
                  splitLine: {
                      show: true,
                      length: 14,
                      lineStyle: {
                          width: 3,
                          color: '#12E5FE', // 用颜色渐变函数不起作用
                      },
                  },
                  /** 刻度线*/
                  axisTick: {
                      show: true,
                      splitNumber: 20,
                      lineStyle: {
                          color: '#12E5FE', // 用颜色渐变函数不起作用
                          width: 1,
                      },
                      length: 5,
                  },
                  /** 刻度标签*/
                  axisLabel: {
                      distance: 2,
                      color: '#CEF3FE',
                  },
                  detail: {
                      show: false,
                  },
                  animationDuration: 4000,
              },
              {
                  name: '渐变进度',
                  type: 'gauge',
                  radius: '80%',
                  splitNumber: 15,
                  axisLine: {
                      lineStyle: {
                          color: [
                              [
                                  dataArr / dataX,
                                  {
                                      type: 'linear',
                                      x: 0,
                                      y: 1,
                                      x2: 0,
                                      y2: 0,
                                      colorStops: [
                                          {
                                              offset: 0,
                                              color: 'rgba(60,207,223,0)', // 0% 处的颜色
                                          },
                                          {
                                              offset: 0.9,
                                              color: 'rgba(60,207,223,0.5)', // 100% 处的颜色
                                          },
                                          {
                                              offset: 1,
                                              color: 'rgba(60,207,223,0.9)', // 100% 处的颜色
                                          },
                                      ],
                                      global: false, // 缺省为 false
                                  },
                              ],
                          ],
                          width: 30,
                      },
                  },
                  axisLabel: {
                      show: false,
                  },
                  axisTick: {
                      show: false,
                  },
                  splitLine: {
                      show: false,
                  },
                  itemStyle: {
                      show: false,
                  },
                  detail: {
                      show: false,
                  },
                  title: {
                      // 标题
                      show: false,
                  },
                  data: [
                      {
                          name: 'title',
                          value: dataArr,
                      },
                  ],
                  pointer: {
                      show: false,
                  },
                  animationDuration: 4000,
              },
              {
                  name: '内层带指针',
                  type: 'gauge',
                  radius: '54%',
                  splitNumber: 10, // 刻度数量
                  min: 0, // 最小刻度
                  max: dataX, // 最大刻度
                  // 仪表盘轴线相关配置
                  axisLine: {
                      lineStyle: {
                          color: [
                              [
                                  1,
                                  {
                                      type: 'radial',
                                      x: 0.5,
                                      y: 0.59,
                                      r: 0.6,
                                      colorStops: [
                                          {
                                              offset: 0.72,
                                              color: '#032046',
                                          },
                                          {
                                              offset: 0.94,
                                              color: '#086989',
                                          },
                                          {
                                              offset: 0.98,
                                              color: '#0FAFCB',
                                          },
                                          {
                                              offset: 1,
                                              color: '#0EA4C1',
                                          },
                                      ],
                                  },
                              ],
                          ],
                          width: 1000,
                      },
                  },
                  /** 分隔线样式*/
                  splitLine: {
                      show: false,
                  },
                  /** 刻度线*/
                  axisTick: {
                      show: false,
                  },
                  /** 刻度标签*/
                  axisLabel: {
                      show: false,
                  },
                  /** 仪表盘指针*/
                  pointer: {
                      show: true,
                      length: '95%',
                      width: 5, // 指针粗细
                  },
                  /** 仪表盘指针样式*/
                  itemStyle: {
                      color: '#12E5FF',
                  },
                  data: [
                      {
                          value: dataArr,
                      },
                  ],
                  detail: {
                      show: false,
                  },
              },
          ],
          graphic: {
              elements: [
                  {
                      type: 'line',
                      z: 4,
                      style: {
                          fill: '#075173',
                          stroke: '#075173',
                          lineWidth: 2,
                          shadowBlur: 15,
                          shadowOffsetX: 0,
                          shadowOffsetY: -4,
                          shadowColor: '#13E6FF',
                      },
                      shape: {
                          x1: height1.value * 0.57,
                          y1: 0,
                          x2: 0,
                          y2: 0,
                      },
                      left: 'center',
                      bottom: '21%',
                      silent: true,
                  },
                  {
                      type: 'line',
                      z: 4,
                      style: {
                          fill: '#075173',
                          stroke: '#075173',
                          lineWidth: 2,
                          shadowBlur: 15,
                          shadowOffsetX: 0,
                          shadowOffsetY: -4,
                          shadowColor: '#13E6FF',
                      },
                      shape: {
                          x1: height1.value * 0.43,
                          y1: 0,
                          x2: 0,
                          y2: 0,
                      },
                      left: 'center',
                      bottom: '28.5%',
                      silent: true,
                  },
              ],
          },
      };
      let myChart = this.$echarts.init(id, 'ucdc');
      myChart.setOption(option);
      window.addEventListener('resize', function() {
        myChart.resize();
      });
    },
    btnChoice(index) {
      this.curSeason = index
      this.$refs.leafletMap.CloseMarker(this.popupList)
      this.init()
      this.mapScheduleInfo()
    },
    wgsjYearMethod(item) {
      this.gdCommunityName = item
      let data = this.trendList1.find(item => { return item.name === this.gdCommunityName })
      this.njType1 = data.type
      this.getWorkProgress()
    },
    async getWorkProgress() {
      if (this.timer1) {
        clearInterval(this.timer1)
      }
      this.barShow = false
      let params = {
        farmingSeason: this.curSeason,
        type: this.njType1,
        year: this.headName2,
        areaId: this.areaId,
        areaFlag: this.areaFlag,
      }
      let res = await getWorkProgress(params)
      // this.barChartData = [['product', '']]
      // .concat(res.data.map(item => [item.province, item.data]))
      if (res.data.length > 0) {
        this.barChartData = [['product', '']]
        let arr = res.data.map(item => [item.area_name, item.data.toFixed(0)])
        this.barChartData.push(...arr)
        this.barShow = true
        this.forBarChart()
      } else {
        this.barChartDataTrue = []
      }
    },
    async getOverviewNew() {
      let params = {
        farmingSeason: this.curSeason,
        year: this.year,
        areaId: this.areaId == 100000 ? '' : this.areaId,
        areaFlag: this.areaFlag,
      }
      let res = await getOverviewNew(params)
      if (res.data.length > 0) {
        this.zyzl = res.data[0]
        this.drawLineChart(this.$refs.chart1, (Number(this.zyzl.overall_progress) * 100).toFixed(2), this.dataX, this.height1);
      } else {
        this.zyzl = {}
        this.drawLineChart(this.$refs.chart1, 0, this.dataX, this.height1);
      }
    },
    async getPlantingArea() {
      if (this.timer1) {
        clearInterval(this.timer1)
      }
      this.barShow = false
      let params = {
        farmingSeason: this.curSeason,
        year: this.year,
        areaId: this.areaId == 100000 ? '' : this.areaId,
        areaFlag: this.areaFlag,
        page: this.page,
        size: '5',
      }
      let res = await getPlantingArea(params)
      if (res.data.records.length > 0) {
        this.maxpage = Math.ceil(res.data.total/5)
        this.sewageDataList3 = []
        let arr = res.data.records.map(item => [item.area_name, item.total_planted_area + '万亩'])
        this.sewageDataList3.push(...arr)
        this.barShow = true
        // this.forBarChart()
      } else {
        this.sewageDataList3 = []
      }
    },
    async getReceiptArea() {
      let params = {
        farmingSeason: this.curSeason,
        year: this.year,
        areaId: this.areaId == 100000 ? '' : this.areaId,
        areaFlag: this.areaFlag,
      }
      let res = await getReceiptArea(params)
      if (res.data.length > 0) {
        this.sczzData1[0].data = [].concat(
          res.data.map((it) => ({
            name: it.area_name,
            value: Number(it.harvested_area).toFixed(2)
          }))
        )
        this.sczzData1[1].data = [].concat(
          res.data.map((it) => ({
            name: it.area_name,
            value: it.machine_income_ratio
          }))
        )
      } else {
        this.sczzData1[0].data = []
        this.sczzData1[1].data = []
      }
    },
    async getWorkProgressNew() {
      let params = {
        farmingSeason: this.curSeason,
        year: this.year,
        areaId: this.areaId == 100000 ? '' : this.areaId,
        areaFlag: this.areaFlag,
      }
      let res = await getWorkProgressNew(params)
      if (res.data.length > 0) {
        this.zczData = [['product', '']]
        let arr = res.data.map(item => [item.dt_day, (Number(item.overall_progress) * 100).toFixed(2)])
        this.zczData.push(...arr)
      } else {
        this.zczData = []
      }
    },
    async mapScheduleInfo() {
      let params = {
        farmingSeason: this.curSeason,
        year: this.year,
        areaId: this.areaId == 100000 ? '' : this.areaId,
        areaFlag: this.areaFlag,
      }
      let res = await mapScheduleInfo(params)
      if (res.data.length > 0) {
        this.popupList = res.data
        this.$refs.leafletMap.InformationMarker(res.data)
      } else {
      }
    },
    njzytj(e) {
    },
    changeSelect1() {
      this.$refs.leafletMap.CloseMarker(this.popupList)
      // this.init()
      // this.mapScheduleInfo()
    },
    forBarChart() {
      const getBarChartDataTrue = () => {
        this.barChartDataTrue = this.barChartData.slice(this.time, this.time + 10)
        this.time += 1
        if (this.time + 10 > this.barChartData.length) {
          this.time = 0
        }
      }
      this.timer1 = setInterval(getBarChartDataTrue, 2000)
    },
    showMoreFn1() {
      let params = {
        farmingSeason: this.curSeason,
        year: this.year,
        areaId: this.areaId == 100000 ? '' : this.areaId,
        areaFlag: this.areaFlag,
      }
      this.$refs.zyjdskxxPop.workScheduleList(params)
      this.iszyjdskxxShow = true
    },
    updateChange() {
    },
    handleChange(i) {
    },
    mkssBtn() {
      this.mkdx = !this.mkdx
      this.$store.commit('invokerightShow4', this.mkdx)
    },
    sxTk() {
      this.$refs.leafletMap.CloseMarker(this.popupList)
      this.init()
      this.mapScheduleInfo()
    },
    async cscpCurrentUserDetails(data) {
      let res = await cscpCurrentUserDetails(data)
      if (res?.code == '0') {
        this.areaId = Number(res.data.areaId);
        this.areaLevel_init = res.data.areaLevel;
        this.areaFlag = res.data.areaLevel;
        this.$refs.leafletMap.lastAreaCode.push(res.data.areaId)
        if(res.data.areaId!=res.data.areaIdNew){ //区县级别
            this.$refs.leafletMap.loadGeoJsonLayerFromUrl(`https://geo.datav.aliyun.com/areas_v3/bound/${res.data.areaId}.json`, 'geojson', false)
            // this.$refs.leafletMap.loadGeoJsonLayerFromUrl(`https://dpzs.camtc.cn/areas_v3/bound/${res.data.areaId}.json`, 'geojson', false)
          }else{
            this.$refs.leafletMap.loadGeoJsonLayerFromUrl(`https://geo.datav.aliyun.com/areas_v3/bound/${res.data.areaId}_full.json`, 'geojson', res.data.areaLevel==0?true:false)
            // this.$refs.leafletMap.loadGeoJsonLayerFromUrl(`https://dpzs.camtc.cn/areas_v3/bound/${res.data.areaId}_full.json`, 'geojson', res.data.areaLevel==0?true:false)
          }
        if (res.data.areaLevel == 3) { //区县
          this.barShow_tr = false;
        } else {
          this.barShow_tr = true;
        }
        this.init()
        this.mapScheduleInfo()
      }
    },
    init() {
      // this.getWorkProgress()
      this.getOverviewNew()
      this.getPlantingArea()
      this.getReceiptArea()
      this.getWorkProgressNew()
    },
    gridClick(properties) {
      this.$refs.leafletMap.CloseMarker(this.popupList)
      this.$store.commit('invokerightShow2', properties)
      if (properties.level == "province") {
        this.areaFlag = 1
      } else if (properties.level == "city") {
        this.areaFlag = 2
      } else if (properties.level == "district" && this.areaFlag != 3) {
        this.areaFlag = 3
      }
      this.areaId = properties.adcode
      this.init()
      this.mapScheduleInfo()
      // this.removeAllPoi()
    },
    operate(areaId,areaId2) {
      this.$refs.leafletMap.CloseMarker(this.popupList)
      this.areaId = areaId
      this.$store.commit('invokerightShow3', areaId2)
      if (this.areaId) {
        this.areaFlag = this.areaLevel_init + this.$refs.leafletMap.lastAreaCode.indexOf(areaId)
      }
      this.init();
      this.mapScheduleInfo()
      // this.removeAllPoi()
    },
    qctc(){
      // this.toolTipShow=false;
      this.activaIdx = -1
      this.$refs.LeaderFooter.activaIdx = -1
      this.$refs.LeaderFooter.activeList = []
    },
  },
};
</script>
<style lang="less" scoped>
@font-face {
  font-family: QuartzRegular;
  src: url(~@/assets/leader/font/QuartzRegular.ttf);
}

@keyframes rotateS {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes rotateN {
  0% {
    transform: rotate(360deg);
  }

  100% {
    transform: rotate(0);
  }
}

@keyframes rotateY {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotateY(360deg);
  }
}
/deep/ .el-input__inner {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #C2DDFC;
  line-height: 22px;
  text-shadow: 0px 2px 3px rgba(0,48,72,0.5);
  text-align: left;
  font-style: normal;
}
/deep/ .leaflet-popup-close-button {
  display: none;
}
/deep/ .leaflet-popup-tip-container {
  display: none;
}
/deep/ .leaflet-popup {
  margin-bottom: 0;
}
/deep/ .leaflet-popup-content-wrapper {
  background: none;
}
/deep/ .leaflet-popup-content {
  width: auto !important;
  background: rgba(22,210,56,0.8);
  border-radius: 3px;
  border: 1px solid #01E446;
  font-family: DINAlternate, DINAlternate;
  font-weight: bold;
  font-size: 16px;
  color: #FFFFFF;
  line-height: 23px;
  text-align: center;
  font-style: normal;
  padding: 0 5px;
  margin: 0;
}

.map_box {
  position: absolute;
  width: 1920px;
  height: 1080px;
  top: 0px;
  left: 0;
  z-index: 999;
  // border: 1px solid red;
}
.middle {
  width: 1856px;
  height: 290px;
  position: absolute;
  left: 50%;
  bottom: 33px;
  transform: translateX(-50%);
  background: url(~@/assets/bjnj/dbmc.png) no-repeat center / 100% 100%;
  z-index: 1003;
  -webkit-transition: all .5s ease-in;
  -moz-transition: all .5s ease-in;
  transition: all .5s ease-in;
  .box10 {
    .cont {
      width: 100%;
      height: 100%;
      margin: 0 auto;
      position: relative;
      .couts {
        width: 100%;
        height: 100%;
        .zwsjImg {
          height: 80%;
          margin: 0 auto;
          margin-top: 45px;
        }
      }
      .nfxz {
        position: absolute;
        left: 57.5%;
        top: 12px;
        z-index: 1003;
        width: 100px;
        height: 20px;
        ::v-deep .el-input {
          font-size: 12px;
        }
        ::v-deep .el-input__inner {
          color: #ccf4ff;
          height: 20px;
          line-height: 20px;
          background: linear-gradient(180deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #FFFFFF 100%), rgba(0, 74, 143, 0.4);
          border: 1px solid rgba(0, 162, 255, 0.6);
        }
        ::v-deep .el-input__icon {
          line-height: 20px;
        }
      }
      .yfxz {
        position: absolute;
        left: 63.5%;
        top: 12px;
        z-index: 1003;
        width: 100px;
        height: 20px;
        ::v-deep .el-input {
          font-size: 12px;
        }
        ::v-deep .el-input__inner {
          color: #ccf4ff;
          height: 20px;
          line-height: 20px;
          background: linear-gradient(180deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #FFFFFF 100%), rgba(0, 74, 143, 0.4);
          border: 1px solid rgba(0, 162, 255, 0.6);
        }
        ::v-deep .el-input__icon {
          line-height: 20px;
        }
      }
      .fhBox {
        width: 80px;
        border: 1px solid rgba(0, 162, 255, 0.6);
        border-radius: 5px;
        position: absolute;
        left: 69.5%;
        top: 12px;
        z-index: 1003;
        font-size: 12px;
        height: 20px;
        color: #7e94a9;
        padding-top: 1px;
        display: flex;
        align-items: center;
        padding: 0 10px;
        cursor: pointer;
        .fh {
          height: 16px;
          margin-right: 4px;
        }
      }
      .fhBox:active{
        color: #ccf4ff;
      }
    }
    .cout {
      width: 100%;
      height: 100%;
      .zwsjImg {
        height: 80%;
        margin: 0 auto;
        margin-top: 45px;
      }
    }
  }
}
.mkdxMiddle {
  bottom: -325px;
  opacity: 0;
}
.btnBox {
  width: 736px;
  height: 43px;
  position: absolute;
  top: 124px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1003;
  .btn {
    width: 105px;
    height: 43px;
    background: linear-gradient( 180deg, rgba(17,77,168,0) 0%, rgba(36,132,212,0.5) 100%), rgba(2,52,114,0.8);
    box-shadow: inset 0px 0px 2px 0px #57AFFF;
    border-radius: 2px;
    border: 1px solid #0A5EAA;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #C2DDFC;
    line-height: 43px;
    text-shadow: 0px 2px 3px rgba(0,48,72,0.5);
    text-align: center;
    font-style: normal;
    float: left;
    margin-right: 24px;
  }
  .btnActive {
    width: 105px;
    height: 43px;
    background: linear-gradient( 180deg, rgba(17,77,168,0) 0%, rgba(36,132,212,0.5) 100%), rgba(4,98,218,0.8);
    box-shadow: inset 0px 0px 2px 0px #57AFFF;
    border-radius: 2px;
    border: 1px solid #0A5EAA;
  }
  .select {
    width: 220px;
    height: 43px;
    float: left;
    .selectName {
      float: left;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #C2DDFC;
      line-height: 43px;
      text-shadow: 0px 2px 3px rgba(0,48,72,0.5);
      text-align: left;
      font-style: normal;
      margin-right: 11px;
    }
    /deep/ .el-input {
      width: 158px;
      height: 43px;
      .el-input__inner {
        width: 158px;
        height: 43px;
        background: linear-gradient(180deg , rgba(181,223,248,0) 0%, rgba(29,172,255,0.29) 100%, #FFFFFF 100%), rgba(0,74,143,0.4);
        border: 1px solid rgba(0,162,255,0.6);
      }
    }
  }
}

.leader_sjts {
  width: 1920px;
  height: 1080px;
  position: absolute;
  top: 0;
  display: flex;
  justify-content: space-between;

  .leader_city_box {
    width: 450px;

    .leader_zt_contain {
      height: 100%;
    }
  }

  .left_bg {
    width: 460px;
    height: 1033px;
    background: url(~@/assets/leader/img/component/left_bg.png) no-repeat center / 100% 100%;
    position: absolute;
    left: 50px;
    top: 25px;
    z-index: 102;
  }

  .right_bg {
    width: 520px;
    height: 1033px;
    background: url(~@/assets/leader/img/component/right_bg.png) no-repeat center / 100% 100%;
    position: absolute;
    right: 50px;
    top: 25px;
    z-index: 102;
  }


  .box {
    .cout {
      width: 100%;
      height: 100%;
    }
  }

  .box1 {
    .cont {
      width: 100%;
      height: 100%;
      .cont1 {
        width: 100%;
        height: 282px;
        padding-top: 10px;
      }
      .cont2 {
        width: 100%;
        height: 306px;
        padding-left: 24px;
        .cont2Item {
          width: 412px;
          height: 52px;
          margin-bottom: 22px;
          .cont2Img {
            float: left;
            width: 52px;
            height: 52px;
          }
          .cont2Right {
            float: right;
            width: 341px;
            height: 36px;
            background: linear-gradient( 90deg, rgba(0,123,255,0.6) 0%, rgba(0,197,255,0) 100%);
            margin-top: 7px;
            .cont2Right1 {
              float: left;
              margin-left: 30px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              font-size: 16px;
              color: #FFFFFF;
              line-height: 36px;
              text-align: right;
              font-style: normal;
            }
            .cont2Right2 {
              float: right;
              margin-right: 42px;
              span {
                font-family: DINAlternate, DINAlternate;
                font-weight: bold;
                font-size: 24px;
                color: #78DEFB;
                line-height: 36px;
                letter-spacing: 2px;
                text-align: left;
                font-style: normal;
                background: linear-gradient(180deg, #46B9FF 0%, #ADE0FF 100%);
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                img {
                  width: 14px;
                  height: 20px;
                  margin-left: 4px;
                }
              }
            }
          }
        }
      }
    }
    .cout {
      width: 100%;
      height: 100%;
      .zwsjImg {
        height: 80%;
        margin: 0 auto;
        margin-top: 45px;
      }
    }
  }

  .box2 {
  }
  
  .box7 {
    position: relative;
    .cont {
      width: 100%;
      height: 100%;
      padding: 6px 0;
      img {
        float: left;
        margin: 114px 7px;
      }
      .contTable {
        float: left;
        width: 360px;
        height: 100%;
      }
    }
    .couts {
      width: 100%;
      height: 100%;
      .zwsjImg {
        height: 80%;
        margin: 0 auto;
        margin-top: 45px;
      }
    }
  }

  .box8 {
    margin-top: 22px;
    .cont {
      width: 100%;
      height: 100%;
      margin: 0 auto;
      position: relative;
      .couts {
        width: 100%;
        height: 100%;
        .zwsjImg {
          height: 80%;
          margin: 0 auto;
          margin-top: 45px;
        }
      }
      .nfxz {
        position: absolute;
        left: 57.5%;
        top: 12px;
        z-index: 1003;
        width: 100px;
        height: 20px;
        ::v-deep .el-input {
          font-size: 12px;
        }
        ::v-deep .el-input__inner {
          color: #ccf4ff;
          height: 20px;
          line-height: 20px;
          background: linear-gradient(180deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #FFFFFF 100%), rgba(0, 74, 143, 0.4);
          border: 1px solid rgba(0, 162, 255, 0.6);
        }
        ::v-deep .el-input__icon {
          line-height: 20px;
        }
      }
      .yfxz {
        position: absolute;
        left: 63.5%;
        top: 12px;
        z-index: 1003;
        width: 100px;
        height: 20px;
        ::v-deep .el-input {
          font-size: 12px;
        }
        ::v-deep .el-input__inner {
          color: #ccf4ff;
          height: 20px;
          line-height: 20px;
          background: linear-gradient(180deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #FFFFFF 100%), rgba(0, 74, 143, 0.4);
          border: 1px solid rgba(0, 162, 255, 0.6);
        }
        ::v-deep .el-input__icon {
          line-height: 20px;
        }
      }
      .fhBox {
        width: 80px;
        border: 1px solid rgba(0, 162, 255, 0.6);
        border-radius: 5px;
        position: absolute;
        left: 69.5%;
        top: 12px;
        z-index: 1003;
        font-size: 12px;
        height: 20px;
        color: #7e94a9;
        padding-top: 1px;
        display: flex;
        align-items: center;
        padding: 0 10px;
        cursor: pointer;
        .fh {
          height: 16px;
          margin-right: 4px;
        }
      }
      .fhBox:active{
        color: #ccf4ff;
      }
    }
    .cout {
      width: 100%;
      height: 100%;
      .zwsjImg {
        height: 80%;
        margin: 0 auto;
        margin-top: 45px;
      }
    }
  }
  
  .box10 {
    .cout {
      width: 100%;
      height: 100%;
      .zwsjImg {
        height: 80%;
        margin: 0 auto;
        margin-top: 45px;
      }
    }
  }

  .left {
    width: 450px;
    display: flex;
    justify-content: space-between;
    margin-top: 87px;
    margin-left: 32px;
    position: relative;
    left: 0;
    opacity: 1;
    z-index: 1003;
    -webkit-transition: all .5s ease-in;
    -moz-transition: all .5s ease-in;
    transition: all .5s ease-in;
  }
  .mkdxLeft {
    left: -450px;
    opacity: 0;
  }

  .right {
    width: 450px;
    display: flex;
    justify-content: space-between;
    margin-top: 87px;
    margin-right: 32px;
    position: relative;
    right: 0;
    opacity: 1;
    z-index: 1003;
    -webkit-transition: all .5s ease-in;
    -moz-transition: all .5s ease-in;
    transition: all .5s ease-in;
  }

  .mkdxRight {
    right: -450px;
    opacity: 0;
  }
  .zwsjImg {
    height: 80%;
    margin: 0 auto;
    margin-top: 36px;
  }
}

.mkss {
  width: 44px;
  height: 44px;
  position: absolute;
  bottom: 735px;
  right: 540px;
  z-index: 999;
  background: url(~@/assets/map/ss2.png) no-repeat center / 100% 100%;

  .mkss2 {
    font-size: 16px;
    color: #fff;
    opacity: 0;
  }
}

.mkss1 {
  width: 44px;
  height: 44px;
  position: absolute;
  bottom: 735px;
  right: 540px;
  z-index: 999;
  background: url(~@/assets/map/ss1.png) no-repeat center / 100% 100%;

  .mkss2 {
    font-size: 16px;
    color: #fff;
    opacity: 0;
  }
}
.sxBox {
  width: 44px;
  height: 44px;
  position: absolute;
  bottom: 685px;
  right: 539px;
  z-index: 1099;
  img {
    width: 44px;
    height: 44px;
  }
}
.wgsj_fx {
  width: 100%;

  // height: 100%;
  .select_s {
    position: absolute;
    right: 0;
    top: -40px;
    line-height: 30px;
    font-size: 16px;
    font-family: PingFangSC, PingFang SC;
    color: #caecff;
    text-shadow: 0px 0px 1px #00132e;

    ::v-deep .el-dropdown button {
      font-family: PingFangSC, PingFang SC;
      font-size: 16px;
      color: #caecff;
      line-height: 21px;
      text-align: center;
      font-style: normal;

      // width: 100px;
      height: 30px;
      // background: url(~@/assets/mskx/icon_drop.png) no-repeat center / 100% 100%;
      background: transparent;
      border: none;
      padding: 0 0 0 5px;
    }

    ::v-deep .el-icon-arrow-down:before {
      // content: '\e790' !important;
      color: #caecff;
    }

    ::v-deep .el-popper[x-placement^='bottom'] .popper__arrow:after {
      display: none;
    }

    ::v-deep .el-popper .popper__arrow:after {
      border-bottom-color: #185493 !important;
    }
  }

  .select_r1 {
    position: absolute;
    right: 0;
    top: -32px;
    line-height: 30px;
    font-size: 16px;
    font-family: PingFangSC, PingFang SC;
    color: #caecff;
    text-shadow: 0px 0px 1px #00132e;

    ::v-deep .el-dropdown button {
      // width: 100px;
      height: 30px;
      // background: url(~@/assets/mskx/bg_select.png) no-repeat center / 100% 100%;
      background: transparent;
      border: none;
      padding: 0 0 0 5px;

      font-family: PingFangSC, PingFang SC;
      font-size: 16px;
      color: #caecff;
      line-height: 21px;
      text-align: center;
      font-style: normal;
    }

    ::v-deep .el-icon-arrow-down:before {
      // content: '\e790' !important;
      color: #caecff;
    }

    ::v-deep .el-popper[x-placement^='bottom'] .popper__arrow:after {
      border-bottom-color: #185493 !important;
    }
  }
}
</style>