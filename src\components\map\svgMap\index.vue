<template>
	<div class="mapContainer">
		<div ref="svgMap" style="width: 100%; height: 100%"></div>
		<!-- 地图弹窗 -->
		<div ref="tooltip" v-show="showTooltipDom" class="dom-tooltip">
			<p class="title">
				{{ tooltipAreaName }}
			</p>
			<div
				v-if="curSeason == 'sanx' && index !== 0 && index !== 1"
				v-for="(item, index) in tooltipFields"
				:key="index"
				class="tooltip-content"
			>
				<p class="tooltip-key">{{ item.label }}:</p>
				<p class="tooltip-value">
					{{
						contentSwitch == 1
							? tooltipValues[item.key]
								? tooltipValues[item.key]
								: 0
							: tooltipValues[item.key2]
							? tooltipValues[item.key2]
							: 0
					}}
					{{ item.Company }}
				</p>
			</div>
			<div
				v-if="curSeason == 'cg' || curSeason == 'sq' || curSeason == 'shuangqiang'"
				v-for="(item, index) in tooltipFields"
				:key="index"
				class="tooltip-content"
			>
				<p class="tooltip-key">{{ contentSwitch == 1 ? item.label : item.label2 }}:</p>
				<p class="tooltip-value">
					{{
						contentSwitch == 1
							? tooltipValues[item.key]
								? tooltipValues[item.key]
								: 0
							: tooltipValues[item.key2]
							? tooltipValues[item.key2]
							: 0
					}}
					{{ item.Company }}
				</p>
			</div>
		</div>
	</div>
</template>

<script>
import * as echarts from 'shzl-datav/node_modules/echarts'
import svg_data from './data.json'
let points = []
export default {
	props: {
		contentSwitch: {
			type: Number,
			default: 1,
		},
		curSeason: {
			type: String,
			default: '',
		},
	},
	data() {
		return {
			myChart: null,
			option: null,
			mapId: 'svgMap',
			showTooltipDom: false,
			tooltipAreaName: '',
			tooltipFields: [
				{
					key: 'collected_area',
					key2: 'planted_area',
					label: '早稻已收',
					label2: '晚稻已种',
					Company: '万亩',
				},
				{
					key: 'harvest_pgs',
					key2: 'sowing_pgs',
					label: '收获进度',
					label2: '播种进度',
					Company: '%',
				},
				{
					key: 'machn_coll_area',
					key2: 'machn_sowed_area',
					label: '机收面积',
					label2: '机播面积',
					Company: '万亩',
				},
				{
					key: 'ppot_of_machn_coll',
					key2: 'ppot_of_machn_sowed',
					label: '机收占比',
					label2: '机播占比',
					Company: '%',
				},
			],
			tooltipValues: {},
			curPoint: [],
		}
	},
	async mounted() {
		await this.init()
		// 添加分色图层
		// this.loadAreaColorLayer(svg_data.area_data, true)
		// 移除分色图层
		// setTimeout(() => {
		//   this.loadAreaColorLayer([], false)
		// }, 5000)
		// 添加点位
		// this.loadPointLayer('湖北省', true)
		// 移除点位
		// setTimeout(() => {
		//   this.loadPointLayer('', false)
		// }, 5000)
		// 高亮区域
		// this.highlightMultiArea(['湖北省', '湖南省'], true)
		// 取消高亮区域
		// setTimeout(() => {
		//   this.highlightMultiArea(['湖北省', '湖南省'], false)
		// }, 5000)
	},
	methods: {
		/**
		 * 初始化地图
		 */
		async init() {
			this.myChart = echarts.init(this.$refs.svgMap)
			const response = await fetch('./static/svg/100000.svg')
			const data = await response.text()
			echarts.registerMap(this.mapId, { svg: data })
			this.option = {
				visualMap: {
					type: 'continuous',
					show: false,
					left: '20%',
					bottom: '20%',
					text: '',
					min: 0,
					max: 100000,
					inRange: {
						color: [
							// 'rgba(0, 80, 179, 1)',
							// 'rgba(171, 217, 233, 1)',
							// 'rgba(255, 255, 191, 1)',
							// 'rgba(253, 174, 97, 1)',
							// 'rgba(244, 109, 67, 1)',
							// 'rgba(165, 0, 38, 1)'

							'#395AFE',
							'#1FCEE6',
							'#33C127',
							'#FFD03B',
							'#FF7E2A',
							'#FF7B60',
						],
					},
					seriesIndex: '0',
					textStyle: {
						color: '#fff',
						fontSize: 12,
					},
					calculable: true,
				},
				geo: {
					left: 0,
					right: 0,
					map: this.mapId,
					roam: true,
					layoutCenter: ['50%', '55%'], //地图中心
					layoutSize: '120%', //地图大小
					itemStyle: {
						borderWidth: 0,
						color: 'transparent',
					},
					tooltip: {
						show: false,
						backgroundColor: 'rgba(17,16,203,0.8)',
					},
					emphasis: {
						label: {
							show: true,
							color: '#fff',
						},
						itemStyle: {
							areaColor: 'rgba(255,150,73,0.85)',
						},
					},
				},
				tooltip: { show: true },
				series: [
					{
						type: 'map',
						geoIndex: 0,
						roam: false,
						label: {
							show: true,
						},
						// selectedMode: 'multiple',
						selectedMode: false,
						data: [],
					},
					{
						type: 'scatter',
						coordinateSystem: 'geo',
						label: {
							show: true,
							formatter: function(params) {
								const txt = '预警总数：' + params.data.num
								return `{b|${txt}}`
							},
							position: 'top',
							distance: 0,
							rich: {
								b: {
									color: '#FFE9E9',
									fontSize: 11,
									fontWeight: 'bold',
									backgroundColor: {
										image: './imgs/highlight-bg.png',
									},
									height: 1,
									width: 80,
									align: 'right',
									verticalAlign: 'middle',
									padding: [12, 3, 10, 0],
								},
							},
						},
						emphasis: {
							show: false,
							color: '#f60',
						},
						symbol: `image://./imgs/highlight-point.png`,
						symbolSize: [36, 36],
						data: [this.curPoint],
						tooltip: {
							show: false,
							formatter: (params) => {
								return params.name
							},
						},
					},
				],
			}
			this.myChart.setOption(this.option)
			let that = this
			this.myChart.on('click', function(params) {
				console.log('点击', params)
				const pixelPoint = [params.event.offsetX, params.event.offsetY]
				// console.log('转换前坐标', pixelPoint)
				// const coverPoint = that.covertPixelPoint(params.name, pixelPoint)
				// points.push(coverPoint)
				// console.log('各省点数据', JSON.stringify(points))

				if (params.seriesType === 'scatter') {
					console.log('点图层点击', params)
					that.$emit('warningEvent', params)
				}
			})
		},
		/**
		 * 加载区域分色图层
		 * @param {Array} data 区域数据
		 * @param {Boolean} isShow 是否显示
		 */
		loadAreaColorLayer(data, isShow, visualMapName = '') {
			let sjdata = data.map((item) => ({
				...item,
				name: item.area_name,
				value: this.contentSwitch == 1 ? item.harvest_pgs : item.sowing_pgs,
			}))
			let that = this
			if (isShow) {
				this.option.visualMap.show = true
				this.option.visualMap.text = ['', visualMapName]
				this.option.visualMap.min = Math.min(...data.map((item) => (this.contentSwitch == 1 ? item.harvest_pgs : item.sowing_pgs)))
				this.option.visualMap.max = Math.max(...data.map((item) => (this.contentSwitch == 1 ? item.harvest_pgs : item.sowing_pgs)))

				this.option.geo.tooltip = {
					show: true,
					padding: 0,
					formatter: function(params) {
						console.log('sss', params)

						that.tooltipAreaName = params.name
						if (params.data) {
							that.tooltipValues = params.data
						} else {
							that.tooltipValues.collected_area = 0
							that.tooltipValues.planted_area = 0
							that.tooltipValues.harvest_pgs = 0
							that.tooltipValues.sowing_pgs = 0
							that.tooltipValues.machn_coll_area = 0
							that.tooltipValues.machn_sowed_area = 0
							that.tooltipValues.ppot_of_machn_coll = 0
							that.tooltipValues.ppot_of_machn_sowed = 0
						}

						that.showTooltipDom = true
						return that.$refs.tooltip
					},
				}
			} else {
				this.option.visualMap.show = false
				this.option.geo.tooltip = {
					show: false,
				}
			}
			this.option.series[0].data = sjdata
			// isShow ? (this.option.series[0].data = data) : (this.option.series[0].data = [])
			this.myChart.setOption(this.option)
		},
		/**
		 * 加载点图层
		 * @param {String} name 地图名称
		 * @param {Boolean} show 是否显示
		 */
		loadPointLayer(name, show) {
			if (show) {
				// const point = svg_data.area_point.find(e => e.name === name)
				// // console.log('loadPointLayer', point)
				// this.curPoint = {
				//   name,
				//   value: point.coord
				// }
				// this.curPoint = name.map(item => ({
				//   name: item.area,
				//   value: item.num,
				// }))

				this.curPoint = name.map((item) => {
					const point = svg_data.area_point.find((e) => e.name === item.area)
					return {
						name: item.area,
						num: item.num,
						areaId: item.areaCode,
						value: point.coord,
					}
				})
			} else {
				this.curPoint = []
			}
			console.log('this.curPoint123', this.curPoint)
			this.option.series[1].data = this.curPoint
			console.log('loadPointLayer', this.option)

			this.myChart.setOption(this.option)
			// this.init()
		},
		/**
		 * 转换像素坐标为逻辑坐标
		 * @param {String} name 名称
		 * @param {Array} pixelPoint 像素坐标
		 */
		covertPixelPoint(name, pixelPoint) {
			const convert = this.myChart.convertFromPixel({ geoIndex: 0 }, pixelPoint)
			const coord = [Number(convert[0].toFixed(2)), Number(convert[1].toFixed(2))]
			// console.log('转换后坐标', name, convert)
			return {
				name,
				coord,
			}
		},
		/**
		 * 高亮多区域
		 * @param {Array} names 地图名称集合
		 * @param {Boolean} show 显示或隐藏
		 */
		highlightMultiArea(names, show) {
			if (show) {
				this.myChart.dispatchAction({
					type: 'highlight',
					seriesIndex: 0,
					name: names,
				})
			} else {
				// const allAreaNames = svg_data.area_data.map(item => item.name)
				// console.log('allAreaNames', allAreaNames)
				this.myChart.dispatchAction({
					type: 'downplay',
					seriesIndex: 0,
					// name: allAreaNames
				})
			}
		},
	},
}
</script>
<style scoped lang="scss">
.mapContainer {
	width: 100%;
	height: 100%;
	position: relative;
	.dom-tooltip {
		width: auto;
		height: auto;
		padding: 10px 10px 20px 10px;
		background: url('~@/assets/map/dialog-bg.png') no-repeat center / 100% 100%;

		.title {
			font-size: 16px;
			color: #ffffff;
			font-weight: bold;
			line-height: 26px;
			text-shadow: 0px 3px 2px rgba(0, 69, 255, 0.63);
			text-align: left;
		}

		.tooltip-content {
			display: flex;
			height: 30px;

			.tooltip-key {
				height: 30px;
				font-weight: 400;
				line-height: 40px;
				font-size: 14px;
				color: #fefefe;
			}

			.tooltip-value {
				margin-left: 12px;
				font-weight: bold;
				line-height: 40px;
				font-size: 18px;
				color: #ffba00;
			}
		}
	}
}
</style>
