<template>
	<div class="xzqh-scjg-detail-dialog" v-if="show">
		<div class="ai_waring">
			<div class="title">
				<span>应急资源</span>
				<i class="el-icon-close close_btn" @click="closeEmitai"></i>
			</div>
			<div class="content">
				<div v-if="type == '1'" class="table_box">
					<SwiperTableMap
						:titles="['序号', '中心名称', '负责人', '联系方式', '具体地址', '作业类型', '操作']"
						:widths="['6%', '14%', '10%', '14%', '22%', '24%', '10%']"
						:data="tableList1"
						:contentHeight="'100%'"
						:settled="settled"
						@operate="operate"
						v-if="tableList1.length"
					></SwiperTableMap>
					<img class="zwsjImg" src="@/assets/bjnj/zwsj.png" v-else />
				</div>
				<div v-if="type == '2'" class="table_box">
					<SwiperTableMap
						:titles="['序号', '队伍名称', '负责人', '联系方式', '具体地址', '作业类型', '操作']"
						:widths="['6%', '14%', '10%', '14%', '22%', '24%', '10%']"
						:data="tableList2"
						:contentHeight="'100%'"
						:settled="settled"
						@operate="operate"
						v-if="tableList2.length"
					></SwiperTableMap>
					<img class="zwsjImg" src="@/assets/bjnj/zwsj.png" v-else />
				</div>
				<div v-if="type == '3'" class="table_box">
					<SwiperTableMap
						:titles="['序号', '中心名称', '负责人', '联系方式', '具体地址', '作业类型', '操作']"
						:widths="['6%', '14%', '10%', '14%', '22%', '24%', '10%']"
						:data="tableList3"
						:contentHeight="'100%'"
						:settled="settled"
						@operate="operate"
						v-if="tableList3.length"
					></SwiperTableMap>
					<img class="zwsjImg" src="@/assets/bjnj/zwsj.png" v-else />
				</div>
				<div class="fy_page">
					<el-pagination
						@current-change="pageNumChange"
						:current-page="pageNum"
						:page-size="this.pageSize"
						layout="total, prev, pager, next, jumper"
						:total="this.total"
					>
					</el-pagination>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import SwiperTableMap from './table/RealTimeEventTable3.vue'
// api
import { myMixins } from '@/libs/myMixins.js'
import { queryPageTEmergencyCenterByCondition, areaTree, queryPageTDryingCenterByCondition } from '@/api/bjnj/zhdd.js'
import { getCscpBasicHxItemCode } from '@/api/bjnj/zhdd.js'

export default {
	name: 'RealTimeEventDialogQgd',
	mixins: [myMixins],
	components: { SwiperTableMap },
	props: {
		value: {
			type: Boolean,
			default: false,
		},
		list: {
			type: Array,
			default: () => {
				return []
			},
		},
		// userAreaId: {
		// 	type: String,
		// 	default: '',
		// },

		isJzAmach: {
			type: Boolean,
			default: false,
		},
		type: {
			type: String,
			default: () => {
				return '1'
			},
		},
		level: {
			type: String,
			default: () => {
				return ''
			},
		},
		data: {
			type: Object,
			default: () => {},
		},
	},
	data() {
		return {
			qgdCodesqlxOption: [],
			qgdCodesqlxOptions: [],
			btnList: [
				{
					name: '区域农机社会化服务中心',
				},
				{
					name: '农机应急作业服务队',
				},
				{
					name: '区域农业应急救灾中心',
				},
				{
					name: '机具保有量',
				},
			],
			tabs: ['未办结', '办结'],
			btnsArr: [
				{
					name: '未办结',
					normalBg: require('@/assets/shzl/map/left_n.png'),
					activeBg: require('@/assets/shzl/map/left_a.png'),
				},
				{
					name: '已办结',
					normalBg: require('@/assets/shzl/map/right_n.png'),
					activeBg: require('@/assets/shzl/map/right_a.png'),
				},
			],
			urgentOptions: [
				{
					value: '0',
					label: '普通',
				},
				{
					value: '1',
					label: '突发',
				},
				{
					value: '2',
					label: '重要',
				},
				{
					value: '3',
					label: '紧急',
				},
			],
			eventTypeOptions: [
				{
					value: '0',
					label: '普通',
				},
			],
			timeOutOptions: [
				{
					value: '0',
					label: '是',
				},
				{
					value: '1',
					label: '否',
				},
			],
			proityValue: '',
			timeOutValue: '',
			settled: true,
			total: 100,
			pageNum: 1,
			pageSize: 8,
			finished: 1,
			tabList1_all: [],
			tabList2_all: [],
			tabList3_all: [],
			tableList1: [],
			tableList2: [],
			tableList3: [],
			tableList4: [],
			jobTypeList: [],
			businessStateList: [],
			isJzOptions: [
				{
					value: '1',
					label: '是',
				},
				{
					value: '0',
					label: '否',
				},
			],
			equipmentFlag: '',
		}
	},
	watch: {
		qgdCodejjcdOptions(val) {
			if (this.qgdCodejjcdOptions.length > 0) {
			}
		},
		// // 防止出现获取不到annexNum
		// userAreaId: {
		// 	immediate: true,
		// 	handler(annexNum) {
		// 		// console.log('annexNum', annexNum)
		// 		this.userAreaId = annexNum + ''
		// 		this.areaTree()
		// 		// if (annexNum) {
		// 		//   this.queryAnnexByAnnexNum();
		// 		// }
		// 	},
		// },
	},
	computed: {
		show() {
			return this.value
		},
		isJzAmach_() {
			return this.isJzAmach
		},
	},
	mounted() {
		Promise.all([
			this.getCscpBasicHxItemCode1('jobType'),
			this.getCscpBasicHxItemCode1('businessState'),
			this.getCscpBasicHxItemCode1('agmachTypeId'),
		]).then((res) => {
			this.switchChangeMethods()

			// this.queryPageTEmergencyCenterByCondition({
			// 	area: this.gdObj.area,
			// 	current: this.pageNum,
			// 	name: this.gdObj.name,
			// 	size: this.pageSize,
			// 	type: this.tableShow + 1,
			// })
		})

		// this.queryPageTEmergencyCenterByCondition({
		// 	area: this.data.regionCode,
		// 	current: this.pageNum,
		// 	size: this.pageSize,
		// 	type: this.type == '1' || this.type == '3' ? '1' : this.type == '2' ? '2' : '',
		// 	level: this.level,
		// })
	},
	methods: {
		async getCscpBasicHxItemCode1(data) {
			let res = await getCscpBasicHxItemCode(data)
			console.log('res', res)
			console.log('data', data)
			if (res?.code == '0') {
				if (data == 'jobType') {
					this.jobTypeList = res.data
				} else if (data == 'businessState') {
					this.businessStateList = res.data
				} else if (data == 'agmachTypeId') {
					this.qgdCodesqlxOptions = res.data
				}
			}
		},

		async queryPageTEmergencyCenterByCondition(data) {
			this.tableList1 = []
			this.tableList2 = []
			let res = await queryPageTEmergencyCenterByCondition(data)
			if (this.type == '1') {
				let records = res.data.records
				records.forEach((element) => {
					if (element.service) {
						element.service = element.service.split(',')
						let arr = []
						element.service.forEach((ele) => {
							this.jobTypeList.forEach((item) => {
								if (ele == item.itemCode) {
									arr.push(item.itemValue)
								}
							})
						})
						element.service = arr
						element.service = element.service.join(',')
					}
				})
				this.tabList1_all = res.data.records
				this.tableList1 = [].concat(
					records.map((it, index) => [
						this.pageSize * (this.pageNum - 1) + index + 1,
						it.name,
						it.contact,
						it.phone,
						it.address,
						it.service,
					]),
				)
				this.total = res.data.total
			} else if (this.type == '2') {
				let records = res.data.records
				records.forEach((element) => {
					if (element.service) {
						element.service = element.service.split(',')
						let arr = []
						element.service.forEach((ele) => {
							this.jobTypeList.forEach((item) => {
								if (ele == item.itemCode) {
									arr.push(item.itemValue)
								}
							})
						})
						element.service = arr
						element.service = element.service.join(',')
					}
				})
				this.tabList2_all = res.data.records
				this.tableList2 = [].concat(
					records.map((it, index) => [
						this.pageSize * (this.pageNum - 1) + index + 1,
						it.name,
						it.contact,
						it.phone,
						it.address,
						it.service,
					]),
				)
				this.total = res.data.total
			}
		},
		async queryPageTEmergencyCenterByCondition2(data) {
			this.tableList3 = []
			let res = await queryPageTEmergencyCenterByCondition(data)
			let records = res.data.records
			records.forEach((element) => {
				if (element.service) {
					element.service = element.service.split(',')
					let arr = []
					element.service.forEach((ele) => {
						this.jobTypeList.forEach((item) => {
							if (ele == item.itemCode) {
								arr.push(item.itemValue)
							}
						})
					})
					element.service = arr
					element.service = element.service.join(',')
				}
			})
			this.tabList3_all = res.data.records
			this.tableList3 = [].concat(
				records.map((it, index) => [
					this.pageSize * (this.pageNum - 1) + index + 1,
					it.name,
					it.contact,
					it.phone,
					it.address,
					it.service,
				]),
			)
			this.total = res.data.total
		},
		async queryPageTDryingCenterByCondition(data) {
			this.tableList3 = []
			let res = await queryPageTDryingCenterByCondition(data)
			let records = res.data.records
			records.forEach((element) => {
				this.businessStateList.forEach((ele) => {
					if (element.businessState == ele.itemCode) {
						element.businessState = ele.itemValue
					}
				})
			})
			this.tabList3_all = res.data.records
			this.tableList3 = [].concat(
				records.map((it, index) => [
					this.pageSize * (this.pageNum - 1) + index + 1,
					it.name,
					it.talent,
					it.contact,
					it.phone,
					it.address,
					it.businessState,
				]),
			)
			this.total = res.data.total
		},

		handleChange(value) {
			this.pageNum = 1
		},

		switchChangeMethods() {
			if (this.type == '1' || this.type == '2') {
				this.queryPageTEmergencyCenterByCondition({
					area: this.data.regionCode,
					current: this.pageNum,
					size: this.pageSize,
					type: this.type == '1' || this.type == '3' ? '1' : this.type == '2' ? '2' : '',
					level: this.level,
				})
			} else if (this.type == '3') {
				// this.queryPageTDryingCenterByCondition({
				//   current: this.pageNum,
				//   size: this.pageSize,
				// })
				this.queryPageTEmergencyCenterByCondition2({
					area: this.data.regionCode,
					current: this.pageNum,
					size: this.pageSize,
					type: this.type == '1' || this.type == '3' ? '1' : this.type == '2' ? '2' : '',
					level: this.level,
				})
			}
		},
		operate(it, e, u) {
			console.log('it', it)
			console.log('e', e)
			console.log('u', u)
			console.log('tableList1_all', this.tabList1_all)
			let info = {}
			if (this.type == '1') {
				info = this.tabList1_all[it]
			} else if (this.type == '2') {
				info = this.tabList2_all[it]
			} else if (this.type == '3') {
				console.log('this.tabList3_all--==',this.tabList3_all);
				info = this.tabList3_all[it]
			}
			console.log('info--==', info)
			this.$emit('handle', it, info)
		},
		closeEmitai() {
			this.tableShow = '1'
			this.equipmentFlag = ''
			this.$emit('setCurrentTabIndex', 0)
			this.$emit('input', false)
		},
		pageNumChange(val) {
			this.pageNum = val
			this.switchChangeMethods()
			// this.queryPageTEmergencyCenterByCondition({
			//   current: this.pageNum,
			//   size: this.pageSize,
			// })
		},

		// searchBtn() {
		// 	this.switchChangeMethods()
		// },
		handleChangeEquipmentFlag(value) {
			console.log('value--==', value)
			this.equipmentFlag = value
		},
		// resetBtn() {
		// 	this.pageNum = 1
		// 	this.equipmentFlag = ''
		// 	this.switchChangeMethods()
		// },
	},
}
</script>

<style lang="less" scoped>
.bg-header {
	position: absolute;
	width: 100%;
	height: 1080px;
	background: rgba(2, 14, 50, 0.75);
	z-index: 999999;
	top: 0;
	left: 0;
}

.zwsjImg {
	width: 250px;
	margin: 20px 0;
}
/deep/ .ivu-select {
	width: 189px;
}

/deep/ .ivu-select-selection {
	width: 189px;
	height: 34px;
	background: rgba(0, 74, 143, 0.4) linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
	border: 1px solid rgba(0, 162, 255, 0.6);
}

/deep/ .ivu-select-selection .ivu-select-selected-value {
	font-size: 13px;
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	color: #ccf4ff;
	line-height: 34px;
}

/deep/ .ivu-input {
	background: rgba(0, 74, 143, 0.4) linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
	border: 1px solid rgba(0, 162, 255, 0.6);
	font-size: 16px;
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	color: #ccf4ff;
	line-height: 22px;
}

/deep/ .ivu-select-placeholder {
	height: 34px !important;
	font-size: 13px !important;
	line-height: 34px !important;
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	color: #ccf4ff !important;
}

/deep/ .ivu-page-item {
	background: transparent;
	color: #fff;
}

/deep/ .ivu-page-item a {
	color: #fff;
}

/deep/ .ivu-page-prev,
/deep/ .ivu-page-next {
	background: transparent;
}

/deep/ .el-input__inner {
	color: #ccf4ff;
}

.xzqh-scjg-detail-dialog {
	width: 1920px;
	height: 1080px;
	position: absolute;
	left: 0;
	top: 0;
	background-color: rgba(0, 0, 0, 0.3);
	z-index: 1105;
	display: flex;
	justify-content: center;
	align-items: center;
}

.ai_waring {
	width: 1367px;
	height: 652px;
	background: #001c40;

	border: 1px solid #023164;
	z-index: 1101;

	.title {
		width: 100%;
		height: 48px;
		line-height: 48px;
		// background: url(~@/assets/map/dialog/title7.png) no-repeat;

		padding: 0 16px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background: #104993;
		span {
			font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
			font-weight: normal;
			font-size: 16px;
			color: #d0deee;
			line-height: 16px;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}
		.close_btn {
			font-size: 14px;
			color: #80caff;
			cursor: pointer;
		}
	}

	.content {
		padding: 18px 16px;

		.btns {
			// border: 1px solid red;
			margin: 0 auto;
			display: flex;
			justify-content: center;

			& > div {
				width: 154px;
				height: 53px;

				span {
					font-size: 20px;
					font-family: YouSheBiaoTiHei;
					color: #ffffff;
					line-height: 53px;
					background: linear-gradient(180deg, #ffffff 0%, #ffeba0 100%);
					background-clip: text;
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
				}
			}
		}

		.table_box {
			height: 510px;
			overflow-y: auto;

			&::-webkit-scrollbar {
				width: 4px; /* 滚动条宽度 */
			}

			//轨道
			&::-webkit-scrollbar-track {
				background: #04244e; /* 滚动条轨道背景 */
				border-radius: 10px;
				padding: 5px 0;
			}

			//滑块
			&::-webkit-scrollbar-thumb {
				background: #82aed0; /* 滑块背景颜色 */
				border-radius: 10px;
				height: 15px;
			}

			//悬停滑块颜色
			&::-webkit-scrollbar-thumb:hover {
				background: #ff823b; /* 鼠标悬停时滑块颜色 */
			}

			//箭头
			&::-webkit-scrollbar-button {
				display: none; /* 隐藏滚动条的箭头按钮 */
			}

			.czBtn {
				display: inline-block;
				button {
					margin: 0 8px !important;
				}
			}
			/deep/ .el-table th.el-table__cell {
				background-color: rgba(0, 0, 0, 0);
			}
			/deep/ .el-table tr {
				background-color: rgba(0, 0, 0, 0);
			}
			/deep/ .el-table td.el-table__cell,
			/deep/ .el-table th.el-table__cell.is-leaf {
				// border-bottom: none;
				border-bottom: 1px solid #263e5d !important;
			}
			/deep/ .el-table,
			/deep/ .el-table__expanded-cell {
				background-color: rgba(0, 0, 0, 0);
			}
			/deep/ .el-table__header-wrapper {
				background-color: #023164;
				// border-bottom: 1px solid rgba(255, 255, 255, 0.15);
			}
			/deep/ .el-table th.el-table__cell.is-leaf {
				// border-right: 1px solid #2968c7;
			}
			/deep/ .el-table th.el-table__cell > .cell {
				text-align: left;

				font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
				font-weight: normal;
				font-size: 14px;
				color: #159aff;
				line-height: 14px;
				font-style: normal;
				text-transform: none;
			}
			/deep/ .el-table .cell {
				text-align: left;
				-webkit-user-select: text;
				-moz-user-select: text;
				-ms-user-select: text;
				user-select: text;
			}

			/deep/ .el-table .el-checkbox__inner {
				border: 1px solid rgba(0, 162, 255, 0.6) !important;
				background-color: #001c40 !important;
			}

			/deep/ .el-table__row {
			}
			/deep/ .el-table__row:nth-child(odd) {
			}
			/deep/ .el-table__body tr.hover-row > td.el-table__cell {
				background-color: rgba(0, 0, 0, 0);
			}

			/deep/ .el-table__body-wrapper {
				height: calc(100% - 48px);

				overflow-x: auto;
				&::-webkit-scrollbar {
					width: 4px; /* 滚动条宽度 */
					height: 4px;
				}

				//轨道
				&::-webkit-scrollbar-track {
					background: #04244e; /* 滚动条轨道背景 */
					border-radius: 10px;
					padding: 5px 0;
				}

				//滑块
				&::-webkit-scrollbar-thumb {
					background: #82aed0; /* 滑块背景颜色 */
					border-radius: 10px;
					height: 15px;
				}

				//悬停滑块颜色
				&::-webkit-scrollbar-thumb:hover {
					background: #ff823b; /* 鼠标悬停时滑块颜色 */
				}

				//箭头
				&::-webkit-scrollbar-button {
					display: none; /* 隐藏滚动条的箭头按钮 */
				}
			}
		}

		.fy_page {
			margin-top: 24px;
			display: flex;
			justify-content: flex-end;
			align-items: center;

			/deep/ .el-pager {
				margin: 0 8px;

				background: #001c40;
				li {
					// width: 32px;
					height: 32px;
					border-radius: 2px;
					padding: 10px !important;
					line-height: 13px !important;
					&.active {
						color: #ffffff;
						background: #159aff;
						border-radius: 2px 2px 2px 2px;
					}
				}

				.number,
				.more {
					// width: 32px;
					height: 32px;
					color: #ffffff;
					padding: 10px 12px !important;
					border-radius: 2px 2px 2px 2px;
					border: 1px solid #dcdee2;
					background-color: #001c40;

					&:hover {
						color: #ffffff;
						background: #159aff;
					}
					&:before {
						line-height: 14px;
					}
				}
			}

			/deep/ .el-pagination {
				display: flex;
				justify-content: flex-start;
				align-items: center;
			}

			/deep/ .el-pagination span {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #d0deee;
				text-align: left;
				font-style: normal;
				text-transform: none;
				&:not {
				}
			}

			/deep/ .el-pagination button {
				width: 32px;
				height: 32px;
				font-family: Helvetica Neue, Helvetica Neue;
				font-weight: 400;
				font-size: 14px;
				background: #001c40;
				color: #d0deee;
				line-height: 22px;
				text-align: center;
				font-style: normal;
				text-transform: none;
				border: 1px solid #dcdee2;
				border-radius: 2px;
				padding: 10px 8px !important;
			}

			/deep/ .el-pagination__jump {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #d0deee;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}
			/deep/ .el-pagination__editor.el-input {
				width: 48px;
				margin: 0 8px;
			}

			/deep/ .el-input__inner {
				border-radius: 2px 2px 2px 2px;
				border: 1px solid #dcdee2;
				background-color: #001c40;
				color: #d0deee;
			}
		}
		.tableBox {
			height: 41px;
			margin-bottom: 21px;

			.tableItem {
				float: left;
				width: 180px;
				height: 41px;
				background: url(~@/assets/map/dialog/btn5.png) no-repeat center / 100% 100%;
				margin-right: 10px;
				font-family: PingFangSC, PingFang SC;
				font-size: 22px;
				color: #ffffff;
				line-height: 41px;
				text-align: center;
				font-style: normal;
				text-transform: none;

				&:nth-child(1),
				&:nth-child(2),
				&:nth-child(3) {
					width: 250px;
				}

				span {
					background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
					background-clip: text;
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
					font-family: YouSheBiaoTiHei;
					cursor: pointer;
				}
			}

			.tableActive {
				background: url(~@/assets/map/dialog/btn4.png) no-repeat center / 100% 100%;

				span {
					font-size: 22px;
					color: #ffffff;
					// font-weight: 600;
					background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
					background-clip: text;
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
					font-family: YouSheBiaoTiHei;
				}
			}
		}
	}
}

// /deep/ .ivu-page-total {
//   font-size: 18px;
//   color: #fff;
// }
</style>
