/* 黑色语音背景 */
.txdd_box .video-container.common-container {
  display: none !important;
}

/* 正在发言 */
.txdd_box .rtc-meet .talking {
  position: absolute;
  left: 508px;
  top: 102px;
  font-size: 16px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  line-height: 22px;
}

/* 人员列表 */
.txdd_box .meet-side .user-list {
  width: 902px;
  position: absolute;
  top: 122px;
  left: 332px;
  background: rgba(9, 19, 34, 0.9);
  height: 510px;
  display: flex;
  flex-wrap: wrap;
  padding: 10px 20px;
  height: 516px;
  overflow-y: scroll;
}

.txdd_box .meet-side .user-list::-webkit-scrollbar {
  width: 6px;
  background: transparent;
}

.txdd_box .meet-side .user-list::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 2px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgb(45, 124, 228);
  height: 100px;
}

.txdd_box .meet-side .user-list .user-item {
  width: 180px;
  height: 230px;
  background: url(~@/assets/rhtx/img/item_bg.png) no-repeat;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  margin-right: 30px;
}

.txdd_box .meet-side .user-list .user-item .left-info.txdd_left_info .info-desc .name {
  font-size: 18px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #FFFFFF;
  line-height: 25px;
}



/* 底部按钮 */
.txdd_box .meet-container .meet-bottom-tools {
  margin-top: 500px;
}

.txdd_box .tool-btn.meetOff .text {
  color: #fff;
}

/* 人员头像 */
.txdd_box .left-info .avatar_show {
  margin-top: 10px;
  display: block !important;
  width: 80px;
  height: 80px;
  background: url(~@/assets/rhtx/img/avatar_show.png) no-repeat;
  background-size: 100% 100%;
}