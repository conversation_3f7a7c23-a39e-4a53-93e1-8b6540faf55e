html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
font,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
textarea,
input {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  user-select: none;
}

address,
cite,
dfn,
em,
var,
i {
  font-style: normal;
}

body {
  width: 1920px;
  height: 1080px;
  font-size: 16px;
  line-height: 1.5;
  font-family: 'Microsoft Yahei', 'simsun', 'arial', 'tahoma';
  color: #222;
  background: #eee;
  /* margin-top: 360px; */
}
body .el-popover {
  background: rgb(153, 153, 153, 0.6);
  border: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

h1,
h2,
h3,
h4,
h5,
h6,
th {
  font-size: 100%;
  font-weight: normal;
}

button,
input,
select,
textarea {
  font-size: 100%;
}

fieldset,
img {
  border: 0;
}

a {
  text-decoration: none;
  color: #666;
  background: none;
}

ul,
ol {
  list-style: none;
}

:focus {
  outline: none;
}

.clearfix {
  clear: both;
  content: '';
  display: block;
  overflow: hidden;
}

.clear {
  clear: both;
}

.fl {
  float: left;
}

.fr {
  float: right;
}
