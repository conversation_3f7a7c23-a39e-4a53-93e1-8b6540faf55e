<template>
  <div v-if="show">
    <div class="ai_waring">
      <div class="title">
        <span>天气预警</span>
      </div>
      <div class="close_btn" @click="closeEmitai"></div>
      <div class="content">
        <div class="check_types">
          <div class="jjcd">
            <span>预警时间：</span>
            <el-date-picker
              v-model="gdObj.releaseTime"
              style="border-color:#008aff;color: #008aff;"
              popper-class="elDatePicker"
              type="date"
              placeholder="选择预警时间">
            </el-date-picker>
          </div>
          <div class="jjcd">
            <span>预警类型：</span>
            <!-- <Select v-model="gdObj.type">
              <Option
                v-for="item in qgdCodesqlxOptions"
                :value="item.itemValue"
                :key="item.itemValue"
              >
                {{
                item.itemValue
                }}
              </Option>
            </Select> -->
            <el-select v-model="gdObj.type" placeholder="请选择">
              <el-option v-for="item in qgdCodesqlxOptions" :key="item.itemValue" :label="item.itemValue"
                :value="item.itemValue"></el-option>
            </el-select>
          </div>
          <div class="type_btns">
            <div @click="searchBtn">查询</div>
            <div @click="resetBtn">重置</div>
          </div>
        </div>
        <div class="table_box">
          <SwiperTableMap
            :titles="[
              '序号',
              '预警时间',
              '预警类型',
              '预警区域',
              '预警等级',
              '预警信息',
              '操作'
            ]"
            :widths="['6%', '14%', '13%', '13%', '14%', '30%', '10%',]"
            :data="tableList"
            :contentHeight="'510px'"
            :settled="settled"
            @operate="operate"
          ></SwiperTableMap>
        </div>
        <div class="fy_page">
          <Page :total="total" @on-change="pageNumChange" show-total></Page>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import SwiperTableMap from './table/RealTimeEventTable6.vue'
// api
import { getEventType, getUnDoneEventList, getDoneEventList, getQgdSssj } from '@/api/hs/hs.api.js'
import { myMixins } from '@/libs/myMixins.js'
import {
  getAlarm,
  getCscpBasicHxItemCode,
} from '@/api/bjnj/zhdd.js'

export default {
  name: 'RealTimeEventDialogQgd',
  mixins: [myMixins],
  components: { SwiperTableMap },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default: () => {
        return []
      }
    },
    areaId: {
      type: String,
      default: ''
    },
    dtDay: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      qgdCodesqlxOptions: [],
      tabActive: 0,
      tabs: ['未办结', '办结'],
      btnsArr: [
        {
          name: '未办结',
          normalBg: require('@/assets/shzl/map/left_n.png'),
          activeBg: require('@/assets/shzl/map/left_a.png')
        },
        {
          name: '已办结',
          normalBg: require('@/assets/shzl/map/right_n.png'),
          activeBg: require('@/assets/shzl/map/right_a.png')
        }
      ],
      urgentOptions: [
        {
          value: '0',
          label: '普通'
        },
        {
          value: '1',
          label: '突发'
        },
        {
          value: '2',
          label: '重要'
        },
        {
          value: '3',
          label: '紧急'
        }
      ],
      eventTypeOptions: [
        {
          value: '0',
          label: '普通'
        }
      ],
      timeOutOptions: [
        {
          value: '0',
          label: '是'
        },
        {
          value: '1',
          label: '否'
        }
      ],
      proityValue: '',
      timeOutValue: '',
      settled: true,
      total: 0,
      pageNum: 1,
      pageSize: 10,
      finished: 1,
      tableList: [],
      gdObj: {
        jjcdValue: '',
        sqlxValue: '',
        releaseTime: ''
      },
      regionCode:""
    }
  },
  watch: {
    qgdCodejjcdOptions(val) {
      if (this.qgdCodejjcdOptions.length > 0) {
        // this.getQgdSssj()
      }
    },
    // 防止出现获取不到annexNum
    // areaId: {
    //   immediate: true,
    //   handler(annexNum) {
    //     console.log('annexNum111', annexNum)
    //     this.getAlarm()
    //   }
    // },
    value: {
      immediate: true,
      handler(annexNum) {
        this.gdObj.releaseTime = this.dtDay
        this.getAlarm()
      }
    }
  },
  computed: {
    show() {
      return this.value
    }
  },
  mounted() {
    this.getCscpBasicHxItemCode1()
    // this.getAlarm()
  },
  methods: {
    async getCscpBasicHxItemCode1() {
      let res = await getCscpBasicHxItemCode('weatherAlarmType')
      console.log(res)
      if (res?.code == '0') {
        this.qgdCodesqlxOptions = res.data
      }
    },
    async getAlarm() {
      console.log(this.dtDay)
      console.log(this.gdObj.releaseTime)
      this.tableList = []
      let res = await getAlarm({
        regionCode:this.areaId,
        pageNo: this.pageNum,
        pageSize: this.pageSize,
        type: this.gdObj.type,
        releaseTime: this.gdObj.releaseTime ? dayjs(this.gdObj.releaseTime).format('YYYY-MM-DD') : '',
      })
      console.log('getAlarm', res)
      if (res?.code == '0') {
        this.tableList = res.data.alarm.map((it, i) => [
          this.pageSize * (this.pageNum - 1) + i + 1,
          it.time,
          it.type,
          it.area,
          it.level,
          it.detail,
          it.title,
        ])
        this.total = res.data.total
      }
    },
    operate(i, it) {
      this.$emit('operate', i, it)
    },
    closeEmitai() {
      this.gdObj = {
        releaseTime: this.dtDay,
        type: '',
      }
      this.pageNum = 1
      this.pageSize = 10
      this.$emit('input', false)
    },
    pageNumChange(val) {
      this.pageNum = val
      this.getAlarm()
    },
    async getQgdSssj() {
      this.tableList = []
      let res = await getQgdSssj({
        // street: '1649962979288023040',
        page: this.pageNum,
        size: this.pageSize,
        emeLevel: this.gdObj.jjcdValue,
        appealType: this.gdObj.sqlxValue
      })
      console.log('实时事件弹窗', res)
      console.log(this.qgdCodejjcdOptions)
      if (res?.code == '200' && res.result.data.length > 0) {
        console.log('res.result.data', res.result.data)
        this.tableList = res.result.data.map((it, i) => [
          this.pageSize * (this.pageNum - 1) + i + 1,
          it.orderNum,
          it.emeLevel,
          it.appealContent,
          it.appealType,
          it.community,
          it.eventDate,
          it.formStatus,
          it.eventLocation,
          it.id,
          it
        ])
        this.total = res.result.recordsTotal
      }
      // console.log('this.tableList', this.tableList)
    },
    searchBtn() {
      this.getAlarm()
    },
    resetBtn() {
      this.gdObj = {
        releaseTime: this.dtDay,
        type: '',
      }
      this.getAlarm()
    }
  },
}
</script>
<style lang="less">
.elDatePicker.el-picker-panel {
  color: #fff; //设置当前面板的月份的字体为白色，记为1
  background: #002450; //定义整体面板的颜色
  border: 1px solid #1384b4; //定义整体面板的轮廓
  .el-input__inner {
    background: #002450;
    color: #fff;
  }
  .el-picker-panel__icon-btn {
    //设置年份月份调节按钮颜色，记为2
    color: #ffffff;
  }
  .el-date-picker__header-label {
    //设置年月显示颜色，记为3
    color: #ffffff;
  }
  .el-date-table th {
    //设置星期颜色，记为4
    color: #ffffff;
  }
  .el-picker-panel__footer {
    background: #002450;
  }
}
</style>
<style lang="less" scoped>
.bg-header {
  position: absolute;
  width: 100%;
  height: 1080px;
  background: rgba(2, 14, 50, 0.75);
  z-index: 999999;
  top: 0;
  left: 0;
}
/deep/ .ivu-select {
  width: 189px;
}
/deep/ .ivu-select-selection {
  width: 189px;
  height: 34px;
  background: rgba(0, 74, 143, 0.4)
    linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
}
/deep/ .ivu-select-selection .ivu-select-selected-value {
  font-size: 13px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 34px;
}

/deep/ .ivu-input {
  background: rgba(0, 74, 143, 0.4)
    linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 22px;
}
/deep/ .ivu-select-placeholder {
  height: 34px !important;
  font-size: 13px !important;
  line-height: 34px !important;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff !important;
}
/deep/ .ivu-page-item {
  background: transparent;
  color: #fff;
}
/deep/ .ivu-page-item a {
  color: #fff;
}
/deep/ .ivu-page-prev,
/deep/ .ivu-page-next {
  background: transparent;
}
/deep/ .el-input__inner {
  color: #CCF4FF;
}
.ai_waring {
  width: 1435px;
  height: 780px;
  padding-bottom: 20px;
  background: rgba(9, 19, 34, 0.95);
  box-shadow: 0px 0px 22px 0px rgba(11, 54, 138, 0.35), 0px 2px 8px 0px rgba(0, 0, 0, 0.7),
    inset 0px 0px 8px 0px rgba(17, 146, 214, 0.35);
  border-radius: 11px;
  border: 1px solid #015c8c;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1100;
  .title {
    margin: 0 auto 23px;
    width: 635px;
    height: 76px;
    line-height: 76px;
    background: url(~@/assets/map/dialog/title2.png) no-repeat;
    display: grid;
    place-items: center;
    font-family: YouSheBiaoTiHei;
    span {
      display: inline-block;
      font-size: 36px;
      font-family: YouSheBiaoTiHei;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .close_btn {
    position: absolute;
    width: 23px;
    height: 23px;
    top: 25px;
    right: 40px;
    background: url(~@/assets/map/dialog/btn3.png) no-repeat;
    cursor: pointer;
  }
  .content {
    padding: 0 30px;
    .btns {
      // border: 1px solid red;
      margin: 0 auto;
      display: flex;
      justify-content: center;
      & > div {
        width: 154px;
        height: 53px;
        span {
          font-size: 24px;
          font-family: PingFangSC, PingFang SC;
          color: #ffffff;
          line-height: 53px;
          background: linear-gradient(180deg, #ffffff 0%, #ffeba0 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
    .check_types {
      text-align: left;
      display: flex;
      .jjcd {
        display: flex;
        align-items: center;
        margin-right: 24px;
        span {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
        }
        /deep/ .el-input {
          width: 189px;
          height: 34px;
          .el-input__inner {
            width: 189px;
            height: 34px;
            background: linear-gradient(180deg , rgba(181,223,248,0) 0%, rgba(29,172,255,0.29) 100%, #FFFFFF 100%), rgba(0,74,143,0.4);
            border: 1px solid rgba(0,162,255,0.6);
          }
        }
      }
      .report_time {
        margin-left: 26px;
        display: flex;
        align-items: center;
        span {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
      }
      .istime_out {
        margin-left: 26px;
        align-items: center;
        display: flex;
        span {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
      }
      .type_btns {
        margin-left: 27px;
        display: flex;
        align-items: center;
        & div {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 43px;
          background: url(~@/assets/shzl/map/type_btn.png) no-repeat center / 100% 100%;
          background-size: 100% 100%;
          text-align: center;
          cursor: pointer;
          &:first-of-type {
            width: 98px;
            height: 43px;
          }
          &:nth-of-type(2) {
            margin-left: 20px;
            width: 98px;
            height: 43px;
          }
          &:nth-of-type(3) {
            margin-left: 20px;
            width: 98px;
            height: 43px;
          }
        }
      }
    }
    .table_box {
      margin-top: 31px;
      overflow: hidden;
    }
    .fy_page {
      margin-top: 20px;
    }
  }
}
// /deep/ .ivu-page-total {
//   font-size: 18px;
//   color: #fff;
// }
</style>
