<template>
  <div>
    <div class="zwfw_pop">
      <div class="title">
        <div class="title_text">挖掘关联</div>
        <img class="close_btn" src="@/assets/leader/img/zwfw/close.png" @click="close" />
      </div>
      <div class="pop_content">
        <BlockBox
          title="低收入家庭资格认证"
          class="content_box"
          :isListBtns="false"
          :blockHeight="342"
        >
          <multi-barChart3D
            :data="zgrz.data"
            :options="zgrz.options"
            :init-option="{
              yAxis: {
                name: '',
              },
            }"
          />
          <div class="gajq_lz">
            <img class="" src="@/assets/shzl/zhuzhuangtu_lz.png" alt="" />
          </div>
        </BlockBox>
        <BlockBox title="简政放权" class="content_box" :isListBtns="false" :blockHeight="342">
          <div class="jzfq_content">
            <div v-for="(item, index) in csbnData" :key="index" class="jzfq_item">
              <div class="num_box">
                <div class="num">
                  <countTo
                    ref="countTo"
                    :startVal="$countTo.startVal"
                    :decimals="$countTo.decimals(item.num)"
                    :endVal="item.num"
                    :duration="$countTo.duration"
                  />
                </div>
                <img v-if="item.dm" src="@/assets/leader/img/zwfw/csbn7.png" />
                <div v-if="item.dm" class="num_dm">{{ item.dm }}</div>
              </div>
              <div class="num_title">{{ item.tit }}</div>
            </div>
          </div>
        </BlockBox>
        <BlockBox title="特种设备" class="content_box" :isListBtns="false" :blockHeight="342">
          <div class="tzsb_chart">
            <pieCharts />
          </div>
        </BlockBox>
        <BlockBox title="优化服务" class="content_box" :isListBtns="false" :blockHeight="342">
          <WordCloud :data="data1" :options="options1" />
        </BlockBox>
      </div>
    </div>
    <div>
      <transition name="fade">
      <div
        class="bg-header"
      ></div>
    </transition>
    </div>
  </div>
</template>

<script>
import BlockBox from '@/components/leader/common/blockBox.vue'
import SwiperTable from '@/components/leader/leader_zwjs/SwiperTable.vue'
import pieCharts from '@/components/zwfw/pieChart-wjgl.vue'
import 'swiper/css/swiper.css'
import echarts from 'echarts'

export default {
  name: 'ywnlPop',
  components: {
    BlockBox,
    SwiperTable,
    pieCharts
  },
  data() {
    return {
      qsfx: {
        data: [
          ['product', '受理数量', '办结数量'],
          ['7月', 150, 100],
          ['8月', 450, 310],
          ['9月', 390, 320],
          ['10月', 500, 350],
          ['11月', 419, 360],
          ['12月', 319, 260],
        ],
        options: {
          // 颜色数据
          color: ['#FAE699', '#00A3D7'],
          // 柱子的宽度
          barWidth: 20,
          // 柱子X轴位置
          symbolOffsetX: ['-25%', '25%', '25%'],
          // 顶部的大小
          symbolSize: [20, 11],
        },
      },
      data1: [
        ['name', 'value'],
        ['云上如意店小二', 5],
        ['好差评', 9],
        ['一网通办', 1],
        ['不见面审批', 8],
        ['随时办结', 3],
        ['开办企业半日通', 6],
        ['一次告知', 1],
        ['电子踏勘', 3],
        ['一件事', 6],
        ['一窗受理', 3],
        ['并联办理', 7],
        ['物业管理', 8],
        ['首问负责', 1],
        ['中介超市', 3],
        ['社会保险', 4],
      ],
      options1: {
        colors: [
          '#3785FE',
          '#49C384',
          '#F9E164',
          '#009FEC',
          '#8A71FF',
          '#F7B13F',
          '#37FEE7',
          '#F9E164',
          '#49C384',
        ],
        fontSizeMin: 12,
        fontSizeMax: 32,
        showTooltip: false,
        gridSize: 5,
      },
      zgrz: {
        data: [
          ['product', ''],
          ['2018', 50],
          ['2019', 70],
          ['2020', 90],
          ['2021', 95],
          ['2022', 88],
        ],
        options: {
          // 颜色数据
          color: ['#00A3D7'],
          // 柱子的宽度
          barWidth: 20,
          // 柱子X轴位置
          symbolOffsetX: ['', '25%', '25%'],
          // 顶部的大小
          symbolSize: [20, 11],
        },
      },
      ldtjData: [
        ['南京市', '30', '10', '20', '3'],
        ['市场监督管理局', '37', '14', '20', '8'],
        ['税务局', '31', '10', '26', '9'],
        ['城管局', '36', '14', '28', '6'],
        ['水利局', '38', '11', '22', '2'],
        ['统计局', '28', '16', '25', '7'],
      ],
      sxqhList: [
        ['NO.1', '食品经营', '500'],
        ['NO.2', '药品经营', '422'],
        ['NO.3', '养老保险', '390'],
        ['NO.4', '个体工商户登记', '320'],
        ['NO.5', '婚姻登记', '311'],
        ['NO.6', '大额存款', '300'],
        ['NO.7', '劳务纠纷', '287'],
        ['NO.8', '离婚登记', '267'],
        ['NO.9', '租房补贴', '259'],
        ['NO.10', '人才补贴', '240'],
      ],
      tzsbData: [
        { name: '特种设备作业人员考核', value: 16257 },
        { name: '特种设备作业人员复核', value: 2296 },
        { name: '特种设备检验监测人员\n资格认定，特种设备作\n业人员咨格认定', value: 1851 },
      ],
      csbnData: [
        {
          num: 123,
          dm: '11条',
          tit: '行政许可事项梳理',
        },
        {
          num: 123,
          dm: '11条',
          tit: '行政备案事项梳理',
        },
        {
          num: 1972,
          tit: '取消事项',
        },
        {
          num: 123132,
          tit: '下放事项',
        },
        {
          num: 2011,
          tit: '调整事项',
        },
        {
          num: 1972,
          tit: '承接事项',
        },
      ],
    }
  },
  mounted() {
    // this.tzsbChartMethod(this.$refs.tzsbChart)
  },
  methods: {
    close() {
      this.$emit('closeEmit')
    },
    tzsbChartMethod(id) {
      let seriesData = this.tzsbData
      let total = seriesData
        .map((item) => {
          return item.value
        })
        .reduce((sum, currentValue) => {
          return (sum += currentValue)
        }, 0)
      let option = {
        color: ['#2EF6FF', '#6D5AE2', '#079AE9'],
        title: [
          {
            top: 90,
            left: 54,
            text: '{val|' + total + '}',
            textStyle: {
              rich: {
                val: {
                  fontSize: 28,
                  fontWeight: 'bold',
                  color: '#fff',
                  padding: [10, 40],
                },
                name: {
                  fontSize: 14,
                  color: '#C2DDFC',
                  padding: [0, 60],
                },
              },
            },
          },
        ],
        grid: {
          top: '5%',
          left: 0,
          right: '1%',
          bottom: 5,
          containLabel: true,
        },
        legend: {
          show: true,
          itemWidth: 14,
          itemHeight: 10,
          itemGap: 18,
          right: 0,
          top: 60,
          orient: 'vertical',
          icon: 'circle',
          formatter: (name) => {
            if (seriesData.length) {
              return '{a|' + name + '}'
            }
          },
          textStyle: {
            rich: {
              //这里定义a的样式
              a: {
                width: 180,
                lineHeight: 20,
                fontSize: 18,
                color: '#ffffff',
              },
            },
          },
        },
        series: [
          {
            name: '需求类型占比',
            type: 'pie',
            center: ['30%', '50%'],
            radius: ['60%', '80%'],
            itemStyle: {
              normal: {
                borderWidth: 5,
              },
            },
            label: {
              normal: {
                show: false,
                position: 'center',
                formatter: '{value|{c}}\n{label|{b}}',
                rich: {
                  value: {
                    padding: 5,
                    align: 'center',
                    verticalAlign: 'middle',
                    fontSize: 32,
                  },
                  label: {
                    align: 'center',
                    verticalAlign: 'middle',
                    fontSize: 16,
                  },
                },
              },
              emphasis: {
                show: false,
                textStyle: {
                  fontSize: '12',
                },
              },
            },
            labelLine: {
              show: false,
              length: 0,
              length2: 0,
            },
            data: seriesData,
          },
          {
            name: '',
            type: 'gauge',
            center: ['30%', '50%'],
            radius: '54%',
            // splitNumber: 10, // 刻度数量
            // min: 0, // 最小刻度
            // max: dataX, // 最大刻度
            // 仪表盘轴线相关配置
            axisLine: {
              lineStyle: {
                color: [
                  [
                    1,
                    {
                      type: 'radial',
                      x: 0.5,
                      y: 0.59,
                      r: 0.6,
                      colorStops: [
                        {
                          offset: 0.72,
                          color: '#032046',
                        },
                        {
                          offset: 0.94,
                          color: '#086989',
                        },
                        {
                          offset: 0.98,
                          color: '#0FAFCB',
                        },
                        {
                          offset: 1,
                          color: '#0EA4C1',
                        },
                      ],
                    },
                  ],
                ],
                width: 1,
              },
            },
            // 分隔线
            splitLine: {
              show: false,
            },
            // 刻度线
            axisTick: {
              show: false,
            },
            // 刻度标签
            axisLabel: {
              show: false,
            },

            detail: {
              show: false,
            },
          },
        ],
      }
      let myChart = echarts.init(id, 'ucdc')
      myChart.setOption(option)
      window.addEventListener('resize', function () {
        myChart.resize()
      })
    },
  },
}
</script>
<style lang="less" scoped>
 .bg-header {
    position: absolute;
    width: 100%;
    height: 1080px;
    background: rgba(2, 14, 50, 0.75);
    z-index: 999999;
    top:0;
    left:0;
  }
.gajq_lz {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  img {
    width: 100%;
    height: 100%;
  }
}
.zwfw_pop {
  width: 1251px;
  height: 839px;
  position: absolute;
  z-index: 99999999;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border-radius: 11px;
  border: 1px solid;
  background: #001638;
  border-image: linear-gradient(360deg, rgba(57, 133, 255, 0.5), rgba(0, 205, 255, 0)) 1 1;
  .title {
    margin: 0 auto;
    width: 634px;
    height: 74px;
    background: url(~@/assets/csts/title1.png) no-repeat;
    text-align: center;
    margin-bottom: 37px;
    .title_text {
      display: inline-block;
      line-height: 74px;
      font-size: 40px;
      font-family: YouSheBiaoTiHei;
      color: #ffffff;
      background: linear-gradient(180deg, #ffffff 0%, #2aebff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .close_btn {
      position: absolute;
      top: 10px;
      right: 30px;
      width: 44px;
      height: 44px;
      cursor: pointer;
    }
  }
  .pop_content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    .content_box {
      width: 533px;
      .tzsb_chart {
        width: 450px;
        height: 200px;
      }
      .table_content {
        margin-top: 24px;
        margin-left: 5px;
        overflow: hidden;
        width: 522px;
        .item_cont1 {
          flex: 1;
          ::v-deep .row-item {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #37c1ff;
            line-height: 20px;
          }
          ::v-deep .swiper-item {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
          }
        }
      }
      .tzsb_chart {
        width: 450px;
        height: 250px;
      }
      .jzfq_content {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        width: 424px;
        margin-left: 55px;
        .jzfq_item {
          display: flex;
          flex-direction: column;
          // justify-content: center;
          align-items: center;
          width: 132px;
          height: 107px;
          padding-top: 42px;
          margin-top: 24px;
          .num_box {
            display: flex;
            align-items: flex-end;
            img {
              width: 8px;
              height: 9px;
            }
            .num {
              font-size: 26px;
              font-family: DINAlternate-Bold, DINAlternate;
              font-weight: bold;
              color: #ffffff;
              line-height: 30px;
            }
            .num_dm {
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
            }
          }
          .num_title {
            margin-top: 3px;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 20px;
          }
        }
        .jzfq_item:first-of-type {
          background: url(~@/assets/leader/img/zwfw/csbn1.png) no-repeat center / 100% 100%;
        }
        .jzfq_item:nth-of-type(2) {
          background: url(~@/assets/leader/img/zwfw/csbn2.png) no-repeat center / 100% 100%;
        }
        .jzfq_item:nth-of-type(3) {
          background: url(~@/assets/leader/img/zwfw/csbn3.png) no-repeat center / 100% 100%;
        }
        .jzfq_item:nth-of-type(4) {
          background: url(~@/assets/leader/img/zwfw/csbn4.png) no-repeat center / 100% 100%;
        }
        .jzfq_item:nth-of-type(5) {
          background: url(~@/assets/leader/img/zwfw/csbn5.png) no-repeat center / 100% 100%;
        }
        .jzfq_item:nth-of-type(6) {
          background: url(~@/assets/leader/img/zwfw/csbn6.png) no-repeat center / 100% 100%;
        }
      }
    }
  }
}
</style>