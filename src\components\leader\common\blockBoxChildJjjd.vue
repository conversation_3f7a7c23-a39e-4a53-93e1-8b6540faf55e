<template>
	<div class="child-block">
		<slot></slot>

		<div v-if="!loading && data.length > 0" class="content-box">
			<div class="content-item-box" v-for="(item, index) in data" :key="index" @click="handleItemClick">
				<div class="line1-box">
					<img :src="item.iconUrl" alt="" />

					<!-- <div class="line1-icon-box"></div> -->
					<div class="line1-label">{{ item.label }}</div>
				</div>
				<div class="line2-box">{{ item.content }}</div>
			</div>
		</div>

		<div v-else-if="loading" class="alert-empty content-box">加载中...</div>
		<div v-else-if="data.length == 0" class="alert-empty content-box">暂无数据</div>
	</div>
</template>

<script>
export default {
	props: {
		data: {
			type: Array,
			default: () => {},
		},
		loading: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {}
	},
	computed: {},
	methods: {
		handleItemClick() {
			this.$emit('handleItemClick')
		},
	},

	watch: {},
}
</script>

<style lang="less" scoped>
.child-block {
	width: 451px;
	height: 100%;
	box-sizing: border-box;
	z-index: 101;
	position: relative;
	overflow: auto;

	&::-webkit-scrollbar {
		width: 4px; /* 滚动条宽度 */
	}

	//轨道
	&::-webkit-scrollbar-track {
		background: #04244e; /* 滚动条轨道背景 */
		border-radius: 10px;
		padding: 5px 0;
	}

	//滑块
	&::-webkit-scrollbar-thumb {
		background: #82aed0; /* 滑块背景颜色 */
		border-radius: 10px;
		height: 15px;
	}

	//悬停滑块颜色
	&::-webkit-scrollbar-thumb:hover {
		background: #ff823b; /* 鼠标悬停时滑块颜色 */
	}

	//箭头
	&::-webkit-scrollbar-button {
		display: none; /* 隐藏滚动条的箭头按钮 */
	}

	.content-box {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
		padding: 0 15px;
		padding-left: 22px;

		.content-item-box {
			width: 100%;
			padding: 16px;
			border-bottom: 1px solid #222b38;
			cursor: pointer;

			.line1-box {
				width: 100%;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				margin-bottom: 8px;

				img {
					width: 20px;
					height: 20px;
					margin-right: 10px;
				}

				.line1-icon-box {
					width: 20px;
					height: 20px;
					background: skyblue;
					margin-right: 5px;
				}
				.line1-label {
					font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
					font-size: 16px;
					color: #159aff;
					font-weight: bold;
					text-align: left;
					font-style: normal;
					text-transform: none;
					white-space: nowrap;
					overflow: hidden;
					text-align: left;
					text-overflow: ellipsis;
				}
			}

			.line2-box {
				width: 100%;

				font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
				font-weight: normal;
				font-size: 14px;
				color: #d0deee;
				text-align: left;
				font-style: normal;
				text-transform: none;
				white-space: nowrap;
				overflow: hidden;
				text-align: left;
				text-overflow: ellipsis;
			}
		}

		.content-item-box:last-child {
			border-bottom: none;
		}
	}

	.alert-empty {
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		font-family: PangMenZhengDao-3, PangMenZhengDao-3;
		font-weight: 500;
		font-size: 20px;
		color: #dbf1ff;
		text-shadow: 0px 0px 10px #bddcf1;
		text-align: center;
		font-style: normal;
		text-transform: none;
	}
}

.popDropDown {
	::v-deep .el-dropdown {
		color: #caecff;
		font-family: YouSheBiaoTiHei;
		font-size: 16px;
		line-height: 21px;
		text-align: right;
		font-style: normal;
		line-height: 40px;
		right: 0;
		position: absolute;
	}
	::v-deep .el-dropdown-menu {
		background-color: #00173b !important;
		border: 1px solid #2b7bbb !important;
	}
	.el-dropdown-menu__item {
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
		line-height: 40px;
		padding: 0 30px;
		&:hover {
			background: #185493;
			color: rgba(255, 255, 255, 0.8);
		}
	}
}
</style>
