<template>
	<div class="time-range-player">
		<!-- 控制按钮 -->
		<div class="player-controls">
			<div @click="togglePlay" class="operate-button-box">
				<i v-if="isPlaying" class="el-icon-video-pause icon-box"></i>
				<i v-else class="el-icon-video-play icon-box"></i>
			</div>
			<div class="time-display">
				{{ formattedCurrentTime }}
			</div>
		</div>

		<!-- 时间刻度显示 -->
		<div class="track-box">
			<div class="progress-container" ref="progressContainer">
				<div class="progress-bar" :style="{ width: progress + '%' }"></div>
				<div class="progress-handle" :style="{ left: progress + '%' }" @mousedown="startDrag"></div>
			</div>
			<div class="time-marks">
				<div v-for="(mark, index) in timeMarks" :key="index" class="time-mark" :style="{ left: mark.position + '%' }">
					<!-- <div v-for="(mark, index) in timeMarks" :key="index" class="time-mark"> -->
					<div class="mark-line"></div>
					<span class="mark-label">{{ mark.label }}</span>
				</div>
			</div>

			<!-- 进度条 -->
		</div>

		<!-- 时间范围选择 -->
		<div class="range-selector">
			<!-- <button
				v-for="option in timeRangeOptions"
				:key="option.value"
				:class="{ active: selectedRange === option.value }"
				
			>
				{{ option.label }}
			</button> -->
			<el-select v-model="selectedRange" placeholder="请选择预测天数" @change="changeRange">
				<el-option v-for="item in timeRangeOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
			</el-select>
		</div>
	</div>
</template>

<script>
export default {
	name: 'TimeRangePlayer',
	data() {
		return {
			selectedRange: '24h',
			timeRangeOptions: [
				{ value: '24h', label: '24小时' },
				{ value: '3d', label: '3天' },
				{ value: '7d', label: '7天' },
			],
			isPlaying: false,
			currentTime: 0,
			animationFrameId: null,
			lastTimestamp: 0,
			isDragging: false,
			playInterval: null,
			predictDateType: '24h',
		}
	},
	computed: {
		// 计算总时间单位数
		totalUnits() {
			switch (this.selectedRange) {
				case '24h':
					return 24 // 24小时
				case '3d':
					return 3 // 3天
				case '7d':
					return 7 // 7天
				default:
					return 24
			}
		},
		// 计算进度百分比
		progress() {
			if (this.selectedRange === '24h') {
				return (this.currentTime / this.totalUnits) * 100
			} else {
				return (this.currentTime / (this.totalUnits - 1)) * 100
			}
		},
		// 格式化当前时间显示
		formattedCurrentTime() {
			switch (this.selectedRange) {
				case '24h':
					return `${this.currentTime}:00`
				case '3d':
					return `第${this.currentTime + 1}天`
				case '7d':
					return `第${this.currentTime + 1}天`
				default:
					return ''
			}
		},
		// 生成时间刻度
		timeMarks() {
			const marks = []
			const total = this.totalUnits
			if (this.selectedRange === '24h') {
				for (let i = 0; i <= total; i++) {
					marks.push({
						position: (i / total) * 100,
						label: this.selectedRange === '24h' ? `${i}:00` : `第${i + 1}天`,
					})
				}
			} else {
				for (let i = 0; i <= total - 1; i++) {
					marks.push({
						position: (i / (total - 1)) * 100,
						label: this.selectedRange === '24h' ? `${i}:00` : `第${i + 1}天`,
					})
				}
			}

			return marks
		},
		// 获取播放间隔时间（毫秒）
		playIntervalTime() {
			// 1秒更新一次进度
			if (this.selectedRange != '24h') {
				return 1500
			} else {
				return 1500
			}
		},
		// 获取每次前进的单位数
		playStep() {
			// 24小时模式下每小时前进1单位，天模式下每天前进1单位

			return 1
		},
	},
	methods: {
		// 切换时间范围
		changeRange(range) {
			this.currentTime = 0
			// this.selectedRange = range
			this.$emit('time-update', this.currentTime, this.selectedRange)

			this.resetPlayback()
		},
		// 切换播放/暂停
		togglePlay() {
			if (this.isPlaying) {
				this.pause()
			} else {
				this.play()
			}
		},
		// 开始播放
		play() {
			this.isPlaying = true
			this.lastTimestamp = Date.now()

			// 使用setInterval而不是requestAnimationFrame，因为我们想要固定的时间间隔
			this.playInterval = setInterval(() => {
				this.currentTime += this.playStep

				// 检查是否到达终点
				if (this.currentTime >= this.totalUnits) {
					this.currentTime = this.totalUnits
					this.pause()
					this.$emit('end')
					this.currentTime = 0
					this.togglePlay()
				}

				this.$emit('time-update', this.currentTime, this.selectedRange)
			}, this.playIntervalTime)
		},
		// 暂停播放
		pause() {
			this.isPlaying = false
			if (this.playInterval) {
				clearInterval(this.playInterval)
				this.playInterval = null
			}
		},
		// 重置播放
		resetPlayback() {
			this.pause()
			this.currentTime = 0
		},
		// 开始拖动进度条
		startDrag(e) {
			this.isDragging = true
			this.pause()
			document.addEventListener('mousemove', this.handleDrag)
			document.addEventListener('mouseup', this.stopDrag)
			this.updateProgress(e)
		},
		// 处理拖动
		handleDrag(e) {
			if (this.isDragging) {
				this.updateProgress(e)
			}
		},
		// 停止拖动
		stopDrag() {
			this.isDragging = false

			document.removeEventListener('mousemove', this.handleDrag)
			document.removeEventListener('mouseup', this.stopDrag)
		},
		// 更新进度
		updateProgress(e) {
			const container = this.$refs.progressContainer
			const rect = container.getBoundingClientRect()
			let pos = (e.clientX - rect.left) / rect.width

			pos = Math.max(0, Math.min(1, pos)) // 限制在0-1之间

			this.currentTime = Math.floor(pos * this.totalUnits)

			this.$emit('time-update', this.currentTime, this.selectedRange)
		},
	},
	beforeDestroy() {
		this.pause()
		document.removeEventListener('mousemove', this.handleDrag)
		document.removeEventListener('mouseup', this.stopDrag)
	},
}
</script>

<style lang="less" scoped>
.time-range-player {
	width: 100%;
	margin: 0 auto;
	font-family: Arial, sans-serif;
	display: flex;
	justify-content: flex-start;
	align-items: center;
}

.range-selector {
	margin-bottom: 15px;

	/deep/ .el-select {
		width: 90px !important;
		.el-input {
			width: 90px !important;
			height: 28px;

			.el-input__inner {
				width: 100% !important;
			}
		}
	}
}

.range-selector button {
	padding: 5px 15px;
	margin-right: 10px;
	background: #f0f0f0;
	border: 1px solid #ddd;
	border-radius: 4px;
	cursor: pointer;
	transition: all 0.3s;
}

.range-selector button.active {
	background: #42b983;
	color: white;
	border-color: #42b983;
}

.time-marks {
	width: 890px;
	position: relative;
	height: 40px;
	display: flex;
	justify-content: flex-start;
	align-items: center;
}

.time-mark {
	position: absolute;
	bottom: 0;
	transform: translateX(-50%);
	text-align: center;
	margin: 0 2px;
}

.mark-label {
	display: block;
	margin-bottom: 5px;
	white-space: nowrap;

	font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
	font-weight: 400;
	font-size: 14px;
	color: #fff;
	// color: #000;

	text-align: left;
	font-style: normal;
	text-transform: none;
}

.mark-line {
	height: 10px;
	width: 1px;
	background: #fff;
	// background: #000;
	margin: 0 auto;
}

.track-box {
	width: 100%;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: flex-start;
	padding-left: 20px;
}

.progress-container {
	width: 894px;
	height: 8px;
	background: #eee;
	border-radius: 4px;
	position: relative;
	cursor: pointer;
}

.progress-bar {
	height: 100%;
	background: #42b983;
	border-radius: 4px;
	width: 0;
	transition: width 0.3s;
}

.progress-handle {
	width: 16px;
	height: 16px;
	background: #42b983;
	border-radius: 50%;
	position: absolute;
	top: 50%;
	transform: translate(-50%, -50%);
	cursor: grab;
	transition: left 0.3s;
}

.player-controls {
	display: flex;
	justify-content: flex-start;
	align-items: center;

	.operate-button-box {
		// background-color: green;
		border-radius: 5px;
		white-space: nowrap;
		font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
		font-weight: 400;
		font-size: 14px;
		color: #d0deee;
		text-align: center;
		font-style: normal;
		text-transform: none;
		margin-right: 10px;
		cursor: pointer;

		.icon-box {
			font-size: 28px;
			color: #fff;
			// color: #0B99F0;

			// background-color: #298BF4;
		}
	}
}

.player-controls button {
	padding: 8px 20px;
	background: #42b983;
	color: white;
	border: none;
	border-radius: 4px;
	cursor: pointer;
	font-size: 14px;
	transition: background 0.3s;
}

.player-controls button:hover {
	background: #3aa876;
}

.time-display {
	font-size: 16px;
	font-weight: bold;
	text-align: center;
	color: #fff;
	// color: #000;
white-space: nowrap;
}
</style>
