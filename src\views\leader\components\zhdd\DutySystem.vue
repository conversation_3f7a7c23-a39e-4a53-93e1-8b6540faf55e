<template>
  <div class="wrap">
    <ul class="organize">
      <li v-for="(it, i) of list1" :key="i">
        <div class="icon" :style="{ backgroundImage: `url(${it.icon})` }"></div>
        <div class="info">
          <div class="label">{{ it.label }}</div>
          <div class="name">{{ it.name }}</div>
        </div>
      </li>
    </ul>
    <ul class="infos">
      <li v-for="(it, i) of list2" :key="i">
        <div class="icon" :style="{ background: `url(${it.icon}) no-repeat` }"></div>
        <div class="count">
          <div class="num">
            <countTo
              ref="countTo"
              :startVal="$countTo.startVal"
              :decimals="$countTo.decimals(it.count)"
              :endVal="it.count"
              :duration="$countTo.duration"
            /><span class="unit">{{ it.unit }}</span>
          </div>

          <div class="line"></div>
          <div class="label">{{ it.label }}</div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'DutySystem',
  data() {
    return {
      list1: [
        {
          label: '指挥长',
          name: '冯书记',
          icon: require('@/assets/zhdd/icon28.png')
        },
        {
          label: '副指挥长',
          name: '施主任',
          icon: require('@/assets/zhdd/icon29.png')
        },
        {
          label: '值班长',
          name: '雷主任',
          icon: require('@/assets/zhdd/icon30.png')
        },
        {
          label: '值班长',
          name: '蓝主任',
          icon: require('@/assets/zhdd/icon31.png')
        }
      ],
      list2: [
        {
          icon: require('@/assets/zhdd/icon32.png'),
          label: '总人数',
          count: 20,
          unit: ''
        },
        {
          icon: require('@/assets/zhdd/icon33.png'),
          label: '在岗数',
          count: 16,
          unit: ''
        },
        {
          icon: require('@/assets/zhdd/icon34.png'),
          label: '登录率',
          count: 80,
          unit: '%'
        }
      ]
    }
  }
}
</script>

<style lang="less" scoped>
.wrap {
  width: 100%;
  height: 100%;
  padding: 13px 19px 0 17px;
  .organize {
    width: 100%;
    height: 115px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-content: space-between;
    margin-bottom: 23px;
    li {
      width: 205px;
      height: 49px;
      display: flex;
      align-items: center;
      .icon {
        position: relative;
        width: 50px;
        height: 49px;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        z-index: 2;
      }
      .info {
        position: relative;
        width: 185px;
        height: 49px;
        margin-left: -30px;
        background-image: url('~@/assets/zhdd/bg9.png');
        display: flex;
        justify-content: space-between;
        align-items: center;
        z-index: 1;
        padding-left: 41px;
        padding-right: 19px;
        .label {
          height: 20px;
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #8bc7ff;
          line-height: 20px;
        }
        .name {
          height: 20px;
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 20px;
        }
      }
    }
  }
  .infos {
    display: flex;
    justify-content: space-between;
    li {
      // width: 118px;
      display: flex;
      align-items: center;
      .icon {
        width: 63px;
        height: 57px;
        margin-right: 3px;
      }
      .count {
        display: flex;
        flex-direction: column;
        text-align: left;
        .label {
          height: 20px;
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 20px;
        }
        .line {
          width: 70px;
          height: 4px;
          background-image: url('~@/assets/zhdd/line1.png');
        }
        span {
          height: 24px;
          font-size: 20px;
          font-family: DINAlternate-Bold, DINAlternate;
          font-weight: bold;
          line-height: 24px;
          background: linear-gradient(180deg, #ffffff 0%, #00fffa 67%, #04a1ff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          margin-bottom: 3px;
        }
      }
      &:nth-child(2) {
        .count {
          span {
            background: linear-gradient(180deg, #ffffff 0%, #86ff00 67%, #1cff04 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
      }
      &:nth-child(3) {
        .count {
          span {
            background: linear-gradient(180deg, #ffffff 0%, #ffd500 67%, #ffb904 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
      }
    }
  }
}
</style>
