import http from '@/utils/special/request'
import { finaUrl } from '@/utils/special/const'



//总体概览
export const getOverView = () => {
  return http.get(finaUrl + '/api/disputes/overview')
}

//  矛盾纠纷地域分布-近一周
export const getWeekDistributionByArea = () => {
  return http.get(finaUrl + '/api/disputes/weekDistributionByArea')
}
//  矛盾纠纷地域分布-近一月
export const getMonthDistributionByArea = () => {
  return http.get(finaUrl + '/api/disputes/monthDistributionByArea')
}
//  矛盾纠纷地域分布-近一年
export const getYearDistributionByArea = () => {
    return http.get(finaUrl + '/api/disputes/yearDistributionByArea')
}


//矛盾纠纷类型分布-近一周
export const getWeekDistributionByType = () => {
  return http.get(finaUrl + '/api/disputes/weekDistributionByType')
}
//矛盾纠纷类型分布-近一月
export const getMonthDistributionByType = () => {
  return http.get(finaUrl + '/api/disputes/monthDistributionByType')
}
//矛盾纠纷类型分布-近一年
export const getYearDistributionByType = () => {
  return http.get(finaUrl + '/api/disputes/yearDistributionByType')
}

//矛盾纠纷办结时长排名-近一月
export const getMonthRankOfCompletionTime = () => {
  return http.get(finaUrl + '/api/disputes/monthRankOfCompletionTime')
}
//矛盾纠纷办结时长排名-近一年
export const getYearRankOfCompletionTime = () => {
  return http.get(finaUrl + '/api/disputes/yearRankOfCompletionTime')
}


//矛盾纠纷办结率排名-近一月
export const getMonthRankOfCompletionRate = () => {
  return http.get(finaUrl + '/api/disputes/monthRankOfCompletionRate')
}
//矛盾纠纷办结率排名-近一年
export const getYearRankOfCompletionRate = () => {
  return http.get(finaUrl + '/api/disputes/yearRankOfCompletionRate')
}

//  矛盾纠纷概览-近一月
export const getMonthDisputesGeneralView = () => {
  return http.get(finaUrl + '/api/disputes/monthDisputesGeneralView')
}
//  矛盾纠纷概览-近一年
export const getYearDisputesGeneralView = () => {
  return http.get(finaUrl + '/api/disputes/yearDisputesGeneralView')
}
// 矛盾纠纷详细列表
export const getOrderList = () => {
  return http.get(finaUrl + '/api/disputes/orderList?type=2')
}