<template>
  <aside>
    <ul>
      <li
        v-for="(it, i) of list"
        @click="marker(it, i)"
        :style="{ backgroundImage: it.active ? `url(${it.bgActive})` : `url(${it.bg})` }"
      >
        <div
          class="icon"
          :style="{ backgroundImage: it.active ? `url(${it.iconActive})` : `url(${it.icon})` }"
        ></div>
        <div class="label">{{ it.label }}</div>
      </li>
    </ul>
    <!-- <SurroundingResources v-model="showYjzyList" left="175px" bottom="43px" /> -->
  </aside>
</template>

<script>
// import { SurroundingResources } from '../map'
export default {
  name: 'Aside',
  // components: { SurroundingResources },
  props: {
    list: {
      type: Array,
      default: () => {
        return [
          {
            bg: require('@/assets/aside/bg1.png'),
            bgActive: require('@/assets/aside/bg2.png'),
            icon: require('@/assets/aside/icon1.png'),
            iconActive: require('@/assets/aside/icon6.png'),
            label: '事件信息',
            active: false
          },
          {
            bg: require('@/assets/aside/bg1.png'),
            bgActive: require('@/assets/aside/bg2.png'),
            icon: require('@/assets/aside/icon2.png'),
            iconActive: require('@/assets/aside/icon7.png'),
            label: '周边资源',
            active: false
          },
          {
            bg: require('@/assets/aside/bg1.png'),
            bgActive: require('@/assets/aside/bg2.png'),
            icon: require('@/assets/aside/icon3.png'),
            iconActive: require('@/assets/aside/icon8.png'),
            label: '视频会商',
            active: false
          },
          {
            bg: require('@/assets/aside/bg1.png'),
            bgActive: require('@/assets/aside/bg2.png'),
            icon: require('@/assets/aside/icon4.png'),
            iconActive: require('@/assets/aside/icon9.png'),
            label: '任务下发',
            active: false
          },
          {
            bg: require('@/assets/aside/bg1.png'),
            bgActive: require('@/assets/aside/bg2.png'),
            icon: require('@/assets/aside/icon5.png'),
            iconActive: require('@/assets/aside/icon10.png'),
            label: '视频监控',
            active: false
          }
        ]
      }
    }
  },
  data() {
    return {
      showYjzyList: false
    }
  },
  methods: {
    marker(it, i) {
      it.active = !it.active
      if (i === 1) {
        this.showYjzyList = !this.showYjzyList
      }
      this.$emit('marker', it.active, i)
    }
  }
}
</script>

<style lang="less" scoped>
aside {
  position: absolute;
  left: 564px;
  top: 145px;
  z-index: 1000;
  ul {
    display: flex;
    flex-direction: column;
    gap: 20px;
    li {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-top: 7px;
      width: 64px;
      height: 67px;
      background-repeat: no-repeat;
      cursor: pointer;
      .icon {
        position: relative;
        width: 34px;
        height: 34px;
        background-repeat: no-repeat;
      }
      .label {
        font-size: 12px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #ffffff;
      }
      &:hover {
        .icon {
          animation: move infinite 3s ease-in-out;
          @keyframes move {
            0% {
              transform: rotateY(0);
            }
            50% {
              transform: rotateY(180deg);
            }
            100% {
              transform: rotateY(360deg);
            }
          }
        }
      }
    }
  }
}
</style>
