function e(e,n,r){return n=h(n),function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return m(e)}(e,t()?Reflect.construct(n,r||[],h(e).constructor):n.apply(e,r))}function t(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(<PERSON>olean,[],(function(){})))}catch(e){}return(t=function(){return!!e})()}function n(){n=function(){return t};var e,t={},r=Object.prototype,i=r.hasOwnProperty,a=Object.defineProperty||function(e,t,n){e[t]=n.value},o="function"==typeof Symbol?Symbol:{},s=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function d(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(e){d=function(e,t,n){return e[t]=n}}function l(e,t,n,r){var i=t&&t.prototype instanceof g?t:g,o=Object.create(i.prototype),s=new N(r||[]);return a(o,"_invoke",{value:E(e,n,s)}),o}function h(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=l;var f="suspendedStart",p="suspendedYield",m="executing",v="completed",k={};function g(){}function y(){}function b(){}var S={};d(S,s,(function(){return this}));var w=Object.getPrototypeOf,T=w&&w(w(O([])));T&&T!==r&&i.call(T,s)&&(S=T);var C=b.prototype=g.prototype=Object.create(S);function x(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function P(e,t){function n(r,a,o,s){var c=h(e[r],e,a);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==typeof d&&i.call(d,"__await")?t.resolve(d.__await).then((function(e){n("next",e,o,s)}),(function(e){n("throw",e,o,s)})):t.resolve(d).then((function(e){u.value=e,o(u)}),(function(e){return n("throw",e,o,s)}))}s(c.arg)}var r;a(this,"_invoke",{value:function(e,i){function a(){return new t((function(t,r){n(e,i,t,r)}))}return r=r?r.then(a,a):a()}})}function E(t,n,r){var i=f;return function(a,o){if(i===m)throw new Error("Generator is already running");if(i===v){if("throw"===a)throw o;return{value:e,done:!0}}for(r.method=a,r.arg=o;;){var s=r.delegate;if(s){var c=R(s,r);if(c){if(c===k)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===f)throw i=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=m;var u=h(t,n,r);if("normal"===u.type){if(i=r.done?v:p,u.arg===k)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(i=v,r.method="throw",r.arg=u.arg)}}}function R(t,n){var r=n.method,i=t.iterator[r];if(i===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,R(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),k;var a=h(i,t.iterator,n.arg);if("throw"===a.type)return n.method="throw",n.arg=a.arg,n.delegate=null,k;var o=a.arg;return o?o.done?(n[t.resultName]=o.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,k):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,k)}function I(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function D(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function N(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(I,this),this.reset(!0)}function O(t){if(t||""===t){var n=t[s];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,a=function n(){for(;++r<t.length;)if(i.call(t,r))return n.value=t[r],n.done=!1,n;return n.value=e,n.done=!0,n};return a.next=a}}throw new TypeError(typeof t+" is not iterable")}return y.prototype=b,a(C,"constructor",{value:b,configurable:!0}),a(b,"constructor",{value:y,configurable:!0}),y.displayName=d(b,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,d(e,u,"GeneratorFunction")),e.prototype=Object.create(C),e},t.awrap=function(e){return{__await:e}},x(P.prototype),d(P.prototype,c,(function(){return this})),t.AsyncIterator=P,t.async=function(e,n,r,i,a){void 0===a&&(a=Promise);var o=new P(l(e,n,r,i),a);return t.isGeneratorFunction(n)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},x(C),d(C,u,"Generator"),d(C,s,(function(){return this})),d(C,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=O,N.prototype={constructor:N,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(D),!t)for(var n in this)"t"===n.charAt(0)&&i.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function r(r,i){return s.type="throw",s.arg=t,n.next=r,i&&(n.method="next",n.arg=e),!!i}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],s=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var c=i.call(o,"catchLoc"),u=i.call(o,"finallyLoc");if(c&&u){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&i.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=e,o.arg=t,a?(this.method="next",this.next=a.finallyLoc,k):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),k},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),D(n),k}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;D(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:O(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),k}},t}function r(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function a(e,t,n,r,i,a,o){try{var s=e[a](o),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,i)}function o(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var o=e.apply(t,n);function s(e){a(o,r,i,s,c,"next",e)}function c(e){a(o,r,i,s,c,"throw",e)}s(void 0)}))}}function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function c(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,r(i.key),i)}}function u(e,t,n){return t&&c(e.prototype,t),n&&c(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function d(e,t,n){return(t=r(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&f(e,t)}function h(e){return h=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},h(e)}function f(e,t){return f=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},f(e,t)}function p(e){var n="function"==typeof Map?new Map:void 0;return p=function(e){if(null===e||!function(e){try{return-1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"==typeof e}}(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==n){if(n.has(e))return n.get(e);n.set(e,r)}function r(){return function(e,n,r){if(t())return Reflect.construct.apply(null,arguments);var i=[null];i.push.apply(i,n);var a=new(e.bind.apply(e,i));return r&&f(a,r.prototype),a}(e,arguments,h(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),f(r,e)},p(e)}function m(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function v(){return v="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=h(e)););return e}(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(3>arguments.length?e:n):i.value}},v.apply(this,arguments)}function k(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,a,o,s=[],c=!0,u=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=a.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){u=!0,i=e}finally{try{if(!c&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(u)throw i}}return s}}(e,t)||y(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function g(e){return function(e){if(Array.isArray(e))return b(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||y(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(e,t){if(e){if("string"==typeof e)return b(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?b(e,t):void 0}}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);t>n;n++)r[n]=e[n];return r}function S(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=y(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r<e.length?{done:!1,value:e[r++]}:{done:!0}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){s=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(s)throw a}}}}function w(e,t){return function(e,t){if(t.get)return t.get.call(e);return t.value}(e,C(e,t,"get"))}function T(e,t,n){return function(e,t,n){if(t.set)t.set.call(e,n);else{if(!t.writable)throw new TypeError("attempted to set read only private field");t.value=n}}(e,C(e,t,"set"),n),n}function C(e,t,n){if(!t.has(e))throw new TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function x(e,t,n){if(!t.has(e))throw new TypeError("attempted to get private field on non-instance");return n}function P(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function E(e,t,n){P(e,t),t.set(e,n)}function R(e,t){P(e,t),t.add(e)}function I(e,t){return t.forEach((function(t){t&&"string"!=typeof t&&!Array.isArray(t)&&Object.keys(t).forEach((function(n){if("default"!==n&&!(n in e)){var r=Object.getOwnPropertyDescriptor(t,n);Object.defineProperty(e,n,r.get?r:{enumerable:!0,get:function(){return t[n]}})}}))})),Object.freeze(e)}var D="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function N(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var O,_,M,L={exports:{}};_=D,M=function(){var e=function(){},t="undefined",n=("undefined"==typeof window?"undefined":i(window))!==t&&i(window.navigator)!==t&&/Trident\/|MSIE /.test(window.navigator.userAgent),r=["trace","debug","info","warn","error"];function a(e,t){var n=e[t];if("function"==typeof n.bind)return n.bind(e);try{return Function.prototype.bind.call(n,e)}catch(t){return function(){return Function.prototype.apply.apply(n,[e,arguments])}}}function o(){console.log&&(console.log.apply||Function.prototype.apply.apply(console.log,[console,arguments])),console.trace}function s(t,n){for(var i=0;i<r.length;i++){var a=r[i];this[a]=t>i?e:this.methodFactory(a,t,n)}this.log=this.debug}function c(e,n,r){return function(){("undefined"==typeof console?"undefined":i(console))!==t&&(s.call(this,n,r),this[e].apply(this,arguments))}}function u(r,s,u){return function(r){return"debug"===r&&(r="log"),("undefined"==typeof console?"undefined":i(console))!==t&&("trace"===r&&n?o:void 0!==console[r]?a(console,r):void 0!==console.log?a(console,"log"):e)}(r)||c.apply(this,arguments)}function d(e,n,a){var o,c=this;n=null==n?"WARN":n;var d="loglevel";function l(){var e;if(("undefined"==typeof window?"undefined":i(window))!==t&&d){try{e=window.localStorage[d]}catch(e){}if(i(e)===t)try{var n=window.document.cookie,r=n.indexOf(encodeURIComponent(d)+"=");-1!==r&&(e=/^([^;]+)/.exec(n.slice(r))[1])}catch(e){}return void 0===c.levels[e]&&(e=void 0),e}}"string"==typeof e?d+=":"+e:"symbol"===i(e)&&(d=void 0),c.name=e,c.levels={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,SILENT:5},c.methodFactory=a||u,c.getLevel=function(){return o},c.setLevel=function(n,a){if("string"==typeof n&&void 0!==c.levels[n.toUpperCase()]&&(n=c.levels[n.toUpperCase()]),"number"!=typeof n||0>n||n>c.levels.SILENT)throw"log.setLevel() called with invalid level: "+n;if(o=n,!1!==a&&function(e){var n=(r[e]||"silent").toUpperCase();if(("undefined"==typeof window?"undefined":i(window))!==t&&d){try{return void(window.localStorage[d]=n)}catch(e){}try{window.document.cookie=encodeURIComponent(d)+"="+n+";"}catch(e){}}}(n),s.call(c,n,e),("undefined"==typeof console?"undefined":i(console))===t&&n<c.levels.SILENT)return"No console available for logging"},c.setDefaultLevel=function(e){n=e,l()||c.setLevel(e,!1)},c.resetLevel=function(){c.setLevel(n,!1),function(){if(("undefined"==typeof window?"undefined":i(window))!==t&&d){try{return void window.localStorage.removeItem(d)}catch(e){}try{window.document.cookie=encodeURIComponent(d)+"=; expires=Thu, 01 Jan 1970 00:00:00 UTC"}catch(e){}}}()},c.enableAll=function(e){c.setLevel(c.levels.TRACE,e)},c.disableAll=function(e){c.setLevel(c.levels.SILENT,e)};var h=l();null==h&&(h=n),c.setLevel(h,!1)}var l=new d,h={};l.getLogger=function(e){if("symbol"!==i(e)&&"string"!=typeof e||""===e)throw new TypeError("You must supply a name when creating a logger.");var t=h[e];return t||(t=h[e]=new d(e,l.getLevel(),l.methodFactory)),t};var f=("undefined"==typeof window?"undefined":i(window))!==t?window.log:void 0;return l.noConflict=function(){return("undefined"==typeof window?"undefined":i(window))!==t&&window.log===l&&(window.log=f),l},l.getLoggers=function(){return h},l.default=l,l},(O=L).exports?O.exports=M():_.log=M();var A,U=L.exports;!function(e){e[e.trace=0]="trace",e[e.debug=1]="debug",e[e.info=2]="info",e[e.warn=3]="warn",e[e.error=4]="error",e[e.silent=5]="silent"}(A||(A={}));var B=U.getLogger("livekit");function F(e,t){if(!e)throw new Error(t)}B.setDefaultLevel(A.info),U.getLogger("lk-e2ee");var J=34028234663852886e22,j=-34028234663852886e22,q=4294967295,V=2147483647,H=-2147483648;function W(e){if("number"!=typeof e)throw new Error("invalid int 32: "+i(e));if(!Number.isInteger(e)||e>V||H>e)throw new Error("invalid int 32: "+e)}function G(e){if("number"!=typeof e)throw new Error("invalid uint 32: "+i(e));if(!Number.isInteger(e)||e>q||0>e)throw new Error("invalid uint 32: "+e)}function K(e){if("number"!=typeof e)throw new Error("invalid float 32: "+i(e));if(Number.isFinite(e)&&(e>J||j>e))throw new Error("invalid float 32: "+e)}var z=Symbol("@bufbuild/protobuf/enum-type");function Q(e){var t=e[z];return F(t,"missing enum type on enum object"),t}function Y(e,t,n,r){e[z]=X(t,n.map((function(t){return{no:t.no,name:t.name,localName:e[t.no]}})))}function X(e,t,n){var r,i=Object.create(null),a=Object.create(null),o=[],s=S(t);try{for(s.s();!(r=s.n()).done;){var c=r.value,u=$(c);o.push(u),i[c.name]=u,a[c.no]=u}}catch(e){s.e(e)}finally{s.f()}return{typeName:e,values:o,findName:function(e){return i[e]},findNumber:function(e){return a[e]}}}function Z(e,t,n){var r,i={},a=S(t);try{for(a.s();!(r=a.n()).done;){var o=$(r.value);i[o.localName]=o.no,i[o.no]=o.localName}}catch(e){a.e(e)}finally{a.f()}return Y(i,e,t),i}function $(e){return"localName"in e?e:Object.assign(Object.assign({},e),{localName:e.name})}var ee,te,ne=function(){function e(){s(this,e)}return u(e,[{key:"equals",value:function(e){return this.getType().runtime.util.equals(this.getType(),this,e)}},{key:"clone",value:function(){return this.getType().runtime.util.clone(this)}},{key:"fromBinary",value:function(e,t){var n=this.getType().runtime.bin,r=n.makeReadOptions(t);return n.readMessage(this,r.readerFactory(e),e.byteLength,r),this}},{key:"fromJson",value:function(e,t){var n=this.getType(),r=n.runtime.json,i=r.makeReadOptions(t);return r.readMessage(n,e,i,this),this}},{key:"fromJsonString",value:function(e,t){var n;try{n=JSON.parse(e)}catch(e){throw new Error("cannot decode ".concat(this.getType().typeName," from JSON: ").concat(e instanceof Error?e.message:String(e)))}return this.fromJson(n,t)}},{key:"toBinary",value:function(e){var t=this.getType().runtime.bin,n=t.makeWriteOptions(e),r=n.writerFactory();return t.writeMessage(this,r,n),r.finish()}},{key:"toJson",value:function(e){var t=this.getType().runtime.json,n=t.makeWriteOptions(e);return t.writeMessage(this,n)}},{key:"toJsonString",value:function(e){var t,n=this.toJson(e);return JSON.stringify(n,null,null!==(t=null==e?void 0:e.prettySpaces)&&void 0!==t?t:0)}},{key:"toJSON",value:function(){return this.toJson({emitDefaultValues:!0})}},{key:"getType",value:function(){return Object.getPrototypeOf(this).constructor}}]),e}();function re(){for(var e=0,t=0,n=0;28>n;n+=7){var r=this.buf[this.pos++];if(e|=(127&r)<<n,0==(128&r))return this.assertBounds(),[e,t]}var i=this.buf[this.pos++];if(e|=(15&i)<<28,t=(112&i)>>4,0==(128&i))return this.assertBounds(),[e,t];for(var a=3;31>=a;a+=7){var o=this.buf[this.pos++];if(t|=(127&o)<<a,0==(128&o))return this.assertBounds(),[e,t]}throw new Error("invalid varint")}function ie(e,t,n){for(var r=0;28>r;r+=7){var i=e>>>r,a=!(i>>>7==0&&0==t),o=255&(a?128|i:i);if(n.push(o),!a)return}var s=e>>>28&15|(7&t)<<4,c=!(t>>3==0);if(n.push(255&(c?128|s:s)),c){for(var u=3;31>u;u+=7){var d=t>>>u,l=!(d>>>7==0),h=255&(l?128|d:d);if(n.push(h),!l)return}n.push(t>>>31&1)}}!function(e){e[e.DOUBLE=1]="DOUBLE",e[e.FLOAT=2]="FLOAT",e[e.INT64=3]="INT64",e[e.UINT64=4]="UINT64",e[e.INT32=5]="INT32",e[e.FIXED64=6]="FIXED64",e[e.FIXED32=7]="FIXED32",e[e.BOOL=8]="BOOL",e[e.STRING=9]="STRING",e[e.BYTES=12]="BYTES",e[e.UINT32=13]="UINT32",e[e.SFIXED32=15]="SFIXED32",e[e.SFIXED64=16]="SFIXED64",e[e.SINT32=17]="SINT32",e[e.SINT64=18]="SINT64"}(ee||(ee={})),function(e){e[e.BIGINT=0]="BIGINT",e[e.STRING=1]="STRING"}(te||(te={}));var ae=4294967296;function oe(e){var t="-"===e[0];t&&(e=e.slice(1));var n=1e6,r=0,i=0;function a(t,a){var o=Number(e.slice(t,a));i*=n,ae>(r=r*n+o)||(i+=r/ae|0,r%=ae)}return a(-24,-18),a(-18,-12),a(-12,-6),a(-6),t?ue(r,i):ce(r,i)}function se(e,t){var n=function(e,t){return{lo:e>>>0,hi:t>>>0}}(e,t);if(e=n.lo,2097151>=(t=n.hi))return String(ae*t+e);var r=16777215&(e>>>24|t<<8),i=t>>16&65535,a=(16777215&e)+6777216*r+6710656*i,o=r+8147497*i,s=2*i,c=1e7;return c>a||(o+=Math.floor(a/c),a%=c),c>o||(s+=Math.floor(o/c),o%=c),s.toString()+de(o)+de(a)}function ce(e,t){return{lo:0|e,hi:0|t}}function ue(e,t){return t=~t,e?e=1+~e:t+=1,ce(e,t)}var de=function(e){var t=String(e);return"0000000".slice(t.length)+t};function le(e,t){if(e<0){for(var n=0;9>n;n++)t.push(127&e|128),e>>=7;t.push(1)}else{for(;e>127;)t.push(127&e|128),e>>>=7;t.push(e)}}function he(){var e=this.buf[this.pos++],t=127&e;if(0==(128&e))return this.assertBounds(),t;if(t|=(127&(e=this.buf[this.pos++]))<<7,0==(128&e))return this.assertBounds(),t;if(t|=(127&(e=this.buf[this.pos++]))<<14,0==(128&e))return this.assertBounds(),t;if(t|=(127&(e=this.buf[this.pos++]))<<21,0==(128&e))return this.assertBounds(),t;t|=(15&(e=this.buf[this.pos++]))<<28;for(var n=5;0!=(128&e)&&10>n;n++)e=this.buf[this.pos++];if(0!=(128&e))throw new Error("invalid varint");return this.assertBounds(),t>>>0}var fe,pe=function(){var e=new DataView(new ArrayBuffer(8));if("function"==typeof BigInt&&"function"==typeof e.getBigInt64&&"function"==typeof e.getBigUint64&&"function"==typeof e.setBigInt64&&"function"==typeof e.setBigUint64&&("object"!=("undefined"==typeof process?"undefined":i(process))||"object"!=i(process.env)||"1"!==process.env.BUF_BIGINT_DISABLE)){var t=BigInt("-9223372036854775808"),n=BigInt("9223372036854775807"),r=BigInt("0"),a=BigInt("18446744073709551615");return{zero:BigInt(0),supported:!0,parse:function(e){var r="bigint"==typeof e?e:BigInt(e);if(r>n||t>r)throw new Error("int64 invalid: ".concat(e));return r},uParse:function(e){var t="bigint"==typeof e?e:BigInt(e);if(t>a||r>t)throw new Error("uint64 invalid: ".concat(e));return t},enc:function(t){return e.setBigInt64(0,this.parse(t),!0),{lo:e.getInt32(0,!0),hi:e.getInt32(4,!0)}},uEnc:function(t){return e.setBigInt64(0,this.uParse(t),!0),{lo:e.getInt32(0,!0),hi:e.getInt32(4,!0)}},dec:function(t,n){return e.setInt32(0,t,!0),e.setInt32(4,n,!0),e.getBigInt64(0,!0)},uDec:function(t,n){return e.setInt32(0,t,!0),e.setInt32(4,n,!0),e.getBigUint64(0,!0)}}}var o=function(e){return F(/^-?[0-9]+$/.test(e),"int64 invalid: ".concat(e))},s=function(e){return F(/^[0-9]+$/.test(e),"uint64 invalid: ".concat(e))};return{zero:"0",supported:!1,parse:function(e){return"string"!=typeof e&&(e=e.toString()),o(e),e},uParse:function(e){return"string"!=typeof e&&(e=e.toString()),s(e),e},enc:function(e){return"string"!=typeof e&&(e=e.toString()),o(e),oe(e)},uEnc:function(e){return"string"!=typeof e&&(e=e.toString()),s(e),oe(e)},dec:function(e,t){return function(e,t){var n=ce(e,t),r=2147483648&n.hi;r&&(n=ue(n.lo,n.hi));var i=se(n.lo,n.hi);return r?"-"+i:i}(e,t)},uDec:function(e,t){return se(e,t)}}}();!function(e){e[e.Varint=0]="Varint",e[e.Bit64=1]="Bit64",e[e.LengthDelimited=2]="LengthDelimited",e[e.StartGroup=3]="StartGroup",e[e.EndGroup=4]="EndGroup",e[e.Bit32=5]="Bit32"}(fe||(fe={}));var me=function(){function e(t){s(this,e),this.stack=[],this.textEncoder=null!=t?t:new TextEncoder,this.chunks=[],this.buf=[]}return u(e,[{key:"finish",value:function(){this.chunks.push(new Uint8Array(this.buf));for(var e=0,t=0;t<this.chunks.length;t++)e+=this.chunks[t].length;for(var n=new Uint8Array(e),r=0,i=0;i<this.chunks.length;i++)n.set(this.chunks[i],r),r+=this.chunks[i].length;return this.chunks=[],n}},{key:"fork",value:function(){return this.stack.push({chunks:this.chunks,buf:this.buf}),this.chunks=[],this.buf=[],this}},{key:"join",value:function(){var e=this.finish(),t=this.stack.pop();if(!t)throw new Error("invalid state, fork stack empty");return this.chunks=t.chunks,this.buf=t.buf,this.uint32(e.byteLength),this.raw(e)}},{key:"tag",value:function(e,t){return this.uint32((e<<3|t)>>>0)}},{key:"raw",value:function(e){return this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]),this.chunks.push(e),this}},{key:"uint32",value:function(e){for(G(e);e>127;)this.buf.push(127&e|128),e>>>=7;return this.buf.push(e),this}},{key:"int32",value:function(e){return W(e),le(e,this.buf),this}},{key:"bool",value:function(e){return this.buf.push(e?1:0),this}},{key:"bytes",value:function(e){return this.uint32(e.byteLength),this.raw(e)}},{key:"string",value:function(e){var t=this.textEncoder.encode(e);return this.uint32(t.byteLength),this.raw(t)}},{key:"float",value:function(e){K(e);var t=new Uint8Array(4);return new DataView(t.buffer).setFloat32(0,e,!0),this.raw(t)}},{key:"double",value:function(e){var t=new Uint8Array(8);return new DataView(t.buffer).setFloat64(0,e,!0),this.raw(t)}},{key:"fixed32",value:function(e){G(e);var t=new Uint8Array(4);return new DataView(t.buffer).setUint32(0,e,!0),this.raw(t)}},{key:"sfixed32",value:function(e){W(e);var t=new Uint8Array(4);return new DataView(t.buffer).setInt32(0,e,!0),this.raw(t)}},{key:"sint32",value:function(e){return W(e),le(e=(e<<1^e>>31)>>>0,this.buf),this}},{key:"sfixed64",value:function(e){var t=new Uint8Array(8),n=new DataView(t.buffer),r=pe.enc(e);return n.setInt32(0,r.lo,!0),n.setInt32(4,r.hi,!0),this.raw(t)}},{key:"fixed64",value:function(e){var t=new Uint8Array(8),n=new DataView(t.buffer),r=pe.uEnc(e);return n.setInt32(0,r.lo,!0),n.setInt32(4,r.hi,!0),this.raw(t)}},{key:"int64",value:function(e){var t=pe.enc(e);return ie(t.lo,t.hi,this.buf),this}},{key:"sint64",value:function(e){var t=pe.enc(e),n=t.hi>>31;return ie(t.lo<<1^n,(t.hi<<1|t.lo>>>31)^n,this.buf),this}},{key:"uint64",value:function(e){var t=pe.uEnc(e);return ie(t.lo,t.hi,this.buf),this}}]),e}(),ve=function(){function e(t,n){s(this,e),this.varint64=re,this.uint32=he,this.buf=t,this.len=t.length,this.pos=0,this.view=new DataView(t.buffer,t.byteOffset,t.byteLength),this.textDecoder=null!=n?n:new TextDecoder}return u(e,[{key:"tag",value:function(){var e=this.uint32(),t=e>>>3,n=7&e;if(0>=t||0>n||n>5)throw new Error("illegal tag: field no "+t+" wire type "+n);return[t,n]}},{key:"skip",value:function(e){var t=this.pos;switch(e){case fe.Varint:for(;128&this.buf[this.pos++];);break;case fe.Bit64:this.pos+=4;case fe.Bit32:this.pos+=4;break;case fe.LengthDelimited:var n=this.uint32();this.pos+=n;break;case fe.StartGroup:for(var r;(r=this.tag()[1])!==fe.EndGroup;)this.skip(r);break;default:throw new Error("cant skip wire type "+e)}return this.assertBounds(),this.buf.subarray(t,this.pos)}},{key:"assertBounds",value:function(){if(this.pos>this.len)throw new RangeError("premature EOF")}},{key:"int32",value:function(){return 0|this.uint32()}},{key:"sint32",value:function(){var e=this.uint32();return e>>>1^-(1&e)}},{key:"int64",value:function(){return pe.dec.apply(pe,g(this.varint64()))}},{key:"uint64",value:function(){return pe.uDec.apply(pe,g(this.varint64()))}},{key:"sint64",value:function(){var e=k(this.varint64(),2),t=e[0],n=e[1],r=-(1&t);return t=(t>>>1|(1&n)<<31)^r,n=n>>>1^r,pe.dec(t,n)}},{key:"bool",value:function(){var e=k(this.varint64(),2),t=e[0],n=e[1];return 0!==t||0!==n}},{key:"fixed32",value:function(){return this.view.getUint32((this.pos+=4)-4,!0)}},{key:"sfixed32",value:function(){return this.view.getInt32((this.pos+=4)-4,!0)}},{key:"fixed64",value:function(){return pe.uDec(this.sfixed32(),this.sfixed32())}},{key:"sfixed64",value:function(){return pe.dec(this.sfixed32(),this.sfixed32())}},{key:"float",value:function(){return this.view.getFloat32((this.pos+=4)-4,!0)}},{key:"double",value:function(){return this.view.getFloat64((this.pos+=8)-8,!0)}},{key:"bytes",value:function(){var e=this.uint32(),t=this.pos;return this.pos+=e,this.assertBounds(),this.buf.subarray(t,t+e)}},{key:"string",value:function(){return this.textDecoder.decode(this.bytes())}}]),e}();function ke(e,t){return t instanceof ne||!e.fieldWrapper?t:e.fieldWrapper.wrapField(t)}function ge(e,t,n){if(t===n)return!0;if(e==ee.BYTES){if(!(t instanceof Uint8Array&&n instanceof Uint8Array))return!1;if(t.length!==n.length)return!1;for(var r=0;r<t.length;r++)if(t[r]!==n[r])return!1;return!0}switch(e){case ee.UINT64:case ee.FIXED64:case ee.INT64:case ee.SFIXED64:case ee.SINT64:return t==n}return!1}function ye(e,t){switch(e){case ee.BOOL:return!1;case ee.UINT64:case ee.FIXED64:case ee.INT64:case ee.SFIXED64:case ee.SINT64:return 0==t?pe.zero:"0";case ee.DOUBLE:case ee.FLOAT:return 0;case ee.BYTES:return new Uint8Array(0);case ee.STRING:return"";default:return 0}}function be(e,t){var n=void 0===t,r=fe.Varint,i=0===t;switch(e){case ee.STRING:i=n||!t.length,r=fe.LengthDelimited;break;case ee.BOOL:i=!1===t;break;case ee.DOUBLE:r=fe.Bit64;break;case ee.FLOAT:r=fe.Bit32;break;case ee.INT64:i=n||0==t;break;case ee.UINT64:i=n||0==t;break;case ee.FIXED64:i=n||0==t,r=fe.Bit64;break;case ee.BYTES:i=n||!t.byteLength,r=fe.LengthDelimited;break;case ee.FIXED32:r=fe.Bit32;break;case ee.SFIXED32:r=fe.Bit32;break;case ee.SFIXED64:i=n||0==t,r=fe.Bit64;break;case ee.SINT64:i=n||0==t;break}return[r,ee[e].toLowerCase(),n||i]}ee.DOUBLE,ee.FLOAT,ee.INT64,ee.UINT64,ee.INT32,ee.UINT32,ee.BOOL,ee.STRING,ee.BYTES;var Se=Symbol("@bufbuild/protobuf/unknown-fields"),we={readUnknownFields:!0,readerFactory:function(e){return new ve(e)}},Te={writeUnknownFields:!0,writerFactory:function(){return new me}};function Ce(e){return e?Object.assign(Object.assign({},we),e):we}function xe(e){return e?Object.assign(Object.assign({},Te),e):Te}function Pe(e,t,n){return t.getType().runtime.bin.readMessage(t,e,e.uint32(),n),t}function Ee(e,t,n){for(var r,i,a=t.uint32(),o=t.pos+a;t.pos<o;){switch(k(t.tag(),1)[0]){case 1:r=Ie(t,e.K);break;case 2:switch(e.V.kind){case"scalar":i=Ie(t,e.V.T);break;case"enum":i=t.int32();break;case"message":i=Pe(t,new e.V.T,n);break}break}}if(void 0===r){var s=ye(e.K,te.BIGINT);r=e.K==ee.BOOL?s.toString():s}if("string"!=typeof r&&"number"!=typeof r&&(r=r.toString()),void 0===i)switch(e.V.kind){case"scalar":i=ye(e.V.T,te.BIGINT);break;case"enum":i=0;break;case"message":i=new e.V.T;break}return[r,i]}function Re(e,t){var n=Ie(e,t);return"bigint"==typeof n?n.toString():n}function Ie(e,t){switch(t){case ee.STRING:return e.string();case ee.BOOL:return e.bool();case ee.DOUBLE:return e.double();case ee.FLOAT:return e.float();case ee.INT32:return e.int32();case ee.INT64:return e.int64();case ee.UINT64:return e.uint64();case ee.FIXED64:return e.fixed64();case ee.BYTES:return e.bytes();case ee.FIXED32:return e.fixed32();case ee.SFIXED32:return e.sfixed32();case ee.SFIXED64:return e.sfixed64();case ee.SINT64:return e.sint64();case ee.UINT32:return e.uint32();case ee.SINT32:return e.sint32()}}function De(e,t,n,r,i){e.tag(n.no,fe.LengthDelimited),e.fork();var a=r;switch(n.K){case ee.INT32:case ee.FIXED32:case ee.UINT32:case ee.SFIXED32:case ee.SINT32:a=Number.parseInt(r);break;case ee.BOOL:F("true"==r||"false"==r),a="true"==r;break}switch(Oe(e,n.K,1,a,!0),n.V.kind){case"scalar":Oe(e,n.V.T,2,i,!0);break;case"enum":Oe(e,ee.INT32,2,i,!0);break;case"message":Ne(e,t,n.V.T,2,i);break}e.join()}function Ne(e,t,n,r,i){if(void 0!==i){var a=ke(n,i);e.tag(r,fe.LengthDelimited).bytes(a.toBinary(t))}}function Oe(e,t,n,r,i){var a=k(be(t,r),3),o=a[0],s=a[1];a[2]&&!i||e.tag(n,o)[s](r)}function _e(e,t,n,r){if(r.length){e.tag(n,fe.LengthDelimited).fork();for(var i=k(be(t),2)[1],a=0;a<r.length;a++)e[i](r[a]);e.join()}}for(var Me="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),Le=[],Ae=0;Ae<Me.length;Ae++)Le[Me[Ae].charCodeAt(0)]=Ae;Le["-".charCodeAt(0)]=Me.indexOf("+"),Le["_".charCodeAt(0)]=Me.indexOf("/");var Ue={dec:function(e){var t=3*e.length/4;"="==e[e.length-2]?t-=2:"="==e[e.length-1]&&(t-=1);for(var n,r=new Uint8Array(t),i=0,a=0,o=0,s=0;s<e.length;s++){if(void 0===(n=Le[e.charCodeAt(s)]))switch(e[s]){case"=":a=0;case"\n":case"\r":case"\t":case" ":continue;default:throw Error("invalid base64 string.")}switch(a){case 0:o=n,a=1;break;case 1:r[i++]=o<<2|(48&n)>>4,o=n,a=2;break;case 2:r[i++]=(15&o)<<4|(60&n)>>2,o=n,a=3;break;case 3:r[i++]=(3&o)<<6|n,a=0;break}}if(1==a)throw Error("invalid base64 string.");return r.subarray(0,i)},enc:function(e){for(var t,n="",r=0,i=0,a=0;a<e.length;a++)switch(t=e[a],r){case 0:n+=Me[t>>2],i=(3&t)<<4,r=1;break;case 1:n+=Me[i|t>>4],i=(15&t)<<2,r=2;break;case 2:n+=Me[i|t>>6],n+=Me[63&t],r=0;break}return r&&(n+=Me[i],n+="=",1==r&&(n+="=")),n}},Be={ignoreUnknownFields:!1},Fe={emitDefaultValues:!1,enumAsInteger:!1,useProtoFieldName:!1,prettySpaces:0};function Je(e){return e?Object.assign(Object.assign({},Be),e):Be}function je(e){return e?Object.assign(Object.assign({},Fe),e):Fe}function qe(e){if(null===e)return"null";switch(i(e)){case"object":return Array.isArray(e)?"array":"object";case"string":return e.length>100?"string":'"'.concat(e.split('"').join('\\"'),'"');default:return String(e)}}function Ve(e,t,n){switch(e){case ee.DOUBLE:case ee.FLOAT:if(null===t)return 0;if("NaN"===t)return Number.NaN;if("Infinity"===t)return Number.POSITIVE_INFINITY;if("-Infinity"===t)return Number.NEGATIVE_INFINITY;if(""===t)break;if("string"==typeof t&&t.trim().length!==t.length)break;if("string"!=typeof t&&"number"!=typeof t)break;var r=Number(t);if(Number.isNaN(r))break;if(!Number.isFinite(r))break;return e==ee.FLOAT&&K(r),r;case ee.INT32:case ee.FIXED32:case ee.SFIXED32:case ee.SINT32:case ee.UINT32:if(null===t)return 0;var i;if("number"==typeof t?i=t:"string"==typeof t&&t.length>0&&t.trim().length===t.length&&(i=Number(t)),void 0===i)break;return e==ee.UINT32?G(i):W(i),i;case ee.INT64:case ee.SFIXED64:case ee.SINT64:if(null===t)return pe.zero;if("number"!=typeof t&&"string"!=typeof t)break;var a=pe.parse(t);return n?a.toString():a;case ee.FIXED64:case ee.UINT64:if(null===t)return pe.zero;if("number"!=typeof t&&"string"!=typeof t)break;var o=pe.uParse(t);return n?o.toString():o;case ee.BOOL:if(null===t)return!1;if("boolean"!=typeof t)break;return t;case ee.STRING:if(null===t)return"";if("string"!=typeof t)break;try{encodeURIComponent(t)}catch(e){throw new Error("invalid UTF8")}return t;case ee.BYTES:if(null===t||""===t)return new Uint8Array(0);if("string"!=typeof t)break;return Ue.dec(t)}throw new Error}function He(e,t,n){if(null===t)return 0;switch(i(t)){case"number":if(Number.isInteger(t))return t;break;case"string":var r=e.findName(t);if(r||n)return null==r?void 0:r.no;break}throw new Error("cannot decode enum ".concat(e.typeName," from JSON: ").concat(qe(t)))}function We(e,t,n,r){var i;if(void 0===t)return t;if(0!==t||n){if(r)return t;if("google.protobuf.NullValue"==e.typeName)return null;var a=e.findNumber(t);return null!==(i=null==a?void 0:a.name)&&void 0!==i?i:t}}function Ge(e,t,n){if(void 0!==t)switch(e){case ee.INT32:case ee.SFIXED32:case ee.SINT32:case ee.FIXED32:case ee.UINT32:return F("number"==typeof t),0!=t||n?t:void 0;case ee.FLOAT:case ee.DOUBLE:return F("number"==typeof t),Number.isNaN(t)?"NaN":t===Number.POSITIVE_INFINITY?"Infinity":t===Number.NEGATIVE_INFINITY?"-Infinity":0!==t||n?t:void 0;case ee.STRING:return F("string"==typeof t),t.length>0||n?t:void 0;case ee.BOOL:return F("boolean"==typeof t),t||n?t:void 0;case ee.UINT64:case ee.FIXED64:case ee.INT64:case ee.SFIXED64:case ee.SINT64:return F("bigint"==typeof t||"string"==typeof t||"number"==typeof t),n||0!=t?t.toString(10):void 0;case ee.BYTES:return F(t instanceof Uint8Array),n||t.byteLength>0?Ue.enc(t):void 0}}function Ke(e){if(void 0===e)return e;if(e instanceof ne)return e.clone();if(e instanceof Uint8Array){var t=new Uint8Array(e.byteLength);return t.set(e),t}return e}function ze(e){return e instanceof Uint8Array?e:new Uint8Array(e)}var Qe=function(){function e(t,n){s(this,e),this._fields=t,this._normalizer=n}return u(e,[{key:"findJsonName",value:function(e){if(!this.jsonNames){var t,n={},r=S(this.list());try{for(r.s();!(t=r.n()).done;){var i=t.value;n[i.jsonName]=n[i.name]=i}}catch(e){r.e(e)}finally{r.f()}this.jsonNames=n}return this.jsonNames[e]}},{key:"find",value:function(e){if(!this.numbers){var t,n={},r=S(this.list());try{for(r.s();!(t=r.n()).done;){var i=t.value;n[i.no]=i}}catch(e){r.e(e)}finally{r.f()}this.numbers=n}return this.numbers[e]}},{key:"list",value:function(){return this.all||(this.all=this._normalizer(this._fields)),this.all}},{key:"byNumber",value:function(){return this.numbersAsc||(this.numbersAsc=this.list().concat().sort((function(e,t){return e.no-t.no}))),this.numbersAsc}},{key:"byMember",value:function(){if(!this.members){this.members=[];var e,t,n=this.members,r=S(this.list());try{for(r.s();!(t=r.n()).done;){var i=t.value;i.oneof?i.oneof!==e&&(e=i.oneof,n.push(e)):n.push(i)}}catch(e){r.e(e)}finally{r.f()}}return this.members}}]),e}();function Ye(e,t){var n=Ze(e);return t?n:ut(ct(n))}var Xe=Ze;function Ze(e){for(var t=!1,n=[],r=0;r<e.length;r++){var i=e.charAt(r);switch(i){case"_":t=!0;break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":n.push(i),t=!1;break;default:t&&(t=!1,i=i.toUpperCase()),n.push(i);break}}return n.join("")}var $e,et,tt,nt,rt,it,at=new Set(["constructor","toString","toJSON","valueOf"]),ot=new Set(["getType","clone","equals","fromBinary","fromJson","fromJsonString","toBinary","toJson","toJsonString","toObject"]),st=function(e){return"".concat(e,"$")},ct=function(e){return ot.has(e)?st(e):e},ut=function(e){return at.has(e)?st(e):e},dt=function(){function e(t){s(this,e),this.kind="oneof",this.repeated=!1,this.packed=!1,this.opt=!1,this.default=void 0,this.fields=[],this.name=t,this.localName=Ye(t,!1)}return u(e,[{key:"addField",value:function(e){F(e.oneof===this,"field ".concat(e.name," not one of ").concat(this.name)),this.fields.push(e)}},{key:"findField",value:function(e){if(!this._lookup){this._lookup=Object.create(null);for(var t=0;t<this.fields.length;t++)this._lookup[this.fields[t].localName]=this.fields[t]}return this._lookup[e]}}]),e}(),lt=($e="proto3",rt=function(e,t){return function(n,r,i){if("map"==n.kind){var a={};switch(n.V.kind){case"scalar":for(var o=0,s=Object.entries(r);o<s.length;o++){var c=k(s[o],2),u=c[0],d=c[1],l=t(n.V.T,d,!0);F(void 0!==l),a[u.toString()]=l}break;case"message":for(var h=0,f=Object.entries(r);h<f.length;h++){var p=k(f[h],2),m=p[0],v=p[1];a[m.toString()]=v.toJson(i)}break;case"enum":for(var g=n.V.T,y=0,b=Object.entries(r);y<b.length;y++){var S=k(b[y],2),w=S[0],T=S[1];F(void 0===T||"number"==typeof T);var C=e(g,T,!0,i.enumAsInteger);F(void 0!==C),a[w.toString()]=C}break}return i.emitDefaultValues||Object.keys(a).length>0?a:void 0}if(n.repeated){var x=[];switch(n.kind){case"scalar":for(var P=0;P<r.length;P++)x.push(t(n.T,r[P],!0));break;case"enum":for(var E=0;E<r.length;E++)x.push(e(n.T,r[E],!0,i.enumAsInteger));break;case"message":for(var R=0;R<r.length;R++)x.push(ke(n.T,r[R]).toJson(i));break}return i.emitDefaultValues||x.length>0?x:void 0}switch(n.kind){case"scalar":return t(n.T,r,!!n.oneof||n.opt||i.emitDefaultValues);case"enum":return e(n.T,r,!!n.oneof||n.opt||i.emitDefaultValues,i.enumAsInteger);case"message":return void 0!==r?ke(n.T,r).toJson(i):void 0}}},it=rt(We,Ge),et={makeReadOptions:Je,makeWriteOptions:je,readMessage:function(e,t,n,r){if(null==t||Array.isArray(t)||"object"!=i(t))throw new Error("cannot decode message ".concat(e.typeName," from JSON: ").concat(this.debug(t)));r=null!=r?r:new e;for(var a={},o=0,s=Object.entries(t);o<s.length;o++){var c=k(s[o],2),u=c[0],d=c[1],l=e.fields.findJsonName(u);if(l){var h=l.localName,f=r;if(l.oneof){if(null===d&&"scalar"==l.kind)continue;var p=a[l.oneof.localName];if(p)throw new Error("cannot decode message ".concat(e.typeName,' from JSON: multiple keys for oneof "').concat(l.oneof.name,'" present: "').concat(p,'", "').concat(u,'"'));a[l.oneof.localName]=u,f=f[l.oneof.localName]={case:h},h="value"}if(l.repeated){if(null===d)continue;if(!Array.isArray(d))throw new Error("cannot decode field ".concat(e.typeName,".").concat(l.name," from JSON: ").concat(this.debug(d)));var m,v=f[h],g=S(d);try{for(g.s();!(m=g.n()).done;){var y=m.value;if(null===y)throw new Error("cannot decode field ".concat(e.typeName,".").concat(l.name," from JSON: ").concat(this.debug(y)));var b=void 0;switch(l.kind){case"message":b=l.T.fromJson(y,n);break;case"enum":if(void 0===(b=He(l.T,y,n.ignoreUnknownFields)))continue;break;case"scalar":try{b=Ve(l.T,y,l.L)}catch(t){var w="cannot decode field ".concat(e.typeName,".").concat(l.name," from JSON: ").concat(this.debug(y));throw t instanceof Error&&t.message.length>0&&(w+=": ".concat(t.message)),new Error(w)}break}v.push(b)}}catch(e){g.e(e)}finally{g.f()}}else if("map"==l.kind){if(null===d)continue;if(Array.isArray(d)||"object"!=i(d))throw new Error("cannot decode field ".concat(e.typeName,".").concat(l.name," from JSON: ").concat(this.debug(d)));for(var T=f[h],C=0,x=Object.entries(d);C<x.length;C++){var P=k(x[C],2),E=P[0],R=P[1];if(null===R)throw new Error("cannot decode field ".concat(e.typeName,".").concat(l.name," from JSON: map value null"));var I=void 0;switch(l.V.kind){case"message":I=l.V.T.fromJson(R,n);break;case"enum":if(void 0===(I=He(l.V.T,R,n.ignoreUnknownFields)))continue;break;case"scalar":try{I=Ve(l.V.T,R,te.BIGINT)}catch(t){var D="cannot decode map value for field ".concat(e.typeName,".").concat(l.name," from JSON: ").concat(this.debug(d));throw t instanceof Error&&t.message.length>0&&(D+=": ".concat(t.message)),new Error(D)}break}try{T[Ve(l.K,l.K==ee.BOOL?"true"==E||"false"!=E&&E:E,te.BIGINT).toString()]=I}catch(t){var N="cannot decode map key for field ".concat(e.typeName,".").concat(l.name," from JSON: ").concat(this.debug(d));throw t instanceof Error&&t.message.length>0&&(N+=": ".concat(t.message)),new Error(N)}}}else switch(l.kind){case"message":var O=l.T;if(null===d&&"google.protobuf.Value"!=O.typeName){if(l.oneof)throw new Error("cannot decode field ".concat(e.typeName,".").concat(l.name,' from JSON: null is invalid for oneof field "').concat(u,'"'));continue}f[h]instanceof ne?f[h].fromJson(d,n):(f[h]=O.fromJson(d,n),O.fieldWrapper&&!l.oneof&&(f[h]=O.fieldWrapper.unwrapField(f[h])));break;case"enum":var _=He(l.T,d,n.ignoreUnknownFields);void 0!==_&&(f[h]=_);break;case"scalar":try{f[h]=Ve(l.T,d,l.L)}catch(t){var M="cannot decode field ".concat(e.typeName,".").concat(l.name," from JSON: ").concat(this.debug(d));throw t instanceof Error&&t.message.length>0&&(M+=": ".concat(t.message)),new Error(M)}break}}else if(!n.ignoreUnknownFields)throw new Error("cannot decode message ".concat(e.typeName,' from JSON: key "').concat(u,'" is unknown'))}return r},writeMessage:function(e,t){var n,r=e.getType(),i={};try{var a,o=S(r.fields.byMember());try{for(o.s();!(a=o.n()).done;){var s=a.value,c=void 0;if("oneof"==s.kind){var u=e[s.localName];if(void 0===u.value)continue;if(!(n=s.findField(u.case)))throw"oneof case not found: "+u.case;c=it(n,u.value,t)}else c=it(n=s,e[n.localName],t);void 0!==c&&(i[t.useProtoFieldName?n.name:n.jsonName]=c)}}catch(e){o.e(e)}finally{o.f()}}catch(e){var d=n?"cannot encode field ".concat(r.typeName,".").concat(n.name," to JSON"):"cannot encode message ".concat(r.typeName," to JSON"),l=e instanceof Error?e.message:String(e);throw new Error(d+(l.length>0?": ".concat(l):""))}return i},readScalar:Ve,writeScalar:Ge,debug:qe},tt=Object.assign(Object.assign({},{makeReadOptions:Ce,makeWriteOptions:xe,listUnknownFields:function(e){var t;return null!==(t=e[Se])&&void 0!==t?t:[]},discardUnknownFields:function(e){delete e[Se]},writeUnknownFields:function(e,t){var n=e[Se];if(n){var r,i=S(n);try{for(i.s();!(r=i.n()).done;){var a=r.value;t.tag(a.no,a.wireType).raw(a.data)}}catch(e){i.e(e)}finally{i.f()}}},onUnknownField:function(e,t,n,r){var i=e;Array.isArray(i[Se])||(i[Se]=[]),i[Se].push({no:t,wireType:n,data:r})},readMessage:function(e,t,n,r){for(var i=e.getType(),a=void 0===n?t.len:t.pos+n;t.pos<a;){var o=k(t.tag(),2),s=o[0],c=o[1],u=i.fields.find(s);if(u){var d=e,l=u.repeated,h=u.localName;switch(u.oneof&&((d=d[u.oneof.localName]).case!=h&&delete d.value,d.case=h,h="value"),u.kind){case"scalar":case"enum":var f="enum"==u.kind?ee.INT32:u.T,p=Ie;if("scalar"==u.kind&&u.L>0&&(p=Re),l){var m=d[h];if(c==fe.LengthDelimited&&f!=ee.STRING&&f!=ee.BYTES)for(var v=t.uint32()+t.pos;t.pos<v;)m.push(p(t,f));else m.push(p(t,f))}else d[h]=p(t,f);break;case"message":var g=u.T;l?d[h].push(Pe(t,new g,r)):d[h]instanceof ne?Pe(t,d[h],r):(d[h]=Pe(t,new g,r),!g.fieldWrapper||u.oneof||u.repeated||(d[h]=g.fieldWrapper.unwrapField(d[h])));break;case"map":var y=k(Ee(u,t,r),2),b=y[0],S=y[1];d[h][b]=S;break}}else{var w=t.skip(c);r.readUnknownFields&&this.onUnknownField(e,s,c,w)}}}}),{writeMessage:function(e,t,n){var r,i=S(e.getType().fields.byNumber());try{for(i.s();!(r=i.n()).done;){var a=r.value,o=void 0,s=a.repeated,c=a.localName;if(a.oneof){var u=e[a.oneof.localName];if(u.case!==c)continue;o=u.value}else o=e[c];switch(a.kind){case"scalar":case"enum":var d="enum"==a.kind?ee.INT32:a.T;if(s)if(a.packed)_e(t,d,a.no,o);else{var l,h=S(o);try{for(h.s();!(l=h.n()).done;){var f=l.value;Oe(t,d,a.no,f,!0)}}catch(e){h.e(e)}finally{h.f()}}else void 0!==o&&Oe(t,d,a.no,o,!!a.oneof||a.opt);break;case"message":if(s){var p,m=S(o);try{for(m.s();!(p=m.n()).done;){var v=p.value;Ne(t,n,a.T,a.no,v)}}catch(e){m.e(e)}finally{m.f()}}else Ne(t,n,a.T,a.no,o);break;case"map":for(var g=0,y=Object.entries(o);g<y.length;g++){var b=k(y[g],2);De(t,n,a,b[0],b[1])}break}}}catch(e){i.e(e)}finally{i.f()}return n.writeUnknownFields&&this.writeUnknownFields(e,t),t}}),nt=Object.assign(Object.assign({},{setEnumType:Y,initPartial:function(e,t){if(void 0!==e){var n,r=S(t.getType().fields.byMember());try{var i=function(){var r=n.value,i=r.localName,a=t,o=e;if(void 0===o[i])return 0;switch(r.kind){case"oneof":var s=o[i].case;if(void 0===s)return 0;var c=r.findField(s),u=o[i].value;!c||"message"!=c.kind||u instanceof c.T?c&&"scalar"===c.kind&&c.T===ee.BYTES&&(u=ze(u)):u=new c.T(u),a[i]={case:s,value:u};break;case"scalar":case"enum":var d=o[i];r.T===ee.BYTES&&(d=r.repeated?d.map(ze):ze(d)),a[i]=d;break;case"map":switch(r.V.kind){case"scalar":case"enum":if(r.V.T===ee.BYTES)for(var l=0,h=Object.entries(o[i]);l<h.length;l++){var f=k(h[l],2),p=f[0],m=f[1];a[i][p]=ze(m)}else Object.assign(a[i],o[i]);break;case"message":for(var v=r.V.T,g=0,y=Object.keys(o[i]);g<y.length;g++){var b=y[g],S=o[i][b];v.fieldWrapper||(S=new v(S)),a[i][b]=S}break}break;case"message":var w=r.T;if(r.repeated)a[i]=o[i].map((function(e){return e instanceof w?e:new w(e)}));else if(void 0!==o[i]){var T=o[i];w.fieldWrapper?"google.protobuf.BytesValue"===w.typeName?a[i]=ze(T):a[i]=T:a[i]=T instanceof w?T:new w(T)}break}};for(r.s();!(n=r.n()).done;)i()}catch(e){r.e(e)}finally{r.f()}}},equals:function(e,t,n){return t===n||!(!t||!n)&&e.fields.byMember().every((function(e){var r=t[e.localName],i=n[e.localName];if(e.repeated){if(r.length!==i.length)return!1;switch(e.kind){case"message":return r.every((function(t,n){return e.T.equals(t,i[n])}));case"scalar":return r.every((function(t,n){return ge(e.T,t,i[n])}));case"enum":return r.every((function(e,t){return ge(ee.INT32,e,i[t])}))}throw new Error("repeated cannot contain ".concat(e.kind))}switch(e.kind){case"message":return e.T.equals(r,i);case"enum":return ge(ee.INT32,r,i);case"scalar":return ge(e.T,r,i);case"oneof":if(r.case!==i.case)return!1;var a=e.findField(r.case);if(void 0===a)return!0;switch(a.kind){case"message":return a.T.equals(r.value,i.value);case"enum":return ge(ee.INT32,r.value,i.value);case"scalar":return ge(a.T,r.value,i.value)}throw new Error("oneof cannot contain ".concat(a.kind));case"map":var o=Object.keys(r).concat(Object.keys(i));switch(e.V.kind){case"message":var s=e.V.T;return o.every((function(e){return s.equals(r[e],i[e])}));case"enum":return o.every((function(e){return ge(ee.INT32,r[e],i[e])}));case"scalar":var c=e.V.T;return o.every((function(e){return ge(c,r[e],i[e])}))}break}}))},clone:function(e){var t,n=e.getType(),r=new n,i=r,a=S(n.fields.byMember());try{for(a.s();!(t=a.n()).done;){var o=t.value,s=e[o.localName],c=void 0;if(o.repeated)c=s.map(Ke);else if("map"==o.kind){c=i[o.localName];for(var u=0,d=Object.entries(s);u<d.length;u++){var l=k(d[u],2),h=l[0],f=l[1];c[h]=Ke(f)}}else c="oneof"==o.kind?o.findField(s.case)?{case:s.case,value:Ke(s.value)}:{case:void 0}:Ke(s);i[o.localName]=c}}catch(e){a.e(e)}finally{a.f()}return r}}),{newFieldList:function(e){return new Qe(e,ht)},initFields:function(e){var t,n=S(e.getType().fields.byMember());try{for(n.s();!(t=n.n()).done;){var r=t.value;if(!r.opt){var i=r.localName,a=e;if(r.repeated)a[i]=[];else switch(r.kind){case"oneof":a[i]={case:void 0};break;case"enum":a[i]=0;break;case"map":a[i]={};break;case"scalar":a[i]=ye(r.T,r.L);break}}}}catch(e){n.e(e)}finally{n.f()}}}),{syntax:$e,json:et,bin:tt,util:nt,makeMessageType:function(e,t,n){return function(e,t,n,r){var i,a=null!==(i=null==r?void 0:r.localName)&&void 0!==i?i:t.substring(t.lastIndexOf(".")+1),o=d({},a,(function(t){e.util.initFields(this),e.util.initPartial(t,this)}))[a];return Object.setPrototypeOf(o.prototype,new ne),Object.assign(o,{runtime:e,typeName:t,fields:e.util.newFieldList(n),fromBinary:function(e,t){return(new o).fromBinary(e,t)},fromJson:function(e,t){return(new o).fromJson(e,t)},fromJsonString:function(e,t){return(new o).fromJsonString(e,t)},equals:function(t,n){return e.util.equals(o,t,n)}}),o}(this,e,t,n)},makeEnum:Z,makeEnumType:X,getEnumType:Q});function ht(e){var t,n,r,i,a,o,s=[],c=S("function"==typeof e?e():e);try{for(c.s();!(o=c.n()).done;){var u=o.value,d=u;if(d.localName=Ye(u.name,void 0!==u.oneof),d.jsonName=null!==(t=u.jsonName)&&void 0!==t?t:Xe(u.name),d.repeated=null!==(n=u.repeated)&&void 0!==n&&n,"scalar"==u.kind&&(d.L=null!==(r=u.L)&&void 0!==r?r:te.BIGINT),d.packed=null!==(i=u.packed)&&void 0!==i?i:"enum"==u.kind||"scalar"==u.kind&&u.T!=ee.BYTES&&u.T!=ee.STRING,void 0!==u.oneof){var l="string"==typeof u.oneof?u.oneof:u.oneof.name;a&&a.name==l||(a=new dt(l)),d.oneof=a,a.addField(d)}s.push(d)}}catch(e){c.e(e)}finally{c.f()}return s}var ft,pt,mt,vt,kt,gt,yt,bt,St,wt,Tt,Ct=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).seconds=pe.zero,r.nanos=0,lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,[{key:"fromJson",value:function(e,t){if("string"!=typeof e)throw new Error("cannot decode google.protobuf.Timestamp from JSON: ".concat(lt.json.debug(e)));var n=e.match(/^([0-9]{4})-([0-9]{2})-([0-9]{2})T([0-9]{2}):([0-9]{2}):([0-9]{2})(?:Z|\.([0-9]{3,9})Z|([+-][0-9][0-9]:[0-9][0-9]))$/);if(!n)throw new Error("cannot decode google.protobuf.Timestamp from JSON: invalid RFC 3339 string");var r=Date.parse(n[1]+"-"+n[2]+"-"+n[3]+"T"+n[4]+":"+n[5]+":"+n[6]+(n[8]?n[8]:"Z"));if(Number.isNaN(r))throw new Error("cannot decode google.protobuf.Timestamp from JSON: invalid RFC 3339 string");if(r<Date.parse("0001-01-01T00:00:00Z")||r>Date.parse("9999-12-31T23:59:59Z"))throw new Error("cannot decode message google.protobuf.Timestamp from JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive");return this.seconds=pe.parse(r/1e3),this.nanos=0,n[7]&&(this.nanos=parseInt("1"+n[7]+"0".repeat(9-n[7].length))-1e9),this}},{key:"toJson",value:function(e){var t=1e3*Number(this.seconds);if(t<Date.parse("0001-01-01T00:00:00Z")||t>Date.parse("9999-12-31T23:59:59Z"))throw new Error("cannot encode google.protobuf.Timestamp to JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive");if(0>this.nanos)throw new Error("cannot encode google.protobuf.Timestamp to JSON: nanos must not be negative");var n="Z";if(this.nanos>0){var r=(this.nanos+1e9).toString().substring(1);n="000000"===r.substring(3)?"."+r.substring(0,3)+"Z":"000"===r.substring(6)?"."+r.substring(0,6)+"Z":"."+r+"Z"}return new Date(t).toISOString().replace(".000Z",n)}},{key:"toDate",value:function(){return new Date(1e3*Number(this.seconds)+Math.ceil(this.nanos/1e6))}}],[{key:"now",value:function(){return n.fromDate(new Date)}},{key:"fromDate",value:function(e){var t=e.getTime();return new n({seconds:pe.parse(Math.floor(t/1e3)),nanos:t%1e3*1e6})}},{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Ct.runtime=lt,Ct.typeName="google.protobuf.Timestamp",Ct.fields=lt.util.newFieldList((function(){return[{no:1,name:"seconds",kind:"scalar",T:3},{no:2,name:"nanos",kind:"scalar",T:5}]})),function(e){e[e.DEFAULT_AC=0]="DEFAULT_AC",e[e.OPUS=1]="OPUS",e[e.AAC=2]="AAC"}(ft||(ft={})),lt.util.setEnumType(ft,"livekit.AudioCodec",[{no:0,name:"DEFAULT_AC"},{no:1,name:"OPUS"},{no:2,name:"AAC"}]),function(e){e[e.DEFAULT_VC=0]="DEFAULT_VC",e[e.H264_BASELINE=1]="H264_BASELINE",e[e.H264_MAIN=2]="H264_MAIN",e[e.H264_HIGH=3]="H264_HIGH",e[e.VP8=4]="VP8"}(pt||(pt={})),lt.util.setEnumType(pt,"livekit.VideoCodec",[{no:0,name:"DEFAULT_VC"},{no:1,name:"H264_BASELINE"},{no:2,name:"H264_MAIN"},{no:3,name:"H264_HIGH"},{no:4,name:"VP8"}]),function(e){e[e.IC_DEFAULT=0]="IC_DEFAULT",e[e.IC_JPEG=1]="IC_JPEG"}(mt||(mt={})),lt.util.setEnumType(mt,"livekit.ImageCodec",[{no:0,name:"IC_DEFAULT"},{no:1,name:"IC_JPEG"}]),function(e){e[e.AUDIO=0]="AUDIO",e[e.VIDEO=1]="VIDEO",e[e.DATA=2]="DATA"}(vt||(vt={})),lt.util.setEnumType(vt,"livekit.TrackType",[{no:0,name:"AUDIO"},{no:1,name:"VIDEO"},{no:2,name:"DATA"}]),function(e){e[e.UNKNOWN=0]="UNKNOWN",e[e.CAMERA=1]="CAMERA",e[e.MICROPHONE=2]="MICROPHONE",e[e.SCREEN_SHARE=3]="SCREEN_SHARE",e[e.SCREEN_SHARE_AUDIO=4]="SCREEN_SHARE_AUDIO"}(kt||(kt={})),lt.util.setEnumType(kt,"livekit.TrackSource",[{no:0,name:"UNKNOWN"},{no:1,name:"CAMERA"},{no:2,name:"MICROPHONE"},{no:3,name:"SCREEN_SHARE"},{no:4,name:"SCREEN_SHARE_AUDIO"}]),function(e){e[e.LOW=0]="LOW",e[e.MEDIUM=1]="MEDIUM",e[e.HIGH=2]="HIGH",e[e.OFF=3]="OFF"}(gt||(gt={})),lt.util.setEnumType(gt,"livekit.VideoQuality",[{no:0,name:"LOW"},{no:1,name:"MEDIUM"},{no:2,name:"HIGH"},{no:3,name:"OFF"}]),function(e){e[e.POOR=0]="POOR",e[e.GOOD=1]="GOOD",e[e.EXCELLENT=2]="EXCELLENT"}(yt||(yt={})),lt.util.setEnumType(yt,"livekit.ConnectionQuality",[{no:0,name:"POOR"},{no:1,name:"GOOD"},{no:2,name:"EXCELLENT"}]),function(e){e[e.UNSET=0]="UNSET",e[e.DISABLED=1]="DISABLED",e[e.ENABLED=2]="ENABLED"}(bt||(bt={})),lt.util.setEnumType(bt,"livekit.ClientConfigSetting",[{no:0,name:"UNSET"},{no:1,name:"DISABLED"},{no:2,name:"ENABLED"}]),function(e){e[e.UNKNOWN_REASON=0]="UNKNOWN_REASON",e[e.CLIENT_INITIATED=1]="CLIENT_INITIATED",e[e.DUPLICATE_IDENTITY=2]="DUPLICATE_IDENTITY",e[e.SERVER_SHUTDOWN=3]="SERVER_SHUTDOWN",e[e.PARTICIPANT_REMOVED=4]="PARTICIPANT_REMOVED",e[e.ROOM_DELETED=5]="ROOM_DELETED",e[e.STATE_MISMATCH=6]="STATE_MISMATCH",e[e.JOIN_FAILURE=7]="JOIN_FAILURE"}(St||(St={})),lt.util.setEnumType(St,"livekit.DisconnectReason",[{no:0,name:"UNKNOWN_REASON"},{no:1,name:"CLIENT_INITIATED"},{no:2,name:"DUPLICATE_IDENTITY"},{no:3,name:"SERVER_SHUTDOWN"},{no:4,name:"PARTICIPANT_REMOVED"},{no:5,name:"ROOM_DELETED"},{no:6,name:"STATE_MISMATCH"},{no:7,name:"JOIN_FAILURE"}]),function(e){e[e.RR_UNKNOWN=0]="RR_UNKNOWN",e[e.RR_SIGNAL_DISCONNECTED=1]="RR_SIGNAL_DISCONNECTED",e[e.RR_PUBLISHER_FAILED=2]="RR_PUBLISHER_FAILED",e[e.RR_SUBSCRIBER_FAILED=3]="RR_SUBSCRIBER_FAILED",e[e.RR_SWITCH_CANDIDATE=4]="RR_SWITCH_CANDIDATE"}(wt||(wt={})),lt.util.setEnumType(wt,"livekit.ReconnectReason",[{no:0,name:"RR_UNKNOWN"},{no:1,name:"RR_SIGNAL_DISCONNECTED"},{no:2,name:"RR_PUBLISHER_FAILED"},{no:3,name:"RR_SUBSCRIBER_FAILED"},{no:4,name:"RR_SWITCH_CANDIDATE"}]),function(e){e[e.SE_UNKNOWN=0]="SE_UNKNOWN",e[e.SE_CODEC_UNSUPPORTED=1]="SE_CODEC_UNSUPPORTED",e[e.SE_TRACK_NOTFOUND=2]="SE_TRACK_NOTFOUND"}(Tt||(Tt={})),lt.util.setEnumType(Tt,"livekit.SubscriptionError",[{no:0,name:"SE_UNKNOWN"},{no:1,name:"SE_CODEC_UNSUPPORTED"},{no:2,name:"SE_TRACK_NOTFOUND"}]);var xt=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).sid="",r.name="",r.emptyTimeout=0,r.maxParticipants=0,r.creationTime=pe.zero,r.turnPassword="",r.enabledCodecs=[],r.metadata="",r.numParticipants=0,r.numPublishers=0,r.activeRecording=!1,lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();xt.runtime=lt,xt.typeName="livekit.Room",xt.fields=lt.util.newFieldList((function(){return[{no:1,name:"sid",kind:"scalar",T:9},{no:2,name:"name",kind:"scalar",T:9},{no:3,name:"empty_timeout",kind:"scalar",T:13},{no:4,name:"max_participants",kind:"scalar",T:13},{no:5,name:"creation_time",kind:"scalar",T:3},{no:6,name:"turn_password",kind:"scalar",T:9},{no:7,name:"enabled_codecs",kind:"message",T:Pt,repeated:!0},{no:8,name:"metadata",kind:"scalar",T:9},{no:9,name:"num_participants",kind:"scalar",T:13},{no:11,name:"num_publishers",kind:"scalar",T:13},{no:10,name:"active_recording",kind:"scalar",T:8}]}));var Pt=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).mime="",r.fmtpLine="",lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Pt.runtime=lt,Pt.typeName="livekit.Codec",Pt.fields=lt.util.newFieldList((function(){return[{no:1,name:"mime",kind:"scalar",T:9},{no:2,name:"fmtp_line",kind:"scalar",T:9}]})),lt.util.newFieldList((function(){return[{no:1,name:"enabled",kind:"scalar",T:8},{no:2,name:"min",kind:"scalar",T:13},{no:3,name:"max",kind:"scalar",T:13}]}));var Et=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).canSubscribe=!1,r.canPublish=!1,r.canPublishData=!1,r.canPublishSources=[],r.hidden=!1,r.recorder=!1,r.canUpdateMetadata=!1,lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Et.runtime=lt,Et.typeName="livekit.ParticipantPermission",Et.fields=lt.util.newFieldList((function(){return[{no:1,name:"can_subscribe",kind:"scalar",T:8},{no:2,name:"can_publish",kind:"scalar",T:8},{no:3,name:"can_publish_data",kind:"scalar",T:8},{no:9,name:"can_publish_sources",kind:"enum",T:lt.getEnumType(kt),repeated:!0},{no:7,name:"hidden",kind:"scalar",T:8},{no:8,name:"recorder",kind:"scalar",T:8},{no:10,name:"can_update_metadata",kind:"scalar",T:8}]}));var Rt,It,Dt=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).sid="",r.identity="",r.state=Rt.JOINING,r.tracks=[],r.metadata="",r.joinedAt=pe.zero,r.name="",r.version=0,r.region="",r.isPublisher=!1,lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Dt.runtime=lt,Dt.typeName="livekit.ParticipantInfo",Dt.fields=lt.util.newFieldList((function(){return[{no:1,name:"sid",kind:"scalar",T:9},{no:2,name:"identity",kind:"scalar",T:9},{no:3,name:"state",kind:"enum",T:lt.getEnumType(Rt)},{no:4,name:"tracks",kind:"message",T:Ot,repeated:!0},{no:5,name:"metadata",kind:"scalar",T:9},{no:6,name:"joined_at",kind:"scalar",T:3},{no:9,name:"name",kind:"scalar",T:9},{no:10,name:"version",kind:"scalar",T:13},{no:11,name:"permission",kind:"message",T:Et},{no:12,name:"region",kind:"scalar",T:9},{no:13,name:"is_publisher",kind:"scalar",T:8}]})),function(e){e[e.JOINING=0]="JOINING",e[e.JOINED=1]="JOINED",e[e.ACTIVE=2]="ACTIVE",e[e.DISCONNECTED=3]="DISCONNECTED"}(Rt||(Rt={})),lt.util.setEnumType(Rt,"livekit.ParticipantInfo.State",[{no:0,name:"JOINING"},{no:1,name:"JOINED"},{no:2,name:"ACTIVE"},{no:3,name:"DISCONNECTED"}]),lt.util.newFieldList((function(){return[]})),function(e){e[e.NONE=0]="NONE",e[e.GCM=1]="GCM",e[e.CUSTOM=2]="CUSTOM"}(It||(It={})),lt.util.setEnumType(It,"livekit.Encryption.Type",[{no:0,name:"NONE"},{no:1,name:"GCM"},{no:2,name:"CUSTOM"}]);var Nt=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).mimeType="",r.mid="",r.cid="",r.layers=[],lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Nt.runtime=lt,Nt.typeName="livekit.SimulcastCodecInfo",Nt.fields=lt.util.newFieldList((function(){return[{no:1,name:"mime_type",kind:"scalar",T:9},{no:2,name:"mid",kind:"scalar",T:9},{no:3,name:"cid",kind:"scalar",T:9},{no:4,name:"layers",kind:"message",T:_t,repeated:!0}]}));var Ot=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).sid="",r.type=vt.AUDIO,r.name="",r.muted=!1,r.width=0,r.height=0,r.simulcast=!1,r.disableDtx=!1,r.source=kt.UNKNOWN,r.layers=[],r.mimeType="",r.mid="",r.codecs=[],r.stereo=!1,r.disableRed=!1,r.encryption=It.NONE,r.stream="",lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Ot.runtime=lt,Ot.typeName="livekit.TrackInfo",Ot.fields=lt.util.newFieldList((function(){return[{no:1,name:"sid",kind:"scalar",T:9},{no:2,name:"type",kind:"enum",T:lt.getEnumType(vt)},{no:3,name:"name",kind:"scalar",T:9},{no:4,name:"muted",kind:"scalar",T:8},{no:5,name:"width",kind:"scalar",T:13},{no:6,name:"height",kind:"scalar",T:13},{no:7,name:"simulcast",kind:"scalar",T:8},{no:8,name:"disable_dtx",kind:"scalar",T:8},{no:9,name:"source",kind:"enum",T:lt.getEnumType(kt)},{no:10,name:"layers",kind:"message",T:_t,repeated:!0},{no:11,name:"mime_type",kind:"scalar",T:9},{no:12,name:"mid",kind:"scalar",T:9},{no:13,name:"codecs",kind:"message",T:Nt,repeated:!0},{no:14,name:"stereo",kind:"scalar",T:8},{no:15,name:"disable_red",kind:"scalar",T:8},{no:16,name:"encryption",kind:"enum",T:lt.getEnumType(It)},{no:17,name:"stream",kind:"scalar",T:9}]}));var _t=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).quality=gt.LOW,r.width=0,r.height=0,r.bitrate=0,r.ssrc=0,lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();_t.runtime=lt,_t.typeName="livekit.VideoLayer",_t.fields=lt.util.newFieldList((function(){return[{no:1,name:"quality",kind:"enum",T:lt.getEnumType(gt)},{no:2,name:"width",kind:"scalar",T:13},{no:3,name:"height",kind:"scalar",T:13},{no:4,name:"bitrate",kind:"scalar",T:13},{no:5,name:"ssrc",kind:"scalar",T:13}]}));var Mt,Lt=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).kind=Mt.RELIABLE,r.value={case:void 0},lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Lt.runtime=lt,Lt.typeName="livekit.DataPacket",Lt.fields=lt.util.newFieldList((function(){return[{no:1,name:"kind",kind:"enum",T:lt.getEnumType(Mt)},{no:2,name:"user",kind:"message",T:Bt,oneof:"value"},{no:3,name:"speaker",kind:"message",T:At,oneof:"value"}]})),function(e){e[e.RELIABLE=0]="RELIABLE",e[e.LOSSY=1]="LOSSY"}(Mt||(Mt={})),lt.util.setEnumType(Mt,"livekit.DataPacket.Kind",[{no:0,name:"RELIABLE"},{no:1,name:"LOSSY"}]);var At=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).speakers=[],lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();At.runtime=lt,At.typeName="livekit.ActiveSpeakerUpdate",At.fields=lt.util.newFieldList((function(){return[{no:1,name:"speakers",kind:"message",T:Ut,repeated:!0}]}));var Ut=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).sid="",r.level=0,r.active=!1,lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Ut.runtime=lt,Ut.typeName="livekit.SpeakerInfo",Ut.fields=lt.util.newFieldList((function(){return[{no:1,name:"sid",kind:"scalar",T:9},{no:2,name:"level",kind:"scalar",T:2},{no:3,name:"active",kind:"scalar",T:8}]}));var Bt=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).participantSid="",r.participantIdentity="",r.payload=new Uint8Array(0),r.destinationSids=[],r.destinationIdentities=[],lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Bt.runtime=lt,Bt.typeName="livekit.UserPacket",Bt.fields=lt.util.newFieldList((function(){return[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:5,name:"participant_identity",kind:"scalar",T:9},{no:2,name:"payload",kind:"scalar",T:12},{no:3,name:"destination_sids",kind:"scalar",T:9,repeated:!0},{no:6,name:"destination_identities",kind:"scalar",T:9,repeated:!0},{no:4,name:"topic",kind:"scalar",T:9,opt:!0}]}));var Ft=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).participantSid="",r.trackSids=[],lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Ft.runtime=lt,Ft.typeName="livekit.ParticipantTracks",Ft.fields=lt.util.newFieldList((function(){return[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:2,name:"track_sids",kind:"scalar",T:9,repeated:!0}]}));var Jt,jt=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).edition=Jt.Standard,r.version="",r.protocol=0,r.region="",r.nodeId="",r.debugInfo="",lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();jt.runtime=lt,jt.typeName="livekit.ServerInfo",jt.fields=lt.util.newFieldList((function(){return[{no:1,name:"edition",kind:"enum",T:lt.getEnumType(Jt)},{no:2,name:"version",kind:"scalar",T:9},{no:3,name:"protocol",kind:"scalar",T:5},{no:4,name:"region",kind:"scalar",T:9},{no:5,name:"node_id",kind:"scalar",T:9},{no:6,name:"debug_info",kind:"scalar",T:9}]})),function(e){e[e.Standard=0]="Standard",e[e.Cloud=1]="Cloud"}(Jt||(Jt={})),lt.util.setEnumType(Jt,"livekit.ServerInfo.Edition",[{no:0,name:"Standard"},{no:1,name:"Cloud"}]);var qt,Vt=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).sdk=qt.UNKNOWN,r.version="",r.protocol=0,r.os="",r.osVersion="",r.deviceModel="",r.browser="",r.browserVersion="",r.address="",r.network="",lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Vt.runtime=lt,Vt.typeName="livekit.ClientInfo",Vt.fields=lt.util.newFieldList((function(){return[{no:1,name:"sdk",kind:"enum",T:lt.getEnumType(qt)},{no:2,name:"version",kind:"scalar",T:9},{no:3,name:"protocol",kind:"scalar",T:5},{no:4,name:"os",kind:"scalar",T:9},{no:5,name:"os_version",kind:"scalar",T:9},{no:6,name:"device_model",kind:"scalar",T:9},{no:7,name:"browser",kind:"scalar",T:9},{no:8,name:"browser_version",kind:"scalar",T:9},{no:9,name:"address",kind:"scalar",T:9},{no:10,name:"network",kind:"scalar",T:9}]})),function(e){e[e.UNKNOWN=0]="UNKNOWN",e[e.JS=1]="JS",e[e.SWIFT=2]="SWIFT",e[e.ANDROID=3]="ANDROID",e[e.FLUTTER=4]="FLUTTER",e[e.GO=5]="GO",e[e.UNITY=6]="UNITY",e[e.REACT_NATIVE=7]="REACT_NATIVE",e[e.RUST=8]="RUST",e[e.PYTHON=9]="PYTHON",e[e.CPP=10]="CPP"}(qt||(qt={})),lt.util.setEnumType(qt,"livekit.ClientInfo.SDK",[{no:0,name:"UNKNOWN"},{no:1,name:"JS"},{no:2,name:"SWIFT"},{no:3,name:"ANDROID"},{no:4,name:"FLUTTER"},{no:5,name:"GO"},{no:6,name:"UNITY"},{no:7,name:"REACT_NATIVE"},{no:8,name:"RUST"},{no:9,name:"PYTHON"},{no:10,name:"CPP"}]);var Ht=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).resumeConnection=bt.UNSET,r.forceRelay=bt.UNSET,lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Ht.runtime=lt,Ht.typeName="livekit.ClientConfiguration",Ht.fields=lt.util.newFieldList((function(){return[{no:1,name:"video",kind:"message",T:Wt},{no:2,name:"screen",kind:"message",T:Wt},{no:3,name:"resume_connection",kind:"enum",T:lt.getEnumType(bt)},{no:4,name:"disabled_codecs",kind:"message",T:Gt},{no:5,name:"force_relay",kind:"enum",T:lt.getEnumType(bt)}]}));var Wt=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).hardwareEncoder=bt.UNSET,lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Wt.runtime=lt,Wt.typeName="livekit.VideoConfiguration",Wt.fields=lt.util.newFieldList((function(){return[{no:1,name:"hardware_encoder",kind:"enum",T:lt.getEnumType(bt)}]}));var Gt=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).codecs=[],r.publish=[],lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Gt.runtime=lt,Gt.typeName="livekit.DisabledCodecs",Gt.fields=lt.util.newFieldList((function(){return[{no:1,name:"codecs",kind:"message",T:Pt,repeated:!0},{no:2,name:"publish",kind:"message",T:Pt,repeated:!0}]}));var Kt=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).duration=0,r.startTimestamp=pe.zero,r.endTimestamp=pe.zero,r.rtpClockTicks=pe.zero,r.driftSamples=pe.zero,r.driftMs=0,r.clockRate=0,lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Kt.runtime=lt,Kt.typeName="livekit.RTPDrift",Kt.fields=lt.util.newFieldList((function(){return[{no:1,name:"start_time",kind:"message",T:Ct},{no:2,name:"end_time",kind:"message",T:Ct},{no:3,name:"duration",kind:"scalar",T:1},{no:4,name:"start_timestamp",kind:"scalar",T:4},{no:5,name:"end_timestamp",kind:"scalar",T:4},{no:6,name:"rtp_clock_ticks",kind:"scalar",T:4},{no:7,name:"drift_samples",kind:"scalar",T:3},{no:8,name:"drift_ms",kind:"scalar",T:1},{no:9,name:"clock_rate",kind:"scalar",T:1}]})),lt.util.newFieldList((function(){return[{no:1,name:"start_time",kind:"message",T:Ct},{no:2,name:"end_time",kind:"message",T:Ct},{no:3,name:"duration",kind:"scalar",T:1},{no:4,name:"packets",kind:"scalar",T:13},{no:5,name:"packet_rate",kind:"scalar",T:1},{no:6,name:"bytes",kind:"scalar",T:4},{no:39,name:"header_bytes",kind:"scalar",T:4},{no:7,name:"bitrate",kind:"scalar",T:1},{no:8,name:"packets_lost",kind:"scalar",T:13},{no:9,name:"packet_loss_rate",kind:"scalar",T:1},{no:10,name:"packet_loss_percentage",kind:"scalar",T:2},{no:11,name:"packets_duplicate",kind:"scalar",T:13},{no:12,name:"packet_duplicate_rate",kind:"scalar",T:1},{no:13,name:"bytes_duplicate",kind:"scalar",T:4},{no:40,name:"header_bytes_duplicate",kind:"scalar",T:4},{no:14,name:"bitrate_duplicate",kind:"scalar",T:1},{no:15,name:"packets_padding",kind:"scalar",T:13},{no:16,name:"packet_padding_rate",kind:"scalar",T:1},{no:17,name:"bytes_padding",kind:"scalar",T:4},{no:41,name:"header_bytes_padding",kind:"scalar",T:4},{no:18,name:"bitrate_padding",kind:"scalar",T:1},{no:19,name:"packets_out_of_order",kind:"scalar",T:13},{no:20,name:"frames",kind:"scalar",T:13},{no:21,name:"frame_rate",kind:"scalar",T:1},{no:22,name:"jitter_current",kind:"scalar",T:1},{no:23,name:"jitter_max",kind:"scalar",T:1},{no:24,name:"gap_histogram",kind:"map",K:5,V:{kind:"scalar",T:13}},{no:25,name:"nacks",kind:"scalar",T:13},{no:37,name:"nack_acks",kind:"scalar",T:13},{no:26,name:"nack_misses",kind:"scalar",T:13},{no:38,name:"nack_repeated",kind:"scalar",T:13},{no:27,name:"plis",kind:"scalar",T:13},{no:28,name:"last_pli",kind:"message",T:Ct},{no:29,name:"firs",kind:"scalar",T:13},{no:30,name:"last_fir",kind:"message",T:Ct},{no:31,name:"rtt_current",kind:"scalar",T:13},{no:32,name:"rtt_max",kind:"scalar",T:13},{no:33,name:"key_frames",kind:"scalar",T:13},{no:34,name:"last_key_frame",kind:"message",T:Ct},{no:35,name:"layer_lock_plis",kind:"scalar",T:13},{no:36,name:"last_layer_lock_pli",kind:"message",T:Ct},{no:44,name:"packet_drift",kind:"message",T:Kt},{no:45,name:"report_drift",kind:"message",T:Kt}]})),lt.util.newFieldList((function(){return[{no:1,name:"unix_micro",kind:"scalar",T:3},{no:2,name:"ticks",kind:"scalar",T:5}]}));var zt=7e3,Qt=[0,300,1200,2700,4800,zt,zt,zt,zt,zt],Yt=function(){function e(t){s(this,e),this._retryDelays=void 0!==t?g(t):Qt}return u(e,[{key:"nextRetryDelayInMs",value:function(e){if(e.retryCount>=this._retryDelays.length)return null;var t=this._retryDelays[e.retryCount];return e.retryCount>1?t+1e3*Math.random():t}}]),e}();function Xt(e,t,n,r){return new(n||(n=Promise))((function(i,a){function o(e){try{c(r.next(e))}catch(e){a(e)}}function s(e){try{c(r.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,s)}c((r=r.apply(e,t||[])).next())}))}function Zt(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function $t(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=Zt(e),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(n){t[n]=e[n]&&function(t){return new Promise((function(r,i){(function(e,t,n,r){Promise.resolve(r).then((function(t){e({value:t,done:n})}),t)})(r,i,(t=e[n](t)).done,t.value)}))}}}"function"==typeof SuppressedError&&SuppressedError;var en,tn={exports:{}},nn="object"===("undefined"==typeof Reflect?"undefined":i(Reflect))?Reflect:null,rn=nn&&"function"==typeof nn.apply?nn.apply:function(e,t,n){return Function.prototype.apply.call(e,t,n)};en=nn&&"function"==typeof nn.ownKeys?nn.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var an=Number.isNaN||function(e){return e!=e};function on(){on.init.call(this)}tn.exports=on,tn.exports.once=function(e,t){return new Promise((function(n,r){function i(n){e.removeListener(t,a),r(n)}function a(){"function"==typeof e.removeListener&&e.removeListener("error",i),n([].slice.call(arguments))}vn(e,t,a,{once:!0}),"error"!==t&&function(e,t,n){"function"==typeof e.on&&vn(e,"error",t,n)}(e,i,{once:!0})}))},on.EventEmitter=on,on.prototype._events=void 0,on.prototype._eventsCount=0,on.prototype._maxListeners=void 0;var sn=10;function cn(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+i(e))}function un(e){return void 0===e._maxListeners?on.defaultMaxListeners:e._maxListeners}function dn(e,t,n,r){var i,a,o;if(cn(n),void 0===(a=e._events)?(a=e._events=Object.create(null),e._eventsCount=0):(void 0!==a.newListener&&(e.emit("newListener",t,n.listener?n.listener:n),a=e._events),o=a[t]),void 0===o)o=a[t]=n,++e._eventsCount;else if("function"==typeof o?o=a[t]=r?[n,o]:[o,n]:r?o.unshift(n):o.push(n),(i=un(e))>0&&o.length>i&&!o.warned){o.warned=!0;var s=new Error("Possible EventEmitter memory leak detected. "+o.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");s.name="MaxListenersExceededWarning",s.emitter=e,s.type=t,s.count=o.length,console&&console.warn}return e}function ln(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function hn(e,t,n){var r={fired:!1,wrapFn:void 0,target:e,type:t,listener:n},i=ln.bind(r);return i.listener=n,r.wrapFn=i,i}function fn(e,t,n){var r=e._events;if(void 0===r)return[];var i=r[t];return void 0===i?[]:"function"==typeof i?n?[i.listener||i]:[i]:n?function(e){for(var t=new Array(e.length),n=0;n<t.length;++n)t[n]=e[n].listener||e[n];return t}(i):mn(i,i.length)}function pn(e){var t=this._events;if(void 0!==t){var n=t[e];if("function"==typeof n)return 1;if(void 0!==n)return n.length}return 0}function mn(e,t){for(var n=new Array(t),r=0;t>r;++r)n[r]=e[r];return n}function vn(e,t,n,r){if("function"==typeof e.on)r.once?e.once(t,n):e.on(t,n);else{if("function"!=typeof e.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+i(e));e.addEventListener(t,(function i(a){r.once&&e.removeEventListener(t,i),n(a)}))}}Object.defineProperty(on,"defaultMaxListeners",{enumerable:!0,get:function(){return sn},set:function(e){if("number"!=typeof e||0>e||an(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");sn=e}}),on.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},on.prototype.setMaxListeners=function(e){if("number"!=typeof e||0>e||an(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},on.prototype.getMaxListeners=function(){return un(this)},on.prototype.emit=function(e){for(var t=[],n=1;arguments.length>n;n++)t.push(arguments[n]);var r="error"===e,i=this._events;if(void 0!==i)r=r&&void 0===i.error;else if(!r)return!1;if(r){var a;if(t.length>0&&(a=t[0]),a instanceof Error)throw a;var o=new Error("Unhandled error."+(a?" ("+a.message+")":""));throw o.context=a,o}var s=i[e];if(void 0===s)return!1;if("function"==typeof s)rn(s,this,t);else{var c=s.length,u=mn(s,c);for(n=0;c>n;++n)rn(u[n],this,t)}return!0},on.prototype.addListener=function(e,t){return dn(this,e,t,!1)},on.prototype.on=on.prototype.addListener,on.prototype.prependListener=function(e,t){return dn(this,e,t,!0)},on.prototype.once=function(e,t){return cn(t),this.on(e,hn(this,e,t)),this},on.prototype.prependOnceListener=function(e,t){return cn(t),this.prependListener(e,hn(this,e,t)),this},on.prototype.removeListener=function(e,t){var n,r,i,a,o;if(cn(t),void 0===(r=this._events))return this;if(void 0===(n=r[e]))return this;if(n===t||n.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete r[e],r.removeListener&&this.emit("removeListener",e,n.listener||t));else if("function"!=typeof n){for(i=-1,a=n.length-1;a>=0;a--)if(n[a]===t||n[a].listener===t){o=n[a].listener,i=a;break}if(0>i)return this;0===i?n.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(n,i),1===n.length&&(r[e]=n[0]),void 0!==r.removeListener&&this.emit("removeListener",e,o||t)}return this},on.prototype.off=on.prototype.removeListener,on.prototype.removeAllListeners=function(e){var t,n,r;if(void 0===(n=this._events))return this;if(void 0===n.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==n[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete n[e]),this;if(0===arguments.length){var i,a=Object.keys(n);for(r=0;r<a.length;++r)"removeListener"!==(i=a[r])&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=n[e]))this.removeListener(e,t);else if(void 0!==t)for(r=t.length-1;r>=0;r--)this.removeListener(e,t[r]);return this},on.prototype.listeners=function(e){return fn(this,e,!0)},on.prototype.rawListeners=function(e){return fn(this,e,!1)},on.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):pn.call(e,t)},on.prototype.listenerCount=pn,on.prototype.eventNames=function(){return this._eventsCount>0?en(this._events):[]};var kn=tn.exports,gn=!0;function yn(e,t,n){var r=e.match(t);return r&&r.length>=n&&parseInt(r[n],10)}function bn(e,t,n){if(e.RTCPeerConnection){var r=e.RTCPeerConnection.prototype,i=r.addEventListener;r.addEventListener=function(e,r){if(e!==t)return i.apply(this,arguments);var a=function(e){var t=n(e);t&&(r.handleEvent?r.handleEvent(t):r(t))};return this._eventMap=this._eventMap||{},this._eventMap[t]||(this._eventMap[t]=new Map),this._eventMap[t].set(r,a),i.apply(this,[e,a])};var a=r.removeEventListener;r.removeEventListener=function(e,n){if(e!==t||!this._eventMap||!this._eventMap[t])return a.apply(this,arguments);if(!this._eventMap[t].has(n))return a.apply(this,arguments);var r=this._eventMap[t].get(n);return this._eventMap[t].delete(n),0===this._eventMap[t].size&&delete this._eventMap[t],0===Object.keys(this._eventMap).length&&delete this._eventMap,a.apply(this,[e,r])},Object.defineProperty(r,"on"+t,{get:function(){return this["_on"+t]},set:function(e){this["_on"+t]&&(this.removeEventListener(t,this["_on"+t]),delete this["_on"+t]),e&&this.addEventListener(t,this["_on"+t]=e)},enumerable:!0,configurable:!0})}}function Sn(e){return"boolean"!=typeof e?new Error("Argument type: "+i(e)+". Please use a boolean."):(gn=e,e?"adapter.js logging disabled":"adapter.js logging enabled")}function wn(e){return"boolean"!=typeof e?new Error("Argument type: "+i(e)+". Please use a boolean."):(!e,"adapter.js deprecation warnings "+(e?"disabled":"enabled"))}function Tn(){if("object"===("undefined"==typeof window?"undefined":i(window))){if(gn)return;"undefined"!=typeof console&&console.log}}function Cn(e){return"[object Object]"===Object.prototype.toString.call(e)}function xn(e){return Cn(e)?Object.keys(e).reduce((function(t,n){var r=Cn(e[n]),i=r?xn(e[n]):e[n],a=r&&!Object.keys(i).length;return void 0===i||a?t:Object.assign(t,d({},n,i))}),{}):e}function Pn(e,t,n){t&&!n.has(t.id)&&(n.set(t.id,t),Object.keys(t).forEach((function(r){r.endsWith("Id")?Pn(e,e.get(t[r]),n):r.endsWith("Ids")&&t[r].forEach((function(t){Pn(e,e.get(t),n)}))})))}function En(e,t,n){var r=n?"outbound-rtp":"inbound-rtp",i=new Map;if(null===t)return i;var a=[];return e.forEach((function(e){"track"===e.type&&e.trackIdentifier===t.id&&a.push(e)})),a.forEach((function(t){e.forEach((function(n){n.type===r&&n.trackId===t.id&&Pn(e,n,i)}))})),i}var Rn=Tn;function In(e,t){var n=e&&e.navigator;if(n.mediaDevices){var r=function(e){if("object"!==i(e)||e.mandatory||e.optional)return e;var t={};return Object.keys(e).forEach((function(n){if("require"!==n&&"advanced"!==n&&"mediaSource"!==n){var r="object"===i(e[n])?e[n]:{ideal:e[n]};void 0!==r.exact&&"number"==typeof r.exact&&(r.min=r.max=r.exact);var a=function(e,t){return e?e+t.charAt(0).toUpperCase()+t.slice(1):"deviceId"===t?"sourceId":t};if(void 0!==r.ideal){t.optional=t.optional||[];var o={};"number"==typeof r.ideal?(o[a("min",n)]=r.ideal,t.optional.push(o),(o={})[a("max",n)]=r.ideal,t.optional.push(o)):(o[a("",n)]=r.ideal,t.optional.push(o))}void 0!==r.exact&&"number"!=typeof r.exact?(t.mandatory=t.mandatory||{},t.mandatory[a("",n)]=r.exact):["min","max"].forEach((function(e){void 0!==r[e]&&(t.mandatory=t.mandatory||{},t.mandatory[a(e,n)]=r[e])}))}})),e.advanced&&(t.optional=(t.optional||[]).concat(e.advanced)),t},a=function(e,a){if(t.version>=61)return a(e);if((e=JSON.parse(JSON.stringify(e)))&&"object"===i(e.audio)){var o=function(e,t,n){t in e&&!(n in e)&&(e[n]=e[t],delete e[t])};o((e=JSON.parse(JSON.stringify(e))).audio,"autoGainControl","googAutoGainControl"),o(e.audio,"noiseSuppression","googNoiseSuppression"),e.audio=r(e.audio)}if(e&&"object"===i(e.video)){var s=e.video.facingMode;s=s&&("object"===i(s)?s:{ideal:s});var c,u=66>t.version;if(s&&("user"===s.exact||"environment"===s.exact||"user"===s.ideal||"environment"===s.ideal)&&(!n.mediaDevices.getSupportedConstraints||!n.mediaDevices.getSupportedConstraints().facingMode||u))if(delete e.video.facingMode,"environment"===s.exact||"environment"===s.ideal?c=["back","rear"]:"user"!==s.exact&&"user"!==s.ideal||(c=["front"]),c)return n.mediaDevices.enumerateDevices().then((function(t){var n=(t=t.filter((function(e){return"videoinput"===e.kind}))).find((function(e){return c.some((function(t){return e.label.toLowerCase().includes(t)}))}));return!n&&t.length&&c.includes("back")&&(n=t[t.length-1]),n&&(e.video.deviceId=s.exact?{exact:n.deviceId}:{ideal:n.deviceId}),e.video=r(e.video),Rn("chrome: "+JSON.stringify(e)),a(e)}));e.video=r(e.video)}return Rn("chrome: "+JSON.stringify(e)),a(e)},o=function(e){return 64>t.version?{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[e.name]||e.name,message:e.message,constraint:e.constraint||e.constraintName,toString:function(){return this.name+(this.message&&": ")+this.message}}:e};if(n.getUserMedia=function(e,t,r){a(e,(function(e){n.webkitGetUserMedia(e,t,(function(e){r&&r(o(e))}))}))}.bind(n),n.mediaDevices.getUserMedia){var s=n.mediaDevices.getUserMedia.bind(n.mediaDevices);n.mediaDevices.getUserMedia=function(e){return a(e,(function(e){return s(e).then((function(t){if(e.audio&&!t.getAudioTracks().length||e.video&&!t.getVideoTracks().length)throw t.getTracks().forEach((function(e){e.stop()})),new DOMException("","NotFoundError");return t}),(function(e){return Promise.reject(o(e))}))}))}}}}function Dn(e){e.MediaStream=e.MediaStream||e.webkitMediaStream}function Nn(e){if("object"===i(e)&&e.RTCPeerConnection&&!("ontrack"in e.RTCPeerConnection.prototype)){Object.defineProperty(e.RTCPeerConnection.prototype,"ontrack",{get:function(){return this._ontrack},set:function(e){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=e)},enumerable:!0,configurable:!0});var t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){var n=this;return this._ontrackpoly||(this._ontrackpoly=function(t){t.stream.addEventListener("addtrack",(function(r){var i;i=e.RTCPeerConnection.prototype.getReceivers?n.getReceivers().find((function(e){return e.track&&e.track.id===r.track.id})):{track:r.track};var a=new Event("track");a.track=r.track,a.receiver=i,a.transceiver={receiver:i},a.streams=[t.stream],n.dispatchEvent(a)})),t.stream.getTracks().forEach((function(r){var i;i=e.RTCPeerConnection.prototype.getReceivers?n.getReceivers().find((function(e){return e.track&&e.track.id===r.id})):{track:r};var a=new Event("track");a.track=r,a.receiver=i,a.transceiver={receiver:i},a.streams=[t.stream],n.dispatchEvent(a)}))},this.addEventListener("addstream",this._ontrackpoly)),t.apply(this,arguments)}}else bn(e,"track",(function(e){return e.transceiver||Object.defineProperty(e,"transceiver",{value:{receiver:e.receiver}}),e}))}function On(e){if("object"===i(e)&&e.RTCPeerConnection&&!("getSenders"in e.RTCPeerConnection.prototype)&&"createDTMFSender"in e.RTCPeerConnection.prototype){var t=function(e,t){return{track:t,get dtmf(){return void 0===this._dtmf&&("audio"===t.kind?this._dtmf=e.createDTMFSender(t):this._dtmf=null),this._dtmf},_pc:e}};if(!e.RTCPeerConnection.prototype.getSenders){e.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};var n=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,r){var i=n.apply(this,arguments);return i||(i=t(this,e),this._senders.push(i)),i};var r=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){r.apply(this,arguments);var t=this._senders.indexOf(e);-1!==t&&this._senders.splice(t,1)}}var a=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){var n=this;this._senders=this._senders||[],a.apply(this,[e]),e.getTracks().forEach((function(e){n._senders.push(t(n,e))}))};var o=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){var t=this;this._senders=this._senders||[],o.apply(this,[e]),e.getTracks().forEach((function(e){var n=t._senders.find((function(t){return t.track===e}));n&&t._senders.splice(t._senders.indexOf(n),1)}))}}else if("object"===i(e)&&e.RTCPeerConnection&&"getSenders"in e.RTCPeerConnection.prototype&&"createDTMFSender"in e.RTCPeerConnection.prototype&&e.RTCRtpSender&&!("dtmf"in e.RTCRtpSender.prototype)){var s=e.RTCPeerConnection.prototype.getSenders;e.RTCPeerConnection.prototype.getSenders=function(){var e=this,t=s.apply(this,[]);return t.forEach((function(t){return t._pc=e})),t},Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get:function(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}}function _n(e){if(e.RTCPeerConnection){var t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){var e=this,n=Array.prototype.slice.call(arguments),r=n[0],i=n[1],a=n[2];if(arguments.length>0&&"function"==typeof r)return t.apply(this,arguments);if(0===t.length&&(0===arguments.length||"function"!=typeof r))return t.apply(this,[]);var o=function(e){var t={};return e.result().forEach((function(e){var n={id:e.id,timestamp:e.timestamp,type:{localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[e.type]||e.type};e.names().forEach((function(t){n[t]=e.stat(t)})),t[n.id]=n})),t},s=function(e){return new Map(Object.keys(e).map((function(t){return[t,e[t]]})))};if(arguments.length>=2){return t.apply(this,[function(e){i(s(o(e)))},r])}return new Promise((function(n,r){t.apply(e,[function(e){n(s(o(e)))},r])})).then(i,a)}}}function Mn(e){if("object"===i(e)&&e.RTCPeerConnection&&e.RTCRtpSender&&e.RTCRtpReceiver){if(!("getStats"in e.RTCRtpSender.prototype)){var t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){var e=this,n=t.apply(this,[]);return n.forEach((function(t){return t._pc=e})),n});var n=e.RTCPeerConnection.prototype.addTrack;n&&(e.RTCPeerConnection.prototype.addTrack=function(){var e=n.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){var e=this;return this._pc.getStats().then((function(t){return En(t,e.track,!0)}))}}if(!("getStats"in e.RTCRtpReceiver.prototype)){var r=e.RTCPeerConnection.prototype.getReceivers;r&&(e.RTCPeerConnection.prototype.getReceivers=function(){var e=this,t=r.apply(this,[]);return t.forEach((function(t){return t._pc=e})),t}),bn(e,"track",(function(e){return e.receiver._pc=e.srcElement,e})),e.RTCRtpReceiver.prototype.getStats=function(){var e=this;return this._pc.getStats().then((function(t){return En(t,e.track,!1)}))}}if("getStats"in e.RTCRtpSender.prototype&&"getStats"in e.RTCRtpReceiver.prototype){var a=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){if(arguments.length>0&&arguments[0]instanceof e.MediaStreamTrack){var t,n,r,i=arguments[0];return this.getSenders().forEach((function(e){e.track===i&&(t?r=!0:t=e)})),this.getReceivers().forEach((function(e){return e.track===i&&(n?r=!0:n=e),e.track===i})),r||t&&n?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):t?t.getStats():n?n.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return a.apply(this,arguments)}}}}function Ln(e){e.RTCPeerConnection.prototype.getLocalStreams=function(){var e=this;return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map((function(t){return e._shimmedLocalStreams[t][0]}))};var t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,n){if(!n)return t.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};var r=t.apply(this,arguments);return this._shimmedLocalStreams[n.id]?-1===this._shimmedLocalStreams[n.id].indexOf(r)&&this._shimmedLocalStreams[n.id].push(r):this._shimmedLocalStreams[n.id]=[n,r],r};var n=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){var t=this;this._shimmedLocalStreams=this._shimmedLocalStreams||{},e.getTracks().forEach((function(e){if(t.getSenders().find((function(t){return t.track===e})))throw new DOMException("Track already exists.","InvalidAccessError")}));var r=this.getSenders();n.apply(this,arguments);var i=this.getSenders().filter((function(e){return-1===r.indexOf(e)}));this._shimmedLocalStreams[e.id]=[e].concat(i)};var r=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[e.id],r.apply(this,arguments)};var i=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){var t=this;return this._shimmedLocalStreams=this._shimmedLocalStreams||{},e&&Object.keys(this._shimmedLocalStreams).forEach((function(n){var r=t._shimmedLocalStreams[n].indexOf(e);-1!==r&&t._shimmedLocalStreams[n].splice(r,1),1===t._shimmedLocalStreams[n].length&&delete t._shimmedLocalStreams[n]})),i.apply(this,arguments)}}function An(e,t){if(e.RTCPeerConnection){if(e.RTCPeerConnection.prototype.addTrack&&t.version>=65)return Ln(e);var n=e.RTCPeerConnection.prototype.getLocalStreams;e.RTCPeerConnection.prototype.getLocalStreams=function(){var e=this,t=n.apply(this);return this._reverseStreams=this._reverseStreams||{},t.map((function(t){return e._reverseStreams[t.id]}))};var r=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(t){var n=this;if(this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},t.getTracks().forEach((function(e){if(n.getSenders().find((function(t){return t.track===e})))throw new DOMException("Track already exists.","InvalidAccessError")})),!this._reverseStreams[t.id]){var i=new e.MediaStream(t.getTracks());this._streams[t.id]=i,this._reverseStreams[i.id]=t,t=i}r.apply(this,[t])};var i=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},i.apply(this,[this._streams[e.id]||e]),delete this._reverseStreams[this._streams[e.id]?this._streams[e.id].id:e.id],delete this._streams[e.id]},e.RTCPeerConnection.prototype.addTrack=function(t,n){var r=this;if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");var i=[].slice.call(arguments,1);if(1!==i.length||!i[0].getTracks().find((function(e){return e===t})))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");if(this.getSenders().find((function(e){return e.track===t})))throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{};var a=this._streams[n.id];if(a)a.addTrack(t),Promise.resolve().then((function(){r.dispatchEvent(new Event("negotiationneeded"))}));else{var o=new e.MediaStream([t]);this._streams[n.id]=o,this._reverseStreams[o.id]=n,this.addStream(o)}return this.getSenders().find((function(e){return e.track===t}))},["createOffer","createAnswer"].forEach((function(t){var n=e.RTCPeerConnection.prototype[t],r=d({},t,(function(){var e=this,t=arguments;return arguments.length&&"function"==typeof arguments[0]?n.apply(this,[function(n){var r=s(e,n);t[0].apply(null,[r])},function(e){t[1]&&t[1].apply(null,e)},arguments[2]]):n.apply(this,arguments).then((function(t){return s(e,t)}))}));e.RTCPeerConnection.prototype[t]=r[t]}));var a=e.RTCPeerConnection.prototype.setLocalDescription;e.RTCPeerConnection.prototype.setLocalDescription=function(){return arguments.length&&arguments[0].type?(arguments[0]=function(e,t){var n=t.sdp;return Object.keys(e._reverseStreams||[]).forEach((function(t){var r=e._reverseStreams[t],i=e._streams[r.id];n=n.replace(new RegExp(r.id,"g"),i.id)})),new RTCSessionDescription({type:t.type,sdp:n})}(this,arguments[0]),a.apply(this,arguments)):a.apply(this,arguments)};var o=Object.getOwnPropertyDescriptor(e.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(e.RTCPeerConnection.prototype,"localDescription",{get:function(){var e=o.get.apply(this);return""===e.type?e:s(this,e)}}),e.RTCPeerConnection.prototype.removeTrack=function(e){var t,n=this;if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!e._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(!(e._pc===this))throw new DOMException("Sender was not created by this connection.","InvalidAccessError");this._streams=this._streams||{},Object.keys(this._streams).forEach((function(r){n._streams[r].getTracks().find((function(t){return e.track===t}))&&(t=n._streams[r])})),t&&(1===t.getTracks().length?this.removeStream(this._reverseStreams[t.id]):t.removeTrack(e.track),this.dispatchEvent(new Event("negotiationneeded")))}}function s(e,t){var n=t.sdp;return Object.keys(e._reverseStreams||[]).forEach((function(t){var r=e._reverseStreams[t],i=e._streams[r.id];n=n.replace(new RegExp(i.id,"g"),r.id)})),new RTCSessionDescription({type:t.type,sdp:n})}}function Un(e,t){!e.RTCPeerConnection&&e.webkitRTCPeerConnection&&(e.RTCPeerConnection=e.webkitRTCPeerConnection),e.RTCPeerConnection&&53>t.version&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(t){var n=e.RTCPeerConnection.prototype[t],r=d({},t,(function(){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),n.apply(this,arguments)}));e.RTCPeerConnection.prototype[t]=r[t]}))}function Bn(e,t){bn(e,"negotiationneeded",(function(e){var n=e.target;if(!(72>t.version||n.getConfiguration&&"plan-b"===n.getConfiguration().sdpSemantics)||"stable"===n.signalingState)return e}))}var Fn=Object.freeze({__proto__:null,fixNegotiationNeeded:Bn,shimAddTrackRemoveTrack:An,shimAddTrackRemoveTrackWithNative:Ln,shimGetDisplayMedia:function(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&"function"==typeof t&&(e.navigator.mediaDevices.getDisplayMedia=function(n){return t(n).then((function(t){var r=n.video&&n.video.width,i=n.video&&n.video.height,a=n.video&&n.video.frameRate;return n.video={mandatory:{chromeMediaSource:"desktop",chromeMediaSourceId:t,maxFrameRate:a||3}},r&&(n.video.mandatory.maxWidth=r),i&&(n.video.mandatory.maxHeight=i),e.navigator.mediaDevices.getUserMedia(n)}))})},shimGetSendersWithDtmf:On,shimGetStats:_n,shimGetUserMedia:In,shimMediaStream:Dn,shimOnTrack:Nn,shimPeerConnection:Un,shimSenderReceiverGetStats:Mn});function Jn(e,t){var n=e&&e.navigator,r=e&&e.MediaStreamTrack;if(n.getUserMedia=function(e,t,r){n.mediaDevices.getUserMedia(e).then(t,r)},55>=t.version||!("autoGainControl"in n.mediaDevices.getSupportedConstraints())){var a=function(e,t,n){t in e&&!(n in e)&&(e[n]=e[t],delete e[t])},o=n.mediaDevices.getUserMedia.bind(n.mediaDevices);if(n.mediaDevices.getUserMedia=function(e){return"object"===i(e)&&"object"===i(e.audio)&&(e=JSON.parse(JSON.stringify(e)),a(e.audio,"autoGainControl","mozAutoGainControl"),a(e.audio,"noiseSuppression","mozNoiseSuppression")),o(e)},r&&r.prototype.getSettings){var s=r.prototype.getSettings;r.prototype.getSettings=function(){var e=s.apply(this,arguments);return a(e,"mozAutoGainControl","autoGainControl"),a(e,"mozNoiseSuppression","noiseSuppression"),e}}if(r&&r.prototype.applyConstraints){var c=r.prototype.applyConstraints;r.prototype.applyConstraints=function(e){return"audio"===this.kind&&"object"===i(e)&&(e=JSON.parse(JSON.stringify(e)),a(e,"autoGainControl","mozAutoGainControl"),a(e,"noiseSuppression","mozNoiseSuppression")),c.apply(this,[e])}}}}function jn(e){"object"===i(e)&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get:function(){return{receiver:this.receiver}}})}function qn(e,t){if("object"===i(e)&&(e.RTCPeerConnection||e.mozRTCPeerConnection)){!e.RTCPeerConnection&&e.mozRTCPeerConnection&&(e.RTCPeerConnection=e.mozRTCPeerConnection),53>t.version&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(t){var n=e.RTCPeerConnection.prototype[t],r=d({},t,(function(){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),n.apply(this,arguments)}));e.RTCPeerConnection.prototype[t]=r[t]}));var n={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},r=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){var e=Array.prototype.slice.call(arguments),i=e[0],a=e[1],o=e[2];return r.apply(this,[i||null]).then((function(e){if(53>t.version&&!a)try{e.forEach((function(e){e.type=n[e.type]||e.type}))}catch(t){if("TypeError"!==t.name)throw t;e.forEach((function(t,r){e.set(r,Object.assign({},t,{type:n[t.type]||t.type}))}))}return e})).then(a,o)}}}function Vn(e){if("object"===i(e)&&e.RTCPeerConnection&&e.RTCRtpSender&&(!e.RTCRtpSender||!("getStats"in e.RTCRtpSender.prototype))){var t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){var e=this,n=t.apply(this,[]);return n.forEach((function(t){return t._pc=e})),n});var n=e.RTCPeerConnection.prototype.addTrack;n&&(e.RTCPeerConnection.prototype.addTrack=function(){var e=n.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}}function Hn(e){if("object"===i(e)&&e.RTCPeerConnection&&e.RTCRtpSender&&(!e.RTCRtpSender||!("getStats"in e.RTCRtpReceiver.prototype))){var t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){var e=this,n=t.apply(this,[]);return n.forEach((function(t){return t._pc=e})),n}),bn(e,"track",(function(e){return e.receiver._pc=e.srcElement,e})),e.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}}}function Wn(e){e.RTCPeerConnection&&!("removeStream"in e.RTCPeerConnection.prototype)&&(e.RTCPeerConnection.prototype.removeStream=function(e){var t=this;this.getSenders().forEach((function(n){n.track&&e.getTracks().includes(n.track)&&t.removeTrack(n)}))})}function Gn(e){e.DataChannel&&!e.RTCDataChannel&&(e.RTCDataChannel=e.DataChannel)}function Kn(e){if("object"===i(e)&&e.RTCPeerConnection){var t=e.RTCPeerConnection.prototype.addTransceiver;t&&(e.RTCPeerConnection.prototype.addTransceiver=function(){this.setParametersPromises=[];var e=arguments[1]&&arguments[1].sendEncodings;void 0===e&&(e=[]);var n=(e=g(e)).length>0;n&&e.forEach((function(e){if("rid"in e){if(!/^[a-z0-9]{0,16}$/i.test(e.rid))throw new TypeError("Invalid RID value provided.")}if("scaleResolutionDownBy"in e&&parseFloat(e.scaleResolutionDownBy)<1)throw new RangeError("scale_resolution_down_by must be >= 1.0");if("maxFramerate"in e&&parseFloat(e.maxFramerate)<0)throw new RangeError("max_framerate must be >= 0.0")}));var r=t.apply(this,arguments);if(n){var i=r.sender,a=i.getParameters();(!("encodings"in a)||1===a.encodings.length&&0===Object.keys(a.encodings[0]).length)&&(a.encodings=e,i.sendEncodings=e,this.setParametersPromises.push(i.setParameters(a).then((function(){delete i.sendEncodings})).catch((function(){delete i.sendEncodings}))))}return r})}}function zn(e){if("object"===i(e)&&e.RTCRtpSender){var t=e.RTCRtpSender.prototype.getParameters;t&&(e.RTCRtpSender.prototype.getParameters=function(){var e=t.apply(this,arguments);return"encodings"in e||(e.encodings=[].concat(this.sendEncodings||[{}])),e})}}function Qn(e){if("object"===i(e)&&e.RTCPeerConnection){var t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(){var e=arguments,n=this;return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then((function(){return t.apply(n,e)})).finally((function(){n.setParametersPromises=[]})):t.apply(this,arguments)}}}function Yn(e){if("object"===i(e)&&e.RTCPeerConnection){var t=e.RTCPeerConnection.prototype.createAnswer;e.RTCPeerConnection.prototype.createAnswer=function(){var e=arguments,n=this;return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then((function(){return t.apply(n,e)})).finally((function(){n.setParametersPromises=[]})):t.apply(this,arguments)}}}var Xn=Object.freeze({__proto__:null,shimAddTransceiver:Kn,shimCreateAnswer:Yn,shimCreateOffer:Qn,shimGetDisplayMedia:function(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&(e.navigator.mediaDevices.getDisplayMedia=function(n){if(!n||!n.video){var r=new DOMException("getDisplayMedia without video constraints is undefined");return r.name="NotFoundError",r.code=8,Promise.reject(r)}return!0===n.video?n.video={mediaSource:t}:n.video.mediaSource=t,e.navigator.mediaDevices.getUserMedia(n)})},shimGetParameters:zn,shimGetUserMedia:Jn,shimOnTrack:jn,shimPeerConnection:qn,shimRTCDataChannel:Gn,shimReceiverGetStats:Hn,shimRemoveStream:Wn,shimSenderGetStats:Vn});function Zn(e){if("object"===i(e)&&e.RTCPeerConnection){if("getLocalStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),!("addStream"in e.RTCPeerConnection.prototype)){var t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addStream=function(e){var n=this;this._localStreams||(this._localStreams=[]),this._localStreams.includes(e)||this._localStreams.push(e),e.getAudioTracks().forEach((function(r){return t.call(n,r,e)})),e.getVideoTracks().forEach((function(r){return t.call(n,r,e)}))},e.RTCPeerConnection.prototype.addTrack=function(e){for(var n=this,r=arguments.length,i=new Array(r>1?r-1:0),a=1;r>a;a++)i[a-1]=arguments[a];return i&&i.forEach((function(e){n._localStreams?n._localStreams.includes(e)||n._localStreams.push(e):n._localStreams=[e]})),t.apply(this,arguments)}}"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(e){var t=this;this._localStreams||(this._localStreams=[]);var n=this._localStreams.indexOf(e);if(-1!==n){this._localStreams.splice(n,1);var r=e.getTracks();this.getSenders().forEach((function(e){r.includes(e.track)&&t.removeTrack(e)}))}})}}function $n(e){if("object"===i(e)&&e.RTCPeerConnection&&("getRemoteStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),!("onaddstream"in e.RTCPeerConnection.prototype))){Object.defineProperty(e.RTCPeerConnection.prototype,"onaddstream",{get:function(){return this._onaddstream},set:function(e){var t=this;this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=e),this.addEventListener("track",this._onaddstreampoly=function(e){e.streams.forEach((function(e){if(t._remoteStreams||(t._remoteStreams=[]),!t._remoteStreams.includes(e)){t._remoteStreams.push(e);var n=new Event("addstream");n.stream=e,t.dispatchEvent(n)}}))})}});var t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){var e=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(t){t.streams.forEach((function(t){if(e._remoteStreams||(e._remoteStreams=[]),0>e._remoteStreams.indexOf(t)){e._remoteStreams.push(t);var n=new Event("addstream");n.stream=t,e.dispatchEvent(n)}}))}),t.apply(e,arguments)}}}function er(e){if("object"===i(e)&&e.RTCPeerConnection){var t=e.RTCPeerConnection.prototype,n=t.createOffer,r=t.createAnswer,a=t.setLocalDescription,o=t.setRemoteDescription,s=t.addIceCandidate;t.createOffer=function(e,t){var r=2>arguments.length?arguments[0]:arguments[2],i=n.apply(this,[r]);return t?(i.then(e,t),Promise.resolve()):i},t.createAnswer=function(e,t){var n=2>arguments.length?arguments[0]:arguments[2],i=r.apply(this,[n]);return t?(i.then(e,t),Promise.resolve()):i};var c=function(e,t,n){var r=a.apply(this,[e]);return n?(r.then(t,n),Promise.resolve()):r};t.setLocalDescription=c,c=function(e,t,n){var r=o.apply(this,[e]);return n?(r.then(t,n),Promise.resolve()):r},t.setRemoteDescription=c,c=function(e,t,n){var r=s.apply(this,[e]);return n?(r.then(t,n),Promise.resolve()):r},t.addIceCandidate=c}}function tr(e){var t=e&&e.navigator;if(t.mediaDevices&&t.mediaDevices.getUserMedia){var n=t.mediaDevices,r=n.getUserMedia.bind(n);t.mediaDevices.getUserMedia=function(e){return r(nr(e))}}!t.getUserMedia&&t.mediaDevices&&t.mediaDevices.getUserMedia&&(t.getUserMedia=function(e,n,r){t.mediaDevices.getUserMedia(e).then(n,r)}.bind(t))}function nr(e){return e&&void 0!==e.video?Object.assign({},e,{video:xn(e.video)}):e}function rr(e){if(e.RTCPeerConnection){var t=e.RTCPeerConnection;e.RTCPeerConnection=function(e,n){if(e&&e.iceServers){for(var r=[],i=0;i<e.iceServers.length;i++){var a=e.iceServers[i];void 0===a.urls&&a.url?((a=JSON.parse(JSON.stringify(a))).urls=a.url,delete a.url,r.push(a)):r.push(e.iceServers[i])}e.iceServers=r}return new t(e,n)},e.RTCPeerConnection.prototype=t.prototype,"generateCertificate"in t&&Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get:function(){return t.generateCertificate}})}}function ir(e){"object"===i(e)&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get:function(){return{receiver:this.receiver}}})}function ar(e){var t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(e){if(e){void 0!==e.offerToReceiveAudio&&(e.offerToReceiveAudio=!!e.offerToReceiveAudio);var n=this.getTransceivers().find((function(e){return"audio"===e.receiver.track.kind}));!1===e.offerToReceiveAudio&&n?"sendrecv"===n.direction?n.setDirection?n.setDirection("sendonly"):n.direction="sendonly":"recvonly"===n.direction&&(n.setDirection?n.setDirection("inactive"):n.direction="inactive"):!0!==e.offerToReceiveAudio||n||this.addTransceiver("audio",{direction:"recvonly"}),void 0!==e.offerToReceiveVideo&&(e.offerToReceiveVideo=!!e.offerToReceiveVideo);var r=this.getTransceivers().find((function(e){return"video"===e.receiver.track.kind}));!1===e.offerToReceiveVideo&&r?"sendrecv"===r.direction?r.setDirection?r.setDirection("sendonly"):r.direction="sendonly":"recvonly"===r.direction&&(r.setDirection?r.setDirection("inactive"):r.direction="inactive"):!0!==e.offerToReceiveVideo||r||this.addTransceiver("video",{direction:"recvonly"})}return t.apply(this,arguments)}}function or(e){"object"!==i(e)||e.AudioContext||(e.AudioContext=e.webkitAudioContext)}var sr=Object.freeze({__proto__:null,shimAudioContext:or,shimCallbacksAPI:er,shimConstraints:nr,shimCreateOfferLegacy:ar,shimGetUserMedia:tr,shimLocalStreamsAPI:Zn,shimRTCIceServerUrls:rr,shimRemoteStreamsAPI:$n,shimTrackEventTransceiver:ir}),cr={exports:{}};!function(e){var t={generateIdentifier:function(){return Math.random().toString(36).substring(2,12)}};t.localCName=t.generateIdentifier(),t.splitLines=function(e){return e.trim().split("\n").map((function(e){return e.trim()}))},t.splitSections=function(e){return e.split("\nm=").map((function(e,t){return(t>0?"m="+e:e).trim()+"\r\n"}))},t.getDescription=function(e){var n=t.splitSections(e);return n&&n[0]},t.getMediaSections=function(e){var n=t.splitSections(e);return n.shift(),n},t.matchPrefix=function(e,n){return t.splitLines(e).filter((function(e){return 0===e.indexOf(n)}))},t.parseCandidate=function(e){for(var t,n={foundation:(t=0===e.indexOf("a=candidate:")?e.substring(12).split(" "):e.substring(10).split(" "))[0],component:{1:"rtp",2:"rtcp"}[t[1]]||t[1],protocol:t[2].toLowerCase(),priority:parseInt(t[3],10),ip:t[4],address:t[4],port:parseInt(t[5],10),type:t[7]},r=8;r<t.length;r+=2)switch(t[r]){case"raddr":n.relatedAddress=t[r+1];break;case"rport":n.relatedPort=parseInt(t[r+1],10);break;case"tcptype":n.tcpType=t[r+1];break;case"ufrag":n.ufrag=t[r+1],n.usernameFragment=t[r+1];break;default:void 0===n[t[r]]&&(n[t[r]]=t[r+1]);break}return n},t.writeCandidate=function(e){var t=[];t.push(e.foundation);var n=e.component;"rtp"===n?t.push(1):"rtcp"===n?t.push(2):t.push(n),t.push(e.protocol.toUpperCase()),t.push(e.priority),t.push(e.address||e.ip),t.push(e.port);var r=e.type;return t.push("typ"),t.push(r),"host"!==r&&e.relatedAddress&&e.relatedPort&&(t.push("raddr"),t.push(e.relatedAddress),t.push("rport"),t.push(e.relatedPort)),e.tcpType&&"tcp"===e.protocol.toLowerCase()&&(t.push("tcptype"),t.push(e.tcpType)),(e.usernameFragment||e.ufrag)&&(t.push("ufrag"),t.push(e.usernameFragment||e.ufrag)),"candidate:"+t.join(" ")},t.parseIceOptions=function(e){return e.substring(14).split(" ")},t.parseRtpMap=function(e){var t=e.substring(9).split(" "),n={payloadType:parseInt(t.shift(),10)};return t=t[0].split("/"),n.name=t[0],n.clockRate=parseInt(t[1],10),n.channels=3===t.length?parseInt(t[2],10):1,n.numChannels=n.channels,n},t.writeRtpMap=function(e){var t=e.payloadType;void 0!==e.preferredPayloadType&&(t=e.preferredPayloadType);var n=e.channels||e.numChannels||1;return"a=rtpmap:"+t+" "+e.name+"/"+e.clockRate+(1!==n?"/"+n:"")+"\r\n"},t.parseExtmap=function(e){var t=e.substring(9).split(" ");return{id:parseInt(t[0],10),direction:t[0].indexOf("/")>0?t[0].split("/")[1]:"sendrecv",uri:t[1],attributes:t.slice(2).join(" ")}},t.writeExtmap=function(e){return"a=extmap:"+(e.id||e.preferredId)+(e.direction&&"sendrecv"!==e.direction?"/"+e.direction:"")+" "+e.uri+(e.attributes?" "+e.attributes:"")+"\r\n"},t.parseFmtp=function(e){for(var t,n={},r=e.substring(e.indexOf(" ")+1).split(";"),i=0;i<r.length;i++)n[(t=r[i].trim().split("="))[0].trim()]=t[1];return n},t.writeFmtp=function(e){var t="",n=e.payloadType;if(void 0!==e.preferredPayloadType&&(n=e.preferredPayloadType),e.parameters&&Object.keys(e.parameters).length){var r=[];Object.keys(e.parameters).forEach((function(t){void 0!==e.parameters[t]?r.push(t+"="+e.parameters[t]):r.push(t)})),t+="a=fmtp:"+n+" "+r.join(";")+"\r\n"}return t},t.parseRtcpFb=function(e){var t=e.substring(e.indexOf(" ")+1).split(" ");return{type:t.shift(),parameter:t.join(" ")}},t.writeRtcpFb=function(e){var t="",n=e.payloadType;return void 0!==e.preferredPayloadType&&(n=e.preferredPayloadType),e.rtcpFeedback&&e.rtcpFeedback.length&&e.rtcpFeedback.forEach((function(e){t+="a=rtcp-fb:"+n+" "+e.type+(e.parameter&&e.parameter.length?" "+e.parameter:"")+"\r\n"})),t},t.parseSsrcMedia=function(e){var t=e.indexOf(" "),n={ssrc:parseInt(e.substring(7,t),10)},r=e.indexOf(":",t);return r>-1?(n.attribute=e.substring(t+1,r),n.value=e.substring(r+1)):n.attribute=e.substring(t+1),n},t.parseSsrcGroup=function(e){var t=e.substring(13).split(" ");return{semantics:t.shift(),ssrcs:t.map((function(e){return parseInt(e,10)}))}},t.getMid=function(e){var n=t.matchPrefix(e,"a=mid:")[0];if(n)return n.substring(6)},t.parseFingerprint=function(e){var t=e.substring(14).split(" ");return{algorithm:t[0].toLowerCase(),value:t[1].toUpperCase()}},t.getDtlsParameters=function(e,n){return{role:"auto",fingerprints:t.matchPrefix(e+n,"a=fingerprint:").map(t.parseFingerprint)}},t.writeDtlsParameters=function(e,t){var n="a=setup:"+t+"\r\n";return e.fingerprints.forEach((function(e){n+="a=fingerprint:"+e.algorithm+" "+e.value+"\r\n"})),n},t.parseCryptoLine=function(e){var t=e.substring(9).split(" ");return{tag:parseInt(t[0],10),cryptoSuite:t[1],keyParams:t[2],sessionParams:t.slice(3)}},t.writeCryptoLine=function(e){return"a=crypto:"+e.tag+" "+e.cryptoSuite+" "+("object"===i(e.keyParams)?t.writeCryptoKeyParams(e.keyParams):e.keyParams)+(e.sessionParams?" "+e.sessionParams.join(" "):"")+"\r\n"},t.parseCryptoKeyParams=function(e){if(0!==e.indexOf("inline:"))return null;var t=e.substring(7).split("|");return{keyMethod:"inline",keySalt:t[0],lifeTime:t[1],mkiValue:t[2]?t[2].split(":")[0]:void 0,mkiLength:t[2]?t[2].split(":")[1]:void 0}},t.writeCryptoKeyParams=function(e){return e.keyMethod+":"+e.keySalt+(e.lifeTime?"|"+e.lifeTime:"")+(e.mkiValue&&e.mkiLength?"|"+e.mkiValue+":"+e.mkiLength:"")},t.getCryptoParameters=function(e,n){return t.matchPrefix(e+n,"a=crypto:").map(t.parseCryptoLine)},t.getIceParameters=function(e,n){var r=t.matchPrefix(e+n,"a=ice-ufrag:")[0],i=t.matchPrefix(e+n,"a=ice-pwd:")[0];return r&&i?{usernameFragment:r.substring(12),password:i.substring(10)}:null},t.writeIceParameters=function(e){var t="a=ice-ufrag:"+e.usernameFragment+"\r\na=ice-pwd:"+e.password+"\r\n";return e.iceLite&&(t+="a=ice-lite\r\n"),t},t.parseRtpParameters=function(e){var n={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},r=t.splitLines(e)[0].split(" ");n.profile=r[2];for(var i=3;i<r.length;i++){var a=r[i],o=t.matchPrefix(e,"a=rtpmap:"+a+" ")[0];if(o){var s=t.parseRtpMap(o),c=t.matchPrefix(e,"a=fmtp:"+a+" ");switch(s.parameters=c.length?t.parseFmtp(c[0]):{},s.rtcpFeedback=t.matchPrefix(e,"a=rtcp-fb:"+a+" ").map(t.parseRtcpFb),n.codecs.push(s),s.name.toUpperCase()){case"RED":case"ULPFEC":n.fecMechanisms.push(s.name.toUpperCase());break}}}t.matchPrefix(e,"a=extmap:").forEach((function(e){n.headerExtensions.push(t.parseExtmap(e))}));var u=t.matchPrefix(e,"a=rtcp-fb:* ").map(t.parseRtcpFb);return n.codecs.forEach((function(e){u.forEach((function(t){e.rtcpFeedback.find((function(e){return e.type===t.type&&e.parameter===t.parameter}))||e.rtcpFeedback.push(t)}))})),n},t.writeRtpDescription=function(e,n){var r="";r+="m="+e+" ",r+=n.codecs.length>0?"9":"0",r+=" "+(n.profile||"UDP/TLS/RTP/SAVPF")+" ",r+=n.codecs.map((function(e){return void 0!==e.preferredPayloadType?e.preferredPayloadType:e.payloadType})).join(" ")+"\r\n",r+="c=IN IP4 0.0.0.0\r\n",r+="a=rtcp:9 IN IP4 0.0.0.0\r\n",n.codecs.forEach((function(e){r+=t.writeRtpMap(e),r+=t.writeFmtp(e),r+=t.writeRtcpFb(e)}));var i=0;return n.codecs.forEach((function(e){e.maxptime>i&&(i=e.maxptime)})),i>0&&(r+="a=maxptime:"+i+"\r\n"),n.headerExtensions&&n.headerExtensions.forEach((function(e){r+=t.writeExtmap(e)})),r},t.parseRtpEncodingParameters=function(e){var n,r=[],i=t.parseRtpParameters(e),a=-1!==i.fecMechanisms.indexOf("RED"),o=-1!==i.fecMechanisms.indexOf("ULPFEC"),s=t.matchPrefix(e,"a=ssrc:").map((function(e){return t.parseSsrcMedia(e)})).filter((function(e){return"cname"===e.attribute})),c=s.length>0&&s[0].ssrc,u=t.matchPrefix(e,"a=ssrc-group:FID").map((function(e){return e.substring(17).split(" ").map((function(e){return parseInt(e,10)}))}));u.length>0&&u[0].length>1&&u[0][0]===c&&(n=u[0][1]),i.codecs.forEach((function(e){if("RTX"===e.name.toUpperCase()&&e.parameters.apt){var t={ssrc:c,codecPayloadType:parseInt(e.parameters.apt,10)};c&&n&&(t.rtx={ssrc:n}),r.push(t),a&&((t=JSON.parse(JSON.stringify(t))).fec={ssrc:c,mechanism:o?"red+ulpfec":"red"},r.push(t))}})),0===r.length&&c&&r.push({ssrc:c});var d=t.matchPrefix(e,"b=");return d.length&&(d=0===d[0].indexOf("b=TIAS:")?parseInt(d[0].substring(7),10):0===d[0].indexOf("b=AS:")?1e3*parseInt(d[0].substring(5),10)*.95-16e3:void 0,r.forEach((function(e){e.maxBitrate=d}))),r},t.parseRtcpParameters=function(e){var n={},r=t.matchPrefix(e,"a=ssrc:").map((function(e){return t.parseSsrcMedia(e)})).filter((function(e){return"cname"===e.attribute}))[0];r&&(n.cname=r.value,n.ssrc=r.ssrc);var i=t.matchPrefix(e,"a=rtcp-rsize");n.reducedSize=i.length>0,n.compound=0===i.length;var a=t.matchPrefix(e,"a=rtcp-mux");return n.mux=a.length>0,n},t.writeRtcpParameters=function(e){var t="";return e.reducedSize&&(t+="a=rtcp-rsize\r\n"),e.mux&&(t+="a=rtcp-mux\r\n"),void 0!==e.ssrc&&e.cname&&(t+="a=ssrc:"+e.ssrc+" cname:"+e.cname+"\r\n"),t},t.parseMsid=function(e){var n,r=t.matchPrefix(e,"a=msid:");if(1===r.length)return{stream:(n=r[0].substring(7).split(" "))[0],track:n[1]};var i=t.matchPrefix(e,"a=ssrc:").map((function(e){return t.parseSsrcMedia(e)})).filter((function(e){return"msid"===e.attribute}));return i.length>0?{stream:(n=i[0].value.split(" "))[0],track:n[1]}:void 0},t.parseSctpDescription=function(e){var n,r=t.parseMLine(e),i=t.matchPrefix(e,"a=max-message-size:");i.length>0&&(n=parseInt(i[0].substring(19),10)),isNaN(n)&&(n=65536);var a=t.matchPrefix(e,"a=sctp-port:");if(a.length>0)return{port:parseInt(a[0].substring(12),10),protocol:r.fmt,maxMessageSize:n};var o=t.matchPrefix(e,"a=sctpmap:");if(o.length>0){var s=o[0].substring(10).split(" ");return{port:parseInt(s[0],10),protocol:s[1],maxMessageSize:n}}},t.writeSctpDescription=function(e,t){var n=[];return n="DTLS/SCTP"!==e.protocol?["m="+e.kind+" 9 "+e.protocol+" "+t.protocol+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctp-port:"+t.port+"\r\n"]:["m="+e.kind+" 9 "+e.protocol+" "+t.port+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctpmap:"+t.port+" "+t.protocol+" 65535\r\n"],void 0!==t.maxMessageSize&&n.push("a=max-message-size:"+t.maxMessageSize+"\r\n"),n.join("")},t.generateSessionId=function(){return Math.random().toString().substr(2,22)},t.writeSessionBoilerplate=function(e,n,r){var i=void 0!==n?n:2;return"v=0\r\no="+(r||"thisisadapterortc")+" "+(e||t.generateSessionId())+" "+i+" IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n"},t.getDirection=function(e,n){for(var r=t.splitLines(e),i=0;i<r.length;i++)switch(r[i]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return r[i].substring(2)}return n?t.getDirection(n):"sendrecv"},t.getKind=function(e){return t.splitLines(e)[0].split(" ")[0].substring(2)},t.isRejected=function(e){return"0"===e.split(" ",2)[1]},t.parseMLine=function(e){var n=t.splitLines(e)[0].substring(2).split(" ");return{kind:n[0],port:parseInt(n[1],10),protocol:n[2],fmt:n.slice(3).join(" ")}},t.parseOLine=function(e){var n=t.matchPrefix(e,"o=")[0].substring(2).split(" ");return{username:n[0],sessionId:n[1],sessionVersion:parseInt(n[2],10),netType:n[3],addressType:n[4],address:n[5]}},t.isValidSDP=function(e){if("string"!=typeof e||0===e.length)return!1;for(var n=t.splitLines(e),r=0;r<n.length;r++)if(2>n[r].length||"="!==n[r].charAt(1))return!1;return!0},e.exports=t}(cr);var ur=cr.exports,dr=N(ur),lr=I({__proto__:null,default:dr},[ur]);function hr(e){if(!(!e.RTCIceCandidate||e.RTCIceCandidate&&"foundation"in e.RTCIceCandidate.prototype)){var t=e.RTCIceCandidate;e.RTCIceCandidate=function(e){if("object"===i(e)&&e.candidate&&0===e.candidate.indexOf("a=")&&((e=JSON.parse(JSON.stringify(e))).candidate=e.candidate.substring(2)),e.candidate&&e.candidate.length){var n=new t(e),r=dr.parseCandidate(e.candidate);for(var a in r)a in n||Object.defineProperty(n,a,{value:r[a]});return n.toJSON=function(){return{candidate:n.candidate,sdpMid:n.sdpMid,sdpMLineIndex:n.sdpMLineIndex,usernameFragment:n.usernameFragment}},n}return new t(e)},e.RTCIceCandidate.prototype=t.prototype,bn(e,"icecandidate",(function(t){return t.candidate&&Object.defineProperty(t,"candidate",{value:new e.RTCIceCandidate(t.candidate),writable:"false"}),t}))}}function fr(e){!e.RTCIceCandidate||e.RTCIceCandidate&&"relayProtocol"in e.RTCIceCandidate.prototype||bn(e,"icecandidate",(function(e){if(e.candidate){var t=dr.parseCandidate(e.candidate.candidate);"relay"===t.type&&(e.candidate.relayProtocol={0:"tls",1:"tcp",2:"udp"}[t.priority>>24])}return e}))}function pr(e,t){if(e.RTCPeerConnection){"sctp"in e.RTCPeerConnection.prototype||Object.defineProperty(e.RTCPeerConnection.prototype,"sctp",{get:function(){return void 0===this._sctp?null:this._sctp}});var n=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){var e,r;(this._sctp=null,"chrome"!==t.browser||t.version<76)||"plan-b"===this.getConfiguration().sdpSemantics&&Object.defineProperty(this,"sctp",{get:function(){return void 0===this._sctp?null:this._sctp},enumerable:!0,configurable:!0});if(function(e){if(!e||!e.sdp)return!1;var t=dr.splitSections(e.sdp);return t.shift(),t.some((function(e){var t=dr.parseMLine(e);return t&&"application"===t.kind&&-1!==t.protocol.indexOf("SCTP")}))}(arguments[0])){var i,a=function(e){var t=e.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(null===t||2>t.length)return-1;var n=parseInt(t[1],10);return n!=n?-1:n}(arguments[0]),o=(e=a,r=65536,"firefox"===t.browser&&(r=57>t.version?-1===e?16384:2147483637:60>t.version?57===t.version?65535:65536:2147483637),r),s=function(e,n){var r=65536;"firefox"===t.browser&&57===t.version&&(r=65535);var i=dr.matchPrefix(e.sdp,"a=max-message-size:");return i.length>0?r=parseInt(i[0].substring(19),10):"firefox"===t.browser&&-1!==n&&(r=2147483637),r}(arguments[0],a);i=0===o&&0===s?Number.POSITIVE_INFINITY:0===o||0===s?Math.max(o,s):Math.min(o,s);var c={};Object.defineProperty(c,"maxMessageSize",{get:function(){return i}}),this._sctp=c}return n.apply(this,arguments)}}}function mr(e){if(e.RTCPeerConnection&&"createDataChannel"in e.RTCPeerConnection.prototype){var t=e.RTCPeerConnection.prototype.createDataChannel;e.RTCPeerConnection.prototype.createDataChannel=function(){var e=t.apply(this,arguments);return n(e,this),e},bn(e,"datachannel",(function(e){return n(e.channel,e.target),e}))}function n(e,t){var n=e.send;e.send=function(){var r=arguments[0],i=r.length||r.size||r.byteLength;if("open"===e.readyState&&t.sctp&&i>t.sctp.maxMessageSize)throw new TypeError("Message too large (can send a maximum of "+t.sctp.maxMessageSize+" bytes)");return n.apply(e,arguments)}}}function vr(e){if(e.RTCPeerConnection&&!("connectionState"in e.RTCPeerConnection.prototype)){var t=e.RTCPeerConnection.prototype;Object.defineProperty(t,"connectionState",{get:function(){return{completed:"connected",checking:"connecting"}[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(t,"onconnectionstatechange",{get:function(){return this._onconnectionstatechange||null},set:function(e){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),e&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=e)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach((function(e){var n=t[e];t[e]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=function(e){var t=e.target;if(t._lastConnectionState!==t.connectionState){t._lastConnectionState=t.connectionState;var n=new Event("connectionstatechange",e);t.dispatchEvent(n)}return e},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),n.apply(this,arguments)}}))}}function kr(e,t){if(e.RTCPeerConnection&&("chrome"!==t.browser||71>t.version)&&("safari"!==t.browser||605>t.version)){var n=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(t){if(t&&t.sdp&&-1!==t.sdp.indexOf("\na=extmap-allow-mixed")){var r=t.sdp.split("\n").filter((function(e){return"a=extmap-allow-mixed"!==e.trim()})).join("\n");e.RTCSessionDescription&&t instanceof e.RTCSessionDescription?arguments[0]=new e.RTCSessionDescription({type:t.type,sdp:r}):t.sdp=r}return n.apply(this,arguments)}}}function gr(e,t){if(e.RTCPeerConnection&&e.RTCPeerConnection.prototype){var n=e.RTCPeerConnection.prototype.addIceCandidate;n&&0!==n.length&&(e.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?("chrome"===t.browser&&78>t.version||"firefox"===t.browser&&68>t.version||"safari"===t.browser)&&arguments[0]&&""===arguments[0].candidate?Promise.resolve():n.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())})}}function yr(e,t){if(e.RTCPeerConnection&&e.RTCPeerConnection.prototype){var n=e.RTCPeerConnection.prototype.setLocalDescription;n&&0!==n.length&&(e.RTCPeerConnection.prototype.setLocalDescription=function(){var e=this,t=arguments[0]||{};if("object"!==i(t)||t.type&&t.sdp)return n.apply(this,arguments);if(!(t={type:t.type,sdp:t.sdp}).type)switch(this.signalingState){case"stable":case"have-local-offer":case"have-remote-pranswer":t.type="offer";break;default:t.type="answer";break}return t.sdp||"offer"!==t.type&&"answer"!==t.type?n.apply(this,[t]):("offer"===t.type?this.createOffer:this.createAnswer).apply(this).then((function(t){return n.apply(e,[t])}))})}}var br,Sr,wr,Tr=Object.freeze({__proto__:null,removeExtmapAllowMixed:kr,shimAddIceCandidateNullOrEmpty:gr,shimConnectionState:vr,shimMaxMessageSize:pr,shimParameterlessSetLocalDescription:yr,shimRTCIceCandidate:hr,shimRTCIceCandidateRelayProtocol:fr,shimSendThrowTypeError:mr});!function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).window,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{shimChrome:!0,shimFirefox:!0,shimSafari:!0},n=Tn,r=function(e){var t={browser:null,version:null};if(void 0===e||!e.navigator||!e.navigator.userAgent)return t.browser="Not a browser.",t;var n=e.navigator;if(n.mozGetUserMedia)t.browser="firefox",t.version=yn(n.userAgent,/Firefox\/(\d+)\./,1);else if(n.webkitGetUserMedia||!1===e.isSecureContext&&e.webkitRTCPeerConnection)t.browser="chrome",t.version=yn(n.userAgent,/Chrom(e|ium)\/(\d+)\./,2);else{if(!e.RTCPeerConnection||!n.userAgent.match(/AppleWebKit\/(\d+)\./))return t.browser="Not a supported browser.",t;t.browser="safari",t.version=yn(n.userAgent,/AppleWebKit\/(\d+)\./,1),t.supportsUnifiedPlan=e.RTCRtpTransceiver&&"currentDirection"in e.RTCRtpTransceiver.prototype}return t}(e),i={browserDetails:r,commonShim:Tr,extractVersion:yn,disableLog:Sn,disableWarnings:wn,sdp:lr};switch(r.browser){case"chrome":if(!Fn||!Un||!t.shimChrome)return n("Chrome shim is not included in this adapter release."),i;if(null===r.version)return n("Chrome shim can not determine version, not shimming."),i;n("adapter.js shimming chrome."),i.browserShim=Fn,gr(e,r),yr(e),In(e,r),Dn(e),Un(e,r),Nn(e),An(e,r),On(e),_n(e),Mn(e),Bn(e,r),hr(e),fr(e),vr(e),pr(e,r),mr(e),kr(e,r);break;case"firefox":if(!Xn||!qn||!t.shimFirefox)return n("Firefox shim is not included in this adapter release."),i;n("adapter.js shimming firefox."),i.browserShim=Xn,gr(e,r),yr(e),Jn(e,r),qn(e,r),jn(e),Wn(e),Vn(e),Hn(e),Gn(e),Kn(e),zn(e),Qn(e),Yn(e),hr(e),vr(e),pr(e,r),mr(e);break;case"safari":if(!sr||!t.shimSafari)return n("Safari shim is not included in this adapter release."),i;n("adapter.js shimming safari."),i.browserShim=sr,gr(e,r),yr(e),rr(e),ar(e),er(e),Zn(e),$n(e),ir(e),tr(e),or(e),hr(e),fr(e),pr(e,r),mr(e),kr(e,r);break;default:n("Unsupported browser!");break}}({window:"undefined"==typeof window?void 0:window}),function(e){e[e.PUBLISHER=0]="PUBLISHER",e[e.SUBSCRIBER=1]="SUBSCRIBER"}(br||(br={})),lt.util.setEnumType(br,"livekit.SignalTarget",[{no:0,name:"PUBLISHER"},{no:1,name:"SUBSCRIBER"}]),function(e){e[e.ACTIVE=0]="ACTIVE",e[e.PAUSED=1]="PAUSED"}(Sr||(Sr={})),lt.util.setEnumType(Sr,"livekit.StreamState",[{no:0,name:"ACTIVE"},{no:1,name:"PAUSED"}]),function(e){e[e.UDP=0]="UDP",e[e.TCP=1]="TCP",e[e.TLS=2]="TLS"}(wr||(wr={})),lt.util.setEnumType(wr,"livekit.CandidateProtocol",[{no:0,name:"UDP"},{no:1,name:"TCP"},{no:2,name:"TLS"}]);var Cr=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).message={case:void 0},lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Cr.runtime=lt,Cr.typeName="livekit.SignalRequest",Cr.fields=lt.util.newFieldList((function(){return[{no:1,name:"offer",kind:"message",T:Mr,oneof:"message"},{no:2,name:"answer",kind:"message",T:Mr,oneof:"message"},{no:3,name:"trickle",kind:"message",T:Rr,oneof:"message"},{no:4,name:"add_track",kind:"message",T:Er,oneof:"message"},{no:5,name:"mute",kind:"message",T:Ir,oneof:"message"},{no:6,name:"subscription",kind:"message",T:Ar,oneof:"message"},{no:7,name:"track_setting",kind:"message",T:Ur,oneof:"message"},{no:8,name:"leave",kind:"message",T:Br,oneof:"message"},{no:10,name:"update_layers",kind:"message",T:Fr,oneof:"message"},{no:11,name:"subscription_permission",kind:"message",T:Zr,oneof:"message"},{no:12,name:"sync_state",kind:"message",T:ei,oneof:"message"},{no:13,name:"simulate",kind:"message",T:ni,oneof:"message"},{no:14,name:"ping",kind:"scalar",T:3,oneof:"message"},{no:15,name:"update_metadata",kind:"message",T:Jr,oneof:"message"},{no:16,name:"ping_req",kind:"message",T:ri,oneof:"message"}]}));var xr=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).message={case:void 0},lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();xr.runtime=lt,xr.typeName="livekit.SignalResponse",xr.fields=lt.util.newFieldList((function(){return[{no:1,name:"join",kind:"message",T:Dr,oneof:"message"},{no:2,name:"answer",kind:"message",T:Mr,oneof:"message"},{no:3,name:"offer",kind:"message",T:Mr,oneof:"message"},{no:4,name:"trickle",kind:"message",T:Rr,oneof:"message"},{no:5,name:"update",kind:"message",T:Lr,oneof:"message"},{no:6,name:"track_published",kind:"message",T:Or,oneof:"message"},{no:8,name:"leave",kind:"message",T:Br,oneof:"message"},{no:9,name:"mute",kind:"message",T:Ir,oneof:"message"},{no:10,name:"speakers_changed",kind:"message",T:qr,oneof:"message"},{no:11,name:"room_update",kind:"message",T:Vr,oneof:"message"},{no:12,name:"connection_quality",kind:"message",T:Wr,oneof:"message"},{no:13,name:"stream_state_update",kind:"message",T:Kr,oneof:"message"},{no:14,name:"subscribed_quality_update",kind:"message",T:Yr,oneof:"message"},{no:15,name:"subscription_permission_update",kind:"message",T:$r,oneof:"message"},{no:16,name:"refresh_token",kind:"scalar",T:9,oneof:"message"},{no:17,name:"track_unpublished",kind:"message",T:_r,oneof:"message"},{no:18,name:"pong",kind:"scalar",T:3,oneof:"message"},{no:19,name:"reconnect",kind:"message",T:Nr,oneof:"message"},{no:20,name:"pong_resp",kind:"message",T:ii,oneof:"message"},{no:21,name:"subscription_response",kind:"message",T:oi,oneof:"message"}]}));var Pr=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).codec="",r.cid="",lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Pr.runtime=lt,Pr.typeName="livekit.SimulcastCodec",Pr.fields=lt.util.newFieldList((function(){return[{no:1,name:"codec",kind:"scalar",T:9},{no:2,name:"cid",kind:"scalar",T:9}]}));var Er=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).cid="",r.name="",r.type=vt.AUDIO,r.width=0,r.height=0,r.muted=!1,r.disableDtx=!1,r.source=kt.UNKNOWN,r.layers=[],r.simulcastCodecs=[],r.sid="",r.stereo=!1,r.disableRed=!1,r.encryption=It.NONE,r.stream="",lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Er.runtime=lt,Er.typeName="livekit.AddTrackRequest",Er.fields=lt.util.newFieldList((function(){return[{no:1,name:"cid",kind:"scalar",T:9},{no:2,name:"name",kind:"scalar",T:9},{no:3,name:"type",kind:"enum",T:lt.getEnumType(vt)},{no:4,name:"width",kind:"scalar",T:13},{no:5,name:"height",kind:"scalar",T:13},{no:6,name:"muted",kind:"scalar",T:8},{no:7,name:"disable_dtx",kind:"scalar",T:8},{no:8,name:"source",kind:"enum",T:lt.getEnumType(kt)},{no:9,name:"layers",kind:"message",T:_t,repeated:!0},{no:10,name:"simulcast_codecs",kind:"message",T:Pr,repeated:!0},{no:11,name:"sid",kind:"scalar",T:9},{no:12,name:"stereo",kind:"scalar",T:8},{no:13,name:"disable_red",kind:"scalar",T:8},{no:14,name:"encryption",kind:"enum",T:lt.getEnumType(It)},{no:15,name:"stream",kind:"scalar",T:9}]}));var Rr=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).candidateInit="",r.target=br.PUBLISHER,lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Rr.runtime=lt,Rr.typeName="livekit.TrickleRequest",Rr.fields=lt.util.newFieldList((function(){return[{no:1,name:"candidateInit",kind:"scalar",T:9},{no:2,name:"target",kind:"enum",T:lt.getEnumType(br)}]}));var Ir=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).sid="",r.muted=!1,lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Ir.runtime=lt,Ir.typeName="livekit.MuteTrackRequest",Ir.fields=lt.util.newFieldList((function(){return[{no:1,name:"sid",kind:"scalar",T:9},{no:2,name:"muted",kind:"scalar",T:8}]}));var Dr=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).otherParticipants=[],r.serverVersion="",r.iceServers=[],r.subscriberPrimary=!1,r.alternativeUrl="",r.serverRegion="",r.pingTimeout=0,r.pingInterval=0,r.sifTrailer=new Uint8Array(0),lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Dr.runtime=lt,Dr.typeName="livekit.JoinResponse",Dr.fields=lt.util.newFieldList((function(){return[{no:1,name:"room",kind:"message",T:xt},{no:2,name:"participant",kind:"message",T:Dt},{no:3,name:"other_participants",kind:"message",T:Dt,repeated:!0},{no:4,name:"server_version",kind:"scalar",T:9},{no:5,name:"ice_servers",kind:"message",T:jr,repeated:!0},{no:6,name:"subscriber_primary",kind:"scalar",T:8},{no:7,name:"alternative_url",kind:"scalar",T:9},{no:8,name:"client_configuration",kind:"message",T:Ht},{no:9,name:"server_region",kind:"scalar",T:9},{no:10,name:"ping_timeout",kind:"scalar",T:5},{no:11,name:"ping_interval",kind:"scalar",T:5},{no:12,name:"server_info",kind:"message",T:jt},{no:13,name:"sif_trailer",kind:"scalar",T:12}]}));var Nr=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).iceServers=[],lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Nr.runtime=lt,Nr.typeName="livekit.ReconnectResponse",Nr.fields=lt.util.newFieldList((function(){return[{no:1,name:"ice_servers",kind:"message",T:jr,repeated:!0},{no:2,name:"client_configuration",kind:"message",T:Ht}]}));var Or=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).cid="",lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Or.runtime=lt,Or.typeName="livekit.TrackPublishedResponse",Or.fields=lt.util.newFieldList((function(){return[{no:1,name:"cid",kind:"scalar",T:9},{no:2,name:"track",kind:"message",T:Ot}]}));var _r=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).trackSid="",lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();_r.runtime=lt,_r.typeName="livekit.TrackUnpublishedResponse",_r.fields=lt.util.newFieldList((function(){return[{no:1,name:"track_sid",kind:"scalar",T:9}]}));var Mr=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).type="",r.sdp="",lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Mr.runtime=lt,Mr.typeName="livekit.SessionDescription",Mr.fields=lt.util.newFieldList((function(){return[{no:1,name:"type",kind:"scalar",T:9},{no:2,name:"sdp",kind:"scalar",T:9}]}));var Lr=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).participants=[],lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Lr.runtime=lt,Lr.typeName="livekit.ParticipantUpdate",Lr.fields=lt.util.newFieldList((function(){return[{no:1,name:"participants",kind:"message",T:Dt,repeated:!0}]}));var Ar=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).trackSids=[],r.subscribe=!1,r.participantTracks=[],lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Ar.runtime=lt,Ar.typeName="livekit.UpdateSubscription",Ar.fields=lt.util.newFieldList((function(){return[{no:1,name:"track_sids",kind:"scalar",T:9,repeated:!0},{no:2,name:"subscribe",kind:"scalar",T:8},{no:3,name:"participant_tracks",kind:"message",T:Ft,repeated:!0}]}));var Ur=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).trackSids=[],r.disabled=!1,r.quality=gt.LOW,r.width=0,r.height=0,r.fps=0,r.priority=0,lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Ur.runtime=lt,Ur.typeName="livekit.UpdateTrackSettings",Ur.fields=lt.util.newFieldList((function(){return[{no:1,name:"track_sids",kind:"scalar",T:9,repeated:!0},{no:3,name:"disabled",kind:"scalar",T:8},{no:4,name:"quality",kind:"enum",T:lt.getEnumType(gt)},{no:5,name:"width",kind:"scalar",T:13},{no:6,name:"height",kind:"scalar",T:13},{no:7,name:"fps",kind:"scalar",T:13},{no:8,name:"priority",kind:"scalar",T:13}]}));var Br=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).canReconnect=!1,r.reason=St.UNKNOWN_REASON,lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Br.runtime=lt,Br.typeName="livekit.LeaveRequest",Br.fields=lt.util.newFieldList((function(){return[{no:1,name:"can_reconnect",kind:"scalar",T:8},{no:2,name:"reason",kind:"enum",T:lt.getEnumType(St)}]}));var Fr=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).trackSid="",r.layers=[],lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Fr.runtime=lt,Fr.typeName="livekit.UpdateVideoLayers",Fr.fields=lt.util.newFieldList((function(){return[{no:1,name:"track_sid",kind:"scalar",T:9},{no:2,name:"layers",kind:"message",T:_t,repeated:!0}]}));var Jr=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).metadata="",r.name="",lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Jr.runtime=lt,Jr.typeName="livekit.UpdateParticipantMetadata",Jr.fields=lt.util.newFieldList((function(){return[{no:1,name:"metadata",kind:"scalar",T:9},{no:2,name:"name",kind:"scalar",T:9}]}));var jr=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).urls=[],r.username="",r.credential="",lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();jr.runtime=lt,jr.typeName="livekit.ICEServer",jr.fields=lt.util.newFieldList((function(){return[{no:1,name:"urls",kind:"scalar",T:9,repeated:!0},{no:2,name:"username",kind:"scalar",T:9},{no:3,name:"credential",kind:"scalar",T:9}]}));var qr=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).speakers=[],lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();qr.runtime=lt,qr.typeName="livekit.SpeakersChanged",qr.fields=lt.util.newFieldList((function(){return[{no:1,name:"speakers",kind:"message",T:Ut,repeated:!0}]}));var Vr=function(t){function n(t){var r;return s(this,n),r=e(this,n),lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Vr.runtime=lt,Vr.typeName="livekit.RoomUpdate",Vr.fields=lt.util.newFieldList((function(){return[{no:1,name:"room",kind:"message",T:xt}]}));var Hr=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).participantSid="",r.quality=yt.POOR,r.score=0,lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Hr.runtime=lt,Hr.typeName="livekit.ConnectionQualityInfo",Hr.fields=lt.util.newFieldList((function(){return[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:2,name:"quality",kind:"enum",T:lt.getEnumType(yt)},{no:3,name:"score",kind:"scalar",T:2}]}));var Wr=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).updates=[],lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Wr.runtime=lt,Wr.typeName="livekit.ConnectionQualityUpdate",Wr.fields=lt.util.newFieldList((function(){return[{no:1,name:"updates",kind:"message",T:Hr,repeated:!0}]}));var Gr=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).participantSid="",r.trackSid="",r.state=Sr.ACTIVE,lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Gr.runtime=lt,Gr.typeName="livekit.StreamStateInfo",Gr.fields=lt.util.newFieldList((function(){return[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:2,name:"track_sid",kind:"scalar",T:9},{no:3,name:"state",kind:"enum",T:lt.getEnumType(Sr)}]}));var Kr=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).streamStates=[],lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Kr.runtime=lt,Kr.typeName="livekit.StreamStateUpdate",Kr.fields=lt.util.newFieldList((function(){return[{no:1,name:"stream_states",kind:"message",T:Gr,repeated:!0}]}));var zr=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).quality=gt.LOW,r.enabled=!1,lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();zr.runtime=lt,zr.typeName="livekit.SubscribedQuality",zr.fields=lt.util.newFieldList((function(){return[{no:1,name:"quality",kind:"enum",T:lt.getEnumType(gt)},{no:2,name:"enabled",kind:"scalar",T:8}]}));var Qr=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).codec="",r.qualities=[],lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Qr.runtime=lt,Qr.typeName="livekit.SubscribedCodec",Qr.fields=lt.util.newFieldList((function(){return[{no:1,name:"codec",kind:"scalar",T:9},{no:2,name:"qualities",kind:"message",T:zr,repeated:!0}]}));var Yr=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).trackSid="",r.subscribedQualities=[],r.subscribedCodecs=[],lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Yr.runtime=lt,Yr.typeName="livekit.SubscribedQualityUpdate",Yr.fields=lt.util.newFieldList((function(){return[{no:1,name:"track_sid",kind:"scalar",T:9},{no:2,name:"subscribed_qualities",kind:"message",T:zr,repeated:!0},{no:3,name:"subscribed_codecs",kind:"message",T:Qr,repeated:!0}]}));var Xr=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).participantSid="",r.allTracks=!1,r.trackSids=[],r.participantIdentity="",lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Xr.runtime=lt,Xr.typeName="livekit.TrackPermission",Xr.fields=lt.util.newFieldList((function(){return[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:2,name:"all_tracks",kind:"scalar",T:8},{no:3,name:"track_sids",kind:"scalar",T:9,repeated:!0},{no:4,name:"participant_identity",kind:"scalar",T:9}]}));var Zr=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).allParticipants=!1,r.trackPermissions=[],lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();Zr.runtime=lt,Zr.typeName="livekit.SubscriptionPermission",Zr.fields=lt.util.newFieldList((function(){return[{no:1,name:"all_participants",kind:"scalar",T:8},{no:2,name:"track_permissions",kind:"message",T:Xr,repeated:!0}]}));var $r=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).participantSid="",r.trackSid="",r.allowed=!1,lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();$r.runtime=lt,$r.typeName="livekit.SubscriptionPermissionUpdate",$r.fields=lt.util.newFieldList((function(){return[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:2,name:"track_sid",kind:"scalar",T:9},{no:3,name:"allowed",kind:"scalar",T:8}]}));var ei=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).publishTracks=[],r.dataChannels=[],lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();ei.runtime=lt,ei.typeName="livekit.SyncState",ei.fields=lt.util.newFieldList((function(){return[{no:1,name:"answer",kind:"message",T:Mr},{no:2,name:"subscription",kind:"message",T:Ar},{no:3,name:"publish_tracks",kind:"message",T:Or,repeated:!0},{no:4,name:"data_channels",kind:"message",T:ti,repeated:!0},{no:5,name:"offer",kind:"message",T:Mr}]}));var ti=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).label="",r.id=0,r.target=br.PUBLISHER,lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();ti.runtime=lt,ti.typeName="livekit.DataChannelInfo",ti.fields=lt.util.newFieldList((function(){return[{no:1,name:"label",kind:"scalar",T:9},{no:2,name:"id",kind:"scalar",T:13},{no:3,name:"target",kind:"enum",T:lt.getEnumType(br)}]}));var ni=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).scenario={case:void 0},lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();ni.runtime=lt,ni.typeName="livekit.SimulateScenario",ni.fields=lt.util.newFieldList((function(){return[{no:1,name:"speaker_update",kind:"scalar",T:5,oneof:"scenario"},{no:2,name:"node_failure",kind:"scalar",T:8,oneof:"scenario"},{no:3,name:"migration",kind:"scalar",T:8,oneof:"scenario"},{no:4,name:"server_leave",kind:"scalar",T:8,oneof:"scenario"},{no:5,name:"switch_candidate_protocol",kind:"enum",T:lt.getEnumType(wr),oneof:"scenario"},{no:6,name:"subscriber_bandwidth",kind:"scalar",T:3,oneof:"scenario"}]}));var ri=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).timestamp=pe.zero,r.rtt=pe.zero,lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();ri.runtime=lt,ri.typeName="livekit.Ping",ri.fields=lt.util.newFieldList((function(){return[{no:1,name:"timestamp",kind:"scalar",T:3},{no:2,name:"rtt",kind:"scalar",T:3}]}));var ii=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).lastPingTimestamp=pe.zero,r.timestamp=pe.zero,lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();ii.runtime=lt,ii.typeName="livekit.Pong",ii.fields=lt.util.newFieldList((function(){return[{no:1,name:"last_ping_timestamp",kind:"scalar",T:3},{no:2,name:"timestamp",kind:"scalar",T:3}]})),lt.util.newFieldList((function(){return[{no:1,name:"regions",kind:"message",T:ai,repeated:!0}]}));var ai=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).region="",r.url="",r.distance=pe.zero,lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();ai.runtime=lt,ai.typeName="livekit.RegionInfo",ai.fields=lt.util.newFieldList((function(){return[{no:1,name:"region",kind:"scalar",T:9},{no:2,name:"url",kind:"scalar",T:9},{no:3,name:"distance",kind:"scalar",T:3}]}));var oi=function(t){function n(t){var r;return s(this,n),(r=e(this,n)).trackSid="",r.err=Tt.SE_UNKNOWN,lt.util.initPartial(t,m(r)),r}return l(n,ne),u(n,null,[{key:"fromBinary",value:function(e,t){return(new n).fromBinary(e,t)}},{key:"fromJson",value:function(e,t){return(new n).fromJson(e,t)}},{key:"fromJsonString",value:function(e,t){return(new n).fromJsonString(e,t)}},{key:"equals",value:function(e,t){return lt.util.equals(n,e,t)}}]),n}();oi.runtime=lt,oi.typeName="livekit.SubscriptionResponse",oi.fields=lt.util.newFieldList((function(){return[{no:1,name:"track_sid",kind:"scalar",T:9},{no:2,name:"err",kind:"enum",T:lt.getEnumType(Tt)}]}));var si,ci=function(t){function n(t,r){var i;return s(this,n),(i=e(this,n,[r||"an error has occured"])).code=t,i}return l(n,p(Error)),u(n)}(),ui=function(t){function n(t,r,i){var a;return s(this,n),(a=e(this,n,[1,t])).status=i,a.reason=r,a}return l(n,ci),u(n)}(),di=function(t){function n(t){return s(this,n),e(this,n,[21,null!=t?t:"device is unsupported"])}return l(n,ci),u(n)}(),li=function(t){function n(t){return s(this,n),e(this,n,[20,null!=t?t:"track is invalid"])}return l(n,ci),u(n)}(),hi=function(t){function n(t){return s(this,n),e(this,n,[10,null!=t?t:"unsupported server"])}return l(n,ci),u(n)}(),fi=function(t){function n(t){return s(this,n),e(this,n,[12,null!=t?t:"unexpected connection state"])}return l(n,ci),u(n)}(),pi=function(t){function n(t){return s(this,n),e(this,n,[13,null!=t?t:"unable to negotiate"])}return l(n,ci),u(n)}();!function(e){e.PermissionDenied="PermissionDenied",e.NotFound="NotFound",e.DeviceInUse="DeviceInUse",e.Other="Other"}(si||(si={})),function(e){e.getFailure=function(t){if(t&&"name"in t)return"NotFoundError"===t.name||"DevicesNotFoundError"===t.name?e.NotFound:"NotAllowedError"===t.name||"PermissionDeniedError"===t.name?e.PermissionDenied:"NotReadableError"===t.name||"TrackStartError"===t.name?e.DeviceInUse:e.Other}}(si||(si={}));var mi=u((function e(){s(this,e)}));mi.setTimeout=function(){return setTimeout.apply(void 0,arguments)},mi.setInterval=function(){return setInterval.apply(void 0,arguments)},mi.clearTimeout=function(){return clearTimeout.apply(void 0,arguments)},mi.clearInterval=function(){return clearInterval.apply(void 0,arguments)};var vi,ki=/version\/(\d+(\.?_?\d+)+)/i;function gi(e){var t=1>=arguments.length||void 0===arguments[1]||arguments[1];if(void 0!==e||"undefined"!=typeof navigator){var n=(null!=e?e:navigator.userAgent).toLowerCase();if(void 0===vi||t){var r=yi.find((function(e){return e.test.test(n)}));vi=null==r?void 0:r.describe(n)}return vi}}var yi=[{test:/firefox|iceweasel|fxios/i,describe:function(e){return{name:"Firefox",version:bi(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,e),os:e.toLowerCase().includes("fxios")?"iOS":void 0}}},{test:/chrom|crios|crmo/i,describe:function(e){return{name:"Chrome",version:bi(/(?:chrome|chromium|crios|crmo)\/(\d+(\.?_?\d+)+)/i,e),os:e.toLowerCase().includes("crios")?"iOS":void 0}}},{test:/safari|applewebkit/i,describe:function(e){return{name:"Safari",version:bi(ki,e),os:e.includes("mobile/")?"iOS":"macOS"}}}];function bi(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=t.match(e);return r&&r.length>=n&&r[n]||""}var Si,wi=function(){function e(t,n,r,i,a){s(this,e),this.width=t,this.height=n,this.encoding={maxBitrate:r,maxFramerate:i,priority:a}}return u(e,[{key:"resolution",get:function(){return{width:this.width,height:this.height,frameRate:this.encoding.maxFramerate,aspectRatio:this.width/this.height}}}]),e}(),Ti=["vp8","h264"],Ci=["vp8","h264","vp9","av1"];function xi(e){return!!Ti.find((function(t){return t===e}))}!function(e){e.telephone={maxBitrate:12e3},e.speech={maxBitrate:2e4},e.music={maxBitrate:32e3},e.musicStereo={maxBitrate:48e3},e.musicHighQuality={maxBitrate:64e3},e.musicHighQualityStereo={maxBitrate:96e3}}(Si||(Si={}));var Pi,Ei,Ri,Ii,Di={h90:new wi(160,90,9e4,20),h180:new wi(320,180,16e4,20),h216:new wi(384,216,18e4,20),h360:new wi(640,360,45e4,20),h540:new wi(960,540,8e5,25),h720:new wi(1280,720,17e5,30),h1080:new wi(1920,1080,3e6,30),h1440:new wi(2560,1440,5e6,30),h2160:new wi(3840,2160,8e6,30)},Ni={h120:new wi(160,120,7e4,20),h180:new wi(240,180,125e3,20),h240:new wi(320,240,14e4,20),h360:new wi(480,360,33e4,20),h480:new wi(640,480,5e5,20),h540:new wi(720,540,6e5,25),h720:new wi(960,720,13e5,30),h1080:new wi(1440,1080,23e5,30),h1440:new wi(1920,1440,38e5,30)},Oi={h360fps3:new wi(640,360,2e5,3,"medium"),h720fps5:new wi(1280,720,4e5,5,"medium"),h720fps15:new wi(1280,720,15e5,15,"medium"),h720fps30:new wi(1280,720,2e6,30,"medium"),h1080fps15:new wi(1920,1080,25e5,15,"medium"),h1080fps30:new wi(1920,1080,4e6,30,"medium")};!function(e){e.Connected="connected",e.Reconnecting="reconnecting",e.Reconnected="reconnected",e.Disconnected="disconnected",e.ConnectionStateChanged="connectionStateChanged",e.StateChanged="connectionStateChanged",e.MediaDevicesChanged="mediaDevicesChanged",e.ParticipantConnected="participantConnected",e.ParticipantDisconnected="participantDisconnected",e.TrackPublished="trackPublished",e.TrackSubscribed="trackSubscribed",e.TrackSubscriptionFailed="trackSubscriptionFailed",e.TrackUnpublished="trackUnpublished",e.TrackUnsubscribed="trackUnsubscribed",e.TrackMuted="trackMuted",e.TrackUnmuted="trackUnmuted",e.LocalTrackPublished="localTrackPublished",e.LocalTrackUnpublished="localTrackUnpublished",e.LocalAudioSilenceDetected="localAudioSilenceDetected",e.ActiveSpeakersChanged="activeSpeakersChanged",e.ParticipantMetadataChanged="participantMetadataChanged",e.ParticipantNameChanged="participantNameChanged",e.RoomMetadataChanged="roomMetadataChanged",e.DataReceived="dataReceived",e.ConnectionQualityChanged="connectionQualityChanged",e.TrackStreamStateChanged="trackStreamStateChanged",e.TrackSubscriptionPermissionChanged="trackSubscriptionPermissionChanged",e.TrackSubscriptionStatusChanged="trackSubscriptionStatusChanged",e.AudioPlaybackStatusChanged="audioPlaybackChanged",e.MediaDevicesError="mediaDevicesError",e.ParticipantPermissionsChanged="participantPermissionsChanged",e.SignalConnected="signalConnected",e.RecordingStatusChanged="recordingStatusChanged",e.ParticipantEncryptionStatusChanged="participantEncryptionStatusChanged",e.EncryptionError="encryptionError",e.DCBufferStatusChanged="dcBufferStatusChanged",e.ActiveDeviceChanged="activeDeviceChanged"}(Pi||(Pi={})),function(e){e.TrackPublished="trackPublished",e.TrackSubscribed="trackSubscribed",e.TrackSubscriptionFailed="trackSubscriptionFailed",e.TrackUnpublished="trackUnpublished",e.TrackUnsubscribed="trackUnsubscribed",e.TrackMuted="trackMuted",e.TrackUnmuted="trackUnmuted",e.LocalTrackPublished="localTrackPublished",e.LocalTrackUnpublished="localTrackUnpublished",e.ParticipantMetadataChanged="participantMetadataChanged",e.ParticipantNameChanged="participantNameChanged",e.DataReceived="dataReceived",e.IsSpeakingChanged="isSpeakingChanged",e.ConnectionQualityChanged="connectionQualityChanged",e.TrackStreamStateChanged="trackStreamStateChanged",e.TrackSubscriptionPermissionChanged="trackSubscriptionPermissionChanged",e.TrackSubscriptionStatusChanged="trackSubscriptionStatusChanged",e.MediaDevicesError="mediaDevicesError",e.AudioStreamAcquired="audioStreamAcquired",e.ParticipantPermissionsChanged="participantPermissionsChanged",e.PCTrackAdded="pcTrackAdded"}(Ei||(Ei={})),function(e){e.TransportsCreated="transportsCreated",e.Connected="connected",e.Disconnected="disconnected",e.Resuming="resuming",e.Resumed="resumed",e.Restarting="restarting",e.Restarted="restarted",e.SignalResumed="signalResumed",e.SignalRestarted="signalRestarted",e.Closing="closing",e.MediaTrackAdded="mediaTrackAdded",e.ActiveSpeakersUpdate="activeSpeakersUpdate",e.DataPacketReceived="dataPacketReceived",e.RTPVideoMapUpdate="rtpVideoMapUpdate",e.DCBufferStatusChanged="dcBufferStatusChanged",e.ParticipantUpdate="participantUpdate",e.RoomUpdate="roomUpdate",e.SpeakersChanged="speakersChanged",e.StreamStateChanged="streamStateChanged",e.ConnectionQualityUpdate="connectionQualityUpdate",e.SubscriptionError="subscriptionError",e.SubscriptionPermissionUpdate="subscriptionPermissionUpdate"}(Ri||(Ri={})),function(e){e.Message="message",e.Muted="muted",e.Unmuted="unmuted",e.Restarted="restarted",e.Ended="ended",e.Subscribed="subscribed",e.Unsubscribed="unsubscribed",e.UpdateSettings="updateSettings",e.UpdateSubscription="updateSubscription",e.AudioPlaybackStarted="audioPlaybackStarted",e.AudioPlaybackFailed="audioPlaybackFailed",e.AudioSilenceDetected="audioSilenceDetected",e.VisibilityChanged="visibilityChanged",e.VideoDimensionsChanged="videoDimensionsChanged",e.ElementAttached="elementAttached",e.ElementDetached="elementDetached",e.UpstreamPaused="upstreamPaused",e.UpstreamResumed="upstreamResumed",e.SubscriptionPermissionChanged="subscriptionPermissionChanged",e.SubscriptionStatusChanged="subscriptionStatusChanged",e.SubscriptionFailed="subscriptionFailed"}(Ii||(Ii={}));var _i=[],Mi=function(t){function r(t,n){var i;return s(this,r),(i=e(this,r)).attachedElements=[],i.isMuted=!1,i.streamState=r.StreamState.Active,i.isInBackground=!1,i._currentBitrate=0,i.appVisibilityChangedListener=function(){i.backgroundTimeout&&clearTimeout(i.backgroundTimeout),"hidden"===document.visibilityState?i.backgroundTimeout=setTimeout((function(){return i.handleAppVisibilityChanged()}),5e3):i.handleAppVisibilityChanged()},i.setMaxListeners(100),i.kind=n,i._mediaStreamTrack=t,i._mediaStreamID=t.id,i.source=r.Source.Unknown,i}return l(r,kn.EventEmitter),u(r,[{key:"currentBitrate",get:function(){return this._currentBitrate}},{key:"mediaStreamTrack",get:function(){return this._mediaStreamTrack}},{key:"mediaStreamID",get:function(){return this._mediaStreamID}},{key:"attach",value:function(e){var t=this,n="audio";this.kind===r.Kind.Video&&(n="video"),0===this.attachedElements.length&&r.Kind.Video&&this.addAppVisibilityListener(),e||("audio"===n&&(_i.forEach((function(t){null!==t.parentElement||e||(e=t)})),e&&_i.splice(_i.indexOf(e),1)),e||(e=document.createElement(n))),this.attachedElements.includes(e)||this.attachedElements.push(e),Li(this.mediaStreamTrack,e);var i=e.srcObject.getTracks();return i.some((function(e){return"audio"===e.kind}))&&e.play().then((function(){t.emit(Ii.AudioPlaybackStarted)})).catch((function(n){"NotAllowedError"===n.name?t.emit(Ii.AudioPlaybackFailed,n):B.warn("could not playback audio",n),e&&i.some((function(e){return"video"===e.kind}))&&"NotAllowedError"===n.name&&(e.muted=!0,e.play().catch((function(){})))})),this.emit(Ii.ElementAttached,e),e}},{key:"detach",value:function(e){var t=this;try{if(e){Ai(this.mediaStreamTrack,e);var n=this.attachedElements.indexOf(e);return 0>n||(this.attachedElements.splice(n,1),this.recycleElement(e),this.emit(Ii.ElementDetached,e)),e}var r=[];return this.attachedElements.forEach((function(e){Ai(t.mediaStreamTrack,e),r.push(e),t.recycleElement(e),t.emit(Ii.ElementDetached,e)})),this.attachedElements=[],r}finally{0===this.attachedElements.length&&this.removeAppVisibilityListener()}}},{key:"stop",value:function(){this.stopMonitor(),this._mediaStreamTrack.stop()}},{key:"enable",value:function(){this._mediaStreamTrack.enabled=!0}},{key:"disable",value:function(){this._mediaStreamTrack.enabled=!1}},{key:"stopMonitor",value:function(){this.monitorInterval&&clearInterval(this.monitorInterval)}},{key:"recycleElement",value:function(e){if(e instanceof HTMLAudioElement){var t=!0;e.pause(),_i.forEach((function(e){e.parentElement||(t=!1)})),t&&_i.push(e)}}},{key:"handleAppVisibilityChanged",value:function(){return Xt(this,void 0,void 0,n().mark((function e(){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:this.isInBackground="hidden"===document.visibilityState;case 1:case"end":return e.stop()}}),e,this)})))}},{key:"addAppVisibilityListener",value:function(){na()?(this.isInBackground="hidden"===document.visibilityState,document.addEventListener("visibilitychange",this.appVisibilityChangedListener)):this.isInBackground=!1}},{key:"removeAppVisibilityListener",value:function(){na()&&document.removeEventListener("visibilitychange",this.appVisibilityChangedListener)}}]),r}();function Li(e,t){var n,r;n=t.srcObject instanceof MediaStream?t.srcObject:new MediaStream,(r="audio"===e.kind?n.getAudioTracks():n.getVideoTracks()).includes(e)||(r.forEach((function(e){n.removeTrack(e)})),n.addTrack(e)),ea()&&t instanceof HTMLVideoElement||(t.autoplay=!0),t.muted=0===n.getAudioTracks().length,t instanceof HTMLVideoElement&&(t.playsInline=!0),t.srcObject!==n&&(t.srcObject=n,(ea()||$i())&&t instanceof HTMLVideoElement&&setTimeout((function(){t.srcObject=n,t.play().catch((function(){}))}),0))}function Ai(e,t){if(t.srcObject instanceof MediaStream){var n=t.srcObject;n.removeTrack(e),n.getTracks().length>0?t.srcObject=n:t.srcObject=null}}function Ui(e,t,n){var r,i=null!==(r=function(e){if(void 0!==e)return"function"==typeof structuredClone?structuredClone(e):JSON.parse(JSON.stringify(e))}(e))&&void 0!==r?r:{};return!0===i.audio&&(i.audio={}),!0===i.video&&(i.video={}),i.audio&&Bi(i.audio,t),i.video&&Bi(i.video,n),i}function Bi(e,t){return Object.keys(t).forEach((function(n){void 0===e[n]&&(e[n]=t[n])})),e}function Fi(e){var t={};if(e.video)if("object"===i(e.video)){var n={},r=n,a=e.video;Object.keys(a).forEach((function(e){switch(e){case"resolution":Bi(r,a.resolution);break;default:r[e]=a[e]}})),t.video=n}else t.video=e.video;else t.video=!1;return e.audio?"object"===i(e.audio)?t.audio=e.audio:t.audio=!0:t.audio=!1,t}function Ji(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:200;return Xt(this,void 0,void 0,n().mark((function r(){var i,a,o,s,c;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(!(i=ji())){n.next=14;break}return(a=i.createAnalyser()).fftSize=2048,o=a.frequencyBinCount,s=new Uint8Array(o),i.createMediaStreamSource(new MediaStream([e.mediaStreamTrack])).connect(a),n.next=10,Wi(t);case 10:return a.getByteTimeDomainData(s),c=s.some((function(e){return 128!==e&&0!==e})),i.close(),n.abrupt("return",!c);case 14:return n.abrupt("return",!1);case 15:case"end":return n.stop()}}),r)})))}function ji(){var e="undefined"!=typeof window&&(window.AudioContext||window.webkitAudioContext);if(e)return new e({latencyHint:"interactive"})}function qi(e){var t,n,r=null===(t=e.video)||void 0===t||t;return e.resolution&&(r="boolean"==typeof r?{}:r,r=ea()?Object.assign(Object.assign({},r),{width:{max:e.resolution.width},height:{max:e.resolution.height},frameRate:e.resolution.frameRate}):Object.assign(Object.assign({},r),{width:{ideal:e.resolution.width},height:{ideal:e.resolution.height},frameRate:e.resolution.frameRate})),{audio:null!==(n=e.audio)&&void 0!==n&&n,video:r,controller:e.controller,selfBrowserSurface:e.selfBrowserSurface,surfaceSwitching:e.surfaceSwitching,systemAudio:e.systemAudio}}function Vi(e){var t=e.split("/")[1].toLowerCase();if(!Ci.includes(t))throw Error("Video codec not supported: ".concat(t));return t}!function(e){var t,n,r;!function(e){e.Audio="audio",e.Video="video",e.Unknown="unknown"}(t=e.Kind||(e.Kind={})),function(e){e.Camera="camera",e.Microphone="microphone",e.ScreenShare="screen_share",e.ScreenShareAudio="screen_share_audio",e.Unknown="unknown"}(n=e.Source||(e.Source={})),function(e){e.Active="active",e.Paused="paused",e.Unknown="unknown"}(r=e.StreamState||(e.StreamState={})),e.kindToProto=function(e){switch(e){case t.Audio:return vt.AUDIO;case t.Video:return vt.VIDEO;default:return vt.DATA}},e.kindFromProto=function(e){switch(e){case vt.AUDIO:return t.Audio;case vt.VIDEO:return t.Video;default:return t.Unknown}},e.sourceToProto=function(e){switch(e){case n.Camera:return kt.CAMERA;case n.Microphone:return kt.MICROPHONE;case n.ScreenShare:return kt.SCREEN_SHARE;case n.ScreenShareAudio:return kt.SCREEN_SHARE_AUDIO;default:return kt.UNKNOWN}},e.sourceFromProto=function(e){switch(e){case kt.CAMERA:return n.Camera;case kt.MICROPHONE:return n.Microphone;case kt.SCREEN_SHARE:return n.ScreenShare;case kt.SCREEN_SHARE_AUDIO:return n.ScreenShareAudio;default:return n.Unknown}},e.streamStateFromProto=function(e){switch(e){case Sr.ACTIVE:return r.Active;case Sr.PAUSED:return r.Paused;default:return r.Unknown}}}(Mi||(Mi={}));var Hi="https://aomediacodec.github.io/av1-rtp-spec/#dependency-descriptor-rtp-header-extension";function Wi(e){return Xt(this,void 0,void 0,n().mark((function t(){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",new Promise((function(t){return mi.setTimeout(t,e)})));case 1:case"end":return t.stop()}}),t)})))}function Gi(){return"addTransceiver"in RTCPeerConnection.prototype}function Ki(){return"addTrack"in RTCPeerConnection.prototype}function zi(){if(!("getCapabilities"in RTCRtpSender))return!1;var e=RTCRtpSender.getCapabilities("video"),t=!1;if(e){var n,r=S(e.codecs);try{for(r.s();!(n=r.n()).done;){if("video/AV1"===n.value.mimeType){t=!0;break}}}catch(e){r.e(e)}finally{r.f()}}return t}function Qi(){if(!("getCapabilities"in RTCRtpSender))return!1;if($i())return!1;var e=RTCRtpSender.getCapabilities("video"),t=!1;if(e){var n,r=S(e.codecs);try{for(r.s();!(n=r.n()).done;){if("video/VP9"===n.value.mimeType){t=!0;break}}}catch(e){r.e(e)}finally{r.f()}}return t}function Yi(e){return"av1"===e||"vp9"===e}function Xi(e){return!!document&&(e||(e=document.createElement("audio")),"setSinkId"in e)}var Zi={Chrome:"100",Safari:"15",Firefox:"100"};function $i(){var e;return"Firefox"===(null===(e=gi())||void 0===e?void 0:e.name)}function ea(){var e;return"Safari"===(null===(e=gi())||void 0===e?void 0:e.name)}function ta(){return!!na()&&/Tablet|iPad|Mobile|Android|BlackBerry/.test(navigator.userAgent)}function na(){return"undefined"!=typeof document}function ra(){return"ReactNative"==navigator.product}function ia(e){return e.hostname.endsWith(".livekit.cloud")||e.hostname.endsWith(".livekit.run")}function aa(){if(global&&global.LiveKitReactNativeGlobal)return global.LiveKitReactNativeGlobal}function oa(){if(ra()){var e=aa();return e?e.platform:void 0}}function sa(){if(na())return window.devicePixelRatio;if(ra()){var e=aa();if(e)return e.devicePixelRatio}return 1}function ca(e,t){for(var n=e.split("."),r=t.split("."),i=Math.min(n.length,r.length),a=0;i>a;++a){var o=parseInt(n[a],10),s=parseInt(r[a],10);if(o>s)return 1;if(s>o)return-1;if(a===i-1&&o===s)return 0}return""===e&&""!==t?-1:""===t?1:n.length==r.length?0:n.length<r.length?-1:1}function ua(e){var t,n=S(e);try{for(n.s();!(t=n.n()).done;){var r=t.value;r.target.handleResize(r)}}catch(e){n.e(e)}finally{n.f()}}function da(e){var t,n=S(e);try{for(n.s();!(t=n.n()).done;){var r=t.value;r.target.handleVisibilityChanged(r)}}catch(e){n.e(e)}finally{n.f()}}var la,ha=null,fa=function(){return ha||(ha=new ResizeObserver(ua)),ha},pa=null,ma=function(){return pa||(pa=new IntersectionObserver(da,{root:null,rootMargin:"0px"})),pa};function va(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:16,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:16,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=document.createElement("canvas");i.width=e,i.height=t;var a=i.getContext("2d");null==a||a.fillRect(0,0,i.width,i.height),r&&a&&(a.beginPath(),a.arc(e/2,t/2,50,0,2*Math.PI,!0),a.closePath(),a.fillStyle="grey",a.fill());var o=k(i.captureStream().getTracks(),1)[0];if(!o)throw Error("Could not get empty media stream video track");return o.enabled=n,o}function ka(){if(!la){var e=new AudioContext,t=e.createOscillator(),n=e.createGain();n.gain.setValueAtTime(0,0);var r=e.createMediaStreamDestination();t.connect(n),n.connect(r),t.start();var i=k(r.stream.getAudioTracks(),1);if(!(la=i[0]))throw Error("Could not get empty media stream audio track");la.enabled=!1}return la.clone()}var ga,ya=u((function e(t,r){var i=this;s(this,e),this.onFinally=r,this.promise=new Promise((function(e,r){return Xt(i,void 0,void 0,n().mark((function i(){return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(this.resolve=e,this.reject=r,!t){n.next=5;break}return n.next=5,t(e,r);case 5:case"end":return n.stop()}}),i,this)})))})).finally((function(){var e;return null===(e=i.onFinally)||void 0===e?void 0:e.call(i)}))})),ba=function(){function e(){s(this,e),this._locking=Promise.resolve(),this._locks=0}return u(e,[{key:"isLocked",value:function(){return this._locks>0}},{key:"lock",value:function(){var e,t=this;this._locks+=1;var n=new Promise((function(n){return e=function(){t._locks-=1,n()}})),r=this._locking.then((function(){return e}));return this._locking=this._locking.then((function(){return n})),r}}]),e}();function Sa(e){if("string"==typeof e)return e;if(Array.isArray(e))return e[0];if(e.exact)return Array.isArray(e.exact)?e.exact[0]:e.exact;if(e.ideal)return Array.isArray(e.ideal)?e.ideal[0]:e.ideal;throw Error("could not unwrap constraint")}function wa(e){return e.startsWith("ws")?e.replace(/^(ws)/,"http"):e}!function(e){e[e.WAITING=0]="WAITING",e[e.RUNNING=1]="RUNNING",e[e.COMPLETED=2]="COMPLETED"}(ga||(ga={}));var Ta=function(){function e(){s(this,e),this.pendingTasks=new Map,this.taskMutex=new ba,this.nextTaskIndex=0}return u(e,[{key:"run",value:function(e){return Xt(this,void 0,void 0,n().mark((function t(){var r,i;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r={id:this.nextTaskIndex++,enqueuedAt:Date.now(),status:ga.WAITING},this.pendingTasks.set(r.id,r),t.next=4,this.taskMutex.lock();case 4:return i=t.sent,t.prev=5,r.executedAt=Date.now(),r.status=ga.RUNNING,t.next=10,e();case 10:return t.abrupt("return",t.sent);case 11:return t.prev=11,r.status=ga.COMPLETED,this.pendingTasks.delete(r.id),i(),t.finish(11);case 16:case"end":return t.stop()}}),t,this,[[5,,11,16]])})))}},{key:"flush",value:function(){return Xt(this,void 0,void 0,n().mark((function e(){var t=this;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.run((function(){return Xt(t,void 0,void 0,n().mark((function e(){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e)})))})));case 1:case"end":return e.stop()}}),e,this)})))}},{key:"snapshot",value:function(){return Array.from(this.pendingTasks.values())}}]),e}(),Ca=["syncState","trickle","offer","answer","simulate","leave"];function xa(e){var t=Ca.indexOf(e.case)>=0;return B.trace("request allowed to bypass queue:",{canPass:t,req:e}),t}var Pa=function(){function e(){var t=this;s(this,e);var n=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.rtt=0,this.resetCallbacks=function(){t.onAnswer=void 0,t.onLeave=void 0,t.onLocalTrackPublished=void 0,t.onLocalTrackUnpublished=void 0,t.onNegotiateRequested=void 0,t.onOffer=void 0,t.onRemoteMuteChanged=void 0,t.onSubscribedQualityUpdate=void 0,t.onTokenRefresh=void 0,t.onTrickle=void 0,t.onClose=void 0},this.isConnected=!1,this.isReconnecting=!1,this.useJSON=n,this.requestQueue=new Ta,this.queuedRequests=[],this.closingLock=new ba}return u(e,[{key:"join",value:function(e,t,r,i){return Xt(this,void 0,void 0,n().mark((function a(){var o;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return this.isConnected=!1,this.options=r,n.next=4,this.connect(e,t,r,i);case 4:return o=n.sent,n.abrupt("return",o);case 6:case"end":return n.stop()}}),a,this)})))}},{key:"reconnect",value:function(e,t,r,i){return Xt(this,void 0,void 0,n().mark((function a(){var o;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(this.options){n.next=3;break}return B.warn("attempted to reconnect without signal options being set, ignoring"),n.abrupt("return");case 3:return this.isReconnecting=!0,this.clearPingInterval(),n.next=7,this.connect(e,t,Object.assign(Object.assign({},this.options),{reconnect:!0,sid:r,reconnectReason:i}));case 7:return o=n.sent,n.abrupt("return",o);case 9:case"end":return n.stop()}}),a,this)})))}},{key:"connect",value:function(e,t,r,a){var o=this;this.connectOptions=r,e=(e=function(e){return e.startsWith("http")?e.replace(/^(http)/,"ws"):e}(e)).replace(/\/$/,""),e+="/rtc";var s,c,u=function(e,t,n){var r,i=new URLSearchParams;i.set("access_token",e),n.reconnect&&(i.set("reconnect","1"),n.sid&&i.set("sid",n.sid));i.set("auto_subscribe",n.autoSubscribe?"1":"0"),i.set("sdk",ra()?"reactnative":"js"),i.set("version",t.version),i.set("protocol",t.protocol.toString()),t.deviceModel&&i.set("device_model",t.deviceModel);t.os&&i.set("os",t.os);t.osVersion&&i.set("os_version",t.osVersion);t.browser&&i.set("browser",t.browser);t.browserVersion&&i.set("browser_version",t.browserVersion);void 0!==n.publishOnly&&i.set("publish",n.publishOnly);n.adaptiveStream&&i.set("adaptive_stream","1");n.reconnectReason&&i.set("reconnect_reason",n.reconnectReason.toString());(null===(r=navigator.connection)||void 0===r?void 0:r.type)&&i.set("network",navigator.connection.type);return"?".concat(i.toString())}(t,(c=new Vt({sdk:qt.JS,protocol:10,version:"1.15.0"}),ra()&&(c.os=null!==(s=oa())&&void 0!==s?s:""),c),r);return new Promise((function(t,s){return Xt(o,void 0,void 0,n().mark((function o(){var c,d,l=this;return n().wrap((function(o){for(;;)switch(o.prev=o.next){case 0:if(c=function(){return Xt(l,void 0,void 0,n().mark((function e(){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:this.close(),clearTimeout(d),s(new ui("room connection has been cancelled (signal)"));case 3:case"end":return e.stop()}}),e,this)})))},d=setTimeout((function(){l.close(),s(new ui("room connection has timed out (signal)"))}),r.websocketTimeout),(null==a?void 0:a.aborted)&&c(),null==a||a.addEventListener("abort",c),B.debug("connecting to ".concat(e+u)),!this.ws){o.next=8;break}return o.next=8,this.close();case 8:this.ws=new WebSocket(e+u),this.ws.binaryType="arraybuffer",this.ws.onopen=function(){clearTimeout(d)},this.ws.onerror=function(t){return Xt(l,void 0,void 0,n().mark((function r(){var i,a;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(this.isConnected){n.next=20;break}return clearTimeout(d),n.prev=2,n.next=5,fetch("http".concat(e.substring(2),"/validate").concat(u));case 5:if(!(i=n.sent).status.toFixed(0).startsWith("4")){n.next=13;break}return n.next=9,i.text();case 9:a=n.sent,s(new ui(a,0,i.status)),n.next=14;break;case 13:s(new ui("Internal error",2,i.status));case 14:n.next=19;break;case 16:n.prev=16,n.t0=n.catch(2),s(new ui("server was not reachable",1));case 19:return n.abrupt("return");case 20:this.handleWSError(t);case 21:case"end":return n.stop()}}),r,this,[[2,16]])})))},this.ws.onmessage=function(e){return Xt(l,void 0,void 0,n().mark((function o(){var u,d,l,h,f,p,m;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if("string"!=typeof e.data){n.next=5;break}p=JSON.parse(e.data),f=xr.fromJson(p),n.next=11;break;case 5:if(!(e.data instanceof ArrayBuffer)){n.next=9;break}f=xr.fromBinary(new Uint8Array(e.data)),n.next=11;break;case 9:return B.error("could not decode websocket message: ".concat(i(e.data))),n.abrupt("return");case 11:if(this.isConnected){n.next=16;break}if(m=!1,"join"===(null===(u=f.message)||void 0===u?void 0:u.case)?(this.isConnected=!0,null==a||a.removeEventListener("abort",c),this.pingTimeoutDuration=f.message.value.pingTimeout,this.pingIntervalDuration=f.message.value.pingInterval,this.pingTimeoutDuration&&this.pingTimeoutDuration>0&&(B.debug("ping config",{timeout:this.pingTimeoutDuration,interval:this.pingIntervalDuration}),this.startPingInterval()),t(f.message.value)):r.reconnect?(this.isConnected=!0,null==a||a.removeEventListener("abort",c),this.startPingInterval(),"reconnect"===(null===(d=f.message)||void 0===d?void 0:d.case)?t(null===(l=f.message)||void 0===l?void 0:l.value):(t(),m=!0)):r.reconnect||s(new ui("did not receive join response, got ".concat(null===(h=f.message)||void 0===h?void 0:h.case," instead"))),m){n.next=16;break}return n.abrupt("return");case 16:if(!this.signalLatency){n.next=19;break}return n.next=19,Wi(this.signalLatency);case 19:this.handleSignalResponse(f);case 20:case"end":return n.stop()}}),o,this)})))},this.ws.onclose=function(e){B.warn("websocket closed",{ev:e}),l.handleOnClose(e.reason)};case 14:case"end":return o.stop()}}),o,this)})))}))}},{key:"close",value:function(){return Xt(this,void 0,void 0,n().mark((function e(){var t,r,i=this;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.closingLock.lock();case 2:if(t=e.sent,e.prev=3,this.isConnected=!1,!this.ws){e.next=15;break}if(this.ws.onmessage=null,this.ws.onopen=null,this.ws.onclose=null,r=new Promise((function(e){i.ws?i.ws.onclose=function(){e()}:e()})),this.ws.readyState>=this.ws.CLOSING){e.next=14;break}return this.ws.close(),e.next=14,Promise.race([r,Wi(250)]);case 14:this.ws=void 0;case 15:return e.prev=15,this.clearPingInterval(),t(),e.finish(15);case 19:case"end":return e.stop()}}),e,this,[[3,,15,19]])})))}},{key:"sendOffer",value:function(e){B.debug("sending offer",e),this.sendRequest({case:"offer",value:Ra(e)})}},{key:"sendAnswer",value:function(e){return B.debug("sending answer"),this.sendRequest({case:"answer",value:Ra(e)})}},{key:"sendIceCandidate",value:function(e,t){return B.trace("sending ice candidate",e),this.sendRequest({case:"trickle",value:new Rr({candidateInit:JSON.stringify(e),target:t})})}},{key:"sendMuteTrack",value:function(e,t){return this.sendRequest({case:"mute",value:new Ir({sid:e,muted:t})})}},{key:"sendAddTrack",value:function(e){return this.sendRequest({case:"addTrack",value:e})}},{key:"sendUpdateLocalMetadata",value:function(e,t){return this.sendRequest({case:"updateMetadata",value:new Jr({metadata:e,name:t})})}},{key:"sendUpdateTrackSettings",value:function(e){this.sendRequest({case:"trackSetting",value:e})}},{key:"sendUpdateSubscription",value:function(e){return this.sendRequest({case:"subscription",value:e})}},{key:"sendSyncState",value:function(e){return this.sendRequest({case:"syncState",value:e})}},{key:"sendUpdateVideoLayers",value:function(e,t){return this.sendRequest({case:"updateLayers",value:new Fr({trackSid:e,layers:t})})}},{key:"sendUpdateSubscriptionPermissions",value:function(e,t){return this.sendRequest({case:"subscriptionPermission",value:new Zr({allParticipants:e,trackPermissions:t})})}},{key:"sendSimulateScenario",value:function(e){return this.sendRequest({case:"simulate",value:e})}},{key:"sendPing",value:function(){return Promise.all([this.sendRequest({case:"ping",value:pe.parse(Date.now())}),this.sendRequest({case:"pingReq",value:new ri({timestamp:pe.parse(Date.now()),rtt:pe.parse(this.rtt)})})])}},{key:"sendLeave",value:function(){return this.sendRequest({case:"leave",value:new Br({canReconnect:!1,reason:St.CLIENT_INITIATED})})}},{key:"sendRequest",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return Xt(this,void 0,void 0,n().mark((function r(){var i,a=this;return n().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(!(!t&&!xa(e))||!this.isReconnecting){r.next=4;break}return this.queuedRequests.push((function(){return Xt(a,void 0,void 0,n().mark((function t(){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.sendRequest(e,!0);case 2:case"end":return t.stop()}}),t,this)})))})),r.abrupt("return");case 4:if(t){r.next=7;break}return r.next=7,this.requestQueue.flush();case 7:if(!this.signalLatency){r.next=10;break}return r.next=10,Wi(this.signalLatency);case 10:if(this.ws&&this.ws.readyState===this.ws.OPEN){r.next=13;break}return B.error("cannot send signal request before connected, type: ".concat(null==e?void 0:e.case)),r.abrupt("return");case 13:i=new Cr({message:e});try{this.useJSON?this.ws.send(i.toJsonString()):this.ws.send(i.toBinary())}catch(e){B.error("error sending signal message",{error:e})}case 15:case"end":return r.stop()}}),r,this)})))}},{key:"handleSignalResponse",value:function(e){var t,n,r=e.message;if(null!=r){var i=!1;if("answer"===r.case){var a=Ea(r.value);this.onAnswer&&this.onAnswer(a)}else if("offer"===r.case){var o=Ea(r.value);this.onOffer&&this.onOffer(o)}else if("trickle"===r.case){var s=JSON.parse(r.value.candidateInit);this.onTrickle&&this.onTrickle(s,r.value.target)}else"update"===r.case?this.onParticipantUpdate&&this.onParticipantUpdate(null!==(t=r.value.participants)&&void 0!==t?t:[]):"trackPublished"===r.case?this.onLocalTrackPublished&&this.onLocalTrackPublished(r.value):"speakersChanged"===r.case?this.onSpeakersChanged&&this.onSpeakersChanged(null!==(n=r.value.speakers)&&void 0!==n?n:[]):"leave"===r.case?this.onLeave&&this.onLeave(r.value):"mute"===r.case?this.onRemoteMuteChanged&&this.onRemoteMuteChanged(r.value.sid,r.value.muted):"roomUpdate"===r.case?this.onRoomUpdate&&r.value.room&&this.onRoomUpdate(r.value.room):"connectionQuality"===r.case?this.onConnectionQuality&&this.onConnectionQuality(r.value):"streamStateUpdate"===r.case?this.onStreamStateUpdate&&this.onStreamStateUpdate(r.value):"subscribedQualityUpdate"===r.case?this.onSubscribedQualityUpdate&&this.onSubscribedQualityUpdate(r.value):"subscriptionPermissionUpdate"===r.case?this.onSubscriptionPermissionUpdate&&this.onSubscriptionPermissionUpdate(r.value):"refreshToken"===r.case?this.onTokenRefresh&&this.onTokenRefresh(r.value):"trackUnpublished"===r.case?this.onLocalTrackUnpublished&&this.onLocalTrackUnpublished(r.value):"subscriptionResponse"===r.case?this.onSubscriptionError&&this.onSubscriptionError(r.value):"pong"===r.case||("pongResp"===r.case?(this.rtt=Date.now()-Number.parseInt(r.value.lastPingTimestamp.toString()),this.resetPingTimeout(),i=!0):B.debug("unsupported message",r));i||this.resetPingTimeout()}else B.debug("received unsupported message")}},{key:"setReconnected",value:function(){for(;this.queuedRequests.length>0;){var e=this.queuedRequests.shift();e&&this.requestQueue.run(e)}this.isReconnecting=!1}},{key:"handleOnClose",value:function(e){return Xt(this,void 0,void 0,n().mark((function t(){var r;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this.isConnected){t.next=2;break}return t.abrupt("return");case 2:return r=this.onClose,t.next=5,this.close();case 5:B.debug("websocket connection closed: ".concat(e)),r&&r(e);case 7:case"end":return t.stop()}}),t,this)})))}},{key:"handleWSError",value:function(e){B.error("websocket error",e)}},{key:"resetPingTimeout",value:function(){var e=this;this.clearPingTimeout(),this.pingTimeoutDuration?this.pingTimeout=mi.setTimeout((function(){B.warn("ping timeout triggered. last pong received at: ".concat(new Date(Date.now()-1e3*e.pingTimeoutDuration).toUTCString())),e.handleOnClose("ping timeout")}),1e3*this.pingTimeoutDuration):B.warn("ping timeout duration not set")}},{key:"clearPingTimeout",value:function(){this.pingTimeout&&mi.clearTimeout(this.pingTimeout)}},{key:"startPingInterval",value:function(){var e=this;this.clearPingInterval(),this.resetPingTimeout(),this.pingIntervalDuration?(B.debug("start ping interval"),this.pingInterval=mi.setInterval((function(){e.sendPing()}),1e3*this.pingIntervalDuration)):B.warn("ping interval duration not set")}},{key:"clearPingInterval",value:function(){B.debug("clearing ping interval"),this.clearPingTimeout(),this.pingInterval&&mi.clearInterval(this.pingInterval)}}]),e}();function Ea(e){var t={type:"offer",sdp:e.sdp};switch(e.type){case"answer":case"offer":case"pranswer":case"rollback":t.type=e.type;break}return t}function Ra(e){return new Mr({sdp:e.sdp,type:e.type})}var Ia,Da,Na,Oa,_a="lk_e2ee";function Ma(){return void 0!==window.RTCRtpSender&&void 0!==window.RTCRtpSender.prototype.createEncodedStreams||La()}function La(){return void 0!==window.RTCRtpScriptTransform}function Aa(e,t,n){var r,i,a;void 0===t&&(t=50),void 0===n&&(n={});var o=null!=(r=n.isImmediate)&&r,s=null!=(i=n.callback)&&i,c=n.maxWait,u=Date.now(),d=[];function l(){if(void 0!==c){var e=Date.now()-u;if(e+t>=c)return c-e}return t}var h=function(){var t=[].slice.call(arguments),n=this;return new Promise((function(r,i){var c=o&&void 0===a;if(void 0!==a&&clearTimeout(a),a=setTimeout((function(){if(a=void 0,u=Date.now(),!o){var r=e.apply(n,t);s&&s(r),d.forEach((function(e){return(0,e.resolve)(r)})),d=[]}}),l()),c){var h=e.apply(n,t);return s&&s(h),r(h)}d.push({resolve:r,reject:i})}))};return h.cancel=function(e){void 0!==a&&clearTimeout(a),d.forEach((function(t){return(0,t.reject)(e)})),d=[]},h}!function(e){e.SetKey="setKey",e.RatchetRequest="ratchetRequest",e.KeyRatcheted="keyRatcheted"}(Ia||(Ia={})),function(e){e.KeyRatcheted="keyRatcheted"}(Da||(Da={})),function(e){e.ParticipantEncryptionStatusChanged="participantEncryptionStatusChanged",e.EncryptionError="encryptionError"}(Na||(Na={})),function(e){e.Error="cryptorError"}(Oa||(Oa={}));var Ua="default",Ba=function(){function e(){s(this,e)}return u(e,[{key:"getDevices",value:function(t){var r,i=1>=arguments.length||void 0===arguments[1]||arguments[1];return Xt(this,void 0,void 0,n().mark((function a(){var o,s,c;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if((null===(r=e.userMediaPromiseMap)||void 0===r?void 0:r.size)<=0){n.next=15;break}if(B.debug("awaiting getUserMedia promise"),n.prev=2,!t){n.next=8;break}return n.next=6,e.userMediaPromiseMap.get(t);case 6:n.next=10;break;case 8:return n.next=10,Promise.all(e.userMediaPromiseMap.values());case 10:n.next=15;break;case 12:n.prev=12,n.t0=n.catch(2),B.warn("error waiting for media permissons");case 15:return n.next=17,navigator.mediaDevices.enumerateDevices();case 17:if(o=n.sent,!i||ea()&&this.hasDeviceInUse(t)){n.next=29;break}if(!(0===o.length||o.some((function(e){var n=""===e.label,r=!t||e.kind===t;return n&&r})))){n.next=29;break}return s={video:"audioinput"!==t&&"audiooutput"!==t,audio:"videoinput"!==t},n.next=24,navigator.mediaDevices.getUserMedia(s);case 24:return c=n.sent,n.next=27,navigator.mediaDevices.enumerateDevices();case 27:o=n.sent,c.getTracks().forEach((function(e){e.stop()}));case 29:return t&&(o=o.filter((function(e){return e.kind===t}))),n.abrupt("return",o);case 31:case"end":return n.stop()}}),a,this,[[2,12]])})))}},{key:"normalizeDeviceId",value:function(e,t,r){return Xt(this,void 0,void 0,n().mark((function i(){var a,o;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(t===Ua){n.next=2;break}return n.abrupt("return",t);case 2:return n.next=4,this.getDevices(e);case 4:return a=n.sent,o=a.find((function(e){return e.groupId===r&&e.deviceId!==Ua})),n.abrupt("return",null==o?void 0:o.deviceId);case 7:case"end":return n.stop()}}),i,this)})))}},{key:"hasDeviceInUse",value:function(t){return t?e.userMediaPromiseMap.has(t):e.userMediaPromiseMap.size>0}}],[{key:"getInstance",value:function(){return void 0===this.instance&&(this.instance=new e),this.instance}}]),e}();Ba.mediaDeviceKinds=["audioinput","audiooutput","videoinput"],Ba.userMediaPromiseMap=new Map;var Fa=function(t){function r(t,i,a){var o;s(this,r);var c=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return(o=e(this,r,[t,i]))._isUpstreamPaused=!1,o.handleTrackMuteEvent=function(){return o.debouncedTrackMuteHandler().catch((function(){return B.debug("track mute bounce got cancelled by an unmute event")}))},o.debouncedTrackMuteHandler=Aa((function(){return Xt(m(o),void 0,void 0,n().mark((function e(){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.pauseUpstream();case 2:case"end":return e.stop()}}),e,this)})))}),5e3),o.handleTrackUnmuteEvent=function(){return Xt(m(o),void 0,void 0,n().mark((function e(){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.debouncedTrackMuteHandler.cancel("unmute"),e.next=3,this.resumeUpstream();case 3:case"end":return e.stop()}}),e,this)})))},o.handleEnded=function(){o.isInBackground&&(o.reacquireTrack=!0),o._mediaStreamTrack.removeEventListener("mute",o.handleTrackMuteEvent),o._mediaStreamTrack.removeEventListener("unmute",o.handleTrackUnmuteEvent),o.emit(Ii.Ended,m(o))},o.reacquireTrack=!1,o.providedByUser=c,o.muteLock=new ba,o.pauseUpstreamLock=new ba,o.processorLock=new ba,o.setMediaStreamTrack(t,!0),o._constraints=t.getConstraints(),a&&(o._constraints=a),o}return l(r,Mi),u(r,[{key:"constraints",get:function(){return this._constraints}},{key:"id",get:function(){return this._mediaStreamTrack.id}},{key:"dimensions",get:function(){if(this.kind===Mi.Kind.Video){var e=this._mediaStreamTrack.getSettings(),t=e.width,n=e.height;return t&&n?{width:t,height:n}:void 0}}},{key:"isUpstreamPaused",get:function(){return this._isUpstreamPaused}},{key:"isUserProvided",get:function(){return this.providedByUser}},{key:"mediaStreamTrack",get:function(){var e,t;return null!==(t=null===(e=this.processor)||void 0===e?void 0:e.processedTrack)&&void 0!==t?t:this._mediaStreamTrack}},{key:"setMediaStreamTrack",value:function(e,t){return Xt(this,void 0,void 0,n().mark((function r(){var i,a=this;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(e!==this._mediaStreamTrack||t){n.next=2;break}return n.abrupt("return");case 2:if(this._mediaStreamTrack&&(this.attachedElements.forEach((function(e){Ai(a._mediaStreamTrack,e)})),this.debouncedTrackMuteHandler.cancel("new-track"),this._mediaStreamTrack.removeEventListener("ended",this.handleEnded),this._mediaStreamTrack.removeEventListener("mute",this.handleTrackMuteEvent),this._mediaStreamTrack.removeEventListener("unmute",this.handleTrackUnmuteEvent),this.providedByUser||this._mediaStreamTrack===e||this._mediaStreamTrack.stop()),this.mediaStream=new MediaStream([e]),e&&(e.addEventListener("ended",this.handleEnded),e.addEventListener("mute",this.handleTrackMuteEvent),e.addEventListener("unmute",this.handleTrackUnmuteEvent),this._constraints=e.getConstraints()),!(this.processor&&e&&this.processorElement)){n.next=13;break}if(B.debug("restarting processor"),"unknown"!==this.kind){n.next=9;break}throw TypeError("cannot set processor on track of unknown kind");case 9:return Li(e,this.processorElement),n.next=12,this.processor.restart({track:e,kind:this.kind,element:this.processorElement});case 12:i=this.processor.processedTrack;case 13:if(!this.sender){n.next=16;break}return n.next=16,this.sender.replaceTrack(null!=i?i:e);case 16:if(this._mediaStreamTrack=e,!e){n.next=22;break}return this._mediaStreamTrack.enabled=!this.isMuted,n.next=21,this.resumeUpstream();case 21:this.attachedElements.forEach((function(t){Li(null!=i?i:e,t)}));case 22:case"end":return n.stop()}}),r,this)})))}},{key:"waitForDimensions",value:function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1e3;return Xt(this,void 0,void 0,n().mark((function r(){var i,a;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(this.kind!==Mi.Kind.Audio){n.next=2;break}throw new Error("cannot get dimensions for audio tracks");case 2:if("iOS"!==(null===(e=gi())||void 0===e?void 0:e.os)){n.next=5;break}return n.next=5,Wi(10);case 5:i=Date.now();case 6:if(Date.now()-i>=t){n.next=14;break}if(!(a=this.dimensions)){n.next=10;break}return n.abrupt("return",a);case 10:return n.next=12,Wi(50);case 12:n.next=6;break;case 14:throw new li("unable to get track dimensions after timeout");case 15:case"end":return n.stop()}}),r,this)})))}},{key:"getDeviceId",value:function(){return Xt(this,void 0,void 0,n().mark((function e(){var t,r,i,a;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.source!==Mi.Source.ScreenShare){e.next=2;break}return e.abrupt("return");case 2:return t=this._mediaStreamTrack.getSettings(),r=t.deviceId,i=t.groupId,a=this.kind===Mi.Kind.Audio?"audioinput":"videoinput",e.abrupt("return",Ba.getInstance().normalizeDeviceId(a,r,i));case 5:case"end":return e.stop()}}),e,this)})))}},{key:"mute",value:function(){return Xt(this,void 0,void 0,n().mark((function e(){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.setTrackMuted(!0),e.abrupt("return",this);case 2:case"end":return e.stop()}}),e,this)})))}},{key:"unmute",value:function(){return Xt(this,void 0,void 0,n().mark((function e(){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.setTrackMuted(!1),e.abrupt("return",this);case 2:case"end":return e.stop()}}),e,this)})))}},{key:"replaceTrack",value:function(e){var t=1>=arguments.length||void 0===arguments[1]||arguments[1];return Xt(this,void 0,void 0,n().mark((function r(){return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(this.sender){n.next=2;break}throw new li("unable to replace an unpublished track");case 2:return B.debug("replace MediaStreamTrack"),n.next=5,this.setMediaStreamTrack(e);case 5:if(this.providedByUser=t,!this.processor){n.next=9;break}return n.next=9,this.stopProcessor();case 9:return n.abrupt("return",this);case 10:case"end":return n.stop()}}),r,this)})))}},{key:"restart",value:function(e){return Xt(this,void 0,void 0,n().mark((function t(){var r,i,a,o=this;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e||(e=this._constraints),B.debug("restarting track with constraints",e),r={audio:!1,video:!1},this.kind===Mi.Kind.Video?r.video=e:r.audio=e,this.attachedElements.forEach((function(e){Ai(o.mediaStreamTrack,e)})),this._mediaStreamTrack.removeEventListener("ended",this.handleEnded),this._mediaStreamTrack.stop(),t.next=9,navigator.mediaDevices.getUserMedia(r);case 9:return i=t.sent,(a=i.getTracks()[0]).addEventListener("ended",this.handleEnded),B.debug("re-acquired MediaStreamTrack"),t.next=15,this.setMediaStreamTrack(a);case 15:return this._constraints=e,this.emit(Ii.Restarted,this),t.abrupt("return",this);case 18:case"end":return t.stop()}}),t,this)})))}},{key:"setTrackMuted",value:function(e){B.debug("setting ".concat(this.kind," track ").concat(e?"muted":"unmuted")),this.isMuted===e&&this._mediaStreamTrack.enabled!==e||(this.isMuted=e,this._mediaStreamTrack.enabled=!e,this.emit(e?Ii.Muted:Ii.Unmuted,this))}},{key:"needsReAcquisition",get:function(){return"live"!==this._mediaStreamTrack.readyState||this._mediaStreamTrack.muted||!this._mediaStreamTrack.enabled||this.reacquireTrack}},{key:"handleAppVisibilityChanged",value:function(){var e=this,t=Object.create(null,{handleAppVisibilityChanged:{get:function(){return v(h(r.prototype),"handleAppVisibilityChanged",e)}}});return Xt(this,void 0,void 0,n().mark((function e(){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.handleAppVisibilityChanged.call(this);case 2:if(ta()){e.next=4;break}return e.abrupt("return");case 4:if(B.debug("visibility changed, is in Background: ".concat(this.isInBackground)),this.isInBackground||!this.needsReAcquisition||this.isUserProvided||this.isMuted){e.next=10;break}return B.debug("track needs to be reacquired, restarting ".concat(this.source)),e.next=9,this.restart();case 9:this.reacquireTrack=!1;case 10:case"end":return e.stop()}}),e,this)})))}},{key:"stop",value:function(){var e;v(h(r.prototype),"stop",this).call(this),this._mediaStreamTrack.removeEventListener("ended",this.handleEnded),this._mediaStreamTrack.removeEventListener("mute",this.handleTrackMuteEvent),this._mediaStreamTrack.removeEventListener("unmute",this.handleTrackUnmuteEvent),null===(e=this.processor)||void 0===e||e.destroy(),this.processor=void 0}},{key:"pauseUpstream",value:function(){return Xt(this,void 0,void 0,n().mark((function e(){var t,r;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.pauseUpstreamLock.lock();case 2:if(t=e.sent,e.prev=3,!0!==this._isUpstreamPaused){e.next=6;break}return e.abrupt("return");case 6:if(this.sender){e.next=9;break}return B.warn("unable to pause upstream for an unpublished track"),e.abrupt("return");case 9:if(this._isUpstreamPaused=!0,this.emit(Ii.UpstreamPaused,this),"Safari"!==(null==(r=gi())?void 0:r.name)||ca(r.version,"12.0")>=0){e.next=14;break}throw new di("pauseUpstream is not supported on Safari < 12.");case 14:return e.next=16,this.sender.replaceTrack(null);case 16:return e.prev=16,t(),e.finish(16);case 19:case"end":return e.stop()}}),e,this,[[3,,16,19]])})))}},{key:"resumeUpstream",value:function(){return Xt(this,void 0,void 0,n().mark((function e(){var t;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.pauseUpstreamLock.lock();case 2:if(t=e.sent,e.prev=3,!1!==this._isUpstreamPaused){e.next=6;break}return e.abrupt("return");case 6:if(this.sender){e.next=9;break}return B.warn("unable to resume upstream for an unpublished track"),e.abrupt("return");case 9:return this._isUpstreamPaused=!1,this.emit(Ii.UpstreamResumed,this),e.next=13,this.sender.replaceTrack(this._mediaStreamTrack);case 13:return e.prev=13,t(),e.finish(13);case 16:case"end":return e.stop()}}),e,this,[[3,,13,16]])})))}},{key:"getRTCStatsReport",value:function(){var e;return Xt(this,void 0,void 0,n().mark((function t(){var r;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(null===(e=this.sender)||void 0===e?void 0:e.getStats){t.next=2;break}return t.abrupt("return");case 2:return t.next=4,this.sender.getStats();case 4:return r=t.sent,t.abrupt("return",r);case 6:case"end":return t.stop()}}),t,this)})))}},{key:"setProcessor",value:function(e){var t,r,i=1>=arguments.length||void 0===arguments[1]||arguments[1];return Xt(this,void 0,void 0,n().mark((function a(){var o,s,c,u,d;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,this.processorLock.lock();case 2:if(o=n.sent,n.prev=3,B.debug("setting up processor"),!this.processor){n.next=8;break}return n.next=8,this.stopProcessor();case 8:if("unknown"!==this.kind){n.next=10;break}throw TypeError("cannot set processor on track of unknown kind");case 10:return this.processorElement=null!==(t=this.processorElement)&&void 0!==t?t:document.createElement(this.kind),this.processorElement.muted=!0,Li(this._mediaStreamTrack,this.processorElement),this.processorElement.play().catch((function(e){return B.error("failed to play processor element",{error:e})})),s={kind:this.kind,track:this._mediaStreamTrack,element:this.processorElement},n.next=17,e.init(s);case 17:if(this.processor=e,!this.processor.processedTrack){n.next=23;break}c=S(this.attachedElements);try{for(c.s();!(u=c.n()).done;)(d=u.value)!==this.processorElement&&i&&(Ai(this._mediaStreamTrack,d),Li(this.processor.processedTrack,d))}catch(e){c.e(e)}finally{c.f()}return n.next=23,null===(r=this.sender)||void 0===r?void 0:r.replaceTrack(this.processor.processedTrack);case 23:return n.prev=23,o(),n.finish(23);case 26:case"end":return n.stop()}}),a,this,[[3,,23,26]])})))}},{key:"getProcessor",value:function(){return this.processor}},{key:"stopProcessor",value:function(){var e,t;return Xt(this,void 0,void 0,n().mark((function r(){return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(this.processor){n.next=2;break}return n.abrupt("return");case 2:return B.debug("stopping processor"),null===(e=this.processor.processedTrack)||void 0===e||e.stop(),n.next=6,this.processor.destroy();case 6:return this.processor=void 0,null===(t=this.processorElement)||void 0===t||t.remove(),this.processorElement=void 0,n.next=11,this.restart();case 11:case"end":return n.stop()}}),r,this)})))}}]),r}(),Ja=function(t){function r(t){var n;return s(this,r),(n=e(this,r)).onWorkerMessage=function(e){var t,r,i=e.data,a=i.kind,o=i.data;switch(a){case"error":B.error(o.error.message),n.emit(Na.EncryptionError,o.error);break;case"initAck":o.enabled&&n.keyProvider.getKeys().forEach((function(e){n.postKey(e)}));break;case"enable":if(n.encryptionEnabled!==o.enabled&&o.participantIdentity===(null===(t=n.room)||void 0===t?void 0:t.localParticipant.identity))n.emit(Na.ParticipantEncryptionStatusChanged,o.enabled,n.room.localParticipant),n.encryptionEnabled=o.enabled;else if(o.participantIdentity){var s=null===(r=n.room)||void 0===r?void 0:r.getParticipantByIdentity(o.participantIdentity);if(!s)throw TypeError("couldn't set encryption status, participant not found".concat(o.participantIdentity));n.emit(Na.ParticipantEncryptionStatusChanged,o.enabled,s)}n.encryptionEnabled&&n.keyProvider.getKeys().forEach((function(e){n.postKey(e)}));break;case"ratchetKey":n.keyProvider.emit(Ia.KeyRatcheted,o.material,o.keyIndex);break}},n.onWorkerError=function(e){B.error("e2ee worker encountered an error:",{error:e.error}),n.emit(Na.EncryptionError,e.error)},n.keyProvider=t.keyProvider,n.worker=t.worker,n.encryptionEnabled=!1,n}return l(r,kn.EventEmitter),u(r,[{key:"setup",value:function(e){if(!Ma())throw new di("tried to setup end-to-end encryption on an unsupported browser");if(B.info("setting up e2ee"),e!==this.room){this.room=e,this.setupEventListeners(e,this.keyProvider);var t={kind:"init",data:{keyProviderOptions:this.keyProvider.getOptions()}};this.worker&&(B.info("initializing worker",{worker:this.worker}),this.worker.onmessage=this.onWorkerMessage,this.worker.onerror=this.onWorkerError,this.worker.postMessage(t))}}},{key:"setParticipantCryptorEnabled",value:function(e,t){B.debug("set e2ee to ".concat(e," for participant ").concat(t)),this.postEnable(e,t)}},{key:"setSifTrailer",value:function(e){e&&0!==e.length?this.postSifTrailer(e):B.warn("ignoring server sent trailer as it's empty")}},{key:"setupEngine",value:function(e){var t=this;e.on(Ri.RTPVideoMapUpdate,(function(e){t.postRTPMap(e)}))}},{key:"setupEventListeners",value:function(e,t){var r=this;e.on(Pi.TrackPublished,(function(e,t){return r.setParticipantCryptorEnabled(e.trackInfo.encryption!==It.NONE,t.identity)})),e.on(Pi.ConnectionStateChanged,(function(t){t===Xo.Connected&&e.participants.forEach((function(e){e.tracks.forEach((function(t){r.setParticipantCryptorEnabled(t.trackInfo.encryption!==It.NONE,e.identity)}))}))})).on(Pi.TrackUnsubscribed,(function(e,t,n){var i,a={kind:"removeTransform",data:{participantIdentity:n.identity,trackId:e.mediaStreamID}};null===(i=r.worker)||void 0===i||i.postMessage(a)})).on(Pi.TrackSubscribed,(function(e,t,n){r.setupE2EEReceiver(e,n.identity,t.trackInfo)})).on(Pi.SignalConnected,(function(){if(!r.room)throw new TypeError("expected room to be present on signal connect");r.setParticipantCryptorEnabled(r.room.localParticipant.isE2EEEnabled,r.room.localParticipant.identity),t.getKeys().forEach((function(e){r.postKey(e)}))})),e.localParticipant.on(Ei.LocalTrackPublished,(function(e){return Xt(r,void 0,void 0,n().mark((function t(){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:this.setupE2EESender(e.track,e.track.sender);case 1:case"end":return t.stop()}}),t,this)})))})),t.on(Ia.SetKey,(function(e){return r.postKey(e)})).on(Ia.RatchetRequest,(function(e,t){return r.postRatchetRequest(e,t)}))}},{key:"postRatchetRequest",value:function(e,t){if(!this.worker)throw Error("could not ratchet key, worker is missing");var n={kind:"ratchetRequest",data:{participantIdentity:e,keyIndex:t}};this.worker.postMessage(n)}},{key:"postKey",value:function(e){var t,n=e.key,r=e.participantIdentity,i=e.keyIndex;if(!this.worker)throw Error("could not set key, worker is missing");var a={kind:"setKey",data:{participantIdentity:r,isPublisher:r===(null===(t=this.room)||void 0===t?void 0:t.localParticipant.identity),key:n,keyIndex:i}};this.worker.postMessage(a)}},{key:"postEnable",value:function(e,t){if(!this.worker)throw new ReferenceError("failed to enable e2ee, worker is not ready");var n={kind:"enable",data:{enabled:e,participantIdentity:t}};this.worker.postMessage(n)}},{key:"postRTPMap",value:function(e){var t;if(!this.worker)throw TypeError("could not post rtp map, worker is missing");if(!(null===(t=this.room)||void 0===t?void 0:t.localParticipant.identity))throw TypeError("could not post rtp map, local participant identity is missing");var n={kind:"setRTPMap",data:{map:e,participantIdentity:this.room.localParticipant.identity}};this.worker.postMessage(n)}},{key:"postSifTrailer",value:function(e){if(!this.worker)throw Error("could not post SIF trailer, worker is missing");var t={kind:"setSifTrailer",data:{trailer:e}};this.worker.postMessage(t)}},{key:"setupE2EEReceiver",value:function(e,t,n){if(e.receiver){if(!(null==n?void 0:n.mimeType)||""===n.mimeType)throw new TypeError("MimeType missing from trackInfo, cannot set up E2EE cryptor");this.handleReceiver(e.receiver,e.mediaStreamID,t,"video"===e.kind?Vi(n.mimeType):void 0)}}},{key:"setupE2EESender",value:function(e,t){e instanceof Fa&&t?this.handleSender(t,e.mediaStreamID,void 0):t||B.warn("early return because sender is not ready")}},{key:"handleReceiver",value:function(e,t,r,i){return Xt(this,void 0,void 0,n().mark((function a(){var o,s,c,u,d,l;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(this.worker){n.next=2;break}return n.abrupt("return");case 2:if(!La()){n.next=7;break}o={kind:"decode",participantIdentity:r,trackId:t,codec:i},e.transform=new RTCRtpScriptTransform(this.worker,o),n.next=16;break;case 7:if(!(_a in e)||!i){n.next=11;break}return s={kind:"updateCodec",data:{trackId:t,codec:i,participantIdentity:r}},this.worker.postMessage(s),n.abrupt("return");case 11:c=e.writableStream,u=e.readableStream,c&&u||(d=e.createEncodedStreams(),e.writableStream=d.writable,c=d.writable,e.readableStream=d.readable,u=d.readable),l={kind:"decode",data:{readableStream:u,writableStream:c,trackId:t,codec:i,participantIdentity:r}},this.worker.postMessage(l,[u,c]);case 16:e[_a]=!0;case 17:case"end":return n.stop()}}),a,this)})))}},{key:"handleSender",value:function(e,t,n){var r;if(!(_a in e)&&this.worker){if(!(null===(r=this.room)||void 0===r?void 0:r.localParticipant.identity)||""===this.room.localParticipant.identity)throw TypeError("local identity needs to be known in order to set up encrypted sender");if(La()){B.info("initialize script transform");var i={kind:"encode",participantIdentity:this.room.localParticipant.identity,trackId:t,codec:n};e.transform=new RTCRtpScriptTransform(this.worker,i)}else{B.info("initialize encoded streams");var a=e.createEncodedStreams(),o={kind:"encode",data:{readableStream:a.readable,writableStream:a.writable,codec:n,trackId:t,participantIdentity:this.room.localParticipant.identity}};this.worker.postMessage(o,[a.readable,a.writable])}e[_a]=!0}}}]),r}(),ja={},qa={exports:{}},Va=qa.exports={v:[{name:"version",reg:/^(\d*)$/}],o:[{name:"origin",reg:/^(\S*) (\d*) (\d*) (\S*) IP(\d) (\S*)/,names:["username","sessionId","sessionVersion","netType","ipVer","address"],format:"%s %s %d %s IP%d %s"}],s:[{name:"name"}],i:[{name:"description"}],u:[{name:"uri"}],e:[{name:"email"}],p:[{name:"phone"}],z:[{name:"timezones"}],r:[{name:"repeats"}],t:[{name:"timing",reg:/^(\d*) (\d*)/,names:["start","stop"],format:"%d %d"}],c:[{name:"connection",reg:/^IN IP(\d) (\S*)/,names:["version","ip"],format:"IN IP%d %s"}],b:[{push:"bandwidth",reg:/^(TIAS|AS|CT|RR|RS):(\d*)/,names:["type","limit"],format:"%s:%s"}],m:[{reg:/^(\w*) (\d*) ([\w/]*)(?: (.*))?/,names:["type","port","protocol","payloads"],format:"%s %d %s %s"}],a:[{push:"rtp",reg:/^rtpmap:(\d*) ([\w\-.]*)(?:\s*\/(\d*)(?:\s*\/(\S*))?)?/,names:["payload","codec","rate","encoding"],format:function(e){return e.encoding?"rtpmap:%d %s/%s/%s":e.rate?"rtpmap:%d %s/%s":"rtpmap:%d %s"}},{push:"fmtp",reg:/^fmtp:(\d*) ([\S| ]*)/,names:["payload","config"],format:"fmtp:%d %s"},{name:"control",reg:/^control:(.*)/,format:"control:%s"},{name:"rtcp",reg:/^rtcp:(\d*)(?: (\S*) IP(\d) (\S*))?/,names:["port","netType","ipVer","address"],format:function(e){return null!=e.address?"rtcp:%d %s IP%d %s":"rtcp:%d"}},{push:"rtcpFbTrrInt",reg:/^rtcp-fb:(\*|\d*) trr-int (\d*)/,names:["payload","value"],format:"rtcp-fb:%s trr-int %d"},{push:"rtcpFb",reg:/^rtcp-fb:(\*|\d*) ([\w-_]*)(?: ([\w-_]*))?/,names:["payload","type","subtype"],format:function(e){return null!=e.subtype?"rtcp-fb:%s %s %s":"rtcp-fb:%s %s"}},{push:"ext",reg:/^extmap:(\d+)(?:\/(\w+))?(?: (urn:ietf:params:rtp-hdrext:encrypt))? (\S*)(?: (\S*))?/,names:["value","direction","encrypt-uri","uri","config"],format:function(e){return"extmap:%d"+(e.direction?"/%s":"%v")+(e["encrypt-uri"]?" %s":"%v")+" %s"+(e.config?" %s":"")}},{name:"extmapAllowMixed",reg:/^(extmap-allow-mixed)/},{push:"crypto",reg:/^crypto:(\d*) ([\w_]*) (\S*)(?: (\S*))?/,names:["id","suite","config","sessionConfig"],format:function(e){return null!=e.sessionConfig?"crypto:%d %s %s %s":"crypto:%d %s %s"}},{name:"setup",reg:/^setup:(\w*)/,format:"setup:%s"},{name:"connectionType",reg:/^connection:(new|existing)/,format:"connection:%s"},{name:"mid",reg:/^mid:([^\s]*)/,format:"mid:%s"},{name:"msid",reg:/^msid:(.*)/,format:"msid:%s"},{name:"ptime",reg:/^ptime:(\d*(?:\.\d*)*)/,format:"ptime:%d"},{name:"maxptime",reg:/^maxptime:(\d*(?:\.\d*)*)/,format:"maxptime:%d"},{name:"direction",reg:/^(sendrecv|recvonly|sendonly|inactive)/},{name:"icelite",reg:/^(ice-lite)/},{name:"iceUfrag",reg:/^ice-ufrag:(\S*)/,format:"ice-ufrag:%s"},{name:"icePwd",reg:/^ice-pwd:(\S*)/,format:"ice-pwd:%s"},{name:"fingerprint",reg:/^fingerprint:(\S*) (\S*)/,names:["type","hash"],format:"fingerprint:%s %s"},{push:"candidates",reg:/^candidate:(\S*) (\d*) (\S*) (\d*) (\S*) (\d*) typ (\S*)(?: raddr (\S*) rport (\d*))?(?: tcptype (\S*))?(?: generation (\d*))?(?: network-id (\d*))?(?: network-cost (\d*))?/,names:["foundation","component","transport","priority","ip","port","type","raddr","rport","tcptype","generation","network-id","network-cost"],format:function(e){var t="candidate:%s %d %s %d %s %d typ %s";return t+=null!=e.raddr?" raddr %s rport %d":"%v%v",t+=null!=e.tcptype?" tcptype %s":"%v",null!=e.generation&&(t+=" generation %d"),t+=null!=e["network-id"]?" network-id %d":"%v",t+=null!=e["network-cost"]?" network-cost %d":"%v"}},{name:"endOfCandidates",reg:/^(end-of-candidates)/},{name:"remoteCandidates",reg:/^remote-candidates:(.*)/,format:"remote-candidates:%s"},{name:"iceOptions",reg:/^ice-options:(\S*)/,format:"ice-options:%s"},{push:"ssrcs",reg:/^ssrc:(\d*) ([\w_-]*)(?::(.*))?/,names:["id","attribute","value"],format:function(e){var t="ssrc:%d";return null!=e.attribute&&(t+=" %s",null!=e.value&&(t+=":%s")),t}},{push:"ssrcGroups",reg:/^ssrc-group:([\x21\x23\x24\x25\x26\x27\x2A\x2B\x2D\x2E\w]*) (.*)/,names:["semantics","ssrcs"],format:"ssrc-group:%s %s"},{name:"msidSemantic",reg:/^msid-semantic:\s?(\w*) (\S*)/,names:["semantic","token"],format:"msid-semantic: %s %s"},{push:"groups",reg:/^group:(\w*) (.*)/,names:["type","mids"],format:"group:%s %s"},{name:"rtcpMux",reg:/^(rtcp-mux)/},{name:"rtcpRsize",reg:/^(rtcp-rsize)/},{name:"sctpmap",reg:/^sctpmap:([\w_/]*) (\S*)(?: (\S*))?/,names:["sctpmapNumber","app","maxMessageSize"],format:function(e){return null!=e.maxMessageSize?"sctpmap:%s %s %s":"sctpmap:%s %s"}},{name:"xGoogleFlag",reg:/^x-google-flag:([^\s]*)/,format:"x-google-flag:%s"},{push:"rids",reg:/^rid:([\d\w]+) (\w+)(?: ([\S| ]*))?/,names:["id","direction","params"],format:function(e){return e.params?"rid:%s %s %s":"rid:%s %s"}},{push:"imageattrs",reg:new RegExp("^imageattr:(\\d+|\\*)[\\s\\t]+(send|recv)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*)(?:[\\s\\t]+(recv|send)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*))?"),names:["pt","dir1","attrs1","dir2","attrs2"],format:function(e){return"imageattr:%s %s %s"+(e.dir2?" %s %s":"")}},{name:"simulcast",reg:new RegExp("^simulcast:(send|recv) ([a-zA-Z0-9\\-_~;,]+)(?:\\s?(send|recv) ([a-zA-Z0-9\\-_~;,]+))?$"),names:["dir1","list1","dir2","list2"],format:function(e){return"simulcast:%s %s"+(e.dir2?" %s %s":"")}},{name:"simulcast_03",reg:/^simulcast:[\s\t]+([\S+\s\t]+)$/,names:["value"],format:"simulcast: %s"},{name:"framerate",reg:/^framerate:(\d+(?:$|\.\d+))/,format:"framerate:%s"},{name:"sourceFilter",reg:/^source-filter: *(excl|incl) (\S*) (IP4|IP6|\*) (\S*) (.*)/,names:["filterMode","netType","addressTypes","destAddress","srcList"],format:"source-filter: %s %s %s %s %s"},{name:"bundleOnly",reg:/^(bundle-only)/},{name:"label",reg:/^label:(.+)/,format:"label:%s"},{name:"sctpPort",reg:/^sctp-port:(\d+)$/,format:"sctp-port:%s"},{name:"maxMessageSize",reg:/^max-message-size:(\d+)$/,format:"max-message-size:%s"},{push:"tsRefClocks",reg:/^ts-refclk:([^\s=]*)(?:=(\S*))?/,names:["clksrc","clksrcExt"],format:function(e){return"ts-refclk:%s"+(null!=e.clksrcExt?"=%s":"")}},{name:"mediaClk",reg:/^mediaclk:(?:id=(\S*))? *([^\s=]*)(?:=(\S*))?(?: *rate=(\d+)\/(\d+))?/,names:["id","mediaClockName","mediaClockValue","rateNumerator","rateDenominator"],format:function(e){var t="mediaclk:";return t+=null!=e.id?"id=%s %s":"%v%s",t+=null!=e.mediaClockValue?"=%s":"",t+=null!=e.rateNumerator?" rate=%s":"",t+=null!=e.rateDenominator?"/%s":""}},{name:"keywords",reg:/^keywds:(.+)$/,format:"keywds:%s"},{name:"content",reg:/^content:(.+)/,format:"content:%s"},{name:"bfcpFloorCtrl",reg:/^floorctrl:(c-only|s-only|c-s)/,format:"floorctrl:%s"},{name:"bfcpConfId",reg:/^confid:(\d+)/,format:"confid:%s"},{name:"bfcpUserId",reg:/^userid:(\d+)/,format:"userid:%s"},{name:"bfcpFloorId",reg:/^floorid:(.+) (?:m-stream|mstrm):(.+)/,names:["id","mStream"],format:"floorid:%s mstrm:%s"},{push:"invalid",names:["value"]}]};Object.keys(Va).forEach((function(e){Va[e].forEach((function(e){e.reg||(e.reg=/(.*)/),e.format||(e.format="%s")}))}));var Ha=qa.exports;!function(e){var t=function(e){return String(Number(e))===e?Number(e):e},n=function(e,n,r){var i=e.name&&e.names;e.push&&!n[e.push]?n[e.push]=[]:i&&!n[e.name]&&(n[e.name]={});var a=e.push?{}:i?n[e.name]:n;!function(e,n,r,i){if(i&&!r)n[i]=t(e[1]);else for(var a=0;a<r.length;a+=1)null!=e[a+1]&&(n[r[a]]=t(e[a+1]))}(r.match(e.reg),a,e.names,e.name),e.push&&n[e.push].push(a)},r=Ha,i=RegExp.prototype.test.bind(/^([a-z])=(.*)/);e.parse=function(e){var t={},a=[],o=t;return e.split(/(\r\n|\r|\n)/).filter(i).forEach((function(e){var t=e[0],i=e.slice(2);"m"===t&&(a.push({rtp:[],fmtp:[]}),o=a[a.length-1]);for(var s=0;s<(r[t]||[]).length;s+=1){var c=r[t][s];if(c.reg.test(i))return n(c,o,i)}})),t.media=a,t};var a=function(e,n){var r=n.split(/=(.+)/,2);return 2===r.length?e[r[0]]=t(r[1]):1===r.length&&n.length>1&&(e[r[0]]=void 0),e};e.parseParams=function(e){return e.split(/;\s?/).reduce(a,{})},e.parseFmtpConfig=e.parseParams,e.parsePayloads=function(e){return e.toString().split(" ").map(Number)},e.parseRemoteCandidates=function(e){for(var n=[],r=e.split(" ").map(t),i=0;i<r.length;i+=3)n.push({component:r[i],ip:r[i+1],port:r[i+2]});return n},e.parseImageAttributes=function(e){return e.split(" ").map((function(e){return e.substring(1,e.length-1).split(",").reduce(a,{})}))},e.parseSimulcastStreamList=function(e){return e.split(";").map((function(e){return e.split(",").map((function(e){var n,r=!1;return"~"!==e[0]?n=t(e):(n=t(e.substring(1,e.length)),r=!0),{scid:n,paused:r}}))}))}}(ja);var Wa=Ha,Ga=/%[sdv%]/g,Ka=function(e){var t=1,n=arguments,r=n.length;return e.replace(Ga,(function(e){if(t>=r)return e;var i=n[t];switch(t+=1,e){case"%%":return"%";case"%s":return String(i);case"%d":return Number(i);case"%v":return""}}))},za=function(e,t,n){var r=[e+"="+(t.format instanceof Function?t.format(t.push?n:n[t.name]):t.format)];if(t.names)for(var i=0;i<t.names.length;i+=1){var a=t.names[i];t.name?r.push(n[t.name][a]):r.push(n[t.names[i]])}else r.push(n[t.name]);return Ka.apply(null,r)},Qa=["v","o","s","i","u","e","p","c","b","t","r","z","a"],Ya=["i","c","b","a"],Xa=function(e,t){t=t||{},null==e.version&&(e.version=0),null==e.name&&(e.name=" "),e.media.forEach((function(e){null==e.payloads&&(e.payloads="")}));var n=t.outerOrder||Qa,r=t.innerOrder||Ya,i=[];return n.forEach((function(t){Wa[t].forEach((function(n){n.name in e&&null!=e[n.name]?i.push(za(t,n,e)):n.push in e&&null!=e[n.push]&&e[n.push].forEach((function(e){i.push(za(t,n,e))}))}))})),e.media.forEach((function(e){i.push(za("m",Wa.m[0],e)),r.forEach((function(t){Wa[t].forEach((function(n){n.name in e&&null!=e[n.name]?i.push(za(t,n,e)):n.push in e&&null!=e[n.push]&&e[n.push].forEach((function(e){i.push(za(t,n,e))}))}))}))})),i.join("\r\n")+"\r\n"},Za=Xa,$a=ja.parse,eo="negotiationStarted",to="negotiationComplete",no="rtpVideoPayloadTypes",ro=function(t){function r(t){var n;s(this,r);var i,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return(n=e(this,r)).pendingCandidates=[],n.restartingIce=!1,n.renegotiate=!1,n.trackBitrates=[],n.remoteStereoMids=[],n.remoteNackMids=[],n.negotiate=Aa((function(e){n.emit(eo);try{n.createAndSendOffer()}catch(t){if(!e)throw t;e(t)}}),100),n._pc="Chrome"===(null===(i=gi())||void 0===i?void 0:i.name)?new RTCPeerConnection(t,a):new RTCPeerConnection(t),n._pc.onicecandidate=function(e){var t;e.candidate&&(null===(t=n.onIceCandidate)||void 0===t||t.call(m(n),e.candidate))},n._pc.onicecandidateerror=function(e){var t;null===(t=n.onIceCandidateError)||void 0===t||t.call(m(n),e)},n._pc.onconnectionstatechange=function(){var e,t,r;null===(e=n.onConnectionStateChange)||void 0===e||e.call(m(n),null!==(r=null===(t=n._pc)||void 0===t?void 0:t.connectionState)&&void 0!==r?r:"closed")},n._pc.ondatachannel=function(e){var t;null===(t=n.onDataChannel)||void 0===t||t.call(m(n),e)},n._pc.ontrack=function(e){var t;null===(t=n.onTrack)||void 0===t||t.call(m(n),e)},n}return l(r,kn.EventEmitter),u(r,[{key:"pc",get:function(){if(this._pc)return this._pc;throw new fi("Expected peer connection to be available")}},{key:"isICEConnected",get:function(){return null!==this._pc&&("connected"===this.pc.iceConnectionState||"completed"===this.pc.iceConnectionState)}},{key:"addIceCandidate",value:function(e){return Xt(this,void 0,void 0,n().mark((function t(){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!this.pc.remoteDescription||this.restartingIce){t.next=2;break}return t.abrupt("return",this.pc.addIceCandidate(e));case 2:this.pendingCandidates.push(e);case 3:case"end":return t.stop()}}),t,this)})))}},{key:"setRemoteDescription",value:function(e){var t;return Xt(this,void 0,void 0,n().mark((function r(){var i,a,o,s,c,u=this;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return i=void 0,"offer"===e.type?(a=oo(e),o=a.stereoMids,s=a.nackMids,this.remoteStereoMids=o,this.remoteNackMids=s):"answer"===e.type&&((c=$a(null!==(t=e.sdp)&&void 0!==t?t:"")).media.forEach((function(e){"audio"===e.type&&u.trackBitrates.some((function(t){if(!t.transceiver||e.mid!=t.transceiver.mid)return!1;var n=0;if(e.rtp.some((function(e){return e.codec.toUpperCase()===t.codec.toUpperCase()&&(n=e.payload,!0)})),0===n)return!0;var r,i=!1,a=S(e.fmtp);try{for(a.s();!(r=a.n()).done;){var o=r.value;if(o.payload===n){o.config=o.config.split(";").filter((function(e){return!e.includes("maxaveragebitrate")})).join(";"),t.maxbr>0&&(o.config+=";maxaveragebitrate=".concat(1e3*t.maxbr)),i=!0;break}}}catch(e){a.e(e)}finally{a.f()}return i||t.maxbr>0&&e.fmtp.push({payload:n,config:"maxaveragebitrate=".concat(1e3*t.maxbr)}),!0}))})),i=Za(c)),n.next=4,this.setMungedSDP(e,i,!0);case 4:this.pendingCandidates.forEach((function(e){u.pc.addIceCandidate(e)})),this.pendingCandidates=[],this.restartingIce=!1,this.renegotiate?(this.renegotiate=!1,this.createAndSendOffer()):"answer"===e.type&&(this.emit(to),e.sdp&&$a(e.sdp).media.forEach((function(e){"video"===e.type&&u.emit(no,e.rtp)})));case 8:case"end":return n.stop()}}),r,this)})))}},{key:"createAndSendOffer",value:function(e){var t;return Xt(this,void 0,void 0,n().mark((function r(){var i,a,o,s=this;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(void 0!==this.onOffer){n.next=2;break}return n.abrupt("return");case 2:if((null==e?void 0:e.iceRestart)&&(B.debug("restarting ICE"),this.restartingIce=!0),!this._pc||"have-local-offer"!==this._pc.signalingState){n.next=14;break}if(i=this.pc.remoteDescription,!(null==e?void 0:e.iceRestart)||!i){n.next=10;break}return n.next=8,this.pc.setRemoteDescription(i);case 8:n.next=12;break;case 10:return this.renegotiate=!0,n.abrupt("return");case 12:n.next=17;break;case 14:if(this._pc&&"closed"!==this._pc.signalingState){n.next=17;break}return B.warn("could not createOffer with closed peer connection"),n.abrupt("return");case 17:return B.debug("starting to negotiate"),n.next=20,this.pc.createOffer(e);case 20:return a=n.sent,(o=$a(null!==(t=a.sdp)&&void 0!==t?t:"")).media.forEach((function(e){"audio"===e.type?io(e,[],[]):"video"===e.type&&(ao(e),s.trackBitrates.some((function(t){if(!e.msid||!t.cid||!e.msid.includes(t.cid))return!1;var n=0;if(e.rtp.some((function(e){return e.codec.toUpperCase()===t.codec.toUpperCase()&&(n=e.payload,!0)})),0===n)return!0;var r,i=!1,a=S(e.fmtp);try{for(a.s();!(r=a.n()).done;){var o=r.value;if(o.payload===n){o.config.includes("x-google-start-bitrate")||(o.config+=";x-google-start-bitrate=".concat(Math.round(.7*t.maxbr))),o.config.includes("x-google-max-bitrate")||(o.config+=";x-google-max-bitrate=".concat(t.maxbr)),i=!0;break}}}catch(e){a.e(e)}finally{a.f()}return i||e.fmtp.push({payload:n,config:"x-google-start-bitrate=".concat(Math.round(.7*t.maxbr),";x-google-max-bitrate=").concat(t.maxbr)}),!0})))})),n.next=25,this.setMungedSDP(a,Za(o));case 25:this.onOffer(a);case 26:case"end":return n.stop()}}),r,this)})))}},{key:"createAndSetAnswer",value:function(){var e;return Xt(this,void 0,void 0,n().mark((function t(){var r,i,a=this;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.pc.createAnswer();case 2:return r=t.sent,(i=$a(null!==(e=r.sdp)&&void 0!==e?e:"")).media.forEach((function(e){"audio"===e.type&&io(e,a.remoteStereoMids,a.remoteNackMids)})),t.next=7,this.setMungedSDP(r,Za(i));case 7:return t.abrupt("return",r);case 8:case"end":return t.stop()}}),t,this)})))}},{key:"createDataChannel",value:function(e,t){return this.pc.createDataChannel(e,t)}},{key:"addTransceiver",value:function(e,t){return this.pc.addTransceiver(e,t)}},{key:"addTrack",value:function(e){return this.pc.addTrack(e)}},{key:"setTrackCodecBitrate",value:function(e){this.trackBitrates.push(e)}},{key:"setConfiguration",value:function(e){return this.pc.setConfiguration(e)}},{key:"canRemoveTrack",value:function(){return!!this.pc.removeTrack}},{key:"removeTrack",value:function(e){return this.pc.removeTrack(e)}},{key:"getConnectionState",value:function(){return this.pc.connectionState}},{key:"getICEConnectionState",value:function(){return this.pc.iceConnectionState}},{key:"getSignallingState",value:function(){return this.pc.signalingState}},{key:"getTransceivers",value:function(){return this.pc.getTransceivers()}},{key:"getSenders",value:function(){return this.pc.getSenders()}},{key:"getLocalDescription",value:function(){return this.pc.localDescription}},{key:"getRemoteDescription",value:function(){return this.pc.remoteDescription}},{key:"getStats",value:function(){return this.pc.getStats()}},{key:"getConnectedAddress",value:function(){var e;return Xt(this,void 0,void 0,n().mark((function t(){var r,i,a,o;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this._pc){t.next=2;break}return t.abrupt("return");case 2:return r="",i=new Map,a=new Map,t.next=7,this._pc.getStats();case 7:if(t.sent.forEach((function(e){switch(e.type){case"transport":r=e.selectedCandidatePairId;break;case"candidate-pair":""===r&&e.selected&&(r=e.id),i.set(e.id,e);break;case"remote-candidate":a.set(e.id,"".concat(e.address,":").concat(e.port));break}})),""!==r){t.next=11;break}return t.abrupt("return",void 0);case 11:if(void 0!==(o=null===(e=i.get(r))||void 0===e?void 0:e.remoteCandidateId)){t.next=14;break}return t.abrupt("return",void 0);case 14:return t.abrupt("return",a.get(o));case 15:case"end":return t.stop()}}),t,this)})))}},{key:"close",value:function(){this._pc&&(this._pc.close(),this._pc.onconnectionstatechange=null,this._pc.oniceconnectionstatechange=null,this._pc.onicegatheringstatechange=null,this._pc.ondatachannel=null,this._pc.onnegotiationneeded=null,this._pc.onsignalingstatechange=null,this._pc.onicecandidate=null,this._pc.ondatachannel=null,this._pc.ontrack=null,this._pc.onconnectionstatechange=null,this._pc.oniceconnectionstatechange=null,this._pc=null)}},{key:"setMungedSDP",value:function(e,t,r){return Xt(this,void 0,void 0,n().mark((function i(){var a,o,s;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(!t){n.next=19;break}if(a=e.sdp,e.sdp=t,n.prev=3,B.debug("setting munged ".concat(r?"remote":"local"," description")),!r){n.next=10;break}return n.next=8,this.pc.setRemoteDescription(e);case 8:n.next=12;break;case 10:return n.next=12,this.pc.setLocalDescription(e);case 12:return n.abrupt("return");case 15:n.prev=15,n.t0=n.catch(3),B.warn("not able to set ".concat(e.type,", falling back to unmodified sdp"),{error:n.t0,sdp:t}),e.sdp=a;case 19:if(n.prev=19,!r){n.next=25;break}return n.next=23,this.pc.setRemoteDescription(e);case 23:n.next=27;break;case 25:return n.next=27,this.pc.setLocalDescription(e);case 27:n.next=37;break;case 29:throw n.prev=29,n.t1=n.catch(19),o="unknown error",n.t1 instanceof Error?o=n.t1.message:"string"==typeof n.t1&&(o=n.t1),s={error:o,sdp:e.sdp},!r&&this.pc.remoteDescription&&(s.remoteSdp=this.pc.remoteDescription),B.error("unable to set ".concat(e.type),s),new pi(o);case 37:case"end":return n.stop()}}),i,this,[[3,15],[19,29]])})))}}]),r}();function io(e,t,n){var r=0;e.rtp.some((function(e){return"opus"===e.codec&&(r=e.payload,!0)})),r>0&&(e.rtcpFb||(e.rtcpFb=[]),n.includes(e.mid)&&!e.rtcpFb.some((function(e){return e.payload===r&&"nack"===e.type}))&&e.rtcpFb.push({payload:r,type:"nack"}),t.includes(e.mid)&&e.fmtp.some((function(e){return e.payload===r&&(e.config.includes("stereo=1")||(e.config+=";stereo=1"),!0)})))}function ao(e){var t,n,r,i;if(Yi(null===(n=null===(t=e.rtp[0])||void 0===t?void 0:t.codec)||void 0===n?void 0:n.toLowerCase())){var a=0;(null===(r=e.ext)||void 0===r?void 0:r.some((function(e){return e.uri===Hi||(e.value>a&&(a=e.value),!1)})))||null===(i=e.ext)||void 0===i||i.push({value:a+1,uri:Hi})}}function oo(e){var t,n=[],r=[],i=$a(null!==(t=e.sdp)&&void 0!==t?t:""),a=0;return i.media.forEach((function(e){var t;"audio"===e.type&&(e.rtp.some((function(e){return"opus"===e.codec&&(a=e.payload,!0)})),(null===(t=e.rtcpFb)||void 0===t?void 0:t.some((function(e){return e.payload===a&&"nack"===e.type})))&&r.push(e.mid),e.fmtp.some((function(t){return t.payload===a&&(t.config.includes("sprop-stereo=1")&&n.push(e.mid),!0)})))})),{stereoMids:n,nackMids:r}}var so,co="vp8",uo={audioBitrate:Si.music.maxBitrate,audioPreset:Si.music,dtx:!0,red:!0,forceStereo:!1,simulcast:!0,screenShareEncoding:Oi.h1080fps15.encoding,stopMicTrackOnMute:!1,videoCodec:co,backupCodec:!0},lo={autoGainControl:!0,echoCancellation:!0,noiseSuppression:!0},ho={resolution:Di.h720.resolution},fo={adaptiveStream:!1,dynacast:!1,stopLocalTrackOnUnpublish:!0,reconnectPolicy:new Yt,disconnectOnPageLeave:!0,expWebAudioMix:!1},po={autoSubscribe:!0,maxRetries:1,peerConnectionTimeout:15e3,websocketTimeout:15e3},mo="_lossy",vo="_reliable",ko="leave-reconnect";!function(e){e[e.New=0]="New",e[e.Connected=1]="Connected",e[e.Disconnected=2]="Disconnected",e[e.Reconnecting=3]="Reconnecting",e[e.Closed=4]="Closed"}(so||(so={}));var go=function(t){function r(t){var i;return s(this,r),(i=e(this,r)).options=t,i.rtcConfig={},i.peerConnectionTimeout=po.peerConnectionTimeout,i.fullReconnectOnNext=!1,i.subscriberPrimary=!1,i.pcState=so.New,i._isClosed=!0,i.pendingTrackResolvers={},i.hasPublished=!1,i.reconnectAttempts=0,i.reconnectStart=0,i.attemptingReconnect=!1,i.joinAttempts=0,i.maxJoinAttempts=1,i.shouldFailNext=!1,i.handleDataChannel=function(e){var t=e.channel;return Xt(m(i),void 0,void 0,n().mark((function e(){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=2;break}return e.abrupt("return");case 2:if(t.label!==vo){e.next=6;break}this.reliableDCSub=t,e.next=11;break;case 6:if(t.label!==mo){e.next=10;break}this.lossyDCSub=t,e.next=11;break;case 10:return e.abrupt("return");case 11:B.debug("on data channel ".concat(t.id,", ").concat(t.label)),t.onmessage=this.handleDataMessage;case 13:case"end":return e.stop()}}),e,this)})))},i.handleDataMessage=function(e){return Xt(m(i),void 0,void 0,n().mark((function t(){var r,i,a,o,s;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.dataProcessLock.lock();case 2:if(a=t.sent,t.prev=3,!(e.data instanceof ArrayBuffer)){t.next=8;break}o=e.data,t.next=16;break;case 8:if(!(e.data instanceof Blob)){t.next=14;break}return t.next=11,e.data.arrayBuffer();case 11:o=t.sent,t.next=16;break;case 14:return B.error("unsupported data type",e.data),t.abrupt("return");case 16:s=Lt.fromBinary(new Uint8Array(o)),"speaker"===(null===(r=s.value)||void 0===r?void 0:r.case)?this.emit(Ri.ActiveSpeakersUpdate,s.value.value.speakers):"user"===(null===(i=s.value)||void 0===i?void 0:i.case)&&this.emit(Ri.DataPacketReceived,s.value.value,s.kind);case 18:return t.prev=18,a(),t.finish(18);case 21:case"end":return t.stop()}}),t,this,[[3,,18,21]])})))},i.handleDataError=function(e){var t=0===e.currentTarget.maxRetransmits?"lossy":"reliable";if(e instanceof ErrorEvent&&e.error){var n=e.error.error;B.error("DataChannel error on ".concat(t,": ").concat(e.message),n)}else B.error("Unknown DataChannel error on ".concat(t),e)},i.handleBufferedAmountLow=function(e){var t=0===e.currentTarget.maxRetransmits?Mt.LOSSY:Mt.RELIABLE;i.updateAndEmitDCBufferStatus(t)},i.handleDisconnect=function(e,t){if(!i._isClosed){B.warn("".concat(e," disconnected")),0===i.reconnectAttempts&&(i.reconnectStart=Date.now());var n=Date.now()-i.reconnectStart,r=i.getNextRetryDelay({elapsedMs:n,retryCount:i.reconnectAttempts});null!==r?(e===ko&&(r=0),B.debug("reconnecting in ".concat(r,"ms")),i.clearReconnectTimeout(),i.token&&i.regionUrlProvider&&i.regionUrlProvider.updateToken(i.token),i.reconnectTimeout=mi.setTimeout((function(){return i.attemptReconnect(t)}),r)):function(e){B.warn("could not recover connection after ".concat(i.reconnectAttempts," attempts, ").concat(e,"ms. giving up")),i.emit(Ri.Disconnected),i.close()}(n)}},i.waitForRestarted=function(){return new Promise((function(e,t){i.pcState===so.Connected&&e();var n=function(){i.off(Ri.Disconnected,r),e()},r=function(){i.off(Ri.Restarted,n),t()};i.once(Ri.Restarted,n),i.once(Ri.Disconnected,r)}))},i.updateAndEmitDCBufferStatus=function(e){var t=i.isBufferStatusLow(e);void 0!==t&&t!==i.dcBufferStatus.get(e)&&(i.dcBufferStatus.set(e,t),i.emit(Ri.DCBufferStatusChanged,t,e))},i.isBufferStatusLow=function(e){var t=i.dataChannelForKind(e);if(t)return t.bufferedAmount<=t.bufferedAmountLowThreshold},i.handleBrowserOnLine=function(){i.client.isReconnecting&&(i.clearReconnectTimeout(),i.attemptReconnect(wt.RR_SIGNAL_DISCONNECTED))},i.client=new Pa,i.client.signalLatency=i.options.expSignalLatency,i.reconnectPolicy=i.options.reconnectPolicy,i.registerOnLineListener(),i.closingLock=new ba,i.dataProcessLock=new ba,i.dcBufferStatus=new Map([[Mt.LOSSY,!0],[Mt.RELIABLE,!0]]),i.client.onParticipantUpdate=function(e){return i.emit(Ri.ParticipantUpdate,e)},i.client.onConnectionQuality=function(e){return i.emit(Ri.ConnectionQualityUpdate,e)},i.client.onRoomUpdate=function(e){return i.emit(Ri.RoomUpdate,e)},i.client.onSubscriptionError=function(e){return i.emit(Ri.SubscriptionError,e)},i.client.onSubscriptionPermissionUpdate=function(e){return i.emit(Ri.SubscriptionPermissionUpdate,e)},i.client.onSpeakersChanged=function(e){return i.emit(Ri.SpeakersChanged,e)},i.client.onStreamStateUpdate=function(e){return i.emit(Ri.StreamStateChanged,e)},i}return l(r,kn.EventEmitter),u(r,[{key:"isClosed",get:function(){return this._isClosed}},{key:"join",value:function(e,t,r,i){return Xt(this,void 0,void 0,n().mark((function a(){var o;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return this.url=e,this.token=t,this.signalOpts=r,this.maxJoinAttempts=r.maxRetries,n.prev=4,this.joinAttempts+=1,this.setupSignalClientCallbacks(),n.next=9,this.client.join(e,t,r,i);case 9:return o=n.sent,this._isClosed=!1,this.latestJoinResponse=o,this.subscriberPrimary=o.subscriberPrimary,this.publisher||this.configure(o),this.subscriberPrimary||this.negotiate(),this.clientConfiguration=o.clientConfiguration,n.abrupt("return",o);case 19:if(n.prev=19,n.t0=n.catch(4),!(n.t0 instanceof ui)){n.next=26;break}if(1!==n.t0.reason){n.next=26;break}if(B.warn("Couldn't connect to server, attempt ".concat(this.joinAttempts," of ").concat(this.maxJoinAttempts)),this.joinAttempts>=this.maxJoinAttempts){n.next=26;break}return n.abrupt("return",this.join(e,t,r,i));case 26:throw n.t0;case 27:case"end":return n.stop()}}),a,this,[[4,19]])})))}},{key:"close",value:function(){return Xt(this,void 0,void 0,n().mark((function e(){var t;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.closingLock.lock();case 2:if(t=e.sent,!this.isClosed){e.next=6;break}return t(),e.abrupt("return");case 6:return e.prev=6,this._isClosed=!0,this.emit(Ri.Closing),this.removeAllListeners(),this.deregisterOnLineListener(),this.clearPendingReconnect(),e.next=14,this.cleanupPeerConnections();case 14:return e.next=16,this.cleanupClient();case 16:return e.prev=16,t(),e.finish(16);case 19:case"end":return e.stop()}}),e,this,[[6,,16,19]])})))}},{key:"cleanupPeerConnections",value:function(){return Xt(this,void 0,void 0,n().mark((function e(){var t,r=this;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:this.publisher&&"closed"!==this.publisher.getSignallingState()&&this.publisher.getSenders().forEach((function(e){var t,n;try{(null===(t=r.publisher)||void 0===t?void 0:t.canRemoveTrack())&&(null===(n=r.publisher)||void 0===n||n.removeTrack(e))}catch(e){B.warn("could not removeTrack",{error:e})}})),this.publisher&&(this.publisher.close(),this.publisher=void 0),this.subscriber&&(this.subscriber.close(),this.subscriber=void 0),this.hasPublished=!1,this.primaryTransport=void 0,(t=function(e){e&&(e.close(),e.onbufferedamountlow=null,e.onclose=null,e.onclosing=null,e.onerror=null,e.onmessage=null,e.onopen=null)})(this.lossyDC),t(this.lossyDCSub),t(this.reliableDC),t(this.reliableDCSub),this.lossyDC=void 0,this.lossyDCSub=void 0,this.reliableDC=void 0,this.reliableDCSub=void 0;case 14:case"end":return e.stop()}}),e,this)})))}},{key:"cleanupClient",value:function(){return Xt(this,void 0,void 0,n().mark((function e(){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.client.close();case 2:this.client.resetCallbacks();case 3:case"end":return e.stop()}}),e,this)})))}},{key:"addTrack",value:function(e){var t=this;if(this.pendingTrackResolvers[e.cid])throw new li("a track with the same ID has already been published");return new Promise((function(n,r){var i=setTimeout((function(){delete t.pendingTrackResolvers[e.cid],r(new ui("publication of local track timed out, no response from server"))}),1e4);t.pendingTrackResolvers[e.cid]={resolve:function(e){clearTimeout(i),n(e)},reject:function(){clearTimeout(i),r(new Error("Cancelled publication by calling unpublish"))}},t.client.sendAddTrack(e)}))}},{key:"removeTrack",value:function(e){var t;if(e.track&&this.pendingTrackResolvers[e.track.id]){var n=this.pendingTrackResolvers[e.track.id].reject;n&&n(),delete this.pendingTrackResolvers[e.track.id]}try{return null===(t=this.publisher)||void 0===t||t.removeTrack(e),!0}catch(e){B.warn("failed to remove track",{error:e,method:"removeTrack"})}return!1}},{key:"updateMuteStatus",value:function(e,t){this.client.sendMuteTrack(e,t)}},{key:"dataSubscriberReadyState",get:function(){var e;return null===(e=this.reliableDCSub)||void 0===e?void 0:e.readyState}},{key:"getConnectedServerAddress",value:function(){return Xt(this,void 0,void 0,n().mark((function e(){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(void 0!==this.primaryTransport){e.next=2;break}return e.abrupt("return",void 0);case 2:return e.abrupt("return",this.primaryTransport.getConnectedAddress());case 3:case"end":return e.stop()}}),e,this)})))}},{key:"setRegionUrlProvider",value:function(e){this.regionUrlProvider=e}},{key:"configure",value:function(e){var t,r=this;if(!this.publisher&&!this.subscriber){this.participantSid=null===(t=e.participant)||void 0===t?void 0:t.sid;var i=this.makeRTCConfiguration(e);this.publisher=new ro(i,{optional:[{googDscp:!0}]}),this.subscriber=new ro(i),this.emit(Ri.TransportsCreated,this.publisher,this.subscriber),this.publisher.onIceCandidate=function(e){B.trace("adding ICE candidate for peer",e),r.client.sendIceCandidate(e,br.PUBLISHER)},this.subscriber.onIceCandidate=function(e){r.client.sendIceCandidate(e,br.SUBSCRIBER)},this.publisher.onOffer=function(e){r.client.sendOffer(e)};var a=this.publisher,o=this.subscriber,s=e.subscriberPrimary;s&&(a=this.subscriber,o=this.publisher,this.subscriber.onDataChannel=this.handleDataChannel),this.primaryTransport=a,a.onConnectionStateChange=function(t){return Xt(r,void 0,void 0,n().mark((function r(){var i;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:B.debug("primary PC state changed ".concat(t)),"connected"===t?(i=this.pcState===so.New,this.pcState=so.Connected,i&&this.emit(Ri.Connected,e)):"failed"===t&&this.pcState===so.Connected&&(this.pcState=so.Disconnected,this.handleDisconnect("primary peerconnection",s?wt.RR_SUBSCRIBER_FAILED:wt.RR_PUBLISHER_FAILED));case 2:case"end":return n.stop()}}),r,this)})))},o.onConnectionStateChange=function(e){return Xt(r,void 0,void 0,n().mark((function t(){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:B.debug("secondary PC state changed ".concat(e)),"failed"===e&&this.handleDisconnect("secondary peerconnection",s?wt.RR_PUBLISHER_FAILED:wt.RR_SUBSCRIBER_FAILED);case 2:case"end":return t.stop()}}),t,this)})))},this.subscriber.onTrack=function(e){r.emit(Ri.MediaTrackAdded,e.track,e.streams[0],e.receiver)},this.createDataChannels()}}},{key:"setupSignalClientCallbacks",value:function(){var e=this;this.client.onAnswer=function(t){return Xt(e,void 0,void 0,n().mark((function e(){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.publisher){e.next=2;break}return e.abrupt("return");case 2:return B.debug("received server answer",{RTCSdpType:t.type,signalingState:this.publisher.getSignallingState().toString()}),e.next=5,this.publisher.setRemoteDescription(t);case 5:case"end":return e.stop()}}),e,this)})))},this.client.onTrickle=function(t,n){e.publisher&&e.subscriber&&(B.trace("got ICE candidate from peer",{candidate:t,target:n}),n===br.PUBLISHER?e.publisher.addIceCandidate(t):e.subscriber.addIceCandidate(t))},this.client.onOffer=function(t){return Xt(e,void 0,void 0,n().mark((function e(){var r;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.subscriber){e.next=2;break}return e.abrupt("return");case 2:return B.debug("received server offer",{RTCSdpType:t.type,signalingState:this.subscriber.getSignallingState().toString()}),e.next=5,this.subscriber.setRemoteDescription(t);case 5:return e.next=7,this.subscriber.createAndSetAnswer();case 7:r=e.sent,this.client.sendAnswer(r);case 9:case"end":return e.stop()}}),e,this)})))},this.client.onLocalTrackPublished=function(t){if(B.debug("received trackPublishedResponse",t),e.pendingTrackResolvers[t.cid]){var n=e.pendingTrackResolvers[t.cid].resolve;delete e.pendingTrackResolvers[t.cid],n(t.track)}else B.error("missing track resolver for ".concat(t.cid))},this.client.onTokenRefresh=function(t){e.token=t},this.client.onClose=function(){e.handleDisconnect("signal",wt.RR_SIGNAL_DISCONNECTED)},this.client.onLeave=function(t){(null==t?void 0:t.canReconnect)?(e.fullReconnectOnNext=!0,e.primaryTransport=void 0,e.handleDisconnect(ko)):(e.emit(Ri.Disconnected,null==t?void 0:t.reason),e.close()),B.trace("leave request",{leave:t})}}},{key:"makeRTCConfiguration",value:function(e){var t,n=Object.assign({},this.rtcConfig);if((null===(t=this.signalOpts)||void 0===t?void 0:t.e2eeEnabled)&&(B.debug("E2EE - setting up transports with insertable streams"),n.encodedInsertableStreams=!0),e.iceServers&&!n.iceServers){var r=[];e.iceServers.forEach((function(e){var t={urls:e.urls};e.username&&(t.username=e.username),e.credential&&(t.credential=e.credential),r.push(t)})),n.iceServers=r}return e.clientConfiguration&&e.clientConfiguration.forceRelay===bt.ENABLED&&(n.iceTransportPolicy="relay"),n.sdpSemantics="unified-plan",n.continualGatheringPolicy="gather_continually",n}},{key:"createDataChannels",value:function(){this.publisher&&(this.lossyDC&&(this.lossyDC.onmessage=null,this.lossyDC.onerror=null),this.reliableDC&&(this.reliableDC.onmessage=null,this.reliableDC.onerror=null),this.lossyDC=this.publisher.createDataChannel(mo,{ordered:!0,maxRetransmits:0}),this.reliableDC=this.publisher.createDataChannel(vo,{ordered:!0}),this.lossyDC.onmessage=this.handleDataMessage,this.reliableDC.onmessage=this.handleDataMessage,this.lossyDC.onerror=this.handleDataError,this.reliableDC.onerror=this.handleDataError,this.lossyDC.bufferedAmountLowThreshold=65535,this.reliableDC.bufferedAmountLowThreshold=65535,this.lossyDC.onbufferedamountlow=this.handleBufferedAmountLow,this.reliableDC.onbufferedamountlow=this.handleBufferedAmountLow)}},{key:"setPreferredCodec",value:function(e,t,n){if("getCapabilities"in RTCRtpSender){var r=RTCRtpSender.getCapabilities(t);r&&(B.debug("get capabilities",r),r.codecs.forEach((function(e){var t=e.mimeType.toLowerCase();"audio/opus"!==t&&(t==="video/".concat(n)&&("h264"!==n||e.sdpFmtpLine&&e.sdpFmtpLine.includes("profile-level-id=42e01f")))})),function(e){if(!na())return!1;if(!("setCodecPreferences"in e))return!1;var t=gi();if(!(null==t?void 0:t.name)||!t.version)return!1;var n=Zi[t.name];!!n&&ca(t.version,n)}(e))}}},{key:"createSender",value:function(e,t,r){return Xt(this,void 0,void 0,n().mark((function i(){var a,o;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(!Gi()){n.next=5;break}return n.next=3,this.createTransceiverRTCRtpSender(e,t,r);case 3:return a=n.sent,n.abrupt("return",a);case 5:if(!Ki()){n.next=11;break}return B.warn("using add-track fallback"),n.next=9,this.createRTCRtpSender(e.mediaStreamTrack);case 9:return o=n.sent,n.abrupt("return",o);case 11:throw new fi("Required webRTC APIs not supported on this device");case 12:case"end":return n.stop()}}),i,this)})))}},{key:"createSimulcastSender",value:function(e,t,r,i){return Xt(this,void 0,void 0,n().mark((function a(){return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(!Gi()){n.next=2;break}return n.abrupt("return",this.createSimulcastTransceiverSender(e,t,r,i));case 2:if(!Ki()){n.next=5;break}return B.debug("using add-track fallback"),n.abrupt("return",this.createRTCRtpSender(e.mediaStreamTrack));case 5:throw new fi("Cannot stream on this device");case 6:case"end":return n.stop()}}),a,this)})))}},{key:"createTransceiverRTCRtpSender",value:function(e,t,r){return Xt(this,void 0,void 0,n().mark((function i(){var a,o,s;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(this.publisher){n.next=2;break}throw new fi("publisher is closed");case 2:return a=[],e.mediaStream&&a.push(e.mediaStream),o={direction:"sendonly",streams:a},r&&(o.sendEncodings=r),n.next=8,this.publisher.addTransceiver(e.mediaStreamTrack,o);case 8:return s=n.sent,e.kind===Mi.Kind.Video&&t.videoCodec&&(this.setPreferredCodec(s,e.kind,t.videoCodec),e.codec=t.videoCodec),n.abrupt("return",s.sender);case 11:case"end":return n.stop()}}),i,this)})))}},{key:"createSimulcastTransceiverSender",value:function(e,t,r,i){return Xt(this,void 0,void 0,n().mark((function a(){var o,s;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(this.publisher){n.next=2;break}throw new fi("publisher is closed");case 2:return o={direction:"sendonly"},i&&(o.sendEncodings=i),n.next=6,this.publisher.addTransceiver(t.mediaStreamTrack,o);case 6:if(s=n.sent,r.videoCodec){n.next=9;break}return n.abrupt("return");case 9:return this.setPreferredCodec(s,e.kind,r.videoCodec),e.setSimulcastTrackSender(r.videoCodec,s.sender),n.abrupt("return",s.sender);case 12:case"end":return n.stop()}}),a,this)})))}},{key:"createRTCRtpSender",value:function(e){return Xt(this,void 0,void 0,n().mark((function t(){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this.publisher){t.next=2;break}throw new fi("publisher is closed");case 2:return t.abrupt("return",this.publisher.addTrack(e));case 3:case"end":return t.stop()}}),t,this)})))}},{key:"attemptReconnect",value:function(e){var t,r,i;return Xt(this,void 0,void 0,n().mark((function a(){var o;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(!this._isClosed){n.next=2;break}return n.abrupt("return");case 2:if(!this.attemptingReconnect){n.next=4;break}return n.abrupt("return");case 4:if((null===(t=this.clientConfiguration)||void 0===t?void 0:t.resumeConnection)!==bt.DISABLED&&"closed"!==(null!==(i=null===(r=this.primaryTransport)||void 0===r?void 0:r.getSignallingState())&&void 0!==i?i:"closed")||(this.fullReconnectOnNext=!0),n.prev=5,this.attemptingReconnect=!0,!this.fullReconnectOnNext){n.next=12;break}return n.next=10,this.restartConnection();case 10:n.next=14;break;case 12:return n.next=14,this.resumeConnection(e);case 14:this.clearPendingReconnect(),this.fullReconnectOnNext=!1,n.next=31;break;case 18:if(n.prev=18,n.t0=n.catch(5),this.reconnectAttempts+=1,o=!0,n.t0 instanceof fi?(B.debug("received unrecoverable error",{error:n.t0}),o=!1):n.t0 instanceof yo||(this.fullReconnectOnNext=!0),!o){n.next=27;break}this.handleDisconnect("reconnect",wt.RR_UNKNOWN),n.next=31;break;case 27:return B.info("could not recover connection after ".concat(this.reconnectAttempts," attempts, ").concat(Date.now()-this.reconnectStart,"ms. giving up")),this.emit(Ri.Disconnected),n.next=31,this.close();case 31:return n.prev=31,this.attemptingReconnect=!1,n.finish(31);case 34:case"end":return n.stop()}}),a,this,[[5,18,31,34]])})))}},{key:"getNextRetryDelay",value:function(e){try{return this.reconnectPolicy.nextRetryDelayInMs(e)}catch(e){B.warn("encountered error in reconnect policy",{error:e})}return null}},{key:"restartConnection",value:function(e){var t,r,i;return Xt(this,void 0,void 0,n().mark((function a(){var o,s;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(n.prev=0,this.url&&this.token){n.next=3;break}throw new fi("could not reconnect, url or token not saved");case 3:if(B.info("reconnecting, attempt: ".concat(this.reconnectAttempts)),this.emit(Ri.Restarting),!this.client.isConnected){n.next=8;break}return n.next=8,this.client.sendLeave();case 8:return n.next=10,this.cleanupPeerConnections();case 10:return n.next=12,this.cleanupClient();case 12:if(n.prev=12,this.signalOpts){n.next=16;break}throw B.warn("attempted connection restart, without signal options present"),new yo;case 16:return n.next=18,this.join(null!=e?e:this.url,this.token,this.signalOpts);case 18:o=n.sent,n.next=26;break;case 21:if(n.prev=21,n.t0=n.catch(12),!(n.t0 instanceof ui&&0===n.t0.reason)){n.next=25;break}throw new fi("could not reconnect, token might be expired");case 25:throw new yo;case 26:if(!this.shouldFailNext){n.next=29;break}throw this.shouldFailNext=!1,new Error("simulated failure");case 29:return this.client.setReconnected(),this.emit(Ri.SignalRestarted,o),n.next=33,this.waitForPCReconnected();case 33:null===(t=this.regionUrlProvider)||void 0===t||t.resetAttempts(),this.emit(Ri.Restarted),n.next=50;break;case 37:return n.prev=37,n.t1=n.catch(0),n.next=41,null===(r=this.regionUrlProvider)||void 0===r?void 0:r.getNextBestRegionUrl();case 41:if(!(s=n.sent)){n.next=48;break}return n.next=45,this.restartConnection(s);case 45:return n.abrupt("return");case 48:throw null===(i=this.regionUrlProvider)||void 0===i||i.resetAttempts(),n.t1;case 50:case"end":return n.stop()}}),a,this,[[0,37],[12,21]])})))}},{key:"resumeConnection",value:function(e){var t;return Xt(this,void 0,void 0,n().mark((function r(){var i,a,o;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(this.url&&this.token){n.next=2;break}throw new fi("could not reconnect, url or token not saved");case 2:if(this.publisher&&this.subscriber){n.next=4;break}throw new fi("publisher and subscriber connections unset");case 4:return B.info("resuming signal connection, attempt ".concat(this.reconnectAttempts)),this.emit(Ri.Resuming),n.prev=6,this.setupSignalClientCallbacks(),n.next=10,this.client.reconnect(this.url,this.token,this.participantSid,e);case 10:(i=n.sent)&&(a=this.makeRTCConfiguration(i),this.publisher.setConfiguration(a),this.subscriber.setConfiguration(a)),n.next=21;break;case 14:if(n.prev=14,n.t0=n.catch(6),o="",n.t0 instanceof Error&&(o=n.t0.message,B.error(n.t0.message)),!(n.t0 instanceof ui&&0===n.t0.reason)){n.next=20;break}throw new fi("could not reconnect, token might be expired");case 20:throw new yo(o);case 21:if(this.emit(Ri.SignalResumed),!this.shouldFailNext){n.next=25;break}throw this.shouldFailNext=!1,new Error("simulated failure");case 25:if(this.subscriber.restartingIce=!0,!this.hasPublished){n.next=29;break}return n.next=29,this.publisher.createAndSendOffer({iceRestart:!0});case 29:return n.next=31,this.waitForPCReconnected();case 31:this.client.setReconnected(),"open"===(null===(t=this.reliableDC)||void 0===t?void 0:t.readyState)&&null===this.reliableDC.id&&this.createDataChannels(),this.emit(Ri.Resumed);case 34:case"end":return n.stop()}}),r,this,[[6,14]])})))}},{key:"waitForPCInitialConnection",value:function(e,t){return Xt(this,void 0,void 0,n().mark((function r(){var i=this;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(this.pcState!==so.Connected){n.next=2;break}return n.abrupt("return");case 2:if(this.pcState===so.New){n.next=4;break}throw new fi("Expected peer connection to be new on initial connection");case 4:return n.abrupt("return",new Promise((function(n,r){var a=function(){B.warn("closing engine"),mi.clearTimeout(s),r(new ui("room connection has been cancelled",3))};(null==t?void 0:t.signal.aborted)&&a(),null==t||t.signal.addEventListener("abort",a);var o=function(){mi.clearTimeout(s),null==t||t.signal.removeEventListener("abort",a),n()},s=mi.setTimeout((function(){i.off(Ri.Connected,o),r(new ui("could not establish pc connection"))}),null!=e?e:i.peerConnectionTimeout);i.once(Ri.Connected,o)})));case 5:case"end":return n.stop()}}),r,this)})))}},{key:"waitForPCReconnected",value:function(){var e,t;return Xt(this,void 0,void 0,n().mark((function r(){var i,a;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:i=Date.now(),a=i,this.pcState=so.Reconnecting,B.debug("waiting for peer connection to reconnect");case 4:if(a-i>=this.peerConnectionTimeout){n.next=17;break}if(void 0!==this.primaryTransport){n.next=9;break}return n.abrupt("break",17);case 9:2e3>=a-i||"connected"!==(null===(e=this.primaryTransport)||void 0===e?void 0:e.getConnectionState())||this.hasPublished&&"connected"!==(null===(t=this.publisher)||void 0===t?void 0:t.getConnectionState())||(this.pcState=so.Connected);case 10:if(this.pcState!==so.Connected){n.next=12;break}return n.abrupt("return");case 12:return n.next=14,Wi(100);case 14:a=Date.now(),n.next=4;break;case 17:throw new ui("could not establish PC connection");case 18:case"end":return n.stop()}}),r,this)})))}},{key:"sendDataPacket",value:function(e,t){return Xt(this,void 0,void 0,n().mark((function r(){var i,a;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return i=e.toBinary(),n.next=3,this.ensurePublisherConnected(t);case 3:(a=this.dataChannelForKind(t))&&a.send(i),this.updateAndEmitDCBufferStatus(t);case 6:case"end":return n.stop()}}),r,this)})))}},{key:"ensureDataTransportConnected",value:function(e){var t,r,i,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.subscriberPrimary;return Xt(this,void 0,void 0,n().mark((function o(){var s,c,u,d;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(s=a?this.subscriber:this.publisher,c=a?"Subscriber":"Publisher",s){n.next=4;break}throw new ui("".concat(c," connection not set"));case 4:if(a||(null===(t=this.publisher)||void 0===t?void 0:t.isICEConnected)||"checking"===(null===(r=this.publisher)||void 0===r?void 0:r.getICEConnectionState())||this.negotiate(),"open"!==(null==(u=this.dataChannelForKind(e,a))?void 0:u.readyState)){n.next=8;break}return n.abrupt("return");case 8:d=(new Date).getTime()+this.peerConnectionTimeout;case 9:if((new Date).getTime()>=d){n.next=16;break}if(!s.isICEConnected||"open"!==(null===(i=this.dataChannelForKind(e,a))||void 0===i?void 0:i.readyState)){n.next=12;break}return n.abrupt("return");case 12:return n.next=14,Wi(50);case 14:n.next=9;break;case 16:throw new ui("could not establish ".concat(c," connection, state: ").concat(s.getICEConnectionState()));case 17:case"end":return n.stop()}}),o,this)})))}},{key:"ensurePublisherConnected",value:function(e){return Xt(this,void 0,void 0,n().mark((function t(){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.ensureDataTransportConnected(e,!1);case 2:case"end":return t.stop()}}),t,this)})))}},{key:"verifyTransport",value:function(){if(!this.primaryTransport)return!1;if("closed"===this.primaryTransport.getConnectionState()||"failed"===this.primaryTransport.getConnectionState())return!1;if(this.hasPublished&&this.subscriberPrimary){if(!this.publisher)return!1;if("closed"===this.publisher.getConnectionState()||"failed"===this.publisher.getConnectionState())return!1}return!(!this.client.ws||this.client.ws.readyState===WebSocket.CLOSED)}},{key:"negotiate",value:function(){var e=this;return new Promise((function(t,n){if(e.publisher){e.hasPublished=!0;var r=function(){B.debug("engine disconnected while negotiation was ongoing"),a(),t()};e.isClosed&&n("cannot negotiate on closed engine"),e.on(Ri.Closing,r);var i=setTimeout((function(){n("negotiation timed out"),e.handleDisconnect("negotiation",wt.RR_SIGNAL_DISCONNECTED)}),e.peerConnectionTimeout),a=function(){clearTimeout(i),e.off(Ri.Closing,r)};e.publisher.once(eo,(function(){var n;null===(n=e.publisher)||void 0===n||n.once(to,(function(){a(),t()}))})),e.publisher.once(no,(function(t){var n=new Map;t.forEach((function(e){var t,r=e.codec.toLowerCase();t=r,Ci.includes(t)&&n.set(e.payload,r)})),e.emit(Ri.RTPVideoMapUpdate,n)})),e.publisher.negotiate((function(t){a(),n(t),t instanceof pi&&(e.fullReconnectOnNext=!0),e.handleDisconnect("negotiation",wt.RR_UNKNOWN)}))}else n(new pi("publisher is not defined"))}))}},{key:"dataChannelForKind",value:function(e,t){if(t){if(e===Mt.LOSSY)return this.lossyDCSub;if(e===Mt.RELIABLE)return this.reliableDCSub}else{if(e===Mt.LOSSY)return this.lossyDC;if(e===Mt.RELIABLE)return this.reliableDC}}},{key:"failNext",value:function(){this.shouldFailNext=!0}},{key:"clearReconnectTimeout",value:function(){this.reconnectTimeout&&mi.clearTimeout(this.reconnectTimeout)}},{key:"clearPendingReconnect",value:function(){this.clearReconnectTimeout(),this.reconnectAttempts=0}},{key:"registerOnLineListener",value:function(){na()&&window.addEventListener("online",this.handleBrowserOnLine)}},{key:"deregisterOnLineListener",value:function(){na()&&window.removeEventListener("online",this.handleBrowserOnLine)}}]),r}(),yo=function(t){function n(){return s(this,n),e(this,n,arguments)}return l(n,p(Error)),u(n)}(),bo=function(){function e(t,n){s(this,e),this.lastUpdateAt=0,this.settingsCacheTime=3e3,this.attemptedRegions=[],this.serverUrl=new URL(t),this.token=n}return u(e,[{key:"updateToken",value:function(e){this.token=e}},{key:"isCloud",value:function(){return ia(this.serverUrl)}},{key:"getServerUrl",value:function(){return this.serverUrl}},{key:"getNextBestRegionUrl",value:function(e){return Xt(this,void 0,void 0,n().mark((function t(){var r,i,a=this;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this.isCloud()){t.next=2;break}throw Error("region availability is only supported for LiveKit Cloud domains");case 2:if(this.regionSettings&&Date.now()-this.lastUpdateAt<=this.settingsCacheTime){t.next=6;break}return t.next=5,this.fetchRegionSettings(e);case 5:this.regionSettings=t.sent;case 6:if((r=this.regionSettings.regions.filter((function(e){return!a.attemptedRegions.find((function(t){return t.url===e.url}))}))).length<=0){t.next=14;break}return i=r[0],this.attemptedRegions.push(i),B.debug("next region: ".concat(i.region)),t.abrupt("return",i.url);case 14:return t.abrupt("return",null);case 15:case"end":return t.stop()}}),t,this)})))}},{key:"resetAttempts",value:function(){this.attemptedRegions=[]}},{key:"fetchRegionSettings",value:function(e){return Xt(this,void 0,void 0,n().mark((function t(){var r,i;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,fetch("".concat((n=this.serverUrl,"".concat(n.protocol.replace("ws","http"),"//").concat(n.host,"/settings")),"/regions"),{headers:{authorization:"Bearer ".concat(this.token)},signal:e});case 2:if(!(r=t.sent).ok){t.next=11;break}return t.next=6,r.json();case 6:return i=t.sent,this.lastUpdateAt=Date.now(),t.abrupt("return",i);case 11:throw new ui("Could not fetch region settings: ".concat(r.statusText),401===r.status?0:void 0,r.status);case 12:case"end":return t.stop()}var n}),t,this)})))}}]),e}();var So=2e3;function wo(e,t){return t?("bytesReceived"in e?(n=e.bytesReceived,r=t.bytesReceived):"bytesSent"in e&&(n=e.bytesSent,r=t.bytesSent),void 0===n||void 0===r||void 0===e.timestamp||void 0===t.timestamp?0:8*(n-r)*1e3/(e.timestamp-t.timestamp)):0;var n,r}var To=function(t){function r(t,i){var a;s(this,r);var o=2>=arguments.length||void 0===arguments[2]||arguments[2],c=arguments.length>3?arguments[3]:void 0;return(a=e(this,r,[t,Mi.Kind.Audio,i,o])).stopOnMute=!1,a.monitorSender=function(){return Xt(m(a),void 0,void 0,n().mark((function e(){var t;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.sender){e.next=3;break}return this._currentBitrate=0,e.abrupt("return");case 3:return e.prev=3,e.next=6,this.getSenderStats();case 6:t=e.sent,e.next=13;break;case 9:return e.prev=9,e.t0=e.catch(3),B.error("could not get audio sender stats",{error:e.t0}),e.abrupt("return");case 13:t&&this.prevStats&&(this._currentBitrate=wo(t,this.prevStats)),this.prevStats=t;case 15:case"end":return e.stop()}}),e,this,[[3,9]])})))},a.audioContext=c,a.checkForSilence(),a}return l(r,Fa),u(r,[{key:"setDeviceId",value:function(e){return Xt(this,void 0,void 0,n().mark((function t(){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this._constraints.deviceId!==e){t.next=2;break}return t.abrupt("return",!0);case 2:if(this._constraints.deviceId=e,this.isMuted){t.next=6;break}return t.next=6,this.restartTrack();case 6:return t.abrupt("return",this.isMuted||Sa(e)===this.mediaStreamTrack.getSettings().deviceId);case 7:case"end":return t.stop()}}),t,this)})))}},{key:"mute",value:function(){var e=this,t=Object.create(null,{mute:{get:function(){return v(h(r.prototype),"mute",e)}}});return Xt(this,void 0,void 0,n().mark((function e(){var r;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.muteLock.lock();case 2:return r=e.sent,e.prev=3,this.source===Mi.Source.Microphone&&this.stopOnMute&&!this.isUserProvided&&(B.debug("stopping mic track"),this._mediaStreamTrack.stop()),e.next=7,t.mute.call(this);case 7:return e.abrupt("return",this);case 8:return e.prev=8,r(),e.finish(8);case 11:case"end":return e.stop()}}),e,this,[[3,,8,11]])})))}},{key:"unmute",value:function(){var e=this,t=Object.create(null,{unmute:{get:function(){return v(h(r.prototype),"unmute",e)}}});return Xt(this,void 0,void 0,n().mark((function e(){var r,i;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.muteLock.lock();case 2:if(r=e.sent,e.prev=3,i=this._constraints.deviceId&&this._mediaStreamTrack.getSettings().deviceId!==Sa(this._constraints.deviceId),this.source!==Mi.Source.Microphone||!this.stopOnMute&&"ended"!==this._mediaStreamTrack.readyState&&!i||this.isUserProvided){e.next=9;break}return B.debug("reacquiring mic track"),e.next=9,this.restartTrack();case 9:return e.next=11,t.unmute.call(this);case 11:return e.abrupt("return",this);case 12:return e.prev=12,r(),e.finish(12);case 15:case"end":return e.stop()}}),e,this,[[3,,12,15]])})))}},{key:"restartTrack",value:function(e){return Xt(this,void 0,void 0,n().mark((function t(){var r,i;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e&&"boolean"!=typeof(i=Fi({audio:e})).audio&&(r=i.audio),t.next=3,this.restart(r);case 3:case"end":return t.stop()}}),t,this)})))}},{key:"restart",value:function(e){var t=this,i=Object.create(null,{restart:{get:function(){return v(h(r.prototype),"restart",t)}}});return Xt(this,void 0,void 0,n().mark((function t(){var r;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,i.restart.call(this,e);case 2:return r=t.sent,this.checkForSilence(),t.abrupt("return",r);case 5:case"end":return t.stop()}}),t,this)})))}},{key:"startMonitor",value:function(){var e=this;na()&&(this.monitorInterval||(this.monitorInterval=setInterval((function(){e.monitorSender()}),So)))}},{key:"setProcessor",value:function(e){var t;return Xt(this,void 0,void 0,n().mark((function r(){var i,a;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,this.processorLock.lock();case 2:if(i=n.sent,n.prev=3,this.audioContext){n.next=6;break}throw Error("Audio context needs to be set on LocalAudioTrack in order to enable processors");case 6:if(!this.processor){n.next=9;break}return n.next=9,this.stopProcessor();case 9:if("unknown"!==this.kind){n.next=11;break}throw TypeError("cannot set processor on track of unknown kind");case 11:return a={kind:this.kind,track:this._mediaStreamTrack,audioContext:this.audioContext},B.debug("setting up audio processor ".concat(e.name)),n.next=15,e.init(a);case 15:if(this.processor=e,!this.processor.processedTrack){n.next=19;break}return n.next=19,null===(t=this.sender)||void 0===t?void 0:t.replaceTrack(this.processor.processedTrack);case 19:return n.prev=19,i(),n.finish(19);case 22:case"end":return n.stop()}}),r,this,[[3,,19,22]])})))}},{key:"setAudioContext",value:function(e){this.audioContext=e}},{key:"getSenderStats",value:function(){var e;return Xt(this,void 0,void 0,n().mark((function t(){var r;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(null===(e=this.sender)||void 0===e?void 0:e.getStats){t.next=2;break}return t.abrupt("return",void 0);case 2:return t.next=4,this.sender.getStats();case 4:return t.sent.forEach((function(e){"outbound-rtp"===e.type&&(r={type:"audio",streamId:e.id,packetsSent:e.packetsSent,packetsLost:e.packetsLost,bytesSent:e.bytesSent,timestamp:e.timestamp,roundTripTime:e.roundTripTime,jitter:e.jitter})})),t.abrupt("return",r);case 7:case"end":return t.stop()}}),t,this)})))}},{key:"checkForSilence",value:function(){return Xt(this,void 0,void 0,n().mark((function e(){var t;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Ji(this);case 2:return(t=e.sent)&&(this.isMuted||B.warn("silence detected on local audio track"),this.emit(Ii.AudioSilenceDetected)),e.abrupt("return",t);case 5:case"end":return e.stop()}}),e,this)})))}}]),r}();function Co(e,t){switch(e.kind){case"audio":return new To(e,t,!1);case"video":return new Bo(e,t,!1);default:throw new li("unsupported track type: ".concat(e.kind))}}var xo=Object.values(Di),Po=Object.values(Ni),Eo=Object.values(Oi),Ro=[Di.h180,Di.h360],Io=[Ni.h180,Ni.h360],Do=function(e){return[{scaleResolutionDownBy:2,fps:3}].map((function(t){var n;return new wi(Math.floor(e.width/t.scaleResolutionDownBy),Math.floor(e.height/t.scaleResolutionDownBy),Math.max(15e4,Math.floor(e.encoding.maxBitrate/(Math.pow(t.scaleResolutionDownBy,2)*((null!==(n=e.encoding.maxFramerate)&&void 0!==n?n:30)/t.fps)))),t.fps,e.encoding.priority)}))},No=["q","h","f"];function Oo(e,t,n,r){var i,a,o=null==r?void 0:r.videoEncoding;e&&(o=null==r?void 0:r.screenShareEncoding);var s=null==r?void 0:r.simulcast,c=null==r?void 0:r.scalabilityMode,u=null==r?void 0:r.videoCodec;if(!o&&!s&&!c||!t||!n)return[{}];o||(o=function(e,t,n,r){for(var i=function(e,t,n){if(e)return Eo;var r=t>n?t/n:n/t;if(Math.abs(r-16/9)<Math.abs(r-4/3))return xo;return Po}(e,t,n),a=i[0].encoding,o=Math.max(t,n),s=0;s<i.length;s+=1){var c=i[s];if(a=c.encoding,c.width>=o)break}if(r)switch(r){case"av1":(a=Object.assign({},a)).maxBitrate=.7*a.maxBitrate;break;case"vp9":(a=Object.assign({},a)).maxBitrate=.85*a.maxBitrate;break}return a}(e,t,n,u),B.debug("using video encoding",o));var d=new wi(t,n,o.maxBitrate,o.maxFramerate,o.priority);if(c&&Yi(u)){B.debug("using svc with scalabilityMode ".concat(c));var l=new Uo(c),h=[];if(l.spatial>3)throw new Error("unsupported scalabilityMode: ".concat(c));for(var f=0;f<l.spatial;f+=1)h.push({rid:No[2-f],maxBitrate:o.maxBitrate/Math.pow(3,f),maxFramerate:d.encoding.maxFramerate});return h[0].scalabilityMode=c,B.debug("encodings",h),h}if(!s)return[o];var p,m=[];if((m=e?null!==(i=Ao(null==r?void 0:r.screenShareSimulcastLayers))&&void 0!==i?i:Mo(e,d):null!==(a=Ao(null==r?void 0:r.videoSimulcastLayers))&&void 0!==a?a:Mo(e,d)).length>0){var v=m[0];if(m.length>1)p=k(m,2)[1];var g=Math.max(t,n);if(g>=960&&p)return Lo(t,n,[v,p,d]);if(g>=480)return Lo(t,n,[v,d])}return Lo(t,n,[d])}function _o(e,t,n){var r,i,a,o;if(n.backupCodec&&!0!==n.backupCodec&&n.backupCodec.codec!==n.videoCodec){t!==n.backupCodec.codec&&B.warn("requested a different codec than specified as backup",{serverRequested:t,backup:n.backupCodec.codec}),n.videoCodec=t,n.videoEncoding=n.backupCodec.encoding;var s=e.mediaStreamTrack.getSettings(),c=null!==(r=s.width)&&void 0!==r?r:null===(i=e.dimensions)||void 0===i?void 0:i.width,u=null!==(a=s.height)&&void 0!==a?a:null===(o=e.dimensions)||void 0===o?void 0:o.height;return Oo(e.source===Mi.Source.ScreenShare,c,u,n)}}function Mo(e,t){if(e)return Do(t);var n=t.width,r=t.height,i=n>r?n/r:r/n;return Math.abs(i-16/9)<Math.abs(i-4/3)?Ro:Io}function Lo(e,t,n){var r=[];if(n.forEach((function(n,i){if(i<No.length){var a=Math.min(e,t),o={rid:No[i],scaleResolutionDownBy:Math.max(1,a/Math.min(n.width,n.height)),maxBitrate:n.encoding.maxBitrate};n.encoding.maxFramerate&&(o.maxFramerate=n.encoding.maxFramerate);var s=$i()||0===i;n.encoding.priority&&s&&(o.priority=n.encoding.priority,o.networkPriority=n.encoding.priority),r.push(o)}})),ra()&&"ios"===oa()){var i=void 0;r.forEach((function(e){i?e.maxFramerate&&e.maxFramerate>i&&(i=e.maxFramerate):i=e.maxFramerate}));var a=!0;r.forEach((function(e){var t;e.maxFramerate!=i&&(a&&(a=!1,B.info("Simulcast on iOS React-Native requires all encodings to share the same framerate.")),B.info('Setting framerate of encoding "'.concat(null!==(t=e.rid)&&void 0!==t?t:"",'" to ').concat(i)),e.maxFramerate=i)}))}return r}function Ao(e){if(e)return e.sort((function(e,t){var n=e.encoding,r=t.encoding;return n.maxBitrate>r.maxBitrate?1:n.maxBitrate<r.maxBitrate?-1:n.maxBitrate===r.maxBitrate&&n.maxFramerate&&r.maxFramerate?n.maxFramerate>r.maxFramerate?1:-1:0}))}var Uo=function(){function e(t){s(this,e);var n=t.match(/^L(\d)T(\d)(h|_KEY|_KEY_SHIFT){0,1}$/);if(!n)throw new Error("invalid scalability mode");if(this.spatial=parseInt(n[1]),this.temporal=parseInt(n[2]),n.length>3)switch(n[3]){case"h":case"_KEY":case"_KEY_SHIFT":this.suffix=n[3]}}return u(e,[{key:"toString",value:function(){var e;return"L".concat(this.spatial,"T").concat(this.temporal).concat(null!==(e=this.suffix)&&void 0!==e?e:"")}}]),e}(),Bo=function(t){function r(t,i){var a;s(this,r);var o=2>=arguments.length||void 0===arguments[2]||arguments[2];return(a=e(this,r,[t,Mi.Kind.Video,i,o])).simulcastCodecs=new Map,a.monitorSender=function(){return Xt(m(a),void 0,void 0,n().mark((function e(){var t,r,i,a=this;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.sender){e.next=3;break}return this._currentBitrate=0,e.abrupt("return");case 3:return e.prev=3,e.next=6,this.getSenderStats();case 6:t=e.sent,e.next=13;break;case 9:return e.prev=9,e.t0=e.catch(3),B.error("could not get audio sender stats",{error:e.t0}),e.abrupt("return");case 13:r=new Map(t.map((function(e){return[e.rid,e]}))),this.prevStats&&(i=0,r.forEach((function(e,t){var n,r=null===(n=a.prevStats)||void 0===n?void 0:n.get(t);i+=wo(e,r)})),this._currentBitrate=i),this.prevStats=r;case 16:case"end":return e.stop()}}),e,this,[[3,9]])})))},a.senderLock=new ba,a}return l(r,Fa),u(r,[{key:"isSimulcast",get:function(){return!(!this.sender||1>=this.sender.getParameters().encodings.length)}},{key:"startMonitor",value:function(e){var t,n=this;if(this.signalClient=e,na()){var r=null===(t=this.sender)||void 0===t?void 0:t.getParameters();r&&(this.encodings=r.encodings),this.monitorInterval||(this.monitorInterval=setInterval((function(){n.monitorSender()}),So))}}},{key:"stop",value:function(){this._mediaStreamTrack.getConstraints(),this.simulcastCodecs.forEach((function(e){e.mediaStreamTrack.stop()})),v(h(r.prototype),"stop",this).call(this)}},{key:"pauseUpstream",value:function(){var e,t,i,a,o,s=this,c=Object.create(null,{pauseUpstream:{get:function(){return v(h(r.prototype),"pauseUpstream",s)}}});return Xt(this,void 0,void 0,n().mark((function r(){var s,u,d,l;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,c.pauseUpstream.call(this);case 2:n.prev=2,s=!0,u=$t(this.simulcastCodecs.values());case 4:return n.next=6,u.next();case 6:if(d=n.sent,e=d.done){n.next=17;break}return a=d.value,s=!1,l=a,n.next=14,null===(o=l.sender)||void 0===o?void 0:o.replaceTrack(null);case 14:s=!0,n.next=4;break;case 17:n.next=22;break;case 19:n.prev=19,n.t0=n.catch(2),t={error:n.t0};case 22:if(n.prev=22,n.prev=23,s||e||!(i=u.return)){n.next=27;break}return n.next=27,i.call(u);case 27:if(n.prev=27,!t){n.next=30;break}throw t.error;case 30:return n.finish(27);case 31:return n.finish(22);case 32:case"end":return n.stop()}}),r,this,[[2,19,22,32],[23,,27,31]])})))}},{key:"resumeUpstream",value:function(){var e,t,i,a,o,s=this,c=Object.create(null,{resumeUpstream:{get:function(){return v(h(r.prototype),"resumeUpstream",s)}}});return Xt(this,void 0,void 0,n().mark((function r(){var s,u,d,l;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,c.resumeUpstream.call(this);case 2:n.prev=2,s=!0,u=$t(this.simulcastCodecs.values());case 4:return n.next=6,u.next();case 6:if(d=n.sent,e=d.done){n.next=17;break}return a=d.value,s=!1,l=a,n.next=14,null===(o=l.sender)||void 0===o?void 0:o.replaceTrack(l.mediaStreamTrack);case 14:s=!0,n.next=4;break;case 17:n.next=22;break;case 19:n.prev=19,n.t0=n.catch(2),t={error:n.t0};case 22:if(n.prev=22,n.prev=23,s||e||!(i=u.return)){n.next=27;break}return n.next=27,i.call(u);case 27:if(n.prev=27,!t){n.next=30;break}throw t.error;case 30:return n.finish(27);case 31:return n.finish(22);case 32:case"end":return n.stop()}}),r,this,[[2,19,22,32],[23,,27,31]])})))}},{key:"mute",value:function(){var e=this,t=Object.create(null,{mute:{get:function(){return v(h(r.prototype),"mute",e)}}});return Xt(this,void 0,void 0,n().mark((function e(){var r;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.muteLock.lock();case 2:return r=e.sent,e.prev=3,this.source!==Mi.Source.Camera||this.isUserProvided||(B.debug("stopping camera track"),this._mediaStreamTrack.stop()),e.next=7,t.mute.call(this);case 7:return e.abrupt("return",this);case 8:return e.prev=8,r(),e.finish(8);case 11:case"end":return e.stop()}}),e,this,[[3,,8,11]])})))}},{key:"unmute",value:function(){var e=this,t=Object.create(null,{unmute:{get:function(){return v(h(r.prototype),"unmute",e)}}});return Xt(this,void 0,void 0,n().mark((function e(){var r;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.muteLock.lock();case 2:if(r=e.sent,e.prev=3,this.source!==Mi.Source.Camera||this.isUserProvided){e.next=8;break}return B.debug("reacquiring camera track"),e.next=8,this.restartTrack();case 8:return e.next=10,t.unmute.call(this);case 10:return e.abrupt("return",this);case 11:return e.prev=11,r(),e.finish(11);case 14:case"end":return e.stop()}}),e,this,[[3,,11,14]])})))}},{key:"setTrackMuted",value:function(e){v(h(r.prototype),"setTrackMuted",this).call(this,e);var t,n=S(this.simulcastCodecs.values());try{for(n.s();!(t=n.n()).done;){t.value.mediaStreamTrack.enabled=!e}}catch(e){n.e(e)}finally{n.f()}}},{key:"getSenderStats",value:function(){var e;return Xt(this,void 0,void 0,n().mark((function t(){var r,i;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(null===(e=this.sender)||void 0===e?void 0:e.getStats){t.next=2;break}return t.abrupt("return",[]);case 2:return r=[],t.next=5,this.sender.getStats();case 5:return(i=t.sent).forEach((function(e){var t;if("outbound-rtp"===e.type){var n={type:"video",streamId:e.id,frameHeight:e.frameHeight,frameWidth:e.frameWidth,firCount:e.firCount,pliCount:e.pliCount,nackCount:e.nackCount,packetsSent:e.packetsSent,bytesSent:e.bytesSent,framesSent:e.framesSent,timestamp:e.timestamp,rid:null!==(t=e.rid)&&void 0!==t?t:e.id,retransmittedPacketsSent:e.retransmittedPacketsSent,qualityLimitationReason:e.qualityLimitationReason,qualityLimitationResolutionChanges:e.qualityLimitationResolutionChanges},a=i.get(e.remoteId);a&&(n.jitter=a.jitter,n.packetsLost=a.packetsLost,n.roundTripTime=a.roundTripTime),r.push(n)}})),t.abrupt("return",r);case 8:case"end":return t.stop()}}),t,this)})))}},{key:"setPublishingQuality",value:function(e){for(var t=[],n=gt.LOW;n<=gt.HIGH;n+=1)t.push(new zr({quality:n,enabled:e>=n}));B.debug("setting publishing quality. max quality ".concat(e)),this.setPublishingLayers(t)}},{key:"setDeviceId",value:function(e){return Xt(this,void 0,void 0,n().mark((function t(){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this._constraints.deviceId!==e||this._mediaStreamTrack.getSettings().deviceId!==Sa(e)){t.next=2;break}return t.abrupt("return",!0);case 2:if(this._constraints.deviceId=e,this.isMuted){t.next=6;break}return t.next=6,this.restartTrack();case 6:return t.abrupt("return",this.isMuted||Sa(e)===this._mediaStreamTrack.getSettings().deviceId);case 7:case"end":return t.stop()}}),t,this)})))}},{key:"restartTrack",value:function(e){var t,r,i,a;return Xt(this,void 0,void 0,n().mark((function o(){var s,c,u,d,l,h;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return e&&"boolean"!=typeof(c=Fi({video:e})).video&&(s=c.video),n.next=3,this.restart(s);case 3:n.prev=3,u=!0,d=$t(this.simulcastCodecs.values());case 5:return n.next=7,d.next();case 7:if(l=n.sent,t=l.done){n.next=20;break}if(a=l.value,u=!1,!(h=a).sender){n.next=17;break}return h.mediaStreamTrack=this.mediaStreamTrack.clone(),n.next=17,h.sender.replaceTrack(h.mediaStreamTrack);case 17:u=!0,n.next=5;break;case 20:n.next=25;break;case 22:n.prev=22,n.t0=n.catch(3),r={error:n.t0};case 25:if(n.prev=25,n.prev=26,u||t||!(i=d.return)){n.next=30;break}return n.next=30,i.call(d);case 30:if(n.prev=30,!r){n.next=33;break}throw r.error;case 33:return n.finish(30);case 34:return n.finish(25);case 35:case"end":return n.stop()}}),o,this,[[3,22,25,35],[26,,30,34]])})))}},{key:"setProcessor",value:function(e){var t,i,a,o,s,c,u=this,d=1>=arguments.length||void 0===arguments[1]||arguments[1],l=Object.create(null,{setProcessor:{get:function(){return v(h(r.prototype),"setProcessor",u)}}});return Xt(this,void 0,void 0,n().mark((function r(){var u,h,f,p;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,l.setProcessor.call(this,e,d);case 2:if(!(null===(s=this.processor)||void 0===s?void 0:s.processedTrack)){n.next=33;break}n.prev=3,u=!0,h=$t(this.simulcastCodecs.values());case 5:return n.next=7,h.next();case 7:if(f=n.sent,t=f.done){n.next=18;break}return o=f.value,u=!1,p=o,n.next=15,null===(c=p.sender)||void 0===c?void 0:c.replaceTrack(this.processor.processedTrack);case 15:u=!0,n.next=5;break;case 18:n.next=23;break;case 20:n.prev=20,n.t0=n.catch(3),i={error:n.t0};case 23:if(n.prev=23,n.prev=24,u||t||!(a=h.return)){n.next=28;break}return n.next=28,a.call(h);case 28:if(n.prev=28,!i){n.next=31;break}throw i.error;case 31:return n.finish(28);case 32:return n.finish(23);case 33:case"end":return n.stop()}}),r,this,[[3,20,23,33],[24,,28,32]])})))}},{key:"addSimulcastTrack",value:function(e,t){if(this.simulcastCodecs.has(e))throw new Error("".concat(e," already added"));var n={codec:e,mediaStreamTrack:this.mediaStreamTrack.clone(),sender:void 0,encodings:t};return this.simulcastCodecs.set(e,n),n}},{key:"setSimulcastTrackSender",value:function(e,t){var n=this,r=this.simulcastCodecs.get(e);r&&(r.sender=t,setTimeout((function(){n.subscribedCodecs&&n.setPublishingCodecs(n.subscribedCodecs)}),5e3))}},{key:"setPublishingCodecs",value:function(e){var t,r,i,a,o,s,c;return Xt(this,void 0,void 0,n().mark((function u(){var d,l,h,f,p;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(B.debug("setting publishing codecs",{codecs:e,currentCodec:this.codec}),this.codec||0>=e.length){n.next=5;break}return n.next=4,this.setPublishingLayers(e[0].qualities);case 4:return n.abrupt("return",[]);case 5:this.subscribedCodecs=e,d=[],n.prev=7,t=!0,r=$t(e);case 9:return n.next=11,r.next();case 11:if(i=n.sent,a=i.done){n.next=52;break}if(c=i.value,t=!1,l=c,this.codec&&this.codec!==l.codec){n.next=22;break}return n.next=20,this.setPublishingLayers(l.qualities);case 20:n.next=49;break;case 22:if(h=this.simulcastCodecs.get(l.codec),B.debug("try setPublishingCodec for ".concat(l.codec),h),h&&h.sender){n.next=45;break}f=S(l.qualities),n.prev=26,f.s();case 28:if((p=f.n()).done){n.next=35;break}if(!p.value.enabled){n.next=33;break}return d.push(l.codec),n.abrupt("break",35);case 33:n.next=28;break;case 35:n.next=40;break;case 37:n.prev=37,n.t0=n.catch(26),f.e(n.t0);case 40:return n.prev=40,f.f(),n.finish(40);case 43:n.next=49;break;case 45:if(!h.encodings){n.next=49;break}return B.debug("try setPublishingLayersForSender ".concat(l.codec)),n.next=49,Fo(h.sender,h.encodings,l.qualities,this.senderLock);case 49:t=!0,n.next=9;break;case 52:n.next=57;break;case 54:n.prev=54,n.t1=n.catch(7),o={error:n.t1};case 57:if(n.prev=57,n.prev=58,t||a||!(s=r.return)){n.next=62;break}return n.next=62,s.call(r);case 62:if(n.prev=62,!o){n.next=65;break}throw o.error;case 65:return n.finish(62);case 66:return n.finish(57);case 67:return n.abrupt("return",d);case 68:case"end":return n.stop()}}),u,this,[[7,54,57,67],[26,37,40,43],[58,,62,66]])})))}},{key:"setPublishingLayers",value:function(e){return Xt(this,void 0,void 0,n().mark((function t(){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(B.debug("setting publishing layers",e),this.sender&&this.encodings){t.next=3;break}return t.abrupt("return");case 3:return t.next=5,Fo(this.sender,this.encodings,e,this.senderLock);case 5:case"end":return t.stop()}}),t,this)})))}},{key:"handleAppVisibilityChanged",value:function(){var e=this,t=Object.create(null,{handleAppVisibilityChanged:{get:function(){return v(h(r.prototype),"handleAppVisibilityChanged",e)}}});return Xt(this,void 0,void 0,n().mark((function e(){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.handleAppVisibilityChanged.call(this);case 2:if(ta()){e.next=4;break}return e.abrupt("return");case 4:this.isInBackground&&this.source===Mi.Source.Camera&&(this._mediaStreamTrack.enabled=!1);case 5:case"end":return e.stop()}}),e,this)})))}}]),r}();function Fo(e,t,r,i){return Xt(this,void 0,void 0,n().mark((function a(){var o,s,c,u;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,i.lock();case 2:if(o=n.sent,B.debug("setPublishingLayersForSender",{sender:e,qualities:r,senderEncodings:t}),n.prev=4,s=e.getParameters(),c=s.encodings){n.next=9;break}return n.abrupt("return");case 9:if(c.length===t.length){n.next=12;break}return B.warn("cannot set publishing layers, encodings mismatch"),n.abrupt("return");case 12:if(u=!1,!1,c.forEach((function(e,n){var i,a=null!==(i=e.rid)&&void 0!==i?i:"";""===a&&(a="q");var o=Jo(a),s=r.find((function(e){return e.quality===o}));s&&e.active!==s.enabled&&(u=!0,e.active=s.enabled,B.debug("setting layer ".concat(s.quality," to ").concat(e.active?"enabled":"disabled")),$i()&&(s.enabled?(e.scaleResolutionDownBy=t[n].scaleResolutionDownBy,e.maxBitrate=t[n].maxBitrate,e.maxFrameRate=t[n].maxFrameRate):(e.scaleResolutionDownBy=4,e.maxBitrate=10,e.maxFrameRate=2)))})),!u){n.next=20;break}return s.encodings=c,B.debug("setting encodings",s.encodings),n.next=20,e.setParameters(s);case 20:return n.prev=20,o(),n.finish(20);case 23:case"end":return n.stop()}}),a,null,[[4,,20,23]])})))}function Jo(e){switch(e){case"f":return gt.HIGH;case"h":return gt.MEDIUM;case"q":return gt.LOW;default:return gt.HIGH}}function jo(e,t,n,r){if(!n)return[new _t({quality:gt.HIGH,width:e,height:t,bitrate:0,ssrc:0})];if(r){for(var i=n[0].scalabilityMode,a=new Uo(i),o=[],s=0;s<a.spatial;s+=1)o.push(new _t({quality:gt.HIGH-s,width:Math.ceil(e/Math.pow(2,s)),height:Math.ceil(t/Math.pow(2,s)),bitrate:n[0].maxBitrate?Math.ceil(n[0].maxBitrate/Math.pow(3,s)):0,ssrc:0}));return o}return n.map((function(n){var r,i,a,o=null!==(r=n.scaleResolutionDownBy)&&void 0!==r?r:1,s=Jo(null!==(i=n.rid)&&void 0!==i?i:"");return new _t({quality:s,width:Math.ceil(e/o),height:Math.ceil(t/o),bitrate:null!==(a=n.maxBitrate)&&void 0!==a?a:0,ssrc:0})}))}var qo=function(t){function r(t,n,i,a){var o;return s(this,r),(o=e(this,r,[t,i])).sid=n,o.receiver=a,o}return l(r,Mi),u(r,[{key:"setMuted",value:function(e){this.isMuted!==e&&(this.isMuted=e,this._mediaStreamTrack.enabled=!e,this.emit(e?Ii.Muted:Ii.Unmuted,this))}},{key:"setMediaStream",value:function(e){var t=this;this.mediaStream=e;e.addEventListener("removetrack",(function n(r){r.track===t._mediaStreamTrack&&(e.removeEventListener("removetrack",n),t.receiver=void 0,t._currentBitrate=0,t.emit(Ii.Ended,t))}))}},{key:"start",value:function(){this.startMonitor(),v(h(r.prototype),"enable",this).call(this)}},{key:"stop",value:function(){this.stopMonitor(),v(h(r.prototype),"disable",this).call(this)}},{key:"getRTCStatsReport",value:function(){var e;return Xt(this,void 0,void 0,n().mark((function t(){var r;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(null===(e=this.receiver)||void 0===e?void 0:e.getStats){t.next=2;break}return t.abrupt("return");case 2:return t.next=4,this.receiver.getStats();case 4:return r=t.sent,t.abrupt("return",r);case 6:case"end":return t.stop()}}),t,this)})))}},{key:"startMonitor",value:function(){var e=this;this.monitorInterval||(this.monitorInterval=setInterval((function(){return e.monitorReceiver()}),So))}}]),r}(),Vo=function(t){function r(t,i,a,o,c){var u;return s(this,r),(u=e(this,r,[t,i,Mi.Kind.Audio,a])).monitorReceiver=function(){return Xt(m(u),void 0,void 0,n().mark((function e(){var t;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.receiver){e.next=3;break}return this._currentBitrate=0,e.abrupt("return");case 3:return e.next=5,this.getReceiverStats();case 5:(t=e.sent)&&this.prevStats&&this.receiver&&(this._currentBitrate=wo(t,this.prevStats)),this.prevStats=t;case 8:case"end":return e.stop()}}),e,this)})))},u.audioContext=o,u.webAudioPluginNodes=[],c&&(u.sinkId=c.deviceId),u}return l(r,qo),u(r,[{key:"setVolume",value:function(e){var t,n,r=S(this.attachedElements);try{for(r.s();!(n=r.n()).done;){var i=n.value;this.audioContext?null===(t=this.gainNode)||void 0===t||t.gain.setTargetAtTime(e,0,.1):i.volume=e}}catch(e){r.e(e)}finally{r.f()}ra()&&this._mediaStreamTrack._setVolume(e),this.elementVolume=e}},{key:"getVolume",value:function(){if(this.elementVolume)return this.elementVolume;if(ra())return 1;var e=0;return this.attachedElements.forEach((function(t){t.volume>e&&(e=t.volume)})),e}},{key:"setSinkId",value:function(e){return Xt(this,void 0,void 0,n().mark((function t(){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return this.sinkId=e,t.next=3,Promise.all(this.attachedElements.map((function(t){if(Xi(t))return t.setSinkId(e)})));case 3:case"end":return t.stop()}}),t,this)})))}},{key:"attach",value:function(e){var t=0===this.attachedElements.length;return e?v(h(r.prototype),"attach",this).call(this,e):e=v(h(r.prototype),"attach",this).call(this),this.sinkId&&Xi(e)&&e.setSinkId(this.sinkId),this.audioContext&&t&&(B.debug("using audio context mapping"),this.connectWebAudio(this.audioContext,e),e.volume=0,e.muted=!0),this.elementVolume&&this.setVolume(this.elementVolume),e}},{key:"detach",value:function(e){var t;return e?(t=v(h(r.prototype),"detach",this).call(this,e),this.audioContext&&(this.attachedElements.length>0?this.connectWebAudio(this.audioContext,this.attachedElements[0]):this.disconnectWebAudio())):(t=v(h(r.prototype),"detach",this).call(this),this.disconnectWebAudio()),t}},{key:"setAudioContext",value:function(e){this.audioContext=e,e&&this.attachedElements.length>0?this.connectWebAudio(e,this.attachedElements[0]):e||this.disconnectWebAudio()}},{key:"setWebAudioPlugins",value:function(e){this.webAudioPluginNodes=e,this.attachedElements.length>0&&this.audioContext&&this.connectWebAudio(this.audioContext,this.attachedElements[0])}},{key:"connectWebAudio",value:function(e,t){var n=this;this.disconnectWebAudio(),this.sourceNode=e.createMediaStreamSource(t.srcObject);var r=this.sourceNode;this.webAudioPluginNodes.forEach((function(e){r.connect(e),r=e})),this.gainNode=e.createGain(),r.connect(this.gainNode),this.gainNode.connect(e.destination),this.elementVolume&&this.gainNode.gain.setTargetAtTime(this.elementVolume,0,.1),"running"!==e.state&&e.resume().then((function(){"running"!==e.state&&n.emit(Ii.AudioPlaybackFailed,new Error("Audio Context couldn't be started automatically"))})).catch((function(e){n.emit(Ii.AudioPlaybackFailed,e)}))}},{key:"disconnectWebAudio",value:function(){var e,t;null===(e=this.gainNode)||void 0===e||e.disconnect(),null===(t=this.sourceNode)||void 0===t||t.disconnect(),this.gainNode=void 0,this.sourceNode=void 0}},{key:"getReceiverStats",value:function(){return Xt(this,void 0,void 0,n().mark((function e(){var t;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.receiver&&this.receiver.getStats){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,this.receiver.getStats();case 4:return e.sent.forEach((function(e){"inbound-rtp"===e.type&&(t={type:"audio",timestamp:e.timestamp,jitter:e.jitter,bytesReceived:e.bytesReceived,concealedSamples:e.concealedSamples,concealmentEvents:e.concealmentEvents,silentConcealedSamples:e.silentConcealedSamples,silentConcealmentEvents:e.silentConcealmentEvents,totalAudioEnergy:e.totalAudioEnergy,totalSamplesDuration:e.totalSamplesDuration})})),e.abrupt("return",t);case 7:case"end":return e.stop()}}),e,this)})))}}]),r}(),Ho=function(t){function r(t,i,a,o){var c;return s(this,r),(c=e(this,r,[t,i,Mi.Kind.Video,a])).elementInfos=[],c.monitorReceiver=function(){return Xt(m(c),void 0,void 0,n().mark((function e(){var t;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.receiver){e.next=3;break}return this._currentBitrate=0,e.abrupt("return");case 3:return e.next=5,this.getReceiverStats();case 5:(t=e.sent)&&this.prevStats&&this.receiver&&(this._currentBitrate=wo(t,this.prevStats)),this.prevStats=t;case 8:case"end":return e.stop()}}),e,this)})))},c.debouncedHandleResize=Aa((function(){c.updateDimensions()}),100),c.adaptiveStreamSettings=o,c}return l(r,qo),u(r,[{key:"isAdaptiveStream",get:function(){return void 0!==this.adaptiveStreamSettings}},{key:"mediaStreamTrack",get:function(){return this._mediaStreamTrack}},{key:"setMuted",value:function(e){var t=this;v(h(r.prototype),"setMuted",this).call(this,e),this.attachedElements.forEach((function(n){e?Ai(t._mediaStreamTrack,n):Li(t._mediaStreamTrack,n)}))}},{key:"attach",value:function(e){if(e?v(h(r.prototype),"attach",this).call(this,e):e=v(h(r.prototype),"attach",this).call(this),this.adaptiveStreamSettings&&void 0===this.elementInfos.find((function(t){return t.element===e}))){var t=new Wo(e);this.observeElementInfo(t)}return e}},{key:"observeElementInfo",value:function(e){var t=this;this.adaptiveStreamSettings&&void 0===this.elementInfos.find((function(t){return t===e}))?(e.handleResize=function(){t.debouncedHandleResize()},e.handleVisibilityChanged=function(){t.updateVisibility()},this.elementInfos.push(e),e.observe(),this.debouncedHandleResize(),this.updateVisibility()):B.warn("visibility resize observer not triggered")}},{key:"stopObservingElementInfo",value:function(e){if(this.isAdaptiveStream){var t,n=this.elementInfos.filter((function(t){return t===e})),r=S(n);try{for(r.s();!(t=r.n()).done;){t.value.stopObserving()}}catch(e){r.e(e)}finally{r.f()}this.elementInfos=this.elementInfos.filter((function(t){return t!==e})),this.updateVisibility(),this.debouncedHandleResize()}else B.warn("stopObservingElementInfo ignored")}},{key:"detach",value:function(e){var t;if(e)return this.stopObservingElement(e),v(h(r.prototype),"detach",this).call(this,e);var n,i=S(t=v(h(r.prototype),"detach",this).call(this));try{for(i.s();!(n=i.n()).done;){var a=n.value;this.stopObservingElement(a)}}catch(e){i.e(e)}finally{i.f()}return t}},{key:"getDecoderImplementation",value:function(){var e;return null===(e=this.prevStats)||void 0===e?void 0:e.decoderImplementation}},{key:"getReceiverStats",value:function(){return Xt(this,void 0,void 0,n().mark((function e(){var t;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.receiver&&this.receiver.getStats){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,this.receiver.getStats();case 4:return e.sent.forEach((function(e){"inbound-rtp"===e.type&&(t={type:"video",framesDecoded:e.framesDecoded,framesDropped:e.framesDropped,framesReceived:e.framesReceived,packetsReceived:e.packetsReceived,packetsLost:e.packetsLost,frameWidth:e.frameWidth,frameHeight:e.frameHeight,pliCount:e.pliCount,firCount:e.firCount,nackCount:e.nackCount,jitter:e.jitter,timestamp:e.timestamp,bytesReceived:e.bytesReceived,decoderImplementation:e.decoderImplementation})})),e.abrupt("return",t);case 7:case"end":return e.stop()}}),e,this)})))}},{key:"stopObservingElement",value:function(e){var t,n=this.elementInfos.filter((function(t){return t.element===e})),r=S(n);try{for(r.s();!(t=r.n()).done;){var i=t.value;this.stopObservingElementInfo(i)}}catch(e){r.e(e)}finally{r.f()}}},{key:"handleAppVisibilityChanged",value:function(){var e=this,t=Object.create(null,{handleAppVisibilityChanged:{get:function(){return v(h(r.prototype),"handleAppVisibilityChanged",e)}}});return Xt(this,void 0,void 0,n().mark((function e(){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.handleAppVisibilityChanged.call(this);case 2:if(this.isAdaptiveStream){e.next=4;break}return e.abrupt("return");case 4:this.updateVisibility();case 5:case"end":return e.stop()}}),e,this)})))}},{key:"updateVisibility",value:function(){var e,t,n=this,r=this.elementInfos.reduce((function(e,t){return Math.max(e,t.visibilityChangedAt||0)}),0),i=!(null!==(t=null===(e=this.adaptiveStreamSettings)||void 0===e?void 0:e.pauseVideoInBackground)&&void 0!==t&&!t)&&this.isInBackground,a=this.elementInfos.some((function(e){return e.pictureInPicture})),o=this.elementInfos.some((function(e){return e.visible}))&&!i||a;this.lastVisible!==o&&(o||Date.now()-r>=100?(this.lastVisible=o,this.emit(Ii.VisibilityChanged,o,this)):mi.setTimeout((function(){n.updateVisibility()}),100))}},{key:"updateDimensions",value:function(){var e,t,n,r=0,i=0,a=this.getPixelDensity(),o=S(this.elementInfos);try{for(o.s();!(n=o.n()).done;){var s=n.value,c=s.width()*a,u=s.height()*a;c+u>r+i&&(r=c,i=u)}}catch(e){o.e(e)}finally{o.f()}(null===(e=this.lastDimensions)||void 0===e?void 0:e.width)===r&&(null===(t=this.lastDimensions)||void 0===t?void 0:t.height)===i||(this.lastDimensions={width:r,height:i},this.emit(Ii.VideoDimensionsChanged,this.lastDimensions,this))}},{key:"getPixelDensity",value:function(){var e,t=null===(e=this.adaptiveStreamSettings)||void 0===e?void 0:e.pixelDensity;return"screen"===t?sa():t||(sa()>2?2:1)}}]),r}(),Wo=function(){function e(t,n){var r=this;s(this,e),this.onVisibilityChanged=function(e){var t,n=e.target,i=e.isIntersecting;n===r.element&&(r.isIntersecting=i,r.visibilityChangedAt=Date.now(),null===(t=r.handleVisibilityChanged)||void 0===t||t.call(r))},this.onEnterPiP=function(){var e;r.isPiP=!0,null===(e=r.handleVisibilityChanged)||void 0===e||e.call(r)},this.onLeavePiP=function(){var e;r.isPiP=!1,null===(e=r.handleVisibilityChanged)||void 0===e||e.call(r)},this.element=t,this.isIntersecting=null!=n?n:Go(t),this.isPiP=na()&&document.pictureInPictureElement===t,this.visibilityChangedAt=0}return u(e,[{key:"visible",get:function(){return this.isPiP||this.isIntersecting}},{key:"pictureInPicture",get:function(){return this.isPiP}},{key:"width",value:function(){return this.element.clientWidth}},{key:"height",value:function(){return this.element.clientHeight}},{key:"observe",value:function(){var e=this;this.isIntersecting=Go(this.element),this.isPiP=document.pictureInPictureElement===this.element,this.element.handleResize=function(){var t;null===(t=e.handleResize)||void 0===t||t.call(e)},this.element.handleVisibilityChanged=this.onVisibilityChanged,ma().observe(this.element),fa().observe(this.element),this.element.addEventListener("enterpictureinpicture",this.onEnterPiP),this.element.addEventListener("leavepictureinpicture",this.onLeavePiP)}},{key:"stopObserving",value:function(){var e,t;null===(e=ma())||void 0===e||e.unobserve(this.element),null===(t=fa())||void 0===t||t.unobserve(this.element),this.element.removeEventListener("enterpictureinpicture",this.onEnterPiP),this.element.removeEventListener("leavepictureinpicture",this.onLeavePiP)}}]),e}();function Go(e){for(var t=e.offsetTop,n=e.offsetLeft,r=e.offsetWidth,i=e.offsetHeight,a=e.hidden,o=getComputedStyle(e),s=o.opacity,c=o.display;e.offsetParent;)t+=(e=e.offsetParent).offsetTop,n+=e.offsetLeft;return t<window.pageYOffset+window.innerHeight&&n<window.pageXOffset+window.innerWidth&&t+i>window.pageYOffset&&n+r>window.pageXOffset&&!a&&(""===s||parseFloat(s)>0)&&"none"!==c}var Ko=function(t){function n(t,r,i){var a;return s(this,n),(a=e(this,n)).metadataMuted=!1,a.encryption=It.NONE,a.handleMuted=function(){a.emit(Ii.Muted)},a.handleUnmuted=function(){a.emit(Ii.Unmuted)},a.setMaxListeners(100),a.kind=t,a.trackSid=r,a.trackName=i,a.source=Mi.Source.Unknown,a}return l(n,kn.EventEmitter),u(n,[{key:"setTrack",value:function(e){this.track&&(this.track.off(Ii.Muted,this.handleMuted),this.track.off(Ii.Unmuted,this.handleUnmuted)),this.track=e,e&&(e.on(Ii.Muted,this.handleMuted),e.on(Ii.Unmuted,this.handleUnmuted))}},{key:"isMuted",get:function(){return this.metadataMuted}},{key:"isEnabled",get:function(){return!0}},{key:"isSubscribed",get:function(){return void 0!==this.track}},{key:"isEncrypted",get:function(){return this.encryption!==It.NONE}},{key:"audioTrack",get:function(){if(this.track instanceof To||this.track instanceof Vo)return this.track}},{key:"videoTrack",get:function(){if(this.track instanceof Bo||this.track instanceof Ho)return this.track}},{key:"updateInfo",value:function(e){this.trackSid=e.sid,this.trackName=e.name,this.source=Mi.sourceFromProto(e.source),this.mimeType=e.mimeType,this.kind===Mi.Kind.Video&&e.width>0&&(this.dimensions={width:e.width,height:e.height},this.simulcasted=e.simulcast),this.encryption=e.encryption,this.trackInfo=e,B.debug("update publication info",{info:e})}}]),n}();!function(e){var t,n;(t=e.SubscriptionStatus||(e.SubscriptionStatus={})).Desired="desired",t.Subscribed="subscribed",t.Unsubscribed="unsubscribed",(n=e.PermissionStatus||(e.PermissionStatus={})).Allowed="allowed",n.NotAllowed="not_allowed"}(Ko||(Ko={}));var zo,Qo=function(t){function r(t,n,i){var a;return s(this,r),(a=e(this,r,[t,n.sid,n.name])).track=void 0,a.handleTrackEnded=function(){a.emit(Ii.Ended)},a.updateInfo(n),a.setTrack(i),a}return l(r,Ko),u(r,[{key:"isUpstreamPaused",get:function(){var e;return null===(e=this.track)||void 0===e?void 0:e.isUpstreamPaused}},{key:"setTrack",value:function(e){this.track&&this.track.off(Ii.Ended,this.handleTrackEnded),v(h(r.prototype),"setTrack",this).call(this,e),e&&e.on(Ii.Ended,this.handleTrackEnded)}},{key:"isMuted",get:function(){return this.track?this.track.isMuted:v(h(r.prototype),"isMuted",this)}},{key:"audioTrack",get:function(){return v(h(r.prototype),"audioTrack",this)}},{key:"videoTrack",get:function(){return v(h(r.prototype),"videoTrack",this)}},{key:"mute",value:function(){var e;return Xt(this,void 0,void 0,n().mark((function t(){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",null===(e=this.track)||void 0===e?void 0:e.mute());case 1:case"end":return t.stop()}}),t,this)})))}},{key:"unmute",value:function(){var e;return Xt(this,void 0,void 0,n().mark((function t(){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",null===(e=this.track)||void 0===e?void 0:e.unmute());case 1:case"end":return t.stop()}}),t,this)})))}},{key:"pauseUpstream",value:function(){var e;return Xt(this,void 0,void 0,n().mark((function t(){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,null===(e=this.track)||void 0===e?void 0:e.pauseUpstream();case 2:case"end":return t.stop()}}),t,this)})))}},{key:"resumeUpstream",value:function(){var e;return Xt(this,void 0,void 0,n().mark((function t(){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,null===(e=this.track)||void 0===e?void 0:e.resumeUpstream();case 2:case"end":return t.stop()}}),t,this)})))}}]),r}();!function(e){e.Excellent="excellent",e.Good="good",e.Poor="poor",e.Unknown="unknown"}(zo||(zo={}));var Yo=function(t){function n(t,r,i,a){var o;return s(this,n),(o=e(this,n)).audioLevel=0,o.isSpeaking=!1,o._connectionQuality=zo.Unknown,o.setMaxListeners(100),o.sid=t,o.identity=r,o.name=i,o.metadata=a,o.audioTracks=new Map,o.videoTracks=new Map,o.tracks=new Map,o}return l(n,kn.EventEmitter),u(n,[{key:"isEncrypted",get:function(){return this.tracks.size>0&&Array.from(this.tracks.values()).every((function(e){return e.isEncrypted}))}},{key:"getTracks",value:function(){return Array.from(this.tracks.values())}},{key:"getTrack",value:function(e){var t,n=S(this.tracks);try{for(n.s();!(t=n.n()).done;){var r=k(t.value,2)[1];if(r.source===e)return r}}catch(e){n.e(e)}finally{n.f()}}},{key:"getTrackByName",value:function(e){var t,n=S(this.tracks);try{for(n.s();!(t=n.n()).done;){var r=k(t.value,2)[1];if(r.trackName===e)return r}}catch(e){n.e(e)}finally{n.f()}}},{key:"connectionQuality",get:function(){return this._connectionQuality}},{key:"isCameraEnabled",get:function(){var e,t=this.getTrack(Mi.Source.Camera);return!(null===(e=null==t?void 0:t.isMuted)||void 0===e||e)}},{key:"isMicrophoneEnabled",get:function(){var e,t=this.getTrack(Mi.Source.Microphone);return!(null===(e=null==t?void 0:t.isMuted)||void 0===e||e)}},{key:"isScreenShareEnabled",get:function(){return!!this.getTrack(Mi.Source.ScreenShare)}},{key:"isLocal",get:function(){return!1}},{key:"joinedAt",get:function(){return this.participantInfo?new Date(1e3*Number.parseInt(this.participantInfo.joinedAt.toString())):new Date}},{key:"updateInfo",value:function(e){return(!this.participantInfo||this.participantInfo.sid!==e.sid||this.participantInfo.version<=e.version)&&(this.identity=e.identity,this.sid=e.sid,this._setName(e.name),this._setMetadata(e.metadata),e.permission&&this.setPermissions(e.permission),this.participantInfo=e,B.trace("update participant info",{info:e}),!0)}},{key:"_setMetadata",value:function(e){var t=this.metadata!==e,n=this.metadata;this.metadata=e,t&&this.emit(Ei.ParticipantMetadataChanged,n)}},{key:"_setName",value:function(e){var t=this.name!==e;this.name=e,t&&this.emit(Ei.ParticipantNameChanged,e)}},{key:"setPermissions",value:function(e){var t,n,r,i,a,o=this,s=this.permissions,c=e.canPublish!==(null===(t=this.permissions)||void 0===t?void 0:t.canPublish)||e.canSubscribe!==(null===(n=this.permissions)||void 0===n?void 0:n.canSubscribe)||e.canPublishData!==(null===(r=this.permissions)||void 0===r?void 0:r.canPublishData)||e.hidden!==(null===(i=this.permissions)||void 0===i?void 0:i.hidden)||e.recorder!==(null===(a=this.permissions)||void 0===a?void 0:a.recorder)||e.canPublishSources.length!==this.permissions.canPublishSources.length||e.canPublishSources.some((function(e,t){var n;return e!==(null===(n=o.permissions)||void 0===n?void 0:n.canPublishSources[t])}));return this.permissions=e,c&&this.emit(Ei.ParticipantPermissionsChanged,s),c}},{key:"setIsSpeaking",value:function(e){e!==this.isSpeaking&&(this.isSpeaking=e,e&&(this.lastSpokeAt=new Date),this.emit(Ei.IsSpeakingChanged,e))}},{key:"setConnectionQuality",value:function(e){var t=this._connectionQuality;this._connectionQuality=function(e){switch(e){case yt.EXCELLENT:return zo.Excellent;case yt.GOOD:return zo.Good;case yt.POOR:return zo.Poor;default:return zo.Unknown}}(e),t!==this._connectionQuality&&this.emit(Ei.ConnectionQualityChanged,this._connectionQuality)}},{key:"setAudioContext",value:function(e){this.audioContext=e,this.audioTracks.forEach((function(t){return(t.track instanceof Vo||t.track instanceof To)&&t.track.setAudioContext(e)}))}},{key:"addTrackPublication",value:function(e){var t=this;e.on(Ii.Muted,(function(){t.emit(Ei.TrackMuted,e)})),e.on(Ii.Unmuted,(function(){t.emit(Ei.TrackUnmuted,e)}));var n=e;switch(n.track&&(n.track.sid=e.trackSid),this.tracks.set(e.trackSid,e),e.kind){case Mi.Kind.Audio:this.audioTracks.set(e.trackSid,e);break;case Mi.Kind.Video:this.videoTracks.set(e.trackSid,e);break}}}]),n}();var Xo,Zo=function(t){function n(t,r,i){var a;return s(this,n),(a=e(this,n,[t,r.sid,r.name])).track=void 0,a.allowed=!0,a.disabled=!1,a.currentVideoQuality=gt.HIGH,a.handleEnded=function(e){a.setTrack(void 0),a.emit(Ii.Ended,e)},a.handleVisibilityChange=function(e){B.debug("adaptivestream video visibility ".concat(a.trackSid,", visible=").concat(e),{trackSid:a.trackSid}),a.disabled=!e,a.emitTrackUpdate()},a.handleVideoDimensionsChange=function(e){B.debug("adaptivestream video dimensions ".concat(e.width,"x").concat(e.height),{trackSid:a.trackSid}),a.videoDimensions=e,a.emitTrackUpdate()},a.subscribed=i,a.updateInfo(r),a}return l(n,Ko),u(n,[{key:"setSubscribed",value:function(e){var t=this.subscriptionStatus,n=this.permissionStatus;this.subscribed=e,e&&(this.allowed=!0);var r=new Ar({trackSids:[this.trackSid],subscribe:this.subscribed,participantTracks:[new Ft({participantSid:"",trackSids:[this.trackSid]})]});this.emit(Ii.UpdateSubscription,r),this.emitSubscriptionUpdateIfChanged(t),this.emitPermissionUpdateIfChanged(n)}},{key:"subscriptionStatus",get:function(){return!1===this.subscribed?Ko.SubscriptionStatus.Unsubscribed:v(h(n.prototype),"isSubscribed",this)?Ko.SubscriptionStatus.Subscribed:Ko.SubscriptionStatus.Desired}},{key:"permissionStatus",get:function(){return this.allowed?Ko.PermissionStatus.Allowed:Ko.PermissionStatus.NotAllowed}},{key:"isSubscribed",get:function(){return!1!==this.subscribed&&v(h(n.prototype),"isSubscribed",this)}},{key:"isDesired",get:function(){return!1!==this.subscribed}},{key:"isEnabled",get:function(){return!this.disabled}},{key:"setEnabled",value:function(e){this.isManualOperationAllowed()&&this.disabled!==!e&&(this.disabled=!e,this.emitTrackUpdate())}},{key:"setVideoQuality",value:function(e){this.isManualOperationAllowed()&&this.currentVideoQuality!==e&&(this.currentVideoQuality=e,this.videoDimensions=void 0,this.emitTrackUpdate())}},{key:"setVideoDimensions",value:function(e){var t,n;this.isManualOperationAllowed()&&((null===(t=this.videoDimensions)||void 0===t?void 0:t.width)===e.width&&(null===(n=this.videoDimensions)||void 0===n?void 0:n.height)===e.height||(this.track instanceof Ho&&(this.videoDimensions=e),this.currentVideoQuality=void 0,this.emitTrackUpdate()))}},{key:"setVideoFPS",value:function(e){this.isManualOperationAllowed()&&this.track instanceof Ho&&this.fps!==e&&(this.fps=e,this.emitTrackUpdate())}},{key:"videoQuality",get:function(){return this.currentVideoQuality}},{key:"setTrack",value:function(e){var t=this.subscriptionStatus,r=this.permissionStatus,i=this.track;i!==e&&(i&&(i.off(Ii.VideoDimensionsChanged,this.handleVideoDimensionsChange),i.off(Ii.VisibilityChanged,this.handleVisibilityChange),i.off(Ii.Ended,this.handleEnded),i.detach(),i.stopMonitor(),this.emit(Ii.Unsubscribed,i)),v(h(n.prototype),"setTrack",this).call(this,e),e&&(e.sid=this.trackSid,e.on(Ii.VideoDimensionsChanged,this.handleVideoDimensionsChange),e.on(Ii.VisibilityChanged,this.handleVisibilityChange),e.on(Ii.Ended,this.handleEnded),this.emit(Ii.Subscribed,e)),this.emitPermissionUpdateIfChanged(r),this.emitSubscriptionUpdateIfChanged(t))}},{key:"setAllowed",value:function(e){var t=this.subscriptionStatus,n=this.permissionStatus;this.allowed=e,this.emitPermissionUpdateIfChanged(n),this.emitSubscriptionUpdateIfChanged(t)}},{key:"setSubscriptionError",value:function(e){this.emit(Ii.SubscriptionFailed,e)}},{key:"updateInfo",value:function(e){v(h(n.prototype),"updateInfo",this).call(this,e);var t=this.metadataMuted;this.metadataMuted=e.muted,this.track?this.track.setMuted(e.muted):t!==e.muted&&this.emit(e.muted?Ii.Muted:Ii.Unmuted)}},{key:"emitSubscriptionUpdateIfChanged",value:function(e){var t=this.subscriptionStatus;e!==t&&this.emit(Ii.SubscriptionStatusChanged,t,e)}},{key:"emitPermissionUpdateIfChanged",value:function(e){this.permissionStatus!==e&&this.emit(Ii.SubscriptionPermissionChanged,this.permissionStatus,e)}},{key:"isManualOperationAllowed",value:function(){return this.kind===Mi.Kind.Video&&this.isAdaptiveStream?(B.warn("adaptive stream is enabled, cannot change video track settings",{trackSid:this.trackSid}),!1):!!this.isDesired||(B.warn("cannot update track settings when not subscribed",{trackSid:this.trackSid}),!1)}},{key:"isAdaptiveStream",get:function(){return this.track instanceof Ho&&this.track.isAdaptiveStream}},{key:"emitTrackUpdate",value:function(){var e=new Ur({trackSids:[this.trackSid],disabled:this.disabled,fps:this.fps});this.videoDimensions?(e.width=Math.ceil(this.videoDimensions.width),e.height=Math.ceil(this.videoDimensions.height)):void 0!==this.currentVideoQuality?e.quality=this.currentVideoQuality:e.quality=gt.HIGH,this.emit(Ii.UpdateSettings,e)}}]),n}(),$o=function(t){function r(t,n,i,a,o){var c;return s(this,r),(c=e(this,r,[n,i||"",a,o])).signalClient=t,c.tracks=new Map,c.audioTracks=new Map,c.videoTracks=new Map,c.volumeMap=new Map,c}return l(r,Yo),u(r,[{key:"addTrackPublication",value:function(e){var t=this;v(h(r.prototype),"addTrackPublication",this).call(this,e),e.on(Ii.UpdateSettings,(function(e){B.debug("send update settings",e),t.signalClient.sendUpdateTrackSettings(e)})),e.on(Ii.UpdateSubscription,(function(e){e.participantTracks.forEach((function(e){e.participantSid=t.sid})),t.signalClient.sendUpdateSubscription(e)})),e.on(Ii.SubscriptionPermissionChanged,(function(n){t.emit(Ei.TrackSubscriptionPermissionChanged,e,n)})),e.on(Ii.SubscriptionStatusChanged,(function(n){t.emit(Ei.TrackSubscriptionStatusChanged,e,n)})),e.on(Ii.Subscribed,(function(n){t.emit(Ei.TrackSubscribed,n,e)})),e.on(Ii.Unsubscribed,(function(n){t.emit(Ei.TrackUnsubscribed,n,e)})),e.on(Ii.SubscriptionFailed,(function(n){t.emit(Ei.TrackSubscriptionFailed,e.trackSid,n)}))}},{key:"getTrack",value:function(e){var t=v(h(r.prototype),"getTrack",this).call(this,e);if(t)return t}},{key:"getTrackByName",value:function(e){var t=v(h(r.prototype),"getTrackByName",this).call(this,e);if(t)return t}},{key:"setVolume",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Mi.Source.Microphone;this.volumeMap.set(t,e);var n=this.getTrack(t);n&&n.track&&n.track.setVolume(e)}},{key:"getVolume",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Mi.Source.Microphone,t=this.getTrack(e);return t&&t.track?t.track.getVolume():this.volumeMap.get(e)}},{key:"addSubscribedMediaTrack",value:function(e,t,n,r,i,a){var o,s=this,c=this.getTrackPublication(t);return c||t.startsWith("TR")||this.tracks.forEach((function(t){c||e.kind!==t.kind.toString()||(c=t)})),c?"ended"===e.readyState?(B.error("unable to subscribe because MediaStreamTrack is ended. Do not call MediaStreamTrack.stop()",{participant:this.sid,trackSid:t}),void this.emit(Ei.TrackSubscriptionFailed,t)):((o="video"===e.kind?new Ho(e,t,r,i):new Vo(e,t,r,this.audioContext,this.audioOutput)).source=c.source,o.isMuted=c.isMuted,o.setMediaStream(n),o.start(),c.setTrack(o),this.volumeMap.has(c.source)&&o instanceof Vo&&o.setVolume(this.volumeMap.get(c.source)),c):0===a?(B.error("could not find published track",{participant:this.sid,trackSid:t}),void this.emit(Ei.TrackSubscriptionFailed,t)):(void 0===a&&(a=20),void setTimeout((function(){s.addSubscribedMediaTrack(e,t,n,r,i,a-1)}),150))}},{key:"hasMetadata",get:function(){return!!this.participantInfo}},{key:"getTrackPublication",value:function(e){return this.tracks.get(e)}},{key:"updateInfo",value:function(e){var t=this;if(!v(h(r.prototype),"updateInfo",this).call(this,e))return!1;var n=new Map,i=new Map;return e.tracks.forEach((function(r){var a,o=t.getTrackPublication(r.sid);if(o)o.updateInfo(r);else{var s=Mi.kindFromProto(r.type);if(!s)return;(o=new Zo(s,r,null===(a=t.signalClient.connectOptions)||void 0===a?void 0:a.autoSubscribe)).updateInfo(r),i.set(r.sid,o);var c=Array.from(t.tracks.values()).find((function(e){return e.source===(null==o?void 0:o.source)}));c&&o.source!==Mi.Source.Unknown&&B.debug("received a second track publication for ".concat(t.identity," with the same source: ").concat(o.source),{oldTrack:c,newTrack:o,participant:t,participantInfo:e}),t.addTrackPublication(o)}n.set(r.sid,o)})),this.tracks.forEach((function(e){n.has(e.trackSid)||(B.trace("detected removed track on remote participant, unpublishing",{publication:e,participantSid:t.sid}),t.unpublishTrack(e.trackSid,!0))})),i.forEach((function(e){t.emit(Ei.TrackPublished,e)})),!0}},{key:"unpublishTrack",value:function(e,t){var n=this.tracks.get(e);if(n){var r=n.track;switch(r&&(r.stop(),n.setTrack(void 0)),this.tracks.delete(e),n.kind){case Mi.Kind.Audio:this.audioTracks.delete(e);break;case Mi.Kind.Video:this.videoTracks.delete(e);break}t&&this.emit(Ei.TrackUnpublished,n)}}},{key:"setAudioOutput",value:function(e){return Xt(this,void 0,void 0,n().mark((function t(){var r;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return this.audioOutput=e,r=[],this.audioTracks.forEach((function(t){var n;t.track instanceof Vo&&r.push(t.track.setSinkId(null!==(n=e.deviceId)&&void 0!==n?n:"default"))})),t.next=5,Promise.all(r);case 5:case"end":return t.stop()}}),t,this)})))}},{key:"emit",value:function(e){for(var t,n=arguments.length,i=new Array(n>1?n-1:0),a=1;n>a;a++)i[a-1]=arguments[a];return B.trace("participant event",{participant:this.sid,event:e,args:i}),(t=v(h(r.prototype),"emit",this)).call.apply(t,[this,e].concat(i))}}],[{key:"fromParticipantInfo",value:function(e,t){return new r(e,t.sid,t.identity,t.name,t.metadata)}}]),r}(),es=function(t){function r(t,i,a,o){var c;return s(this,r),(c=e(this,r,[t,i])).pendingPublishing=new Set,c.pendingPublishPromises=new Map,c.participantTrackPermissions=[],c.allParticipantsAllowedToSubscribe=!0,c.encryptionType=It.NONE,c.handleReconnecting=function(){c.reconnectFuture||(c.reconnectFuture=new ya)},c.handleReconnected=function(){var e,t;null===(t=null===(e=c.reconnectFuture)||void 0===e?void 0:e.resolve)||void 0===t||t.call(e),c.reconnectFuture=void 0,c.updateTrackSubscriptionPermissions()},c.handleDisconnected=function(){var e,t;c.reconnectFuture&&(c.reconnectFuture.promise.catch((function(e){return B.warn(e)})),null===(t=null===(e=c.reconnectFuture)||void 0===e?void 0:e.reject)||void 0===t||t.call(e,"Got disconnected during reconnection attempt"),c.reconnectFuture=void 0)},c.updateTrackSubscriptionPermissions=function(){B.debug("updating track subscription permissions",{allParticipantsAllowed:c.allParticipantsAllowedToSubscribe,participantTrackPermissions:c.participantTrackPermissions}),c.engine.client.sendUpdateSubscriptionPermissions(c.allParticipantsAllowedToSubscribe,c.participantTrackPermissions.map((function(e){return function(e){var t,n,r;if(!e.participantSid&&!e.participantIdentity)throw new Error("Invalid track permission, must provide at least one of participantIdentity and participantSid");return new Xr({participantIdentity:null!==(t=e.participantIdentity)&&void 0!==t?t:"",participantSid:null!==(n=e.participantSid)&&void 0!==n?n:"",allTracks:null!==(r=e.allowAll)&&void 0!==r&&r,trackSids:e.allowedTrackSids||[]})}(e)})))},c.onTrackUnmuted=function(e){c.onTrackMuted(e,e.isUpstreamPaused)},c.onTrackMuted=function(e,t){void 0===t&&(t=!0),e.sid?c.engine.updateMuteStatus(e.sid,t):B.error("could not update mute status for unpublished track",e)},c.onTrackUpstreamPaused=function(e){B.debug("upstream paused"),c.onTrackMuted(e,!0)},c.onTrackUpstreamResumed=function(e){B.debug("upstream resumed"),c.onTrackMuted(e,e.isMuted)},c.handleSubscribedQualityUpdate=function(e){return Xt(m(c),void 0,void 0,n().mark((function t(){var r,i,a,o,s,c,u,d,l,h,f,p;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(null===(s=this.roomOptions)||void 0===s?void 0:s.dynacast){t.next=2;break}return t.abrupt("return");case 2:if(u=this.videoTracks.get(e.trackSid)){t.next=6;break}return B.warn("received subscribed quality update for unknown track",{method:"handleSubscribedQualityUpdate",sid:e.trackSid}),t.abrupt("return");case 6:if(e.subscribedCodecs.length<=0){t.next=46;break}if(u.videoTrack){t.next=9;break}return t.abrupt("return");case 9:return t.next=11,u.videoTrack.setPublishingCodecs(e.subscribedCodecs);case 11:d=t.sent,t.prev=12,l=!0,h=$t(d);case 14:return t.next=16,h.next();case 16:if(f=t.sent,r=f.done){t.next=29;break}if(o=f.value,l=!1,!xi(p=o)){t.next=26;break}return B.debug("publish ".concat(p," for ").concat(u.videoTrack.sid)),t.next=26,this.publishAdditionalCodecForTrack(u.videoTrack,p,u.options);case 26:l=!0,t.next=14;break;case 29:t.next=34;break;case 31:t.prev=31,t.t0=t.catch(12),i={error:t.t0};case 34:if(t.prev=34,t.prev=35,l||r||!(a=h.return)){t.next=39;break}return t.next=39,a.call(h);case 39:if(t.prev=39,!i){t.next=42;break}throw i.error;case 42:return t.finish(39);case 43:return t.finish(34);case 44:t.next=49;break;case 46:if(e.subscribedQualities.length<=0){t.next=49;break}return t.next=49,null===(c=u.videoTrack)||void 0===c?void 0:c.setPublishingLayers(e.subscribedQualities);case 49:case"end":return t.stop()}}),t,this,[[12,31,34,44],[35,,39,43]])})))},c.handleLocalTrackUnpublished=function(e){var t=c.tracks.get(e.trackSid);t?c.unpublishTrack(t.track):B.warn("received unpublished event for unknown track",{method:"handleLocalTrackUnpublished",trackSid:e.trackSid})},c.handleTrackEnded=function(e){return Xt(m(c),void 0,void 0,n().mark((function t(){var r;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.source!==Mi.Source.ScreenShare&&e.source!==Mi.Source.ScreenShareAudio){t.next=5;break}B.debug("unpublishing local track due to TrackEnded",{track:e.sid}),this.unpublishTrack(e),t.next=36;break;case 5:if(!e.isUserProvided){t.next=10;break}return t.next=8,e.mute();case 8:t.next=36;break;case 10:if(!(e instanceof To||e instanceof Bo)){t.next=36;break}if(t.prev=11,!na()){t.next=25;break}return t.prev=13,t.next=16,null===navigator||void 0===navigator?void 0:navigator.permissions.query({name:e.source===Mi.Source.Camera?"camera":"microphone"});case 16:if(!(r=t.sent)||"denied"!==r.state){t.next=21;break}throw B.warn("user has revoked access to ".concat(e.source)),r.onchange=function(){"denied"!==r.state&&(e.isMuted||e.restartTrack(),r.onchange=null)},new Error("GetUserMedia Permission denied");case 21:t.next=25;break;case 23:t.prev=23,t.t0=t.catch(13);case 25:if(e.isMuted){t.next=29;break}return B.debug("track ended, attempting to use a different device"),t.next=29,e.restartTrack();case 29:t.next=36;break;case 31:return t.prev=31,t.t1=t.catch(11),B.warn("could not restart track, muting instead"),t.next=36,e.mute();case 36:case"end":return t.stop()}}),t,this,[[11,31],[13,23]])})))},c.audioTracks=new Map,c.videoTracks=new Map,c.tracks=new Map,c.engine=a,c.roomOptions=o,c.setupEngine(a),c.activeDeviceMap=new Map,c}return l(r,Yo),u(r,[{key:"lastCameraError",get:function(){return this.cameraError}},{key:"lastMicrophoneError",get:function(){return this.microphoneError}},{key:"isE2EEEnabled",get:function(){return this.encryptionType!==It.NONE}},{key:"getTrack",value:function(e){var t=v(h(r.prototype),"getTrack",this).call(this,e);if(t)return t}},{key:"getTrackByName",value:function(e){var t=v(h(r.prototype),"getTrackByName",this).call(this,e);if(t)return t}},{key:"setupEngine",value:function(e){var t=this;this.engine=e,this.engine.client.onRemoteMuteChanged=function(e,n){var r=t.tracks.get(e);r&&r.track&&(n?r.mute():r.unmute())},this.engine.client.onSubscribedQualityUpdate=this.handleSubscribedQualityUpdate,this.engine.client.onLocalTrackUnpublished=this.handleLocalTrackUnpublished,this.engine.on(Ri.Connected,this.handleReconnected).on(Ri.SignalRestarted,this.handleReconnected).on(Ri.SignalResumed,this.handleReconnected).on(Ri.Restarting,this.handleReconnecting).on(Ri.Resuming,this.handleReconnecting).on(Ri.Disconnected,this.handleDisconnected)}},{key:"setMetadata",value:function(e){var t;this.engine.client.sendUpdateLocalMetadata(e,null!==(t=this.name)&&void 0!==t?t:"")}},{key:"setName",value:function(e){var t;this.engine.client.sendUpdateLocalMetadata(null!==(t=this.metadata)&&void 0!==t?t:"",e)}},{key:"setCameraEnabled",value:function(e,t,n){return this.setTrackEnabled(Mi.Source.Camera,e,t,n)}},{key:"setMicrophoneEnabled",value:function(e,t,n){return this.setTrackEnabled(Mi.Source.Microphone,e,t,n)}},{key:"setScreenShareEnabled",value:function(e,t,n){return this.setTrackEnabled(Mi.Source.ScreenShare,e,t,n)}},{key:"setPermissions",value:function(e){var t=this.permissions,n=v(h(r.prototype),"setPermissions",this).call(this,e);return n&&t&&this.emit(Ei.ParticipantPermissionsChanged,t),n}},{key:"setE2EEEnabled",value:function(e){return Xt(this,void 0,void 0,n().mark((function t(){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return this.encryptionType=e?It.GCM:It.NONE,t.next=3,this.republishAllTracks(void 0,!1);case 3:case"end":return t.stop()}}),t,this)})))}},{key:"setTrackEnabled",value:function(e,t,r,i){var a,o;return Xt(this,void 0,void 0,n().mark((function s(){var c,u,d,l,h,f,p,m,v;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(B.debug("setTrackEnabled",{source:e,enabled:t}),c=this.getTrack(e),!t){n.next=49;break}if(!c){n.next=8;break}return n.next=6,c.unmute();case 6:n.next=47;break;case 8:if(!this.pendingPublishing.has(e)){n.next=11;break}return B.info("skipping duplicate published source",{source:e}),n.abrupt("return");case 11:this.pendingPublishing.add(e),n.prev=12,n.t0=e,n.next=n.t0===Mi.Source.Camera?16:n.t0===Mi.Source.Microphone?20:n.t0===Mi.Source.ScreenShare?24:28;break;case 16:return n.next=18,this.createTracks({video:null===(a=r)||void 0===a||a});case 18:return u=n.sent,n.abrupt("break",29);case 20:return n.next=22,this.createTracks({audio:null===(o=r)||void 0===o||o});case 22:return u=n.sent,n.abrupt("break",29);case 24:return n.next=26,this.createScreenTracks(Object.assign({},r));case 26:return u=n.sent,n.abrupt("break",29);case 28:throw new li(e);case 29:d=[],l=S(u);try{for(l.s();!(h=l.n()).done;)f=h.value,B.info("publishing track",{localTrack:f}),d.push(this.publishTrack(f,i))}catch(e){l.e(e)}finally{l.f()}return n.next=34,Promise.all(d);case 34:p=n.sent,m=k(p,1),c=m[0],n.next=44;break;case 39:throw n.prev=39,n.t1=n.catch(12),null==u||u.forEach((function(e){e.stop()})),n.t1 instanceof Error&&!(n.t1 instanceof li)&&this.emit(Ei.MediaDevicesError,n.t1),n.t1;case 44:return n.prev=44,this.pendingPublishing.delete(e),n.finish(44);case 47:n.next=60;break;case 49:if(!c||!c.track){n.next=60;break}if(e!==Mi.Source.ScreenShare){n.next=58;break}return n.next=53,this.unpublishTrack(c.track);case 53:c=n.sent,(v=this.getTrack(Mi.Source.ScreenShareAudio))&&v.track&&this.unpublishTrack(v.track),n.next=60;break;case 58:return n.next=60,c.mute();case 60:return n.abrupt("return",c);case 61:case"end":return n.stop()}}),s,this,[[12,39,44,47]])})))}},{key:"enableCameraAndMicrophone",value:function(){return Xt(this,void 0,void 0,n().mark((function e(){var t,r=this;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.pendingPublishing.has(Mi.Source.Camera)&&!this.pendingPublishing.has(Mi.Source.Microphone)){e.next=2;break}return e.abrupt("return");case 2:return this.pendingPublishing.add(Mi.Source.Camera),this.pendingPublishing.add(Mi.Source.Microphone),e.prev=4,e.next=7,this.createTracks({audio:!0,video:!0});case 7:return t=e.sent,e.next=10,Promise.all(t.map((function(e){return r.publishTrack(e)})));case 10:return e.prev=10,this.pendingPublishing.delete(Mi.Source.Camera),this.pendingPublishing.delete(Mi.Source.Microphone),e.finish(10);case 14:case"end":return e.stop()}}),e,this,[[4,,10,14]])})))}},{key:"createTracks",value:function(e){var t,r;return Xt(this,void 0,void 0,n().mark((function i(){var a,o,s;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return a=Ui(e,null===(t=this.roomOptions)||void 0===t?void 0:t.audioCaptureDefaults,null===(r=this.roomOptions)||void 0===r?void 0:r.videoCaptureDefaults),o=Fi(a),n.prev=2,n.next=5,navigator.mediaDevices.getUserMedia(o);case 5:s=n.sent,n.next=12;break;case 8:throw n.prev=8,n.t0=n.catch(2),n.t0 instanceof Error&&(o.audio&&(this.microphoneError=n.t0),o.video&&(this.cameraError=n.t0)),n.t0;case 12:return o.audio&&(this.microphoneError=void 0,this.emit(Ei.AudioStreamAcquired)),o.video&&(this.cameraError=void 0),n.abrupt("return",s.getTracks().map((function(t){var n,r="audio"===t.kind;r?e.audio:e.video;var i=r?o.audio:o.video;"boolean"!=typeof i&&(n=i);var a=Co(t,n);return a.kind===Mi.Kind.Video?a.source=Mi.Source.Camera:a.kind===Mi.Kind.Audio&&(a.source=Mi.Source.Microphone),a.mediaStream=s,a})));case 15:case"end":return n.stop()}}),i,this,[[2,8]])})))}},{key:"createScreenTracks",value:function(e){return Xt(this,void 0,void 0,n().mark((function t(){var r,i,a,o,s,c;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===e&&(e={}),void 0!==navigator.mediaDevices.getDisplayMedia){t.next=3;break}throw new di("getDisplayMedia not supported");case 3:return r=qi(e),t.next=6,navigator.mediaDevices.getDisplayMedia(r);case 6:if(i=t.sent,0!==(a=i.getVideoTracks()).length){t.next=10;break}throw new li("no video track found");case 10:return(o=new Bo(a[0],void 0,!1)).source=Mi.Source.ScreenShare,s=[o],i.getAudioTracks().length>0&&(this.emit(Ei.AudioStreamAcquired),(c=new To(i.getAudioTracks()[0],void 0,!1,this.audioContext)).source=Mi.Source.ScreenShareAudio,s.push(c)),t.abrupt("return",s);case 15:case"end":return t.stop()}}),t,this)})))}},{key:"publishTrack",value:function(e,t){var r,i,a,o;return Xt(this,void 0,void 0,n().mark((function s(){var c,u,d,l,h,f,p,m;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,null===(r=this.reconnectFuture)||void 0===r?void 0:r.promise;case 2:if(!(e instanceof Fa&&this.pendingPublishPromises.has(e))){n.next=5;break}return n.next=5,this.pendingPublishPromises.get(e);case 5:if(!(e instanceof MediaStreamTrack)){n.next=9;break}c=e.getConstraints(),n.next=18;break;case 9:c=e.constraints,u=void 0,n.t0=e.source,n.next=n.t0===Mi.Source.Microphone?14:n.t0===Mi.Source.Camera?16:17;break;case 14:return u="audioinput",n.abrupt("break",17);case 16:u="videoinput";case 17:u&&this.activeDeviceMap.has(u)&&(c=Object.assign(Object.assign({},c),{deviceId:this.activeDeviceMap.get(u)}));case 18:if(!(e instanceof MediaStreamTrack)){n.next=27;break}n.t1=e.kind,n.next="audio"===n.t1?22:"video"===n.t1?24:26;break;case 22:return e=new To(e,c,!0,this.audioContext),n.abrupt("break",27);case 24:return e=new Bo(e,c,!0),n.abrupt("break",27);case 26:throw new li("unsupported MediaStreamTrack kind ".concat(e.kind));case 27:if(e instanceof To&&e.setAudioContext(this.audioContext),this.tracks.forEach((function(t){t.track&&t.track===e&&(d=t)})),!d){n.next=32;break}return B.warn("track has already been published, skipping"),n.abrupt("return",d);case 32:return l="channelCount"in e.mediaStreamTrack.getSettings()&&2===e.mediaStreamTrack.getSettings().channelCount||2===e.mediaStreamTrack.getConstraints().channelCount,(h=null!==(i=null==t?void 0:t.forceStereo)&&void 0!==i?i:l)&&(t||(t={}),void 0===t.dtx&&B.info("Opus DTX will be disabled for stereo tracks by default. Enable them explicitly to make it work."),void 0===t.red&&B.info("Opus RED will be disabled for stereo tracks by default. Enable them explicitly to make it work."),null!==(a=t.dtx)&&void 0!==a||(t.dtx=!1),null!==(o=t.red)&&void 0!==o||(t.red=!1)),f=Object.assign(Object.assign({},this.roomOptions.publishDefaults),t),ea()&&this.roomOptions.e2ee&&(B.info("End-to-end encryption is set up, simulcast publishing will be disabled on Safari"),f.simulcast=!1),f.source&&(e.source=f.source),p=this.publish(e,f,h),this.pendingPublishPromises.set(e,p),n.prev=40,n.next=43,p;case 43:return m=n.sent,n.abrupt("return",m);case 47:throw n.prev=47,n.t2=n.catch(40),n.t2;case 50:return n.prev=50,this.pendingPublishPromises.delete(e),n.finish(50);case 53:case"end":return n.stop()}}),s,this,[[40,47,50,53]])})))}},{key:"publish",value:function(e,t,r){var i,a,o,s,c,u,d,l,h,f,p,m,v;return Xt(this,void 0,void 0,n().mark((function k(){var g,y,b,w,T,C,x,P,E,R,I,D,N;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(Array.from(this.tracks.values()).find((function(t){return e instanceof Fa&&t.source===e.source}))&&e.source!==Mi.Source.Unknown&&B.info("publishing a second track with the same source: ".concat(e.source)),t.stopMicTrackOnMute&&e instanceof To&&(e.stopOnMute=!0),e.source===Mi.Source.ScreenShare&&$i()&&(t.simulcast=!1),"av1"!==t.videoCodec||zi()||(t.videoCodec=void 0),"vp9"!==t.videoCodec||Qi()||(t.videoCodec=void 0),void 0===t.videoCodec&&(t.videoCodec=co),g=t.videoCodec,e.on(Ii.Muted,this.onTrackMuted),e.on(Ii.Unmuted,this.onTrackUnmuted),e.on(Ii.Ended,this.handleTrackEnded),e.on(Ii.UpstreamPaused,this.onTrackUpstreamPaused),e.on(Ii.UpstreamResumed,this.onTrackUpstreamResumed),y=new Er({cid:e.mediaStreamTrack.id,name:t.name,type:Mi.kindToProto(e.kind),muted:e.isMuted,source:Mi.sourceToProto(e.source),disableDtx:!(null===(i=t.dtx)||void 0===i||i),encryption:this.encryptionType,stereo:r,disableRed:this.isE2EEEnabled||!(null===(a=t.red)||void 0===a||a),stream:null==t?void 0:t.stream}),e.kind!==Mi.Kind.Video){n.next=34;break}return w={width:0,height:0},n.prev=16,n.next=19,e.waitForDimensions();case 19:w=n.sent,n.next=27;break;case 22:n.prev=22,n.t0=n.catch(16),T=null!==(s=null===(o=this.roomOptions.videoCaptureDefaults)||void 0===o?void 0:o.resolution)&&void 0!==s?s:Di.h720.resolution,w={width:T.width,height:T.height},B.error("could not determine track dimensions, using defaults",w);case 27:y.width=w.width,y.height=w.height,e instanceof Bo&&(Yi(g)&&(e.source===Mi.Source.ScreenShare&&"vp9"===g&&(t.scalabilityMode="L1T3"),t.scalabilityMode=null!==(c=t.scalabilityMode)&&void 0!==c?c:"L3T3_KEY"),y.simulcastCodecs=[new Pr({codec:g,cid:e.mediaStreamTrack.id})],!0===t.backupCodec&&(t.backupCodec={codec:co}),t.backupCodec&&g!==t.backupCodec.codec&&(this.roomOptions.dynacast||(this.roomOptions.dynacast=!0),y.simulcastCodecs.push(new Pr({codec:t.backupCodec.codec,cid:""})))),b=Oo(e.source===Mi.Source.ScreenShare,y.width,y.height,t),y.layers=jo(y.width,y.height,b,Yi(t.videoCodec)),n.next=35;break;case 34:e.kind===Mi.Kind.Audio&&(b=[{maxBitrate:null!==(d=null===(u=t.audioPreset)||void 0===u?void 0:u.maxBitrate)&&void 0!==d?d:t.audioBitrate,priority:null!==(h=null===(l=t.audioPreset)||void 0===l?void 0:l.priority)&&void 0!==h?h:"high",networkPriority:null!==(p=null===(f=t.audioPreset)||void 0===f?void 0:f.priority)&&void 0!==p?p:"high"}]);case 35:if(this.engine&&!this.engine.isClosed){n.next=37;break}throw new fi("cannot publish track when not connected");case 37:return n.next=39,this.engine.addTrack(y);case 39:if((C=n.sent).codecs.forEach((function(e){void 0===x&&(x=e.mimeType)})),x&&e.kind===Mi.Kind.Video&&(P=Vi(x))!==g&&(B.debug("falling back to server selected codec",{codec:P}),t.videoCodec=P,b=Oo(e.source===Mi.Source.ScreenShare,y.width,y.height,t)),(E=new Qo(e.kind,C,e)).options=t,e.sid=C.sid,this.engine.publisher){n.next=47;break}throw new fi("publisher is closed");case 47:return B.debug("publishing ".concat(e.kind," with encodings"),{encodings:b,trackInfo:C}),n.next=50,this.engine.createSender(e,t,b);case 50:if(e.sender=n.sent,!b){n.next=76;break}if(!$i()||e.kind!==Mi.Kind.Audio){n.next=75;break}R=void 0,I=S(this.engine.publisher.getTransceivers()),n.prev=55,I.s();case 57:if((D=I.n()).done){n.next=64;break}if((N=D.value).sender!==e.sender){n.next=62;break}return R=N,n.abrupt("break",64);case 62:n.next=57;break;case 64:n.next=69;break;case 66:n.prev=66,n.t1=n.catch(55),I.e(n.t1);case 69:return n.prev=69,I.f(),n.finish(69);case 72:R&&this.engine.publisher.setTrackCodecBitrate({transceiver:R,codec:"opus",maxbr:(null===(m=b[0])||void 0===m?void 0:m.maxBitrate)?b[0].maxBitrate/1e3:0}),n.next=76;break;case 75:e.codec&&Yi(e.codec)&&(null===(v=b[0])||void 0===v?void 0:v.maxBitrate)&&this.engine.publisher.setTrackCodecBitrate({cid:y.cid,codec:e.codec,maxbr:b[0].maxBitrate/1e3});case 76:return n.next=78,this.engine.negotiate();case 78:return e instanceof Bo?e.startMonitor(this.engine.client):e instanceof To&&e.startMonitor(),this.addTrackPublication(E),this.emit(Ei.LocalTrackPublished,E),n.abrupt("return",E);case 82:case"end":return n.stop()}}),k,this,[[16,22],[55,66,69,72]])})))}},{key:"isLocal",get:function(){return!0}},{key:"publishAdditionalCodecForTrack",value:function(e,t,r){var i;return Xt(this,void 0,void 0,n().mark((function a(){var o,s,c,u,d,l;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(this.tracks.forEach((function(t){t.track&&t.track===e&&(o=t)})),o){n.next=3;break}throw new li("track is not published");case 3:if(e instanceof Bo){n.next=5;break}throw new li("track is not a video track");case 5:if(s=Object.assign(Object.assign({},null===(i=this.roomOptions)||void 0===i?void 0:i.publishDefaults),r),c=_o(e,t,s)){n.next=10;break}return B.info("backup codec has been disabled, ignoring request to add additional codec for track"),n.abrupt("return");case 10:if(u=e.addSimulcastTrack(t,c),(d=new Er({cid:u.mediaStreamTrack.id,type:Mi.kindToProto(e.kind),muted:e.isMuted,source:Mi.sourceToProto(e.source),sid:e.sid,simulcastCodecs:[{codec:s.videoCodec,cid:u.mediaStreamTrack.id}]})).layers=jo(d.width,d.height,c),this.engine&&!this.engine.isClosed){n.next=15;break}throw new fi("cannot publish track when not connected");case 15:return n.next=17,this.engine.addTrack(d);case 17:return l=n.sent,n.next=20,this.engine.createSimulcastSender(e,u,s,c);case 20:return n.next=22,this.engine.negotiate();case 22:B.debug("published ".concat(t," for track ").concat(e.sid),{encodings:c,trackInfo:l});case 23:case"end":return n.stop()}}),a,this)})))}},{key:"unpublishTrack",value:function(e,t){var r,i;return Xt(this,void 0,void 0,n().mark((function a(){var o,s,c,u,d,l,h,f,p,m;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(o=this.getPublicationForTrack(e),B.debug("unpublishing track",{track:e,method:"unpublishTrack"}),o&&o.track){n.next=5;break}return B.warn("track was not unpublished because no publication was found",{track:e,method:"unpublishTrack"}),n.abrupt("return",void 0);case 5:if((e=o.track).off(Ii.Muted,this.onTrackMuted),e.off(Ii.Unmuted,this.onTrackUnmuted),e.off(Ii.Ended,this.handleTrackEnded),e.off(Ii.UpstreamPaused,this.onTrackUpstreamPaused),e.off(Ii.UpstreamResumed,this.onTrackUpstreamResumed),void 0===t&&(t=null===(i=null===(r=this.roomOptions)||void 0===r?void 0:r.stopLocalTrackOnUnpublish)||void 0===i||i),t&&e.stop(),s=!1,c=e.sender,e.sender=void 0,this.engine.publisher&&"closed"!==this.engine.publisher.getConnectionState()&&c)try{u=S(this.engine.publisher.getTransceivers());try{for(u.s();!(d=u.n()).done;)(l=d.value).sender===c&&(l.direction="inactive",s=!0)}catch(e){u.e(e)}finally{u.f()}if(this.engine.removeTrack(c)&&(s=!0),e instanceof Bo){h=S(e.simulcastCodecs);try{for(h.s();!(f=h.n()).done;)p=k(f.value,2),(m=p[1]).sender&&(this.engine.removeTrack(m.sender)&&(s=!0),m.sender=void 0)}catch(e){h.e(e)}finally{h.f()}e.simulcastCodecs.clear()}}catch(e){B.warn("failed to unpublish track",{error:e,method:"unpublishTrack"})}this.tracks.delete(o.trackSid),n.t0=o.kind,n.next=n.t0===Mi.Kind.Audio?21:n.t0===Mi.Kind.Video?23:25;break;case 21:return this.audioTracks.delete(o.trackSid),n.abrupt("break",25);case 23:return this.videoTracks.delete(o.trackSid),n.abrupt("break",25);case 25:if(this.emit(Ei.LocalTrackUnpublished,o),o.setTrack(void 0),!s){n.next=30;break}return n.next=30,this.engine.negotiate();case 30:return n.abrupt("return",o);case 31:case"end":return n.stop()}}),a,this)})))}},{key:"unpublishTracks",value:function(e){return Xt(this,void 0,void 0,n().mark((function t(){var r,i=this;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,Promise.all(e.map((function(e){return i.unpublishTrack(e)})));case 2:return r=t.sent,t.abrupt("return",r.filter((function(e){return e instanceof Qo})));case 4:case"end":return t.stop()}}),t)})))}},{key:"republishAllTracks",value:function(e){var t=1>=arguments.length||void 0===arguments[1]||arguments[1];return Xt(this,void 0,void 0,n().mark((function r(){var i,a=this;return n().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return i=[],this.tracks.forEach((function(t){t.track&&(e&&(t.options=Object.assign(Object.assign({},t.options),e)),i.push(t))})),r.next=4,Promise.all(i.map((function(e){return Xt(a,void 0,void 0,n().mark((function r(){var i;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return i=e.track,n.next=3,this.unpublishTrack(i,!1);case 3:if(!t||i.isMuted||!(i instanceof To||i instanceof Bo)||i.isUserProvided){n.next=7;break}return B.debug("restarting existing track",{track:e.trackSid}),n.next=7,i.restartTrack();case 7:return n.next=9,this.publishTrack(i,e.options);case 9:case"end":return n.stop()}}),r,this)})))})));case 4:case"end":return r.stop()}}),r,this)})))}},{key:"publishData",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return Xt(this,void 0,void 0,n().mark((function i(){var a,o,s,c;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return a=Array.isArray(r)?r:null==r?void 0:r.destination,o=[],s=Array.isArray(r)?void 0:r.topic,void 0!==a&&a.forEach((function(e){e instanceof $o?o.push(e.sid):o.push(e)})),c=new Lt({kind:t,value:{case:"user",value:new Bt({participantSid:this.sid,payload:e,destinationSids:o,topic:s})}}),n.next=7,this.engine.sendDataPacket(c,t);case 7:case"end":return n.stop()}}),i,this)})))}},{key:"setTrackSubscriptionPermissions",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];this.participantTrackPermissions=t,this.allParticipantsAllowedToSubscribe=e,this.engine.client.isConnected&&this.updateTrackSubscriptionPermissions()}},{key:"updateInfo",value:function(e){var t=this;return e.sid===this.sid&&(!!v(h(r.prototype),"updateInfo",this).call(this,e)&&(e.tracks.forEach((function(e){var n,r,i=t.tracks.get(e.sid);if(i){var a=i.isMuted||null!==(r=null===(n=i.track)||void 0===n?void 0:n.isUpstreamPaused)&&void 0!==r&&r;a!==e.muted&&(B.debug("updating server mute state after reconcile",{sid:e.sid,muted:a}),t.engine.client.sendMuteTrack(e.sid,a))}})),!0))}},{key:"getPublicationForTrack",value:function(e){var t;return this.tracks.forEach((function(n){var r=n.track;r&&(e instanceof MediaStreamTrack?(r instanceof To||r instanceof Bo)&&r.mediaStreamTrack===e&&(t=n):e===r&&(t=n))})),t}},{key:"publishedTracksInfo",value:function(){var e=[];return this.tracks.forEach((function(t){void 0!==t.track&&e.push(new Or({cid:t.track.mediaStreamID,track:t.trackInfo}))})),e}},{key:"dataChannelsInfo",value:function(){var e=[],t=function(t,n){void 0!==(null==t?void 0:t.id)&&null!==t.id&&e.push(new ti({label:t.label,id:t.id,target:n}))};return t(this.engine.dataChannelForKind(Mt.LOSSY),br.PUBLISHER),t(this.engine.dataChannelForKind(Mt.RELIABLE),br.PUBLISHER),t(this.engine.dataChannelForKind(Mt.LOSSY,!0),br.SUBSCRIBER),t(this.engine.dataChannelForKind(Mt.RELIABLE,!0),br.SUBSCRIBER),e}}]),r}();!function(e){e.Disconnected="disconnected",e.Connecting="connecting",e.Connected="connected",e.Reconnecting="reconnecting"}(Xo||(Xo={}));var ts,ns=function(t){function r(t){var a,o,c;return s(this,r),a=e(this,r),o=m(a),a.state=Xo.Disconnected,a.activeSpeakers=[],a.isE2EEEnabled=!1,a.audioEnabled=!0,a.connect=function(e,t,r){return Xt(m(a),void 0,void 0,n().mark((function i(){var a,o,s,c,u=this;return n().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:return i.next=2,this.disconnectLock.lock();case 2:if(o=i.sent,this.state!==Xo.Connected){i.next=7;break}return B.info("already connected to room ".concat(this.name)),o(),i.abrupt("return",Promise.resolve());case 7:if(!this.connectFuture){i.next=10;break}return o(),i.abrupt("return",this.connectFuture.promise);case 10:return this.setAndEmitConnectionState(Xo.Connecting),(null===(a=this.regionUrlProvider)||void 0===a?void 0:a.getServerUrl().toString())!==e&&(this.regionUrl=void 0,this.regionUrlProvider=void 0),ia(new URL(e))&&(void 0===this.regionUrlProvider?this.regionUrlProvider=new bo(e,t):this.regionUrlProvider.updateToken(t),this.regionUrlProvider.fetchRegionSettings().catch((function(e){B.warn("could not fetch region settings",{error:e})}))),s=function i(a,s,c){return Xt(u,void 0,void 0,n().mark((function u(){var d,l,h;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return this.abortController&&this.abortController.abort(),l=new AbortController,this.abortController=l,null==o||o(),n.prev=4,n.next=7,this.attemptConnection(null!=c?c:e,t,r,l);case 7:this.abortController=void 0,a(),n.next=39;break;case 11:if(n.prev=11,n.t0=n.catch(4),!(this.regionUrlProvider&&n.t0 instanceof ui&&3!==n.t0.reason&&0!==n.t0.reason)){n.next=37;break}return h=null,n.prev=15,n.next=18,this.regionUrlProvider.getNextBestRegionUrl(null===(d=this.abortController)||void 0===d?void 0:d.signal);case 18:h=n.sent,n.next=27;break;case 21:if(n.prev=21,n.t1=n.catch(15),!(n.t1 instanceof ui)||401!==n.t1.status&&3!==n.t1.reason){n.next=27;break}return this.handleDisconnect(this.options.stopLocalTrackOnUnpublish),s(n.t1),n.abrupt("return");case 27:if(!h){n.next=33;break}return B.info("Initial connection failed with ConnectionError: ".concat(n.t0.message,". Retrying with another region: ").concat(h)),n.next=31,i(a,s,h);case 31:n.next=35;break;case 33:this.handleDisconnect(this.options.stopLocalTrackOnUnpublish),s(n.t0);case 35:n.next=39;break;case 37:this.handleDisconnect(this.options.stopLocalTrackOnUnpublish),s(n.t0);case 39:case"end":return n.stop()}}),u,this,[[4,11],[15,21]])})))},c=this.regionUrl,this.regionUrl=void 0,this.connectFuture=new ya((function(e,t){s(e,t,c)}),(function(){u.clearConnectionFutures()})),i.abrupt("return",this.connectFuture.promise);case 18:case"end":return i.stop()}}),i,this)})))},a.connectSignal=function(e,t,r,o,s,c){return Xt(m(a),void 0,void 0,n().mark((function a(){var u,d;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,r.join(e,t,{autoSubscribe:o.autoSubscribe,publishOnly:o.publishOnly,adaptiveStream:"object"===i(s.adaptiveStream)||s.adaptiveStream,maxRetries:o.maxRetries,e2eeEnabled:!!this.e2eeManager,websocketTimeout:o.websocketTimeout},c.signal);case 2:if(u=n.sent,(d=u.serverInfo)||(d={version:u.serverVersion,region:u.serverRegion}),B.debug("connected to Livekit Server ".concat(Object.entries(d).map((function(e){var t=k(e,2),n=t[0],r=t[1];return"".concat(n,": ").concat(r)})).join(", "))),u.serverVersion){n.next=8;break}throw new hi("unknown server version");case 8:return"0.15.1"===u.serverVersion&&this.options.dynacast&&(B.debug("disabling dynacast due to server version"),s.dynacast=!1),n.abrupt("return",u);case 10:case"end":return n.stop()}}),a,this)})))},a.applyJoinResponse=function(e){var t=e.participant;a.localParticipant.sid=t.sid,a.localParticipant.identity=t.identity,a.handleParticipantUpdates([t].concat(g(e.otherParticipants))),e.room&&a.handleRoomUpdate(e.room),a.options.e2ee&&a.e2eeManager&&a.e2eeManager.setSifTrailer(e.sifTrailer)},a.attemptConnection=function(e,t,r,i){return Xt(m(a),void 0,void 0,n().mark((function a(){var o,s,c,u;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return this.state===Xo.Reconnecting?(B.info("Reconnection attempt replaced by new connection attempt"),this.recreateEngine()):this.maybeCreateEngine(),(null===(o=this.regionUrlProvider)||void 0===o?void 0:o.isCloud())&&this.engine.setRegionUrlProvider(this.regionUrlProvider),this.acquireAudioContext(),this.connOptions=Object.assign(Object.assign({},po),r),this.connOptions.rtcConfig&&(this.engine.rtcConfig=this.connOptions.rtcConfig),this.connOptions.peerConnectionTimeout&&(this.engine.peerConnectionTimeout=this.connOptions.peerConnectionTimeout),n.prev=6,n.next=9,this.connectSignal(e,t,this.engine,this.connOptions,this.options,i);case 9:c=n.sent,this.applyJoinResponse(c),this.setupLocalParticipantEvents(),this.emit(Pi.SignalConnected),n.next=25;break;case 15:return n.prev=15,n.t0=n.catch(6),n.next=19,this.engine.close();case 19:throw this.recreateEngine(),u=new ui("could not establish signal connection"),n.t0 instanceof Error&&(u.message="".concat(u.message,": ").concat(n.t0.message)),n.t0 instanceof ui&&(u.reason=n.t0.reason,u.status=n.t0.status),B.debug("error trying to establish signal connection",{error:n.t0}),u;case 25:if(!i.signal.aborted){n.next=30;break}return n.next=28,this.engine.close();case 28:throw this.recreateEngine(),new ui("Connection attempt aborted");case 30:return n.prev=30,n.next=33,this.engine.waitForPCInitialConnection(this.connOptions.peerConnectionTimeout,i);case 33:n.next=41;break;case 35:return n.prev=35,n.t1=n.catch(30),n.next=39,this.engine.close();case 39:throw this.recreateEngine(),n.t1;case 41:na()&&this.options.disconnectOnPageLeave&&(window.addEventListener("pagehide",this.onPageLeave),window.addEventListener("beforeunload",this.onPageLeave)),na()&&(document.addEventListener("freeze",this.onPageLeave),null===(s=navigator.mediaDevices)||void 0===s||s.addEventListener("devicechange",this.handleDeviceChange)),this.setAndEmitConnectionState(Xo.Connected),this.emit(Pi.Connected),this.registerConnectionReconcile();case 46:case"end":return n.stop()}}),a,this,[[6,15],[30,35]])})))},a.disconnect=function(){var e=0>=arguments.length||void 0===arguments[0]||arguments[0];return Xt(o,void 0,void 0,n().mark((function t(){var r,i,a,o,s;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.disconnectLock.lock();case 2:if(s=t.sent,t.prev=3,this.state!==Xo.Disconnected){t.next=7;break}return B.debug("already disconnected"),t.abrupt("return");case 7:if(B.info("disconnect from room",{identity:this.localParticipant.identity}),this.state!==Xo.Connecting&&this.state!==Xo.Reconnecting||(B.warn("abort connection attempt"),null===(r=this.abortController)||void 0===r||r.abort(),null===(a=null===(i=this.connectFuture)||void 0===i?void 0:i.reject)||void 0===a||a.call(i,new ui("Client initiated disconnect")),this.connectFuture=void 0),!(null===(o=this.engine)||void 0===o?void 0:o.client.isConnected)){t.next=12;break}return t.next=12,this.engine.client.sendLeave();case 12:if(!this.engine){t.next=15;break}return t.next=15,this.engine.close();case 15:this.handleDisconnect(e,St.CLIENT_INITIATED),this.engine=void 0;case 17:return t.prev=17,s(),t.finish(17);case 20:case"end":return t.stop()}}),t,this,[[3,,17,20]])})))},a.onPageLeave=function(){return Xt(m(a),void 0,void 0,n().mark((function e(){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.disconnect();case 2:case"end":return e.stop()}}),e,this)})))},a.startAudio=function(){return Xt(m(a),void 0,void 0,n().mark((function e(){var t,r,i,a,o,s;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=[],(r=gi())&&"iOS"===r.os&&(i="livekit-dummy-audio-el",(a=document.getElementById(i))||((a=document.createElement("audio")).id=i,a.autoplay=!0,a.hidden=!0,(o=ka()).enabled=!0,s=new MediaStream([o]),a.srcObject=s,document.addEventListener("visibilitychange",(function(){a&&(a.srcObject=document.hidden?null:s)})),document.body.append(a),this.once(Pi.Disconnected,(function(){null==a||a.remove()}))),t.push(a)),this.participants.forEach((function(e){e.audioTracks.forEach((function(e){e.track&&e.track.attachedElements.forEach((function(e){t.push(e)}))}))})),e.prev=4,e.next=7,Promise.all([this.acquireAudioContext()].concat(g(t.map((function(e){return e.muted=!1,e.play()})))));case 7:this.handleAudioPlaybackStarted(),e.next=14;break;case 10:throw e.prev=10,e.t0=e.catch(4),this.handleAudioPlaybackFailed(e.t0),e.t0;case 14:case"end":return e.stop()}}),e,this,[[4,10]])})))},a.handleRestarting=function(){a.clearConnectionReconcile();var e,t=S(a.participants.values());try{for(t.s();!(e=t.n()).done;){var n=e.value;a.handleParticipantDisconnected(n.sid,n)}}catch(e){t.e(e)}finally{t.f()}a.setAndEmitConnectionState(Xo.Reconnecting)&&a.emit(Pi.Reconnecting)},a.handleSignalRestarted=function(e){return Xt(m(a),void 0,void 0,n().mark((function t(){var r,i=this;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return B.debug("signal reconnected to server",{region:e.serverRegion}),this.cachedParticipantSids=[],this.applyJoinResponse(e),t.prev=3,r=[],this.localParticipant.tracks.forEach((function(e){e.track&&r.push(e)})),t.next=8,Promise.all(r.map((function(e){return Xt(i,void 0,void 0,n().mark((function t(){var r;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e.track,this.localParticipant.unpublishTrack(r,!1),r.isMuted){t.next=10;break}if(!(r instanceof To||r instanceof Bo)||r.source===Mi.Source.ScreenShare||r.source===Mi.Source.ScreenShareAudio||r.isUserProvided){t.next=7;break}return B.debug("restarting existing track",{track:e.trackSid}),t.next=7,r.restartTrack();case 7:return B.debug("publishing new track",{track:e.trackSid}),t.next=10,this.localParticipant.publishTrack(r,e.options);case 10:case"end":return t.stop()}}),t,this)})))})));case 8:t.next=13;break;case 10:t.prev=10,t.t0=t.catch(3),B.error("error trying to re-publish tracks after reconnection",{error:t.t0});case 13:return t.prev=13,t.next=16,this.engine.waitForRestarted();case 16:B.debug("fully reconnected to server",{region:e.serverRegion}),t.next=22;break;case 19:return t.prev=19,t.t1=t.catch(13),t.abrupt("return");case 22:this.setAndEmitConnectionState(Xo.Connected),this.emit(Pi.Reconnected),this.registerConnectionReconcile(),this.participants.forEach((function(e){i.emit(Pi.ParticipantConnected,e)}));case 26:case"end":return t.stop()}}),t,this,[[3,10],[13,19]])})))},a.handleParticipantUpdates=function(e){e.forEach((function(e){if(e.identity!==a.localParticipant.identity){var t=a.identityToSid.get(e.identity);t&&t!==e.sid&&a.handleParticipantDisconnected(t,a.participants.get(t));var n=a.participants.get(e.sid),r=!n;e.state===Rt.DISCONNECTED?a.handleParticipantDisconnected(e.sid,n):(n=a.getOrCreateParticipant(e.sid,e),r||n.updateInfo(e))}else a.localParticipant.updateInfo(e)}))},a.handleActiveSpeakersUpdate=function(e){var t=[],n={};e.forEach((function(e){if(n[e.sid]=!0,e.sid===a.localParticipant.sid)a.localParticipant.audioLevel=e.level,a.localParticipant.setIsSpeaking(!0),t.push(a.localParticipant);else{var r=a.participants.get(e.sid);r&&(r.audioLevel=e.level,r.setIsSpeaking(!0),t.push(r))}})),n[a.localParticipant.sid]||(a.localParticipant.audioLevel=0,a.localParticipant.setIsSpeaking(!1)),a.participants.forEach((function(e){n[e.sid]||(e.audioLevel=0,e.setIsSpeaking(!1))})),a.activeSpeakers=t,a.emitWhenConnected(Pi.ActiveSpeakersChanged,t)},a.handleSpeakersChanged=function(e){var t=new Map;a.activeSpeakers.forEach((function(e){t.set(e.sid,e)})),e.forEach((function(e){var n=a.participants.get(e.sid);e.sid===a.localParticipant.sid&&(n=a.localParticipant),n&&(n.audioLevel=e.level,n.setIsSpeaking(e.active),e.active?t.set(e.sid,n):t.delete(e.sid))}));var n=Array.from(t.values());n.sort((function(e,t){return t.audioLevel-e.audioLevel})),a.activeSpeakers=n,a.emitWhenConnected(Pi.ActiveSpeakersChanged,n)},a.handleStreamStateUpdate=function(e){e.streamStates.forEach((function(e){var t=a.participants.get(e.participantSid);if(t){var n=t.getTrackPublication(e.trackSid);n&&n.track&&(n.track.streamState=Mi.streamStateFromProto(e.state),t.emit(Ei.TrackStreamStateChanged,n,n.track.streamState),a.emitWhenConnected(Pi.TrackStreamStateChanged,n,n.track.streamState,t))}}))},a.handleSubscriptionPermissionUpdate=function(e){var t=a.participants.get(e.participantSid);if(t){var n=t.getTrackPublication(e.trackSid);n&&n.setAllowed(e.allowed)}},a.handleSubscriptionError=function(e){var t=Array.from(a.participants.values()).find((function(t){return t.tracks.has(e.trackSid)}));if(t){var n=t.getTrackPublication(e.trackSid);n&&n.setSubscriptionError(e.err)}},a.handleDataPacket=function(e,t){var n=a.participants.get(e.participantSid);a.emit(Pi.DataReceived,e.payload,n,t,e.topic),null==n||n.emit(Ei.DataReceived,e.payload,t)},a.handleAudioPlaybackStarted=function(){a.canPlaybackAudio||(a.audioEnabled=!0,a.emit(Pi.AudioPlaybackStatusChanged,!0))},a.handleAudioPlaybackFailed=function(e){B.warn("could not playback audio",e),a.canPlaybackAudio&&(a.audioEnabled=!1,a.emit(Pi.AudioPlaybackStatusChanged,!1))},a.handleDeviceChange=function(){return Xt(m(a),void 0,void 0,n().mark((function e(){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:this.emit(Pi.MediaDevicesChanged);case 1:case"end":return e.stop()}}),e,this)})))},a.handleRoomUpdate=function(e){var t=a.roomInfo;a.roomInfo=e,t&&t.metadata!==e.metadata&&a.emitWhenConnected(Pi.RoomMetadataChanged,e.metadata),(null==t?void 0:t.activeRecording)!==e.activeRecording&&a.emitWhenConnected(Pi.RecordingStatusChanged,e.activeRecording)},a.handleConnectionQualityUpdate=function(e){e.updates.forEach((function(e){if(e.participantSid!==a.localParticipant.sid){var t=a.participants.get(e.participantSid);t&&t.setConnectionQuality(e.quality)}else a.localParticipant.setConnectionQuality(e.quality)}))},a.onLocalParticipantMetadataChanged=function(e){a.emit(Pi.ParticipantMetadataChanged,e,a.localParticipant)},a.onLocalParticipantNameChanged=function(e){a.emit(Pi.ParticipantNameChanged,e,a.localParticipant)},a.onLocalTrackMuted=function(e){a.emit(Pi.TrackMuted,e,a.localParticipant)},a.onLocalTrackUnmuted=function(e){a.emit(Pi.TrackUnmuted,e,a.localParticipant)},a.onLocalTrackPublished=function(e){return Xt(m(a),void 0,void 0,n().mark((function t(){var r,i,a;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this.emit(Pi.LocalTrackPublished,e,this.localParticipant),!(e.track instanceof To)){t.next=6;break}return t.next=4,e.track.checkForSilence();case 4:t.sent&&this.emit(Pi.LocalAudioSilenceDetected,e);case 6:return t.next=8,null===(r=e.track)||void 0===r?void 0:r.getDeviceId();case 8:i=t.sent,n=e.source,(a=n===Mi.Source.Microphone?"audioinput":n===Mi.Source.Camera?"videoinput":void 0)&&i&&i!==this.localParticipant.activeDeviceMap.get(a)&&(this.localParticipant.activeDeviceMap.set(a,i),this.emit(Pi.ActiveDeviceChanged,a,i));case 11:case"end":return t.stop()}var n}),t,this)})))},a.onLocalTrackUnpublished=function(e){a.emit(Pi.LocalTrackUnpublished,e,a.localParticipant)},a.onLocalConnectionQualityChanged=function(e){a.emit(Pi.ConnectionQualityChanged,e,a.localParticipant)},a.onMediaDevicesError=function(e){a.emit(Pi.MediaDevicesError,e)},a.onLocalParticipantPermissionsChanged=function(e){a.emit(Pi.ParticipantPermissionsChanged,e,a.localParticipant)},a.setMaxListeners(100),a.participants=new Map,a.cachedParticipantSids=[],a.identityToSid=new Map,a.options=Object.assign(Object.assign({},fo),t),a.options.audioCaptureDefaults=Object.assign(Object.assign({},lo),null==t?void 0:t.audioCaptureDefaults),a.options.videoCaptureDefaults=Object.assign(Object.assign({},ho),null==t?void 0:t.videoCaptureDefaults),a.options.publishDefaults=Object.assign(Object.assign({},uo),null==t?void 0:t.publishDefaults),a.maybeCreateEngine(),a.disconnectLock=new ba,a.localParticipant=new es("","",a.engine,a.options),a.options.videoCaptureDefaults.deviceId&&a.localParticipant.activeDeviceMap.set("videoinput",Sa(a.options.videoCaptureDefaults.deviceId)),a.options.audioCaptureDefaults.deviceId&&a.localParticipant.activeDeviceMap.set("audioinput",Sa(a.options.audioCaptureDefaults.deviceId)),(null===(c=a.options.audioOutput)||void 0===c?void 0:c.deviceId)&&a.switchActiveDevice("audiooutput",Sa(a.options.audioOutput.deviceId)),a.options.e2ee&&a.setupE2EE(),a}return l(r,kn.EventEmitter),u(r,[{key:"setE2EEEnabled",value:function(e){return Xt(this,void 0,void 0,n().mark((function t(){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!this.e2eeManager){t.next=6;break}return t.next=3,Promise.all([this.localParticipant.setE2EEEnabled(e)]);case 3:""!==this.localParticipant.identity&&this.e2eeManager.setParticipantCryptorEnabled(e,this.localParticipant.identity),t.next=7;break;case 6:throw Error("e2ee not configured, please set e2ee settings within the room options");case 7:case"end":return t.stop()}}),t,this)})))}},{key:"setupE2EE",value:function(){var e,t=this;this.options.e2ee&&(this.e2eeManager=new Ja(this.options.e2ee),this.e2eeManager.on(Na.ParticipantEncryptionStatusChanged,(function(e,n){n instanceof es&&(t.isE2EEEnabled=e),t.emit(Pi.ParticipantEncryptionStatusChanged,e,n)})),this.e2eeManager.on(Na.EncryptionError,(function(e){return t.emit(Pi.EncryptionError,e)})),null===(e=this.e2eeManager)||void 0===e||e.setup(this))}},{key:"isRecording",get:function(){var e,t;return null!==(t=null===(e=this.roomInfo)||void 0===e?void 0:e.activeRecording)&&void 0!==t&&t}},{key:"sid",get:function(){var e,t;return null!==(t=null===(e=this.roomInfo)||void 0===e?void 0:e.sid)&&void 0!==t?t:""}},{key:"name",get:function(){var e,t;return null!==(t=null===(e=this.roomInfo)||void 0===e?void 0:e.name)&&void 0!==t?t:""}},{key:"metadata",get:function(){var e;return null===(e=this.roomInfo)||void 0===e?void 0:e.metadata}},{key:"numParticipants",get:function(){var e,t;return null!==(t=null===(e=this.roomInfo)||void 0===e?void 0:e.numParticipants)&&void 0!==t?t:0}},{key:"numPublishers",get:function(){var e,t;return null!==(t=null===(e=this.roomInfo)||void 0===e?void 0:e.numPublishers)&&void 0!==t?t:0}},{key:"maybeCreateEngine",value:function(){var e=this;this.engine&&!this.engine.isClosed||(this.engine=new go(this.options),this.engine.on(Ri.ParticipantUpdate,this.handleParticipantUpdates).on(Ri.RoomUpdate,this.handleRoomUpdate).on(Ri.SpeakersChanged,this.handleSpeakersChanged).on(Ri.StreamStateChanged,this.handleStreamStateUpdate).on(Ri.ConnectionQualityUpdate,this.handleConnectionQualityUpdate).on(Ri.SubscriptionError,this.handleSubscriptionError).on(Ri.SubscriptionPermissionUpdate,this.handleSubscriptionPermissionUpdate).on(Ri.MediaTrackAdded,(function(t,n,r){e.onTrackAdded(t,n,r)})).on(Ri.Disconnected,(function(t){e.handleDisconnect(e.options.stopLocalTrackOnUnpublish,t)})).on(Ri.ActiveSpeakersUpdate,this.handleActiveSpeakersUpdate).on(Ri.DataPacketReceived,this.handleDataPacket).on(Ri.Resuming,(function(){e.clearConnectionReconcile(),e.setAndEmitConnectionState(Xo.Reconnecting)&&e.emit(Pi.Reconnecting),e.cachedParticipantSids=Array.from(e.participants.keys())})).on(Ri.Resumed,(function(){e.setAndEmitConnectionState(Xo.Connected),e.emit(Pi.Reconnected),e.registerConnectionReconcile(),e.updateSubscriptions(),Array.from(e.participants.values()).filter((function(t){return!e.cachedParticipantSids.includes(t.sid)})).forEach((function(t){return e.emit(Pi.ParticipantConnected,t)})),e.cachedParticipantSids=[]})).on(Ri.SignalResumed,(function(){e.state===Xo.Reconnecting&&e.sendSyncState()})).on(Ri.Restarting,this.handleRestarting).on(Ri.SignalRestarted,this.handleSignalRestarted).on(Ri.DCBufferStatusChanged,(function(t,n){e.emit(Pi.DCBufferStatusChanged,t,n)})),this.localParticipant&&this.localParticipant.setupEngine(this.engine),this.e2eeManager&&this.e2eeManager.setupEngine(this.engine))}},{key:"prepareConnection",value:function(e,t){return Xt(this,void 0,void 0,n().mark((function r(){var i;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(this.state===Xo.Disconnected){n.next=2;break}return n.abrupt("return");case 2:if(B.debug("prepareConnection to ".concat(e)),n.prev=3,!ia(new URL(e))||!t){n.next=16;break}return this.regionUrlProvider=new bo(e,t),n.next=8,this.regionUrlProvider.getNextBestRegionUrl();case 8:if(!(i=n.sent)||this.state!==Xo.Disconnected){n.next=14;break}return this.regionUrl=i,n.next=13,fetch(wa(i),{method:"HEAD"});case 13:B.debug("prepared connection to ".concat(i));case 14:n.next=18;break;case 16:return n.next=18,fetch(wa(e),{method:"HEAD"});case 18:n.next=23;break;case 20:n.prev=20,n.t0=n.catch(3),B.warn("could not prepare connection",{error:n.t0});case 23:case"end":return n.stop()}}),r,this,[[3,20]])})))}},{key:"getParticipantByIdentity",value:function(e){if(this.localParticipant.identity===e)return this.localParticipant;var t=this.identityToSid.get(e);return t?this.participants.get(t):void 0}},{key:"clearConnectionFutures",value:function(){this.connectFuture=void 0}},{key:"simulateScenario",value:function(e,t){return Xt(this,void 0,void 0,n().mark((function r(){var i,a,o=this;return n().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:i=function(){},r.t0=e,r.next="signal-reconnect"===r.t0?4:"speaker"===r.t0?7:"node-failure"===r.t0?9:"server-leave"===r.t0?11:"migration"===r.t0?13:"resume-reconnect"===r.t0?15:"full-reconnect"===r.t0?19:"force-tcp"===r.t0||"force-tls"===r.t0?23:"subscriber-bandwidth"===r.t0?26:30;break;case 4:return r.next=6,this.engine.client.handleOnClose("simulate disconnect");case 6:return r.abrupt("break",30);case 7:return a=new ni({scenario:{case:"speakerUpdate",value:3}}),r.abrupt("break",30);case 9:return a=new ni({scenario:{case:"nodeFailure",value:!0}}),r.abrupt("break",30);case 11:return a=new ni({scenario:{case:"serverLeave",value:!0}}),r.abrupt("break",30);case 13:return a=new ni({scenario:{case:"migration",value:!0}}),r.abrupt("break",30);case 15:return this.engine.failNext(),r.next=18,this.engine.client.handleOnClose("simulate resume-disconnect");case 18:return r.abrupt("break",30);case 19:return this.engine.fullReconnectOnNext=!0,r.next=22,this.engine.client.handleOnClose("simulate full-reconnect");case 22:return r.abrupt("break",30);case 23:return a=new ni({scenario:{case:"switchCandidateProtocol",value:"force-tls"===e?2:1}}),i=function(){return Xt(o,void 0,void 0,n().mark((function e(){var t;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:(t=this.engine.client.onLeave)&&t(new Br({reason:St.CLIENT_INITIATED,canReconnect:!0}));case 2:case"end":return e.stop()}}),e,this)})))},r.abrupt("break",30);case 26:if(void 0!==t&&"number"==typeof t){r.next=28;break}throw new Error("subscriber-bandwidth requires a number as argument");case 28:return a=new ni({scenario:{case:"subscriberBandwidth",value:BigInt(t)}}),r.abrupt("break",30);case 30:a&&(this.engine.client.sendSimulateScenario(a),i());case 31:case"end":return r.stop()}}),r,this)})))}},{key:"canPlaybackAudio",get:function(){return this.audioEnabled}},{key:"getActiveAudioOutputDevice",value:function(){var e,t;return null!==(t=null===(e=this.options.audioOutput)||void 0===e?void 0:e.deviceId)&&void 0!==t?t:""}},{key:"getActiveDevice",value:function(e){return this.localParticipant.activeDeviceMap.get(e)}},{key:"switchActiveDevice",value:function(e,t){var r,i,a,o=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return Xt(this,void 0,void 0,n().mark((function s(){var c,u,d,l,h,f,p,m;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(c=!1,u=!0,d=o?{exact:t}:t,"audioinput"!==e){n.next=20;break}return l=this.options.audioCaptureDefaults.deviceId,this.options.audioCaptureDefaults.deviceId=d,c=l!==d,h=Array.from(this.localParticipant.audioTracks.values()).filter((function(e){return e.source===Mi.Source.Microphone})),n.prev=8,n.next=11,Promise.all(h.map((function(e){var t;return null===(t=e.audioTrack)||void 0===t?void 0:t.setDeviceId(d)})));case 11:u=n.sent.every((function(e){return!0===e})),n.next=18;break;case 14:throw n.prev=14,n.t0=n.catch(8),this.options.audioCaptureDefaults.deviceId=l,n.t0;case 18:n.next=57;break;case 20:if("videoinput"!==e){n.next=37;break}return f=this.options.videoCaptureDefaults.deviceId,this.options.videoCaptureDefaults.deviceId=d,c=f!==d,p=Array.from(this.localParticipant.videoTracks.values()).filter((function(e){return e.source===Mi.Source.Camera})),n.prev=25,n.next=28,Promise.all(p.map((function(e){var t;return null===(t=e.videoTrack)||void 0===t?void 0:t.setDeviceId(d)})));case 28:u=n.sent.every((function(e){return!0===e})),n.next=35;break;case 31:throw n.prev=31,n.t1=n.catch(25),this.options.videoCaptureDefaults.deviceId=f,n.t1;case 35:n.next=57;break;case 37:if("audiooutput"!==e){n.next=57;break}if(!(!Xi()&&!this.options.expWebAudioMix||this.options.expWebAudioMix&&this.audioContext&&!("setSinkId"in this.audioContext))){n.next=40;break}throw new Error("cannot switch audio output, setSinkId not supported");case 40:if(null!==(r=(a=this.options).audioOutput)&&void 0!==r||(a.audioOutput={}),m=this.options.audioOutput.deviceId,this.options.audioOutput.deviceId=t,c=m!==d,n.prev=44,!this.options.expWebAudioMix){n.next=49;break}null===(i=this.audioContext)||void 0===i||i.setSinkId(t),n.next=51;break;case 49:return n.next=51,Promise.all(Array.from(this.participants.values()).map((function(e){return e.setAudioOutput({deviceId:t})})));case 51:n.next=57;break;case 53:throw n.prev=53,n.t2=n.catch(44),this.options.audioOutput.deviceId=m,n.t2;case 57:return c&&u&&(this.localParticipant.activeDeviceMap.set(e,t),this.emit(Pi.ActiveDeviceChanged,e,t)),n.abrupt("return",u);case 59:case"end":return n.stop()}}),s,this,[[8,14],[25,31],[44,53]])})))}},{key:"setupLocalParticipantEvents",value:function(){this.localParticipant.on(Ei.ParticipantMetadataChanged,this.onLocalParticipantMetadataChanged).on(Ei.ParticipantNameChanged,this.onLocalParticipantNameChanged).on(Ei.TrackMuted,this.onLocalTrackMuted).on(Ei.TrackUnmuted,this.onLocalTrackUnmuted).on(Ei.LocalTrackPublished,this.onLocalTrackPublished).on(Ei.LocalTrackUnpublished,this.onLocalTrackUnpublished).on(Ei.ConnectionQualityChanged,this.onLocalConnectionQualityChanged).on(Ei.MediaDevicesError,this.onMediaDevicesError).on(Ei.AudioStreamAcquired,this.startAudio).on(Ei.ParticipantPermissionsChanged,this.onLocalParticipantPermissionsChanged)}},{key:"recreateEngine",value:function(){var e;null===(e=this.engine)||void 0===e||e.close(),this.engine=void 0,this.participants.clear(),this.maybeCreateEngine()}},{key:"onTrackAdded",value:function(e,t,n){var r=this;if(this.state===Xo.Connecting||this.state===Xo.Reconnecting){var a=function(){r.onTrackAdded(e,t,n),o()},o=function e(){r.off(Pi.Reconnected,a),r.off(Pi.Connected,a),r.off(Pi.Disconnected,e)};return this.once(Pi.Reconnected,a),this.once(Pi.Connected,a),void this.once(Pi.Disconnected,o)}if(this.state!==Xo.Disconnected){var s=function(e){var t=e.split("|");return t.length>1?[t[0],e.substr(t[0].length+1)]:[e,""]}(t.id),c=s[0],u=s[1],d=e.id;if(u&&u.startsWith("TR")&&(d=u),c!==this.localParticipant.sid){var l,h=this.participants.get(c);if(h)this.options.adaptiveStream&&(l="object"===i(this.options.adaptiveStream)?this.options.adaptiveStream:{}),h.addSubscribedMediaTrack(e,d,t,n,l);else B.error("Tried to add a track for a participant, that's not present. Sid: ".concat(c))}else B.warn("tried to create RemoteParticipant for local participant")}else B.warn("skipping incoming track after Room disconnected")}},{key:"handleDisconnect",value:function(){var e,t=this,n=0>=arguments.length||void 0===arguments[0]||arguments[0],r=arguments.length>1?arguments[1]:void 0;if(this.clearConnectionReconcile(),this.state!==Xo.Disconnected){this.regionUrl=void 0;try{this.participants.forEach((function(e){e.tracks.forEach((function(t){e.unpublishTrack(t.trackSid)}))})),this.localParticipant.tracks.forEach((function(e){var r,i;e.track&&t.localParticipant.unpublishTrack(e.track,n),n&&(null===(r=e.track)||void 0===r||r.detach(),null===(i=e.track)||void 0===i||i.stop())})),this.localParticipant.off(Ei.ParticipantMetadataChanged,this.onLocalParticipantMetadataChanged).off(Ei.ParticipantNameChanged,this.onLocalParticipantNameChanged).off(Ei.TrackMuted,this.onLocalTrackMuted).off(Ei.TrackUnmuted,this.onLocalTrackUnmuted).off(Ei.LocalTrackPublished,this.onLocalTrackPublished).off(Ei.LocalTrackUnpublished,this.onLocalTrackUnpublished).off(Ei.ConnectionQualityChanged,this.onLocalConnectionQualityChanged).off(Ei.MediaDevicesError,this.onMediaDevicesError).off(Ei.AudioStreamAcquired,this.startAudio).off(Ei.ParticipantPermissionsChanged,this.onLocalParticipantPermissionsChanged),this.localParticipant.tracks.clear(),this.localParticipant.videoTracks.clear(),this.localParticipant.audioTracks.clear(),this.participants.clear(),this.activeSpeakers=[],this.audioContext&&"boolean"==typeof this.options.expWebAudioMix&&(this.audioContext.close(),this.audioContext=void 0),na()&&(window.removeEventListener("beforeunload",this.onPageLeave),window.removeEventListener("pagehide",this.onPageLeave),window.removeEventListener("freeze",this.onPageLeave),null===(e=navigator.mediaDevices)||void 0===e||e.removeEventListener("devicechange",this.handleDeviceChange))}finally{this.setAndEmitConnectionState(Xo.Disconnected),this.emit(Pi.Disconnected,r)}}}},{key:"handleParticipantDisconnected",value:function(e,t){this.participants.delete(e),t&&(this.identityToSid.delete(t.identity),t.tracks.forEach((function(e){t.unpublishTrack(e.trackSid,!0)})),this.emit(Pi.ParticipantDisconnected,t))}},{key:"acquireAudioContext",value:function(){var e,t;return Xt(this,void 0,void 0,n().mark((function r(){var i,a=this;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if("boolean"!=typeof this.options.expWebAudioMix&&this.options.expWebAudioMix.audioContext?this.audioContext=this.options.expWebAudioMix.audioContext:this.audioContext&&"closed"!==this.audioContext.state||(this.audioContext=null!==(e=ji())&&void 0!==e?e:void 0),!this.audioContext||"suspended"!==this.audioContext.state){n.next=10;break}return n.prev=2,n.next=5,this.audioContext.resume();case 5:n.next=10;break;case 7:n.prev=7,n.t0=n.catch(2),B.warn(n.t0);case 10:this.options.expWebAudioMix&&this.participants.forEach((function(e){return e.setAudioContext(a.audioContext)})),this.localParticipant.setAudioContext(this.audioContext),(i="running"===(null===(t=this.audioContext)||void 0===t?void 0:t.state))!==this.canPlaybackAudio&&(this.audioEnabled=i,this.emit(Pi.AudioPlaybackStatusChanged,i));case 14:case"end":return n.stop()}}),r,this,[[2,7]])})))}},{key:"createParticipant",value:function(e,t){var n;return n=t?$o.fromParticipantInfo(this.engine.client,t):new $o(this.engine.client,e,"",void 0,void 0),this.options.expWebAudioMix&&n.setAudioContext(this.audioContext),n}},{key:"getOrCreateParticipant",value:function(e,t){var n=this;if(this.participants.has(e))return this.participants.get(e);var r=this.createParticipant(e,t);return this.participants.set(e,r),this.identityToSid.set(t.identity,t.sid),this.emitWhenConnected(Pi.ParticipantConnected,r),r.on(Ei.TrackPublished,(function(e){n.emitWhenConnected(Pi.TrackPublished,e,r)})).on(Ei.TrackSubscribed,(function(e,t){e.kind===Mi.Kind.Audio&&(e.on(Ii.AudioPlaybackStarted,n.handleAudioPlaybackStarted),e.on(Ii.AudioPlaybackFailed,n.handleAudioPlaybackFailed)),n.emit(Pi.TrackSubscribed,e,t,r)})).on(Ei.TrackUnpublished,(function(e){n.emit(Pi.TrackUnpublished,e,r)})).on(Ei.TrackUnsubscribed,(function(e,t){n.emit(Pi.TrackUnsubscribed,e,t,r)})).on(Ei.TrackSubscriptionFailed,(function(e){n.emit(Pi.TrackSubscriptionFailed,e,r)})).on(Ei.TrackMuted,(function(e){n.emitWhenConnected(Pi.TrackMuted,e,r)})).on(Ei.TrackUnmuted,(function(e){n.emitWhenConnected(Pi.TrackUnmuted,e,r)})).on(Ei.ParticipantMetadataChanged,(function(e){n.emitWhenConnected(Pi.ParticipantMetadataChanged,e,r)})).on(Ei.ParticipantNameChanged,(function(e){n.emitWhenConnected(Pi.ParticipantNameChanged,e,r)})).on(Ei.ConnectionQualityChanged,(function(e){n.emitWhenConnected(Pi.ConnectionQualityChanged,e,r)})).on(Ei.ParticipantPermissionsChanged,(function(e){n.emitWhenConnected(Pi.ParticipantPermissionsChanged,e,r)})).on(Ei.TrackSubscriptionStatusChanged,(function(e,t){n.emitWhenConnected(Pi.TrackSubscriptionStatusChanged,e,t,r)})).on(Ei.TrackSubscriptionFailed,(function(e,t){n.emit(Pi.TrackSubscriptionFailed,e,r,t)})).on(Ei.TrackSubscriptionPermissionChanged,(function(e,t){n.emitWhenConnected(Pi.TrackSubscriptionPermissionChanged,e,t,r)})),t&&r.updateInfo(t),r}},{key:"sendSyncState",value:function(){var e,t,n,r,i=null===(e=this.engine.subscriber)||void 0===e?void 0:e.getLocalDescription(),a=null===(t=this.engine.subscriber)||void 0===t?void 0:t.getRemoteDescription();if(i){var o=null===(r=null===(n=this.connOptions)||void 0===n?void 0:n.autoSubscribe)||void 0===r||r,s=new Array;this.participants.forEach((function(e){e.tracks.forEach((function(e){e.isDesired!==o&&s.push(e.trackSid)}))})),this.engine.client.sendSyncState(new ei({answer:Ra({sdp:i.sdp,type:i.type}),offer:a?Ra({sdp:a.sdp,type:a.type}):void 0,subscription:new Ar({trackSids:s,subscribe:!o,participantTracks:[]}),publishTracks:this.localParticipant.publishedTracksInfo(),dataChannels:this.localParticipant.dataChannelsInfo()}))}}},{key:"updateSubscriptions",value:function(){var e,t=S(this.participants.values());try{for(t.s();!(e=t.n()).done;){var n,r=S(e.value.videoTracks.values());try{for(r.s();!(n=r.n()).done;){var i=n.value;i.isSubscribed&&i instanceof Zo&&i.emitTrackUpdate()}}catch(e){r.e(e)}finally{r.f()}}}catch(e){t.e(e)}finally{t.f()}}},{key:"registerConnectionReconcile",value:function(){var e=this;this.clearConnectionReconcile();var t=0;this.connectionReconcileInterval=mi.setInterval((function(){e.engine&&!e.engine.isClosed&&e.engine.verifyTransport()?t=0:(t++,B.warn("detected connection state mismatch",{numFailures:t}),3>t||(e.recreateEngine(),e.handleDisconnect(e.options.stopLocalTrackOnUnpublish,St.STATE_MISMATCH)))}),2e3)}},{key:"clearConnectionReconcile",value:function(){this.connectionReconcileInterval&&mi.clearInterval(this.connectionReconcileInterval)}},{key:"setAndEmitConnectionState",value:function(e){return e!==this.state&&(this.state=e,this.emit(Pi.ConnectionStateChanged,this.state),!0)}},{key:"emitWhenConnected",value:function(e){if(this.state===Xo.Connected){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;t>r;r++)n[r-1]=arguments[r];return this.emit.apply(this,[e].concat(n))}return!1}},{key:"simulateParticipants",value:function(e){var t,r;return Xt(this,void 0,void 0,n().mark((function i(){var a,o,s,c,u,d,l,h,f,p,m;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(a=Object.assign({audio:!0,video:!0,useRealTracks:!1},e.publish),o=Object.assign({count:9,audio:!1,video:!0,aspectRatios:[1.66,1.7,1.3]},e.participants),this.handleDisconnect(),this.roomInfo=new xt({sid:"RM_SIMULATED",name:"simulated-room",emptyTimeout:0,maxParticipants:0,creationTime:pe.parse((new Date).getTime()),metadata:"",numParticipants:1,numPublishers:1,turnPassword:"",enabledCodecs:[],activeRecording:!1}),this.localParticipant.updateInfo(new Dt({identity:"simulated-local",name:"local-name"})),this.setupLocalParticipantEvents(),this.emit(Pi.SignalConnected),this.emit(Pi.Connected),this.setAndEmitConnectionState(Xo.Connected),!a.video){n.next=26;break}if(n.t0=Qo,n.t1=Mi.Kind.Video,n.t2=new Ot({source:kt.CAMERA,sid:Math.floor(1e4*Math.random()).toString(),type:vt.AUDIO,name:"video-dummy"}),n.t3=Bo,!a.useRealTracks){n.next=20;break}return n.next=17,window.navigator.mediaDevices.getUserMedia({video:!0});case 17:n.t4=n.sent.getVideoTracks()[0],n.next=21;break;case 20:n.t4=va(null!==(t=160*o.aspectRatios[0])&&void 0!==t?t:1,160,!0,!0);case 21:n.t5=n.t4,n.t6=new n.t3(n.t5),s=new n.t0(n.t1,n.t2,n.t6),this.localParticipant.addTrackPublication(s),this.localParticipant.emit(Ei.LocalTrackPublished,s);case 26:if(!a.audio){n.next=43;break}if(n.t7=Qo,n.t8=Mi.Kind.Audio,n.t9=new Ot({source:kt.MICROPHONE,sid:Math.floor(1e4*Math.random()).toString(),type:vt.AUDIO}),n.t10=To,!a.useRealTracks){n.next=37;break}return n.next=34,navigator.mediaDevices.getUserMedia({audio:!0});case 34:n.t11=n.sent.getAudioTracks()[0],n.next=38;break;case 37:n.t11=ka();case 38:n.t12=n.t11,n.t13=new n.t10(n.t12),c=new n.t7(n.t8,n.t9,n.t13),this.localParticipant.addTrackPublication(c),this.localParticipant.emit(Ei.LocalTrackPublished,c);case 43:for(u=0;u<o.count-1;u+=1)d=new Dt({sid:Math.floor(1e4*Math.random()).toString(),identity:"simulated-".concat(u),state:Rt.ACTIVE,tracks:[],joinedAt:pe.parse(Date.now())}),l=this.getOrCreateParticipant(d.identity,d),o.video&&(h=va(null!==(r=160*o.aspectRatios[u%o.aspectRatios.length])&&void 0!==r?r:1,160,!1,!0),f=new Ot({source:kt.CAMERA,sid:Math.floor(1e4*Math.random()).toString(),type:vt.AUDIO}),l.addSubscribedMediaTrack(h,f.sid,new MediaStream([h])),d.tracks=[].concat(g(d.tracks),[f])),o.audio&&(p=ka(),m=new Ot({source:kt.MICROPHONE,sid:Math.floor(1e4*Math.random()).toString(),type:vt.AUDIO}),l.addSubscribedMediaTrack(p,m.sid,new MediaStream([p])),d.tracks=[].concat(g(d.tracks),[m])),l.updateInfo(d);case 44:case"end":return n.stop()}}),i,this)})))}},{key:"emit",value:function(e){for(var t,n=arguments.length,i=new Array(n>1?n-1:0),a=1;n>a;a++)i[a-1]=arguments[a];return e!==Pi.ActiveSpeakersChanged&&B.debug("room event ".concat(e),{event:e,args:i}),(t=v(h(r.prototype),"emit",this)).call.apply(t,[this,e].concat(i))}}],[{key:"getLocalDevices",value:function(e){var t=1>=arguments.length||void 0===arguments[1]||arguments[1];return Ba.getInstance().getDevices(e,t)}}]),r}();!function(e){e[e.IDLE=0]="IDLE",e[e.RUNNING=1]="RUNNING",e[e.SKIPPED=2]="SKIPPED",e[e.SUCCESS=3]="SUCCESS",e[e.FAILED=4]="FAILED"}(ts||(ts={}));function rs(e){var t,r;return Xt(this,void 0,void 0,n().mark((function i(){var a,o,s,c;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return null!=e||(e={}),null!==(t=e.audio)&&void 0!==t||(e.audio=!0),null!==(r=e.video)&&void 0!==r||(e.video=!0),a=Ui(e,lo,ho),o=Fi(a),s=navigator.mediaDevices.getUserMedia(o),e.audio&&(Ba.userMediaPromiseMap.set("audioinput",s),s.catch((function(){return Ba.userMediaPromiseMap.delete("audioinput")}))),e.video&&(Ba.userMediaPromiseMap.set("videoinput",s),s.catch((function(){return Ba.userMediaPromiseMap.delete("videoinput")}))),n.next=10,s;case 10:return c=n.sent,n.abrupt("return",c.getTracks().map((function(t){var n,r="audio"===t.kind;r?e.audio:e.video;var i=r?o.audio:o.video;"boolean"!=typeof i&&(n=i),n?n.deviceId=t.getSettings().deviceId:n={deviceId:t.getSettings().deviceId};var a=Co(t,n);return a.kind===Mi.Kind.Video?a.source=Mi.Source.Camera:a.kind===Mi.Kind.Audio&&(a.source=Mi.Source.Microphone),a.mediaStream=c,a})));case 12:case"end":return n.stop()}}),i)})))}var is=new WeakMap,as=function(){function e(t){s(this,e),E(this,is,{writable:!0,value:null}),this.url=t,this.socket=null,this.isOpen=!1,this.messageHandlers=new Map,this.openHandlers=new Map,this.closeHandlers=[],this.errorHandlers=[]}return u(e,[{key:"connect",value:function(){var e=this;this.socket=new WebSocket(this.url),this.socket.onopen=function(t){e.isOpen=!0,e.openHandlers.size>0&&e.openHandlers.forEach((function(e){return e()})),w(e,is)&&(clearInterval(w(e,is)),T(e,is,null)),T(e,is,setInterval((function(){e.send({cmd:"HeartBreak"})}),1e4))},this.socket.onmessage=function(t){e.messageHandlers.size>0&&e.messageHandlers.forEach((function(e){return e(t.data)}))},this.socket.onclose=function(t){e.closeHandlers.forEach((function(e){return e(t)}))},this.socket.onerror=function(t){e.errorHandlers.forEach((function(e){return e(t)}))}}},{key:"send",value:function(e){var t=this;this.socket.readyState===WebSocket.OPEN?this.socket.send(JSON.stringify(e)):this.socket.readyState===WebSocket.CLOSED?this.isOpen&&(this.connect(),this.clearOpenHandler(),this.addOpenHandler("MsgSend",(function(){t.socket.send(JSON.stringify(e))}))):(this.clearOpenHandler(),this.addOpenHandler("MsgSend",(function(){t.socket.send(JSON.stringify(e))})))}},{key:"addMessageHandler",value:function(e,t){this.messageHandlers.has(e)?(this.messageHandlers.delete(e),this.messageHandlers.set(e,t)):this.messageHandlers.set(e,t)}},{key:"addOpenHandler",value:function(e,t){this.openHandlers.has(e)?(this.openHandlers.delete(e),this.openHandlers.set(e,t)):this.openHandlers.set(e,t)}},{key:"addCloseHandler",value:function(e){this.closeHandlers.push(e)}},{key:"addErrorHandler",value:function(e){this.errorHandlers.push(e)}},{key:"clearMessageHandler",value:function(){this.messageHandlers.clear()}},{key:"clearOpenHandler",value:function(){this.openHandlers.clear()}},{key:"clearCloseHandler",value:function(){this.closeHandlers=[]}},{key:"clearErrorHandler",value:function(){this.errorHandlers=[]}},{key:"close",value:function(){this.socket.close(),this.isOpen=!1,w(this,is)&&(clearInterval(w(this,is)),T(this,is,null))}}]),e}();var os=new WeakMap,ss=function(){function e(){var t;s(this,e),E(this,os,{writable:!0,value:void 0}),T(this,os,{all:t=t||new Map,on:function(e,n){var r=t.get(e);r?r.push(n):t.set(e,[n])},off:function(e,n){var r=t.get(e);r&&(n?r.splice(r.indexOf(n)>>>0,1):t.set(e,[]))},emit:function(e,n){var r=t.get(e);r&&r.slice().map((function(e){e(n)})),(r=t.get("*"))&&r.slice().map((function(t){t(e,n)}))}})}return u(e,[{key:"on",value:function(e,t){w(this,os).on(e,t)}},{key:"off",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;t&&"function"==typeof t?w(this,os).off(e,t):w(this,os).off(e)}},{key:"emit",value:function(e,t){w(this,os).emit(e,t)}},{key:"removeAllListeners",value:function(){w(this,os).all.clear()}}]),e}(),cs=new WeakMap,us=new WeakMap,ds=new WeakMap,ls=new WeakSet,hs=new WeakSet,fs=new WeakSet,ps=new WeakSet,ms=new WeakSet,vs=new WeakSet,ks=new WeakSet,gs=new WeakSet,ys=new WeakSet,bs=function(t){function r(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=n.userId,a=n.org,o=n.roomName,c=void 0===o?"":o,u=n.roomMode,d=void 0===u?"auto":u,l=n.baseUrl;s(this,r),R(m(t=e(this,r)),ys),R(m(t),gs),R(m(t),ks),R(m(t),vs),R(m(t),ms),R(m(t),ps),R(m(t),fs),R(m(t),hs),R(m(t),ls),E(m(t),cs,{writable:!0,value:""}),E(m(t),us,{writable:!0,value:""}),E(m(t),ds,{writable:!0,value:null}),t.userId=i,t.org=a,t.roomName=c,t.roomNum,t.room=null,t.roomMode=d,t.baseUrl=l,t.encoder=new TextEncoder,t.decoder=new TextDecoder;var h="http:"==t.baseUrl.split("//")[0]?"ws://":"wss://";return t.websocketUrl="".concat(h).concat(t.baseUrl.split("//")[1],"/socket?org=").concat(t.org,"&identity=").concat(t.userId),t.socket=new as(t.websocketUrl),t.socket.addOpenHandler("SocketOpen",(function(){t.emit("socketOpen")})),t.socket.connect(),x(m(t),ls,Ss).call(m(t)),t}var i,a,c,d,h,f,p,v,k,g,y,b,C,P,I;return l(r,ss),u(r,[{key:"isShareEnable",get:function(){return!(!this.room||!this.room.localParticipant)&&this.room.localParticipant.isScreenShareEnabled}},{key:"isCameraEnable",get:function(){return!(!this.room||!this.room.localParticipant)&&this.room.localParticipant.isCameraEnabled}},{key:"isMicrophoneEnable",get:function(){return!(!this.room||!this.room.localParticipant)&&this.room.localParticipant.isMicrophoneEnabled}},{key:"sendMessage",value:function(e,t){var n={cmd:"SendText",data:{room:this.roomNum,identity:this.userId,name:t,text:e}};this.socket.send(n)}},{key:"screenShot",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=x(this,fs,Ts).call(this,e);if(n){var r=document.createElement("canvas");return r.width=t.width,r.height=t.height,r.getContext("2d").drawImage(n,0,0,t.width,t.height),r.toDataURL("image/png",t.quality)}this.emit("errorPush",{type:"screenShot",message:"DOM元素不存在"})}},{key:"uploadScreenShot",value:(I=o(n().mark((function e(t){var r,i,a,o,s,c,u=arguments;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=u.length>1&&void 0!==u[1]?u[1]:{},a=this.screenShot(t,r),(o=a.indexOf(","))>-1&&(i=a.substring(o+1)),e.next=6,fetch(this.baseUrl+"/api/FileFind/upload/image/sample",{method:"POST",headers:{"Content-Type":"application/json;charset=utf-8"},body:JSON.stringify({base64:i,org:this.org,owner:this.userId})});case 6:return s=e.sent,e.prev=7,e.next=10,s.json();case 10:200===(null==(c=e.sent)?void 0:c.code)?this.emit("imageUploadSuccess",a):this.emit("imageUploadFail",c),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(7),this.emit("imageUploadFail",e.t0);case 17:case"end":return e.stop()}}),e,this,[[7,14]])}))),function(e){return I.apply(this,arguments)})},{key:"judgeUserInMeeting",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return new Promise((function(n,r){fetch(e.baseUrl+"/api/v1/IsParticipantExist",{method:"POST",headers:{"Content-Type":"application/json;charset=utf-8"},body:JSON.stringify({room:t,identity:e.userId})}).then((function(e){return e.json()})).then((function(e){200===(null==e?void 0:e.code)?n(null==e?void 0:e.data):r()})).catch((function(e){r(e)}))}))}},{key:"setParticipantDeafness",value:function(e){var t=this;if(e)return new Promise((function(n,r){var i={cmd:"DoDeafness",data:{room:t.roomNum,identity:e}};t.socket.send(i),t.socket.addMessageHandler("SetParticipantDeafness",(function(e){var i,a=JSON.parse(e);"DoDeafness"===a.cmd&&("200"==(null==a?void 0:a.data.code)?n():(t.emit("errorPush",{type:"setParticipantDeafness",message:null!==(i=a.data.msg)&&void 0!==i?i:"与会者置聋失败"}),r()))}))}));this.emit("errorPush",{type:"setParticipantDeafness",message:"identity不能为空"})}},{key:"createRoom",value:(P=o(n().mark((function e(){var t,r,i,a,o;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.baseUrl){e.next=3;break}return this.emit("errorPush",{type:"createRoom",message:"请输入baseUrl"}),e.abrupt("return");case 3:if(this.roomName){e.next=6;break}return this.emit("errorPush",{type:"createRoom",message:"房间名不能为空"}),e.abrupt("return");case 6:if(this.org&&this.userId){e.next=9;break}return this.emit("errorPush",{type:"createRoom",message:"创建房间前请先登录"}),e.abrupt("return");case 9:return i="",e.prev=10,e.next=13,fetch(this.baseUrl+"/api/v1/creatRoom",{method:"POST",body:JSON.stringify({roomAlias:this.roomName,org:this.org,owner:this.userId})});case 13:return a=e.sent,e.next=16,a.json();case 16:if(200!=(t=e.sent).code){e.next=34;break}return i=t.data,e.next=21,fetch(this.baseUrl+"/api/v1/createDispatcher",{method:"POST",body:JSON.stringify({org:this.org,room:i,called:i})});case 21:return o=e.sent,e.next=24,o.json();case 24:if(200!=(r=e.sent).code){e.next=30;break}this.emit("roomCreatedSuccess",i),T(this,ds,r.data),e.next=32;break;case 30:return this.emit("roomCreatedFail",r.msg),e.abrupt("return","");case 32:e.next=36;break;case 34:return this.emit("roomCreatedFail",t.msg),e.abrupt("return","");case 36:e.next=41;break;case 38:e.prev=38,e.t0=e.catch(10),this.emit("roomCreatedFail",e.t0);case 41:return e.abrupt("return",i);case 42:case"end":return e.stop()}}),e,this,[[10,38]])}))),function(){return P.apply(this,arguments)})},{key:"joinRoom",value:(C=o(n().mark((function e(){var t,r,i,a,o,s,c,u,d,l,h,f,p,m,v,k,g,y,b,S,C,P,E,R,I,D,N=this,O=arguments;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(o=(a=O.length>0&&void 0!==O[0]?O[0]:{}).audioDeviceId,s=void 0===o?"":o,c=a.videoDeviceId,u=void 0===c?"":c,d=a.outputDeviceId,l=void 0===d?"":d,h=a.facingMode,f=void 0===h?"user":h,p=a.autoGainControl,m=a.echoCancellation,v=a.noiseSuppression,k=a.roomNum,g=a.cameraStatus,y=void 0===g||g,b=a.microphoneStatus,S=void 0===b||b,k){e.next=4;break}return this.emit("errorPush",{type:"joinRoom",message:"请输入房间号"}),e.abrupt("return");case 4:return C={audioCaptureDefaults:{deviceId:s||"default",autoGainControl:null!=p&&p,echoCancellation:null!=m&&m,noiseSuppression:null!=v&&v},videoCaptureDefaults:{},stopLocalTrackOnUnpublish:!0,adaptiveStream:!0},l&&(C.audioOutput={deviceId:l}),P={autoSubscribe:!0},u?C.videoCaptureDefaults.deviceId=u:C.videoCaptureDefaults.facingMode=f,this.roomNum=k,e.prev=9,e.next=12,x(this,ms,Ps).call(this,this.roomNum);case 12:return E=e.sent,T(this,cs,E.roomUrl),T(this,us,E.token),this.room=new ns(C),e.next=18,this.room.prepareConnection(w(this,cs),w(this,us));case 18:return x(this,vs,Es).call(this),e.next=21,this.room.connect(w(this,cs),w(this,us),P);case 21:e.next=27;break;case 23:return e.prev=23,e.t0=e.catch(9),this.emit("joinRoomError",e.t0),e.abrupt("return");case 27:if("auto"!==this.roomMode){e.next=35;break}return e.next=30,this.room.localParticipant.setCameraEnabled(y);case 30:return e.next=32,this.room.localParticipant.setMicrophoneEnabled(S);case 32:x(this,hs,ws).call(this,"video"),e.next=40;break;case 35:return e.next=37,this.room.localParticipant.setMicrophoneEnabled(S);case 37:return e.next=39,this.room.localParticipant.setCameraEnabled(!1);case 39:x(this,hs,ws).call(this,"audio");case 40:return R=null!==(t=this.room.getActiveDevice("audioinput"))&&void 0!==t?t:"default",I=null!==(r=this.room.getActiveDevice("videoinput"))&&void 0!==r?r:"default",D=null!==(i=this.room.getActiveDevice("audiooutput"))&&void 0!==i?i:"default",this.room.participants.forEach((function(e){x(N,ks,Rs).call(N,e)})),x(this,ks,Rs).call(this,this.room.localParticipant),e.abrupt("return",{audioInputDevice:R,videoInputDevice:I,audioOutputDevice:D});case 46:case"end":return e.stop()}}),e,this,[[9,23]])}))),function(){return C.apply(this,arguments)})},{key:"makeCall",value:(b=o(n().mark((function e(t,r,i){var a,o,s,c;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.roomNum){e.next=3;break}return this.emit("errorPush",{type:"makeCall",message:"请先加入房间"}),e.abrupt("return");case 3:if(this.baseUrl){e.next=6;break}return this.emit("errorPush",{type:"makeCall",message:"baseUrl为空"}),e.abrupt("return");case 6:return a=null,o=null,o=i?{name:t,org:this.org,room:this.roomNum,dnis:r,type:i}:{name:t,org:this.org,room:this.roomNum,dnis:r},e.prev=8,e.next=11,fetch(this.baseUrl+"/api/v1/makeCall",{method:"POST",body:JSON.stringify(o)});case 11:return s=e.sent,e.next=14,s.json();case 14:200==(a=e.sent).code?this.emit("makeCallSuccess"):this.emit("errorPush",{type:"makeCall",message:null!==(c=a.msg)&&void 0!==c?c:"呼出失败"}),e.next=21;break;case 18:e.prev=18,e.t0=e.catch(8),this.emit("errorPush",{type:"makeCall",message:e.t0});case 21:case"end":return e.stop()}}),e,this,[[8,18]])}))),function(e,t,n){return b.apply(this,arguments)})},{key:"pushVideoStream",value:function(){var e,t=this;if(this.roomNum)return new Promise((function(e,n){var r={cmd:"StartOutputStream",data:{room:t.roomNum,url:"rtmp://10.2.14.199/live/"}};t.socket.send(r),t.socket.addMessageHandler("StartOutputStream",(function(r){var i,a,o=JSON.parse(r);"StartOutputStream"===o.cmd&&("200"==(null==o?void 0:o.data.code)?(t.emit("startPushing",o.data),e(o.data.result)):(t.emit("errorPush",{type:"pushVideoStream",message:null!==(i=null===(a=o.data)||void 0===a?void 0:a.msg)&&void 0!==i?i:"推流失败"}),n()))}))}));this.emit("errorPush",{type:"pushVideoStream",message:null!==(e=result.msg)&&void 0!==e?e:"请先加入房间"})}},{key:"stopPushVideoStream",value:function(){var e=this;if(this.roomNum)return new Promise((function(t,n){var r={cmd:"StopOutputStream",data:{room:e.roomNum}};e.socket.send(r),e.socket.addMessageHandler("StopOutputStream",(function(r){var i,a,o=JSON.parse(r);"StopOutputStream"===o.cmd&&("200"==(null==o?void 0:o.data.code)?(e.emit("stopPushing",o.data),t()):(e.emit("errorPush",{type:"stopPushVideoStream",message:null!==(i=null===(a=o.data)||void 0===a?void 0:a.msg)&&void 0!==i?i:"停止推流失败"}),n()))}))}));this.emit("errorPush",{type:"stopPushVideoStream",message:"请先加入房间"})}},{key:"moveoutParticipant",value:function(e){var t=this;if(this.roomNum)return new Promise((function(n,r){var i={cmd:"RemoveParticipant",data:{room:t.roomNum,identity:e}};t.socket.send(i),t.socket.addMessageHandler("RemoveParticipant",(function(e){var i,a=JSON.parse(e);"RemoveParticipant"===a.cmd&&("200"==(null==a?void 0:a.data.code)?n():(t.emit("errorPush",{type:"moveoutParticipant",message:null!==(i=a.data.msg)&&void 0!==i?i:"移出房间失败"}),r()))}))}));this.emit("errorPush",{type:"moveoutParticipant",message:"请先加入房间"})}},{key:"updateParticipantName",value:function(e,t){var n=this;if(this.roomNum)return new Promise((function(r,i){var a={cmd:"UpdateParticipant",data:{room:n.roomNum,identity:e,name:t}};n.socket.send(a),n.socket.addMessageHandler("UpdateParticipant",(function(e){var t,a=JSON.parse(e);"UpdateParticipant"===a.cmd&&("200"==(null==a?void 0:a.data.code)?r():(n.emit("errorPush",{type:"updateParticipantName",message:null!==(t=a.data.msg)&&void 0!==t?t:"修改与会者名称失败"}),i()))}))}));this.emit("errorPush",{type:"updateParticipantName",message:"请先加入房间"})}},{key:"setParticipantToHost",value:function(e,t){var n=this;if(this.roomNum&&e&&t&&this.userId)return new Promise((function(r,i){var a={cmd:"AddHost",data:{room:n.roomNum,identity:n.userId,hostId:e,hostName:t}};n.socket.addMessageHandler("SetParticipantToHost",(function(e){var t,a=JSON.parse(e);"AddHost"===a.cmd&&("200"==(null==a?void 0:a.data.code)?r():(n.emit("errorPush",{type:"setParticipantToHost",message:null!==(t=a.data.msg)&&void 0!==t?t:"添加为主持人失败"}),i()))})),n.socket.send(a)}));this.emit("errorPush",{type:"setParticipantToHost",message:"关键参数缺失"})}},{key:"startRecord",value:function(){var e=this;if(this.roomNum)return new Promise((function(t,n){var r={cmd:"StartRecording",data:{room:e.roomNum}};e.socket.send(r),e.socket.addMessageHandler("StartRecording",(function(r){var i,a=JSON.parse(r);"StartRecording"===a.cmd&&("200"==(null==a?void 0:a.data.code)?(e.emit("startRecording",a.data.result),t(a.data.result)):(e.emit("errorPush",{type:"startRecord",message:null!==(i=a.data.msg)&&void 0!==i?i:"录制失败"}),n()))}))}));this.emit("errorPush",{type:"startRecord",message:"请先加入房间"})}},{key:"stopRecord",value:function(){var e=this;if(this.roomNum)return new Promise((function(t,n){var r={cmd:"StopRecording",data:{room:e.roomNum}};e.socket.send(r),e.socket.addMessageHandler("StopRecording",(function(r){var i,a,o=JSON.parse(r);"StopRecording"===o.cmd&&("200"==(null==o?void 0:o.data.code)?(e.emit("stopRecording",o.data),t()):(e.emit("errorPush",{type:"stopRecord",message:null!==(i=null===(a=o.data)||void 0===a?void 0:a.msg)&&void 0!==i?i:"停止录制失败"}),n()))}))}));this.emit("errorPush",{type:"stopRecord",message:"请先加入房间"})}},{key:"pullStream",value:function(e,t){var n=this;return new Promise((function(r,i){t||i("streamIdentity不能为空");var a={cmd:"StartInputStream",data:{room:n.roomNum,identity:t,url:e}};n.socket.send(a),n.socket.addMessageHandler("StartInputStream",(function(e){var t,a,o=JSON.parse(e);"StartInputStream"===o.cmd&&("200"==(null==o?void 0:o.data.code)?(n.emit("startPulling",o.data.result),r(o.data.result)):(n.emit("errorPush",{type:"pullStream",message:null!==(t=null===(a=o.data)||void 0===a?void 0:a.msg)&&void 0!==t?t:"拉流失败"}),i()))}))}))}},{key:"stopPullStream",value:function(){var e=this;return new Promise((function(t,n){var r={cmd:"StopInputStream",data:{room:e.roomNum,identity:"liuang3a5gdf"}};e.socket.send(r),e.socket.addMessageHandler("StopInputStream",(function(r){var i,a,o=JSON.parse(r);"StopInputStream"===o.cmd&&("200"==(null==o?void 0:o.data.code)?(e.emit("stopPulling",o.data),t()):(e.emit("errorPush",{type:"stopPullStream",message:null!==(i=null===(a=o.data)||void 0===a?void 0:a.msg)&&void 0!==i?i:"停止拉流失败"}),n()))}))}))}},{key:"inviteThirdParty",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"1478649882954985474",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";return new Promise((function(i,a){e||(t.emit("errorPush",{type:"inviteThirdParty",message:"被邀请人姓名不能为空"}),a());var o={cmd:"Invite",data:{room:t.roomNum,identity:t.userId,inviteOrg:n,inviteIdentity:e,metadata:r}};t.socket.addMessageHandler("inviteThirdParty",(function(e){var n,r=JSON.parse(e);"Invite"===r.cmd&&("200"===(null===(n=r.data)||void 0===n?void 0:n.code)?(t.emit("inviteThirdSuccess"),i()):(t.emit("inviteThirdFail"),a()))})),t.socket.send(o)}))}},{key:"acceptInvite",value:function(e,t,n){var r=this;return new Promise((function(i,a){e&&t&&n||(r.emit("errorPush",{type:"acceptInvite",message:"关键参数缺失"}),a());var o={cmd:"Accept",data:{room:e,identity:r.userId,toOrg:n,toIdentity:t}};r.socket.addMessageHandler("inviteAccept",(function(e){var t,n=JSON.parse(e);"Accept"===n.cmd&&("200"===(null===(t=n.data)||void 0===t?void 0:t.code)?(r.emit("acceptInviteSuccess"),i()):(r.emit("acceptInviteFail"),a()))})),r.socket.send(o)}))}},{key:"refuseInvite",value:function(e,t,n){var r=this;return new Promise((function(i,a){e&&t&&n||(r.emit("errorPush",{type:"refuseInvite",message:"关键参数缺失"}),a());var o={cmd:"Refuse",data:{room:e,identity:r.userId,toOrg:n,toIdentity:t}};r.socket.addMessageHandler("inviteRefuse",(function(e){var t,n=JSON.parse(e);"Refuse"===n.cmd&&("200"===(null===(t=n.data)||void 0===t?void 0:t.code)?(r.emit("refuseInviteSuccess"),i()):(r.emit("refuseInviteFail"),a()))})),r.socket.send(o)}))}},{key:"enterText",value:function(e){if(this.room&&this.room.localParticipant){var t=this.room.localParticipant,n=this.encoder.encode(e);t.publishData(n,Mt.RELIABLE)}else this.emit("errorPush",{type:"enterText",message:"请先加入房间"})}},{key:"leaveRoom",value:(y=o(n().mark((function e(){var t;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.room){e.next=6;break}return e.next=3,this.room.disconnect();case 3:t={cmd:"DeleteRoom",data:{room:this.roomNum,identity:this.room.localParticipant.identity}},this.socket.send(t),this.room=null;case 6:case"end":return e.stop()}}),e,this)}))),function(){return y.apply(this,arguments)})},{key:"closeSocket",value:function(){this.socket.close()}},{key:"publishLocalParticipant",value:(g=o(n().mark((function e(t){var r,i,a,o,s;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,rs({audio:!0,video:!0});case 2:r=e.sent,i=S(r),e.prev=4,i.s();case 6:if((a=i.n()).done){e.next=13;break}return o=a.value,e.next=10,this.room.localParticipant.publishTrack(o);case 10:"video"===o.kind&&(s=document.getElementById(t),o.attach(s));case 11:e.next=6;break;case 13:e.next=18;break;case 15:e.prev=15,e.t0=e.catch(4),i.e(e.t0);case 18:return e.prev=18,i.f(),e.finish(18);case 21:case"end":return e.stop()}}),e,this,[[4,15,18,21]])}))),function(e){return g.apply(this,arguments)})},{key:"changeAudioDevice",value:(k=o(n().mark((function e(t){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.room){e.next=3;break}return e.next=3,this.room.switchActiveDevice("audioinput",t);case 3:case"end":return e.stop()}}),e,this)}))),function(e){return k.apply(this,arguments)})},{key:"changeVideoDevice",value:(v=o(n().mark((function e(t){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.room){e.next=11;break}return e.prev=1,e.next=4,this.room.switchActiveDevice("videoinput",t);case 4:e.sent,e.next=11;break;case 8:throw e.prev=8,e.t0=e.catch(1),new Error("sdkError",e.t0);case 11:case"end":return e.stop()}}),e,this,[[1,8]])}))),function(e){return v.apply(this,arguments)})},{key:"changeOutputDevice",value:(p=o(n().mark((function e(t){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.room){e.next=3;break}return e.next=3,this.room.switchActiveDevice("audiooutput",t);case 3:case"end":return e.stop()}}),e,this)}))),function(e){return p.apply(this,arguments)})},{key:"flipCamera",value:function(){var e,t,n,r=null===(e=this.room)||void 0===e?void 0:e.localParticipant.getTrack(Mi.Source.Camera);if(r){var i={facingMode:"user"===(null===(t=r.videoTrack)||void 0===t?void 0:t.constraints).facingMode?"environment":"user"};return null===(n=r.videoTrack)||void 0===n||n.restartTrack(i),i.facingMode}this.emit("errorPush",{type:"flipCamera",message:"请先加入房间"})}},{key:"changeCameraStatus",value:(f=o(n().mark((function e(){var t;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.room||!this.room.localParticipant){e.next=12;break}if(!(t=this.room.localParticipant).isCameraEnabled){e.next=7;break}return e.next=5,t.setCameraEnabled(!1);case 5:e.next=9;break;case 7:return e.next=9,t.setCameraEnabled(!0);case 9:this.emit("localCameraChange",t.isCameraEnabled),e.next=13;break;case 12:this.emit("errorPush",{type:"changeCameraStatus",message:"请先加入房间"});case 13:case"end":return e.stop()}}),e,this)}))),function(){return f.apply(this,arguments)})},{key:"changeMicrophoneStatus",value:(h=o(n().mark((function e(){var t;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.room||!this.room.localParticipant){e.next=12;break}if(!(t=this.room.localParticipant).isMicrophoneEnabled){e.next=7;break}return e.next=5,t.setMicrophoneEnabled(!1);case 5:e.next=9;break;case 7:return e.next=9,t.setMicrophoneEnabled(!0);case 9:this.emit("localMicrophoneChange",t.isMicrophoneEnabled),e.next=13;break;case 12:this.emit("errorPush",{type:"changeMicrophoneStatus",message:"请先加入房间"});case 13:case"end":return e.stop()}}),e,this)}))),function(){return h.apply(this,arguments)})},{key:"changeScreenShareStatus",value:(d=o(n().mark((function e(){var t,r;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.room||!this.room.localParticipant){e.next=9;break}return t=this.room.localParticipant,r=t.isScreenShareEnabled,e.next=5,t.setScreenShareEnabled(!r,{audio:!0});case 5:r||x(this,hs,ws).call(this,"share"),this.emit("localScreenShareChange",t.isScreenShareEnabled),e.next=10;break;case 9:this.emit("errorPush",{type:"changeScreenShareStatus",message:"请先加入房间"});case 10:case"end":return e.stop()}}),e,this)}))),function(){return d.apply(this,arguments)})},{key:"changeParticipantMicrophoneStatus",value:(c=o(n().mark((function e(t,r){var i,a,o,s,c,u=this;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=3;break}return this.emit("errorPush",{type:"changeParticipantMicrophoneStatus",message:"请输入要查询的identity"}),e.abrupt("return");case 3:if(i=this.room.getParticipantByIdentity(t)){e.next=7;break}return this.emit("errorPush",{type:"changeParticipantMicrophoneStatus",message:"该参与者不存在"}),e.abrupt("return");case 7:if(!i.isLocal){e.next=13;break}return e.next=10,i.setMicrophoneEnabled(!r);case 10:this.emit("localMicrophoneChange",this.room.localParticipant.isMicrophoneEnabled),e.next=19;break;case 13:if(a=i.getTrack(Mi.Source.Microphone),o=a?a.trackSid:void 0){e.next=18;break}return this.emit("errorPush",{type:"changeParticipantMicrophoneStatus",message:"trackId不存在"}),e.abrupt("return");case 18:r?(s={cmd:"Mute",data:{org:this.org,room:this.roomNum,input:"1",identity:t,trackId:o}},this.socket.addMessageHandler("Mute",(function(e){var t,n,r=JSON.parse(e);"Mute"===r.cmd&&("200"==(null==r?void 0:r.data.code)||u.emit("errorPush",{type:"changeParticipantMicrophoneStatus",message:null!==(t=null===(n=r.data)||void 0===n?void 0:n.msg)&&void 0!==t?t:"关闭指定参与者麦克风失败"}))})),this.socket.send(s)):(c={cmd:"UnMute",data:{org:this.org,room:this.roomNum,input:"1",identity:t,trackId:o}},this.socket.addMessageHandler("UnMute",(function(e){var t,n,r,i=JSON.parse(e);"UnMute"===i.cmd&&("200"==(null==i?void 0:i.data.code)||null!==(t=i.data)&&void 0!==t&&t.msg&&u.emit("errorPush",{type:"changeParticipantMicrophoneStatus",message:null!==(n=null===(r=i.data)||void 0===r?void 0:r.msg)&&void 0!==n?n:"请求开启指定参与者麦克风失败"}))})),this.socket.send(c));case 19:case"end":return e.stop()}}),e,this)}))),function(e,t){return c.apply(this,arguments)})},{key:"changeParticipantCameraStatus",value:(a=o(n().mark((function e(t,r){var i,a,o,s,c,u=this;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=3;break}return this.emit("errorPush",{type:"changeParticipantCameraStatus",message:"请输入要查询的identity"}),e.abrupt("return");case 3:if(i=this.room.getParticipantByIdentity(t)){e.next=7;break}return this.emit("errorPush",{type:"changeParticipantCameraStatus",message:"该参与者不存在"}),e.abrupt("return");case 7:if(!i.isLocal){e.next=13;break}return e.next=10,i.setCameraEnabled(!r);case 10:this.emit("localCameraChange",this.room.localParticipant.isCameraEnabled),e.next=19;break;case 13:if(a=i.getTrack(Mi.Source.Camera),o=a?a.trackSid:void 0){e.next=18;break}return this.emit("errorPush",{type:"changeParticipantCameraStatus",message:"trackId不存在"}),e.abrupt("return");case 18:r?(s={cmd:"Mute",data:{org:this.org,room:this.roomNum,identity:t,trackId:o,input:"2"}},this.socket.addMessageHandler("Mute",(function(e){var t,n,r=JSON.parse(e);"Mute"===r.cmd&&("200"==(null==r?void 0:r.data.code)||u.emit("errorPush",{type:"changeParticipantCameraStatus",message:null!==(t=null===(n=r.data)||void 0===n?void 0:n.msg)&&void 0!==t?t:"关闭指定参与者摄像头失败"}))})),this.socket.send(s)):(c={cmd:"UnMute",data:{org:this.org,room:this.roomNum,identity:t,trackId:o,input:"2"}},this.socket.addMessageHandler("UnMute",(function(e){var t,n,r,i=JSON.parse(e);"UnMute"===i.cmd&&("200"==(null==i?void 0:i.data.code)||null!==(t=i.data)&&void 0!==t&&t.msg&&u.emit("errorPush",{type:"changeParticipantCameraStatus",message:null!==(n=null===(r=i.data)||void 0===r?void 0:r.msg)&&void 0!==n?n:"请求打开指定参与者摄像头失败"}))})),this.socket.send(c));case 19:case"end":return e.stop()}}),e,this)}))),function(e,t){return a.apply(this,arguments)})},{key:"startTrackAttach",value:function(e){var t={};if(t.name=e.name?e.name:e.identity,t.roomMode=this.roomMode,t.identity=e.identity,t.audioLevel=e.audioLevel,t.isSpeaking=e.isSpeaking,t.metadata=e.metadata,t.sid=e.sid,t.isLocal=e.isLocal,t.isMicrophoneEnabled=e.isMicrophoneEnabled,"auto"===this.roomMode){t.isCameraEnabled=e.isCameraEnabled,t.isScreenShareEnabled=e.isScreenShareEnabled;var n=e.getTrack(Mi.Source.Camera),r=e.getTrack(Mi.Source.Microphone);e.isScreenShareEnabled&&(n=e.getTrack(Mi.Source.ScreenShare),r=e.getTrack(Mi.Source.ScreenShareAudio)),t.videoTrack=n?n.videoTrack:void 0,t.audioTrack=r?r.audioTrack:void 0,this.emit("trackAttach",t)}else{var i=e.getTrack(Mi.Source.Microphone);t.audioTrack=i?i.audioTrack:void 0,this.emit("trackAttach",t)}}},{key:"renderParticipant",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];"auto"===this.roomMode?this.renderToVideo(e,t):"audio"===this.roomMode&&this.renderToAudio(e,t)}},{key:"renderToVideo",value:function(e,t){var n={},r=e.name,i=e.identity,a=e.audioLevel,o=e.isSpeaking,s=e.metadata,c=e.sid,u=e.isCameraEnabled,d=e.isLocal,l=e.isMicrophoneEnabled,h=e.isScreenShareEnabled;n.remove=t,n.name=r||i,n.roomMode=this.roomMode,n.identity=i,n.audioLevel=a,n.isSpeaking=o,n.metadata=s,n.sid=c,n.isCameraEnabled=u,n.isLocal=d,n.isMicrophoneEnabled=l,n.isScreenShareEnabled=h;var f=e.getTrack(Mi.Source.Camera),p=e.getTrack(Mi.Source.Microphone);h&&(f=e.getTrack(Mi.Source.ScreenShare),p=e.getTrack(Mi.Source.ScreenShareAudio)),n.videoTrack=f?f.videoTrack:void 0,n.audioTrack=p?p.audioTrack:void 0,this.emit("videoCallRender",n)}},{key:"renderToAudio",value:function(e,t){var n={},r=e.name,i=e.identity,a=e.audioLevel,o=e.isSpeaking,s=e.metadata,c=e.sid,u=e.isLocal,d=e.isMicrophoneEnabled;n.remove=t,n.name=r||i,n.roomMode=this.roomMode,n.identity=i,n.audioLevel=a,n.isSpeaking=o,n.metadata=s,n.sid=c,n.isLocal=u,n.isMicrophoneEnabled=d;var l=e.getTrack(Mi.Source.Microphone);n.audioTrack=l?l.audioTrack:void 0,this.emit("audioCallRender",n)}},{key:"renderScreenShare",value:function(){var e,t,n={},r=this.room.localParticipant.getTrack(Mi.Source.ScreenShare);r?e=this.room.localParticipant:this.room.participants.forEach((function(n){if(!r){var i=n.getTrack(Mi.Source.ScreenShare);null!=i&&i.isSubscribed&&(r=i,e=n);var a=n.getTrack(Mi.Source.ScreenShareAudio);null!=a&&a.isSubscribed&&(t=a)}})),r&&e?(n.audioLevel=e.audioLevel,n.identity=e.identity,n.sid=e.sid,n.isCameraEnabled=e.isCameraEnabled,n.isMicrophoneEnabled=e.isMicrophoneEnabled,n.isScreenShareEnabled=e.isScreenShareEnabled,n.videoTrack=r&&r.videoTrack,n.audioTrack=t&&t.audioTrack,this.emit("screenShareRender",n)):this.emit("screenShareUnrender")}},{key:"updateVideoSize",value:function(e,t){t.innerHTML="(".concat(e.videoWidth,"x").concat(e.videoHeight,")")}}],[{key:"getDeviceList",value:(i=o(n().mark((function e(){var t,r,i;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,ns.getLocalDevices("audioinput");case 2:return t=e.sent,e.next=5,ns.getLocalDevices("videoinput");case 5:return r=e.sent,e.next=8,ns.getLocalDevices("audiooutput");case 8:return i=e.sent,e.abrupt("return",{audioDevices:t,videoDevices:r,outputDevices:i});case 10:case"end":return e.stop()}}),e)}))),function(){return i.apply(this,arguments)})}]),r}();function Ss(){var e=this;this.socket.addMessageHandler("ResUnMute",(function(t){var n,r=JSON.parse(t);"ResUnMute"===r.cmd&&("200"==(null==r?void 0:r.data.code)?e.emit("requestDeviceOpen",r.data):e.emit("errorPush",{type:"requestDeviceOpen",message:null!==(n=null==r?void 0:r.data.msg)&&void 0!==n?n:"更改用户设备失败"}))})),this.socket.addMessageHandler("ResThirdInvite",(function(t){var n,r=JSON.parse(t);"ResInvite"===r.cmd&&"200"==(null===(n=r.data)||void 0===n?void 0:n.code)&&e.emit("receiveInviteSuccess",{roomNum:r.data.room,fromIdentity:r.data.fromIdentity,fromOrg:r.data.fromOrg,metadata:r.data.metadata})})),this.socket.addMessageHandler("ResAccept",(function(t){var n,r=JSON.parse(t);"ResAccept"===r.cmd&&"200"==(null===(n=r.data)||void 0===n?void 0:n.code)&&e.emit("receiveInviteAccept",{roomNum:r.data.room,fromIdentity:r.data.fromIdentity,fromOrg:r.data.fromOrg})})),this.socket.addMessageHandler("ResRefuse",(function(t){var n,r=JSON.parse(t);"ResRefuse"===r.cmd&&"200"==(null===(n=r.data)||void 0===n?void 0:n.code)&&e.emit("receiveInviteRefuse",{roomNum:r.data.room,fromIdentity:r.data.fromIdentity,fromOrg:r.data.fromOrg})})),this.socket.addMessageHandler("SendText",(function(t){var n,r=JSON.parse(t);"SendText"===r.cmd&&("200"==(null===(n=r.data)||void 0===n?void 0:n.code)?e.emit("sendTextSuccess",r.data.msg):e.emit("sendTextFailed",r.data.msg))})),this.socket.addMessageHandler("ResReceiveMsg",(function(t){var n,r=JSON.parse(t);"ResReceiveMsg"===r.cmd&&("200"==(null===(n=r.data)||void 0===n?void 0:n.code)&&e.emit("messageReceived",{origin:null==r?void 0:r.data.fromIdentity,content:null==r?void 0:r.data.text,fromName:null==r?void 0:r.data.fromName,fromOrg:null==r?void 0:r.data.fromOrg,room:null==r?void 0:r.data.room}))})),this.socket.addMessageHandler("ResAddHost",(function(t){var n,r=JSON.parse(t);"ResAddHost"===r.cmd&&("200"==(null===(n=r.data)||void 0===n?void 0:n.code)&&e.emit("setHostSuccess",{hostId:null==r?void 0:r.data.hostId,hostName:null==r?void 0:r.data.hostName}))}))}function ws(e){if(this.roomNum){var t={cmd:"SdkCall",data:{room:this.roomNum,sdkType:e}};this.socket.send(t)}else this.emit("errorPush",{type:"getSdkCallNum",message:"请先加入房间"})}function Ts(e){return document.getElementById(e)}function Cs(){return xs.apply(this,arguments)}function xs(){return(xs=o(n().mark((function e(){var t;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(w(this,ds)){e.next=2;break}return e.abrupt("return");case 2:if(this.baseUrl){e.next=4;break}return e.abrupt("return");case 4:return e.prev=4,e.next=7,fetch(this.baseUrl+"/api/v1/deleteDispatcher",{method:"POST",body:JSON.stringify({ruleId:w(this,ds)})});case 7:return t=e.sent,e.next=10,t.json();case 10:200!==e.sent.code&&this.emit("errorPush",{type:"dispatchInCall",message:"解除呼入绑定失败"}),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(4),this.emit("errorPush",{type:"dispatchInCall",message:"解除呼入绑定失败"});case 17:case"end":return e.stop()}}),e,this,[[4,14]])})))).apply(this,arguments)}function Ps(e){var t=this;if(e)return new Promise((function(n,r){var i={cmd:"GetToken",data:{org:t.org,room:e,identity:t.userId}};t.socket.send(i),t.socket.addMessageHandler("GetToken",(function(e){var i,a,o=JSON.parse(e);"GetToken"===o.cmd&&("200"==(null==o?void 0:o.data.code)?n({roomUrl:o.data.url,token:o.data.token}):(t.emit("errorPush",{type:"getToken",message:null!==(i=null===(a=o.data)||void 0===a?void 0:a.msg)&&void 0!==i?i:"获取会议token失败"}),r()))}))}));this.emit("errorPush",{type:"getToken",message:"未获取到房间号"})}function Es(){var e=this;this.room.on(Pi.Connected,(function(){e.emit("roomConnected")})),this.room.on(Pi.Disconnected,function(){var t=o(n().mark((function t(r){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,x(e,ys,Ds).call(e);case 2:e.emit("roomDisconnected",r);case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()),this.room.on(Pi.ParticipantConnected,(function(t){x(e,ks,Rs).call(e,t)})),this.room.on(Pi.ParticipantDisconnected,(function(t){x(e,gs,Is).call(e,t)})),this.room.on(Pi.ActiveDeviceChanged,(function(t,n){e.emit("mediaDeviceChange",{kind:t,deviceId:n})})),this.room.on(Pi.ActiveSpeakersChanged,(function(t){var n;n=t.map((function(e){var t;return{identity:e.identity,isLocal:e.isLocal,audioLevel:e.audioLevel,name:null!==(t=e.name)&&void 0!==t?t:e.identity}})),e.emit("activeSpeakerChange",n)})),this.room.on(Pi.LocalTrackPublished,(function(t){e.renderParticipant(e.room.localParticipant),e.startTrackAttach(e.room.localParticipant)})),this.room.on(Pi.LocalTrackUnpublished,(function(){e.renderParticipant(e.room.localParticipant),e.startTrackAttach(e.room.localParticipant)})),this.room.on(Pi.TrackPublished,(function(t,n){e.startTrackAttach(n)})),this.room.on(Pi.TrackUnpublished,(function(t,n){e.startTrackAttach(n)})),this.room.on(Pi.TrackSubscribed,(function(t,n,r){e.renderParticipant(r),e.startTrackAttach(r)})),this.room.on(Pi.TrackUnsubscribed,(function(t,n,r){e.renderParticipant(r),e.startTrackAttach(r)}))}function Rs(e){var t=this;this.emit("participantConnected",e),e.on(Ei.TrackMuted,(function(n){t.renderParticipant(e),t.startTrackAttach(e)})),e.on(Ei.TrackUnmuted,(function(n){t.renderParticipant(e),t.startTrackAttach(e)})),e.on(Ei.IsSpeakingChanged,(function(){t.renderParticipant(e)})),e.on(Ei.ConnectionQualityChanged,(function(){t.renderParticipant(e)})),e.on(Ei.ParticipantNameChanged,(function(n){t.renderParticipant(e)}))}function Is(e){this.renderParticipant(e,!0)}function Ds(e){return Ns.apply(this,arguments)}function Ns(){return(Ns=o(n().mark((function e(t){var r=this;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.room){e.next=2;break}return e.abrupt("return");case 2:return this.renderParticipant(this.room.localParticipant,!0),this.room.participants.forEach((function(e){r.renderParticipant(e,!0)})),e.next=7,x(this,ps,Cs).call(this);case 7:case"end":return e.stop()}}),e,this)})))).apply(this,arguments)}export{bs as default};
