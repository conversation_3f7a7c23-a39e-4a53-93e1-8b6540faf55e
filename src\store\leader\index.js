import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
	state: {
		SHZLRyllAddPointsTrigger: true,
		SHZLRyllAddPointsParam: {},
		SHZLClickPointTrigger: true,
		SHZLClickPointParam: {},
		SHZLRyllSHZLGjTrigger: true,
		SHZLXlgcAddPointsTrigger: true,
		SHZLXlgcAddPointsParam: {},
		SHZLCssjAddPointsTrigger: true,
		SHZLCssjAddPointsParam: {},
		SHZLSjxx1AddPointsTrigger: true,
		SHZLSjxx1AddPointsParam: {},
		SHZLaiAddPointsTrigger: true,
		SHZLaiAddPointsParam: {},
		removePoints_GjTrigger: true,
		rhtxState: '',
		queryOrgStreetTreeData: [],
		rightShow: true,
		rightShow2: 0,
		rightShow3: 0,
		rightShow4: false,
		rightShow5: 0,
		username: '',
		mobile: '',
		areaId: '',
		headerMainCurrentIndex: -1,
		currentIndex: 0,
		mapLoaded: true,
		messageToken: '',
	},
	getters: {
		getSHZLRyllAddParam: (state) => {
			return state.SHZLRyllAddPointsParam
		},
		getSHZLClickParam: (state) => {
			return state.SHZLClickPointParam
		},
		getSHZLXlgcAddParam: (state) => {
			return state.SHZLXlgcAddPointsParam
		},
		getSHZLCssjAddParam: (state) => {
			return state.SHZLCssjAddPointsParam
		},
		getSHZLSjxx1AddParam: (state) => {
			return state.SHZLSjxx1AddPointsParam
		},
		getSHZLaiAddParam: (state) => {
			return state.SHZLaiAddPointsParam
		},
		getHeaderMainCurrentIndex(state) {
			return state.headerMainCurrentIndex
		},
		getMapLoaded(state) {
			return state.mapLoaded
		},
		gteMessageToken(state) {
			return state.messageToken
		},
	},
	mutations: {
		setScreenUsername(state, payload) {
			state.username = payload.username
		},
		setScreenUserMobile(state, payload) {
			state.mobile = payload.mobile
		},
		setAreaId(state, payload) {
			state.areaId = payload.areaId
		},
		setCurrentIndex(state, param) {
			state.currentIndex = param
		},
		invokerightShow(state, param) {
			// console.log('param', param)
			state.rightShow = param
		},
		invokerightShow2(state, param) {
			state.rightShow2 = param
		},
		invokerightShow3(state, param) {
			console.log('param', param)
			if (param == '100000' && state.rightShow3 != 0) {
				state.rightShow3 = 0
			} else {
				state.rightShow3 = param
			}
		},
		invokerightShow4(state, param) {
			console.log('param', param)
			state.rightShow4 = param
		},
		invokerightShow5(state, param) {
			console.log('param', param)
			state.rightShow5 = param
		},
		// 【社会治理-人员力量】打点方法
		invokeSHZLRyllAddPoints(state, param) {
			// console.log('param', param)
			state.SHZLRyllAddPointsTrigger = !state.SHZLRyllAddPointsTrigger
			state.SHZLRyllAddPointsParam = param
		},
		// 【社会治理-人员力量】点击落点
		invokeSHZLClickPoint(state, param) {
			// console.log('param', param)
			state.SHZLClickPointTrigger = !state.SHZLClickPointTrigger
			state.SHZLClickPointParam = param
		},
		// 【社会治理-轨迹】轨迹巡查
		invokeSHZLGj(state) {
			state.SHZLRyllSHZLGjTrigger = !state.SHZLRyllSHZLGjTrigger
		},
		// 【社会治理-雪亮工程】打点方法
		invokeSHZLXlgcAddPoints(state, param) {
			// console.log('param', param)
			state.SHZLXlgcAddPointsTrigger = !state.SHZLXlgcAddPointsTrigger
			state.SHZLXlgcAddPointsParam = param
		},
		// 【社会治理-城市事件】打点方法
		invokeSHZLCssjAddPoints(state, param) {
			// console.log('param', param)
			state.SHZLCssjAddPointsTrigger = !state.SHZLCssjAddPointsTrigger
			state.SHZLCssjAddPointsParam = param
		},
		// 【社会治理-事件信息定位】打点方法
		invokeSHZLSjxx1AddPoints(state, param) {
			// console.log('param', param)
			state.SHZLSjxx1AddPointsTrigger = !state.SHZLSjxx1AddPointsTrigger
			state.SHZLSjxx1AddPointsParam = param
		},
		// 【社会治理-ai定位】打点方法
		invokeSHZLaiAddPoints(state, param) {
			// console.log('param', param)
			state.SHZLaiAddPointsTrigger = !state.SHZLaiAddPointsTrigger
			state.SHZLaiAddPointsParam = param
		},
		// 【点位轨迹移除】
		invokeRemovePoints_Gj(state) {
			state.removePoints_GjTrigger = !state.removePoints_GjTrigger
		},
		//  融合通信
		invokeRHtx(state, param) {
			state.rhtxState = param
		},
		//  区 视频会商成员
		setQueryOrgStreetTreeData(state, param) {
			console.log('param', param)
			state.queryOrgStreetTreeData = param
		},
		//切换header当前模块
		setHeaderMainCurrentIndex(state, index) {
			state.headerMainCurrentIndex = index
		},
		updateMapLoaded(state, value) {
			state.mapLoaded = value
		},
		unpdateMessageToken(state, value) {
			state.messageToken = value
		},
	},
	actions: {},
	modules: {},
})
