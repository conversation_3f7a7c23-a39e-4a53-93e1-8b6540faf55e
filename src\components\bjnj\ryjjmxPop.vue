<!-- 站内资源 -->
<template>
	<div class="ryjjmx-dialog" v-if="show">
		<div class="ai_waring">
			
			<div class="title">
				<span>{{ title }}</span>
				<i class="el-icon-close close_btn" @click="closeEmitai"></i>
			</div>
			<div class="content">
				<div class="tableBox">
					<div
						class="tableItem"
						:class="tableShow == index ? 'tableActive' : ''"
						v-for="(item, index) of btnList"
						:key="index"
						@click="tableswitch(index)"
					>
						<span>{{ item.name }}</span>
					</div>
				</div>
				<div v-if="tableShow == 0">
					<div class="check_types">
						<div class="jjcd">
							<span>姓名：</span>
							<el-input v-model="gdObj.name" placeholder="请输入成员姓名"></el-input>
						</div>
						<!-- <div class="jjcd">
							<span>联系方式：</span>
							<el-input v-model="gdObj.phone" placeholder="请输入联系方式"></el-input>
						</div> -->
						<!-- <div class="jjcd">
							<span>所属区域：</span>
							<el-cascader
								v-model="gdObj.areaId"
								:options="options"
								:props="{ value: 'areaId', label: 'areaName', children: 'sonArea', checkStrictly: true, emitPath: false }"
								@change="handleChange"
							>
							</el-cascader>
						</div> -->

						<div class="jjcd">
							<span>职务：</span>
							<el-input v-model="gdObj.duty" placeholder="请输入职务名称"></el-input>
						</div>

						<div class="type_btns">
							<div @click="searchBtn">查询</div>
							<div @click="resetBtn">重置</div>
						</div>
					</div>
					<div class="table_box">
						<SwiperTableMap
							:titles="['序号', '成员名称', '联系方式', '职务']"
							:widths="['10%', '26%', '30%', '34%']"
							:data="tableList1"
							:contentHeight="'100%'"
							:settled="false"
							:isNeedOperate="false"
							@operate="operate"
						></SwiperTableMap>
					</div>
				</div>
				<div v-if="tableShow == 1">
					<div class="check_types">
						<!-- <div class="jjcd">
							<span>农机编号：</span>
							<el-input v-model="gdObj.agmachId" placeholder="请输入农机编号"></el-input>
						</div> -->
						<!-- <div class="jjcd">
              <span>车牌号：</span>
              <el-input v-model="gdObj.licensePlateCode" placeholder="请输入车牌号"></el-input>
            </div> -->
						<div class="jjcd">
							<!-- <span>农机品目：</span>
							<Select v-model="gdObj.sqlxValue">
								<Option v-for="item in qgdCodesqlxOptions" :value="item.dicValue" :key="item.dicValue">
									{{ item.dicLabel }}
								</Option>
							</Select> -->
							<span>农机品目：</span>
							<el-select v-model="gdObj.agmachTypeId" placeholder="请选择农机品目">
								<el-option v-for="item in agmachTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
							</el-select>
						</div>
						<div class="type_btns">
							<div @click="searchBtn">查询</div>
							<div @click="resetBtn">重置</div>
						</div>
					</div>
					<div class="table_box">
						<!-- <SwiperTableMap
              :titles="[
                '序号',
                '农机品目',
                '型号',
                '车牌号',
                '联系人',
                '农机位置',
              ]"
              :widths="['6%', '20%', '20%', '22%','22%', '14%']"
              :data="tableList2"
              :contentHeight="'520px'"
              :settled="settled"
              @operate="operate"
            ></SwiperTableMap> -->
						<SwiperTableMap
							:titles="['序号', '农机品目', '型号', '车牌号', '联系人']"
							:widths="['6%', '24%', '24%', '25%', '25%']"
							:data="tableList2"
							:contentHeight="'100%'"
							:isNeedOperate="false"
							@operate="operate"
						></SwiperTableMap>
					</div>
				</div>
				<!-- <div v-if="tableShow == 2">
          <div class="check_types">
            <div class="jjcd">
              <span>站点名称：</span>
              <el-input v-model="gdObj.jjcdValue" placeholder="请输入站点名称"></el-input>
            </div>
            <div class="jjcd">
              <span>所属区域：</span>
              <Select v-model="gdObj.sqlxValue">
                <Option
                  v-for="item in qgdCodesqlxOptions"
                  :value="item.dicValue"
                  :key="item.dicValue"
                >
                  {{
                  item.dicLabel
                  }}
                </Option>
              </Select>
            </div>
            <div class="type_btns">
              <div @click="searchBtn">查询</div>
              <div @click="resetBtn">重置</div>
            </div>
          </div>
          <div class="table_box">
            <SwiperTableMap
              :titles="[
                '序号',
                '农机编号',
                '农机品目',
                '型号',
                '车牌号',
                '状态',
              ]"
              :widths="['6%', '16%', '12%', '16%', '24%', '26%']"
              :data="tableList3"
              :contentHeight="'520px'"
              :settled="settled"
              :isNeedOperate="false"
              @operate="operate"
            ></SwiperTableMap>
          </div>
          <div class="fy_page">
            <Page :total="total" @on-change="pageNumChange" show-total></Page>
          </div>
        </div>
        <div v-if="tableShow == 3">
          <div class="check_types">
            <div class="jjcd">
              <span>物资名称：</span>
              <el-input v-model="gdObj.jjcdValue" placeholder="请输入物资名称"></el-input>
            </div>
            <div class="jjcd">
              <span>所属区域：</span>
              <Select v-model="gdObj.sqlxValue">
                <Option
                  v-for="item in qgdCodesqlxOptions"
                  :value="item.dicValue"
                  :key="item.dicValue"
                >
                  {{
                  item.dicLabel
                  }}
                </Option>
              </Select>
            </div>
            <div class="type_btns">
              <div @click="searchBtn">查询</div>
              <div @click="resetBtn">重置</div>
            </div>
          </div>
          <div class="table_box">
            <SwiperTableMap
              :titles="[
                '序号',
                '物资名称',
                '物资类型',
                '物资数量',
                '计量单位',
                '所属单位',
                '现状态',
                '物资照片',
              ]"
              :widths="['10%', '15%', '10%', '10%', '10%', '15%', '15%', '15%']"
              :data="tableList4"
              :contentHeight="'480px'"
              :settled="settled"
              @operate="operate"
            ></SwiperTableMap>
          </div>
          <div class="fy_page">
            <Page :total="total" @on-change="pageNumChange" show-total></Page>
          </div>
        </div>-->
			</div>
			<div class="fy_page">
				<!-- <Page :total="total" :page-size="pageSize" v-model="pageNum" @on-change="pageNumChange" show-total></Page> -->
				<el-pagination
					@current-change="pageNumChange"
					:current-page="pageNum"
					:page-size="this.pageSize"
					layout="total, prev, pager, next, jumper"
					:total="this.total"
				>
				</el-pagination>
			</div>
		</div>
	</div>
</template>

<script>
import SwiperTableMap from './table/RealTimeEventTable8.vue'
// api
import { getEventType, getUnDoneEventList, getDoneEventList, getQgdSssj } from '@/api/hs/hs.api.js'
import { myMixins } from '@/libs/myMixins.js'
import { queryPageByTeamId, queryPageById } from '@/api/njzl/hs.api.js'
import { queryPageTEmergencyCenterByCondition, areaTree, queryPageTDryingCenterByCondition } from '@/api/bjnj/zhdd.js'
import { getCscpBasicHxItemCode } from '@/api/bjnj/zhdd.js'
export default {
	name: 'znzyPop',
	mixins: [myMixins],
	components: { SwiperTableMap },
	props: {
		value: {
			type: Boolean,
			default: false,
		},
		list: {
			type: Array,
			default: () => {
				return []
			},
		},
		basicInfomationId: {
			type: String,
			default: '',
		},
		data: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	data() {
		return {
			title: '人员机具信息',
			options: [],
			btnList: [
				{
					name: '人员信息',
				},
				{
					name: '机具信息',
				},
			],
			tableShow: 0,
			tabs: ['未办结', '办结'],
			btnsArr: [
				{
					name: '未办结',
					normalBg: require('@/assets/shzl/map/left_n.png'),
					activeBg: require('@/assets/shzl/map/left_a.png'),
				},
				{
					name: '已办结',
					normalBg: require('@/assets/shzl/map/right_n.png'),
					activeBg: require('@/assets/shzl/map/right_a.png'),
				},
			],
			urgentOptions: [
				{
					value: '0',
					label: '普通',
				},
				{
					value: '1',
					label: '突发',
				},
				{
					value: '2',
					label: '重要',
				},
				{
					value: '3',
					label: '紧急',
				},
			],
			eventTypeOptions: [
				{
					value: '0',
					label: '普通',
				},
			],
			timeOutOptions: [
				{
					value: '0',
					label: '是',
				},
				{
					value: '1',
					label: '否',
				},
			],
			proityValue: '',
			timeOutValue: '',
			settled: true,
			total: 0,
			pageNum: 1,
			pageSize: 8,
			finished: 1,
			tableList1: [],
			tableList2: [],
			tableList3: [],
			tableList4: [],
			gdObj: {},
			agmachTypeOptions: [],
		}
	},
	watch: {
		// qgdCodejjcdOptions (val) {
		//   if (this.qgdCodejjcdOptions.length > 0) {
		//     // this.getQgdSssj()
		//   }
		// },
		// 防止出现获取不到annexNum
		// userAreaId: {
		// 	immediate: true,
		// 	handler(annexNum) {
		// 		// console.log('annexNum', annexNum)
		// 		this.userAreaId = annexNum + ''
		// 		this.areaTree()
		// 		// if (annexNum) {
		// 		//   this.queryAnnexByAnnexNum();
		// 		// }
		// 	},
		// },
		basicInfomationId() {
			this.getData()
		},
		value: {
			handler(nv, ov) {
				if (nv) {
					if (this.tableShow == 0) {
						this.queryPageByTeamId()
					}
				}
			},
		},
	},
	computed: {
		show() {
			return this.value
		},
	},
	mounted() {
		// this.areaTree()
		// this.queryPageTEmergencyCenterByCondition({
		//   area: this.gdObj.area,
		//   current: this.pageNum,
		//   name: this.gdObj.name,
		//   size: this.pageSize,
		//   type: this.tableShow + 1,
		// })
		this.tableswitch(0)
	},
	methods: {
		async getCscpBasicHxItemCode1(data) {
			let res = await getCscpBasicHxItemCode(data)
			if (res?.code == '0') {
				if (data == 'agmachTypeId') {
					this.agmachTypeOptions = res.data.map((item) => {
						return {
							value: item.itemCode,
							label: item.itemValue,
						}
					})
				}
			}
		},

		async queryPageByTeamId() {
			let params = {
				name: this.gdObj.name,
				phone: this.gdObj.phone,
				size: this.pageSize,
				current: this.pageNum,
				teamId: this.basicInfomationId,
				areaId: this.gdObj.areaId,
				duty: this.gdObj.duty,
			}
			let res = await queryPageByTeamId(params)
			this.tableList1 = []
			this.tableList1 = [].concat(
				res.data.records.map((it, index) => [this.pageSize * (this.pageNum - 1) + index + 1, it.name, it.phone, it.duty]),
			)
			this.total = res.data.total
		},
		async queryPageById() {
			let params = {
				size: this.pageSize,
				current: this.pageNum,
				agmachId: this.gdObj.agmachId,
				licensePlateCode: this.gdObj.licensePlateCode,
				teamId: this.basicInfomationId,
				agmachTypeId: this.gdObj.agmachTypeId,
			}
			let res = await queryPageById(params)
			// if (this.tableShow == 0) {
			this.tableList2 = []
			this.tableList2 = [].concat(
				res.data.records.map((it, index) => [
					this.pageSize * (this.pageNum - 1) + index + 1,
					it.agmachType,
					it.mdlName,
					it.licensePlateCode,
					it.contact + '-' + it.phone,
				]),
			)
			this.total = res.data.total
			// } else if (this.tableShow == 1) {
			//   this.tableList2 = []
			//   this.tableList2 = [].concat(
			//     res.data.records.map((it, index) => [this.pageSize * (this.pageNum - 1) + index + 1, it.name, it.contact, it.phone, it.address, it.service])
			//   )
			// }
		},
		async areaTree(data) {
			// let res = await areaTree(data)
			// console.log(res)
			// this.options = res

			let res = await areaTree(data)
			const fullAreaTree = [res]

			// 根据用户区域信息，在区域树中查找子树
			const getUserAreaTree = (areaList) => {
				if (!areaList) return []
				const userArea = areaList.find((area) => area.areaId === this.data.regionCode)
				if (userArea) return [userArea]
				return areaList.flatMap((area) => getUserAreaTree(area.sonArea))
			}
			const userAreaTree = getUserAreaTree(fullAreaTree)

			// 处理区域树数据
			const parseAreaNode = (area) => {
				area.areaName = area.areaName.trim()
				if (area.sonArea) {
					area.sonArea.forEach(parseAreaNode)
				}
			}
			userAreaTree.forEach(parseAreaNode)
			this.options = userAreaTree
		},
		async queryPageTEmergencyCenterByCondition(data) {
			let res = await queryPageTEmergencyCenterByCondition(data)
			if (this.tableShow == 0) {
				this.tableList1 = []
				this.tableList1 = [].concat(
					res.data.records.map((it, index) => [
						this.pageSize * (this.pageNum - 1) + index + 1,
						it.name,
						it.contact,
						it.phone,
						it.address,
						it.service,
					]),
				)
			} else if (this.tableShow == 1) {
				this.tableList2 = []
				this.tableList2 = [].concat(
					res.data.records.map((it, index) => [
						this.pageSize * (this.pageNum - 1) + index + 1,
						it.name,
						it.contact,
						it.phone,
						it.address,
						it.service,
					]),
				)
			}
		},
		async queryPageTDryingCenterByCondition(data) {
			let res = await queryPageTDryingCenterByCondition(data)
			console.log(res)
			this.tableList3 = []
			this.tableList3 = [].concat(
				res.data.records.map((it, index) => [
					this.pageSize * (this.pageNum - 1) + index + 1,
					it.name,
					it.type,
					it.talent,
					it.contact,
					it.phone,
					it.address,
					it.status,
				]),
			)
		},
		handleChange(value) {
			console.log(value)
		},
		tableswitch(index) {
			this.pageNum = 1
			this.total = 0
			this.tableShow = index
			if (this.tableShow == 0) {
				this.areaTree()
			} else if (this.tableShow == 1) {
				this.getCscpBasicHxItemCode1('agmachTypeId')
			}
			this.getData()
		},
		getData() {
			if (this.tableShow == 0) {
				this.queryPageByTeamId()
			} else {
				this.queryPageById()
			}
		},
		operate(i, id, it) {
			this.$emit('operate', i, id, it)
		},
		fhEmitai() {
			this.$emit('fhEmitai', false)
		},
		closeEmitai() {
			this.$emit('closeEmitai', false)
		},
		pageNumChange(val) {
			this.pageNum = val
			this.getData()
		},
		searchBtn() {
			this.getData()
		},
		resetBtn() {
			this.pageNum = 1
			this.gdObj = {}
			this.getData()
		},
	},
}
</script>

<style lang="less" scoped>
.bg-header {
	position: absolute;
	width: 100%;
	height: 1080px;
	background: rgba(2, 14, 50, 0.75);
	z-index: 999999;
	top: 0;
	left: 0;
}
/deep/ .ivu-select {
	width: 189px;
}
/deep/ .ivu-select-selection {
	width: 189px;
	height: 34px;
	background: rgba(0, 74, 143, 0.4) linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
	border: 1px solid rgba(0, 162, 255, 0.6);
}
/deep/ .ivu-select-selection .ivu-select-selected-value {
	font-size: 13px;
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	color: #ccf4ff;
	line-height: 34px;
}

/deep/ .ivu-input {
	background: rgba(0, 74, 143, 0.4) linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
	border: 1px solid rgba(0, 162, 255, 0.6);
	font-size: 16px;
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	color: #ccf4ff;
	line-height: 22px;
}
/deep/ .ivu-select-placeholder {
	height: 34px !important;
	font-size: 13px !important;
	line-height: 34px !important;
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	color: #ccf4ff !important;
}
/deep/ .ivu-page-item {
	background: transparent;
	color: #fff;
}
/deep/ .ivu-page-item a {
	color: #fff;
}
/deep/ .ivu-page-prev,
/deep/ .ivu-page-next {
	background: transparent;
}
/deep/ .el-input__inner {
	color: #ccf4ff;
}

.ryjjmx-dialog {
	width: 1920px;
	height: 1080px;
	position: absolute;
	left: 0;
	top: 0;
	background-color: rgba(0, 0, 0, 0.3);
	z-index: 1106;
	display: flex;
	justify-content: center;
	align-items: center;
}

.ai_waring {
	width: 1367px;
	height: 690px;
	background: #001c40;
	position: relative;

	border: 1px solid #023164;
	z-index: 1102;
	.title {
		width: 100%;
		height: 48px;
		line-height: 48px;
		// background: url(~@/assets/map/dialog/title7.png) no-repeat;

		padding: 0 16px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background: #104993;
		// padding-left: 60px;
		span {
			font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
			font-weight: normal;
			font-size: 16px;
			color: #d0deee;
			line-height: 16px;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}
		.close_btn {
			font-size: 14px;
			color: #80caff;
			cursor: pointer;
		}
	}
	.fh_btn {
		width: 39px;
		height: 39px;
		position: absolute;
		top: 5px;
		left: 5px;
		cursor: pointer;
		img {
			width: 39px;
			height: 39px;
		}
	}

	.content {
		padding: 26px 16px 0 16px;

		.btns {
			// border: 1px solid red;
			margin: 0 auto;
			display: flex;
			justify-content: center;
			& > div {
				width: 154px;
				height: 53px;
				span {
					font-size: 24px;
					font-family: PingFangSC, PingFang SC;
					color: #ffffff;
					line-height: 53px;
					background: linear-gradient(180deg, #ffffff 0%, #ffeba0 100%);
					background-clip: text;
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
				}
			}
		}
		.check_types {
			text-align: left;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			.jjcd {
				width: 256px;
				height: 32px;
				display: flex;
				align-items: center;
				margin-right: 24px;

				font-size: 14px;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				line-height: 14px;
				color: #ffffff;
				span {
					width: 70px;
					font-size: 14px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					line-height: 14px;
					color: #ffffff;
					margin-right: 5px;
				}
				/deep/ .el-input {
					width: 181px;
					height: 32px;
					.el-input__inner {
						width: 181px !important;
						height: 32px !important;
						// background: linear-gradient(180deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%),
						// 	rgba(0, 74, 143, 0.4);
						background-color: #001c40 !important;
						border: 1px solid #d9d9d9 !important;
						border-radius: 4px 4px 4px 4px !important;

						font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
						font-weight: normal;
						font-size: 14px !important;
						color: #6c8097 !important;
						line-height: 14px;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}
				}

				/deep/ .el-select {
					width: 181px !important;
					height: 32px !important;
				}
			}

			.type_btns {
				display: flex;
				align-items: center;

				& div {
					width: 60px;
					height: 32px;
					font-size: 16px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					color: #ffffff;
					line-height: 32px;
					border-radius: 2px 2px 2px 2px;
					border: 1px solid #d0deee;
					display: flex;
					justify-content: center;
					align-items: center;
					font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
					font-weight: normal;
					font-size: 14px;
					line-height: 14px;
					text-align: center;
					font-style: normal;
					text-transform: none;
					margin: 0 4px;
					cursor: pointer;
				}

				& div:first-child {
					background-color: #001c40;
					color: #d0deee;
				}

				& div:last-child {
					background-color: #159aff;
					color: #d0deee;
				}
			}
			.report_time {
				margin-left: 26px;
				display: flex;
				align-items: center;
				span {
					font-size: 16px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					color: #ffffff;
					line-height: 22px;
				}
			}
			.istime_out {
				margin-left: 26px;
				align-items: center;
				display: flex;
				span {
					font-size: 16px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					color: #ffffff;
					line-height: 22px;
				}
			}
		}
		.table_box {
			margin-top: 18px;
			height: 425px;
			overflow-y: auto;

			&::-webkit-scrollbar {
				width: 4px; /* 滚动条宽度 */
			}

			//轨道
			&::-webkit-scrollbar-track {
				background: #04244e; /* 滚动条轨道背景 */
				border-radius: 10px;
				padding: 5px 0;
			}

			//滑块
			&::-webkit-scrollbar-thumb {
				background: #82aed0; /* 滑块背景颜色 */
				border-radius: 10px;
				height: 15px;
			}

			//悬停滑块颜色
			&::-webkit-scrollbar-thumb:hover {
				background: #ff823b; /* 鼠标悬停时滑块颜色 */
			}

			//箭头
			&::-webkit-scrollbar-button {
				display: none; /* 隐藏滚动条的箭头按钮 */
			}

			.czBtn {
				display: inline-block;
				button {
					margin: 0 8px !important;
				}
			}

			/deep/ .table-header {
				// position: sticky;
				// top: -1px; /* 根据布局调整 */
				// z-index: 10;
				// background-color: #001C40;
			}

			/deep/ .el-table th.el-table__cell {
				background-color: rgba(0, 0, 0, 0);
			}
			/deep/ .el-table tr {
				background-color: rgba(0, 0, 0, 0);
			}
			/deep/ .el-table td.el-table__cell,
			/deep/ .el-table th.el-table__cell.is-leaf {
				// border-bottom: none;

				border-bottom: 1px solid #263e5d !important;
			}
			/deep/ .el-table,
			/deep/ .el-table__expanded-cell {
				background-color: rgba(0, 0, 0, 0);
			}
			/deep/ .el-table__header-wrapper {
				background-color: #023164;
				// border-bottom: 1px solid rgba(255, 255, 255, 0.15);
			}
			/deep/ .el-table th.el-table__cell.is-leaf {
				// border-right: 1px solid #2968c7;
			}
			/deep/ .el-table th.el-table__cell > .cell {
				text-align: left;

				font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
				font-weight: normal;
				font-size: 14px;
				color: #159aff;
				line-height: 14px;
				font-style: normal;
				text-transform: none;
			}
			/deep/ .el-table .cell {
				text-align: left;
				-webkit-user-select: text;
				-moz-user-select: text;
				-ms-user-select: text;
				user-select: text;
			}

			/deep/ .el-table .el-checkbox__inner {
				border: 1px solid rgba(0, 162, 255, 0.6) !important;
				background-color: #001c40 !important;
			}

			/deep/ .el-table__row {
			}
			/deep/ .el-table__row:nth-child(odd) {
			}
			/deep/ .el-table__body tr.hover-row > td.el-table__cell {
				background-color: rgba(0, 0, 0, 0);
			}

			/deep/ .el-table__body-wrapper {
				height: calc(100% - 48px);

				overflow: auto;
				&::-webkit-scrollbar {
					width: 4px; /* 滚动条宽度 */
					height: 4px;
				}

				//轨道
				&::-webkit-scrollbar-track {
					background: #04244e; /* 滚动条轨道背景 */
					border-radius: 10px;
					padding: 5px 0;
				}

				//滑块
				&::-webkit-scrollbar-thumb {
					background: #82aed0; /* 滑块背景颜色 */
					border-radius: 10px;
					height: 15px;
				}

				//悬停滑块颜色
				&::-webkit-scrollbar-thumb:hover {
					background: #ff823b; /* 鼠标悬停时滑块颜色 */
				}

				//箭头
				&::-webkit-scrollbar-button {
					display: none; /* 隐藏滚动条的箭头按钮 */
				}
			}
		}
		
		.tableBox {
			height: 41px;
			margin-bottom: 21px;
			.tableItem {
				float: left;
				width: 152px;
				height: 41px;
				background: url(~@/assets/map/dialog/btn5.png) no-repeat center / 100% 100%;
				margin-right: 10px;
				font-family: PingFangSC, PingFang SC;
				font-size: 22px;
				color: #ffffff;
				line-height: 41px;
				text-align: center;
				font-style: normal;
				text-transform: none;
				span {
					background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
					background-clip: text;
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
				}
			}
			.tableActive {
				background: url(~@/assets/map/dialog/btn4.png) no-repeat center / 100% 100%;
				span {
					font-size: 22px;
					color: #ffffff;
					font-weight: 600;
					background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
					background-clip: text;
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
				}
			}
		}
	}

	.fy_page {
			margin-top: 24px;
			display: flex;
			justify-content: flex-end;
			align-items: center;

			/deep/ .el-pager {
				margin: 0 8px;

				background: #001c40;
				li {
					// width: 32px;
					height: 32px;
					border-radius: 2px;
					padding: 10px !important;
					line-height: 13px !important;
					&.active {
						color: #ffffff;
						background: #159aff;
						border-radius: 2px 2px 2px 2px;
					}
				}

				.number,
				.more {
					// width: 32px;
					height: 32px;
					color: #ffffff;
					padding: 10px 12px !important;
					border-radius: 2px 2px 2px 2px;
					border: 1px solid #dcdee2;
					background-color: #001c40;

					&:hover {
						color: #ffffff;
						background: #159aff;
					}
					&:before {
						line-height: 14px;
					}
				}
			}

			/deep/ .el-pagination {
				display: flex;
				justify-content: flex-start;
				align-items: center;
			}

			/deep/ .el-pagination span {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #d0deee;
				text-align: left;
				font-style: normal;
				text-transform: none;
				&:not {
				}
			}

			/deep/ .el-pagination button {
				width: 32px;
				height: 32px;
				font-family: Helvetica Neue, Helvetica Neue;
				font-weight: 400;
				font-size: 14px;
				background: #001c40;
				color: #d0deee;
				line-height: 22px;
				text-align: center;
				font-style: normal;
				text-transform: none;
				border: 1px solid #dcdee2;
				border-radius: 2px;
				padding: 10px 8px !important;
			}

			/deep/ .el-pagination__jump {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #d0deee;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}
			/deep/ .el-pagination__editor.el-input {
				width: 48px;
				margin: 0 8px;
			}

			/deep/ .el-input__inner {
				border-radius: 2px 2px 2px 2px;
				border: 1px solid #dcdee2;
				background-color: #001c40;
				color: #d0deee;
			}
		}

}
// /deep/ .ivu-page-total {
//   font-size: 18px;
//   color: #fff;
// }
</style>
