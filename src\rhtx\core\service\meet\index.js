import { tplReplace } from '../../util';
import Meet from '../../operate/meet/index';
import { useMeetProcess } from './process';
import Render from './render';
import { meetInviteTip } from './tpl';
import { MEET_TYPE, MEET_TYPE_TEXT_MAP } from '../../dict/index';

export default class MeetService extends Meet {
  constructor({ userInfo, ws }) {
    super({ userInfo, ws });
    this.render = null;
    this.mountedId = null;
    this.process = useMeetProcess.call(this);
    this.isOpen = false;
    this.meetSession = {};
    this.meetlistObj = {};
    this.initWsNotice();
    this.initMeetList();
  }

  isMine(phone) {
    return this.userInfo.user.phone === phone;
  }

  async initMeetList() {
    const videoList = await this.api.meetList({
      user_id: this.userInfo.user.id,
      type: MEET_TYPE.MEET_VIDEO
    });
    const audioList = await this.api.meetList({
      user_id: this.userInfo.user.id,
      type: MEET_TYPE.MEET_VOICE
    });
    [...videoList, ...audioList].forEach((item) => {
      this.meetlistObj[item.meetnum] = item;
    });
  }

  initWsNotice() {
    layui.use(() => {
      const { layer } = window.layui;
      window._rtc.ws.on('meet_invite', ({ meet, meetAddUser }) => {
        layer.open({
          type: 1,
          title: '会议邀请',
          shade: 0,
          maxmin: true,
          content: tplReplace(meetInviteTip, {
            meetname: meet.meetname,
            type: MEET_TYPE_TEXT_MAP[meet.type]
          }),
          btn: ['接受', '拒绝'],
          yes: (index, layero) => {
            this.render = new Render({ mountedId: this.mountedId });
            this.answer(meet, this.process);
            layer.close(index);
          },
          btn2: (index, layero) => {
            layer.close(index);
          }
        });
      });
    });
  }

  /**
   *@description 挂载元素 不传默认弹窗形式展现
   * @param {String} id 挂载id
   */
  mounted(id) {
    this.mountedId = id;
  }

  async callPopFormal({ meetnum, users }) {
    // 打开弹窗
    const meet = this.meetlistObj[meetnum];
    this.render = new Render({ mountedId: this.mountedId });
    this.render.openMeet(meet);
    await this.call({ meet, users }, this.process);
    return this.meetSession;
  }

  async callPopTempVoice({ users } = { users: [] }) {
    // 打开弹窗
    const meet = await this.api.tempMeet({
      type: MEET_TYPE.MEET_VOICE,
      user_id: this.userInfo.user.id
    });
    this.render = new Render({ mountedId: this.mountedId });
    this.render.openMeet(meet);
    await this.callTempVoice({ users, meet }, this.process);
    return this.meetSession;
  }

  async callPopTempVideo({ users } = { users: [] }) {
    // 打开弹窗
    const meet = await this.api.tempMeet({
      type: MEET_TYPE.MEET_VIDEO,
      user_id: this.userInfo.user.id
    });
    this.render = new Render({ mountedId: this.mountedId });
    this.render.openMeet(meet);
    await this.callTempVideo({ users, meet }, this.process);
    return this.meetSession;
  }
}
