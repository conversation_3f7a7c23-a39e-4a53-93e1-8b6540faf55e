<template>
  <chart class="charts" :autoresize="true" :option="options" :loading="loading"></chart>
</template>

<script setup>
import { ref } from 'vue'
// import { graphic } from 'echarts/lib/export/api.js'
const loading = ref(true)
const options = ref({
  tooltip: {
    //提示框组件
    trigger: 'axis',
    backgroundColor: 'rgba(0, 33, 59, 0.8)',
    textStyle: {
      color: '#fff'
    },
    borderColor: 'rgba(24, 174, 236, 1)',
    formatter: '{b}:{c}',
    axisPointer: {
      type: 'shadow',
      label: {
        backgroundColor: '#6a7985'
      }
    }
  },
  grid: {
    left: '10%',
    right: '10%',
    bottom: '5%',
    top: 40,
    padding: '0 0 10 0',
    containLabel: true
  },
  xAxis: [
    {
      type: 'value',
      boundaryGap: true, //坐标轴两边留白
      axisLabel: {
        //坐标轴刻度标签的相关设置。
        interval: 0, //设置为 1，表示『隔一个标签显示一个标签』
        textStyle: {
          color: 'rgba(157, 226, 255, 1)',
          fontStyle: 'normal',
          fontFamily: '微软雅黑',
          fontSize: 12
        }
      },
      axisTick: {
        //坐标轴刻度相关设置。
        show: false
      },
      axisLine: {
        //坐标轴轴线相关设置
        show: true,
        lineStyle: {
          color: '#fff',
          opacity: 0.3
        }
      },
      splitLine: {
        //坐标轴在 grid 区域中的分隔线。
        show: true,
        lineStyle: {
          color: '#fff',
          opacity: 0.3,
          type: 'dashed'
        }
      }
    }
  ],
  yAxis: [
    {
      type: 'category',
      axisLabel: {
        textStyle: {
          color: 'rgba(157, 226, 255, 1)',
          fontStyle: 'normal',
          fontFamily: '微软雅黑',
          fontSize: 12
        }
      },
      axisLine: {
        show: true,
        color: 'rgba(99, 110, 124, 1)'
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: false,
        lineStyle: {
          color: ['#fff'],
          opacity: 0.06
        }
      },
      data: ['客户端', '论坛', '视频', '博客', '新闻']
    }
  ],
  series: [
    {
      type: 'bar',
      data: [15, 23, 28, 26, 21],
      barWidth: 5,
      barGap: 0, //柱间距离
      label: {
        show: true,
        formatter: '{a|}',
        color: '#fff',
        position: 'right',
        distance: -5,
        backgroundColor: 'rgba(96, 254, 254, 1)',
        padding: 2,
        borderRadius: 4,
        rich: {
          a: {
            width: 2,
            height: 2,
            borderRadius: 2,
            lineHeight: 2,
            backgroundColor: '#fff'
          }
        }
      },
      itemStyle: {
        //图形样式
        normal: {
          barBorderRadius: [0, 3, 3, 0],
          color: {
            type: 'linear',
            x: 1,
            y: 0,
            x2: 0,
            y2: 0,
            colorStops: [
              {
                offset: 1,
                color: 'rgba(52, 104, 138, 1)' // 0% 处的颜色
              },
              {
                offset: 0,
                color: 'rgba(44, 186, 175, 1)' // 100% 处的颜色
              }
            ],
            globalCoord: false // 缺省为 false
          }
        }
      },
      z: 1
    },
    {
      name: '框',
      type: 'bar',
      barGap: '-150%',
      data: [15.2, 23.2, 28.2, 26.2, 21.2],
      barWidth: 10,
      itemStyle: {
        normal: {
          color: 'none',
          borderColor: 'rgba(52, 104, 138, 1)',
          borderWidth: 1,
          barBorderRadius: [0, 3, 3, 0]
        }
      },
      z: 2
    }
  ]
})
setTimeout(() => {
  loading.value = false
}, 300)
</script>

<style lang="scss" scoped>
.charts {
  width: 100%;
  height: 100%;
}
</style>