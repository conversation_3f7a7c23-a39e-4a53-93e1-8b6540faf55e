<template>
  <footer>
    <ul>
      <li
        v-for="(it, i) of btns"
        :key="i"
        :style="{
          background:
            activaIdx === i
              ? `url(${it.activeBg}) no-repeat center`
              : `url(${it.normalBg}) no-repeat center`
        }"
        :class="{ active: activaIdx === i }"
      >
        <span @click="handleClick(i)">{{ it.name }}</span>
        <div class="popups" v-show="false"></div>
      </li>
    </ul>
  </footer>
</template>

<script>
export default {
  name: 'LeaderFooter',
  props: {
    btns: {
      type: Array,
      default() {
        return [
          {
            normalBg: require('@/assets/csts/n_btn11.png'),
            activeBg: require('@/assets/csts/a_btn11.png'),
            name: '机构组织',
            children: [{}]
          },
          {
            normalBg: require('@/assets/csts/n_btn12.png'),
            activeBg: require('@/assets/csts/a_btn12.png'),
            name: '人员力量'
          },
          {
            normalBg: require('@/assets/csts/n_btn13.png'),
            activeBg: require('@/assets/csts/a_btn13.png'),
            name: '视频监控'
          },
          {
            normalBg: require('@/assets/csts/n_btn14.png'),
            activeBg: require('@/assets/csts/a_btn14.png'),
            name: '城市部件'
          },
          {
            normalBg: require('@/assets/csts/n_btn15.png'),
            activeBg: require('@/assets/csts/a_btn15.png'),
            name: '城市事件'
          },
          {
            normalBg: require('@/assets/csts/n_btn16.png'),
            activeBg: require('@/assets/csts/a_btn16.png'),
            name: '重点场所'
          }
        ]
      }
    },
    activaIdx: {
      type: Number,
      default: -1
    }
  },
  methods: {
    handleClick(i) {
      this.$emit('mark', i)
    }
  }
}
</script>

<style lang="less" scoped>
footer {
  display: grid;
  place-items: center;
  width: 1920px;
  height: 52px;
  position: absolute;
  left: 0;
  bottom: 0;
  background: url(~@/assets/img/footer_bg.png) no-repeat center / cover;
  z-index: 1001;
  ul {
    margin-left: -90px;
    display: flex;
    gap: 29px;
    li {
      position: relative;
      width: 110px;
      height: 42px;
      line-height: 42px;
      margin-top: -7px;
      span {
        padding-left: 18px;
        font-size: 17px;
        font-family: YouSheBiaoTiHei;
        color: #e9fffe;
        background: linear-gradient(180deg, #ffffff 0%, #2f8af4 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        cursor: pointer;
      }
      &.active {
        span {
          color: #edfffd;
          background: linear-gradient(180deg, #fdfeff 0%, #cbedff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        &::after {
          content: '';
          position: absolute;
          width: 66px;
          height: 20px;
          left: 50%;
          transform: translateX(-50%);
          bottom: -15px;
          background: url(~@/assets/csts/btn_light2.png) no-repeat;
        }
      }
      .popups {
        position: absolute;
        left: 50%;
        top: -264px;
        transform: translateX(-50%);
        width: 156px;
        height: 243px;
        background: url(~@/assets/csts/popups.png) no-repeat;
        transition: all 2s;
      }
    }
  }
}
</style>
