/**
 *  key: 索引值;
 *  name: 标签名称;
 *  layerId: 标签id（用于地图打点作类型识别）;
 *  count: 该类型数量;
 *  markerIcon: 该类型打点图片;
 */
export const surroundingResourceTabs = [
  {
    key: 0,
    name: '人员信息',
    layerId: 'personnelInformation',
    count: 0,
    markerIcon: require(`@/assets/map/point/point2.png`)
  },
  {
    key: 1,
    name: '物资信息',
    layerId: 'materialInformation',
    count: 0,
    markerIcon: require(`@/assets/map/point/point3.png`)
  },
  {
    key: 2,
    name: '车辆信息',
    layerId: 'vehicleInformation',
    count: 0,
    markerIcon: require(`@/assets/map/point/point4.png`)
  },
  {
    key: 3,
    name: '医疗机构',
    layerId: 'medicalInstitution',
    count: 0,
    markerIcon: require(`@/assets/map/point/point5.png`)
  },
  {
    key: 4,
    name: '避难场所',
    layerId: 'shelter',
    count: 0,
    markerIcon: require(`@/assets/map/point/point6.png`)
  },
  {
    key: 5,
    name: '监控摄像',
    layerId: 'surveillanceCamera',
    count: 0,
    markerIcon: require(`@/assets/map/point/point7.png`)
  },
  {
    key: 6,
    name: '无人机',
    layerId: 'drone',
    count: 0,
    markerIcon: require(`@/assets/map/point/point8.png`)
  }
]
