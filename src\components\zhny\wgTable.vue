<template>
  <div class="wg-info">
    <div class="close" @click="$emit('close')"></div>
    <div class="title">
      <span>网格信息</span>
    </div>
    <div class="wg_pic" v-if="wgPic">
      <img :src="wgPic" alt="">
    </div>
    <div class="content">
      <table>
          <div class="left">
            <td class="label">网格名称：</td>
            <td class="value">{{wgTableObj.gridName}}</td>
          </div>
          <div class="right">
            <td class="label">所属社区：</td>
            <td class="value">{{wgTableObj.community}}</td>
          </div>
          <div class="left">
            <td class="label">网格员：</td>
            <td class="value">{{wgTableObj.name}}</td>
          </div>
          <div class="right">
            <td class="label">联系方式：</td>
            <td class="value">
              {{wgTableObj.phone}}
              <img class="video_icon" @click="videoMethod(wgTableObj.phone)" src="@/assets/zhny/map/video_icon.png"  />
            </td>
          </div>
          <div class="left">
            <td class="label">网格员类型：</td>
            <td class="value">{{wgTableObj.job}}</td>
          </div>
      </table>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BasicInformation',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    wgTableObj: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      wg: {},
      wgPic:''
    }
  },
  methods:{
    videoMethod(val){
      this.$emit('videoMethod',val)
    }
  },
  mounted(){
    console.log('this.wgTableObj',this.wgTableObj);
    this.wgPic = this.wgTableObj.tbAppendixList[0].url
  }
}
</script>

<style lang="less" scoped>
.wg-info {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  width: 500px;
  // height: 280px;
  margin: 0 auto;
  background: url('~@/assets/map/dialog/bg1.png') no-repeat center / 100% 100%;
  background-size: 100% 100%;
  padding: 14px 14px 14px 14px;
  box-sizing: border-box;
  .close {
    position: absolute;
    top: 26px;
    right: 34px;
    width: 24px;
    height: 24px;
    background: url('~@/assets/map/dialog/btn2.png') no-repeat;
    box-sizing: border-box;
    cursor: pointer;
  }
  .title {
    height: 47px;
    line-height: 47px;
    padding-left: 27px;
    margin: 0 auto;
    text-align: left;
    background: url('~@/assets/map/dialog/title1.png') no-repeat center / 100% 100%;
    box-sizing: border-box;
    span {
      font-size: 18px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      background: linear-gradient(180deg, #ffffff 0%, #2aa6ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .wg_pic{
    width: 300px;
    height: 200px;
    margin: 20px auto 10px;
    img{
      width: 100%;
      height: 100%;
    }
  }
  .content {
    margin: 0px 0 20px 0;
    padding: 0 10px 0 76px;
    table {
      border-collapse: collapse;
      width: 100%;
      color: #fff;
    }
    th,
    td {
      text-align: left;
      padding: 8px;
    }
    tr {
      display: flex;
      .label {
        width: 100px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
        flex-shrink: 0;
        padding: 8px 0;
      }
      .value {
        width: 120px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        padding: 8px 0;
        .video_icon{
          cursor: pointer;
        }
      }
      .right {
        margin-left: 10px;
      }
    }
  }
}
</style>
