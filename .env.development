# just a flag
ENV = 'development'

# base api
VUE_APP_BASE_URL = 'http://218.90.228.176:8088'
VUE_APP_MAP_TOKEN = '1c88ecf7281a1078fbafe5385ead6e4b'
# vue-cli uses the VUE_CLI_BABEL_TRANSPILE_MODULES environment variable,
# to control whether the babel-plugin-dynamic-import-node plugin is enabled.
# It only does one thing by converting all import() to require().
# This configuration can significantly increase the speed of hot updates,
# when you have a large number of pages.
# Detail:  https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/babel-preset-app/index.js

VUE_CLI_BABEL_TRANSPILE_MODULES = true
