<template>
	<div v-if="show">
		<div class="ai_waring">
			<div class="title">
				<span>运行轨迹</span>
			</div>
			<div class="close_btn" @click="closeEmitai"></div>
			<div class="content">
				<LeafletMapNjTrack ref="leafletMap1" :clear2="clear2" :back2="back2" :xzpd="xzpd" />
				<div class="info" v-if="njInfoShow">
					<ul>
						<li v-for="(item, index) in njInfo" :key="index">
							<span class="name">{{ item.name }}：</span>
							<span class="value">{{ item.value }}{{ item.unit }}</span>
						</li>
					</ul>
				</div>
				<div class="info" v-else>
					<ul>
						<li v-for="(item, index) in njInfo2" :key="index">
							<span class="name">{{ item.name }}：</span>
							<span class="value">{{ item.value }}{{ item.unit }}</span>
						</li>
					</ul>
				</div>
			</div>
			<!-- <div class="info">
        <ul>
          <li v-for="(item, index) in njInfo" :key="index">
            <span class="name">{{ item.name }}：</span>
            <span class="value">{{ item.value }}{{ item.unit }}</span>
          </li>
        </ul>
      </div> -->
		</div>
	</div>
</template>

<script>
import LeafletMapNjTrack from '@/components/map/LeafletMapNjTrack.vue'
export default {
	name: 'mapPop',
	mixins: [],
	props: {
		value: {
			type: Boolean,
			default: false,
		},
		njInfoShow: {
			type: Boolean,
			default: true,
		},
		startRunTime: {
			type: String,
			default: '',
		},
	},
	components: {
		LeafletMapNjTrack,
	},
	data() {
		return {
			back2: false,
			clear2: false,
			xzpd: false,
			// njInfo: [
			// 	{
			// 		name: '农机品目',
			// 		value: '履带式拖拉机',
			// 		unit: '',
			// 	},
			// 	{
			// 		name: '农机型号',
			// 		value: '-',
			// 		unit: '',
			// 	},
			// 	{
			// 		name: '联系人',
			// 		value: '-',
			// 		unit: '',
			// 	},
			// 	{
			// 		name: '联系方式',
			// 		value: '-',
			// 		unit: '',
			// 	},
			// 	{
			// 		name: '所属单位',
			// 		value: '-',
			// 		unit: '',
			// 	},
			// 	{
			// 		name: '运行时间',
			// 		value: '-',
			// 		unit: '',
			// 	},
			// 	{
			// 		name: '运行时长',
			// 		value: '-',
			// 		unit: 'h',
			// 	},
			// 	{
			// 		name: '运行里程',
			// 		value: '-',
			// 		unit: '公里',
			// 	},
			// 	{
			// 		name: '作业面积',
			// 		value: '-',
			// 		unit: '亩',
			// 	},
			// ],
			njInfo: [
				{
					name: '农机品目',
					value: '履带式拖拉机',
					unit: '',
				},
				{
					name: '联系人',
					value: '-',
					unit: '',
				},
				{
					name: '运行时间',
					value: '-',
					unit: '',
				},
			],
			njInfo2: [
				{
					name: '农机品目',
					value: '履带式拖拉机',
					unit: '',
				},
				{
					name: '联系人',
					value: '-',
					unit: '',
				},
				{
					name: '运行时长',
					value: '-',
					unit: 'h',
				},
				{
					name: '作业面积',
					value: '-',
					unit: '亩',
				},
			],
		}
	},
	computed: {
		show() {
			return this.value
		},
	},
	mounted() {},
	methods: {
		track(trackData) {
			console.log('trackData--==', trackData)
			// this.njInfo[5].value = this.startRunTime
			this.njInfo[2].value = this.startRunTime

			this.$refs.leafletMap1.loadTrackLayer('trcak', trackData)
		},
		track2(data) {
			this.$refs.leafletMap1.drawCircle(data, 2, 'rgba(255,215,0)')
		},
		closeEmitai() {
			this.$refs.leafletMap1.clearLayers()
			this.$emit('input', false)
		},
		getNjInfo(data) {
			console.log('data+++++++++++++++', data)
			const { agmachTypeName, mdlName, userName, phoneNo, workStartTime, workTime, workArea, ownerName } = data
			this.njInfo[0].value = agmachTypeName || '-'
			// this.njInfo[1].value = mdlName && mdlName != 'null' ? mdlName : '-'
			// this.njInfo[2].value = userName || '-'
			this.njInfo[1].value = phoneNo || '-'
			// this.njInfo[4].value = '-'
			// this.njInfo[5].value = workStartTime || '-'
			// this.njInfo[6].value = workTime || '-'
			// this.njInfo[7].value = '-'
			// this.njInfo[8].value = workArea || '-'
			this.njInfo2[0].value = agmachTypeName || '-'
			this.njInfo2[1].value = ownerName || '-'
			this.njInfo2[2].value = workTime || '-'
			this.njInfo2[3].value = workArea || '-'
		},
	},
}
</script>

<style lang="less" scoped>
.ai_waring {
	width: 1535px;
	height: 900px;
	background: rgba(9, 19, 34, 0.95);
	box-shadow: 0px 0px 22px 0px rgba(11, 54, 138, 0.35), 0px 2px 8px 0px rgba(0, 0, 0, 0.7),
		inset 0px 0px 8px 0px rgba(17, 146, 214, 0.35);
	border-radius: 11px;
	border: 1px solid #015c8c;
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	z-index: 1900 !important;

	.title {
		margin: 0 auto 0;
		width: 635px;
		height: 76px;
		line-height: 76px;
		background: url(~@/assets/map/dialog/title2.png) no-repeat;
		display: grid;
		place-items: center;

		span {
			display: inline-block;
			font-size: 36px;
			font-family: PingFangSC, PingFang SC;
			background: linear-gradient(180deg, #ffffff 40%, #0079ff 70%);
			background-clip: text;
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}
	}

	.close_btn {
		position: absolute;
		width: 23px;
		height: 23px;
		top: 25px;
		right: 40px;
		background: url(~@/assets/map/dialog/btn3.png) no-repeat;
		cursor: pointer;
	}

	.content {
		position: relative;
		height: calc(100% - 120px);
		width: 1428px;
		margin-left: 40px;
		.info {
			position: absolute;
			bottom: 0;
			// height: 150px;
			padding: 20px;
			width: 1428px;
			background: rgba(0, 0, 0, 0.6);
			border-radius: 0px 0px 8px 8px;
			backdrop-filter: blur(5px);
			ul {
				width: 100%;
				margin: 0 auto;
				display: flex;
				justify-content: space-between;
				flex-wrap: wrap;
				// padding-top: 10px;

				li {
					text-align: left;
					width: 33.3%;
					margin-bottom: 10px;
					padding-left: 33px;
					display: flex;
					align-items: center;
					// justify-content: space-between;
					.name {
						height: 17px;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 12px;
						color: rgba(106, 146, 187, 1);
						line-height: 17px;
						text-align: center;
						font-style: normal;
					}

					.value {
						height: 17px;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 12px;
						color: #ffffff;
						line-height: 17px;
						text-align: center;
						font-style: normal;
					}
				}
			}
		}
	}
}
</style>
