<template>
  <div v-if="value" class="invite-board" :style="{width: width, height: height}">
    <div class="avatar"></div>
    <div class="board-text">{{ inviteBoardData.split('_')[1] || '' }}请求加入视频通话</div>
    <div class="board-btns">
      <div class="btn" @click="hangOff">拒绝</div>
      <div class="btn" @click="listen">同意</div>
    </div>
    <audio id="bell" src="@/assets/alarm/bell.mp3" loop autoplay></audio>
  </div>
</template>

<script>
  export default {
    name: 'inviteBoard',
    props: {
      width: {
        type: String,
        default: '392px'
      },
      height: {
        type: String, 
        default: '203px'
      },
      value: {
        type: Boolean,
        default: false
      },
      inviteBoardData: {
        type: String,
        default: ''
      }
    },
    data() {
      return {

      }
    },
    methods: {
      hangOff() {
        this.$emit('refuse', this.inviteBoardData)
        this.$emit('input', false)
      },
      listen() {
        this.$emit('accept', this.inviteBoardData)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .invite-board {
    position: absolute;
    left: 1024px;
    top: 121px;
    z-index: 2000;
    background: url('~@/assets/service/invite-bg.png') no-repeat center / 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .avatar {
      width: 60px;
      height: 60px;
      margin-top: 28px;
      background: url('~@/assets/service/bohao.png') no-repeat center / 100% 100%;
    }
    .board-text {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #FFFFFF;
      line-height: 24px;
      margin-top: 12px;
      margin-bottom: 27px;
    }
    .board-btns {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      .btn {
        cursor: pointer;
        width: 108px;
        &:not(:last-child) {
          margin-right: 12px;
        }
        height: 31px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #FFFFFF;
        text-align: center;
        line-height: 31px;
        background: url('~@/assets/service/invite-btn.png') no-repeat center / 100% 100%;
      }
    }
    
  }
</style>