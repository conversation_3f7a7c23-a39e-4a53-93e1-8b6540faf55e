#rhtx_BoxId{
  border: none;
}
.rtc-meet,
.meet-container .meet-top-tools {
  /* background: rgba(0, 74, 143, 0.4) linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #FFFFFF 100%); */
  background: rgba(9, 19, 34, 0.9);
  box-shadow: 0px 0px 22px 0px rgba(11, 54, 138, 0.35), 0px 2px 8px 0px rgba(0, 0, 0, 0.7), inset 0px 0px 8px 0px rgba(17, 146, 214, 0.35);
}

.rtc-meet .talking .name {
  font-family: PangMenZhengDao;
  color: #FFFFFF;
  font-size: 18px;
}

.meet-main .meet-side {
  color: #FFFFFF;
}

.meet-container .meet-bottom-tools .tool-btn,
.iconfont.iconrenyuanxiaozu {
  color: skyblue;
}

.info-desc {
  width: 136px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: left;
}

.meet-side .user-item .info-desc .name {
  font-size: 16px;
}

.meet-side .user-item .info-desc .desc {
  color: #eee;
}

.iconfont.iconguanbizhongqi {
  color: #fff;
}

.tool-btn.meetOff {
  color: #fff;

}

.meet-side .meet-controls {
  border: none;
}

.meet-side .meet-controls .actions button:first-of-type {
  border: none;
  /* width: 64px; */
  height: 33px;
  color: #fff;
  background: url(~@/assets/img/video_reset.png) no-repeat;
  background-size: 100% 100%;
}

.meet-side .meet-controls .actions button:last-of-type {
  border: none;
  /* width: 88px; */
  height: 33px;
  color: #fff;
  background: url(~@/assets/img/video_reset.png) no-repeat;
  background-size: 100% 100%;
  margin-left: 2px;
}

.meet-side .search-wrap input {
  background: transparent;
  color: #eee;
}

.meet-side .search-wrap input::placeholder {
  text-indent: 12px;
}