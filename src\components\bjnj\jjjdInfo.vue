<template>
	<div class="jjjd-detail-dialog" v-if="show">
		<div class="ai_waring">
			<div class="title">
				<span>机具进度</span>
				<i class="el-icon-close close_btn" @click="closeEmitai"></i>
			</div>
			<div class="content">
				<div class="check_types">
					<div class="filter-box"></div>
					<div class="jjcd">
						<span>任务编号：</span>
						{{ data.id || '-' }}
					</div>
					<div class="jjcd">
						<span>受灾区域：</span>

						{{ data.areaname || '-' }}
					</div>

					<div class="jjcd">
						<span>受灾类型：</span>
						{{ data.affectedType || '-' }}
					</div>
					<div class="jjcd">
						<span>受灾作物：</span>
						{{ data.affectedCrop || '-' }}
					</div>
					<div class="jjcd">
						<span>受灾面积：</span>
						{{ data.affectedArea || '-' }}
					</div>
					<div class="jjcd">
						<span>请求时间：</span>
						{{ data.requestDate || '-' }}
					</div>
					<!-- <div class="jjcd">
						<span>状态：</span>
						{{ data.agmachStatus || '-' }}
					</div> -->
				</div>
				<div class="table_box">
					<!-- <SwiperTableMap
            :titles="[
              '序号',
              '请求编号',
              '请求类型',
              '受灾区域',
              '受灾面积',
              '受灾类型',
              '受灾作物',
              '所需农机',
              '上报时间',
              '请求调度时间',
              '状态',
              '操作',
            ]"
            :widths="['4%','9%','6%','10%','6%','6%','6%','9%','10%','9%', '6%', '19%',]"
            :data="tableList"
            :contentHeight="'510px'"
            :settled="settled"
            @operate="operate"
          ></SwiperTableMap> -->
					<el-table
						ref="multipleTable"
						v-if="!loading && tableList.length > 0"
						:data="tableList"
						tooltip-effect="dark"
						style="width: 100%;height: 100%;"
						@selection-change="handleSelectionChange"
					>
						<!-- <el-table-column type="selection" width="48"> </el-table-column> -->
						<el-table-column type="index" label="序号" :index="indexMethod" width="66"> </el-table-column>
						<el-table-column prop="area" label="调出区域"> </el-table-column>
						<el-table-column prop="name" label="生产组织"> </el-table-column>
						<el-table-column prop="argmachType" label="农机类型"> </el-table-column>
						<el-table-column prop="argmachNumber" label="农机数量"> </el-table-column>
						<el-table-column label="状态">
							<template slot-scope="scope">{{ data.agmachStatus || '-'}}</template>
						</el-table-column>
					</el-table>
					<div v-else-if="loading" class="alert-empty">加载中...</div>
					<img v-else-if="tableList.length == 0" class="zwsjImg" src="@/assets/bjnj/zwsj.png" />
				</div>
				<!-- <div class="fy_page">
					<el-pagination
						@current-change="pageNumChange"
						:current-page="pageNum"
						:page-size="this.pageSize"
						layout="total, prev, pager, next, jumper"
						:total="this.total"
					>
					</el-pagination>
				</div> -->
			</div>
		</div>
	</div>
</template>

<script>
import dayjs from 'dayjs'
import SwiperTableMap from './table/RealTimeEventTable4.vue'
// api
import { getEventType, getUnDoneEventList, getDoneEventList, getQgdSssj } from '@/api/hs/hs.api.js'
import { myMixins } from '@/libs/myMixins.js'
import { api_getJjjdItemScreen, getCscpBasicHxItemCode, cscpCurrentUserDetails } from '@/api/bjnj/zhdd.js'

export default {
	name: 'RealTimeEventDialogQgd',
	mixins: [myMixins],
	components: { SwiperTableMap },
	props: {
		value: {
			type: Boolean,
			default: false,
		},
		data: {
			type: Object,
			default: () => {
				return {}
			},
		},
		list: {
			type: Array,
			default: () => {
				return []
			},
		},
	},
	data() {
		return {
			roleNames: '',
			swiperOption: {
				autoHeight: true,
				direction: 'vertical',
				spaceBetween: 0,
				autoplay: {
					delay: 2500,
					disableOnInteraction: false,
					autoplayDisableOnInteraction: false,
				},
				slidesPerView: 'auto',
				grabCursor: true,
				autoplayDisableOnInteraction: false,
				mousewheelControl: true,
			},
			tabActive: 0,
			tabs: ['未办结', '办结'],
			btnsArr: [
				{
					name: '未办结',
					normalBg: require('@/assets/shzl/map/left_n.png'),
					activeBg: require('@/assets/shzl/map/left_a.png'),
				},
				{
					name: '已办结',
					normalBg: require('@/assets/shzl/map/right_n.png'),
					activeBg: require('@/assets/shzl/map/right_a.png'),
				},
			],
			urgentOptions: [
				{
					value: '0',
					label: '普通',
				},
				{
					value: '1',
					label: '突发',
				},
				{
					value: '2',
					label: '重要',
				},
				{
					value: '3',
					label: '紧急',
				},
			],
			eventTypeOptions: [
				{
					value: '0',
					label: '普通',
				},
			],
			timeOutOptions: [
				{
					value: '0',
					label: '是',
				},
				{
					value: '1',
					label: '否',
				},
			],
			proityValue: '',
			timeOutValue: '',
			settled: true,
			total: 0,
			pageNum: 1,
			pageSize: 10,
			finished: 1,
			tableList: [
				{
					outArea: 'aaaa',
					productionDepartment: 'aaaa',
					argmachType: 'aaaa',
					argmachNumber: 5,
					argmachStatus: 1, //[0]:未开始, [1]:行进中, [2]:作业中
				},
			],
			loading: false,
			gdObj: {
				jjcdValue: '',
				sqlxValue: '',
			},
			selectionList: [],
		}
	},
	watch: {
		qgdCodejjcdOptions(val) {
			if (this.qgdCodejjcdOptions.length > 0) {
				// this.getQgdSssj()
			}
		},
	},
	computed: {
		show() {
			return this.value
		},
	},
	mounted() {
		this.cscpCurrentUserDetails()
		this.getCscpBasicHxItemCode1()
		this.getCscpBasicHxItemCode2()
		// this.jjjdItemScreen({
		//   current: this.pageNum,
		//   size: this.pageSize,
		//   taskTest: 0
		// })
	},
	methods: {
		handleSelectionChange(e) {
			console.log(e)
			this.selectionList = e
			console.log(this.selectionList)
		},
		async cscpCurrentUserDetails(data) {
			let res = await cscpCurrentUserDetails(data)
			console.log(res)
			if (res?.code == '0') {
				this.roleNames = res.data.roleNames
			}
		},
		indexMethod(index) {
			return index + 1
		},
		async jjjdItemScreen(data_) {
			this.loading = true
			const data__ = {
				id: data_.id,
				// ...data,
				// state: data.state || '',
				// areaId: data.areaId || '',
			}
			console.log('data__--==', data__)
			try {
				let res = await api_getJjjdItemScreen(data__)
				console.log('jjjdItemScreenRes--==', res)
				const data___ = res.data[0].resources

				this.tableList = []
				console.log('this.data--==', this.data)
				data___.forEach((item) => {
					this.tableList.push({
						area: item.area,
						name: item.name,
						argmachType: item.tagmachInfos[0].agmachType,
						argmachNumber: item.tagmachInfos.length,
						// argmachStatus:
					})
				})
				this.total = res.data[0].resources.length
				this.loading = false
			} catch {
				this.total = this.tableList.length
				this.loading = false
			}

			// this.tableList = res.data.map((it, i) => [
			//   this.pageSize * (this.pageNum - 1) + i + 1,
			//   it.id,
			//   it.requestType ? '防灾' : '救灾',
			//   it.areaname,
			//   it.affectedArea,
			//   it.affectedType,
			//   it.affectedCrop,
			//   JSON.parse(it.items)[0].name + JSON.parse(it.items)[0].num + '台',
			//   it.createDatetime,
			//   it.requestDate,
			//   it.statusName,
			//   it.actionsForCurrentNode,
			//   it.zstatus,
			//   it.updateDatetime,
			//   it,
			// ])
		},
		async getCscpBasicHxItemCode1() {
			let res = await getCscpBasicHxItemCode('requestType')
			console.log(res)
			if (res?.code == '0') {
				this.qgdCodejjcdOptions = res.data
			}
		},
		async getCscpBasicHxItemCode2() {
			let res = await getCscpBasicHxItemCode('zstatus')
			console.log(res)
			if (res?.code == '0') {
				this.qgdCodesqlxOptions = res.data
			}
		},
		closeEmitai() {
			this.$emit('input', false)
		},
		pageNumChange(val) {
			this.pageNum = val
			let data = {
				id: this.data.id,
				current: this.pageNum,
				size: this.pageSize,
				taskTest: 0,
			}
			this.removeEmptyValues(data)
			data.taskTest = 0
			console.log(data)
			this.jjjdItemScreen(data)
		},
		async getQgdSssj() {
			this.tableList = []
			let res = await getQgdSssj({
				// street: '1649962979288023040',
				page: this.pageNum,
				size: this.pageSize,
				emeLevel: this.gdObj.jjcdValue,
				appealType: this.gdObj.sqlxValue,
			})
			console.log('实时事件弹窗', res)
			console.log(this.qgdCodejjcdOptions)
			if (res?.code == '200' && res.result.data.length > 0) {
				console.log('res.result.data', res.result.data)
				this.tableList = res.result.data.map((it, i) => [
					this.pageSize * (this.pageNum - 1) + i + 1,
					it.orderNum,
					it.emeLevel,
					it.appealContent,
					it.appealType,
					it.community,
					it.eventDate,
					it.formStatus,
					it.eventLocation,
					it.id,
					it,
				])
				this.total = res.result.recordsTotal
			}
			// console.log('this.tableList', this.tableList)
		},
		removeEmptyValues(obj) {
			for (const key in obj) {
				if (typeof obj[key] === 'object') {
					this.removeEmptyValues(obj[key])
				}
				if (!obj[key] || (typeof obj[key] === 'object' && !Reflect.ownKeys(obj[key]).length)) {
					delete obj[key]
				}
			}
		},

		resetBtn() {
			this.gdObj = {}
			this.jjjdItemScreen({
				current: this.pageNum,
				size: this.pageSize,
				taskTest: '0',
			})
		},
	},
}
</script>

<style lang="less">
.elDatePicker.el-picker-panel {
	color: #fff; //设置当前面板的月份的字体为白色，记为1
	background: #002450; //定义整体面板的颜色
	border: 1px solid #1384b4; //定义整体面板的轮廓
	.el-input__inner {
		background: #002450;
		color: #fff;
	}
	.el-picker-panel__icon-btn {
		//设置年份月份调节按钮颜色，记为2
		color: #ffffff;
	}
	.el-date-picker__header-label {
		//设置年月显示颜色，记为3
		color: #ffffff;
	}
	.el-date-table th {
		//设置星期颜色，记为4
		color: #ffffff;
	}
	.el-picker-panel__footer {
		background: #002450;
	}
}
</style>
<style lang="less" scoped>
.zwsjImg {
	width: 250px;
	margin: 20px 0;
}

.alert-empty {
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	font-family: PangMenZhengDao-3, PangMenZhengDao-3;
	font-weight: 500;
	font-size: 20px;
	color: #dbf1ff;
	text-shadow: 0px 0px 10px #bddcf1;
	text-align: center;
	font-style: normal;
	text-transform: none;
}
.bg-header {
	position: absolute;
	width: 100%;
	height: 1080px;
	background: rgba(2, 14, 50, 0.75);
	z-index: 999999;
	top: 0;
	left: 0;
}

/deep/ .ivu-select {
	width: 189px;
}
/deep/ .ivu-select-selection {
	width: 189px;
	height: 34px;
	background: rgba(0, 74, 143, 0.4) linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
	border: 1px solid rgba(0, 162, 255, 0.6);
}
/deep/ .ivu-select-selection .ivu-select-selected-value {
	font-size: 13px;
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	color: #ccf4ff;
	line-height: 34px;
}

/deep/ .ivu-input {
	background: rgba(0, 74, 143, 0.4) linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
	border: 1px solid rgba(0, 162, 255, 0.6);
	font-size: 16px;
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	color: #ccf4ff;
	line-height: 22px;
}
/deep/ .ivu-select-placeholder {
	height: 34px !important;
	font-size: 13px !important;
	line-height: 34px !important;
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	color: #ccf4ff !important;
}
/deep/ .ivu-page-item {
	background: transparent;
	color: #fff;
}
/deep/ .ivu-page-item a {
	color: #fff;
}
/deep/ .ivu-page-prev,
/deep/ .ivu-page-next {
	background: transparent;
}
/deep/ .el-input__inner {
	color: #ccf4ff;
}

.jjjd-detail-dialog {
	width: 1920px;
	height: 1080px;
	position: absolute;
	left: 0;
	top: 0;
	background-color: rgba(0, 0, 0, 0.3);
	z-index: 1106;
	display: flex;
	justify-content: center;
	align-items: center;
}

.ai_waring {
	width: 1367px;
	height: 652px;
	background: #001c40;

	border: 1px solid #023164;
	z-index: 1100;
	.title {
		width: 100%;
		height: 48px;
		line-height: 48px;
		// background: url(~@/assets/map/dialog/title7.png) no-repeat;

		padding: 0 16px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background: #104993;
		span {
			font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
			font-weight: normal;
			font-size: 16px;
			color: #d0deee;
			line-height: 16px;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}
		.close_btn {
			font-size: 14px;
			color: #80caff;
			cursor: pointer;
		}
	}

	.content {
		padding: 26px 16px 24px 16px;
		.btns {
			// border: 1px solid red;
			margin: 0 auto;
			display: flex;
			justify-content: center;
			& > div {
				width: 154px;
				height: 53px;
				span {
					font-size: 24px;
					font-family: PingFangSC, PingFang SC;
					color: #ffffff;
					line-height: 53px;
					background: linear-gradient(180deg, #ffffff 0%, #ffeba0 100%);
					background-clip: text;
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
				}
			}
		}

		.table_box {
			margin-top: 31px;
			// height: 421px;
			height: 490px;

			overflow-y: auto;

			&::-webkit-scrollbar {
				width: 4px; /* 滚动条宽度 */
			}

			//轨道
			&::-webkit-scrollbar-track {
				background: #04244e; /* 滚动条轨道背景 */
				border-radius: 10px;
				padding: 5px 0;
			}

			//滑块
			&::-webkit-scrollbar-thumb {
				background: #82aed0; /* 滑块背景颜色 */
				border-radius: 10px;
				height: 15px;
			}

			//悬停滑块颜色
			&::-webkit-scrollbar-thumb:hover {
				background: #ff823b; /* 鼠标悬停时滑块颜色 */
			}

			//箭头
			&::-webkit-scrollbar-button {
				display: none; /* 隐藏滚动条的箭头按钮 */
			}

			.czBtn {
				display: inline-block;
				button {
					margin: 0 8px !important;
				}
			}
			/deep/ .el-table th.el-table__cell {
				background-color: rgba(0, 0, 0, 0);
			}
			/deep/ .el-table tr {
				background-color: rgba(0, 0, 0, 0);
			}
			/deep/ .el-table td.el-table__cell,
			/deep/ .el-table th.el-table__cell.is-leaf {
				// border-bottom: none;

				border-bottom: 1px solid #263e5d !important;
			}
			/deep/ .el-table,
			/deep/ .el-table__expanded-cell {
				background-color: rgba(0, 0, 0, 0);
			}
			/deep/ .el-table__header-wrapper {
				background-color: #023164;
				// border-bottom: 1px solid rgba(255, 255, 255, 0.15);
			}
			/deep/ .el-table th.el-table__cell.is-leaf {
				// border-right: 1px solid #2968c7;
			}
			/deep/ .el-table th.el-table__cell > .cell {
				text-align: left;

				font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
				font-weight: normal;
				font-size: 14px;
				color: #159aff;
				line-height: 14px;
				font-style: normal;
				text-transform: none;
			}
			/deep/ .el-table .cell {
				text-align: left;
				-webkit-user-select: text;
				-moz-user-select: text;
				-ms-user-select: text;
				user-select: text;
			}

			/deep/ .el-table .el-checkbox__inner {
				border: 1px solid rgba(0, 162, 255, 0.6) !important;
				background-color: #001c40 !important;
			}

			/deep/ .el-table__row {
			}
			/deep/ .el-table__row:nth-child(odd) {
			}
			/deep/ .el-table__body tr.hover-row > td.el-table__cell {
				background-color: rgba(0, 0, 0, 0);
			}

			/deep/ .el-table__body-wrapper {
				height: calc(100% - 48px);

				overflow: auto;
				&::-webkit-scrollbar {
					width: 4px; /* 滚动条宽度 */
					height: 4px;
				}

				//轨道
				&::-webkit-scrollbar-track {
					background: #04244e; /* 滚动条轨道背景 */
					border-radius: 10px;
					padding: 5px 0;
				}

				//滑块
				&::-webkit-scrollbar-thumb {
					background: #82aed0; /* 滑块背景颜色 */
					border-radius: 10px;
					height: 15px;
				}

				//悬停滑块颜色
				&::-webkit-scrollbar-thumb:hover {
					background: #ff823b; /* 鼠标悬停时滑块颜色 */
				}

				//箭头
				&::-webkit-scrollbar-button {
					display: none; /* 隐藏滚动条的箭头按钮 */
				}
			}
		}
		.fy_page {
			margin: 24px 0;
			display: flex;
			justify-content: flex-end;
			align-items: center;

			/deep/ .el-pager {
				margin: 0 8px;

				background: #001c40;
				li {
					width: 32px;
					height: 32px;
					border-radius: 2px;
					padding: 10px !important;
					line-height: 13px !important;
					&.active {
						color: #ffffff;
						background: #159aff;
						border-radius: 2px 2px 2px 2px;
					}
				}
			}

			/deep/ .el-pagination {
				display: flex;
				justify-content: flex-start;
				align-items: center;
			}

			/deep/ .el-pagination span {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #d0deee;
				text-align: left;
				font-style: normal;
				text-transform: none;
				&:not {
				}
			}

			/deep/ .el-pagination button {
				width: 32px;
				height: 32px;
				font-family: Helvetica Neue, Helvetica Neue;
				font-weight: 400;
				font-size: 14px;
				background: #001c40;
				color: #d0deee;
				line-height: 22px;
				text-align: center;
				font-style: normal;
				text-transform: none;
				border: 1px solid #dcdee2;
				border-radius: 2px;
				padding: 10px 8px !important;
			}

			/deep/ .el-pagination__jump {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #d0deee;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}
			/deep/ .el-pagination__editor.el-input {
				width: 48px;
				margin: 0 8px;
			}

			/deep/ .el-input__inner {
				border-radius: 2px 2px 2px 2px;
				border: 1px solid #dcdee2;
				background-color: #001c40;
				color: #d0deee;
			}
		}
	}

	.check_types {
		text-align: left;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		padding: 0 16px 16px 16px !important;
		border-bottom: 1px solid #104993;

		.filter-box {
			text-align: left;
			display: flex;
			justify-content: flex-start;
			align-items: center;
		}

		.jjcd {
			// width: 251px;
			// height: 32px;
			display: flex;
			align-items: center;
			// margin-right: 24px;

			font-size: 14px;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			line-height: 14px;
			padding: 3px 20px;
			border-right: 1px solid #104993;
			color: #ffffff;

			&:last-child {
				border-right: 0px solid #104993;
			}

			span {
				width: 70px;
				font-size: 14px;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				line-height: 14px;
				color: #ffffff;
				margin-right: 5px;
			}

			/deep/ .el-input {
				width: 181px;
				height: 32px;
				.el-input__inner {
					width: 181px !important;
					height: 32px !important;
					// background: linear-gradient(180deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%),
					// 	rgba(0, 74, 143, 0.4);
					background-color: #001c40 !important;
					border: 1px solid #d9d9d9 !important;
					border-radius: 4px 4px 4px 4px !important;

					font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
					font-weight: normal;
					font-size: 14px !important;
					color: #6c8097 !important;
					line-height: 14px;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}
			}
		}

		/deep/ .el-select {
			width: 181px !important;
			height: 32px !important;
		}

		.report_time {
			margin-left: 26px;
			display: flex;
			align-items: center;
			span {
				font-size: 16px;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				color: #ffffff;
				line-height: 22px;
			}
		}
		.istime_out {
			margin-left: 26px;
			align-items: center;
			display: flex;
			span {
				font-size: 16px;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				color: #ffffff;
				line-height: 22px;
			}
		}
		.type_btns {
			display: flex;
			align-items: center;

			& div {
				width: 60px;
				height: 32px;
				font-size: 16px;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				color: #ffffff;
				line-height: 32px;
				border-radius: 2px 2px 2px 2px;
				border: 1px solid #d0deee;
				display: flex;
				justify-content: center;
				align-items: center;
				font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
				font-weight: normal;
				font-size: 14px;
				line-height: 14px;
				text-align: center;
				font-style: normal;
				text-transform: none;
				margin: 0 4px;
				cursor: pointer;
			}

			& div:first-child {
				background-color: #001c40;
				color: #d0deee;
			}

			& div:last-child {
				background-color: #159aff;
				color: #d0deee;
			}
		}
	}
}
/deep/ .el-table .cell {
	color: rgba(255, 255, 255, 0.85);
}
/deep/ .el-button--text {
	color: #ff6600;
}
/deep/ .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
	background-color: rgba(255, 255, 255, 0);
}
// /deep/ .ivu-page-total {
//   font-size: 18px;
//   color: #fff;
// }
</style>
