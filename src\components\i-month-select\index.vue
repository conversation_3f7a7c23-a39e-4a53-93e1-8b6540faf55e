<template>
	<div class="down-box">
		<div class="t-date-box">
			<!-- :disabled-date="disabledDate" -->
			<!-- :picker-options="pickerOptions" -->

			<el-date-picker
				popper-class="t-date-picker"
				v-model="resultDate"
				style="width: 130px; height: 22px;z-index: 1;"
				type="date"
				placeholder="选择日期"
				:clearable="false"
				@focus="setYearDateIcon"
				value-format="yyyy-MM-dd"
				:format="formatDate()"
				:disabledDate="disabledDate"
				@blur="
					() => {
						downDateVisableIcon = true
						upDateVisableIcon = false
					}
				"
				@change="handleChangeDate"
			>
			</el-date-picker>
			<i class="date-picker-icon el-select__caret el-input__icon el-icon-arrow-down"></i>

			<!--  -->
			<!-- <el-icon
				class="t-date-icon"
				:class="downDateVisableIcon == true ? 'data_display' : 'data_no_display'"
				@click="handleClickYearIcon"
				><ArrowDown
			/></el-icon>
			<el-icon class="t-date-icon" :class="upDateVisableIcon == true ? 'data_display' : 'data_no_display'"><ArrowUp /></el-icon> -->
		</div>
	</div>
</template>

<script>
import dayjs from 'dayjs'

export default {
	props: {
		date: {
			type: String,
			default: '',
		},
	},
	data() {
		return {
			currentIndex: 0,
			resultDate: '',
			downDateVisableIcon: true,
			upDateVisableIcon: false,
		}
	},
	mounted() {
		this.resultDate = this.getFormattedDate()
	},
	methods: {
		getFormattedDate() {
			let year = new Date()
				.getFullYear()
				.toString()
				.padStart(4, '0')
			let month = (new Date().getMonth() + 1).toString().padStart(2, '0') // 注意月份是从0开始的，所以要+1
			let day = new Date()
				.getDate()
				.toString()
				.padStart(2, '0')
			return new Date(`${year}-${month}-${day}`)
		},
		setYearDateIcon() {
			if (this.resultDate != '' || this.resultDate != null) {
				this.downDateVisableIcon = false
				this.upDateVisableIcon = true
			} else {
				this.upDateVisableIcon = false
				this.downDateVisableIcon = true
			}
		},
		formatDate() {
			const resultDateObj = new Date(this.resultDate)
			const dateStr = resultDateObj.toString()
			// if (this.resultDate === this.getFormattedDate()) {
			// 	return resultDateObj.getFullYear() + '年当期'
			// } else {
			// 	return 'YYYY-MM-DD'
			// }
			if (dateStr === this.getFormattedDate().toString()) {
				return resultDateObj.getFullYear() + '年当期'
			} else {
				return 'yyyy-MM-dd'
			}
		},
		disabledDate(time) {
			return time.getTime() > Date.now()
		},
		handleChangeDate(val) {
			this.downDateVisableIcon = true
			this.upDateVisableIcon = false
			this.resultDate = val
			this.$emit('update:date', val)
			//在此处调用父组件方法
			this.$emit('changeDate', val)
		},
		handleClickYearIcon() {},
		setSwitchStatus() {},
	},
	watch: {
		// date(val) {
		//     console.log('val--==', val);
		// 	this.resultDate = val
		// },
	},
}
</script>
<style lang="scss" scoped>
.down-box {
	min-width: 120px;
	min-height: 22px;
	position: absolute;
	right: 5px;
	top: 0;
	display: flex;
	justify-content: center;
	align-items: center;
	cursor: pointer;

	.t-date-icon {
		position: absolute;
		right: 8px;
		top: 8px;
		font-size: 14px;
		color: #d0deee;
	}

	.t-date-box {
		min-width: 120px;
		min-height: 22px;
		position: relative;
		flex: 1;
		margin-right: 0;

		.data_display {
			display: block;
		}
		.data_no_display {
			display: none;
		}

		.date-picker-icon {
			position: absolute;
			right: -4px;
			top: -2px;
			font-size: 16px;
			z-index: 0;
		}

		::v-deep(.el-input__inner) {
			height: 30px !important;
			padding: 0 30px;
			padding-right: 8px;
			// border: 0 none rgb(39, 127, 146, 0) !important;
			border-radius: 2px;
			font-size: 14px;
			box-shadow: none;

			background: rgb(0, 0, 0, 0);
			color: #fff;
			border: 2px solid rgb(19, 89, 194, 0) !important;
		}

		::v-deep(.el-input__icon) {
			line-height: 38px;
		}

		::v-deep(.el-input__inner) {
			color: #d0deee;
		}
	}

	.t-month-box {
		min-width: 105px;
		height: 22px;
		position: relative;
		flex: 1;

		.data_month_display {
			display: block;
		}
		.data_month_no_display {
			display: none;
		}

		::v-deep(.el-input__inner) {
			padding: 0 12px;
			border: 0 none rgb(39, 127, 146, 0) !important;
			border-radius: 2px;
			font-size: 14px;
			background: rgb(39, 127, 146, 0.5);
			color: #d0deee;
			box-shadow: none;
		}

		::v-deep(.el-input__inner) {
			color: #d0deee;
		}
	}
}
</style>

<style lang="scss">
.t-date-picker {
	cursor: pointer !important;
	// background-color: rgb(27, 80, 97, 0.8) !important;
	// border: 0px !important;
	background: rgba(15, 43, 87, 0.9) !important;
	border: 1px solid #2b7bbb !important;

	.el-date-table th {
		color: #fff !important;
	}

	.el-date-picker__header-label {
		color: #fff !important;
	}
	.el-picker-panel__icon-btn .el-icon {
		color: #fff !important;
		font-size: 14px !important;
	}
	.el-picker-panel__icon-btn {
		color: #fff;
	}
	.el-picker-panel {
		color: #fff !important;
		background: rgb(27, 80, 97, 1) !important;
		border: 1px solid rgb(27, 80, 97, 1) !important;
	}
	.el-popper__arrow {
		background-color: rgba(30, 84, 128, 0.8) !important;
	}
	.el-picker__popper.el-popper {
		color: #fff;
		background: rgba(30, 84, 128, 0.8);
		border: 1px solid rgba(29, 128, 218, 1);
	}
	.el-picker__popper.el-popper {
		border: 2px solid red !important;
	}

	.cell {
		font-size: 14px !important;
		color: #fff !important;
	}
	.cell:hover {
		color: #fff !important;
		background-color: rgb(64, 158, 255);
		font-weight: 700 !important;
	}

	.el-date-table td.disabled .el-date-table-cell {
		background-color: rgb(60, 81, 83, 0.5);
	}
}
</style>
