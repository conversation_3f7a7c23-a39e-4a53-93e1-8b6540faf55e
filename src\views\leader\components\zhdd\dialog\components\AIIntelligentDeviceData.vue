<template>
  <div class="wrap">
    <div class="item item1">
      <disc-circle :data="chartData1"></disc-circle>
    </div>
    <div class="item item2">
      <pie-chart3D2 :data="chartData2" :options="options2"></pie-chart3D2>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AIIntelligentDeviceData',
  data() {
    return {
      chartData1: {
        name: '设备总数',
        value: 1000
      },
      chartData2: [
        ['product', '占比'],
        ['在线', 91],
        ['离线', 9]
      ],
      options2: {
        color: ['#00A0FF', '#FF9201', '#4CECAA', '#00F7FF', '#FFD400', '#DCFF5B']
      }
    }
  }
}
</script>

<style lang="less" scoped>
.wrap {
  width: 100%;
  height: 100%;
  display: flex;
  .item {
    position: relative;
    width: 50%;
    height: 100%;
  }
  .item1 {
    padding-top: 25px;
  }
  .item2 {
    padding-top: 15px;
    // background: url('~@/assets/zhdd/bg1.png') no-repeat center / 85% 60%;
  }
  :deep(.pieWorkOrder) {
    width: 100%;
    height: 160px;
    background-position: 0 15px;
    background-size: 100% 80%;
    .charts {
      width: 200px;
    }
  }
}
</style>
