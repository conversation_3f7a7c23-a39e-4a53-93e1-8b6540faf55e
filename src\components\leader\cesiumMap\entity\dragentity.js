/**
 * @param {Viewer} viewer
 *
*/
 class DragEntity {
  constructor(val) {
    this.viewer = val.viewer
  }
  addEntity(value) {
    const pinBuilder = new Cesium.PinBuilder()
    const poin = this.viewer.entities.add({
      id: value.id,
      name: value.name,
      position: Cesium.Cartesian3.fromDegrees(value.position.x, value.position.y, value.position.z),
      billboard: {
        image: value.img,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM
      },
      monitoItems: {
        data: value
      }
    })
    return poin
  }
}
export default DragEntity