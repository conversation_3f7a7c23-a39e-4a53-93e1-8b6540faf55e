<template>
  <div class="basic-info" v-if="show">
    <div class="close" @click="$emit('input', false)"></div>
    <div class="title">
      <span>{{ title }}</span>
    </div>
    <ul class="content">
      <li v-for="(it, i) of list" :key="i">
        <div class="label">{{ it.label }}</div>
        <div class="value" :title="it.value">{{ it.value }}</div>
      </li>
    </ul>
    <div class="btns" v-if="btnShow">
      <div class="btn" v-for="(it, i) of btns" :key="i" @click="$emit('handle', i, title)">
        {{ it }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BasicInformation',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '基本信息',
    },
    list: {
      type: Array,
      require: true,
    },
    btnShow: {
      type: Boolean,
      default: false,
    },
    btns: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  computed: {
    show() {
      return this.value
    },
  },
}
</script>

<style lang="less" scoped>
.basic-info {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  width: 413px;
  padding: 20px 0 47px;
  margin: 0 auto;
  background: url('~@/assets/map/dialog/bg1.png') no-repeat;
  background: url('~@/assets/map/dialog/bg1.png') no-repeat center / 100% 100%;
  box-sizing: border-box;
  .close {
    position: absolute;
    top: 31px;
    right: 43px;
    width: 24px;
    height: 24px;
    background: url('~@/assets/map/dialog/btn2.png') no-repeat;
    box-sizing: border-box;
    cursor: pointer;
  }
  .title {
    width: 375px;
    height: 47px;
    line-height: 47px;
    padding-left: 27px;
    margin: 0 auto;
    text-align: left;
    background: url('~@/assets/map/dialog/title1.png') no-repeat;
    box-sizing: border-box;
    span {
      font-size: 18px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      background: linear-gradient(180deg, #ffffff 0%, #2aa6ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  ul {
    display: flex;
    flex-direction: column;
    gap: 14px;
    margin-top: 20px;
    padding: 0 50px 0 48px;
    box-sizing: border-box;
    li {
      display: flex;
      justify-content: space-between;
      box-sizing: border-box;
      .label {
        width: 90px;
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
        text-align: left;
      }
      .value {
        width: 225px;
        text-align: right;
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-height: 200px;
        overflow-y: scroll;
        padding: 0 10px 0 0;
        &::-webkit-scrollbar {
          /*滚动条整体样式*/
          width: 6px;
          background: transparent;
          // border: 1px solid #999;
          /*高宽分别对应横竖滚动条的尺寸*/
          // height: 1px;
        }

        &::-webkit-scrollbar-thumb {
          /*滚动条里面小方块*/
          border-radius: 2px;
          box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
          background: rgb(45, 124, 228);
          height: 100px;
        }
      }
    }
  }
  .btns {
    margin-top: 38px;
    padding: 0 40px;
    display: flex;
    justify-content: space-evenly;
    gap: 10px;
    .btn {
      width: 100px;
      height: 33px;
      line-height: 33px;
      font-size: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #ffffff;
      background: url('~@/assets/map/dialog/btn1.png') no-repeat center / 100% 100%;
      cursor: pointer;
    }
  }
}
</style>
