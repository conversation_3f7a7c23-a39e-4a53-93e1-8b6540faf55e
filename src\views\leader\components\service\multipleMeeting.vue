<template>
	<div :class="['multi-dialog', isFull ? 'multi-dialog-full' : isExpand ? 'multi-dialog-expand' : 'multi-dialog-other']" v-if="value">
		<div class="dialog-head">
			<div class="dialog-head-left">
				<!-- <div class="theme">主题</div> -->
				<div class="invited" @mouseover="meetingBoardShow = true" @mouseleave="meetingBoardShow = false">
					<span>{{ meetingData.isInvite ? meetingData.roomName : roomName1 }}</span>
					<div class="meeting-show-board" v-show="meetingBoardShow">
						<div class="meeting-show-board-title">{{ roomName1 }}</div>
						<div class="meeting-show-board-item">
							<div class="meeting-show-board-item-title">会议ID</div>
							<div class="meeting-show-board-item-value">
								<span id="roomNum">{{ roomId }}</span>
								<i class="icon clip-icon" @click="clipTextToBoard"></i>
							</div>
						</div>
						<div class="meeting-show-board-item">
							<div class="meeting-show-board-item-title">组织者</div>
							<div class="meeting-show-board-item-value">
								<span>{{ mobile }}</span>
							</div>
						</div>
					</div>
				</div>
				<div class="call-duration">时长： {{ meetingDuration }}</div>
			</div>
			<div class="full-btns">
				<div class="full-btn" @click="minumDialog"></div>
				<div class="full-btn" v-show="false"></div>
				<div class="full-btn" @click="hangOff"></div>
			</div>
		</div>
		<div class="dialog-contain">
			<div id="room-contain" :class="['meeting-contain', layout]">
				<!-- <div v-show="camStatus === 'started'" class="local-stream-container participant">
				</div> -->
				<div
					v-show="remoteScreenViews.length != 0"
					v-for="item in remoteScreenViews"
					:key="item"
					:id="item"
					class="remote-stream-container participant"
				>
					<div class="screen-share-name">{{ item.split('_')[2] }}正在共享屏幕</div>
				</div>
				<div id="local" class="local-stream-content participant" v-show="remoteScreenViews.length == 0">
					<div v-if="!localCameraShow" class="board">
						<div class="board-img"></div>
					</div>
					<div :id="`describe-${tel}`" class="describe">
						<div :id="`microphone-${tel}`" :class="['microphone', microphonePic]"></div>
						<div :id="`${tel}`" class="identity">{{ username }}(我)</div>
					</div>
				</div>
				<!-- <div class="remote-container participant" v-if="remoteUsersViews && remoteUsersViews.length != 0"> -->
				<div
					v-show="remoteScreenViews.length == 0 && !(showRightCam && isExpand)"
					v-for="item in remoteUsersViews"
					:key="item"
					:id="item"
					class="remote-stream-container participant"
				>
					<div v-if="remoteStopVideoUserIdList.has(item.split('_main')[0])" class="board">
						<div class="board-img"></div>
					</div>
					<div :id="`describe-${item.split('_')[1]}`" class="describe">
						<div
							:id="`microphone-${item.split('_')[1]}`"
							:class="[
								'microphone',
								remoteStartAudioUserIdList.has(item.split('_main')[0]) ? 'microphone-active' : 'microphone-inactive',
							]"
						></div>
						<div :id="`${item.split('_')[1]}`" class="identity">{{ item.split('_')[2] }}</div>
					</div>
				</div>
				<!-- </div> -->
			</div>
			<div class="member-manage" v-if="false">
				<div class="member-manage-top">
					<el-input v-model="filterText" placeholder="请输入名字进行搜索">
						<i slot="suffix" class="el-input__icon el-icon-search"></i>
					</el-input>
				</div>
				<div class="member-manage-contain">
					<div class="member-list">
						<div class="member-list-group">
							<div class="avatar"></div>
							<div class="name" :title="username">{{ username }}</div>
						</div>
						<div class="member-list-group">
							<div
								:class="['member-icon', { 'mic-on': localMicrophoneShow }, { 'mic-off': !localMicrophoneShow }]"
								@click="changeLocalMicroStatus"
							></div>

							<div class="member-icon"></div>
						</div>
					</div>
					<div class="member-list" v-for="(item, index) of tongxunluList" :key="'m' + index">
						<div class="member-list-group">
							<div class="avatar"></div>
							<div class="name" :title="item.split('_')[2]">{{ item.split('_')[2] }}</div>
						</div>
						<div class="member-list-group" v-if="role == 'host'">
							<div
								:class="[
									'member-icon',
									{ 'mic-on': remoteStartAudioUserIdList.has(item.split('_main')[0]) },
									{ 'mic-off': !remoteStartAudioUserIdList.has(item.split('_main')[0]) },
								]"
								@click="changeParticipantMicrophone(item)"
							></div>
							<div class="member-icon more" @click.stop="openMoreDialog(item, $event)"></div>
						</div>
					</div>
				</div>
				<!-- <div class="member-manage-contain">
					<div class="member-list" v-for="(item, index) of memberList" :key="'m' + index">
						<div class="member-list-group">
							<div class="avatar"></div>
							<div class="name" :title="item.name">{{ item.name }}</div>
						</div>
						<div class="member-list-group">
							<div
								:class="['member-icon', { 'mic-on': item.isMicrophoneEnabled }, { 'mic-off': !item.isMicrophoneEnabled }]"
								@click="changeParticipantMicrophone(item)"
							></div>
							<div
								class="member-icon more"
								v-if="isMoreOptionShow(item.identity)"
								@click.stop="openMoreDialog(item.identity, $event)"
							></div>
							<div class="member-icon" v-else></div>
						</div>
					</div>
				</div> -->
				<div class="member-manage-bottom" v-if="role === 'host'">
					<div class="member-manage-btn" @click="muteAllMicrophone">全员静音</div>
					<div class="member-manage-btn" @click="closeAllCamera">全员关闭摄像头</div>
				</div>
				<moreOptionDialog
					v-if="moreDialogShow"
					:optionList="moreDialogOptionList"
					:identity="optionIdentity"
					:offset="optionDialogOffset"
					@closeCamera="closeParticipantCamera"
					@changeParticipantName="changeParticipantName"
					@removeParticipant="removeParticipant"
				></moreOptionDialog>
			</div>
			<InstantMessage
				v-if="isInstantMessageShow"
				ref="chatComponent"
				:messageList="messageList"
				:chatStyle="chatStyle"
				:isMonitorIn="true"
				@messageSend="sendMessage"
			></InstantMessage>
		</div>
		<div class="dialog-foot">
			<div class="point-foot-group">
				<div class="foot-btn" v-if="localMicrophoneShow" @click.stop="changeLocalMicroStatus">
					<div class="foot-btn-icon mic-on"></div>
					<div>麦克风</div>
				</div>
				<div class="foot-btn" v-else @click.stop="changeLocalMicroStatus">
					<div class="foot-btn-icon mic-off"></div>
					<div>麦克风</div>
				</div>
				<div class="foot-btn" v-if="localCameraShow" @click.stop="changeLocalCameraStatus">
					<div class="foot-btn-icon cam-on"></div>
					<div>摄像头</div>
				</div>
				<div class="foot-btn" v-else @click.stop="changeLocalCameraStatus">
					<div class="foot-btn-icon cam-off"></div>
					<div>摄像头</div>
				</div>
				<div class="foot-btn" @click.stop="isExpand = !isExpand">
					<div class="foot-btn-icon member"></div>
					<div>成员管理</div>
				</div>
				<div class="foot-btn" @click.stop="chooseInviteWay($event)">
					<div class="foot-btn-icon invite"></div>
					<div>邀请</div>
				</div>
				<div class="foot-btn" v-if="!localScreenShow" @click.stop="handleStartScreenShare">
					<div class="foot-btn-icon screen-on"></div>
					<div>屏幕共享</div>
				</div>
				<div class="foot-btn" v-else @click.stop="handleStartScreenShare">
					<div class="foot-btn-icon screen-off"></div>
					<div>屏幕共享</div>
				</div>
			</div>
			<div class="point-foot-group">
				<div class="foot-btn" @click="hangOff">
					<div class="foot-btn-icon hang-off"></div>
					<div>{{ role == 'host' ? '结束会议' : '挂断' }}</div>
				</div>
			</div>
			<inviteOptionDialog
				v-if="inviteOptionShow"
				:isFull="isFull"
				@openAddressBook="openAddressBook"
				@openMessageBoard="openMessageBoard"
			></inviteOptionDialog>
		</div>
		<changeNameDialog
			v-if="changeNameShow"
			:identity="changingIdentity"
			@updateParticipantName="updateParticipantName(arguments)"
			@changeNameDialogClose="changeNameShow = false"
		></changeNameDialog>
		<messageInviteDialog
			v-if="messageInviteShow"
			@sendMessageInvite="sendMessageInvite"
			@messageInviteClose="messageInviteShow = false"
		></messageInviteDialog>
		<addressBook v-model="addressBookShow" type="invite" @inviteToJoin="inviteToJoin"></addressBook>
		<!-- 请求入会弹窗 -->
		<AgreeBoard v-model="AgreeBoardShow" :inviteBoardData="AgreeInfo" @refuse="refuseAgree" @accept="acceptAgree" v-drag></AgreeBoard>
		<!-- <rightUserDrawer v-model="isExpand"></rightUserDrawer> -->
		<div class="right-user-drawer-wrap" v-if="isExpand">
			<img :src="blockBackgroundImage" alt="" />
			<div class="right-user-drawer">
				<div class="top-label">
					视频会商
				</div>
				<div class="content">
					<div class="select-list-type" @click="showRightCam = !showRightCam" style="cursor: pointer;">
						{{ showRightCam ? '显示成员摄像头' : '仅显示名称' }}
					</div>
					<div class="member-manage-top">
						<el-input v-model="filterText" placeholder="请输入" prefix-icon="el-icon-search"> </el-input>
					</div>
					<div class="member-manage-contain" v-if="!showRightCam">
						<div class="member-list">
							<div class="member-list-group">
								<div class="avatar"></div>
								<div class="name" :title="username">{{ username }}</div>
							</div>
							<div class="member-list-group">
								<div
									:class="['member-icon', { 'mic-on': localMicrophoneShow }, { 'mic-off': !localMicrophoneShow }]"
									@click="changeLocalMicroStatus"
								></div>

								<div class="member-icon"></div>
							</div>
						</div>
						<div class="member-list" v-for="(item, index) of tongxunluList" :key="'m' + index">
							<div class="member-list-group">
								<div class="avatar"></div>
								<div class="name" :title="item.split('_')[2]">{{ item.split('_')[2] }}</div>
							</div>
							<div class="member-list-group" v-if="role == 'host'">
								<div
									:class="[
										'member-icon',
										{ 'mic-on': remoteStartAudioUserIdList.has(item.split('_main')[0]) },
										{ 'mic-off': !remoteStartAudioUserIdList.has(item.split('_main')[0]) },
									]"
									@click="changeParticipantMicrophone(item)"
								></div>
								<!-- <div class="member-icon more" @click.stop="openMoreDialog(item, $event)"></div> -->
								<div
									:class="[
										'member-icon',
										{ 'cam-on': remoteStartVideoUserIdList.has(item.split('_main')[0]) },
										{ 'cam-off': !remoteStartVideoUserIdList.has(item.split('_main')[0]) },
									]"
									@click="closeParticipantCamera(item)"
								></div>
							</div>
						</div>
					</div>
					<div class="member-manage-contain" v-else>
						<div v-for="item in remoteUsersViews" :key="item" :id="`right-${item}`" class="participant participant-right">
							<video
								:id="`right-video-${item}`"
								class="p-video"
								autoplay
								v-show="!remoteStopVideoUserIdList.has(item.split('_main')[0])"
							></video>
							<div v-if="remoteStopVideoUserIdList.has(item.split('_main')[0])" class="board">
								<div class="board-img"></div>
							</div>
							<div :id="`right-describe-${item.split('_')[1]}`" class="describe">
								<div
									:id="`right-microphone-${item.split('_')[1]}`"
									:class="[
										'microphone',
										remoteStartAudioUserIdList.has(item.split('_main')[0]) ? 'microphone-active' : 'microphone-inactive',
									]"
								></div>
								<div :id="`right-${item.split('_')[1]}`" class="identity">{{ item.split('_')[2] }}</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { sendInviteMessage, generateWxLink, getSig, joinRoom, saveRoom, getRoom, endRoom, quitRoom } from '@/api/bjnj/zhdd.js'
import util from '@/libs/util.js'
import rtc from '@/components/mixins/rtc.js'
import TRTC from 'trtc-sdk-v5'
import LibGenerateTestUserSig from '@/utils/lib-generate-test-usersig.min.js'
import AgreeBoard from '@/views/leader/components/service/agreeBoard.vue'
import { isMobile } from '@/utils/utils'
import { WebSocketManager } from './tool/index.js'
import rightUserDrawer from './rightUserDrawer.vue'
let meetingInterval = null
export default {
	name: 'multipleMeeting',
	mixins: [rtc],
	components: {
		moreOptionDialog: () => import('./moreOptionDialog.vue'),
		inviteOptionDialog: () => import('./inviteOptionDialog.vue'),
		changeNameDialog: () => import('./changeNameDialog.vue'),
		messageInviteDialog: () => import('./messageInviteDialog.vue'),
		addressBook: () => import('./addressBook.vue'),
		InstantMessage: () => import('./instantMessage.vue'),
		AgreeBoard,
		rightUserDrawer,
	},
	props: {
		value: {
			type: Boolean,
			default: false,
		},
		allTracks: {
			type: Array,
			default: () => [],
		},
		roomName1: {
			type: String,
			default: '快速会议室',
		},
		meetingData: {
			type: Object,
			default: () => ({}),
		},
		// 发起会议邀请人列表
		launchInviteList: {
			type: Array,
			default: () => [],
		},
		// 用户角色 host-会议创建者 user-普通入会者
		// role: {
		//   type: String,
		//   default: 'host'
		// },
		microphoneStatus: {
			type: Boolean,
			default: true,
		},
		cameraStatus: {
			type: Boolean,
			default: true,
		},
	},
	data() {
		return {
			// 用户角色 host-会议创建者 user-普通入会者
			blockBackgroundImage: require('@/assets/img/block-box/block-yingjishijian-detail-bg.png'),
			role: 'user',
			isFull: false,
			isExpand: false,
			localScreenShow: false,
			// 通话时长
			duration: 0,
			// 房间号
			roomNum: '',
			// 房间名
			roomName: '',
			// 房主identity
			roomHost: '',
			// 组件内client实例
			liveClient: null,
			liveClient2: null,
			// 本地与会者identity
			localIdentity: '',
			// 保存当前页面触发的全部事件
			pageEventList: [],
			// 保存会议中绑定的音视频轨道
			trackList: [],
			// 房间与会者列表
			participants: [],
			// 成员管理列表
			memberList: [],
			// 本地用户设备状态
			localMicrophoneShow: true,
			localCameraShow: true,
			// 成员管理
			filterText: '',
			moreDialogShow: false,
			optionDialogOffset: {},
			optionIdentity: '',
			// 邀请
			inviteOptionShow: false,
			inviteOptionDialogOffset: {},
			// 修改用户名称
			changeNameShow: false,
			changingIdentity: '',
			// 发送短信邀请
			messageInviteShow: false,
			// 打开通讯录
			addressBookShow: false,
			// 即时消息
			// messageList: [
			// 	{
			// 		fromName: '系统消息',
			// 		content: '会议内文本聊天已启用',
			// 	},
			// ],
			chatStyle: {
				width: '295px',
				height: '184px',
			},
			isInstantMessageShow: false,
			meetingBoardShow: false,
			messageReceivedHandlerBinder: null,
			// roomId: 14251,
			sdkAppId: 1600089532,
			sdkSecretKey: '3a6edb36029177424289091eb1d729befde4297a6426c3d6f9e8df97ada4cb0b',
			// userId: 'user_651265',
			userSig: null,
			AgreeInfo: '',
			AgreeBoardShow: false,
			realRoomId: NaN,
			previewTimeInterval: null,
			previewTimeInterval2: null,
			showRightCam: false,
		}
	},
	mounted() {
		this.trtc = TRTC.create()
	},
	computed: {
		microphonePic() {
			return this.localMicrophoneShow ? 'microphone-active' : 'microphone-inactive'
		},
		tel() {
			return this.$store.state.mobile
		},
		userId() {
			return 'user_' + this.$store.state.mobile + '_' + this.$store.state.username
		},
		roomId() {
			return this.meetingData.isInvite
				? Number(this.meetingData.roomId)
				: this.meetingData.isJoin
				? Number(this.meetingData.roomNum)
				: parseInt(Math.random() * 1000000, 10)
		},
		meetingDuration() {
			let minute, second
			if (Math.floor(this.duration / 60) > 0) {
				minute = Math.floor(this.duration / 60) <= 9 ? '0' + Math.floor(this.duration / 60) : Math.floor(this.duration / 60)
			} else {
				minute = '00'
			}
			if (Math.floor(this.duration % 60) > 0) {
				second = Math.floor(this.duration % 60) <= 9 ? '0' + Math.floor(this.duration % 60) : Math.floor(this.duration % 60)
			} else {
				second = '00'
			}
			return `${minute}分${second}秒`
		},
		participantLength() {
			return this.participants.length
		},
		layout() {
			console.log(this.remoteUsersViews.length, '来了几个人？？', this.remoteUsersViews)
			if (this.remoteUsersViews.length <= 1) {
				return 'layout1'
			} /* else if (this.remoteUsersViews.length <= 2) {
				return 'layout2'
			} */ else if (this.remoteUsersViews.length <= 3) {
				return 'layout3'
			} else if (this.remoteUsersViews.length <= 5) {
				return 'layout4'
			} else {
				return 'layout5'
			}
			// if (this.participants.length <= 1) {
			//   return 'layout1'
			// } else if (this.participants.length <= 2) {
			//   return 'layout2'
			// } else if (this.participants.length <= 4) {
			//   return 'layout3'
			// } else if (this.participants.length <= 6) {
			//   return 'layout4'
			// } else {
			//   return 'layout5'
			// }
		},
		moreDialogOptionList() {
			if (this.role === 'host') {
				// return ['关闭摄像头', '修改名称', '移出会议'] //修改名称的功能暂时不做
				return ['关闭摄像头', '移出会议']
			} else {
				// return ['修改名称']
				return []
			}
		},
		username() {
			return this.$store.state.username
		},
		mobile() {
			return this.$store.state.mobile
		},
		tongxunluList() {
			return this.remoteUsersViews.filter((d) => {
				return d.indexOf(this.filterText) > -1
			})
		},
	},
	watch: {
		async value(newVal) {
			if (newVal) {
				this.duration = 0
				this.roomNum = ''
				this.roomName = ''
				this.pageEventList = []
				this.trackList = []
				this.participants = []
				this.memberList = []
				this.messageList = []
				this.filterText = ''
				this.optionIdentity = ''
				this.messageReceivedHandlerBinder = null

				this.startMeeting()
			}
		},
		// 监听showRightCam和isExpand的变化，重新渲染右侧视频
		showRightCam(newVal) {
			this.$nextTick(() => {
				this.updateRightVideoDisplay()
			})
		},
		isExpand(newVal) {
			this.$nextTick(() => {
				this.updateRightVideoDisplay()
			})
		},
		camStatus(newVal) {
			if (newVal === 'stopped') this.localCameraShow = false
		},
		micStatus(newVal) {
			if (newVal === 'stopped') this.localMicrophoneShow = false
		},
		roomStatus(newVal) {
			if (newVal === 'exited') {
				this.closeDialog()
				this.isInstantMessageShow = false
			}
			if (this.isBeiTi) {
				quitRoom({ roomNumber: this.realRoomId, mobile: this.$store.state.mobile }).then(async (res) => {
					this.isBeiTi = false
					this.closeDialog()
					this.isInstantMessageShow = false
					if (this.allTracks && this.allTracks.length != 0) {
						this.$emit('clearTracks')
					}
					if (this.previewTimeInterval) {
						await clearInterval(this.previewTimeInterval)
						this.previewTimeInterval = null
					}
					if (this.previewTimeInterval2) {
						await clearInterval(this.previewTimeInterval2)
						this.previewTimeInterval2 = null
					}
				})
			}
		},
		shareStatus(newVal) {
			if (newVal === 'stopped') {
				this.localScreenShow = false
			}
		},
	},
	// beforeDestroy() {
	//   if(this.liveClient) {
	//     this.clearAllInterval()
	//     this.clearTrack();
	//     this.leaveRoom()
	//   }
	// },
	methods: {
		async startMeeting(e) {
			console.log(this.roomId, 'RoomId', this.launchInviteList)
			this.realRoomId = this.roomId
			// const userSigGenerator = new LibGenerateTestUserSig(
			// 	1600089532,
			// 	'3a6edb36029177424289091eb1d729befde4297a6426c3d6f9e8df97ada4cb0b',
			// 	604800,
			// )
			if (!this.meetingData.isInvite) {
				// this.userSig = userSigGenerator.genTestUserSig(this.userId)
				getSig({
					userid: this.userId,
					expire: 1800, //链接失效时间单位s
					roomid: this.roomId,
					privilegeMap: 255,
				}).then(async (res) => {
					console.log('您您你你你你你你你你你那那那', this.roomId, res)
					this.userSig = res.data
					console.log(this.cameraStatus, this.microphoneStatus, 'this.cameraStatus')
					this.localCameraShow = this.cameraStatus
					this.localMicrophoneShow = this.microphoneStatus

					await this.enterRoom()
					if (this.cameraStatus) this.handleStartLocalVideo()
					if (this.microphoneStatus) this.handleStartLocalAudio()
					if (!this.meetingData.isJoin) {
						// this.liveClient = new WebSocket(
						// 	'wss://dpzs.camtc.cn/ws/meeting-websocket/' + this.roomId + '?phone=' + this.$store.state.mobile,
						// 	// 'ws://172.16.50.211:9019/meeting-websocket/' + this.roomId + '?phone=' + this.$store.state.mobile,
						// )
						// this.initClientEvent()
						// if (this.previewTimeInterval) {
						// 	clearInterval(this.previewTimeInterval)
						// 	this.previewTimeInterval = null
						// }
						// this.previewTimeInterval = setInterval(() => {
						// 	console.log('9885')
						// 	let data = { acceptUser: 'root', type: 0 }
						// 	this.liveClient.send(JSON.stringify(data))
						// }, 20000)
						this.liveClient = new WebSocketManager(
							'wss://dpzs.camtc.cn/ws/meeting-websocket/' + this.roomId + '?phone=' + this.$store.state.mobile,
							{
								onOpen: (event) => {
									console.log('管理员会议室连接已建立')
								},
								onMessage: (event) => {
									console.log('收到消息:', event)
									this.AgreeInfo = event.data
									this.AgreeBoardShow = true
								},
								onClose: (event) => {
									console.log('管理员会议室连接已断开')
								},
								onError: (error) => {
									console.error('WebSocket error:', error)
								},
								reconnectInterval: 1000,
								maxReconnectAttempts: 10,
								heartbeatInterval: 20000,
								heartbeatMsg: JSON.stringify({ acceptUser: 'root', type: 0 }),
							},
						)
						saveRoom({
							roomNumber: this.roomId,
							roomName: this.roomName1,
							mobile: this.$store.state.mobile,
						}).then((res) => {
							this.role = 'host'
						})
					} else {
						this.role = 'user'
						// getRoom(this.roomId).then((res) => {
						// 	this.roomName1 = res.data.roomName
						// })
						joinRoom({
							roomNumber: this.roomId,
							mobile: this.$store.state.mobile,
							name: this.$store.state.username,
						}).then((res) => {})
					}
					this.generateInviteLink()
					meetingInterval = setInterval(() => {
						this.duration++
					}, 1000)
					if (this.launchInviteList.length != 0) {
						//e是电话号码
						console.log('16565454564')
						this.launchInviteList.forEach((d) => {
							if (d.mobile) {
								// const useSig = userSigGenerator.genTestUserSig('user_' + d.mobile)
								let params = {
									toUser: d.mobile,
									// toUser: '17634406498',
									message: encodeURI(
										`${location.origin}${location.pathname}#/invite?roomId=${this.roomId}&userId=user_${d.mobile}&roomName=${this.roomName1}`,
									),
								}
								sendInviteMessage(params).then((res) => {})
							}
						})
						// sendNotice({
						// 	userIds: this.launchInviteList.map((d) => {
						// 		return d.mobile
						// 	}),
						// 	message: this.roomId + '+-' + this.username + '+-' + this.roomName1,
						// }).then(async (res) => {
						// 	console.log('成功调用通知接口')
						// })
						let data = {
							userIds: this.launchInviteList.map((d) => {
								return d.mobile
							}),
							message: this.roomId + '+-' + this.username + '+-' + this.roomName1,
						}
						this.$emit('sendNotice', JSON.stringify(data))
					}
				})
			} else {
				// this.userSig = userSigGenerator.genTestUserSig(this.userId)
				getSig({
					userid: this.userId,
					expire: 1800,
					roomid: this.roomId,
					privilegeMap: 255,
				}).then(async (res) => {
					console.log('您您你你你你你你你你你那那那', this.roomId, res)
					this.userSig = res.data
					console.log(this.userId, this.userSig, this.roomId, this.roomName1, 'this.cameraStatus')

					this.role = 'user'
					this.localCameraShow = this.cameraStatus
					this.localMicrophoneShow = this.microphoneStatus
					await joinRoom({
						roomNumber: this.roomId,
						mobile: this.$store.state.mobile,
						name: this.$store.state.username,
					}).then((res) => {
						// this.liveClient2 = new WebSocket(
						// 	'wss://dpzs.camtc.cn/ws/meeting-websocket/' + this.roomId + '?phone=' + this.$store.state.mobile,
						// 	// 'ws://172.16.50.211:9019/meeting-websocket/' + this.roomId + '?phone=' + this.$store.state.mobile,
						// )
						// this.initClientEvent2()
						this.liveClient2 = new WebSocketManager(
							'wss://dpzs.camtc.cn/ws/meeting-websocket/' + this.roomId + '?phone=' + this.$store.state.mobile,
							{
								onOpen: (event) => {
									console.log('会议室连接已建立')
								},
								onMessage: (event) => {
									console.log('收到消息:', event)
									if (event.data.indexOf('disband') > -1) {
										this.outroom()
										this.$message({
											message: '房主解散了会议',
											type: 'warning',
										})
									} else if (event.data == 'agree') {
										this.joinMeetingShow = false
										this.multipleMeetingShow = true
									} else if (event.data == 'refuse') {
										this.$message({
											message: '管理员拒绝了您的入会请求！',
											type: 'warning',
										})
										this.joinMeetingShow = false
									}
								},
								onClose: (event) => {
									console.log('会议室连接已断开')
								},
								onError: (error) => {
									console.error('WebSocket error:', error)
								},
								reconnectInterval: 1000,
								maxReconnectAttempts: 10,
								heartbeatInterval: 20000,
								heartbeatMsg: JSON.stringify({ acceptUser: 'root', type: 0 }),
							},
						)
						// if (this.previewTimeInterval2) {
						// 	clearInterval(this.previewTimeInterval2)
						// 	this.previewTimeInterval2 = null
						// }
						// this.previewTimeInterval2 = setInterval(() => {
						// 	console.log('9885')
						// 	let data = { acceptUser: 'root', type: 0 }
						// 	this.liveClient2.send(JSON.stringify(data))
						// }, 20000)
					})
					await this.enterRoom()
					if (this.cameraStatus) this.handleStartLocalVideo()
					if (this.microphoneStatus) this.handleStartLocalAudio()
					meetingInterval = setInterval(() => {
						this.duration++
					}, 1000)
				})
			}
			this.isInstantMessageShow = true
		},
		initClientEvent2() {
			if (!this.liveClient2) {
				console.log('客户端未初始化')
				return
			}
			// 连接打开时触发
			this.liveClient2.onopen = (event) => {
				console.log('连接已建立')
			}
			// 接收消息时触发
			this.liveClient2.onmessage = (event) => {
				console.log('收到消息:', event)
				if (event.data.indexOf('disband') > -1) {
					this.outroom()
					this.$message({
						message: '房主解散了会议',
						type: 'warning',
					})
				} else if (event.data == 'agree') {
					this.joinMeetingShow = false
					this.multipleMeetingShow = true
				} else if (event.data == 'refuse') {
					this.$message({
						message: '管理员拒绝了您的入会请求！',
						type: 'warning',
					})
					this.joinMeetingShow = false
				}
			}
			this.liveClient2.onclose = (event) => {
				this.liveClient2 = null
			}
		},
		generateInviteLink() {
			let sdkAppId = 1600089532
			let sdkSecretKey = '3a6edb36029177424289091eb1d729befde4297a6426c3d6f9e8df97ada4cb0b'
			const inviteUserId2 = `user_17634406496`
			const inviteUserId3 = `user_17634406495`
			const inviteUserId4 = `user_17634406494`
			const userSigGenerator = new LibGenerateTestUserSig(sdkAppId, sdkSecretKey, 604800)
			const inviteUserSig2 = userSigGenerator.genTestUserSig(inviteUserId2)
			const inviteUserSig3 = userSigGenerator.genTestUserSig(inviteUserId3)
			const inviteUserSig4 = userSigGenerator.genTestUserSig(inviteUserId4)
			console.log(
				encodeURI(
					`${location.origin}${location.pathname}#/invite?sdkAppId=${sdkAppId}&userSig=${inviteUserSig2}&roomId=${this.roomId}&userId=${inviteUserId2}&roomName=${this.roomName1}`,
				),
				encodeURI(
					`${location.origin}${location.pathname}#/invite?sdkAppId=${sdkAppId}&userSig=${inviteUserSig3}&roomId=${this.roomId}&userId=${inviteUserId3}&roomName=${this.roomName1}`,
				),
				encodeURI(
					`${location.origin}${location.pathname}#/invite?sdkAppId=${sdkAppId}&userSig=${inviteUserSig4}&roomId=${this.roomId}&userId=${inviteUserId4}&roomName=${this.roomName1}`,
				),
				'ttttttttttttt',
			)
		},
		initClientEvent() {
			if (!this.liveClient) {
				console.log('客户端未初始化')
				return
			}
			// 连接打开时触发
			this.liveClient.onopen = (event) => {
				console.log('连接已建立')
				// 发送消息
				// this.liveClient.send('Hello Server!')
			}
			// 接收消息时触发
			this.liveClient.onmessage = (event) => {
				console.log('收到消息:', event)
				this.AgreeInfo = event.data
				this.AgreeBoardShow = true
			}
			this.liveClient.onclose = (event) => {
				this.liveClient = null
			}
		},
		// 最小化窗口
		minumDialog() {
			console.log('el', this.$el, this.$el.style)
			this.$emit('multiMeetingMinum', {
				duration: this.duration,
				cameraStatus: this.localCameraShow,
				microphoneStatus: this.localMicrophoneShow,
			})
			this.$el.style.visibility = 'hidden'
		},
		// 还原窗口
		resetDialog() {
			this.$el.style.visibility = 'visible'
		},

		sendMessage(e) {
			this.messageList.push({
				cmdId: 1,
				data: e,
				seq: 11,
				userId: this.userId,
				timestamp: new Date(),
			})
			this.trtc.sendCustomMessage({
				cmdId: 1,
				data: new TextEncoder().encode(e).buffer,
				timestamp: new Date(),
			})
		},
		isMoreOptionShow(identity) {
			if (this.role === 'host') {
				return true
			} else {
				if (this.localIdentity === identity) {
					return true
				} else {
					return false
				}
			}
		},
		// 创建会议房间
		async createRoom() {
			await this.liveClient.createRoom()
		},
		// 加入会议房间
		async joinMeeting() {
			let joinRoomParams = {
				roomNum: this.roomNum,
				cameraStatus: this.cameraStatus,
				microphoneStatus: this.microphoneStatus,
				echoCancellation: true,
				noiseSuppression: true,
			}
			console.log('加入会议')
			await this.liveClient.joinRoom(joinRoomParams)
		},
		// 修改本地与会者状态
		async changeLocalMicroStatus() {
			const microPhoneList = await TRTC.getMicrophoneList()
			if (microPhoneList[0]) {
				if (this.localMicrophoneShow) this.handleStopLocalAudio()
				else this.handleStartLocalAudio()
				this.localMicrophoneShow = !this.localMicrophoneShow
			} else {
				this.$messageNew.message('warning', {
					message: '未检测到设备存在麦克风',
				})
			}
		},
		async changeLocalCameraStatus() {
			const cameraList = await TRTC.getCameraList()
			if (cameraList[0]) {
				if (this.localCameraShow) this.handleStopLocalVideo()
				else this.handleStartLocalVideo()
				this.localCameraShow = !this.localCameraShow
			} else {
				this.$messageNew.message('warning', {
					message: '未检测到设备存在摄像头',
				})
			}
		},

		reportSuccessEvent(name) {},
		reportFailedEvent(name, error, type = 'rtc') {},

		// 即时消息队列处理函数
		messageReceivedHandler(e) {
			this.messageList.push(e)
		},
		// 邀请发起会议时选择的与会者
		inviteLaunchParticipant() {
			if (this.launchInviteList.length > 0) {
				this.launchInviteList.forEach((item) => {
					// 发送短信邀请,PC邀请
					if (item.mobile) {
						this.sendMessageInvite2(item.mobile)
						this.sendPcInvite(item.mobile)
					}
				})
			}
		},
		// 通讯录邀请
		inviteToJoin(e) {
			if (e.length > 0) {
				//e是电话号码
				console.log('16565454564', e)
				e.forEach((d) => {
					if (d.mobile) {
						let params = {
							toUser: d.mobile,
							// toUser: '17634406498',
							message: encodeURI(
								`${location.origin}${location.pathname}#/invite?roomId=${this.roomId}&userId=user_${d.mobile}&roomName=${this.roomName1}`,
							),
						}
						sendInviteMessage(params).then((res) => {})
					}
				})
				let data = {
					userIds: e.map((d) => {
						return d.mobile
					}),
					message: this.roomId + '+-' + this.username + '+-' + this.roomName1,
				}
				this.$emit('sendNotice', JSON.stringify(data))
			}
			this.addressBookShow = false
		},

		// 更新右侧视频显示
		async updateRightVideoDisplay() {
			// 重新绑定所有远程用户的视频流
			for (const item of this.remoteUsersViews) {
				const userId = item.split('_main')[0]
				// 停止当前的视频流
				try {
					await this.trtc.stopRemoteVideo({ userId, streamType: TRTC.TYPE.STREAM_TYPE_MAIN })
				} catch (error) {
					console.log('停止远程视频失败:', error)
				}

				// 重新开始视频流，绑定到新的容器
				try {
					const viewId = this.getVideoViewId(userId)
					await this.trtc.startRemoteVideo({ userId, streamType: TRTC.TYPE.STREAM_TYPE_MAIN, view: viewId })
				} catch (error) {
					console.log('重新开始远程视频失败:', error)
				}
			}
		},

		// 根据当前显示状态获取视频容器ID
		getVideoViewId(userId) {
			// 如果右侧摄像头显示且展开，则使用右侧容器
			if (this.showRightCam && this.isExpand) {
				return `right-video-${userId}_main`
			}
			// 否则使用中间容器
			return `${userId}_main`
		},
		// 清除组件中的定时器
		clearAllInterval() {
			if (meetingInterval) {
				clearInterval(meetingInterval)
				meetingInterval = null
			}
		},

		// 清除组件中绑定的音视频轨道
		clearTrack() {
			if (this.trackList.length > 0) {
				this.trackList.forEach((track) => {
					track.detach()
					track.stop()
				})
			}
		},
		// 退出会议
		leaveRoom() {
			if (this.liveClient) {
				this.liveClient.leaveRoom()
			}
		},
		// 用户点击挂断
		async hangOff() {
			// this.leaveRoom()
			if (this.localScreenShow) this.localScreenShow = !this.localScreenShow
			if (this.shareStatus !== 'shared') {
				this.shareStatus = 'stopping'
				try {
					await this.trtc.stopScreenShare()
					this.shareStatus = 'stopped'
				} catch (error) {
					this.shareStatus = 'shared'
				}
			}
			if (this.role == 'host')
				endRoom(this.realRoomId + '/1').then(async (res) => {
					let data = {
						type: '2', //0心跳 1申请入会，2被解散
						message: 'disband',
						sendUser: this.$store.state.mobile,
						acceptUser: '',
					}
					console.log(data)
					await this.liveClient.send(JSON.stringify(data))

					this.closeDialog()
					this.isInstantMessageShow = false
					if (this.allTracks && this.allTracks.length != 0) {
						this.$emit('clearTracks')
					}
					if (this.previewTimeInterval) {
						clearInterval(this.previewTimeInterval)
						this.previewTimeInterval = null
					}
					if (this.previewTimeInterval2) {
						clearInterval(this.previewTimeInterval2)
						this.previewTimeInterval2 = null
					}
					this.exitRoom()
				})
			else {
				quitRoom({ roomNumber: this.realRoomId, mobile: this.$store.state.mobile }).then((res) => {
					this.closeDialog()
					this.isInstantMessageShow = false
					if (this.allTracks && this.allTracks.length != 0) {
						this.$emit('clearTracks')
					}
					if (this.previewTimeInterval) {
						clearInterval(this.previewTimeInterval)
						this.previewTimeInterval = null
					}
					if (this.previewTimeInterval2) {
						clearInterval(this.previewTimeInterval2)
						this.previewTimeInterval2 = null
					}
					this.exitRoom()
				})
			}
		},
		outroom() {
			this.closeDialog()
			this.isInstantMessageShow = false
			if (this.allTracks && this.allTracks.length != 0) {
				this.$emit('clearTracks')
			}
			if (this.previewTimeInterval) {
				clearInterval(this.previewTimeInterval)
				this.previewTimeInterval = null
			}
			if (this.previewTimeInterval2) {
				clearInterval(this.previewTimeInterval2)
				this.previewTimeInterval2 = null
			}
			this.exitRoom()
		},
		// 用户管理
		// 控制指定用户麦克风
		changeParticipantMicrophone(e) {
			if (this.remoteStartAudioUserIdList.has(e.split('_main')[0])) {
				this.trtc.sendCustomMessage({
					cmdId: 5, // 1是聊天内容  5关麦克风
					data: new TextEncoder().encode(e).buffer,
				})
			} else {
				this.trtc.sendCustomMessage({
					cmdId: 6, // 1是聊天内容  6开麦克风
					data: new TextEncoder().encode(e).buffer,
				})
			}
		},
		// 点击更多按钮
		openMoreDialog(identity, e) {
			console.log('更多按钮点击', e)
			this.optionDialogOffset = {
				left: e.target.offsetLeft - 32 + 'px',
				top: e.target.offsetTop + 16 + 'px',
			}
			if (this.optionIdentity === identity && this.moreDialogShow) {
				this.moreDialogShow = false
			} else {
				this.moreDialogShow = false
				this.optionIdentity = identity
				this.moreDialogShow = true
			}
		},
		// 修改与会者名称
		changeParticipantName(e) {
			this.moreDialogShow = false
			this.changingIdentity = e
			this.changeNameShow = true
		},
		async updateParticipantName(arg) {
			let [name, identity] = arg
			this.trtc.sendCustomMessage({
				cmdId: 3, // 1是聊天内容  3改名字
				data: new TextEncoder().encode(identity + '_' + name).buffer,
			})
			this.changeNameShow = false
		},
		// 移除与会者
		async removeParticipant(e) {
			this.trtc.sendCustomMessage({
				cmdId: 4, // 1是聊天内容  4踢人
				data: new TextEncoder().encode(e).buffer,
			})
			this.moreDialogShow = false
		},
		// 关闭与会者摄像头
		closeParticipantCamera(e) {
			// userId = user_手机号_main_参会名
			if (this.remoteStartVideoUserIdList.has(e.split('_main')[0])) {
				this.trtc.sendCustomMessage({
					cmdId: 2, // 1是聊天内容  其余都是指令 2关闭摄像头
					data: new TextEncoder().encode(e).buffer,
				})
			} else {
				this.trtc.sendCustomMessage({
					cmdId: 7, // 1是聊天内容  其余都是指令 7开摄像头
					data: new TextEncoder().encode(e).buffer,
				})
			}
		},
		// 全员禁言
		muteAllMicrophone() {
			this.remoteStartAudioUserIdList.clear()
			this.remoteUsersViews.length > 0 &&
				this.remoteUsersViews.forEach((d) => {
					this.remoteStopAudioUserIdList.add(d.split('_main')[0])
					this.trtc.sendCustomMessage({
						cmdId: 5, // 1是聊天内容  5关麦克风
						data: new TextEncoder().encode(d).buffer,
					})
				})
		},
		disableMemberMicrophone(e) {
			if (this.liveClient) {
				this.liveClient.changeParticipantMicrophoneStatus(e, true)
			}
		},
		// 全员关闭摄像头
		closeAllCamera() {
			// this.remoteStopVideoUserIdList.clear()
			this.remoteStartVideoUserIdList.clear()
			this.remoteUsersViews.length > 0 &&
				this.remoteUsersViews.forEach((d) => {
					this.remoteStopAudioUserIdList.add(d.split('_main')[0])
					this.trtc.sendCustomMessage({
						cmdId: 2, // 1是聊天内容  5关麦克风
						data: new TextEncoder().encode(d).buffer,
					})
				})
		},
		// 邀请
		// 点击邀请按钮
		chooseInviteWay() {
			this.inviteOptionShow = !this.inviteOptionShow
		},
		// 打开通讯录
		openAddressBook() {
			this.inviteOptionShow = false
			this.addressBookShow = true
		},
		// 打开短信邀请弹窗
		openMessageBoard() {
			this.inviteOptionShow = false
			this.messageInviteShow = true
		},
		// 发送短信邀请
		sendMessageInvite(e) {
			//e是电话号码
			let params = {
				toUser: e,
				message: encodeURI(
					`${location.origin}${location.pathname}#/invite?roomId=${this.roomId}&userId=user_${e}&roomName=${this.roomName1}`,
				),
			}
			sendInviteMessage(params).then((res) => {
				this.$messageNew.message('success', {
					message: '短信发送成功',
				})
				this.messageInviteShow = false
			})
		},
		// 对被发起人发送短信邀请
		sendMessageInvite2(mobile) {
			let params = {
				toUser: mobile,
				message: ``,
			}
			let params2 = {
				query: encodeURI(`roomNum=${this.roomNum}&tenantId=1478649882954985474&identity=${mobile}`),
			}
			let header = {
				signature: util.encryptWxLink(`roomNum=${this.roomNum}&tenantId=1478649882954985474&identity=${mobile}`),
			}
			generateWxLink(params2, header).then((res) => {
				if (res.code == 0) {
					params.message = res.data['url_link']
					sendInviteMessage(params).then((res) => {
						this.$messageNew.message('success', {
							message: '短信发送成功',
						})
					})
				} else {
					this.$messageNew.message('error', {
						message: '生成小程序链接失败',
					})
				}
			})
		},
		// 向PC登录用户发送邀请 identity-被邀请人姓名
		sendPcInvite(identity) {
			this.liveClient.inviteThirdParty(identity)
		},
		async closeDialog() {
			console.log('关闭多人会议弹窗')
			if (this.liveClient) {
				await this.liveClient.close()
			}
			if (this.liveClient2) {
				await this.liveClient2.close()
			}
			this.clearAllInterval()
			this.clearTrack()

			this.$emit('multiMeetingClose')
			this.$emit('input', false)
		},
		clipTextToBoard() {
			let dom = document.getElementById('roomNum')
			if (dom) {
				if (navigator.clipboard) {
					let content = dom.innerHTML
					navigator.clipboard
						.writeText(content)
						.then(() => {
							this.$messageNew.message('success', {
								message: '已复制到剪贴板',
							})
						})
						.catch((err) => {
							console.error('复制失败', err)
						})
				} else {
					this.$messageNew.message('warning', {
						message: '当前浏览器不支持复制内容',
					})
				}
			}
		},
		refuseAgree() {
			let data = {
				type: '1', //1申请入会，2被解散
				message: 'refuse',
				sendUser: this.$store.state.mobile,
				acceptUser: this.AgreeInfo.split('_')[0],
			}
			this.liveClient.send(JSON.stringify(data))
			this.AgreeBoardShow = false
		},
		acceptAgree() {
			let data = {
				type: '1', //1申请入会，2被解散
				message: 'agree',
				sendUser: this.$store.state.mobile,
				acceptUser: this.AgreeInfo.split('_')[0],
			}
			this.liveClient.send(JSON.stringify(data))
			this.AgreeBoardShow = false
		},
		async handleStartScreenShare() {
			TRTC.isSupported().then(async (checkResult) => {
				const { isScreenShareSupported } = checkResult.detail
				if (!isScreenShareSupported) {
					// SDK 不支持当前浏览器，引导用户使用最新版的 Chrome 浏览器。
					this.$confirm(
						'当前浏览器不支持屏幕共享功能，建议使用最新版的 Chrome 浏览器！下载地址：https://www.google.cn/intl/zh-CN/chrome/',
						'提示',
						{
							showConfirmButton: false,
							cancelButtonText: '好的',
							type: 'warning',
						},
					).then(() => {})
				} else {
					if (this.remoteScreenViews.length != 0) {
						this.$message.warning('有人正在共享屏幕！请稍后再试...')
						return
					}
					if (!this.localScreenShow) {
						this.localScreenShow = !this.localScreenShow
						this.shareStatus = 'sharing'
						try {
							await this.trtc.startScreenShare()
							this.shareStatus = 'shared'
						} catch (error) {
							this.shareStatus = 'stopped'
						}
					} else {
						this.localScreenShow = !this.localScreenShow
						if (this.shareStatus !== 'shared') {
							return
						}
						this.shareStatus = 'stopping'
						try {
							await this.trtc.stopScreenShare()
							this.shareStatus = 'stopped'
						} catch (error) {
							this.shareStatus = 'shared'
						}
					}
				}
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.multi-dialog {
	width: 1236px;
	height: 822px;
	position: absolute;
	z-index: 3000;
	left: 235px;
	top: 186px;
	// background: url('~@/assets/service/multi1.png') no-repeat center / 100% 100%;
	// &-expand {
	// 	width: 1522px !important;
	// 	background: url('~@/assets/service/multi2.png') no-repeat center / 100% 100%;
	// }
	.dialog-head {
		height: 48px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 40px 0 38px;
		background: #104993;
		&-left {
			display: flex;
			align-items: center;
			.theme {
				width: 72px;
				line-height: 32px;
				text-align: center;
				background: #59a1ff;
				border-radius: 16px;
				font-family: Source Han Sans CN;
				font-weight: 400;
				font-size: 20px;
				color: #ffffff;
				margin-right: 16px;
			}
			.invited {
				// font-family: YouSheBiaoTiHei;
				font-size: 16px;
				color: #ffffff;
				line-height: 36px;
				margin-right: 38px;
				padding-right: 21px;
				position: relative;
				cursor: pointer;
				&::after {
					content: '';
					position: absolute;
					right: 0;
					top: 17px;
					width: 12px;
					height: 6px;
					background: url('~@/assets/service/to-bottom.png') no-repeat center / 100% 100%;
				}
				.meeting-show-board {
					position: absolute;
					z-index: 5000;
					left: 0;
					top: 36px;
					width: 392px;
					border-radius: 8px;
					background: linear-gradient(to bottom, rgba(66, 130, 208, 0.9) 0%, rgba(17, 48, 88, 0.9) 100%);
					padding: 21px 31px;
					&-title {
						font-family: YouSheBiaoTiHei;
						font-size: 28px;
						color: #ffffff;
						line-height: 36px;
						text-align: left;
						margin-bottom: 18px;
					}
					&-item {
						width: 100%;
						display: flex;
						align-items: flex-start;
						flex-wrap: nowrap;
						font-family: Source Han Sans CN;
						font-weight: 400;
						font-size: 14px;
						color: #ffffff;
						line-height: 21px;
						margin-bottom: 30px;
						&-title {
							flex-shrink: 0;
							width: 72px;
							text-align: left;
						}
						&-value {
							flex-shrink: 0;
							width: calc(100% - 72px);
							text-align: left;
							.icon {
								display: inline-block;
								width: 12px;
								height: 12px;
								cursor: pointer;
								margin-left: 10px;
							}
							.clip-icon {
								background: url('~@/assets/service/clip-icon.png') no-repeat center / 100% 100%;
							}
						}
					}
				}
			}
			.call-duration {
				// font-family: Source Han Sans CN;
				font-weight: 400;
				font-size: 16px;
				color: #cbe5ff;
				line-height: 21px;
				color: #d0deee;
			}
		}
		.full-btns {
			display: flex;
			align-items: center;
			.full-btn {
				&:not(:last-child) {
					margin-right: 10px;
				}
				width: 32px;
				height: 32px;
				cursor: pointer;
				&:nth-child(1) {
					background: url('~@/assets/service/slide.png') no-repeat center / 100% 100%;
				}
				&:nth-child(2) {
					background: url('~@/assets/service/full1.png') no-repeat center / 100% 100%;
				}
				&:nth-child(3) {
					background: url('~@/assets/service/close.png') no-repeat center / 100% 100%;
				}
			}
		}
	}
	.dialog-contain {
		height: calc(100% - 144px);
		display: flex;
		flex-wrap: nowrap;
		position: relative;
		overflow: hidden;
		.meeting-contain {
			width: 1236px;
			height: 100%;
			// padding: 8px;
			-ms-overflow-style: none;
			scrollbar-width: none;
			&::-webkit-scrollbar {
				display: none; /* Chrome Safari */
			}
		}
		.member-manage {
			margin-right: 13px;
			width: calc(100% - 1249px);
			height: 100%;
			background: #0a3368;
			border-radius: 5px;
			padding: 20px;
			&-top {
				width: 100%;
				display: flex;
				justify-content: center;
				margin-bottom: 16px;
				::v-deep {
					.el-input {
						width: 237px !important;
						.el-input__inner {
							height: 36px;
							line-height: 36px;
							border: 1px solid #cbe5ff !important;
							background-color: transparent !important;
							font-size: 16px !important;
							color: #ffffff !important;
							font-family: Source Han Sans CN;
							font-weight: 400;
						}
						.el-input__icon {
							line-height: 36px;
							color: #00aaff;
						}
					}
				}
			}
			&-contain {
				width: 100%;
				height: calc(100% - 100px);
				overflow-y: auto;
				&::-webkit-scrollbar {
					/*滚动条整体样式*/
					width: 0px;
					background: transparent;
				}
				&::-webkit-scrollbar-thumb {
					/*滚动条里面小方块*/
					border-radius: 2px;
					box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
					background: rgb(45, 124, 228);
					height: 100px;
				}
				.member-list {
					width: 100%;
					display: flex;
					align-items: center;
					justify-content: space-between;
					&-group {
						display: flex;
						align-items: center;
						flex-wrap: nowrap;
						.avatar {
							width: 32px;
							height: 32px;
							background: url('~@/assets/service/avatar.png') no-repeat center / 100% 100%;
							margin-right: 10px;
						}
						.name {
							font-family: Source Han Sans CN;
							font-weight: 400;
							font-size: 16px;
							color: #ffffff;
							line-height: 24px;
							white-space: nowrap;
							width: 140px;
							overflow: hidden;
							text-align: left;
							text-overflow: ellipsis;
						}
						.member-icon {
							width: 16px;
							height: 16px;
							cursor: pointer;
							&:not(:last-child) {
								margin-right: 8px;
							}
						}
						.mic-on {
							background: url('~@/assets/service/mic-on.png') no-repeat center / 100% 100%;
						}
						.mic-off {
							background: url('~@/assets/service/mic-off.png') no-repeat center / 100% 100%;
						}
						.more {
							background: url('~@/assets/service/more.png') no-repeat center / 100% 100%;
						}
					}
					&:not(:last-of-type) {
						margin-bottom: 20px;
					}
				}
			}
			&-bottom {
				width: 100%;
				height: 50px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				.member-manage-btn {
					line-height: 36px;
					padding: 0 10px;
					background: url('~@/assets/service/board-btn-bg.png') no-repeat center / 100% 100%;
					cursor: pointer;
					font-family: Source Han Sans CN;
					font-weight: 400;
					font-size: 16px;
					color: #ffffff;
				}
			}
		}
	}
	.dialog-foot {
		height: 96px;
		// margin-bottom: 5px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 43px 0 20px;
		position: relative;
		background: #062855;
		border: 1px solid rgba(255, 255, 255, 0.15);

		.point-foot-group {
			display: flex;
			align-items: center;
			flex-wrap: nowrap;
			.foot-btn {
				display: flex;
				align-items: center;
				padding: 0 32px;
				font-family: Source Han Sans CN;
				font-weight: 400;
				font-size: 14px;
				color: #ffffff;
				cursor: pointer;
				&:not(:last-of-type) {
					border-right: 1px solid;
					border-image: linear-gradient(180deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.1)) 1 1;
				}
				&-icon {
					width: 28px;
					height: 28px;
					margin-right: 6px;
				}
				.mic-on {
					background: url('~@/assets/service/mic-on.png') no-repeat center / 100% 100%;
				}
				.mic-off {
					background: url('~@/assets/service/mic-off.png') no-repeat center / 100% 100%;
				}
				.cam-on {
					background: url('~@/assets/service/cam-on.png') no-repeat center / 100% 100%;
				}
				.cam-off {
					background: url('~@/assets/service/cam-off.png') no-repeat center / 100% 100%;
				}
				.member {
					background: url('~@/assets/service/member.png') no-repeat center / 100% 100%;
				}
				.invite {
					background: url('~@/assets/service/invite.png') no-repeat center / 100% 100%;
				}
				.screen-on {
					background: url('~@/assets/service/screen-on.png') no-repeat center / 100% 100%;
				}
				.screen-off {
					background: url('~@/assets/service/screen-off.png') no-repeat center / 100% 100%;
				}
				.hang-off {
					background: url('~@/assets/service/hang-off.png') no-repeat center / 100% 100%;
				}
			}
		}
	}
}
.local-stream-content {
	width: 100%;
	height: 100%;
}
.remote-stream-container {
	width: 100%;
	height: 100%;
	// width: 320px;
	// height: 240px;
	// margin: 0 10px 10px 0;
	.screen-share-name {
		position: absolute;
		width: 100%;
		text-align: center;
		font-size: 17px;
		font-weight: bold;
	}
}
.right-user-drawer-wrap {
	position: absolute;
	right: -377px;
	// right: -341px;
	top: -77px;
	width: 360px;
	height: 947px;
	img {
		width: 100%;
		height: 100%;
	}
	.right-user-drawer {
		position: absolute;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;

		.top-label {
			font-weight: 800;
			font-size: 18px;
			line-height: 22px;
			letter-spacing: 2px;
			text-shadow: 0px 0px 5px rgba(45, 205, 255, 0.3);
			font-style: normal;
			text-transform: none;
			text-align: left;
			background-image: linear-gradient(180deg, #2897ff 0%, #ffffff 100%);
			-webkit-background-clip: text;
			color: transparent;
			box-sizing: border-box;
			padding-left: 24px;
			padding-top: 12px;
		}
		.content {
			width: calc(100% - 7px);
			height: 900px;
			margin-top: 12px;
			background: #001537;
			margin-left: 6px;
			padding: 15px;
			box-sizing: border-box;
			// overflow-y: auto;
			// &::-webkit-scrollbar {
			// 	/*滚动条整体样式*/
			// 	width: 0px;
			// 	background: transparent;
			// }
			// &::-webkit-scrollbar-thumb {
			// 	/*滚动条里面小方块*/
			// 	border-radius: 2px;
			// 	box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
			// 	background: rgb(45, 124, 228);
			// 	height: 100px;
			// }
			.select-list-type {
				height: 32px;
				line-height: 32px;
				padding-left: 8px;
				font-size: 14px;
				color: #d0deee;
				text-align: left;
				margin-bottom: 14px;
			}
			.member-manage-top {
				width: 100%;
				display: flex;
				justify-content: center;
				margin-bottom: 16px;
				::v-deep {
					.el-input {
						width: 330px !important;
						.el-input__inner {
							height: 32px;
							line-height: 32px;
							border: 1px solid #004c85 !important;
							background-color: transparent !important;
							font-size: 16px !important;
							// color: #ffffff !important;
							font-family: Source Han Sans CN;
							font-weight: 400;
						}
						.el-input__icon {
							line-height: 32px;
							color: rgba(176, 224, 255, 1);
						}
					}
				}
			}
			.member-manage-contain {
				width: 100%;
				height: 792px;
				overflow-y: auto;
				&::-webkit-scrollbar {
					/*滚动条整体样式*/
					width: 0px;
					background: transparent;
				}
				&::-webkit-scrollbar-thumb {
					/*滚动条里面小方块*/
					border-radius: 2px;
					box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
					background: rgb(45, 124, 228);
					height: 100px;
				}
				.member-list {
					width: 100%;
					display: flex;
					align-items: center;
					justify-content: space-between;
					&-group {
						display: flex;
						align-items: center;
						flex-wrap: nowrap;
						.avatar {
							width: 32px;
							height: 32px;
							background: url('~@/assets/service/avatar.png') no-repeat center / 100% 100%;
							margin-right: 13px;
						}
						.name {
							font-family: Source Han Sans CN;
							font-weight: 400;
							font-size: 14px;
							color: #80caff;
							line-height: 24px;
							white-space: nowrap;
							width: 140px;
							overflow: hidden;
							text-align: left;
							text-overflow: ellipsis;
						}
						.member-icon {
							width: 20px;
							height: 20px;
							cursor: pointer;
							&:not(:last-child) {
								margin-right: 16px;
							}
						}
						.mic-on {
							background: url('~@/assets/service/mic-on.png') no-repeat center / 100% 100%;
						}
						.mic-off {
							background: url('~@/assets/service/mic-off.png') no-repeat center / 100% 100%;
						}
						.cam-on {
							background: url('~@/assets/service/cam-on.png') no-repeat center / 100% 100%;
						}
						.cam-off {
							background: url('~@/assets/service/cam-off.png') no-repeat center / 100% 100%;
						}
						.more {
							background: url('~@/assets/service/more.png') no-repeat center / 100% 100%;
						}
					}
					&:not(:last-of-type) {
						margin-bottom: 20px;
					}
				}
				.participant-right {
					width: 329px;
					height: 185px;
				}
			}
		}
	}
}
</style>
<style lang="scss">
@import './style/multipleMeetingStyle.scss';
</style>
