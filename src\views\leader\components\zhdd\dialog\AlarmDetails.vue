<template>
  <div class="event-analysis" v-if="show">
    <div class="close" @click="$emit('input', false)"></div>
    <div class="title">
      <span>AI智能事件分析</span>
    </div>
    <div class="content">
      <div class="left">
        <BlockBox
          title="AI智能设备数据"
          subtitle="AI intelligent device data"
          :isListBtns="false"
          :blockHeight="255"
        >
          <AIIntelligentDeviceData />
        </BlockBox>
        <BlockBox
          title="AI算法使用率TOP5"
          subtitle="AI computing power utilization rate is TOP5"
          :isListBtns="false"
          :blockHeight="346"
        >
          <AIComputingPowerUtilizationRate />
        </BlockBox>
      </div>
      <div class="right">
        <BlockBox
          title="AI智能告警数据"
          subtitle="AI intelligent alarm data"
          :isListBtns="false"
          :blockHeight="285"
        >
          <AIIntelligentAlarmData />
        </BlockBox>
        <BlockBox title="AI智能告警TOP5" :blockHeight="315" :textArr="['按类型', '按区域']">
          <RankBarChartE02 :data="chartData" :options="options" :initOption="initOption" />
        </BlockBox>
      </div>
    </div>
  </div>
</template>

<script>
import BlockBox from '@/components/leader/common/blockBox.vue'
import AIIntelligentDeviceData from './components/AIIntelligentDeviceData.vue'
import AIComputingPowerUtilizationRate from './components/AIComputingPowerUtilizationRate.vue'
import AIIntelligentAlarmData from './components/AIIntelligentAlarmData.vue'
export default {
  name: 'AlarmDetails',
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  components: {
    BlockBox,
    AIIntelligentDeviceData,
    AIComputingPowerUtilizationRate,
    AIIntelligentAlarmData
  },
  computed: {
    show() {
      return this.value
    }
  },
  data() {
    return {
      chartData: [
        ['name', 'value'],
        ['机动车违停', '35'],
        ['店外经营', '12'],
        ['人员滞留', '11'],
        ['违禁捕鱼', '9'],
        ['外来人员', '8']
      ],
      options: {
        color: ['#F8753A', '#D28900', '#3AF8DF', '#00A5FF', '#00A5FF']
      },
      initOption: {}
    }
  }
}
</script>

<style lang="less" scoped>
.event-analysis {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 1135px;
  height: 783px;
  padding: 22px 76px 0 75px;
  background: url('~@/assets/zhdd/bg10.png') no-repeat;
  z-index: 2000;
  .close {
    position: absolute;
    width: 44px;
    height: 44px;
    top: 38px;
    right: 43px;
    background: url('~@/assets/zhdd/close2.png') no-repeat;
    cursor: pointer;
  }
  .title {
    display: grid;
    place-items: center;
    width: 634px;
    height: 74px;
    margin: 0 auto 26px;
    background: url('~@/assets/zhdd/title2.png') no-repeat;
    span {
      height: 36px;
      font-size: 36px;
      font-family: PingFangSC, PingFang SC;
      line-height: 36px;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .content {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
}
</style>
