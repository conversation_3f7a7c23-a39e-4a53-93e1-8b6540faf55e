<template>
  <div
    class="table-wrapper"
    :style="{
      height: `calc(${contentHeight})`
    }"
  >
    <div class="table-header" v-if="showHeader">
      <span
        v-for="(it, i) in widths"
        :key="i"
        :style="{ width: widths[i], padding: headerSpanPadding,color:'rgba(255,255,255,0.9)' }"
      >
        <div class="row-item" :style="{ padding: headerSpanPadding }">{{ titles[i] }}</div></span
      >
    </div>

    <div class="table-content" :style="{ height: `calc(${contentHeight} - 36px)` }">
      <swiper class="swiper" :options="swiperOption" ref="myBotSwiper" v-if="dataList.length">
        <swiper-slide v-for="(it, i) in dataList" :key="i">
          <div
            class="table-row"
            :class="{ stripe: i % 2 === 1, active: i === currentActiveRow }"
            @click="handleRowClick(it, i)"
          >
            <div
              class="col"
              v-for="(ite, ind) in widths"
              :key="ind"
              :title="it[ind]"
              :style="{
                width: widths[ind],
                padding: spanPadding,
                fontSize: rowFontSize
              }"
            >
              <div class="item_span" :class="it[ind] == '待处置' ? 'item_span1' :(it[ind] == '进行中'||it[ind] == '已完成'? 'item_span2':'')">
                {{ it[ind] }}
              </div>
            </div>
          </div>
        </swiper-slide>
      </swiper>
      <img v-else class="zwsjImg" src="@/assets/bjnj/zwsj.png" alt="">
    </div>
  </div>
</template>

<script>
export default {
  name: 'SwiperTable',
  props: {
    data: {
      type: Array,
      default: () => [
        ['区工贸领域白日攻坚方案'],
        ['滨江开发区工贸检维修管理规定'],
        ['区工贸领域白日攻坚方案'],
        ['滨江开发区工贸检维修管理规定'],
        ['区工贸领域白日攻坚方案']
      ]
    },
    titles: {
      type: Array,
      default: () => ['文件名称', '操作']
    },
    widths: {
      type: Array,
      default: () => ['70%', '30%']
    },
    contentHeight: {
      type: String,
      default: '220px'
    },
    showHeader: {
      type: Boolean,
      default: true
    },
    spanPadding: {
      type: String,
      default: '0 8px'
    },
    headerSpanPadding: {
      type: String,
      default: '0 15px'
    },
    rowFontSize: {
      type: String,
      default: '14px'
    },
    settled: {
      type: Boolean,
      default: true
    },
    isNeedOperate: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    // 将手机号的中间四位隐藏
    dataList() {
      return this.data.map(it => {
        // let [name, mobile, time, totals, bfb, qwer] = it;
        // const reg = /^(\d{3})\d{4}(\d{4})$/;
        // mobile = mobile.replace(reg, '$1****$2');

        // 不需要隐藏手机号码就隐去上面的代码
        let qwer = it
        return qwer
      })
    },
    myBotSwiper() {
      return this.$refs.myBotSwiper.$swiper
    }
  },
  data() {
    return {
      currentActiveRow: 999,
      swiperOption: {
        autoHeight: true,
        direction: 'vertical',
        spaceBetween: 0,
        autoplay: {
          delay: 2500,
          disableOnInteraction: false,
          autoplayDisableOnInteraction: false
        },
        slidesPerView: 'auto',
        grabCursor: true,
        autoplayDisableOnInteraction: false,
        mousewheelControl: true
      }
    }
  },
  methods: {
    handleRowClick(it, i) {
      if (this.currentActiveRow == i) {
        this.currentActiveRow = 999
        this.swiperStart()
      } else {
        this.currentActiveRow = i
        this.swiperStop()
      }
      const currentIt = this.data[i]
      // this.swiperStop();
      this.$emit('change', currentIt,i)
    },
    swiperStop() {
      this.myBotSwiper.autoplay.stop()
    },
    swiperStart() {
      this.myBotSwiper.autoplay.start()
    },
    dealMethod() {
      console.log(111)
      this.$parent.dealMethod()
    },
    dwMethod() {
      console.log(222)
      this.$parent.dwMethod()
    }
  },
  watch: {
    data() {
      this.currentActiveRow = 0
    }
  }
}
</script>

<style lang="scss" scoped>
.table-wrapper {
  width: 100%;
  height: 100%;
  span {
    box-sizing: border-box;
  }

  .table-header {
    width: 100%;
    height: 36px;
    display: flex;
    background: rgba(0, 101, 183, 0.3);

    span {
      padding: 0px !important;
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      line-height: 36px;
      font-weight: 400;
      color: #37c1ff;
      line-height: 20px;

      .row-item {
        position: absolute;
        padding: 0 15px;
        padding: 0px !important;
        top: 50%;
        transform: translateY(-50%);
        overflow: hidden;
        text-overflow: ellipsis; /* 文本溢出时显示省略号来代表被修剪的文本 */
        white-space: normal;
        display: -webkit-box; /* 必须结合的属性 ，将对象作为弹性伸缩盒子模型显示 */
        -webkit-box-orient: vertical; /* 必须结合的属性 ，设置或检索伸缩盒对象的子元素的排列方式 */
        -webkit-line-clamp: 2; /* 文本需要显示多少行 */
        word-break: break-all;
      }

      &:nth-child(2) {
        text-align: center;
      }
    }

    span + span {
      margin-left: 2px;
    }
  }

  .table-content {
    width: 100%;
    height: 284px;

    .swiper {
      width: 100%;
      height: 100%;

      .table-row {
        width: 100%;
        height: 36px;
        line-height: 36px;
        cursor: pointer;
        display: flex;

        &.active {
          .col {
            color: rgba(131, 194, 238, 1);
            font-size: 14px;
            font-weight: bold;
          }
        }

        &.stripe {
          background: rgba(0, 101, 183, 0.15);
        }

        .col {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: rgba(255, 255, 255, 0.85);
          display: grid;
          place-items: center;
          box-sizing: border-box;
        }

        .swiper-item {
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .zwsjImg{
      width: 100px;
      margin: 20px 0;
    }
  }

  span {
    height: 100%;
    // line-height: 100%;
    // display: inline-block;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 26px;
    display: flex;
    justify-content: center;
    position: relative;

    .row-point {
      display: flex;
      align-items: center;
      justify-content: center;

      .red-point {
        color: #08bf8a;
      }

      .green-point {
        color: #ff9090;
      }
    }

    .row-item {
      position: absolute;
      padding: 0 15px;
      top: 50%;
      transform: translateY(-50%);
      overflow: hidden;
      text-overflow: ellipsis; /* 文本溢出时显示省略号来代表被修剪的文本 */
      white-space: normal;
      display: -webkit-box; /* 必须结合的属性 ，将对象作为弹性伸缩盒子模型显示 */
      -webkit-box-orient: vertical; /* 必须结合的属性 ，设置或检索伸缩盒对象的子元素的排列方式 */
      -webkit-line-clamp: 2; /* 文本需要显示多少行 */
      word-break: break-all;
    }
  }
  .icon_ {
    display: flex;
    margin: 0 0 0 12px;
    display: flex;
    align-items: center;
    img {
      width: 12px;
      height: 16px;
      margin-left: 2px;
    }
  }
}
.item_span {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
  &.active {
    color: #29d241;
  }
}
.item_span1 {
  color: #F93A4A;
}
.item_span2 {
  color: #08DFFD;
}
.item_spanLast {
  display: flex;
  align-items: center;
  gap: 7px;
  .detail {
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #43b3ff;
  }
  .sign_for {
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #82e16a;
  }
  .locate {
    color: #1efff9;
  }
}
</style>
