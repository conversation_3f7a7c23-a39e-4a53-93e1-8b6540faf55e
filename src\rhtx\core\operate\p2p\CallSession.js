import Session from '../base/Session';
import P2P_EVENT from './event';
import api from '../../api/index';
import { CONNECT_STATE } from '../../dict/index';

export default class CallSession extends Session {
  constructor(
    { publishPcInfo, playerPcInfo, call, userInfo, isMineCaller },
    event
  ) {
    super({ event });
    this.userInfo = userInfo;
    this.publishPcInfo = publishPcInfo;
    this.playerPcInfo = playerPcInfo;
    this.call = call;
    this.controls = {
      mute: false,
      deaf: false
    };
    this.active = true; //这轮通话还是否存活
    this.connectState = CONNECT_STATE.READY;
    this.isMineCaller = isMineCaller || false; //呼叫方法内部会传入true   不传就是对方呼叫我
  }

  getTarget() {
    return this.call.caller === this.userInfo.user.phone
      ? this.call.callee
      : this.call.caller;
  }

  muted() {
    this.controls.mute = true;
    this.fire(P2P_EVENT.CONTROLS_UPDATE, this.controls); //以后可以改成观察者
  }

  cancelMuted() {
    this.controls.mute = false;
    this.fire(P2P_EVENT.CONTROLS_UPDATE, this.controls);
  }

  deaf() {
    this.checkMediaSouce();
    this.publishPcInfo.closeVoice();
    this.controls.deaf = true;
    this.fire(P2P_EVENT.CONTROLS_UPDATE, this.controls);
  }

  cancelDeaf() {
    this.checkMediaSouce();
    this.publishPcInfo.openVoice();
    this.controls.deaf = false;
    this.fire(P2P_EVENT.CONTROLS_UPDATE, this.controls);
  }

  replaceCamera(deivceId) {
    this.checkMediaSouce();
    this.publishPcInfo.replaceCamera(deivceId);
  }

  terminate() {
    // 终止通话   数据删除是在ws通知后删除
    if (this.connectState === CONNECT_STATE.END) return;
    this.connectState = CONNECT_STATE.END;
    if (this.config.isMediaSource) {
      api.sysCallHangup({
        user_id: this.userInfo.user.id,
        uuid: this.call.uuid
      });
    } else {
      this.publishPcInfo && this.publishPcInfo.terminate();
      this.playerPcInfo && this.playerPcInfo.terminate();
    }
    this.publishPcInfo = null;
    this.playerPcInfo = null;
  }

  //视频源检测
  checkMediaSouce() {
    if (this.config.isMediaSource) {
      console.warn(`视频源模式下无法操作`);
      return;
    }
  }

  // 停止推流
  stopPublish() {
    if (this.connectState === CONNECT_STATE.END) return;
    this.publishPcInfo && this.publishPcInfo.terminate();
    this.playerPcInfo && this.playerPcInfo.terminate();
    this.publishPcInfo = null;
    this.playerPcInfo = null;
  }
}
