<template>
	<div class="child-block" :style="{ height: blockHeight + 'px' }">
		<div class="content">
			<slot></slot>
			<div class="cont">
				<el-timeline>
					<el-timeline-item placement="top" v-for="(it, i) of data4" :key="i" :color="it.choice == '新增' ? '#05EE99' : '#05CEEE'">
						<div class="czlcTop">
							<div
								class="czlcLeft"
								:style="{
									background: it.choice == '新增' ? '#05332A' : '#13314D',
									color: it.choice == '新增' ? '#00FFC0' : '#00EAFF',
								}"
							>
								{{ it.choice }}
							</div>
							<div class="czlcRight" :style="{ color: it.choice == '新增' ? '#00FFC0' : '#00EAFF' }">
								{{ it.createDatetime }}
							</div>
						</div>
						<div class="czlcBox">
							<div class="czlcList">
								<div class="czlcLeft"><span>*</span>发布人：</div>
								<div class="czlcRight" :title="it.departName">{{ it.departName }}</div>
							</div>
							<div class="czlcList">
								<div class="czlcLeft"><span>*</span>处理意见：</div>
								<div class="czlcRight" :title="it.comments">{{ it.comments }}</div>
							</div>
						</div>
					</el-timeline-item>
				</el-timeline>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		contentNumberUnitOne: {
			type: String,
			default: '万',
		},

		contentDataList: {
			type: Array,
			default: () => [],
		},

		blockHeight: {
			type: Number,
			default: 350,
		},

		data4: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {}
	},
	computed: {},
	methods: {},
}
</script>

<style lang="less" scoped>
.child-block {
	width: 455px;
	box-sizing: border-box;
	z-index: 101;
	position: relative;
	padding: 16px 16px 0 22px;

	.content {
		position: relative;
		width: 100%;
		height: 100%;
		// height: calc(100% - 74px);
		display: flex;
		justify-content: flex-start;
		flex-wrap: wrap;
		align-items: center;
		overflow: auto;

		.cont {
			width: 100%;
			height: 100%;
			padding-top: 19px;
			padding-left: 3px;
			overflow-y: auto;
			&::-webkit-scrollbar {
				width: 4px; /* 滚动条宽度 */
			}

			//轨道
			&::-webkit-scrollbar-track {
				background: #04244e; /* 滚动条轨道背景 */
				border-radius: 10px;
				padding: 5px 0;
			}

			//滑块
			&::-webkit-scrollbar-thumb {
				background: #82aed0; /* 滑块背景颜色 */
				border-radius: 10px;
				height: 15px;
			}

			//悬停滑块颜色
			&::-webkit-scrollbar-thumb:hover {
				background: #ff823b; /* 鼠标悬停时滑块颜色 */
			}

			//箭头
			&::-webkit-scrollbar-button {
				display: none; /* 隐藏滚动条的箭头按钮 */
			}

			&.no-active-content {
				height: 0;
				display: none;
			}

			.czlcTop {
				overflow: hidden;
				position: relative;
				top: -12px;

				.czlcLeft {
					float: left;
					padding: 0 14px;
					// background: #05332A;
					border-radius: 12px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 14px;
					// color: #00FFC0;
					line-height: 24px;
					text-align: center;
					font-style: normal;
					text-transform: none;
				}

				.czlcRight {
					width: 256px;
					position: absolute;
					left: 107px;
					top: 0;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 14px;
					color: #00ffc0;
					line-height: 20px;
					text-align: left;
					font-style: normal;
					text-transform: none;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
			}

			.czlcBox {
				width: 100%;

				.czlcList {
					width: 100%;
					height: 40px;
					padding: 0 16px 0 20px;

					&:nth-child(even) {
						background: rgba(0, 101, 183, 0.15);
					}

					.czlcLeft {
						height: 40px;
						float: left;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 14px;
						color: #abdcf4;
						line-height: 40px;
						text-align: left;
						font-style: normal;

						span {
							font-family: PingFangSC, PingFang SC;
							font-weight: 500;
							font-size: 16px;
							color: #8bc7ff;
							line-height: 40px;
							text-align: left;
							font-style: normal;
							margin-right: 11px;
						}
					}

					.czlcRight {
						width: 256px;
						height: 40px;
						float: right;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 14px;
						color: #ffffff;
						line-height: 40px;
						text-align: right;
						font-style: normal;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}
				}
			}
		}
	}
}
</style>
