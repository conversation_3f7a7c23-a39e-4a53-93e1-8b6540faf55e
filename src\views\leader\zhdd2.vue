<template>
	<div class="leader_box">
		<!-- 左侧和相关农机数据可视化 -->
		<div class="leader_sjts">
			<div class="left" :class="yjsjDetailHide ? 'yjsjDetailLeft' : ''" v-if="contType.name == '查看1'">
				<div class="left1">
					<!-- :marginTop="39" -->

					<BlockBox
						leftTitle="应急事件"
						style="z-index: 999"
						class="box box1"
						:isListBtns="false"
						:showMore="false"
						:showMore2="true"
						:blockHeight="951"
						:blockBackgroundImage="blockYjsjDetailBackImg"
						@handleShowMore2="showMoreFn11"
					>
						<BlockBoxChildYjsjDetails1
							style="z-index: 999"
							:blockHeight="688"
							:dataObj="yjsjDetail"
							@btnSwitch="btnSwitch"
						></BlockBoxChildYjsjDetails1>
					</BlockBox>
				</div>
			</div>
			<div class="left" :class="mkdx ? 'mkdxLeft' : ''" v-if="contType.name == '查看3' && roleNames == '政府侧-区县'">
				<div class="left1">
					<!-- <BlockBox
						title="调度请求"
						class="box box19"
						:isListBtns="false"
						:blockHeight="874"
						:showMore2="true"
						@handleShowMore2="showMoreFn11"
					> -->
					<!-- :marginTop="39" -->

					<BlockBox
						leftTitle="应急事件"
						style="z-index: 999"
						class="box box1"
						:isListBtns="false"
						:showMore="false"
						:showMore2="true"
						:blockHeight="951"
						:blockBackgroundImage="blockYjsjDetailBackImg"
						@handleShowMore2="showMoreFn11"
					>
						<BlockBoxChildYjsjDetails4
							style="z-index: 999"
							:blockHeight="650"
							:dataObj="yjsjDetail"
							:typeShow="typeShow"
							:data4="data4"
							:data16="data16"
							@typeSwitch="typeSwitch"
							@btnSwitch="btnSwitch"
						></BlockBoxChildYjsjDetails4>
					</BlockBox>
				</div>
			</div>
			<div
				class="left"
				:class="mkdx ? 'mkdxLeft' : ''"
				v-if="
					contType.name == '查看4' ||
						(contType.name == '查看2' && roleNames == '政府侧-市省部') ||
						(contType.name == '查看3' && roleNames == '政府侧-市省部')
				"
			>
				<div class="left1">
					<!-- <BlockBox
						title="调度请求"
						class="box box13"
						:isListBtns="false"
						:blockHeight="874"
						:showMore2="true"
						@handleShowMore2="showMoreFn11"
					> -->
					<!-- :marginTop="39" -->

					<BlockBox
						leftTitle="应急事件"
						style="z-index: 999"
						class="box box1"
						:isListBtns="false"
						:showMore="false"
						:showMore2="true"
						:blockHeight="951"
						:blockBackgroundImage="blockYjsjDetailBackImg"
						@handleShowMore2="showMoreFn11"
					>
						<BlockBoxChildYjsjDetails2
							style="z-index: 999"
							:blockHeight="910"
							:dataObj="yjsjDetail"
							:typeShow2="typeShow2"
							:data4="data4"
							:data7="data7"
							@typeSwitch2="typeSwitch2"
							@btnSwitch="btnSwitch"
						></BlockBoxChildYjsjDetails2>
					</BlockBox>
				</div>
			</div>
			<div class="left" :class="mkdx ? 'mkdxLeft' : ''" v-if="contType.name == '查看2' && roleNames == '政府侧-区县'">
				<div class="left1">
					<!-- <BlockBox
						title="调度请求"
						class="box box16"
						:isListBtns="false"
						:blockHeight="893"
						:showMore2="true"
						@handleShowMore2="showMoreFn11"
					> -->
					<!-- :marginTop="39" -->

					<BlockBox
						leftTitle="应急事件"
						style="z-index: 999"
						class="box box1"
						:isListBtns="false"
						:showMore="false"
						:showMore2="true"
						:blockHeight="951"
						:blockBackgroundImage="blockYjsjDetailBackImg"
						@handleShowMore2="showMoreFn11"
					>
						<BlockBoxChildYjsjDetails3
							style="z-index: 999"
							:blockHeight="910"
							:dataObj="yjsjDetail"
							:typeShow3="typeShow3"
							:data4="data4"
							:data14="data14"
							@typeSwitch3="typeSwitch3"
							@btnSwitch="btnSwitch"
							@ckqd="ckqd"
						></BlockBoxChildYjsjDetails3>
					</BlockBox>
				</div>
			</div>

			<div
				class="left"
				:class="mkdx ? 'mkdxLeft' : ''"
				v-if="contType.name == '任务下发' || contType.name == '应急演练' || contType.name == '查看5' || contType.name == '查看6'"
			>
				<div class="left1">
					<!-- <BlockBox
						title="调度请求"
						class="box box5"
						:isListBtns="false"
						:blockHeight="400"
						:showMore2="true"
						@handleShowMore2="showMoreFn11"
					> -->
					<!-- :marginTop="39" -->

					<BlockBox
						leftTitle="调度请求"
						style="z-index: 999"
						class="box box1"
						:isListBtns="false"
						:showMore="false"
						:showMore2="true"
						:blockHeight="479"
						:blockBackgroundImage="blockYjsjRwxfBackImg1"
						@handleShowMore2="showMoreFn11"
					>
						<BlockBoxChildYjsjRwxf2 style="z-index: 999" :blockHeight="420" :dataObj="yjsjDetail"></BlockBoxChildYjsjRwxf2>
					</BlockBox>
					<!-- <BlockBox title="处置流程" class="box box12" :isListBtns="false" :blockHeight="331"> -->

					<!-- :marginTop="39" -->

					<BlockBox
						leftTitle="处置流程"
						style="z-index: 999"
						class="box box1"
						:isListBtns="false"
						:showMore="false"
						:showMore2="false"
						:blockHeight="457"
						:blockBackgroundImage="blockYjsjRwxfBackImg2"
					>
						<BlockBoxChildYjsjRwxf3 style="z-index: 999" :blockHeight="400" :data4="data4"></BlockBoxChildYjsjRwxf3>
					</BlockBox>
				</div>
			</div>

			<div class="left" :class="mkdx ? 'mkdxLeft' : ''" v-if="!contType.name">
				<!-- 左侧农机数据可视化 -->
				<!-- :marginTop="39" -->

				<div class="left1">
					<BlockBox
						leftTitle="应急事件"
						style="z-index: 999"
						class="box box1"
						:isListBtns="false"
						:showMore="true"
						:blockHeight="377"
						:blockBackgroundImage="block1BackImg"
						@handleShowMore="handleShowMore('yjsj')"
					>
						<BlockBoxChildYjsj
							style="z-index: 997"
							:blockHeight="338"
							:totalData="yjsjTotalData"
							:tableData="yjsjTableData"
							@handleItemClick="handleShowMore('yjsj')"
						></BlockBoxChildYjsj>
					</BlockBox>
					<!-- :marginTop="45" -->

					<BlockBox
						leftTitle="机具进度"
						style="z-index: 999"
						class="box box1"
						:showMore="true"
						:isListBtns="false"
						:blockHeight="278"
						:blockBackgroundImage="block2BackImg"
						@handleShowMore="handleShowMore('jjjd')"
					>
						<BlockBoxChildJjjd
							style="z-index: 997"
							:loading="jjjdListLoading"
							:data="jjjdDataList"
							@handleItemClick="handleJjjdItemClick"
						></BlockBoxChildJjjd>
					</BlockBox>
					<!-- :marginTop="39" -->

					<BlockBox
						leftTitle="气象预警"
						style="z-index: 999"
						class="box box1"
						:isListBtns="false"
						:showMore="true"
						:blockHeight="265"
						:blockBackgroundImage="block3BackImg"
						@handleShowMore="handleShowMore('qxyj')"
					>
						<BlockBoxChildQxyj
							style="z-index: 997"
							:blockHeight="226"
							:loading="qxyjListLoading"
							:data="qxyjDataList"
							@clickAlertLevelItem="clickAlertLevelItem"
						></BlockBoxChildQxyj>
					</BlockBox>
				</div>
			</div>
			<div class="top-middle">
				<div class="view-type-box" :style="{ top: mapBtnTop + 'px', left: mapBtnLeft + 'px' }">
					<div
						class="mapItem"
						v-for="(item, index) in mapBtns"
						:key="index"
						:class="mapShow == item.code ? 'active-item' : ''"
						@click="mapActive(item.code)"
					>
						<div class="icon-box">
							<img :src="item.iconUrl" alt="" />
						</div>
						<span>{{ item.label }}</span>
					</div>
				</div>
				<!-- 选择农机在地图上显示相应数据 -->
				<div
					v-if="mapShow == 0 && njfbShow"
					:style="{ top: mapBtnTop + 28 + 'px', left: mapBtnLeft + 146 + 'px' }"
					class="njfbBox"
					:class="mapShow == 4 ? 'njfbBox2' : ''"
				>
					<div class="zyfbItem" :class="statisticType == '1' ? 'jgzzActive' : ''" @click="njlxqh('1')">
						<span>区域农机社会化服务中心</span>
					</div>
					<div class="zyfbItem" :class="statisticType == '2' ? 'jgzzActive' : ''" @click="njlxqh('2')">
						<span>农机应急作业服务队</span>
					</div>
					<div class="zyfbItem" :class="statisticType == '3' ? 'jgzzActive' : ''" @click="njlxqh('3')">
						<span>区域农业应急救灾中心</span>
					</div>
				</div>
			</div>

			<div
				class="right"
				:class="mkdx ? 'mkdxRight' : ''"
				v-if="(contType.name == '任务下发' || contType.name == '应急演练') && roleNames == '政府侧-市省部'"
			>
				<div class="right1">
					<!-- <BlockBox
						title="任务下发"
						class="box box9"
						:isListBtns="false"
						:blockHeight="863"
						:showMore2="true"
						@handleShowMore2="showMoreFn11"
					> -->

					<BlockBox
						leftTitle="任务下发"
						style="z-index: 999"
						class="box box1"
						:isListBtns="false"
						:showMore="false"
						:showMore2="false"
						:blockHeight="952"
						:blockBackgroundImage="blockYjsjRwxfBackImg3"
						@handleShowMore2="showMoreFn11"
					>
						<BlockBoxChildYjsjRwxf1
							style="z-index: 999"
							:blockHeight="890"
							:dataObj="yjsjDetail"
							:typeShow3="typeShow3"
							:data4="data4"
							:data14="data14"
							:form="form"
							:departments_all="departments_all"
							:resources_all="resources_all"
							:placeholder="placeholder"
							@typeSwitch3="typeSwitch3"
							@btnSwitch="btnSwitch"
							@tjrw="tjrw"
							@scrw="scrw"
							@deptChange="deptChange"
							@resourceChange="resourceChange"
							@tjsxnj="tjsxnj"
							@scsxnj="scsxnj"
							@qxgd="qxgd"
							@rwxfgd="rwxfgd"
						></BlockBoxChildYjsjRwxf1>
					</BlockBox>
				</div>
			</div>

			<div class="right" :class="mkdx ? 'mkdxRight' : ''" v-if="contType.name == ''">
				<!-- 右侧农机数据可视化 -->
				<div class="right1">
					<!-- :marginTop="48" -->
					<!-- :showMore="true" -->

					<BlockBox
						leftTitle="指挥调度小组"
						style="z-index: 998"
						subtitle="Party Building Information"
						class="box box7"
						:isListBtns="false"
						:blockHeight="377"
						:blockBackgroundImage="block4BackImg"
						@handleShowMore="handleShowMore('zhjg')"
					>
						<!-- <BlockBoxChildZhjg style="z-index: 997" :treeData="zhjgDataList" :btns="zhjgBtns"></BlockBoxChildZhjg> -->
						<BlockBoxChildZhddxz
							style="z-index: 997"
							:loading="zhddxzLoading"
							:data="zhddxzList"
							:btns="zhjgBtns"
						></BlockBoxChildZhddxz>
					</BlockBox>
					<!-- :marginTop="40" -->

					<BlockBox
						leftTitle="生产机构"
						style="z-index: 998"
						subtitle="Full Factor Grid Events"
						class="box box8"
						blockTitleClass="use-long-title-header-bg"
						:isListBtns="false"
						:showMore="scjgShowMore"
						:blockHeight="200"
						:blockBackgroundImage="block5BackImg"
						@handleShowMore="handleShowMore('scjg')"
					>
						<BlockBoxChildScjg style="z-index: 997" :blockHeight="161" :data="scjgDataList"></BlockBoxChildScjg>
					</BlockBox>
					<!-- :marginTop="39" -->

					<BlockBox
						leftTitle="机具资源"
						style="z-index: 998"
						subtitle="Party Building Information"
						class="box box8"
						:isListBtns="false"
						:blockHeight="343"
						:titleNumber="jjzyTotal"
						:showTotalNumber="true"
						:titleNUmberUnit="contentNumberUnitOne"
						:blockBackgroundImage="block6BackImg"
						@handleShowMore="handleShowMore('jjzy')"
					>
						<BlockBoxChildJjzy
							style="z-index: 999"
							:loading="jjzyLoading"
							:contentNumberUnitOne="contentNumberUnitOne"
							:contentDataList="jjzyDataList"
						></BlockBoxChildJjzy>
					</BlockBox>
				</div>
			</div>
		</div>

		<!-- 地图组件 -->
		<div class="map_box">
			<LeaMap
				ref="leafletMap"
				@poiClick="poiClick"
				@gridClick="gridClick"
				@operate="operate"
				@qctc="qctc"
				@polygonResult="polygonResult"
				:clear2="true"
				:back2="true"
			/>
		</div>
		<!-- 搜索地方 -->
		<div class="ssPop" v-if="sspopShow" :style="{ bottom: operateBoxBottom + 38 + 'px', right: operateBoxRight + 'px' }">
			<div class="ssPop1">
				<input type="text" v-model="ssnr" @change="ssqy" />
				<div class="ssImgBox" @click="ssqy">
					<img src="@/assets/bjnj/ss.png" alt="" />
				</div>
			</div>
			<div class="ssPop2" v-if="sspopList">
				<div class="ssList" v-for="(item, index) in ssList" :key="index" @click="ssxz(item)">{{ item.areaName }}</div>
			</div>
		</div>
		<!-- 图例 -->
		<!-- <div class="njtlList" :style="{ bottom: mapBtnBottom + 145 + 'px', right: mapBtnRight - 5 + 'px' }" v-if="isnjtlListShow">
			<div class="njtlItem">
				<img src="@/assets/bjnj/njtlImg2.png" alt="" />
				<div class="njtlName">当前在线</div>
			</div>
			<div class="njtlItem">
				<img src="@/assets/bjnj/njtlImg1.png" alt="" />
				<div class="njtlName">当日在线</div>
			</div>
			<div class="njtlItem">
				<img src="@/assets/bjnj/njtlImg3.png" alt="" />
				<div class="njtlName">离线</div>
			</div>
		</div> -->

		<!-- 地图相关的操作按钮 -->
		<div class="map-operate-box" :style="{ bottom: operateBoxBottom + 'px', right: operateBoxRight + 'px' }">
			<div
				class="operate-item-box"
				v-show="!operateItem.hide"
				v-for="(operateItem, index) in operateBtnList"
				:key="index"
				@click="operateItem.func()"
			>
				<img :src="operateItem.iconUrl" alt="" />
				<div class="operate-title">{{ operateItem.title }}</div>
				<div class="devider"></div>
			</div>
		</div>
		<!-- 基础信息弹窗 -->
		<BasicInformation
			v-model="basicInfomationShow"
			:title="basicInfomationTitle"
			:list="basicInfomationList"
			:btnShow="basicInfomationBtnShow"
			:btns="basicInfomationBtns"
			@handle="clickBasicInformationBtn"
		/>

		<!-- 农机基本信息弹窗 -->
		<njInfo v-if="njInfoShow" :agmachInfo="currentAgmachInfo" @handleClick="handleNjInfo" @closeEmitai="njInfoShow = false" />

		<!-- 历史作业弹窗 -->
		<historyWorkPop
			ref="historyWorkPop"
			v-model="workPopShow"
			:agmachInfo="currentAgmachInfo"
			:agmachId="agmachId"
			@operate="eventInfoBtnClick15"
		/>

		<!-- 作业轨迹弹窗 -->
		<mapPop ref="mapPop" v-model="isMapShow" />
		<YjsjList ref="yjsjChildRef" v-model="isYjsjTableShow" @operate="yjsjInfoBtnClick" />
		<JjjdList ref="jjjdChildRef" v-model="isJjjdTableShow" @operate="jjjdInfoBtnClick" />

		<!-- 机具进度数据查看 -->
		<JjjdInfo ref="jjjdInfoChildRef" v-model="jjjdItemShow" :data="jjjdItemData" />

		<!-- 气象预警查看 -->
		<QxyjList v-if="qxyjListShow" ref="qxyjListRef" v-model="qxyjListShow" :areaId="areaId" @operate="handleQxyjClick" />

		<!-- 生产机构列表数据弹窗 -->
		<ScjgList ref="scjgListRef" :areaId="areaId" v-model="isScjgTableShow" @operate="scjgInfoBtnClick" />
		<!-- 应急事件点击查看触发 -->
		<!-- <LeaderFooterNew ref="LeaderFooter" :btns="btns" @mark="mark" /> -->
		<BhqqPop v-if="isbhqqShow" v-model="isbhqqShow" @handle="handleBohui" />
		<ZylbPop v-if="isZylbShow" v-model="isZylbShow" :list="zylbList" @handle="eventInfoBtnClick11" />
		<!-- 气象预警详情弹窗触发 -->
		<QxyjDetailPop v-if="isQxyjDetailShow" v-model="isQxyjDetailShow" :list="qxyjDetailList" @handle="qxyjDetailEventInfoBtnClick" />

		<!-- 生产机构->各行政区划组织 -->
		<DepartmentInRegionPop
			v-if="isScjgDetailShow"
			v-model="isScjgDetailShow"
			:type="scjgCurrentSelectedTab"
			:level="scjgCurrentSelectedLevel"
			:data="scjgDetailData"
			@operate="ryjjmxClick"
			@handle="ryjjmxClick"
		/>
		<!-- 人员机具明细 -->
		<RyjjmxPop
			v-if="isRyjjmxDataShow"
			ref="RyjjmxPop"
			v-model="isRyjjmxDataShow"
			:data="scjgDetailData"
			:basicInfomationId="departmentId"
			@fhEmitai="fhEmitai"
			@closeEmitai="isRyjjmxDataShow = false"
		/>

		<!-- 机构名录管理 -->
		<JgmlglList ref="jgmlListRef" v-model="isJgmlListShow" :areaId="areaId" @closeEmitai="isJgmlListShow = false" />
		<ZhddxzMessageBox
			ref="zhddxzRef"
			v-model="isZhddxzShow"
			:areaId="areaId"
			:groupInfo="currentGroupInfo"
			:zhjgDataList="zhjgDataList"
			:userName="userName"
			@closeEmitai="handleCloseMessageBox()"
		/>
		<div class="ai_waring" v-if="ncjkDialogShow">
			<div class="title">
				<span>监控查看</span>
			</div>
			<div class="close_btn" @click="ncjkDialogShow = false"></div>
			<div class="content">
				<div id="ezuikit-player" style="width: 100%;height: 100%;"></div>
			</div>
		</div>
		<div class="draw-polygon-tooltip" v-if="showDrawPolygonTooltip">
			<div class="tooltip-text">
				<i class="el-icon-warning waring-box"></i>
				绘制后鼠标双击结束绘制
			</div>
		</div>
	</div>
</template>

<script>
const DataVUrl = 'https://geo.datav.aliyun.com/areas_v3/bound/'

import BlockBox from '@/components/leader/common/blockBox6.vue'
import BlockBox8 from '@/components/leader/common/blockBox8.vue'
import BlockBoxChildNoHeader1 from '@/components/leader/common/blockBoxChildNoHeader1.vue'

import BlockBoxChildYjsj from '@/components/leader/common/blockBoxChildYjsj.vue'

import BlockBoxChildJjjd from '@/components/leader/common/blockBoxChildJjjd.vue'
import BlockBoxChildQxyj from '@/components/leader/common/blockBoxChildQxyj.vue'

import BlockBoxChildZhjg from '@/components/leader/common/blockBoxChildZhjg.vue'
import BlockBoxChildZhddxz from '@/components/leader/common/blockBoxChildZhddxz.vue'

import BlockBoxChildScjg from '@/components/leader/common/blockBoxChildScjg.vue'
import BlockBoxChildJjzy from '@/components/leader/common/blockBoxChildJjzy.vue'
import BlockBoxChildYjsjDetails1 from '@/components/leader/common/blockBoxChildYjsjDetails1.vue'
import BlockBoxChildYjsjDetails2 from '@/components/leader/common/blockBoxChildYjsjDetails2.vue'
import BlockBoxChildYjsjDetails3 from '@/components/leader/common/blockBoxChildYjsjDetails3.vue'
import BlockBoxChildYjsjDetails4 from '@/components/leader/common/blockBoxChildYjsjDetails4.vue'
import BlockBoxChildYjsjRwxf1 from '@/components/leader/common/blockBoxChildYjsjRwxf1.vue'
import BlockBoxChildYjsjRwxf2 from '@/components/leader/common/blockBoxChildYjsjRwxf2.vue'
import BlockBoxChildYjsjRwxf3 from '@/components/leader/common/blockBoxChildYjsjRwxf3.vue'

import HeaderMain from '@/components/leader/common/headerMain.vue'
import effectRankBarSwiper from './components/effectRankBar-swiper.vue'
import particle from './components/particle.vue'
import leaderMiddle from '@/components/leader/common/leaderMiddle.vue'
import myRobot from '@/components/robot/myRobot.vue'
import NumScroll from '@/components/leader/num_scrolls'
import Area from '@/components/leader/leader_csts/area.vue'
import svgMap from '@/components/common/svgMap.vue'
import zlBg from '@/components/common/zlBg.vue'
import LivePlayer from '@liveqing/liveplayer'
import SwiperTable from '@/components/bjnj/SwiperTable.vue'
import LeaMap from '@/components/map/LeafletMapNj.vue'

import {
	cscpCurrentUserDetails,
	getAgmachGuiJiInfo,
	queryByList,
	yjsjScreenDd,
	flowList,
	queryFuPanById,
	distributionAction,
	distributionActionForECenter,
	peripheryList,
	getJWD,
	getCscpBasicHxItemCode,
	beforeD,
	assignableResource,
	e,
	todoAScreen,
	getResourceList,
	api_getQxyjData,
	api_getJjjdDataList,
	getAllAlarmWithGPS,
	getAllAlarmWithGPSV2,
	getOrgTree,
	api_getMachineryDataByPolygon,
	api_getWeatherData,
	api_getZhddxzList,
	api_getZhddGroupInfoList,
	api_getMessageGroupToken,
} from '@/api/bjnj/zhdd.js'
import { BasicInformation } from './components/zhdd/dialog'
import AmachTypeSwiper from './components/zhdd/AmachTypeSwiper.vue'
import znzyPop from '@/components/bjnj/znzyPop.vue'
import njInfo from '@/components/bjnj/njInfo.vue'
import historyWorkPop from '@/components/bjnj/historyWorkPop.vue'
import mapPop from '@/components/bjnj/mapPop.vue'

import EZUIKit from 'ezuikit-js'

import { aqjgGetToken } from '@/api/hs/hs_aqjg.api.js'
import dayjs from 'dayjs'
import {
	locHistory2,
	//新优化
	getNcjkToken,
	getNcjkUrl,
} from '@/api/njzl/hs.api.js'

//新优化
import { getMachineryType } from '@/api/bjnj/zhdd.js'

//新优化
import DrawDataBar from '@/components/ssts/draw-data-bar.vue'
import TopHeaderDescripte from '@/components/leader/common/top-header-descripte.vue'
import YjsjList from '@/components/bjnj/yjsjList.vue' //应急事件
import JjjdList from '@/components/bjnj/jjjdList.vue' //机具进度
import JgmlglList from '@/components/bjnj/jgmlList.vue' //机构名录管理
import ZhddxzMessageBox from '@/components/bjnj/zhjgxzMessageBox.vue'
import JjjdInfo from '@/components/bjnj/jjjdInfo.vue' //机具信息弹窗
import QxyjList from '@/components/bjnj/qxyjList.vue' //气象预警弹窗
import ScjgList from '@/components/bjnj/scjgList.vue' //生产机构弹窗

import LeaderFooterNew from '@/components/leader/common/leaderFooter4.vue'
import BhqqPop from '@/components/bjnj/bhqqPop.vue' //驳回请求
import ZylbPop from '@/components/bjnj/zylbPop.vue' //资源清单
import QxyjDetailPop from '@/components/bjnj/qxyjDetailPop.vue' //预警详情

import DepartmentInRegionPop from '@/components/bjnj/departmentInRegionPop.vue' //生产机构->各行政区划组织
import RyjjmxPop from '@/components/bjnj/ryjjmxPop.vue'
import { appServiceInitAddressUrl } from '@/utils/leader/const'

import 'swiper/css/swiper.css'
// 引入 xlsx 插件
import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'
import XLSXStyle from 'xlsx-style'
// 示例：使用Turf.js（需先引入Turf库）
import * as turf from '@turf/turf'
import { polygon } from '@turf/helpers'
import Grib2 from 'grib2json'
// import qxData from '@/assets/json/qxData/qxData'

export default {
	name: 'Hszl',
	components: {
		BlockBox,
		BlockBox8,
		BlockBoxChildNoHeader1,
		HeaderMain,
		effectRankBarSwiper,
		particle,
		leaderMiddle,
		// gdMap,
		// cesiumMap,
		myRobot,
		Area,
		NumScroll,
		svgMap,
		zlBg,
		LivePlayer,
		SwiperTable,
		LeaMap,
		BasicInformation,
		znzyPop,
		njInfo,
		historyWorkPop,
		mapPop,
		AmachTypeSwiper,
		//新优化
		DrawDataBar,
		TopHeaderDescripte,

		BlockBoxChildYjsj,
		BlockBoxChildJjjd,
		BlockBoxChildQxyj,
		BlockBoxChildZhjg,
		BlockBoxChildScjg,
		BlockBoxChildJjzy,
		YjsjList,
		JjjdList,
		JgmlglList,
		JjjdInfo,
		LeaderFooterNew,
		BlockBoxChildYjsjDetails1,
		BlockBoxChildYjsjDetails2,
		BlockBoxChildYjsjDetails3,
		BlockBoxChildYjsjDetails4,
		BlockBoxChildYjsjRwxf1,
		BlockBoxChildYjsjRwxf2,
		BlockBoxChildYjsjRwxf3,
		BhqqPop,
		ZylbPop,
		QxyjList,
		QxyjDetailPop,
		ScjgList,
		DepartmentInRegionPop,
		RyjjmxPop,
		ZhddxzMessageBox,
		BlockBoxChildZhddxz,
	},
	data() {
		return {
			ssnr: '',
			ssList: [],
			ssboxShow: true,
			sspopShow: false,
			sspopList: false,
			startDate: '',
			endDate: '',

			contentNumberUnitOne: '万台',
			serviceIndustryNumberUnit: '万',

			dqbf: dayjs()
				.subtract(1, 'year')
				.format('YYYY'),
			dqyf: '',
			dqyfs: '',

			groupLevel: 2,
			mapShow: null, //0:区域统计；

			time: 0,
			dwType: '',
			mkdx: false,
			yjsjDetailHide: false,
			agmachId: '',
			areaId: '100000',
			areaIdList: [],
			areaLevel_init: 0,
			//areaLevel 0：全国，1：省份，2：地市，3：区县
			areaLevel: 0,
			areaIdNew: 0,
			trajectoryMark: '',

			workPopShow: false,
			njInfoShow: false,
			isnjtlListShow: false,
			isMapShow: false,
			// 控制基本信息弹窗显示
			basicInfomationShow: false,
			// 基础信息标题
			basicInfomationTitle: '',
			// 基础信息内容
			basicInfomationList: [],
			// 控制基础信息按钮显示
			basicInfomationBtnShow: false,
			// 基础信息按钮
			basicInfomationBtns: [],
			activaIdx: 1, //当前地图中渲染的数据是什么数据
			currentAgmachInfo: {},
			lastAreaLevel: [],

			//新优化
			haveNjDataList: [],
			mechRateDataList: [],
			serviceIndustryDataList: [],

			mapBtnBottom: 260,
			mapBtnRight: 507,
			operateBoxBottom: 15,
			operateBoxRight: 637,
			currentTabValue: 'in',
			mapBtns: [
				{ iconUrl: require('@/assets/img/jxhfz/map-btn-3.png'), code: 0, label: '区域统计' },
				{ iconUrl: require('@/assets/img/jxhfz/map-btn-3.png'), code: 1, label: '清除区域统计' },
				{ iconUrl: require('@/assets/img/jxhfz/map-btn-3.png'), code: 2, label: '视频监控' },
			],

			block1BackImg: require('@/assets/img/block-box/block-yingjishijian-bg.png'),
			block2BackImg: require('@/assets/img/block-box/block-jijvjindu-bg.png'),
			block3BackImg: require('@/assets/img/block-box/block-qixiangyujing-bg.png'),
			block4BackImg: require('@/assets/img/block-box/block-zhihuijigou-bg.png'),
			block5BackImg: require('@/assets/img/block-box/block-shengchanjigou-bg.png'),
			block6BackImg: require('@/assets/img/block-box/block-jijvziyuan-bg.png'),

			//应急事件查看背景
			blockYjsjDetailBackImg: require('@/assets/img/block-box/block-yingjishijian-detail-bg.png'),
			blockYjsjRwxfBackImg1: require('@/assets/img/block-box/block-yingjishijian-rwxf-bg-1.png'),
			blockYjsjRwxfBackImg2: require('@/assets/img/block-box/block-yingjishijian-rwxf-bg-2.png'),
			blockYjsjRwxfBackImg3: require('@/assets/img/block-box/block-yingjishijian-rwxf-bg-3.png'),

			//新属性
			yjsjTotalData: [
				{
					iconUrl: require('@/assets/img/block-box/yingjishijian-ndzs-bg.png'),
					number: 0,
					label: '年度总数',
				},
				{
					iconUrl: require('@/assets/img/block-box/yingjishijian-ycz-bg.png'),
					number: 0,
					label: '已处置',
				},
				{
					iconUrl: require('@/assets/img/block-box/yingjishijian-dcz-bg.png'),
					number: 0,
					label: '待处置',
				},
			],
			yjsjTableData: [],
			jjjdDataList: [],

			jjjdListLoading: false,
			zhddxzLoading: false,
			jjzyLoading: false,
			qxyjDataList: [],
			qxyjListLoading: false,
			weatherAlarmType: '',
			weatherAlarmLevel: '',

			zhjgDataList: [],
			zhddxzList: [],
			zhjgBtns: [
				{
					iconUrl: require('@/assets/img/block-box/zhihuijigou/btn-create-icon.png'),
					iconClass: 'el-icon-circle-plus-outline',
					label: '在线沟通',
					func: () => {
						console.log('点击了在线沟通！')
						this.currentGroupInfo = { uuid: '', name: '' }
						this.isZhddxzShow = true
					},
				},
				{
					iconUrl: require('@/assets/img/block-box/zhihuijigou/btn-jggl-icon.png'),
					iconClass: 'el-icon-s-order',
					label: '机构名录管理',
					func: () => {
						console.log('点击了机构名录管理！')
						this.isJgmlListShow = true
						// this.$refs.jgmlListRef.getOrgTree({ areaId: this.areaId })
					},
				},
				{
					iconUrl: require('@/assets/img/block-box/zhihuijigou/btn-video-connect-icon.png'),
					iconClass: 'el-icon-video-camera',
					label: '视频会商',
					func: () => {
						console.log('点击了视频会商！')
						this.callBoardShow2 = !this.callBoardShow2
						if (this.callBoardShow2) {
							this.$bus.emit('LaunchMeetingDialogOpen', this.areaId)
						}
					},
				},
			],
			scjgDataList: [
				{
					iconUrl: require('@/assets/img/block-box/shengchanjigou-fwd.png'),
					number: 0,
					label: '农机社会化服务中心',
				},
				{
					iconUrl: require('@/assets/img/block-box/shengchanjigou-fwzx.png'),
					number: 0,
					label: '农业应急救灾中心',
				},
				{
					iconUrl: require('@/assets/img/block-box/shengchanjigou-jzzx.png'),
					number: 0,
					label: '农机应急作业服务队',
				},
			],
			jjzyDataList: [],

			njfbShow: false,

			mapBtnTop: 0,
			mapBtnLeft: 0,
			sameMapShow: false,
			statisticType: -1,
			jjzyTotal: 0,
			isYjsjTableShow: false, //应急事件
			isJjjdTableShow: false, //机具进度
			roleNames: '',
			markList: [],
			btns: [
				{
					normalBg: require('@/assets/bjnj/btnImg11.png'),
					activeBg: require('@/assets/bjnj/btnImg12.png'),
					name: '调度请求',
				},
				{
					normalBg: require('@/assets/bjnj/btnImg13.png'),
					activeBg: require('@/assets/bjnj/btnImg14.png'),
					name: '调度任务',
				},
				{
					normalBg: require('@/assets/bjnj/btnImg15.png'),
					activeBg: require('@/assets/bjnj/btnImg16.png'),
					name: '天气预警',
				},
				{
					normalBg: require('@/assets/bjnj/btnImg17.png'),
					activeBg: require('@/assets/bjnj/btnImg18.png'),
					name: '服务组织',
				},
				{
					normalBg: require('@/assets/bjnj/btnImg19.png'),
					activeBg: require('@/assets/bjnj/btnImg20.png'),
					name: '视频连线',
				},
				// {
				//   normalBg: require('@/assets/bjnj/btnImg9.png'),
				//   activeBg: require('@/assets/bjnj/btnImg10.png'),
				//   name: '跨区作业'
				// },
				// {
				//   normalBg: require('@/assets/bjnj/btnImg21.png'),
				//   activeBg: require('@/assets/bjnj/btnImg22.png'),
				//   name: '进度统计'
				// },
			],

			list: [],
			contList: [],
			hlID: '',
			choice: null,
			yjsjDetail: {},
			data14: [{ name: 'XXX', contact: 'XXX', phone: '18032564121', tagmachInfos: [] }],
			contType: {
				name: '',
			},
			typeShow: 1,
			dataContent: {
				detail: {
					node: {},
				},
			},
			data4: [{ departName: '部门1', usernmae: 'XXX', comments: 'XXX', choice: '新增', createDatetime: '2025-05-22 14:50:03' }],

			data7: [
				{ departName: '部门1', usernmae: 'XXX', createDatetime: '2025-05-21', choice: '已完成' },
				{ departName: '部门2', usernmae: 'XXX', createDatetime: '2025-05-20', choice: '未完成' },
			],
			data16: [],
			form2: {
				njlx: '',
				zxmc: '',
				resource: '1',
				items: [
					{
						region: '',
						name: '',
					},
				],
			},
			resources_all: [],

			departments_all: [],
			data11: [],
			markValue: 20,
			rwxfSx: {
				type: '',
				cname: '',
			},
			peripheryData: [],
			isbhqqShow: false,
			isqrqqShow: false,
			qrList: [],
			dictList1: [],
			selectionList: [],
			data5: [],
			data6: [],
			currDDQQListAgmachList: [],
			data2: [],
			ishlqqShow: false,
			typeShow2: 1,
			typeShow3: 1,
			form: {
				njlx: '',
				zxmc: '',
				resource: '1',
				departments: [
					{
						deptId: '',
						username: '',
						phone: '',
						resourceList: [
							{
								type: '',
								num: '',
							},
						],
					},
				],
			},
			sewageDataList3: [],
			maxpage: 1,
			placeholder: '农机数量',
			isZylbShow: false, //资源清单
			zylbList: [], //资源列表

			jjjdItemData: {}, //机具进度数据
			jjjdItemShow: false, //查看机具进度数据弹窗
			qxyjListShow: false, //查看气象预警弹窗
			qxyjDetailList: [],
			isQxyjDetailShow: false,

			isScjgTableShow: false, //机具进度

			scjgDetailData: {},
			isScjgDetailShow: false,
			departmentId: '',
			isRyjjmxDataShow: false,
			scjgCurrentSelectedTab: '1',
			scjgCurrentSelectedLevel: '',
			isJgmlListShow: false,
			isZhddxzShow: false,
			callBoardShow2: false,
			ncjkDialogShow: false,
			polygonResultTimer: null,
			currentClickAlertTypeIndex: -1,
			currentClickAlertLevelIndex: -1,
			config: {
				caiyunToken: 'vzW22KoCx8dqshuq', // 替换为你的彩云天气Token
				dataGranularity: 1, // 数据采集粒度(度)
				// maxPoints: 5000, // 最大显示点数(性能优化)
				maxPoints: 500, // 最大显示点数(性能优化)

				hourlysteps: 1,
				dailysteps: 7,
			},
			provinceGeoJson: {},
			userName: '',
			intervalId: null,
			isPageVisible: true,
			intervalTime: 90 * 60 * 1000, //将1.5小时转为毫秒
			// intervalTime: 5000, //将1.5小时转为毫秒
			currentGroupInfo: { uuid: '', name: '' },
			showDrawPolygonTooltip: false,
			scjgShowMore: true,
		}
	},
	created() {},
	beforeDestroy() {
		this.timer && clearInterval(this.timer)
		this.timer1 && clearInterval(this.timer1)
		this.clearInterval()
		document.removeEventListener('visibilitychange', this.handleVisibilityChange)
	},
	mounted() {
		this.cscpCurrentUserDetails()
		this.$bus.on('LaunchMeetingDialogClose', () => {
			console.log('发起会议弹窗关闭')
			this.callBoardShow2 = false
			// this.activaIdx = -1
			// this.$refs.LeaderFooter.activeList = []
		})
		//获取农机字典
		this.getArgmachList()
		this.getGRB2Data()
	},

	watch: {
		// store.state.messageToken(){

		// }
		//
		messageToken(val) {
			this.getZhddxzData()
		},
	},
	computed: {
		formatNumber() {
			return function(param) {
				if (this.isEmpty(param) || param.length < 4 || isNaN(param)) {
					return param
				}

				let small = ''
				let isSmall = false
				if (param.split('.').length === 2) {
					// 有小数
					isSmall = true
					small = param.split('.')[1]
					param = param.split('.')[0]
				}

				let result = ''

				while (param.length > 3) {
					result = ',' + param.slice(-3) + result
					param = param.slice(0, param.length - 3)
				}

				if (param) {
					result = param + result
				}

				if (isSmall) {
					result = result + '.' + small
				}
				return result
			}
		},
		initOptions1() {
			return {
				yAxis: {
					name: '台/天',
					nameTextStyle: {
						fontSize: 12,
						padding: [30, 0, 0, 0],
					},
					axisLabel: {
						textStyle: {
							fontSize: 12,
						},
					},
				},
				xAxis: {
					axisLabel: {
						show: true,
						textStyle: {
							fontSize: 12,
						},
					},
				},
			}
		},
		initOption2() {
			return {
				yAxis: [
					{
						minInterval: 1,
					},
					{
						minInterval: 1,
					},
				],
				tooltip: {
					formatter: (params) => {
						let relVal = params[0].name
						for (let i = 0, l = params.length; i < l; i++) {
							let seriesName = params[i].seriesName + ':'
							let unit = params[i].seriesType == 'bar' ? '亩' : '台'
							relVal += '<br/>' + params[i].marker + seriesName + params[i].value + unit
						}
						return relVal
					},
				},
				series: [
					{
						barWidth: 30,
					},
				],
			}
		},
		barInitOptions() {
			return {
				yAxis: {
					nameTextStyle: {
						width: 200,
					},
				},
				xAxis: [
					{
						show: false,
						axisTick: {
							show: false, // 不显示坐标轴刻度线
						},
						axisLine: {
							show: false, // 不显示坐标轴线
						},
						axisLabel: {
							show: false, // 不显示坐标轴上的文字
						},
						// max: Math.max(...this.barChartData.map(item => { return item[1] } )),
						nameTextStyle: {
							width: 200,
						},
					},
				],
				grid: {
					right: '10%',
					left: '5%',
				},
				tooltip: {
					show: true,
					triggerOn: 'mousemove',
					formatter: (params) => {
						var relVal = ''
						for (var i = 0, l = params.length; i < l; i++) {
							var unit = '台'
							relVal += params[i].marker + params[i].value + unit
						}
						return relVal
					},
				},
			}
		},

		//新优化

		operateBtnList() {
			return [
				{
					iconClass: 'el-icon-back',
					title: '返回上级',
					iconUrl: require('@/assets/img/operate/return.png'),
					func: () => {
						//返回上一级函数
						this.$refs.leafletMap.backLayers()
					},
				},
				{
					iconClass: 'el-icon-full-screen',
					title: '页面全屏',
					iconUrl: require('@/assets/img/operate/all-screen.png'),

					func: () => {
						//页面全屏
						this.mkssBtn()
					},
				},
				{
					iconClass: 'el-icon-refresh-right',
					title: '刷新内容',
					iconUrl: require('@/assets/img/operate/refresh.png'),

					func: () => {
						//刷新内容
						this.sxTk()
					},
				},
				// {
				// 	iconClass: 'el-icon-menu',
				// 	title: '图例',
				// 	func: () => {
				// 		//图例
				// 		this.njtlTk()
				// 	},
				// },
				{
					iconClass: 'el-icon-view',
					title: '隐藏标记',
					iconUrl: require('@/assets/img/operate/show-operate.png'),
					func: () => {
						//隐藏标记
						this.mapShow = -1
						this.$refs.leafletMap.clearLayers()
					},
				},
				{
					iconClass: 'el-icon-sort',
					type: 'switch',
					title: '切换地图',
					iconUrl: require('@/assets/img/operate/switch-map.png'),

					func: () => {
						//切换地图
						this.$refs.leafletMap.switchMapType(this.areaLevel)
					},
				},
				{
					iconClass: 'el-icon-search',
					title: '点击搜索地方',
					hide: !this.ssboxShow,
					iconUrl: require('@/assets/img/operate/search-location.png'),

					func: () => {
						//点击搜索地方
						this.ssmkQhs()
					},
				},
			]
		},

		messageToken() {
			return this.$store.state.messageToken
		},
	},
	methods: {
		async getGRB2Data() {
			// 			const buffer = file.arrayBuffer();
			//   const result = await Grib2.parse(buffer);
		},
		//新优化
		startInterval() {
			this.clearInterval() // 先清除已有的定时器
			this.intervalId = setInterval(() => {
				this.executeTask()
			}, this.intervalTime)
		},
		clearInterval() {
			if (this.intervalId) {
				clearInterval(this.intervalId)
				this.intervalId = null
			}
		},

		handleVisibilityChange() {
			this.isPageVisible = !document.hidden
			if (this.isPageVisible) {
				// 页面变为可见时立即执行一次并重启定时器
				this.executeTask()
				this.startInterval()
			} else {
				// 页面不可见时清除定时器
				this.clearInterval()
			}
		},

		executeTask() {
			console.log('定时任务执行，当前时间:', new Date().toLocaleString())
			this.getMessageGroupToken()
		},
		getArgmachList() {},

		yjsjInfoBtnClick(name, it, selectionList) {
			this.$store.commit('invokerightShow', false)
			// this.$refs.LeaderFooter.activeList = []
			// this.$refs.LeaderFooter.handleClick()
			this.removeAllPoi()
			let data = [
				{
					latlng: [it.lat, it.lon],
					icon: {
						iconUrl:
							it.statusName == '待处置'
								? require('@/assets/bjnj/ddqqDt1.png')
								: it.statusName == '进行中'
								? require('@/assets/bjnj/ddqqDt2.png')
								: require('@/assets/bjnj/ddqqDt3.png'),
						iconSize: [48, 62],
						iconAnchor: [16, 42],
					},
					props: {
						id: 'yjsjId',
						type: 'yjsjType',
						info: { ...it },
					},
				},
			]
			this.$refs.leafletMap.drawPoiMarker(data, 'yjsj', false)
			this.list = it
			this.contList = it.actionsForCurrentNode
			if (name.name == '查看') {
				this.$store.commit('invokerightShow', false)
				this.hlID = it.id
				this.isYjsjTableShow = false
				this.choice = name.value
				if (it.zstatus == 2 && this.roleNames == '政府侧-区县') {
					this.contType.name = '查看2'
					this.queryFuPanById(it)
					this.flowList(it)
				} else {
					if (it.node.dead == 1) {
						this.contType.name = '查看3'
						this.typeShow = 1
						this.flowList(it)
					} else {
						this.contType.name = '查看1'
						this.yjsjDetail = it
					}
				}
			} else if (name.name == '任务下发') {
				this.contType = name
				this.isYjsjTableShow = false
				this.distributionAction()
				this.distributionActionForECenter(it)
				this.flowList(it)
				this.peripheryList(it)
				if (this.roleNames == '政府侧-区县')
					// this.$refs.leafletMap.loadDaynamicCicle([it.lat,it.lon], this.markValue*1000);
					// this.$refs.leafletMap.loadRadarScan([Number(it.lon),Number(it.lat)], this.markValue*1000);
					this.getJWD(this.markValue * 1000)
				this.$store.commit('invokerightShow', true)
				this.yjsjDetail = it
			} else if (name.name == '驳回') {
				this.isbhqqShow = true
				this.hlID = it.id
			} else if (name.name == '确认') {
				this.isqrqqShow = true
				this.qrList = it
			} else if (name.name == '合并上报') {
				this.getCscpBasicHxItemCode1('affectedType')
				this.selectionList = selectionList
				this.choice = name.value
				if (this.selectionList.length > 0) {
					let ids = []
					ids = [].concat(this.selectionList.map((it) => it.id))
					this.beforeD(
						{
							ids: ids.join(','),
						},
						name,
					)
					// this.contType = name
					// this.isYjsjTableShow = false
				} else {
					this.$Message.warning('请先勾选要合并的工单')
					return false
				}
				this.$store.commit('invokerightShow', true)
			} else if (name.name == '资源分配') {
				this.contType = name
				this.isYjsjTableShow = false
				this.assignableResource({
					id: it.id,
					node: {
						id: it.node.id,
						globalId: it.node.globalId,
					},
				})
				this.$store.commit('invokerightShow', true)
			}
		},

		async queryFuPanById(it) {
			let res = await queryFuPanById({
				id: it.id,
				node: {
					id: it.node.id,
				},
			})
			if (res?.code == '200') {
				this.yjsjDetail = res.data.detail
				this.data14 = res.data.resources
			} else {
				this.$message({
					message: res.msg,
					type: 'warning',
				})
			}
		},

		async flowList(it) {
			let res = await flowList({
				id: it.id,
				node: {
					id: it.node.id,
				},
			})
			if (res?.code == '200') {
				this.dataContent = res.data
				this.yjsjDetail = res.data.detail
				this.data4 = res.data.flowLine
				this.data7 = res.data.flowLine
				this.data16 = res.data.resources
				this.form2 = { ...res.data.detail, affectedArea: res.data.detail.affectedArea.replace('亩', '') }
				this.form2.items = res.data.detail.items ? JSON.parse(res.data.detail.items) : ''
			} else {
				this.$message({
					message: res.msg,
					type: 'warning',
				})
			}
		},

		async distributionAction() {
			let res = await distributionAction()
			if (res?.code == '200') {
				this.departments_all = res.data
				// this.resources_all = res.data.resources
			} else {
				this.$message({
					message: res.msg,
					type: 'warning',
				})
			}
		},

		async distributionActionForECenter(row) {
			let params = {
				...this.rwxfSx,
			}
			if (row && row.id) {
				params['id'] = row.id
			}
			if (this.yjsjDetail && this.yjsjDetail.id) {
				params['id'] = this.yjsjDetail.id
			}
			params['distance'] = this.markValue
			let res = await distributionActionForECenter(params)
			if (res?.code == '200') {
				this.data11 = res.data
			} else {
				this.$message({
					message: res.msg,
					type: 'warning',
				})
			}
		},

		async peripheryList(row) {
			let params = {
				type: this.rwxfSx.type,
			}
			this.peripheryData = []
			if (row && row.id) {
				params['id'] = row.id
				params['areaId'] = row.areacode
			}
			if (this.yjsjDetail && this.yjsjDetail.id) {
				params['id'] = this.yjsjDetail.id
				params['areaId'] = this.yjsjDetail.areacode
			}
			params['distance'] = this.markValue
			// params['distance'] = 20;
			let res = await peripheryList(params)
			this.peripheryData = res.data
			this.peripheryData.forEach((item) => {
				item.state = false
			})
		},
		async getJWD(e) {
			let res = await getJWD()
			this.$refs.leafletMap.loadRadarScan([Number(res.data.lon), Number(res.data.lat)], e)
		},

		async getCscpBasicHxItemCode1(data) {
			let res = await getCscpBasicHxItemCode(data)
			if (res?.code == '0') {
				this.dictList1 = res.data
			} else {
				this.$message({
					message: res.msg,
					type: 'warning',
				})
			}
		},

		async beforeD(data, name) {
			let res = await beforeD(data)
			if (res?.code == '200') {
				this.contType = name
				this.isYjsjTableShow = false
			} else {
				this.$message({
					message: res.msg,
					type: 'warning',
				})
			}
		},

		async assignableResource(data) {
			let res = await assignableResource(data)
			if (res?.code == '200') {
				this.data5 = res.data.taskList
				this.data6 = res.data.resourceList
				for (let index = 0; index < this.data5.length; index++) {
					this.currDDQQListAgmachList[index] = JSON.parse(JSON.stringify(this.data6))
				}
				this.list.departments = [].concat(
					res.data.taskList.map((item) => {
						return {
							taskId: item.id,
							areaname: item.areaname,
							departAgmachs: [],
						}
					}),
				)
			} else {
				this.$message({
					message: res.msg,
					type: 'warning',
				})
			}
		},

		showMoreFn11() {
			this.$refs.leafletMap.clearLayers()
			this.$refs.yjsjChildRef.selectionList = []
			this.contType.name = ''

			this.$refs.yjsjChildRef.yjsjScreen({
				current: 1,
				size: 10,
				taskTest: 0,
				areaId: this.areaId,
			})
			//更新应急事件数据
			this.getYjsjData()
			this.$store.commit('invokerightShow', true)
		},

		btnSwitch(item, index) {
			this.choice = item.value
			if (item.name == '上报') {
				this.getCscpBasicHxItemCode1('affectedType')
				this.contType.name = '上报'
				// this.form2 = this.detail
				// this.form2.items = JSON.parse(this.detail.items)
				this.flowList(this.list)
			} else if (item.name == '任务下发') {
				this.contType.name = '任务下发'
				this.isddqqListShow = false
				this.markValue = 20
				this.distributionAction()
				this.distributionActionForECenter()
				this.flowList(this.list)
				this.peripheryList()
			} else if (item.name == '忽略') {
				this.ishlqqShow = true
				this.hlID = this.detail.id
			} else if (item.name == '驳回') {
				this.isbhqqShow = true
				this.hlID = this.detail.id
			} else if (item.name == '确认') {
				this.isqrqqShow = true
				this.qrList = this.detail
			}
		},
		handleBohui(i) {
			if (i == 0) {
				this.isbhqqShow = false
			} else if (i == 1) {
				this.i({
					id: this.hlID,
					node: {
						choice: 3,
					},
				})
			}
		},

		async i(data) {
			let res = await i(data)
			if (res?.code == '200') {
				this.$message({
					message: res.msg,
					type: 'success',
				})
				this.ishlqqShow = false
				this.isbhqqShow = false

				this.contType.name = ''
				// 更新应急事件数据

				//更新应急事件数据
				this.getYjsjData()

				this.$store.commit('invokerightShow', true)
			} else {
				this.$message({
					message: res.msg,
					type: 'warning',
				})
			}
		},

		typeSwitch(index) {
			this.typeShow = index
		},

		typeSwitch2(index) {
			this.typeShow2 = index
		},

		typeSwitch3(index) {
			this.typeShow3 = index
		},

		tjrw() {
			this.form.departments.push({
				deptId: '',
				username: '',
				phone: '',
				resourceList: [
					{
						type: '',
						num: '',
					},
				],
			})
		},

		scrw(index) {
			this.form.departments.splice(index, 1)
		},

		deptChange(cur, idx) {
			this.resources_all = this.departments_all[idx].resources
			var obj = {}
			obj = this.departments_all.find(function(i) {
				return i.deptId === cur
			})
			this.resources_all = obj['resources']
			this.form.departments[idx] = {
				...obj,
				resourceList: this.form.departments[idx].resourceList,
			}
			this.$forceUpdate()
		},

		resourceChange(cur, idx, inx) {
			var obj = {}
			obj = this.resources_all.find(function(i) {
				return i.type === cur
			})
			this.placeholder = '可选' + obj.num + '台'
			this.form.departments[inx].resourceList[idx].name = obj.name
			this.form.departments[inx].resourceList[idx].max = obj.num
			this.form.departments[inx].resourceList[idx].num = ''
		},

		blurText(e) {
			let flag = new RegExp('^[1-9][0-9]*$').test(e.target.value)
			if (!flag) {
				this.$message.warning('请输入正整数')
				e.target.value = ''
			}
		},

		tjsxnj(index) {
			this.form.departments[index].resourceList.push({
				type: '',
				num: '',
			})
			this.$forceUpdate()
		},

		scsxnj(index, i) {
			this.form.departments[index].resourceList.splice(i, 1)
			this.$forceUpdate()
		},

		qxgd() {
			this.contType.name = ''
			this.$refs.leafletMap.clearLayers()
			this.$refs.yjsjChildRef.selectionList = []
			this.getMachineryType()

			//更新应急事件数据
			this.getYjsjData()
			this.$store.commit('invokerightShow', true)
		},

		async getMachineryType() {
			this.sewageDataList3 = []
			let res = await getMachineryType({
				areaId: this.areaId,
				page: this.page,
				size: this.size,
			})
			if (res?.code == '0') {
				this.maxpage = Math.ceil(res.data.recordsTotal / this.size)
				for (let i = 0; i < res.data.data.length; i++) {
					this.sewageDataList3.push([res.data.data[i].name, res.data.data[i].value])
				}
				// this.datas1 = []
				// this.datas1 = [].concat(
				//   res.data.map((it) => it.value)
				// )
				// this.datas2 = []
				// this.datas2 = [].concat(
				//   res.data.map((it) => it.name)
				// )
				// this.datas1 = [['product', '事件']]
				// let arr = res.data.map(item => [item.name, item.value])
				// this.datas1.push(...arr)
				// this.forBarChart()
				// this.yjlxEchartMethod(this.$refs.yjlxEchart)
			} else {
				this.$message({
					message: res.msg,
					type: 'warning',
				})
			}
		},

		rwxfgd() {
			this.dataContent.detail.departments = this.form.departments
			if (typeof this.dataContent.detail.items != 'string') {
				this.dataContent.detail.items = JSON.stringify(this.dataContent.detail.items)
			}
			this.dataContent.detail.node.choice = 4
			this.e(this.dataContent.detail)
		},

		async e(data) {
			let res = await e(data)
			if (res?.code == '200') {
				this.$message({
					message: res.msg,
					type: 'success',
				})
				this.contType.name = ''
				this.getMachineryType()
				//更新应急事件数据
				this.getYjsjData()

				this.$store.commit('invokerightShow', true)
			} else {
				this.$message({
					message: res.msg,
					type: 'warning',
				})
			}
		},

		ckqd(list) {
			this.isZylbShow = true
			this.zylbList = []
			this.zylbList = [].concat(list.map((it) => [it.agmachId, it.agmachType, it.mdlName, it.licensePlateCode, it.time, it.workArea]))
		},

		eventInfoBtnClick11(i, it) {},

		jjjdInfoBtnClick(name, it, selectionList) {
			// this.jjjdData =
			if (name.name == '查看') {
				this.jjjdItemData = it
				this.jjjdItemShow = true
				this.$refs.jjjdInfoChildRef.jjjdItemScreen({
					id: it.id,
					current: 1,
					size: 10,
					taskTest: 0,
					areaId: this.areaId,
				})
			}
		},
		toFixed(number, unit = 10000) {
			if (number === undefined || number === null || isNaN(number)) {
				return 0
			}

			// 将数字除以 10000
			let result = Number(number) / unit

			// 如果结果是整数，则返回整数
			if (Number.isInteger(result)) {
				return Number(result)
			} else {
				// 否则，保留两位小数
				return Number(result.toFixed(2))
			}
		},

		// 搜索地方
		ssqy() {
			this.ssList = []
			if (this.ssnr) {
				this.sspopList = true
				this.queryByList(this.ssnr)
			} else {
				this.sspopList = false
			}
		},
		async queryByList() {
			this.ssList = []
			let res = await queryByList({
				areaName: this.ssnr,
			})
			if (res.code == 0) {
				this.sspopList = true
				this.ssList = []
				this.ssList = res.data
			} else if (res.code == -1) {
				this.sspopList = false
				this.ssList = []
				this.$message({
					message: res.msg,
					type: 'warning',
				})
			}
		},
		ssxz(item) {
			this.areaLevel = item.level
			this.areaId = item.areaId
			this.$store.commit('invokerightShow5', item)
			if (this.$refs.leafletMap.lastAreaCode.indexOf(item.areaId) == -1) {
				this.$refs.leafletMap.lastAreaCode.push(item.areaId)
			}
			if (item.level == '3') {
				//区县级别
				setTimeout(() => {
					this.areaLevel = 3
					this.$refs.leafletMap.isChangeMapBg = true
					this.$refs.leafletMap.currentMapLevel = 'district'
					this.$refs.leafletMap.loadGeoJsonLayerFromUrl(
						`https://geo.datav.aliyun.com/areas_v3/bound/${item.areaId}.json`,
						'geojson',
						false,
					)
					// this.$refs.leafletMap.loadGeoJsonLayerFromUrl(`https://dpzs.camtc.cn/areas_v3/bound/${item.areaId}.json`, 'geojson', false)
				}, 300)
				setTimeout(() => {
					this.removeAllPoi()
				}, 500)
			} else {
				setTimeout(() => {
					this.$refs.leafletMap.isChangeMapBg = true

					this.$refs.leafletMap.loadGeoJsonLayerFromUrl(
						`https://geo.datav.aliyun.com/areas_v3/bound/${item.areaId}_full.json`,
						'geojson',
						item.level == 0 ? true : false,
					)
					// this.$refs.leafletMap.loadGeoJsonLayerFromUrl(`https://dpzs.camtc.cn/areas_v3/bound/${item.areaId}_full.json`, 'geojson', item.level == 0 ? true : false)
				}, 300)
				setTimeout(() => {
					this.removeAllPoi()
				}, 500)
			}
			if (this.areaLevel != 2 && this.areaLevel != 3) {
				this.contentNumberUnitOne = '万台'
				this.serviceIndustryNumberUnit = '万'
			} else {
				this.contentNumberUnitOne = '台'
				this.serviceIndustryNumberUnit = '个'
			}
			this.activaIdx = -1
			this.mapShow = -1
			this.getData()
			this.ssboxShow = false
			this.sspopShow = false
			this.sspopList = false
			this.ssnr = ''
			this.ssList = []
		},

		mapActive(index) {
			if (!this.$store.state.mapLoaded) {
				this.$message.warning('请等待地图加载完成！')
				return
			}
			this.showDrawPolygonTooltip = false

			if (this.mapShow == index && this.njfbShow) {
				this.removeAllPoi()

				this.njfbShow = false
				this.mapShow = -1
				this.$store.commit('updateMapLoaded', true)
				this.$refs.leafletMap.deleteDrawPolygon()
				this.$refs.leafletMap.canGridClick = true
				this.$refs.leafletMap.redrawGeoJSON()
				return
			}

			if (this.mapShow == index) {
				this.sameMapShow = true
			} else {
				this.sameMapShow = false
			}
			this.mapShow = index
			if (this.mapShow == 0) {
				this.removeAllPoi()
				this.njfbShow = true
				this.activaIdx = -1
			} else if (this.mapShow == 1) {
				this.$refs.leafletMap.deleteDrawPolygon()
				this.mapShow = -1
				this.activaIdx = -1
			} else if (this.mapShow == 2) {
				this.removeAllPoi()

				this.$refs.leafletMap.canGridClick = true
				this.$refs.leafletMap.redrawGeoJSON()

				if (!this.$store.state.mapLoaded) {
					this.$message.warning('请等待地图加载完成！')
					return
				}
				this.drawFormVideo()
			}

			if (this.activaIdx == index) {
				this.activaIdx = -1
				this.removeAllPoi()
				if (this.$refs.leafletMap.polygon) {
					this.$refs.leafletMap.polygon.remove()
				}
			} else {
				this.activaIdx = index
			}
		},

		drawFormVideo() {
			this.$store.commit('updateMapLoaded', false)
			this.$refs.leafletMap.drawPoiMarkerCustomCluster(
				[
					{
						latlng: ['33.866940', '115.229080'],
						icon: {
							iconUrl: require('@/assets/mskx/icon_shhfwzx_b.png'),
							iconSize: [48, 62],
							iconAnchor: [16, 42],
						},
						props: {
							id: 'ncjkId',
							type: 'ncjkType',
							info: { deviceSerial: 'FX6180183' },
						},
					},
				],
				'ncjkType',
				true,
				false,
				false,
				{
					largeIconUrl: './imgs/cluster/qy1.png',
					mediumIconUrl: './imgs/cluster/qy2.png',
					smallIconUrl: './imgs/cluster/qy3.png',
				},
			)
		},

		async njlxqh(agmachTypeId) {
			if (!this.$store.state.mapLoaded) {
				this.$message.warning('请等待地图加载完成！')
				return
			}

			this.njfbShow = false
			if (agmachTypeId == -1) {
				return
			}

			this.removeAllPoi()
			if (this.sameMapShow) {
				//判断本次的绘制分类参数是否和上次的一致，若一致则清除掉上次的绘制
				if (this.statisticType == agmachTypeId) {
					this.statisticType = '-1'
					this.removeAllPoi()
					this.$refs.leafletMap.showHeat = false
					this.mapShow = -1
					this.$refs.leafletMap.canGridClick = true
					this.$refs.leafletMap.redrawGeoJSON()
				} else {
					this.statisticType = agmachTypeId
					//若绘制的是区域统计，则进行多边形绘制
					if (this.mapShow == 0) {
						this.$refs.leafletMap.drawPolygon()
					}
				}
			} else {
				this.statisticType = agmachTypeId
				if (this.mapShow == 0) {
					if (agmachTypeId == '1') {
						this.showDrawPolygonTooltip = true
					}
					this.$refs.leafletMap.drawPolygon()
				}
			}
		},

		async getZhjgData() {
			const params = { areaId: this.areaId }
			const res = await getOrgTree(params)
			this.zhjgDataList = res.data
		},

		handleCloseMessageBox() {
			this.isZhddxzShow = false
			this.currentGroupInfo = { uuid: '', name: '' }

			this.getZhddxzData()
		},

		async getZhddxzData() {
			this.zhddxzLoading = true
			this.zhddxzList = []
			const params = {
				pageNumber: 1,
				pageSize: 3,
				lastMsgSeqId: '',
				types: 2,
			}
			try {
				const res = await api_getZhddxzList(params)

				if (res.code == 0) {
					const data = res.data
					const groupIds = data.map((item) => {
						return item.otherId
					})
					const groupInfoRes = await api_getZhddGroupInfoList(groupIds)
					this.zhddxzList = groupInfoRes.data.map((item) => {
						return {
							iconUrl: require('@/assets/img/block-box/yingjishijian-jzqq-icon.png'),
							messageIcon: require('@/assets/img/block-box/zhihuijigou-message-icon.png'),
							uuid: item.uuid,
							label: item.name,
							content: '',
							func: (item_) => {
								this.currentGroupInfo = {
									uuid: item_.uuid,
									name: item_.label,
								}
								this.isZhddxzShow = true
							},
						}
					})
				}
				this.zhddxzLoading = false
			} catch (error) {
				this.zhddxzList = []
				console.log('error--==', error)
				this.zhddxzLoading = false
			}
		},

		async getScjgData() {
			let sczyTotalRes = await getResourceList({
				areaId: this.areaId,
			})
			this.scjgDataList[0].number = sczyTotalRes.data.centerNum || 0
			this.scjgDataList[1].number = sczyTotalRes.data.emergencyCenterNum || 0
			this.scjgDataList[2].number = sczyTotalRes.data.serveNum || 0
		},

		//打开/关闭图例
		njtlTk() {
			this.isnjtlListShow = !this.isnjtlListShow
		},
		//刷新内容
		sxTk() {
			this.getData()
		},
		//打开/关闭搜索地方的弹窗
		ssmkQhs() {
			this.sspopShow = !this.sspopShow
			this.sspopList = false
			this.ssList = []
			this.ssnr = ''
		},
		qctc() {
			this.activaIdx = -1
			this.mapShow = -1
		},

		//点击作业轨迹打开轨迹弹窗 待定
		eventInfoBtnClick15(i, form) {
			this.getAgmachGuiJiInfo(
				{
					agmachId: form.agmachId,
					startTime: form.workStartTime,
					endTime: form.workEndTime,
				},
				form,
			)
		},
		//页面全屏
		async mkssBtn() {
			this.mkdx = !this.mkdx
			await this.$store.commit('invokerightShow4', this.mkdx)
			if (!this.mkdx) {
				// this.mapBtnBottom = 260
				this.mapBtnRight = 507
				this.operateBoxBottom = 15
				// this.operateBoxRight = 637

				this.mapBtnTop = 0
				this.mapBtnLeft = 0
			} else {
				this.mapBtnTop = 0
				this.mapBtnLeft = -480
			}

			// this.$store.commit('invokerightShow', !this.mkdx)
		},
		//调用作业轨迹的接口
		async getAgmachGuiJiInfo(data, infoData) {
			let res = await getAgmachGuiJiInfo(data)
			if (res?.code == '0') {
				// this.workPopShow = false
				let trackData = []
				trackData = [].concat(res.data.map((it) => [it.lon, it.lat]))
				// this.$refs.leafletMap.loadTrackLayer('trcak', trackData)
				this.isMapShow = true
				this.$nextTick(() => {
					this.$refs.mapPop.track(trackData)
					this.$refs.mapPop.getNjInfo(infoData)
				})
			}
		},

		async cscpCurrentUserDetails(data) {
			let res = await cscpCurrentUserDetails(data)
			if (res?.code == '0') {
				this.userName = res.data.username

				// 监听页面可见性变化
				document.addEventListener('visibilitychange', this.handleVisibilityChange)
				this.executeTask()
				this.startInterval()

				this.roleNames = res.data.roleNames
				if (res.data.areaId) {
					const areaId_ = res.data.areaId == '0' ? 100000 : res.data.areaId
					this.areaId = areaId_
					this.areaIdList[0] = areaId_
					this.areaIdNew = res.data.areaIdNew
					this.areaLevel_init = res.data.areaLevel
					this.areaLevel = res.data.areaLevel
					this.$refs.leafletMap.lastAreaCode.push(Number(areaId_))
					this.lastAreaLevel.push(res.data.areaLevel)

					if (areaId_ != res.data.areaIdNew) {
						//区县级别
						setTimeout(() => {
							this.$refs.leafletMap.isChangeMapBg = true
							this.$refs.leafletMap.currentMapLevel = 'district'

							this.$refs.leafletMap.loadGeoJsonLayerFromUrl(
								`https://geo.datav.aliyun.com/areas_v3/bound/${areaId_}.json`,
								'geojson',
								false,
							)
							// this.$refs.leafletMap.loadGeoJsonLayerFromUrl(`https://dpzs.camtc.cn/areas_v3/bound/${res.data.areaId}.json`, 'geojson', false)
						}, 300)
					} else {
						setTimeout(() => {
							this.$refs.leafletMap.isChangeMapBg = true
							this.$refs.leafletMap.loadGeoJsonLayerFromUrl(
								`https://geo.datav.aliyun.com/areas_v3/bound/${areaId_}_full.json`,
								'geojson',
								res.data.areaLevel == 0 ? true : false,
							)
							// this.$refs.leafletMap.loadGeoJsonLayerFromUrl(`https://dpzs.camtc.cn/areas_v3/bound/${res.data.areaId}_full.json`, 'geojson', res.data.areaLevel == 0 ? true : false)
						}, 300)
					}
					if (this.areaLevel == 3) {
						this.scjgShowMore = false
					} else {
						this.scjgShowMore = true
					}
					if (this.areaLevel != 2 && this.areaLevel != 3) {
						this.contentNumberUnitOne = '万台'
						this.serviceIndustryNumberUnit = '万'
					} else {
						this.contentNumberUnitOne = '台'
						this.serviceIndustryNumberUnit = '个'
					}
					// [[最小纬度，最小经度], [最大纬度，最大经度]]
					// [minLat, minLng, maxLat, maxLng]
					const imageBounds = [
						// [3.58, 73.33],
						// [53.55, 135.05]
						[18.143579, 73.502355],
						[53.563269, 135.09567],
					]

					// this.$refs.leafletMap.loadWaterLayer('precipitationType', imageBounds)
				}
			}
		},

		async getMessageGroupToken() {
			await api_getMessageGroupToken(this.userName)
				.then((res_) => {
					console.log('api_getMessageGroupTokenRes_--==', res_)
					let token_ = res_.data[0].access_token
					let messageUserIduserId = res_.data[0].userId || ''
					window.sessionStorage.setItem('messageGroupToken', 'Bearer ' + token_)
					window.sessionStorage.setItem('messageUserId', messageUserIduserId)
					window.sessionStorage.setItem(
						'messageGroupParams',
						JSON.stringify({
							appId: 'a365ad3b-9f76-453f-b3e8-15e379ffc1f9', // 应用id
							appServiceInitAddress: appServiceInitAddressUrl, // 应用初始化地址
							appServiceBaseUrl: '/eepm', // 应用业务前缀接口地址
							userId: messageUserIduserId, // 用户id
							title: '指挥调度小组', // 应用名称
							messageToken: token_,
						}),
					)
					this.$store.commit('unpdateMessageToken', token_)
				})
				.finally(() => {
					this.getData()
				})
		},
		getData() {
			let time = this.dqbf + '-' + this.dqyfs
			if (this.groupLevel == 2) {
				this.startDate = dayjs(this.dqbf + '-')
					.startOf('year')
					.format('YYYY-MM-DD')
				this.endDate = dayjs(this.dqbf + '-')
					.endOf('year')
					.format('YYYY-MM-DD')
			} else if (this.groupLevel == 3) {
				this.startDate = dayjs(time)
					.startOf('month')
					.format('YYYY-MM-DD')
				this.endDate = dayjs(time)
					.endOf('month')
					.format('YYYY-MM-DD')
			}

			//新优化
			//获取应急事件大屏数据
			this.getYjsjData()
			//获取机具进度大屏数据
			this.getJjjdData()
			//获取气象预警大屏统计数据
			this.getQxyjData()
			//获取指挥机构大屏数据
			this.getZhjgData()
			//获取指挥调度小组列表大屏数据
			this.getZhddxzData()
			//获取生产机构大屏统计数据
			this.getScjgData()
			//获取机具资源数据
			this.getMechResourceDataList()
		},

		async getMechResourceDataList() {
			// const res = await getMachineryType({
			// 	areaId: this.areaId,
			// 	page: 1,
			// 	size: 999,
			// })

			let jjzyTotalRes = await getResourceList({
				areaId: this.areaId == '0' ? 100000 : this.areaId,
			})

			if (!jjzyTotalRes.data || jjzyTotalRes.data == 'null') {
			} else {
				this.jjzyTotal = Number(
					this.areaLevel == 0 || this.areaLevel == 1
						? this.toFixed(jjzyTotalRes.data.emergencyMachineryNum)
						: jjzyTotalRes.data.emergencyMachineryNum,
				)
			}
			this.jjzyLoading = true

			let jjzyListRes = await getMachineryType({
				areaId: this.areaId == '0' ? 100000 : this.areaId,
				page: 1,
				size: 999,
			})
			this.jjzyDataList = []
			if (jjzyListRes?.code == '0') {
				if (!jjzyListRes.data || jjzyListRes.data == 'null') {
				} else {
					for (let i = 0; i < jjzyListRes.data.data.length; i++) {
						const dataItem = jjzyListRes.data.data[i]
						let item = {
							title: dataItem.name || '--',
							number: this.areaLevel == 0 || this.areaLevel == 1 ? this.toFixed(dataItem.value) : dataItem.value,
							iconUrl: '',
							argmachType: i,
							argmachName: dataItem.name,
							func: (argmachType) => {
								//获取该农机类型在各省的农机数据
								this.getArgmachByProvinceData(argmachType, 'currentWork')
							},
						}

						if (dataItem.name.includes('谷物联合收割机')) {
							item.iconUrl = require('@/assets/img/block-box/jijv-source/ldsgwlhsgj-icon.png')
						} else if (dataItem.name.includes('拖拉机')) {
							item.iconUrl = require('@/assets/img/block-box/jijv-source/ldstlj-icon.png')
						} else if (dataItem.name.includes('玉米收获机')) {
							item.iconUrl = require('@/assets/img/block-box/jijv-source/ldsymshj-icon.png')
						} else if (dataItem.name.includes('泵')) {
							item.iconUrl = require('@/assets/img/block-box/jijv-source/nysb-icon.png')
						} else if (dataItem.name == '谷物（粮食）干燥机') {
							item.iconUrl = require('@/assets/img/block-box/jijv-source/gw-lsgzj-icon.png')
						} else if (dataItem.name.includes('无人驾驶航空器')) {
							item.iconUrl = require('@/assets/img/block-box/jijv-source/nywrjshkq-icon.png')
						} else {
							item.iconUrl = require('@/assets/img/block-box/jijv-source/other-jijv-icon.png')
						}

						this.jjzyDataList.push(item)
					}
				}
				this.jjzyLoading = false
			} else {
				this.jjzyLoading = false
				this.$message({
					message: jjzyListRes.msg,
					type: 'warning',
				})
			}

			// if (jijvSourceData.length > 0) {
			// 	const data_ = jijvSourceData[0]
			// 	this.jjzyTotal = this.areaLevel == 0 || this.areaLevel == 1 ? this.toFixed(data_.argmachNumber) : data_.argmachNumber
			// 	this.jjzyDataList = data_.data.map((item) => {
			// 		let icon = ''
			// 		switch (item.agmach_type_id) {
			// 			case '1':
			// 				//轮式拖拉机
			// 				icon = require('@/assets/img/block-box/jijv-source/ldstlj-icon.png')
			// 				break
			// 			case '2':
			// 				icon = require('@/assets/img/block-box/jijv-source/ldsgwlhsgj-icon.png')
			// 				break
			// 			case '3':
			// 				icon = require('@/assets/img/block-box/jijv-source/ldsymshj-icon.png')
			// 				break
			// 			case '4':
			// 				icon = require('@/assets/img/block-box/jijv-source/gw-lsgzj-icon.png')
			// 				break
			// 			case '5':
			// 				icon = require('@/assets/img/block-box/jijv-source/ydsgw-lsgzj-icon.png')
			// 				break
			// 			case '6':
			// 				icon = require('@/assets/img/block-box/jijv-source/nysb-icon.png')
			// 				break
			// 			case '7':
			// 				icon = require('@/assets/img/block-box/jijv-source/nywrjshkq-icon.png')
			// 				break
			// 			default:
			// 				icon = require('@/assets/img/block-box/jijv-source/other-jijv-icon.png')
			// 				break
			// 		}
			// 		return {
			// 			title: item.agmach_type_name,
			// 			number: this.areaLevel == 0 || this.areaLevel == 1 ? this.toFixed(item.argmachNumber) : item.argmachNumber,
			// 			iconUrl: icon,
			// 			argmachType: item.agmach_type_id,
			// 			func: (argmachType) => {
			// 				//获取该农机类型在各省的农机数据
			// 				this.getArgmachByProvinceData(argmachType, 'currentWork')
			// 			},
			// 		}
			// 	})
			// } else {
			// 	this.$message({
			// 		message: res.msg,
			// 		type: 'warning',
			// 	})
			// }

			// if (jjzyData.length > 0) {
			// 	// const data_ = currentZxnjRes.data[0]
			// 	const data_ = jjzyData[0]

			// 	this.jjzyDataList = data_.data.map((item) => {
			// 		let icon = ''
			// 		// 					agmach_type_id
			// 		// agmach_type_name
			// 		switch (item.agmach_type_id) {
			// 			case '510101':
			// 				//轮式拖拉机
			// 				icon = require('@/assets/img/block-box/lstlj-icon.png')
			// 				break
			// 			case '150105':
			// 				icon = require('@/assets/img/block-box/gwlhsgj-icon.png')
			// 				break
			// 			case '150106':
			// 				icon = require('@/assets/img/block-box/ymshj-icon.png')
			// 				break
			// 			case '150107':
			// 				icon = require('@/assets/img/block-box/bzj-icon.png')
			// 				break
			// 			case '120401':
			// 				icon = require('@/assets/img/block-box/cyj-icon.png')
			// 				break
			// 			default:
			// 				icon = require('@/assets/img/block-box/other-argmach-icon.png')
			// 				break
			// 		}
			// 		return {
			// 			label: item.agmach_type_name,
			// 			number: this.areaLevel == 0 || this.areaLevel == 1 ? this.toFixed(item.argmachNumber) : item.argmachNumber,
			// 			iconUrl: icon,
			// 			argmachType: item.agmach_type_id,
			// 			func: (argmachType) => {
			// 				//获取该农机类型在各省的农机数据
			// 			},
			// 		}
			// 	})
			// }
		},

		async getYjsjData() {
			//更新应急事件数据
			let res = await todoAScreen({
				areaId: this.areaId,
			})
			if (res?.code == '200') {
				this.yjsjTotalData[0].number = res.data.totalNum
				this.yjsjTotalData[1].number = res.data.ybNum
				this.yjsjTotalData[2].number = res.data.wbNum

				this.yjsjTableData = []

				this.yjsjTableData = []
					.concat(
						res.data.zFlowTaskList.map((it) => ({
							iconUrl: require('@/assets/img/block-box/yingjishijian-jzqq-icon.png'),
							label: it.requestType,
							date: it.time,
							content: it.comments,
						})),
					)
					.slice(0, 2)
			} else {
				this.$message({
					message: res.msg,
					type: 'warning',
				})
			}
		},

		async getJjjdData() {
			this.jjjdListLoading = true
			const params = {
				// key: '',
				areaId: !this.areaId || this.areaId == '100000' ? '' : this.areaId,
				page: 1,
				size: 3,
				schedule: 1,
				taskTest: 0,
			}

			try {
				const res = await api_getJjjdDataList(params)
				const data_ = res.data
				let jjjdDataList_ = []
				data_.forEach((item) => {
					jjjdDataList_.push({
						iconUrl: require('@/assets/img/block-box/yingjishijian-jjjd-icon.png'),
						label: item.comments || '--',
						content: `调度机具${item.agmachNum}台，作业中${item.wokNum}台，行进中${item.driveNum}台`,
					})
				})

				this.jjjdDataList = jjjdDataList_.slice(0, 3)
				this.jjjdListLoading = false
			} catch {
				this.jjjdListLoading = false
				this.jjjdDataList = []
			}
		},

		async getQxyjData() {
			this.qxyjListLoading = true
			const params = {
				pageNo: '1',
				pageSize: '100',
				type: '',
				releaseTime: '',
				regionCode: this.areaId == '100000' ? '' : this.areaId,
			}
			try {
				const res = await api_getQxyjData(params)
				if (res?.code == 0) {
					this.qxyjDataList = []
					const data_ = res.data.alarm
					data_.forEach((item) => {
						// if(item.type == '强降雨'){
						// 	this.qxyjDataList
						// }
						if (
							item.type == '强降雨' ||
							item.type == '雷雨大风' ||
							item.type == '暴雨' ||
							item.type == '冰雹' ||
							item.type == '干旱' ||
							item.type == '大风' ||
							item.type == '台风'
						) {
							this.qxyjDataList.push({
								weatherIConUrl: getWeatherIConUrl(item.type),
								label: item.type,
								data: getQxData(item.levelList),
							})
						}
					})
				}
				this.qxyjListLoading = false
			} catch {
				this.qxyjDataList = []
				this.qxyjListLoading = false
			}

			function getWeatherIConUrl(type) {
				let bg = ''
				switch (type) {
					case '强降雨':
						bg = require('@/assets/img/block-box/qixiangyujing/qixiangyujing-qjy.png')
						break
					case '雷雨大风':
						bg = require('@/assets/img/block-box/qixiangyujing/qixiangyujing-lydf.png')
						break
					case '暴雨':
						bg = require('@/assets/img/block-box/qixiangyujing/qixiangyujing-by.png')
						break
					case '冰雹':
						bg = require('@/assets/img/block-box/qixiangyujing/qixiangyujing-bb.png')
						break
					case '干旱':
						bg = require('@/assets/img/block-box/qixiangyujing/qixiangyujing-gh.png')
						break
					case '大风':
						bg = require('@/assets/img/block-box/qixiangyujing/qixiangyujing-df.png')
						break
					case '台风':
						bg = require('@/assets/img/block-box/qixiangyujing/qixiangyujing-tf.png')
						break
					default:
						bg = ''
						break
				}

				return bg
			}

			function getQxData(list) {
				const dataItem = [
					{
						color: '#3860AB',
						label: '蓝色预警',
						name: '蓝色',
						number: 0,
					},
					{
						color: '#CFC520',
						label: '黄色预警',
						name: '黄色',
						number: 0,
					},
					{
						color: '#EC8D15',
						label: '橙色预警',
						name: '橙色',
						number: 0,
					},
					{
						color: '#CA3029',
						label: '红色预警',
						name: '红色',
						number: 0,
					},
				]
				list.forEach((item) => {
					if (item.name == '蓝色') {
						dataItem[0].number = item.value
					} else if (item.name == '黄色') {
						dataItem[1].number = item.value
					} else if (item.name == '橙色') {
						dataItem[2].number = item.value
					} else if (item.name == '红色') {
						dataItem[3].number = item.value
					}
				})
				return dataItem
			}
		},

		clickAlertLevelItem(i1, i2) {
			if (this.currentClickAlertTypeIndex == i1 && this.currentClickAlertLevelIndex == i2) {
				this.removeAllPoi()
				this.weatherAlarmType = ''
				this.weatherAlarmLevel = ''
				this.currentClickAlertTypeIndex = -1
				this.currentClickAlertLevelIndex = -1
				return
			}

			this.currentClickAlertTypeIndex = i1
			this.currentClickAlertLevelIndex = i2
			const alertTypeItem = this.qxyjDataList[i1]
			const alertLevel = alertTypeItem.data[i2]

			this.weatherAlarmType = alertTypeItem.label
			this.weatherAlarmLevel = alertLevel.name
			this.removeAllPoi()
			this.toolTipShow = false
			this.getAllAlarmWithGPS(true, 'v2')
		},
		async getAllAlarmWithGPS(directDraw = false, apiType = 'v1') {
			const params = {
				type: this.weatherAlarmType,
				level: this.weatherAlarmLevel,
				regionCode: this.areaId == 100000 ? '' : this.areaId,
			}
			let res = null
			if (apiType == 'v1') {
				res = await getAllAlarmWithGPS(params)
			} else {
				res = await getAllAlarmWithGPSV2(params)
			}
			if (directDraw) {
			} else {
				if (this.markList.findIndex((item) => item === 2) == -1) {
					return false
				}
			}

			if (!res || !res.data.length) {
				this.$message({
					message: '暂无天气预警信息',
					offset: 100,
				})
			}
			let data = res.data.map((item) => {
				return {
					latlng: [item.latitude, item.longitude],
					icon: {
						// iconUrl: require('@/assets/mskx/icon_nj_b.png'),
						// iconSize: [48,62],
						iconUrl: require('@/assets/mskx/icon_yj_y.png'),
						iconSize: [100, 100],
						iconAnchor: [50, 100],
					},
					props: {
						id: 'jcyjId',
						type: 'jcyjType',
						info: { ...item },
					},
				}
			})
			this.$refs.leafletMap.drawPoiMarkerCustomCluster(data, 'jcyjType', true, false, false, {
				largeIconUrl: './imgs/cluster/yj1.png',
				mediumIconUrl: './imgs/cluster/yj2.png',
				smallIconUrl: './imgs/cluster/yj3.png',
			})
		},
		//待定
		handleNjInfo() {
			if (this.dwType == 'njfbType2') {
				if (this.trajectoryMark == 1) {
					this.njInfoShow = false
					this.workPopShow = true
					this.$refs.historyWorkPop.init()
				} else {
					this.$message('这台农机无轨迹')
				}
			} else {
				this.njInfoShow = false
				this.workPopShow = true
				this.$refs.historyWorkPop.init()
			}
		},
		// 标记点基础信息弹窗内按钮点击事件 待定
		clickBasicInformationBtn(i, name) {
			if (name == '区域农机社会化服务中心详情') {
				this.basicInfomationShow = false
			}
			if (name == '农机应急作业服务队详情') {
				this.basicInfomationShow = false
			}
			if (name == '区域农业应急救灾中心详情') {
				this.basicInfomationShow = false
			}
		},

		//待定
		async aqjgGetTokenFn() {
			const res = await aqjgGetToken()
			if (res?.data) {
				localStorage.setItem('aqjgToken', res.data.access_token)
			}
		},

		//点击某个行政区划 所获取的数据
		gridClick(properties) {
			// mapShow:[1]:机械总动力；[2]耕种收综合机械化率；[3]机械作业面积
			this.$store.commit('invokerightShow2', properties)
			if (properties.level == 'province') {
				this.scjgShowMore = true
				this.areaLevel = 1
				this.lastAreaLevel.push(this.areaLevel)
			} else if (properties.level == 'city') {
				this.scjgShowMore = true
				this.areaLevel = 2
				this.lastAreaLevel.push(this.areaLevel)
			} else if (properties.level == 'district' && this.areaLevel != 3) {
				this.areaLevel = 3
				this.lastAreaLevel.push(this.areaLevel)
				this.scjgShowMore = false
			}

			if (this.areaLevel != 2 && this.areaLevel != 3) {
				this.contentNumberUnitOne = '万台'
				this.serviceIndustryNumberUnit = '万'
			} else {
				this.contentNumberUnitOne = '台'
				this.serviceIndustryNumberUnit = '个'
			}
			this.ssboxShow = false
			this.sspopShow = false
			this.sspopList = false
			this.ssnr = ''
			this.ssList = []
			this.areaId = properties.adcode

			this.removeAllPoi()
			this.getData()
			setTimeout(() => {
				this.$refs.leafletMap.map.fitBounds(this.$refs.leafletMap.geojsonLayer.getBounds())
			}, 1000)

			this.$store.commit('updateMapLoaded', true)
		},

		async polygonResult(points) {
			this.showDrawPolygonTooltip = false
			if (points.length > 0) {
				let points_ = points.slice(0, points.length - 1)
				points_.push(points[0])
				const latLogPoints = points_.map((item) => {
					return {
						longitude: item[1],
						latitude: item[0],
					}
				})
				const params = {
					type: this.statisticType == '1' || this.statisticType == '2' ? this.statisticType : this.statisticType == '3' ? '1' : '',
					level: this.statisticType == '3' ? '1' : '',
					pointData: latLogPoints,
				}
				try {
					//防抖
					clearTimeout(this.polygonResultTimer)
					const argmachList = ['履带式拖拉机', '履带式谷物联合收割机', '履带式玉米收获机', '轮式拖拉机', '移动式粮食干燥机']
					this.polygonResultTimer = setTimeout(async () => {
						// try {} catch (error) {
						// 	this.$message.warning('暂无数据')
						// }

						const res = await api_getMachineryDataByPolygon(params)
						const data_ = res.data

						if (data_.length > 0 && data_.length < 2) {
							let total1 = [data_[0].centerNum, data_[0].agmachInfo]
							const list1_ = data_[0].nameValueList
							const columnNames = []

							for (let i = 0; i < 5; i++) {
								const element = list1_[0][i]
								columnNames.push(element.name)
							}
							columnNames.push(...argmachList)

							let list = [columnNames]
							for (let i = 0; i < list1_.length; i++) {
								const childData = []
								const items = list1_[i]
								for (let j = 0; j < 5; j++) {
									const element = items[j]
									childData.push(element.value || '')
								}

								for (let index = 0; index < argmachList.length; index++) {
									childData.push(0)
								}

								items.forEach((item) => {
									if (argmachList.indexOf(item.name) != -1) {
										childData[argmachList.indexOf(item.name) + 5] = item.value || ''
									}
								})
								list.push(childData)
							}

							const sheetData = [
								['汇总：', '', '', '', '', '', '', '', '', ''],
								['受灾地区', '农机社会化服务中心数量', '本地现有农机'],
								data_[0].type == '1' ? ['', total1[0], total1[1]] : ['', '', ''],
								['支援地区', '农机社会化服务中心数量', '本地现有农机'],
								data_[0].type == '2' ? ['', total1[0], total1[1]] : ['', '', ''],
								['明细：'],
								...list,
							]

							this.exportData('汇总', sheetData)
						} else if (data_.length > 0) {
							//受灾地区
							let total1 = [data_[0].centerNum, data_[0].agmachInfo]
							//支援地区
							let total2 = [data_[1].centerNum, data_[1].agmachInfo]

							//获取受灾地区数据
							const list1_ = data_[0].nameValueList
							//获取支援地区数据
							const list2_ = data_[1].nameValueList
							//获取表头
							// const columnNames1 = list1_[0].map((item) => {
							// 	return item.name
							// })

							const columnNames1 = []

							for (let i = 0; i < 5; i++) {
								const element = list1_[0][i]
								columnNames1.push(element.name)
							}
							columnNames1.push(...argmachList)

							let list1 = []

							//获取受灾地区的每行数据
							for (let i = 0; i < list1_.length; i++) {
								const rowData1 = []

								// const items = list1_[i]
								// const childData = items.map((item) => {
								// 	return item.value || ''
								// })
								// list1.push(childData)

								const items = list1_[i]
								for (let j = 0; j < 5; j++) {
									const element = items[j]
									rowData1.push(element.value || '')
								}

								for (let index = 0; index < argmachList.length; index++) {
									rowData1.push(0)
								}

								items.forEach((item) => {
									if (argmachList.indexOf(item.name) != -1) {
										rowData1[argmachList.indexOf(item.name) + 5] = item.value || ''
									}
								})
								list1.push(rowData1)
							}

							//获取表头
							// const columnNames2 = list1_[1].map((item) => {
							// 	return item.name
							// })

							const columnNames2 = []

							for (let i = 0; i < 5; i++) {
								const element = list2_[0][i]
								columnNames2.push(element.name)
							}
							columnNames2.push(...argmachList)

							let list2 = []

							//获取支援地区的每行数据
							for (let i = 0; i < list2_.length; i++) {
								const rowData2 = []

								// const items = list2_[i]
								// const childData = items.map((item) => {
								// 	return item.value || ''
								// })
								// list2.push(childData)

								const items = list2_[i]
								for (let j = 0; j < 5; j++) {
									const element = items[j]
									rowData2.push(element.value || '')
								}

								for (let index = 0; index < argmachList.length; index++) {
									rowData2.push(0)
								}

								items.forEach((item) => {
									if (argmachList.indexOf(item.name) != -1) {
										rowData2[argmachList.indexOf(item.name) + 5] = item.value || ''
									}
								})
								list2.push(rowData2)
							}

							const sheetData = [
								['汇总：', '', '', '', '', '', '', '', '', ''],
								['受灾地区', '农机社会化服务中心数量', '本地现有农机'],
								['', total1[0], total1[1]],
								['支援地区', '农机社会化服务中心数量', '本地现有农机'],
								['', total2[0], total2[1]],
								['明细：'],
								columnNames1,
								...list1,
								...list2,
							]

							this.exportData('汇总', sheetData)
						} else {
							this.$message({
								message: '暂无数据',
								type: 'warning',
							})
						}
					}, 500)
				} catch (error) {
					this.$message({
						message: '暂无数据',
						type: 'warning',
					})
				}
			} else {
				this.polygonPoints = []
			}
		},

		exportData(name, sheetData) {
			// 2. 创建工作簿和工作表
			const workbook = XLSX.utils.book_new()
			const worksheet = XLSX.utils.aoa_to_sheet(sheetData)
			// 3. 设置合并单元格（合并相同部门的单元格）
			const mergeRegions = []
			mergeRegions.push({
				s: { r: 1, c: 0 },
				e: { r: 2, c: 0 },
			})
			mergeRegions.push({
				s: { r: 3, c: 0 },
				e: { r: 4, c: 0 },
			})
			worksheet['!merges'] = mergeRegions

			// 4. 设置单元格样式（自动换行、边框等）
			// 通用边框样式
			const borderStyle = {
				// top: { style: 'thin', color: { rgb: '000000' } },
				// bottom: { style: 'thin', color: { rgb: '000000' } },
				// left: { style: 'thin', color: { rgb: '000000' } },
				// right: { style: 'thin', color: { rgb: '000000' } },
				top: { color: { rgb: '000000' } },
				bottom: { color: { rgb: '000000' } },
				left: { color: { rgb: '000000' } },
				right: { color: { rgb: '000000' } },
			}
			console.log('sheetData--==', sheetData)
			// 设置表头样式
			for (let col = 0; col < sheetData[6].length; col++) {
				const cellRef = XLSX.utils.encode_cell({ r: 6, c: col })
				console.log('cellRef--==', cellRef)
				// 确保单元格存在
				if (!worksheet[cellRef]) {
					worksheet[cellRef] = { t: 's', v: sheetData[6][col] }
				}
				worksheet[cellRef]['s'] = {
					// font: { bold: true, color: { rgb: '000000' } },
					font: { bold: true, color: { rgb: '000000' } },

					fill: { fgColor: { rgb: 'D9D9D9' } }, // 背景
					alignment: { wrapText: true, horizontal: 'left', vertical: 'center' },
					border: borderStyle,
				}
			}
			// 设置数据行样式
			for (let row = 0; row < 5; row++) {
				for (let col = 0; col < sheetData[row].length; col++) {
					const cellRef = XLSX.utils.encode_cell({ r: row, c: col })
					// 初始化单元格样式（如果不存在）
					if (!worksheet[cellRef]) {
						worksheet[cellRef] = { t: 's', v: sheetData[row][col] }
					}
					if (col == 0) {
						worksheet[cellRef]['s'] = {
							alignment: { vertical: 'center', horizontal: 'center', wrapText: true }, //vertical: 'top'（因自动换行需要）
							border: borderStyle,
						}
					} else {
						worksheet[cellRef]['s'] = {
							alignment: { vertical: 'center', horizontal: 'left', wrapText: true }, //vertical: 'top'（因自动换行需要）
							border: borderStyle,
						}
					}
				}
			}

			// 设置数据行样式
			for (let row = 7; row < sheetData.length; row++) {
				for (let col = 0; col < sheetData[row].length; col++) {
					const cellRef = XLSX.utils.encode_cell({ r: row, c: col })

					// 初始化单元格样式（如果不存在）
					if (!worksheet[cellRef]) {
						worksheet[cellRef] = { t: 's', v: sheetData[row][col] }
					}

					worksheet[cellRef].s = {
						alignment: { wrapText: true, horizontal: 'left', vertical: 'center' },
						border: borderStyle,
					}
				}
			}

			// 5. 设置列宽
			worksheet['!cols'] = [
				{ wch: 12 }, // 首列列宽
				{ wch: 30 }, // 描述列列宽
				{ wch: 30 }, // 描述列列宽
				{ wch: 14 }, // 季度列宽
				{ wch: 16 }, // 季度列宽
				{ wch: 18 }, // 季度列宽
				{ wch: 25 }, // 季度列宽
				{ wch: 22 }, // 季度列宽
				{ wch: 15 }, // 季度列宽
				{ wch: 28 }, // 季度列宽
			]

			// 6. 将工作表添加到工作簿
			XLSX.utils.book_append_sheet(workbook, worksheet, '汇总')

			// 7. 导出Excel文件
			const excelBuffer = XLSXStyle.write(workbook, { bookType: 'xlsx', type: 'buffer' })
			const blob = new Blob([excelBuffer], { type: 'application/octet-stream' })
			saveAs(blob, '定向统计数据.xlsx')
		},
		//返回上一层地图后所获取的数据
		operate(areaId, areaId2) {
			this.ssboxShow = true
			this.sspopShow = false
			this.sspopList = false
			this.ssnr = ''
			this.ssList = []
			this.areaId = areaId
			this.$store.commit('invokerightShow3', areaId)
			if (this.areaId) {
				this.scjgShowMore = true

				if (this.areaId == 100000) {
					this.areaLevel = 0
					this.$refs.leafletMap.canGridClick = true
				} else {
					this.areaLevel = this.areaLevel_init + this.$refs.leafletMap.lastAreaCode.indexOf(areaId)
					this.$refs.leafletMap.canGridClick = true
				}
			} else {
				this.areaLevel = 0
			}

			this.mapShow = -1
			this.statisticType = '-1'

			if (this.areaLevel != 2 && this.areaLevel != 3) {
				this.contentNumberUnitOne = '万台'
				this.serviceIndustryNumberUnit = '万'
			} else {
				this.contentNumberUnitOne = '台'
				this.serviceIndustryNumberUnit = '个'
			}
			this.removeAllPoi()
			this.getData()

			this.removeAllPoi()
			if (this.mapShow == 1 || this.mapShow == 2 || this.mapShow == 3) {
			} else {
				this.$store.commit('updateMapLoaded', true)
			}
			setTimeout(() => {
				if (this.areaLevel == 0) {
					this.$refs.leafletMap.map.setView(this.$refs.leafletMap.mapOptions.center, this.$refs.leafletMap.mapOptions.zoom)
				} else {
					this.$refs.leafletMap.map.fitBounds(this.$refs.leafletMap.geojsonLayer.getBounds())
				}
			}, 1000)
			// this.$refs.leafletMap.map.fitBounds(this.$refs.leafletMap.geojsonLayer.getBounds())
		},
		//农机图标点击
		poiClick(layerId, it) {
			if (it.trajectoryMark) {
				this.trajectoryMark = it.trajectoryMark
			}
			this.ssboxShow = false
			this.sspopShow = false
			this.sspopList = false
			this.ssnr = ''
			this.ssList = []
			// this.agmachId = it[0]
			this.agmachId = it.props.info.agmachId
			this.dwType = layerId
			if (layerId == 'njfbType') {
				this.locHistory2(it.props.info.agmachId)
			} else if (layerId == 'zxnjType') {
				this.locHistory2(it.props.info.agmachId)
			} else if (layerId == 'njfbType2') {
				this.trajectoryMark = it[2]
				if (it[1] == 1) {
					this.locHistory2(it[0])
				} else {
					this.$message('这台农机无基本信息')
				}
			} else if (layerId == 'ncjkType') {
				getNcjkToken().then((rel) => {
					this.ncjkDialogShow = true
					getNcjkUrl('FX6180183').then(async (res) => {
						const UIKitDEMO = await new EZUIKit.EZUIKitPlayer({
							id: `ezuikit-player`,
							url: res.result,
							accessToken: rel.result.accessToken,
						})
					})
				})
			} else if (layerId == 'jcyjType') {
				this.qxyjDetailList = [
					0,
					it.props.info.time,
					it.props.info.type,
					it.props.info.area,
					it.props.info.level,
					it.props.info.detail,
					it.props.info.title,
				]
				this.isQxyjDetailShow = true
			}
		},

		//获取农机历史数据
		async locHistory2(data) {
			let res = await locHistory2({
				agmachId: data,
				// page_no: 1,
				// page_size: 10
				pageNo: 1,
				pageSize: 10,
			})
			this.njInfoShow = true
			// this.currentAgmachInfo = res.data[0].rows[0]
			if (res.data) {
				this.currentAgmachInfo = res.data[0]
			} else {
				this.currentAgmachInfo = {}
			}
		},
		//移除地图中的所有覆盖物
		removeAllPoi() {
			this.$refs.leafletMap.removeLayer('njfbType')
			this.$refs.leafletMap.removeLayer('zxnjType')
			this.$refs.leafletMap.removeLayer('heat')
			this.$refs.leafletMap.removeLayer('hgzxType')
			this.$refs.leafletMap.removeLayer('yjwzType')
			this.$refs.leafletMap.removeLayer('yjjzType')
			this.$refs.leafletMap.removeLayer('jcyjType')
			this.$refs.leafletMap.removeLayer('fwdType')
			this.$refs.leafletMap.removeLayer('yjsj')
			this.$refs.leafletMap.removeLayer('ncjkType')
			this.$refs.leafletMap.removeLayer('numberMarker')
			this.$refs.leafletMap.clearCluster()
			if (this.$refs.leafletMap.polygon) {
				this.$refs.leafletMap.polygon.remove()
			}
		},
		handleShowMore(type) {
			switch (type) {
				case 'yjsj':
					//应急事件
					this.isYjsjTableShow = true
					this.isJjjdTableShow = false
					this.qxyjListShow = false
					this.isScjgTableShow = false
					this.$refs.yjsjChildRef.yjsjScreen({
						current: 1,
						size: 10,
						taskTest: 0,
						areaId: this.areaId,
					})
					break
				case 'jjjd':
					//机具进度
					this.isYjsjTableShow = false
					this.isJjjdTableShow = true
					this.qxyjListShow = false
					this.isScjgTableShow = false
					this.$refs.jjjdChildRef.jjjdScreen({
						page: 1,
						size: 10,
						taskTest: 0,
						areaId: this.areaId,
						schedule: 1,
					})
					break
				case 'qxyj':
					//气象预警
					this.isYjsjTableShow = false
					this.isJjjdTableShow = false
					this.qxyjListShow = true
					this.isScjgTableShow = false
					break
				case 'zhjg':
					//指挥机构
					this.isYjsjTableShow = false
					this.isJjjdTableShow = false
					this.isScjgTableShow = false
					this.qxyjListShow = false
					break
				case 'scjg':
					//生产机构
					this.isYjsjTableShow = false
					this.isJjjdTableShow = false
					this.qxyjListShow = false
					this.isScjgTableShow = true

					this.$refs.scjgListRef.scjgScreen({
						page: 1,
						size: 10,
					})

					break
				case 'jjzy':
					//机具资源
					this.isYjsjTableShow = false
					this.isJjjdTableShow = false
					this.qxyjListShow = false
					this.isScjgTableShow = false
					break
				default:
					//其它
					this.isYjsjTableShow = false
					this.isJjjdTableShow = false
					this.qxyjListShow = false
					this.isScjgTableShow = false
					break
			}
			console.log(`打开${type}弹窗`)
		},

		handleJjjdItemClick() {
			//机具进度
			this.isYjsjTableShow = false
			this.isJjjdTableShow = true
			this.qxyjListShow = false
			this.isScjgTableShow = false
			this.$refs.jjjdChildRef.jjjdScreen({
				page: 1,
				size: 10,
				taskTest: 0,
				areaId: this.areaId,
				schedule: 1,
			})
		},

		handleQxyjClick(i, it) {
			this.qxyjDetailList = it
			this.isQxyjDetailShow = true
		},

		qxyjDetailEventInfoBtnClick(i) {},

		scjgInfoBtnClick(i, it, params) {
			this.scjgDetailData = it
			const { type, level } = params
			this.scjgCurrentSelectedTab = type
			this.scjgCurrentSelectedLevel = level
			this.isScjgDetailShow = true
		},

		ryjjmxClick(i, it) {
			this.departmentId = it.id
			this.isRyjjmxDataShow = true
		},

		fhEmitai() {
			this.isRyjjmxDataShow = false
			this.isScjgDetailShow = true
		},

		mark(i, list) {
			this.removeAllPoi()
			this.activaIdx = i
			this.markList = list
			this.toolTipShow = false
			if (this.markList.findIndex((item) => item === 0) !== -1) {
				this.yjsjScreenDd({
					taskTest: 0,
					areaId: this.areaId,
				})
			}
			if (this.markList.findIndex((item) => item === 1) !== -1) {
				// this.ddrwScreenDd({
				// 	taskTest: 0,
				// 	areaId: this.areaId,
				// })
			}
			if (this.markList.findIndex((item) => item === 2) !== -1) {
				// this.getAllAlarmWithGPS()
			}
			if (this.markList.findIndex((item) => item === 3) !== -1) {
				// this.jgzzShow = true
				// if (this.resourceType == 0) {
				// 	this.sourceType = false
				// 	if (this.areaLevel == 0 || this.areaLevel == 1) {
				// 		console.log('this.areaId', this.areaId)
				// 		this.getDistributionCount()
				// 	} else {
				// 		this.getNjAreaDayTypes2()
				// 	}
				// }
			}
			if (i === 4) {
				// this.openLaunchMeetingDialog()
			}
			if (this.markList.findIndex((item) => item === 5) !== -1) {
				// this.removeAllPoi()
				// this.iskqzyShow = true
				// this.exitCount()
				// if (this.areaLevel != 0) {
				// 	this.exit(0)
				// 	this.exit(1)
				// } else {
				// 	this.onlyExit()
				// }
			}
			if (this.markList.findIndex((item) => item === 6) !== -1) {
				// this.removeAllPoi()
				// this.geojson()
			} else {
				if (this.$refs.leafletMap.polygon) {
					this.$refs.leafletMap.polygon.remove()
				}
			}
		},

		async yjsjScreenDd(it) {
			let res = await yjsjScreenDd(it)
			let data = res.data.map((item) => {
				return {
					latlng: [item.lat, item.lon],
					icon: {
						iconUrl:
							item.state == '待处置'
								? require('@/assets/bjnj/ddqqDt1.png')
								: item.state == '进行中'
								? require('@/assets/bjnj/ddqqDt2.png')
								: require('@/assets/bjnj/ddqqDt3.png'),
						iconSize: [48, 62],
						iconAnchor: [16, 42],
					},
					props: {
						id: 'ddqqId',
						type: 'ddqqType',
						info: { ...item },
					},
				}
			})
			this.$refs.leafletMap.drawPoiMarker(data, 'ddqqType', false)
		},
	},
}
</script>

<style lang="less" scoped>
@font-face {
	font-family: QuartzRegular;
	src: url(~@/assets/leader/font/QuartzRegular.ttf);
}

@font-face {
	/*给字体命名*/
	font-family: 'YouSheBiaoTiHei';
	/*引入字体文件*/
	src: url(~@/assets/font/youshebiaotihei.ttf);
	font-weight: normal;
	font-style: normal;
}

@font-face {
	/*给字体命名*/
	font-family: 'AlibabaPuHuiTi';
	/*引入字体文件*/
	src: url(~@/assets/font/AlibabaPuHuiTi-2-85-Bold.ttf);
	font-weight: normal;
	font-style: normal;
}

@keyframes rotateS {
	0% {
		transform: rotate(0);
	}

	100% {
		transform: rotate(360deg);
	}
}

@keyframes rotateN {
	0% {
		transform: rotate(360deg);
	}

	100% {
		transform: rotate(0);
	}
}

@keyframes rotateY {
	0% {
		transform: rotate(0);
	}

	100% {
		transform: rotateY(360deg);
	}
}

.leader_sjts {
	width: 1920px;
	height: 1080px;
	position: absolute;
	top: 0;
	display: flex;
	justify-content: space-between;

	.leader_city_box {
		width: 450px;

		.leader_zt_contain {
			height: 100%;
		}
	}

	.box {
		.cont {
			width: 100%;
			height: 100%;
			position: relative;
		}

		.wrapper1 {
			width: 100%;
			height: 100%;

			.introduce_video {
				width: 459px;
				height: 192px;

				::v-deep .video-wrapper {
					padding-bottom: 41.25% !important;
				}
			}

			.introduce {
				margin-top: 11px;
				width: 100%;
				height: 120px;
				padding: 18px 26px;
				background: url(~@/assets/hszl/bg1.png) no-repeat;
				background-size: 100% 100%;
				font-size: 16px;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #4fddff;
				line-height: 22px;
				letter-spacing: 2px;
				text-align: left;
			}
		}

		.wrapper4 {
			width: 100%;
			height: 100%;
			padding: 30px 0 30px 8px;

			ul {
				width: 100%;
				height: 100%;
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;
				align-content: space-between;

				li {
					display: flex;
					height: 49px;
					gap: 10px;

					.icon {
						width: 46px;
						height: 49px;
					}

					.info {
						margin-top: -6px;

						.label {
							font-size: 14px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #ffffff;
							line-height: 20px;
							text-align: left;
							padding-left: 5px;
						}

						.line {
							width: 87px;
							height: 6px;
							background: url('~@/assets/hszl/line1.png') no-repeat;
						}

						.count {
							text-align: left;
							margin-top: 4px;
							width: 87px;
							height: 22px;
							line-height: 22px;
							background: linear-gradient(96deg, #1d4975 0%, rgba(19, 56, 87, 0.24) 100%);
							font-size: 14px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #a8b1b9;
							padding-left: 5px;

							span {
								font-size: 18px;
								font-family: PingFangSC, PingFang SC;
								font-weight: normal;
								color: #ffffff;
								line-height: 22px;
								margin-right: 3px;
							}
						}
					}
				}
			}
		}

		.wrapper5 {
			width: 100%;
			height: 100%;
			padding-top: 31px;

			.info {
				width: 100%;
				height: 71px;
				padding-left: 8px;
				display: flex;
				justify-content: space-between;

				.total {
					width: 130px;
					height: 100%;
					background: url('~@/assets/hszl/bg4.png') no-repeat;
					display: grid;
					place-items: center;

					.cont {
						padding-top: 13px;

						.label {
							font-size: 14px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #ffffff;
							line-height: 20px;
						}

						.value {
							font-size: 18px;
							font-family: PingFangSC, PingFang SC;
							font-weight: normal;
							color: #ffffff;
							line-height: 22px;
							background: linear-gradient(180deg, #ffffff 0%, #00fffa 67%, #04a1ff 100%);
							background-clip: text;
							-webkit-background-clip: text;
							-webkit-text-fill-color: transparent;

							span {
								font-size: 12px;
							}
						}
					}
				}

				.counts {
					width: 312px;
					height: 100%;
					background: url('~@/assets/hszl/bg5.png') no-repeat;
					display: flex;
					padding: 0 9px;

					.men {
						text-align: left;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						margin-right: 4px;

						.count {
							height: 21px;
							font-size: 16px;
							font-family: PingFangSC, PingFang SC;
							font-weight: normal;
							line-height: 19px;
							background: linear-gradient(180deg, #ffffff 0%, #00ceff 67%, #04a1ff 100%);
							background-clip: text;
							-webkit-background-clip: text;
							-webkit-text-fill-color: transparent;

							span {
								font-size: 14px;
							}
						}

						.chart {
							display: flex;
							gap: 4px;

							span {
								width: 10px;
								height: 25px;
								background-repeat: no-repeat;
							}
						}
					}

					.women {
						text-align: right;
						display: flex;
						flex-direction: column;
						justify-content: space-between;

						.count {
							height: 21px;
							font-size: 16px;
							font-family: PingFangSC, PingFang SC;
							font-weight: normal;
							line-height: 19px;
							background: linear-gradient(180deg, #ffffff 0%, #f076ff 66%, #ff04ff 100%);
							background-clip: text;
							-webkit-background-clip: text;
							-webkit-text-fill-color: transparent;

							span {
								font-size: 14px;
							}
						}

						.chart {
							display: flex;
							gap: 4px;

							span {
								width: 12px;
								height: 25px;
								background-repeat: no-repeat;
							}
						}
					}
				}
			}

			.chart_wrap {
				position: relative;
				width: 100%;
				height: 198px;
				top: 41px;

				.sign {
					position: absolute;
					left: 105px;
					top: 25px;

					.woman {
						position: absolute;
						width: 27px;
						height: 57px;
						left: 0;
						top: 0;
						background: url('~@/assets/hszl/woman.png') no-repeat;
					}

					.man {
						position: absolute;
						width: 23px;
						height: 57px;
						left: 95px;
						top: 47px;
						background: url('~@/assets/hszl/man.png') no-repeat;
					}

					.man_count {
						position: absolute;
						left: 10px;
						top: 76px;
						width: 109px;
						height: 31px;
						font-size: 20px;
						font-family: PingFangSC, PingFang SC;
						font-weight: normal;
						color: #ffffff;
						line-height: 24px;
					}

					.woman_count {
						position: absolute;
						left: 10px;
						top: 36px;
						width: 109px;
						height: 31px;
						font-size: 20px;
						font-family: PingFangSC, PingFang SC;
						font-weight: normal;
						color: #ffffff;
						line-height: 24px;
					}
				}
			}
		}
	}

	.box1 {
		margin-bottom: 16px;
		.cout {
			width: 100%;
			height: 100%;
		}
		.cont1 {
			width: 100%;
			height: 60px;
			display: flex;
			justify-content: space-around;

			.contList {
				.contNum {
					width: 124px;
					height: 24px;
					margin-top: 10px;

					span {
						display: inline-block;
						width: 100%;
						height: 24px;
						font-family: PingFangSC, PingFang SC;
						font-weight: bold;
						font-size: 20px;
						color: #ffffff;
						line-height: 24px;
						text-shadow: 0px 0px 4px rgba(0, 0, 0, 0);
						text-align: center;
						font-style: italic;
						background: linear-gradient(180deg, #ffffff 0%, #00fffa 67%, #04a1ff 100%);
						-webkit-background-clip: text;
						-webkit-text-fill-color: transparent;
					}
				}

				&:nth-of-type(2) {
					.contNum {
						width: 124px;
						height: 24px;

						span {
							display: inline-block;
							width: 100%;
							height: 24px;
							font-family: PingFangSC, PingFang SC;
							font-weight: bold;
							font-size: 20px;
							color: #ffffff;
							line-height: 24px;
							text-shadow: 0px 0px 4px rgba(0, 0, 0, 0);
							text-align: center;
							font-style: italic;
							background: linear-gradient(180deg, #ffffff 0%, #ffd500 67%, #ffb904 100%);
							-webkit-background-clip: text;
							-webkit-text-fill-color: transparent;
						}
					}
				}

				&:nth-of-type(3) {
					.contNum {
						width: 124px;
						height: 24px;

						span {
							display: inline-block;
							width: 100%;
							height: 24px;
							font-family: PingFangSC, PingFang SC;
							font-weight: bold;
							font-size: 20px;
							color: #ffffff;
							line-height: 24px;
							text-shadow: 0px 0px 4px rgba(0, 0, 0, 0);
							text-align: center;
							font-style: italic;
							background: linear-gradient(180deg, #ffffff 0%, #86ff00 67%, #1cff04 100%);
							-webkit-background-clip: text;
							-webkit-text-fill-color: transparent;
						}
					}
				}

				img {
					display: block;
					width: 124px;
					height: 4px;
					margin-top: 4px;
				}

				.contName {
					width: 124px;
					height: 20px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 14px;
					color: #ffffff;
					line-height: 20px;
					text-align: center;
					font-style: normal;
				}
			}
		}

		.cont2 {
			width: 100%;
			height: 170px;
		}
	}

	.box2 {
		margin-top: 22px;
		.contLeft {
			position: absolute;
			left: 20px;
			top: 10px;
			text-align: left;
			z-index: 99;
		}

		.contRight {
			position: absolute;
			right: 20px;
			top: 10px;
			text-align: right;
			z-index: 99;
		}

		.contList {
			margin-top: 18px;
			cursor: pointer;
			.contNum {
				font-family: PingFangSC, PingFang SC;
				font-weight: bold;
				font-size: 24px;
				color: #ffffff;
				line-height: 28px;
				letter-spacing: 2px;
				font-style: normal;

				span {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 14px;
					color: #cbe5ff;
					line-height: 20px;
					letter-spacing: 1px;
					text-align: right;
					font-style: normal;
					margin-left: 4px;
				}
			}

			.contName {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #cbe5ff;
				line-height: 20px;
				letter-spacing: 1px;
				font-style: normal;
			}

			&:nth-of-type(1) {
				margin-top: 7px;
			}
		}

		.contImg {
			width: 405px;
			height: 197px;
			position: absolute;
			top: 52px;
			left: 20px;
		}

		.contImg2 {
			width: 59px;
			height: 61px;
			position: absolute;
			top: 93px;
			left: 194px;
		}
	}

	.box3 {
		.cont {
			padding: 12px 22px 0;

			video {
				width: 100%;
				height: 100%;
				object-fit: cover;

				&:not(:root):fullscreen {
					object-fit: contain;
				}
			}
		}
	}

	.box4 {
		.cont {
			padding: 15px 0;
			display: flex;
			flex-wrap: wrap;
			justify-content: space-evenly;

			li {
				position: relative;
				width: 200px;
				height: 66px;
				background: url(~@/assets/csts/bg2.png) no-repeat;
				display: flex;
				flex-direction: column;
				justify-content: center;
				text-align: left;
				padding-left: 92px;

				.icon {
					position: absolute;
					width: 26px;
					height: 26px;
					top: 10px;
					left: 31px;
					animation: move infinite 3s ease-in-out;

					@keyframes move {
						0% {
							transform: translateY(0px) rotateY(0);
						}

						50% {
							transform: translateY(-10px) rotateY(180deg);
						}

						100% {
							transform: translateY(0px) rotateY(360deg);
						}
					}
				}

				&:nth-child(2) {
					span {
						animation-delay: 0.3s;
					}
				}

				&:nth-child(3) {
					span {
						animation-delay: 0.6s;
					}
				}

				&:nth-child(4) {
					span {
						animation-delay: 0.9s;
					}
				}

				&:nth-child(5) {
					span {
						animation-delay: 1.2s;
					}
				}

				&:nth-child(6) {
					span {
						animation-delay: 1.5s;
					}
				}

				.tit {
					font-size: 14px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #ffffff;
					line-height: 20px;
				}

				.num {
					font-size: 18px;
					font-family: PingFangSC, PingFang SC;
					font-weight: normal;
					color: #ffffff;
					line-height: 22px;
				}
			}
		}
	}

	.box5 {
		.cont {
			display: flex;
			flex-direction: column;

			.bar_chart {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-evenly;

				li {
					position: relative;
					display: flex;
					align-items: center;
					padding: 0 30px 0 23px;

					.icon {
						width: 20px;
						height: 22px;
						margin-right: 7px;
					}

					.icon2 {
						width: 10px;
						height: 12px;
						position: absolute;
						left: 28px;
						top: 5px;
						animation: move infinite 3s ease-in-out;

						@keyframes move {
							0% {
								transform: translateY(0px) rotateY(0);
							}

							50% {
								transform: translateY(0px) rotateY(180deg);
							}

							100% {
								transform: translateY(0px) rotateY(360deg);
							}
						}
					}

					.lab {
						font-size: 14px;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						color: #ffffff;
						margin-right: 8px;
					}

					.progress {
						display: block;
						flex: 1;
						height: 100%;
						background: rgba(0, 201, 255, 0.14);

						.cur {
							width: 100%;
							border: 1px solid;
							height: 100%;
							overflow: hidden;
							transition: width 0.5s;
						}
					}

					.num {
						font-size: 16px;
						font-family: PingFangSC, PingFang SC;
						font-weight: normal;
						color: #ffffff;
						margin-left: 9px;
					}

					.percent {
						font-size: 16px;
						font-family: PingFangSC, PingFang SC;
						font-weight: normal;
						color: #ffffff;
						margin-left: 14px;
					}
				}
			}

			.pie_chart {
				height: 123px;
				display: flex;
				justify-content: space-evenly;

				.warp {
					width: 107px;
					height: 107px;
					padding-top: 0;

					/deep/.ring {
						width: 100% !important;
						height: 100% !important;
					}

					/deep/.label {
						margin-top: -65px;

						.name {
							font-size: 13px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #ffffff;
						}
					}
				}
			}
		}
	}

	.box6 {
		margin-top: 12px;

		.cont {
			.select_month {
				position: absolute;
				right: 0;
				top: -20px;

				::v-deep button {
					width: 56px;
					height: 24px;
					right: -46px;
					top: -28px;
					border-radius: 12px;
					background: url(~@/assets/shzl/select_bg.png) no-repeat center / 100% 100%;
					border: none;
					padding: 0;
					// background: linear-gradient(180deg, #0e63ac 0%, #253c69 100%);
					// border-radius: 2px;
					// border: 2px solid;
					// border-image: linear-gradient(180deg, rgba(63, 148, 224, 0.2), rgba(218, 250, 255, 0.3))
					//   2 2;
				}
			}

			// padding: 14px 28px 0;
			// .swiper-slide {
			//   height: 100%;
			// }
			// .swiper-pagination {
			//   text-align: right;
			//   bottom: 3px;
			// }
			// /deep/.swiper-pagination-bullet-active {
			//   background-color: rgba(255, 255, 255, 0.8);
			// }
			// .content {
			//   width: 100%;
			//   height: 100%;
			//   position: relative;
			//   img {
			//     position: absolute;
			//     width: 100%;
			//     height: 100%;
			//     top: 0;
			//     left: 0;
			//   }
			//   .cont {
			//     width: 100%;
			//     height: 30px;
			//     padding: 5px 10px;
			//     position: absolute;
			//     bottom: 0;
			//     font-size: 14px;
			//     font-family: PingFangSC, PingFang SC;
			//     font-weight: 500;
			//     color: #ffffff;
			//     line-height: 22px;
			//     text-align: left;
			//     background-color: rgba(0, 0, 0, 0.35);
			//   }
			// }
		}
	}

	.box7 {
		.cont {
			display: grid;
			place-items: center;
			position: relative;

			.fy_out {
				position: absolute;
				top: 3%;
				left: 27%;
				z-index: 99;
				transform: translate(-50%, -50%);
				animation: rotateS infinite 12s linear;
			}

			.fy_in {
				position: absolute;
				top: 44%;
				left: 50%;
				transform: translate(-50%, -50%);
			}

			.wrap {
				position: relative;
				width: 393px;
				height: 205px;
				background: url(~@/assets/csts/bg3.png) no-repeat;

				li {
					position: absolute;
					text-align: left;

					.num {
						font-size: 20px;
						font-family: PingFangSC, PingFang SC;
						font-weight: normal;
						color: #ffffff;
						line-height: 24px;
					}

					.tit {
						font-size: 14px;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						color: #ffffff;
						line-height: 17px;
					}

					&:nth-child(1) {
						top: 28px;
						left: 24px;
					}

					&:nth-child(2) {
						top: 28px;
						left: 295px;
					}

					&:nth-child(3) {
						bottom: 32px;
						left: 24px;
					}

					&:nth-child(4) {
						bottom: 32px;
						left: 295px;
					}
				}
			}
		}
	}

	.box8 {
		margin-top: 16px;
		position: relative;
		.njfbDw {
			position: absolute;
			left: 50px;
			top: 20px;
			color: #ffffff;
			font-size: 14px;
			font-family: PingFangSC, PingFang SC;
			font-weight: 300;
		}
		.cout {
			width: 100%;
			height: 100%;
		}
	}

	.box9 {
		.cont {
			padding: 16px 23px 0;

			.title {
				width: 100%;
				height: 44px;
				line-height: 44px;
				display: flex;
				justify-content: space-between;
				padding: 0 24px;

				span {
					font-size: 14px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #ffffff;
				}
			}

			.detail {
				position: relative;
				width: 404px;
				height: 156px;
				background: url(~@/assets/csts/bg4.png) no-repeat center;
				display: flex;
				justify-content: space-between;

				.center_out {
					position: absolute;
					left: 29%;
					top: -2%;
					transform: translate(-50%, -50%);
					z-index: 99;
					animation: rotateS infinite 12s linear;
				}

				.center_in {
					position: absolute;
					left: 29%;
					top: 2px;
					transform: translate(-50%, -50%);
					z-index: 99;
					animation: rotateN infinite 12s linear;
				}

				.fs {
					display: flex;
					flex-direction: column;
					justify-content: space-evenly;
					gap: 9px;

					li {
						display: flex;
						justify-content: space-between;
						align-items: center;

						.icon {
							width: 13px;
							height: 16px;
						}

						.lab {
							font-size: 14px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #ffffff;
							line-height: 20px;
							padding-left: 6px;
							padding-right: 22px;
						}

						.num {
							font-size: 10px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #ffffff;

							span {
								font-size: 18px;
								font-family: PingFangSC, PingFang SC;
								font-weight: normal;
								color: #ffffff;
								line-height: 22px;
							}
						}
					}
				}

				.fq {
					display: flex;
					flex-direction: column;
					justify-content: space-evenly;
					gap: 9px;

					li {
						display: flex;
						justify-content: space-between;
						align-items: center;

						.icon {
							width: 14px;
							height: 14px;
						}

						.lab {
							font-size: 14px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #ffffff;
							line-height: 20px;
							padding-left: 22px;
							padding-right: 6px;
						}

						.num {
							font-size: 10px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #ffffff;

							span {
								font-size: 18px;
								font-family: PingFangSC, PingFang SC;
								font-weight: normal;
								color: #ffffff;
								line-height: 22px;
							}
						}
					}
				}

				.zdqy {
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);

					.num {
						font-size: 20px;
						font-family: PingFangSC, PingFang SC;
						font-weight: normal;
						color: #ffffff;
						line-height: 24px;
					}

					.lab {
						font-size: 18px;
						font-family: PingFangSC, PingFang SC;
						color: #ffffff;
						line-height: 21px;
						letter-spacing: 1px;
					}
				}
			}
		}
	}

	.box12 {
		position: relative;

		.gajq_lz {
			width: 100%;
			height: 100%;
			position: absolute;
			top: 0;

			img {
				width: 100%;
				height: 100%;
			}
		}
	}

	.left_bg {
		width: 460px;
		height: 1033px;
		background: url(~@/assets/leader/img/component/left_bg.png) no-repeat center / 100% 100%;
		position: absolute;
		left: 50px;
		top: 25px;
		z-index: 102;
	}

	.right_bg {
		width: 520px;
		height: 1033px;
		background: url(~@/assets/leader/img/component/right_bg.png) no-repeat center / 100% 100%;
		position: absolute;
		right: 50px;
		top: 25px;
		z-index: 102;
	}

	.left {
		// width: 450px;
		height: calc(100% - 104px);

		display: flex;
		justify-content: space-between;
		margin-top: 104px;
		margin-left: 19px;
		position: relative;
		left: 0;
		opacity: 1;
		z-index: 1003;
		-webkit-transition: all 0.5s ease-in;
		-moz-transition: all 0.5s ease-in;
		transition: all 0.5s ease-in;
	}

	.top-middle {
		position: relative;
		top: 110px;
		left: 0;
		width: 100%;
		height: 202px;
		// width: 100%;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
		-webkit-transition: all 0.5s ease-in;
		-moz-transition: all 0.5s ease-in;
		transition: all 0.5s ease-in;

		.top-header-descripte {
			width: 708px;
			position: absolute;
			top: 0;
			left: 0;
			-webkit-transition: all 0.5s ease-in;
			-moz-transition: all 0.5s ease-in;
			transition: all 0.5s ease-in;
			&.top-header-descripte-move {
				top: -182px;
			}
		}
	}

	.view-type-box {
		width: 146px;
		margin-top: 20px;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: center;
		margin-left: 24px;
		z-index: 1101;
		position: absolute;
		left: 0;
		top: 72px;

		-webkit-transition: all 0.5s ease-in;
		-moz-transition: all 0.5s ease-in;
		transition: all 0.5s ease-in;

		.mapItem {
			width: 100%;
			height: 34px;
			margin-bottom: 4px;
			cursor: pointer;
			display: flex;
			justify-content: flex-start;
			align-items: center;

			.icon-box {
				width: 34px;
				height: 34px;
				display: flex;
				justify-content: center;
				align-items: center;

				background-image: url(~@/assets/img/jxhfz/map-btn-icon-bg.png);

				/* 背景不重复 */
				background-repeat: no-repeat;
				/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
				background-size: cover;
				/* 背景居中显示（可选） */
				background-position: center;

				img {
					// width: 18px;
					// height: 14px;
				}
			}

			span {
				width: calc(100% - 34px - 16px);
				height: 20px;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				padding-left: 8px;
				background: linear-gradient(91deg, #1485ff 0%, rgba(11, 100, 195, 0) 100%);
				border-radius: 10px 10px 10px 10px;
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 14px;
				color: #ffffff;
				line-height: 20px;
				text-align: left;
				font-style: normal;
			}

			&:last-child {
				margin-bottom: 0;
			}

			&.active-item {
				span {
					background: linear-gradient(90deg, #fc973b 0%, rgba(132, 67, 31, 0) 100%);
				}
				.icon-box {
					background-image: url(~@/assets/img/jxhfz/map-btn-icon-active-bg.png);
				}
			}
		}

		&.view-type-box-move {
			left: -480px;
		}
	}

	.mkdx-top-middle {
		top: -200px;
	}

	.mkdxLeft {
		left: -450px;
		opacity: 0;
	}

	.yjsjDetailLeft {
		left: -450px;
		opacity: 0;
	}

	.right {
		width: 450px;
		height: calc(100vh - 165px - 216px);
		display: flex;
		justify-content: space-between;
		margin-top: 104px;
		margin-right: 32px;
		position: relative;
		right: 0;
		z-index: 1003;
		-webkit-transition: all 0.5s ease-in;
		-moz-transition: all 0.5s ease-in;
		transition: all 0.5s ease-in;

		.njlx_box {
			&::-webkit-scrollbar {
				width: 6px;
				background: transparent;
			}

			&::-webkit-scrollbar-thumb {
				/*滚动条里面小方块*/
				border-radius: 2px;
				box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
				background: rgb(45, 124, 228);
				height: 100px;
				width: 20px;
			}
		}

		.njlx_box {
			display: flex;
			flex-wrap: wrap;
			margin: 10px auto 0;
			text-align: left;
			width: 100%;
			height: calc(100% - 30px);
			overflow-y: scroll;
			-ms-overflow-style: none;
			/* IE 10+ */
			scrollbar-width: none;
			/* Firefox */

			.njlx_item {
				display: inline-block;
				margin: 0 3% 7px 3%;
				width: 27%;
				// height: 93px;
				// height:45%;
				// height:30%;
				// background-image: url(~@/assets/bjnj/njlx_bgImg.png);
				// background-size: 100% 100%;
				// background-repeat: no-repeat;
				// margin-bottom: 7px;
				// padding-top: 8px;
				box-sizing: border-box;
				text-align: center;

				.njlx_title {
					margin: 0 auto;
					max-width: fit-content;
					min-width: 100px;
					background-image: url(~@/assets/bjnj/njlx_titleImg.png);
					background-size: 100% 18px;
					background-repeat: no-repeat;
					// height: 18px;
					font-family: PingFangSC, PingFang SC;
					font-size: 16px;
					color: #edfbff;
					line-height: 18px;
					letter-spacing: 1px;
					text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.15);
					font-style: normal;
					text-align: center;
				}

				.njlx_number {
					margin: 8px auto;
					width: fit-content;
					height: 24px;
					font-family: PangMenZhengDao;
					font-size: 24px;
					color: #ffffff;
					line-height: 24px;
					letter-spacing: 1px;
					text-shadow: 0px 0px 4px rgba(73, 140, 255, 0.85);
					text-align: left;
					font-style: normal;
					background: linear-gradient(90deg, #00faff 0%, #00b1ff 100%);
					-webkit-background-clip: text;
					/*将设置的背景颜色限制在文字中*/
					-webkit-text-fill-color: transparent;
					/*给文字设置成透明*/
				}
			}
			.njlx_item2 {
				display: inline-block;
				margin: 0 10px;
				width: 28%;
				// height: 93px;
				height: 30%;
				// background-image: url(~@/assets/bjnj/njlx_bgImg2.png);
				background-size: 100% 100%;
				background-repeat: no-repeat;
				margin-bottom: 7px;
				// padding-top: 8px;
				box-sizing: border-box;
				text-align: center;

				.njlx_title2 {
					margin: 0 auto;
					max-width: fit-content;
					min-width: 100px;
					background-image: url(~@/assets/bjnj/njlx_titleImg2.png);
					background-size: 100% 100%;
					background-repeat: no-repeat;
					height: 18px;
					font-family: PingFangSC, PingFang SC;
					font-size: 16px;
					color: #edfbff;
					line-height: 18px;
					letter-spacing: 1px;
					text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.15);
					font-style: normal;
					text-align: center;
				}

				.njlx_number {
					margin: 8px auto;
					width: fit-content;
					height: 24px;
					font-family: PangMenZhengDao;
					font-size: 24px;
					color: #ffffff;
					line-height: 24px;
					letter-spacing: 1px;
					text-shadow: 0px 0px 4px rgba(255, 164, 73, 0.85);
					text-align: left;
					font-style: normal;
					background: linear-gradient(90deg, #ff9800 0%, #ffcb00 100%);
					-webkit-background-clip: text;
					/*将设置的背景颜色限制在文字中*/
					-webkit-text-fill-color: transparent;
					/*给文字设置成透明*/
				}
			}
		}
	}

	.mkdxRight {
		right: -450px;
		opacity: 0;
	}
	.zwsjImg {
		height: 80%;
		margin: 0 auto;
		margin-top: 36px;
	}
}

.jgzzBox {
	width: 156px;
	height: 163px; // 204
	display: flex;
	flex-direction: column;
	position: absolute;
	left: 980px;
	bottom: 93px;
	z-index: 999;
	background: url(~@/assets/mskx/bg_resource.png) no-repeat center / 100% 100%;

	.jgzzItem {
		width: 153px;
		height: 36px;
		margin: 1px 2px 1px 2px;
		cursor: pointer;

		// background: url(~@/assets/csts/btn11.png) no-repeat;
		img {
			width: 22px;
			height: 22px;
			float: left;
			margin: 7px 3px 0 10px;
		}

		span {
			float: left;
			font-size: 17px;
			font-family: PingFangSC, PingFang SC;
			color: #e9fffe;
			line-height: 36px;
			background: linear-gradient(180deg, #ffffff 0%, #2f8af4 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}
	}

	.jgzzActive {
		width: 153px;
		height: 36px;
		margin: 1px 2px 1px 2px;
		// background: url(~@/assets/csts/btn12.png) no-repeat;
		background: rgba(108, 163, 255, 0.3);
	}
}

.zdcsBox {
	width: 109px;
	height: 248px;
	display: flex;
	flex-direction: column;
	position: absolute;
	left: 1197px;
	bottom: 93px;
	z-index: 999;
	background: url(~@/assets/csts/btnBg12.png) no-repeat center / 100% 100%;

	.jgzzItem {
		width: 103px;
		height: 35px;
		margin: 2px 3px 2px 3px;
		background: url(~@/assets/csts/btn11.png) no-repeat;

		img {
			width: 22px;
			height: 22px;
			float: left;
			margin: 7px 0 0 10px;
		}

		span {
			float: left;
			font-size: 17px;
			font-family: PingFangSC, PingFang SC;
			color: #e9fffe;
			line-height: 35px;
			background: linear-gradient(180deg, #ffffff 0%, #2f8af4 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}
	}

	.jgzzActive {
		width: 156px;
		height: 35px;
		margin: 1px 2px 1px 2px;
		background: url(~@/assets/csts/btn12.png) no-repeat;
	}
}
.tjxxBox {
	width: 800px;
	height: 74px;
	position: absolute;
	top: 124px;
	left: 50%;
	transform: translateX(-50%);
	z-index: 1003;
	.tjxxItem {
		width: 180px;
		height: 74px;
		margin: 0 10px;
		float: left;
		.tjxxName {
			width: 180px;
			height: 36px;
			background: url(~@/assets/bjnj/tjxxBg.png) no-repeat center / 100% 100%;
			font-family: AlibabaPuHuiTi;
			font-size: 18px;
			color: #dff3ff;
			line-height: 36px;
			text-align: center;
			font-style: normal;
		}
		.tjxxNum {
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 26px;
			color: #ffffff;
			line-height: 38px;
			text-shadow: 0px 0px 1px rgba(0, 18, 57, 0.84);
			text-align: center;
			font-style: normal;
			span {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #ffffff;
				line-height: 20px;
				text-shadow: 0px 0px 1px rgba(0, 18, 57, 0.84);
				text-align: left;
				font-style: normal;
			}
		}
	}
}
.mapBtn {
	width: 106px;
	position: absolute;
	z-index: 1003;

	-webkit-transition: all 0.5s ease-in;
	-moz-transition: all 0.5s ease-in;
	transition: all 0.5s ease-in;

	.mapItem2 {
		width: 160px;
		height: 40px;
		margin: 8px 0;
		background: url(~@/assets/bjnj/btn2.png) no-repeat center / 100% 100%;
		img {
			vertical-align: middle;
			margin-top: -10px;
		}
		span {
			margin-left: 4px;
			font-family: YouSheBiaoTiHei;
			font-size: 20px;
			color: #ffffff;
			line-height: 40px;
			text-align: left;
			font-style: normal;
			background: linear-gradient(180deg, #ffffff 0%, #ffca00 100%);
			background-clip: text;
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			cursor: pointer;
		}
	}
}

.map-operate-box {
	height: 32px;
	position: absolute;
	z-index: 1002;
	display: flex;
	justify-content: center;
	align-items: center;

	// background: rgba(0, 163, 255, 0.05);
	background: linear-gradient(90deg, rgba(42, 130, 255, 0.5) 0%, rgba(0, 98, 234, 0.9) 100%);

	border-radius: 4px;

	border: 1px solid rgba(0, 163, 255, 0.5);

	-webkit-transition: all 0.5s ease-in;
	-moz-transition: all 0.5s ease-in;
	transition: all 0.5s ease-in;

	.operate-item-box {
		// width: 107px;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		cursor: pointer;
		padding: 0 13px;
		padding-right: 0;

		img {
			width: 16px;
			height: 16px;
			margin-right: 6px;
		}

		.operate-icon {
			font-size: 15px;
			color: #30a0a9;

			/* 背景不重复 */
			background-repeat: no-repeat;
			/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
			background-size: cover;
			/* 背景居中显示（可选） */
			background-position: center;
			margin-right: 6px;
			padding-left: 13px;
		}

		.icon-rotate {
			// transform: rotate(90deg);
			// transform-origin: center; /* 旋转的中心点为元素的中心 */
		}

		.operate-title {
			padding-right: 13px;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 14px;
			color: #b0e0ff;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}

		&:hover {
			background: rgba(255, 128, 12, 0.8);

			.operate-title {
				color: #fff;
			}
		}

		.devider {
			width: 1px;
			height: 12px;
			background: #848991;
		}

		&:last-child {
			.devider {
				display: none;
			}
		}
	}
}

.zyfbBox {
	width: 240px;
	height: 120px;
	background: url(~@/assets/bjnj/mapBg.png) no-repeat center / 100% 100%;
	position: absolute;
	top: 220px;
	left: 665px;
	z-index: 1003;
	padding: 3px 0 0 24px;
	.zyfbItem {
		width: 210px;
		height: 36px;
		cursor: pointer;
		img {
			width: 22px;
			height: 22px;
			float: left;
			margin: 6px 8px 0 7px;
		}

		span {
			display: inline-block;
			width: 100%;
			text-align: center;
			float: left;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 16px;
			color: #ffffff;
			line-height: 36px;
			font-style: normal;
		}
	}
	.zyfbItem:hover {
		background: rgba(108, 163, 255, 0.3);
	}
	.jgzzActive {
		background: rgba(108, 163, 255, 0.3);
	}
}
.njfbBox {
	width: 190px;
	// height: 336px;
	// background: url(~@/assets/bjnj/mapBg2.png) no-repeat center / 100% 100%;
	position: absolute;
	// top: 302px;
	// left: 665px;
	z-index: 1003;
	padding: 3px;
	background: #04244e;

	border: 1px solid rgba(0, 163, 255, 0.5);
	.zyfbItem {
		width: 100%;
		height: 36px;
		color: #4e5969;
		cursor: pointer;
		img {
			width: 22px;
			height: 22px;
			float: left;
			margin: 6px 8px 0 7px;
		}

		span {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 16px;
			// color: #ffffff;
			color: #b0e0ff;

			line-height: 36px;
			text-align: center;
			font-style: normal;
		}
	}
	.zyfbItem:hover {
		// background: rgba(108, 163, 255, 0.3);
		background: #178dfa;

		span {
			color: #fff;
		}
	}
	.jgzzActive {
		// background: rgba(108, 163, 255, 0.3);
		background: #178dfa;

		span {
			color: #fff;
		}
	}
}
.njfbBox2 {
	width: 168px;
	height: 336px;
	// background: url(~@/assets/bjnj/mapBg2.png) no-repeat center / 100% 100%;
	position: absolute;
	// top: 346px;
	// left: 665px;
	z-index: 1003;
	padding: 3px;
	background: #04244e;

	border: 1px solid rgba(0, 163, 255, 0.5);
	.zyfbItem {
		width: 100%;
		height: 36px;
		cursor: pointer;
		img {
			width: 22px;
			height: 22px;
			float: left;
			margin: 6px 8px 0 7px;
		}

		span {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 16px;
			// color: #ffffff;
			color: #b0e0ff;

			line-height: 36px;
			text-align: center;
			font-style: normal;
		}
	}
	.zyfbItem:hover {
		// background: rgba(108, 163, 255, 0.3);
		background: #178dfa;

		span {
			color: #fff;
		}
	}
	.jgzzActive {
		// background: rgba(108, 163, 255, 0.3);
		background: #178dfa;

		span {
			color: #fff;
		}
	}
}

.left,
.right {
	::v-deep {
		.el-carousel {
			width: 450px;
			height: 920px;

			.el-carousel__indicators {
				.el-carousel__indicator {
					&.is-active {
						.el-carousel__button {
							height: 4px;
							background-color: #49e6ff;
						}
					}

					.el-carousel__button {
						height: 4px;
						background-color: rgba(255, 255, 255, 0.4);
						border-radius: 2px;
					}
				}
			}
		}
	}
}

.right {
	::v-deep {
		.el-carousel {
			margin-left: auto;
		}
	}
}

::v-deep .el-carousel--horizontal {
	width: 450px;
	overflow-x: hidden;
}

.map_box {
	position: absolute;
	width: 1920px;
	height: 1080px;
	top: 0px;
	left: 0;
	z-index: 999;
	// border: 1px solid red;
}

.mkss {
	width: 44px;
	height: 44px;
	position: absolute;
	bottom: 735px;
	right: 540px;
	z-index: 999;
	background: url(~@/assets/map/ss2.png) no-repeat center / 100% 100%;

	.mkss2 {
		font-size: 16px;
		color: #fff;
		opacity: 0;
	}
}

.mkss1 {
	width: 44px;
	height: 44px;
	position: absolute;
	bottom: 735px;
	right: 540px;
	z-index: 999;
	background: url(~@/assets/map/ss1.png) no-repeat center / 100% 100%;

	.mkss2 {
		font-size: 16px;
		color: #fff;
		opacity: 0;
	}
}
/* 样式用于控制滚动容器的高度、宽度、边框和滚动效果等 */
.introduce {
	// width: 500px;
	// height: 200px;
	overflow: hidden;
	position: relative;
}

/* 样式用于控制滚动文本的位置和颜色 */
.introduce p {
	position: absolute;
	top: 0;
	left: 0;
	margin: 0;
	padding: 10px;
	font-size: 16px;
}

.njtlBox {
	width: 44px;
	height: 44px;
	position: absolute;
	bottom: 685px;
	right: 539px;
	z-index: 1099;
	img {
		width: 44px;
		height: 44px;
	}
}
.ssBox {
	width: 44px;
	height: 44px;
	position: absolute;
	bottom: 585px;
	right: 539px;
	z-index: 1099;
	img {
		width: 44px;
		height: 44px;
	}
}
.sxBox {
	width: 44px;
	height: 44px;
	position: absolute;
	bottom: 635px;
	right: 539px;
	z-index: 1099;
	img {
		width: 44px;
		height: 44px;
	}
}
.ssPop {
	width: 308px;
	position: absolute;
	// top: 401px;
	// right: 589px;
	z-index: 1099;
	.ssPop1 {
		width: 308px;
		height: 43px;
		input {
			float: left;
			width: 256px;
			height: 43px;
			// background: linear-gradient(180deg, rgba(17, 77, 168, 0) 0%, rgba(36, 132, 212, 0.5) 100%), rgba(2, 52, 114, 0.8);
			background: #fff;

			box-shadow: inset 0px 0px 2px 0px #57afff;
			border-radius: 2px;
			// border: 1px solid #0a5eaa;
			border: 1px solid #a6d2d3;

			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 16px;
			color: #5e5d5d;
			line-height: 43px;
			// text-shadow: 0px 2px 3px rgba(0, 48, 72, 0.5);
			text-shadow: 0px 2px 3px #a6d2d3;

			text-align: left;
			font-style: normal;
			padding-left: 20px;
		}
		.ssImgBox {
			float: left;
			width: 52px;
			height: 43px;
			line-height: 43px;
			// background: linear-gradient(180deg, rgba(17, 77, 168, 0) 0%, rgba(36, 132, 212, 0.5) 100%), #1373d9;
			background: #fff;
			border-radius: 2px;
			// border: 1px solid #0a5eaa;
			border: 1px solid #a6d2d3;

			img {
				vertical-align: middle;
				margin-top: -3px;
			}
		}
	}
	.ssPop2 {
		width: 257px;
		height: 270px;
		// background: linear-gradient(180deg, rgba(17, 77, 168, 0) 0%, rgba(36, 132, 212, 0.5) 100%), rgba(2, 52, 114, 0.8);
		background: #fff;

		box-shadow: inset 0px 0px 2px 0px #57afff;
		border-radius: 2px;
		// border: 1px solid #0a5eaa;
		border: 1px solid #a6d2d3;

		overflow: auto;
		/* 隐藏全局滚动条 */
		&::-webkit-scrollbar {
			display: none;
		}

		.ssList {
			width: 257px;
			height: 45px;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 16px;
			// color: #c2ddfc;
			color: #5e5d5d;
			line-height: 40px;
			// text-shadow: 0px 2px 3px rgba(0, 48, 72, 0.5);
			text-shadow: 0px 2px 3px #a6d2d3;

			text-align: left;
			font-style: normal;
			padding-left: 20px;
		}
	}
}
.njtlList {
	// width: 275px;
	// height: 196px;
	position: absolute;
	bottom: 305px;
	right: 505px;
	z-index: 1099;
	// background: url(~@/assets/bjnj/njtlBg.png) no-repeat center / 100% 100%;
	// padding-top: 33px;
	padding: 10px;

	// background: #d3e7e9;
	background: #bfe2e4;

	border: 2px solid #dcedef;
	border-radius: 20px;

	-webkit-transition: all 0.5s ease-in;
	-moz-transition: all 0.5s ease-in;
	transition: all 0.5s ease-in;

	.njtlItem {
		width: 100%;
		// height: 38px;
		margin-bottom: 10px;
		img {
			width: 31px;
			height: 38px;
			float: left;
		}
		.njtlName {
			float: left;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			// color: #ffffff;
			font-size: 16px;
			color: #30a0a9;
			line-height: 38px;
			text-align: left;
			font-style: normal;
			margin-left: 5px;
		}

		&:last-child {
			margin-bottom: 0;
		}
	}
}
.wgsj_fx {
	width: 100%;

	// height: 100%;
	.select_m {
		position: absolute;
		right: 0;
		top: -50px;
		line-height: 30px;
		font-size: 16px;
		font-family: PingFangSC, PingFang SC;
		color: #caecff;
		text-shadow: 0px 0px 1px #00132e;

		::v-deep .el-dropdown button {
			font-family: PingFangSC, PingFang SC;
			font-size: 16px;
			color: #caecff;
			line-height: 21px;
			text-align: center;
			font-style: normal;

			// width: 100px;
			height: 30px;
			// background: url(~@/assets/mskx/icon_drop.png) no-repeat center / 100% 100%;
			background: transparent;
			border: none;
			padding: 0 0 0 5px;
		}

		::v-deep .el-icon-arrow-down:before {
			// content: '\e790' !important;
			color: #caecff;
		}

		::v-deep .el-popper[x-placement^='bottom'] .popper__arrow:after {
			display: none;
		}

		::v-deep .el-popper .popper__arrow:after {
			border-bottom-color: #185493 !important;
		}
	}
}
::v-deep .el-dropdown {
	color: #caecff;
	font-family: YouSheBiaoTiHei;
	font-size: 16px;
	line-height: 21px;
	text-align: right;
	font-style: normal;
	line-height: 40px;
	right: 0;
	position: absolute;
}
::v-deep .el-dropdown-menu {
	// background: #00173b;
	position: absolute;
	left: 0;
	z-index: 10;
	padding: 10px 0;
	margin: 5px 0;
	// border: 1px solid #2b7bbb;
	border-radius: 4px;
	background-color: #c6e2e7 !important;
	border: 1px solid #d2f2ea !important;
}

.ai_waring {
	width: 1525px;
	height: 850px;
	background: rgba(9, 19, 34, 0.95);
	box-shadow: 0px 0px 22px 0px rgba(11, 54, 138, 0.35), 0px 2px 8px 0px rgba(0, 0, 0, 0.7),
		inset 0px 0px 8px 0px rgba(17, 146, 214, 0.35);
	border-radius: 11px;
	border: 1px solid #015c8c;
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	z-index: 1200;
	.title {
		margin: 0 auto 36px;
		width: 100%;
		height: 72px;
		line-height: 72px;
		background: url(~@/assets/map/dialog/title7.png) no-repeat center / 100% 100%;
		display: grid;
		place-items: center;
		span {
			display: inline-block;
			font-size: 36px;
			font-family: PingFangSC, PingFang SC;
			background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
			background-clip: text;
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}
	}

	.close_btn {
		position: absolute;
		width: 23px;
		height: 23px;
		top: 24px;
		right: 27px;
		background: url(~@/assets/map/dialog/btn3.png) no-repeat center / 100% 100%;
		cursor: pointer;
	}
	.content {
		padding: 0 30px;
		/deep/ #ezuikit-player-wrap {
			width: 100% !important;
			min-height: 600px;
		}
		.table_box {
			margin-top: 12px;
			overflow: hidden;
		}

		.tableBox {
			height: 41px;
			margin-bottom: 21px;
			.tableItem {
				float: left;
				width: 152px;
				height: 41px;
				background: url(~@/assets/map/dialog/btn5.png) no-repeat center / 100% 100%;
				margin-right: 10px;
				font-family: PingFangSC, PingFang SC;
				font-size: 22px;
				color: #ffffff;
				line-height: 41px;
				text-align: center;
				font-style: normal;
				text-transform: none;
				span {
					background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
					background-clip: text;
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
				}
			}
		}
	}
}

.draw-polygon-tooltip {
	width: 100%;
	position: absolute;
	top: 100px;
	left: 0;
	padding: 15px;
	z-index: 1100;
	display: flex;
	justify-content: center;
	align-items: center;

	-webkit-transition: all 0.5s ease-in;
	-moz-transition: all 0.5s ease-in;
	transition: all 0.5s ease-in;

	.tooltip-text {
		color: #e6a23c;
		font-size: 16px;
		padding: 10px;
		border: 1px solid #fdf6ec;
		background-color: #fdf6ec;
		border-radius: 5px;

		.waring-box {
			font-size: 16px;
			color: #e6a23c;
			margin-right: 5px;
		}
	}
}
</style>
