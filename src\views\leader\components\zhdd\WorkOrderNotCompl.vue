<!--
 * @Author: fanjialong <EMAIL>
 * @Date: 2023-04-15 10:29:09
 * @LastEditors: fanjialong 
 * @LastEditTime: 2023-09-12 21:18:14
 * @FilePath: \hs_dp\src\views\leader\components\zhdd\WorkOrderNotCompl.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="wrap">
    <div class="item item1">
      <disc-circle :data="chartData1"></disc-circle>
    </div>
    <div class="item item2">
      <!-- <pie-chart3D2
        :data="chartData2"
        :options="options2"
        :init-option="initOptions2"
      ></pie-chart3D2> -->
      <!-- <RoseChart type="2" :init-option="initOptions2" /> -->
      <!-- <pieChart /> -->
       <pieChartOrder  :unit="''" />
    </div>
  </div>
</template>

<script>
// import pieChart from '@/components/zhny/pieChart.vue'
import pieChartOrder from '@/components/tsgz/pieChart.vue'
export default {
  name: 'WorkOrderNotCompl',
  components:{
    // pieChart,
    pieChartOrder
  },
  props: {
    chartData1: {
      type: Object,
      default: () => {
        return {
          value: 23,
        }
      },
    },
    chartData2: {
      type: Array,
      default: () => [
        ['product', '占比'],
        ['紧急', 71],
        ['一般', 29],
      ],
    },
  },
  data() {
    return {
      options2: {
        color: ['#00A0FF', '#FF9201', '#4CECAA', '#00F7FF', '#FFD400', '#DCFF5B'],
        legend: {
          left: -20,
          itemWidth: 14,
          itemHeight: 14,
          itemRadius: 2,
          fontSize: '20px',
          // itemWidth: 8,
        },
        tooltip: {
          backgroundColor: 'rgba(0, 33, 59, 0.8)',
          // borderColor: 'rgba(24, 174, 236, 1)',
          padding: 10,
          style: {
            color: '#fff',
            fontSize: 14,
          },
          useHTML: true, // 允许使用HTML
          formatter: function () {
            // console.log('this', this)
            return `${this.key} </br>  <span style="color: ${this.point.color}">&#9679;</span>${this.y}`
          },
        },
      },
    }
  },
  computed: {
    initOptions2() {
      return {
        series: [
          {radius: ['20%'],}
        ],
        // tooltip: {
        //   backgroundColor: 'rgba(0, 33, 59, 0.8)',
        //   // borderColor: 'rgba(24, 174, 236, 1)',
        //   padding: 10,
        //   style: {
        //     color: '#fff',
        //     fontSize: 14,
        //   },
        //   useHTML: true, // 允许使用HTML
        //   formatter: function () {
        //     console.log('this', this)
        //     return `${this.key} </br>  <span style="color: ${this.point.color}">&#9679;</span> ${this.y}`
        //   },
        // },
      }
    },
  },
}
</script>

<style lang="less" scoped>
.wrap {
  width: 100%;
  height: 100%;
  display: flex;
  .item {
    position: relative;
    width: 50%;
    height: 100%;
  }
  .item2 {
    // background: url('~@/assets/zhdd/bg1.png') no-repeat center / 85% 60%;
  }
  :deep(.pieWorkOrder) {
    width: 100%;
    background-image: none;
    .charts {
      width: 200px;
    }
  }
}
</style>
