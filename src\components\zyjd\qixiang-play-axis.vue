<template>
	<div class="qixiang-play-axis-box">
		<time-range-player @time-update="handleTimeUpdate" @end="handlePlaybackEnd" />
	</div>
</template>

<script>
import TimeRangePlayer from './time-range-player.vue'

export default {
	components: {
		TimeRangePlayer,
	},
	methods: {
		handleTimeUpdate(currentUnit, selectedRange) {
			// 根据selectedRange和currentUnit更新您的数据或视图
			// 24小时模式下currentUnit是0-23表示小时
			// 天模式下currentUnit是0-2或0-6表示天数
			this.$emit('timeUpdate', currentUnit, selectedRange)
		},
		handlePlaybackEnd() {
			console.log('播放结束')
			// alert('播放已完成')
		},
	},
}
</script>
<style lang="less" scoped>
.qixiang-play-axis-box {
	width: 100%;
}
</style>
