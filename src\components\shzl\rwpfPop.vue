<!--
 * @Author: srd18862913192 <EMAIL>
 * @Date: 2023-01-10 12:08:02
 * @LastEditors: srd18862913192 <EMAIL>
 * @LastEditTime: 2023-01-12 11:37:48
 * @FilePath: \ywtgdp\src\components\shzl\rwpfPop.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="ai_waring">
    <div class="title">
      <div><span>任务派发</span></div>
      <img @click="closeEmitai" class="close_btn" src="@/assets/shzl/map/close_btn.png" alt="" />
    </div>
    <div class="content">
      <div>
        <div class="one_line">
          <div class="jjcd">
            <span>紧急程度：</span>
            <div class="jjcd_btn">
              <div class="active">一般</div>
              <div>紧急</div>
            </div>
          </div>
          <div class="report_time">
            <span>事发时间：</span>
            <Date-picker
              type="date"
              placement="bottom-end"
              placeholder="选择日期"
              style="width: 200px"
            ></Date-picker>
          </div>
        </div>
        <div class="two_line">
          <span>外部来源:</span>
          <Cascader disabled="" :data="lyData" v-model="lyValue" style="width: 552px; height: 40px"></Cascader>
        </div>
        <div class="three_line">
          <span>事件地址:</span>
          <Input v-model="value1"></Input>
        </div>
        <div class="sjnr">
          <span>事件内容:</span>
          <Input v-model="value2" type="textarea" :rows="4" placeholder="请输入..."></Input>
        </div>
        <div class="three_line" style="margin-top: 90px">
          <span>事件类型:</span>
          <Select v-model="value3" style="width: 600px">
            <Option v-for="item in typeOptions" :value="item.value" :key="item">{{
              item.label
            }}</Option>
          </Select>
        </div>
        <div class="three_line">
          <span style="margin-right: 34px">下一步:</span>
          <Select v-model="value4" style="width: 600px">
            <Option v-for="item in nextOptions" :value="item.value" :key="item">{{
              item.label
            }}</Option>
          </Select>
        </div>
        <div class="upload">
          <span>上传附件:</span>
          <div>选择文件</div>
          <!-- <img src="@/assets/shzl/map/upload.png" alt="" /> -->
        </div>
        <div class="submit" @click="pfMethod">提交</div>
      </div>
    </div>
  </div>
</template>

<script>
import SwiperTableAi from '@/components/shzl/SwiperTableAi.vue'

export default {
  name: 'sjtjPop',
  components: { SwiperTableAi },
  data() {
    return {
      btnsArr: [
        {
          name: '未处理',
          normalBg: require('@/assets/shzl/map/left_n.png'),
          activeBg: require('@/assets/shzl/map/left_a.png'),
        },
        {
          name: '已处理',
          normalBg: require('@/assets/shzl/map/right_n.png'),
          activeBg: require('@/assets/shzl/map/right_a.png'),
        },
      ],
      activaIdx: 0,
      sjtjData: [
        [
          '1',
          '人群聚集',
          'JKSB-8394845-01',
          '鼓楼区汉中路与牌楼巷交叉口',
          '鼓楼区网格一',
          '2022-08-08 07:57:49',
        ],
        [
          '2',
          '人群聚集',
          'JKSB-8394845-01',
          '鼓楼区汉中路与牌楼巷交叉口',
          '鼓楼区网格一',
          '2022-08-08 07:57:49',
        ],
        [
          '3',
          '人群聚集',
          'JKSB-8394845-01',
          '鼓楼区汉中路与牌楼巷交叉口',
          '鼓楼区网格一',
          '2022-08-08 07:57:49',
        ],
        [
          '4',
          '人群聚集',
          'JKSB-8394845-01',
          '鼓楼区汉中路与牌楼巷交叉口',
          '鼓楼区网格一',
          '2022-08-08 07:57:49',
        ],
        [
          '5',
          '人群聚集',
          'JKSB-8394845-01',
          '鼓楼区汉中路与牌楼巷交叉口',
          '鼓楼区网格一',
          '2022-08-08 07:57:49',
        ],
        [
          '6',
          '人群聚集',
          'JKSB-8394845-01',
          '鼓楼区汉中路与牌楼巷交叉口',
          '鼓楼区网格一',
          '2022-08-08 07:57:49',
        ],
        [
          '7',
          '人群聚集',
          'JKSB-8394845-01',
          '鼓楼区汉中路与牌楼巷交叉口',
          '鼓楼区网格一',
          '2022-08-08 07:57:49',
        ],
        [
          '8',
          '人群聚集',
          'JKSB-8394845-01',
          '鼓楼区汉中路与牌楼巷交叉口',
          '鼓楼区网格一',
          '2022-08-08 07:57:49',
        ],
        [
          '9',
          '人群聚集',
          'JKSB-8394845-01',
          '鼓楼区汉中路与牌楼巷交叉口',
          '鼓楼区网格一',
          '2022-08-08 07:57:49',
        ],
        [
          '10',
          '人群聚集',
          'JKSB-8394845-01',
          '鼓楼区汉中路与牌楼巷交叉口',
          '鼓楼区网格一',
          '2022-08-08 07:57:49',
        ],
        [
          '11',
          '人群聚集',
          'JKSB-8394845-01',
          '鼓楼区汉中路与牌楼巷交叉口',
          '鼓楼区网格一',
          '2022-08-08 07:57:49',
        ],
        [
          '12',
          '人群聚集',
          'JKSB-8394845-01',
          '鼓楼区汉中路与牌楼巷交叉口',
          '鼓楼区网格一',
          '2022-08-08 07:57:49',
        ],
        [
          '13',
          '人群聚集',
          'JKSB-8394845-01',
          '鼓楼区汉中路与牌楼巷交叉口',
          '鼓楼区网格一',
          '2022-08-08 07:57:49',
        ],
        [
          '14',
          '人群聚集',
          'JKSB-8394845-01',
          '鼓楼区汉中路与牌楼巷交叉口',
          '鼓楼区网格一',
          '2022-08-08 07:57:49',
        ],
        [
          '15',
          '人群聚集',
          'JKSB-8394845-01',
          '鼓楼区汉中路与牌楼巷交叉口',
          '鼓楼区网格一',
          '2022-08-08 07:57:49',
        ],
        [
          '16',
          '人群聚集',
          'JKSB-8394845-01',
          '鼓楼区汉中路与牌楼巷交叉口',
          '鼓楼区网格一',
          '2022-08-08 07:57:49',
        ],
        [
          '17',
          '人群聚集',
          'JKSB-8394845-01',
          '鼓楼区汉中路与牌楼巷交叉口',
          '鼓楼区网格一',
          '2022-08-08 07:57:49',
        ],
        [
          '18',
          '人群聚集',
          'JKSB-8394845-01',
          '鼓楼区汉中路与牌楼巷交叉口',
          '鼓楼区网格一',
          '2022-08-08 07:57:49',
        ],
        [
          '19',
          '人群聚集',
          'JKSB-8394845-01',
          '鼓楼区汉中路与牌楼巷交叉口',
          '鼓楼区网格一',
          '2022-08-08 07:57:49',
        ],
        [
          '20',
          '人群聚集',
          'JKSB-8394845-01',
          '鼓楼区汉中路与牌楼巷交叉口',
          '鼓楼区网格一',
          '2022-08-08 07:57:49',
        ],
      ],
      proityOptions: [
        {
          value: '0',
          label: '一般',
        },
        {
          value: '1',
          label: '紧急',
        },
        //  {
        //     value: '2',
        //     label: '紧急',
        //   },
      ],
      timeOutOptions: [
        {
          value: '0',
          label: '是',
        },
        {
          value: '1',
          label: '否',
        },
      ],
      proityValue: '',
      timeOutValue: '',
      lyValue: ['beijing','gugong'],
      lyData: [
        {
          value: 'beijing',
          label: '监测预警平台',
          children: [
            {
              value: 'gugong',
              label: 'AI监测预警',
            },
          ],
        },
      ],
      typeOptions: [
        {
          name: '',
          value: '',
        },
      ],
      nextOptions: [
        {
          name: '',
          value: '',
        },
      ],
      value1: '',
      value2:
        '',
      value3: '',
      value4: '',
      value5: '',
    }
  },
  methods: {
    closeEmitai() {
      this.$emit('closeEmitai')
    },
    pfMethod(){
      console.log(111);
       this.$emit('pfMethod')
    }
  },
}
</script>

<style lang='less' scoped>
/deep/ .ivu-select-selection {
  background: rgba(0, 74, 143, 0.4)
    linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
}
/deep/ .ivu-select-selection .ivu-select-selected-value {
  font-size: 16px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 22px;
}

/deep/ .ivu-input {
  background: rgba(0, 74, 143, 0.4)
    linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
  font-size: 16px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 22px;
}
/deep/ .ivu-select-placeholder {
  font-size: 16px !important;
}
/deep/ .ivu-page-item {
  background: transparent;
  color: #fff;
}
/deep/ .ivu-page-item a {
  color: #fff;
}
// /deep/ .ivu-cascader-rel{
//   height: 40px;
// }
.ai_waring {
  width: 757px;
  // height: 862px;
  background: rgba(0, 23, 59, 1);
  box-shadow: 0px 0px 43px 0px rgba(11, 54, 138, 0.35), 0px 3px 16px 0px rgba(0, 0, 0, 0.7),
    inset 0px 0px 16px 0px rgba(17, 146, 214, 0.35);
  border-radius: 11px;
  border: 1px solid #015c8c;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  // border: 1px solid red;
  z-index: 999999999999;
  .title {
    & > div {
      margin-left: 132px;
      width: 494px;
      height: 74px;
      background: url(~@/assets/shzl/sjtj_titlebg.png) no-repeat center / 100% 100%;
      background-size: 100% 100%;
    }
    span {
      font-size: 40px;
      font-family: YouSheBiaoTiHei;
      color: #ffffff;
      line-height: 74px;
      background: linear-gradient(180deg, #ffffff 0%, #2aebff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .close_btn {
      width: 44px;
      height: 44px;
      position: absolute;
      top: 16px;
      right: 25px;
      cursor: pointer;
    }
  }
  .content {
    padding: 44px 60px 40px 60px;
    .one_line {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .jjcd {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 22px;
        .jjcd_btn {
          display: flex;
          align-items: center;
          line-height: 39px;
          & > div {
            width: 69px;
            height: 39px;
            background: rgba(0, 74, 143, 0.4)
              linear-gradient(
                360deg,
                rgba(181, 223, 248, 0) 0%,
                rgba(29, 172, 255, 0.29) 100%,
                #ffffff 100%
              );
            border: 1px solid rgba(0, 162, 255, 0.6);
            &.active {
              width: 69px;
              height: 39px;
              background: linear-gradient(180deg, rgba(0, 168, 229, 0.9) 0%, #00bbc3 100%);
            }
            &:last-of-type {
              margin-left: 8px;
            }
          }
        }
      }
      .report_time {
        display: flex;
        align-items: center;
        span {
          font-size: 16px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #ffffff;
        }
      }
    }
    .two_line {
      margin-top: 29px;
      display: flex;
      align-items: center;
      
      span {
        margin-right: 14px;
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
      }
    }
    .three_line {
      height: 40px;
      margin-top: 29px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      span {
        margin-right: 14px;
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        flex-shrink: 0;
      }
    }
    .sjnr {
      height: 40px;
      margin-top: 29px;
      display: flex;
      justify-content: start;
      span {
        margin-right: 14px;
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        flex-shrink: 0;
      }
    }
    .upload {
      height: 40px;
      margin-top: 29px;
      display: flex;
      justify-content: start;
      align-items: center;
      & > span {
        margin-right: 14px;
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        flex-shrink: 0;
      }
      & > div {
        width: 98px;
        height: 30px;
        background: url(~@/assets/shzl/map/upload.png) no-repeat center / 100% 100%;
        font-size: 15px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ccf4ff;
        line-height: 30px;
        text-indent: 20px;
      }
    }
    .submit {
      width: 194px;
      height: 37px;
      margin: 63px auto 0;
      background: url(~@/assets/shzl/map/type_btn.png) no-repeat center / 100% 100%;
      background-size: 100% 100%;
      font-size: 18px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #ffffff;
      line-height: 37px;
    }
  }
}
/deep/ .ivu-select-single .ivu-select-selection .ivu-select-placeholder{
  text-align: left;
}
</style>