<template>
  <div ref="map" class="mars3d-container">
    <!-- <div class="attribute" /> -->
  </div>
</template>

<script>
import Vue from 'vue'
import 'mars3d/dist/mars3d.css'
import * as mars3d from 'mars3d'

Vue.prototype.mars3d = mars3d
Vue.prototype.Cesium = mars3d.Cesium

export default {
  name: 'Mars3dViewer',

  data() {
    return {
      map: null,
      graphicLayer: null,
      index: 0
    }
  },
  mounted() {
    this.initMap()
  },

  methods: {
    /**
     * 初始化地图
     */
    initMap() {
      // 创建三维地球场景
      this.map = new mars3d.Map(this.$refs.map, {
        scene: {
          center: { lat: 30.620978, lng: 118.761564, alt: 152960.7, heading: 3.3, pitch: -49.3 },
          showSun: false,
          showMoon: false,
          showSkyBox: false,
          showSkyAtmosphere: false,
          fog: false,
          backgroundColor: ' #120f44', // 天空背景色
          globe: {
            baseColor: '#120f44', // 地球地面背景色
            showGroundAtmosphere: false,
            enableLighting: false
          }
        },
        control: {
          baseLayerPicker: false,
          contextmenu: { hasDefault: true }
        },
        basemaps: [],
        layers: [
          {
            type: '3dtiles',
            name: '南京市建筑物',
            url: './3dtile/gulou/tileset.json',
            maximumScreenSpaceError: 1,
            maximumMemoryUsage: 1024,
            style: {
              color: 'rgb(0, 99, 255)'
            },
            marsJzwStyle: true,
            // 裁剪区域
            // planClip: {
            //   positions: [
            //     [121.477666, 31.217061, 19.1],
            //     [121.531567, 31.217061, 19.1],
            //     [121.531567, 31.258551, 19.1],
            //     [121.477666, 31.258551, 19.1]
            //   ],
            //   clipOutSide: true
            // },
            show: true
          }
        ]
      })
      const bloomEffect = new mars3d.effect.BloomEffect({
        enabled: true
      })
      this.map.addEffect(bloomEffect)
      // this.addGradientGraphics()
    },
    /**
     * 渐变三维网格
     */
    addGradientGraphics() {
      var that = this
      const geoJsonLayer = new mars3d.layer.GeoJsonLayer({
        name: '南京',
        url: './json/nanjing.json',
        symbol: {
          type: 'polygon',
          styleOptions: {
            materialType: mars3d.MaterialType.PolyGradient, // 重要参数，指定材质
            materialOptions: {
              color: '#3388cc',
              opacity: 0.7,
              alphaPower: 1.3
            },
            // 面中心点，显示文字的配置
            label: {
              text: '{name}', // 对应的属性名称
              opacity: 1,
              font_size: 25,
              color: '#fff',
              font_family: '楷体',
              outline: false,
              scaleByDistance: true,
              scaleByDistance_far: 20000000,
              scaleByDistance_farValue: 0.1,
              scaleByDistance_near: 1000,
              scaleByDistance_nearValue: 1
            }
          },
          callback: function(attr, styleOpt) {
            const randomHeight = (attr.childrenNum || 1) * 2500 // 测试的高度
            return {
              materialOptions: {
                color: that.getColor()
              },
              height: 0,
              diffHeight: randomHeight
            }
          }
        },
        popup: '{name}'
      })
      this.map.addLayer(geoJsonLayer)

      // 绑定事件
      geoJsonLayer.on(mars3d.EventType.load, function(event) {
        console.log('数据加载完成', event)
      })
      geoJsonLayer.on(mars3d.EventType.click, function(event) {
        console.log('单击了图层', event)
      })
    },
    // 颜色设置
    getColor() {
      const arrColor = [
        '#014289',
        '#006FAF',
        '#009AC0',
        '#00CCBC',
        '#60F3AB',
        '#30CE88',
        '#7DE393',
        '#D3F5CE'
      ]
      return arrColor[++this.index % arrColor.length]
    }
  }
}
</script>

<style scoped>
.mars3d-container {
  height: 100%;
  overflow: hidden;
}

/* .attribute {
    position: absolute;
    width: 100px;
    height: 25px;
    left: 10px;
    bottom: 4px;
    filter: blur(5px);
    background: url('~@/assets/image/mapbg.png') no-repeat !important;
    background-size: 100% 100% !important;
    z-index: 1;
  } */

/**cesium 工具按钮栏*/
.cesium-viewer-toolbar {
  top: auto !important;
  bottom: 35px !important;
  left: 12px !important;
  right: auto !important;
}
.cesium-toolbar-button img {
  height: 100%;
}
.cesium-viewer-toolbar > .cesium-toolbar-button,
.cesium-navigationHelpButton-wrapper,
.cesium-viewer-geocoderContainer {
  margin-bottom: 5px;
  float: left;
  clear: both;
  text-align: center;
}
.cesium-button {
  background-color: #3f4854;
  color: #e6e6e6;
  fill: #e6e6e6;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
  line-height: 32px;
}

/**cesium 底图切换面板*/
.cesium-baseLayerPicker-dropDown {
  bottom: 0;
  left: 40px;
  max-height: 700px;
  margin-bottom: 5px;
}

/**cesium 帮助面板*/
.cesium-navigation-help {
  top: auto;
  bottom: 0;
  left: 40px;
  transform-origin: left bottom;
}

/**cesium 二维三维切换*/
.cesium-sceneModePicker-wrapper {
  width: auto;
}
.cesium-sceneModePicker-wrapper .cesium-sceneModePicker-dropDown-icon {
  float: right;
  margin: 0 3px;
}

/**cesium POI查询输入框*/
.cesium-viewer-geocoderContainer .search-results {
  left: 0;
  right: 40px;
  width: auto;
  z-index: 9999;
}
.cesium-geocoder-searchButton {
  background-color: #3f4854;
}
.cesium-viewer-geocoderContainer .cesium-geocoder-input {
  background-color: rgba(63, 72, 84, 0.7);
}
.cesium-viewer-geocoderContainer .cesium-geocoder-input:focus {
  background-color: rgba(63, 72, 84, 0.9);
}
.cesium-viewer-geocoderContainer .search-results {
  background-color: #3f4854;
}

/**cesium info信息框*/
.cesium-infoBox {
  top: 50px;
  background: rgba(63, 72, 84, 0.9);
}
.cesium-infoBox-title {
  background-color: #3f4854;
}

/**cesium 任务栏的FPS信息*/
.cesium-performanceDisplay-defaultContainer {
  top: auto;
  bottom: 35px;
  right: 50px;
}
.cesium-performanceDisplay-ms,
.cesium-performanceDisplay-fps {
  color: #fff;
}

/**cesium tileset调试信息面板*/
.cesium-viewer-cesiumInspectorContainer {
  top: 10px;
  left: 10px;
  right: auto;
  background-color: #3f4854;
}
</style>
