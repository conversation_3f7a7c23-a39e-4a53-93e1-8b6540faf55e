<template>
  <div v-if="show">
    <div class="ai_waring">
      <div class="title">
        <span>应急资源</span>
      </div>
      <div class="close_btn" @click="closeEmitai"></div>
      <div class="tabs">
        <div
          class="tab"
          :class="{ active: tabActive === i }"
          v-for="(it, i) of tabs"
          :key="i"
          @click="changeTab(i)"
        >
          <span>{{ it }}</span>
        </div>
      </div>
      <div class="content">
        <div class="check_types">
          <div class="jjcd">
            <span>队伍名称：</span>
            <el-input v-model="inputValue" placeholder="请输入队伍名称名称"></el-input>
          </div>
          <div class="type_btns">
            <div>查询</div>
            <div>重置</div>
          </div>
        </div>
        <div class="table_box">
          <SwiperTableMap
            :titles="[
              '序号',
              '队伍名称',
              '队伍人数',
              '队伍性质',
              '队伍职责',
              '成立时间',
              '联系方式',
              '地址'
            ]"
            :widths="['5%', '15%', '8%', '8%', '20%', '15%', '12%', '23%']"
            :data="sjtjData"
            :contentHeight="'394px'"
            :settled="settled"
            :isNeedOperate="false"
          ></SwiperTableMap>
        </div>
        <div class="fy_page">
          <Page :total="100"></Page>
        </div>
      </div>
    </div>
    <div>
      <transition name="fade">
        <div class="bg-header"></div>
      </transition>
    </div>
  </div>
</template>

<script>
import SwiperTableMap from '../table/SwiperTableMap.vue'
import SwiperTableMapFinish from '@/components/shzl/SwiperTableMapFinish.vue'

export default {
  name: 'EmergencyResources',
  components: { SwiperTableMap, SwiperTableMapFinish },
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      inputValue: '',
      tabActive: 0,
      tabs: ['应急队伍', '机具保有量', '应急车辆', '医疗机构', '避难场所'],
      btnsArr: [
        {
          name: '未办结',
          normalBg: require('@/assets/shzl/map/left_n.png'),
          activeBg: require('@/assets/shzl/map/left_a.png')
        },
        {
          name: '已办结',
          normalBg: require('@/assets/shzl/map/right_n.png'),
          activeBg: require('@/assets/shzl/map/right_a.png')
        }
      ],
      sjtjData: [
        [
          '1',
          '',
          '25',
          '专职',
          '',
          '2023-04-06 12:10:34',
          '18733984443',
          '南京市玄武区玄武门街道中央路2号'
        ],
        [
          '2',
          '南京人防应急救援队',
          '40',
          '专职',
          '组织制定民防应急救援预案...',
          '2023-04-06 12:10:34',
          '18733984443',
          '江苏省南京市鼓楼区广州路189号'
        ]
      ],
      sjtjData1: [
        [
          '1',
          'DH6101002205050065902',
          '一般',
          '2022年5月4日，清洁工杭扣珠因几天没有巷道，找她去说一声，不料与她老公高连发生争执',
          '城市管理',
          '鼓楼区网格一',
          '2022-08-08 07:57:49',
          '-',
          '2022-08-10 07:57:49'
        ],
        [
          '2',
          'DH6101002205040020202',
          '一般',
          '',
          '城市管理',
          '鼓楼区网格一',
          '2022-08-08 07:57:49',
          '3天',
          '2022-08-10 07:57:49'
        ]
      ],
      proityOptions: [
        {
          value: '0',
          label: '一般'
        },
        {
          value: '1',
          label: '紧急'
        }
        //  {
        //     value: '2',
        //     label: '紧急',
        //   },
      ],
      timeOutOptions: [
        {
          value: '0',
          label: '是'
        },
        {
          value: '1',
          label: '否'
        }
      ],
      proityValue: '',
      timeOutValue: '',
      settled: false
    }
  },
  computed: {
    show() {
      return this.value
    }
  },
  methods: {
    closeEmitai() {
      this.$emit('input', false)
    },
    dealMethod() {
      console.log(1111)
      this.$parent.dealMethod()
    },
    dwMethod() {
      this.$parent.dwMethod()
    },
    changeTab(i) {
      this.tabActive = i
      i === 0 ? (this.settled = true) : (this.settled = false)
    }
  }
}
</script>

<style lang="less" scoped>
.bg-header {
  position: absolute;
  width: 100%;
  height: 1080px;
  background: rgba(2, 14, 50, 0.75);
  z-index: 999999;
  top: 0;
  left: 0;
}
/deep/ .ivu-select {
  width: 168px;
}
/deep/ .ivu-select-selection {
  width: 168px;
  height: 34px;
  background: rgba(0, 74, 143, 0.4)
    linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
}
/deep/ .ivu-select-selection .ivu-select-selected-value {
  font-size: 13px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 34px;
}

/deep/ .ivu-input {
  background: rgba(0, 74, 143, 0.4)
    linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 22px;
}
/deep/ .ivu-select-placeholder {
  font-size: 13px !important;
  line-height: 34px !important;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff !important;
}
/deep/ .ivu-page-item {
  background: transparent;
  color: #fff;
}
/deep/ .ivu-page-item a {
  color: #fff;
}
/deep/ .ivu-page-prev,
/deep/ .ivu-page-next {
  background: transparent;
}
.ai_waring {
  width: 1435px;
  height: 759px;
  background: rgba(9, 19, 34, 0.95);
  box-shadow: 0px 0px 22px 0px rgba(11, 54, 138, 0.35), 0px 2px 8px 0px rgba(0, 0, 0, 0.7),
    inset 0px 0px 8px 0px rgba(17, 146, 214, 0.35);
  border-radius: 11px;
  border: 1px solid #015c8c;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 99999999;
  .title {
    margin: 0 auto 23px;
    width: 635px;
    height: 76px;
    line-height: 76px;
    background: url(~@/assets/map/dialog/title2.png) no-repeat;
    display: grid;
    place-items: center;
    span {
      font-size: 36px;
      font-family: PingFangSC, PingFang SC;
      line-height: 36px;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .tabs {
    display: flex;
    gap: 13px;
    margin: 28px 0 42px 28px;
    .tab {
      width: 191px;
      height: 41px;
      line-height: 41px;
      background: url('~@/assets/map/dialog/bg5.png') no-repeat center / 100% 100%;
      cursor: pointer;
      span {
        font-size: 24px;
        font-family: PingFangSC, PingFang SC;
        color: #ffffff;
      }
      &.active {
        background: url('~@/assets/map/dialog/bg4.png') no-repeat center / 100% 100%;
        span {
          font-size: 30px;
          font-family: PingFangSC, PingFang SC;
          background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
  }
  .close_btn {
    position: absolute;
    width: 23px;
    height: 23px;
    top: 25px;
    right: 40px;
    background: url(~@/assets/map/dialog/btn3.png) no-repeat;
    cursor: pointer;
  }
  .content {
    padding: 0 30px;
    .btns {
      // border: 1px solid red;
      margin: 0 auto;
      display: flex;
      justify-content: center;
      & > div {
        width: 154px;
        height: 53px;
        span {
          font-size: 24px;
          font-family: PingFangSC, PingFang SC;
          color: #ffffff;
          line-height: 53px;
          background: linear-gradient(180deg, #ffffff 0%, #ffeba0 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
    .check_types {
      text-align: left;
      display: flex;
      .jjcd {
        display: flex;
        align-items: center;
        margin-right: 24px;
        span {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
        }
      }
      .report_time {
        margin-left: 26px;
        display: flex;
        align-items: center;
        span {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
      }
      .istime_out {
        margin-left: 26px;
        align-items: center;
        display: flex;
        span {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
      }
      .type_btns {
        margin-left: 27px;
        display: flex;
        align-items: center;
        & div {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 43px;
          background: url(~@/assets/shzl/map/type_btn.png) no-repeat center / 100% 100%;
          background-size: 100% 100%;
          text-align: center;
          cursor: pointer;
          &:first-of-type {
            width: 98px;
            height: 43px;
          }
          &:nth-of-type(2) {
            margin-left: 20px;
            width: 98px;
            height: 43px;
          }
          &:nth-of-type(3) {
            margin-left: 20px;
            width: 98px;
            height: 43px;
          }
        }
      }
    }
    .table_box {
      margin-top: 31px;
      overflow: hidden;
    }
    .fy_page {
      margin-top: 20px;
    }
  }
}
/deep/ .el-input {
  width: 200px;
  height: 34px;
  .el-input__inner {
    height: 34px;
    background: rgba(0, 74, 143, 0.4)
      linear-gradient(
        360deg,
        rgba(181, 223, 248, 0) 0%,
        rgba(29, 172, 255, 0.29) 100%,
        #ffffff 100%
      );
    border: 1px solid rgba(0, 162, 255, 0.6);
    color: #fff;
    padding: 0 30px 0 15px;
  }
}
</style>
