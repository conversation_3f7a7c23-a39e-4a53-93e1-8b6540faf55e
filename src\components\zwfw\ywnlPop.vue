<template>
  <div>
    <div class="zwfw_pop">
    <div class="title">
      <div class="title_text">业务能力</div>
      <img class="close_btn" src="@/assets/leader/img/zwfw/close.png" @click="close" />
    </div>
    <div class="pop_content">
      <BlockBox title="办件量趋势分析" class="content_box" :isListBtns="false" :blockHeight="342">
        <multi-barChart3D
          :data="qsfx.data"
          :options="qsfx.options"
          :init-option="{
            yAxis: {
              name: '万件',
            },
          }"
        />
        <div class="gajq_lz">
          <img class="" src="@/assets/shzl/zhuzhuangtu_lz.png" alt="" />
        </div>
      </BlockBox>
      <BlockBox title="审管联动统计" class="content_box" :isListBtns="false" :blockHeight="342">
        <div class="table_content">
          <swiper-table
            :data="ldtjData"
            :titles="['监管部门', '推送指数', '已查看', '未查看', '超期查看']"
            :widths="['110px', '100px', '110px', '107px', '']"
            content-height="220px"
          />
        </div>
      </BlockBox>
      <BlockBox title="事项分类统计" class="content_box" :isListBtns="false" :blockHeight="342">
        <multi-barChart3D
          :data="fltj.data"
          :options="fltj.options"
          :init-option="{
            yAxis: {
              name: '',
            },
          }"
        />
        <div class="gajq_lz">
          <img class="" src="@/assets/shzl/zhuzhuangtu_lz.png" alt="" />
        </div>
      </BlockBox>
      <BlockBox
        title="事项取号量TOP10分析"
        class="content_box"
        :isListBtns="false"
        :blockHeight="342"
      >
        <div class="table_content">
          <swiper-table
            :data="sxqhList"
            :titles="['排名', '事项', '取号量']"
            :widths="['162px', '200px', '160px']"
            content-height="220px"
          />
        </div>
      </BlockBox>
    </div>
    </div>
    <div>
      <transition name="fade">
      <div
        class="bg-header"
      ></div>
    </transition>
    </div>
  </div>
</template>

<script>
import BlockBox from '@/components/leader/common/blockBox.vue'
import SwiperTable from '@/components/leader/leader_zwjs/SwiperTable.vue'
import 'swiper/css/swiper.css'

export default {
  name: 'ywnlPop',
  components: {
    BlockBox,
    SwiperTable,
  },
  data() {
    return {
      qsfx: {
        data: [
          ['product', '受理数量', '办结数量'],
          ['7月', 150, 100],
          ['8月', 450, 310],
          ['9月', 390, 320],
          ['10月', 500, 350],
          ['11月', 419, 360],
          ['12月', 319, 260],
        ],
        options: {
          // 颜色数据
          color: ['#FAE699', '#00A3D7'],
          // 柱子的宽度
          barWidth: 20,
          // 柱子X轴位置
          symbolOffsetX: ['-25%', '25%', '25%'],
          // 顶部的大小
          symbolSize: [20, 11],
        },
      },
      fltj: {
        data: [
          ['product', ''],
          ['行政许可', 150],
          ['行政征收', 450],
          ['行政确认', 390],
          ['行政奖励', 500],
          ['行政裁决', 419],
          ['行政征用', 319],
          ['行政强制', 250],
        ],
        options: {
          // 颜色数据
          color: ['#00A3D7'],
          // 柱子的宽度
          barWidth: 20,
          // 柱子X轴位置
          symbolOffsetX: ['', '25%', '25%'],
          // 顶部的大小
          symbolSize: [20, 11],
        },
      },
      ldtjData: [
        ['南京市', '30', '10', '20', '3'],
        ['市场监督管理局', '37', '14', '20', '8'],
        ['税务局', '31', '10', '26', '9'],
        ['城管局', '36', '14', '28', '6'],
        ['水利局', '38', '11', '22', '2'],
        ['统计局', '28', '16', '25', '7'],
      ],
      sxqhList: [
        ['NO.1', '食品经营', '500'],
        ['NO.2', '药品经营', '422'],
        ['NO.3', '养老保险', '390'],
        ['NO.4', '个体工商户登记', '320'],
        ['NO.5', '婚姻登记', '311'],
        ['NO.6', '大额存款', '300'],
        ['NO.7', '劳务纠纷', '287'],
        ['NO.8', '离婚登记', '267'],
        ['NO.9', '租房补贴', '259'],
        ['NO.10', '人才补贴', '240'],
      ],
    }
  },
  methods: {
    close() {
      this.$emit('closeEmit')
    },
  },
}
</script>
<style lang="less" scoped>
 .bg-header {
    position: absolute;
    width: 100%;
    height: 1080px;
    background: rgba(2, 14, 50, 0.75);
    z-index: 999999;
    top:0;
    left:0;
  }
.gajq_lz {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  img {
    width: 100%;
    height: 100%;
  }
}
.zwfw_pop {
  width: 1251px;
  height: 839px;
  position: absolute;
  z-index: 99999999;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border-radius: 11px;
  border: 1px solid;
  background: #001638;
  border-image: linear-gradient(360deg, rgba(57, 133, 255, 0.5), rgba(0, 205, 255, 0)) 1 1;
  .title {
    margin: 0 auto;
    width: 634px;
    height: 74px;
    background: url(~@/assets/csts/title1.png) no-repeat;
    text-align: center;
    margin-bottom: 37px;
    .title_text {
      display: inline-block;
      line-height: 74px;
      font-size: 40px;
      font-family: YouSheBiaoTiHei;
      color: #ffffff;
      background: linear-gradient(180deg, #ffffff 0%, #2aebff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .close_btn {
      position: absolute;
      top: 10px;
      right: 30px;
      width: 44px;
      height: 44px;
      cursor: pointer;
    }
  }
  .pop_content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    .content_box {
      width: 533px;
      .table_content {
        margin-top: 24px;
        margin-left: 5px;
        overflow: hidden;
        width: 522px;
        .item_cont1 {
          flex: 1;
          ::v-deep .row-item {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #37c1ff;
            line-height: 20px;
          }
          ::v-deep .swiper-item {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
          }
        }
      }
    }
  }
}
</style>