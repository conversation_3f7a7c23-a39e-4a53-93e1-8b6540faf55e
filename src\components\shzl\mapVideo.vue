<template>
  <div v-if="isVisible" class="map_video">
    <!-- 视频连线 -->
    <div>
      <div class="del" @click="leave"></div>
      <div class="title"><span>视频连线</span></div>
      <div class="content">
        <div class="personVideo">
          <div class="video-cont">
            <div class="pending">
              <!-- <img src="" class="person" />
              <img src="" class="reject" /> -->
            </div>
            <div class="chating">
              <div id="video" style="margin: 0 auto">
                <div
                  id="agora_local"
                  style="
                    float: right;
                    width: 210px;
                    height: 147px;
                    display: inline-block;
                    z-index: 20;
                  "
                ></div>
              </div>
              <!-- <img src="" class="reject" /> -->
            </div>
          </div>
        </div>
      </div>
      <div @click="leave" class="reject">
        <div class="close_video"></div>
        <p class="close_p">挂断</p>
      </div>
    </div>

    <!-- 视频通话 start -->
    <div id="div_device" class="panel panel-default" style="display: none">
      <div class="select">
        <label for="audioSource">Audio source:</label>
        <select id="audioSource"></select>
      </div>
      <div class="select">
        <label for="videoSource">Video source:</label>
        <select id="videoSource"></select>
      </div>
    </div>
    <div id="div_join" class="panel panel-default" style="display: none">
      <div class="panel-body">
        App ID:
        <input id="appId" type="text" :value="spth.appId" size="36" />
        Channel:
        <input id="channel" type="text" value="demoChannel" size="4" />
        Host:
        <input id="video" type="checkbox" checked />
        <button id="join" class="btn btn-primary" @click="join()">Join</button>
        <button id="leave" class="btn btn-primary" @click="leave()">Leave</button>
        <button id="publish" class="btn btn-primary" @click="publish()">Publish</button>
        <button id="unpublish" class="btn btn-primary" @click="unpublish()">Unpublish</button>
      </div>
    </div>
  </div>
</template>

<script type='text/ecmascript-6'>
// import $ from '@/assets/jquery.min.js';
import $ from 'jquery'
import { mapSpth } from '@/api/fzjc/dwhj'
export default {
  name: 'mapVideo',
  props: {
    id: { type: String, default: '18076699312' },
    isVisible: { type: Boolean, default: true },
  },
  data() {
    return {
      // isVisible: true,
      ismapVideoShow: false,
      mobile: '',
      spth: {
        appId: '801944aa4ee442b3a69ad918bda838b2',
      },

      localStream: {},
      client: {},
    }
  },
  created() {
    console.log('isVisible', this.isVisible)
  },
  mounted() {
    this.clientInit()
    this.zhddClick()
  },
  methods: {
    show() {
      this.isVisible = true
    },
    hide() {
      // this.isVisible = false;
      this.$emit('closeVideo')
      // this.leave();
    },
    // ---------------------------------- 视频连线 start ----------------------------------
    /**
     * 人员视频连线
     */
    join(userName, id) {
      console.log('join client实例', this.client)
      console.log('人员视频连线', '进入join方法')

      // this.userName = userName;
      console.log(11111)
      console.log($('.video-cont'))
      $('.map_video').show()
      $('.video-cont').show()
      $('.video-cont .pending').show()
      console.log(11111)

      var that = this
      console.log(11111)

      document.getElementById('join').disabled = true
      document.getElementById('video').disabled = true
      var channel_key = null
      console.log(11111)

      this.client = AgoraRTC.createClient({
        mode: 'live',
      })
      console.log(11111)
      this.client.init(
        // appId.value,
        this.spth.appId,
        () => {
          mapSpth('18076699312').then((data) => {
            console.log(4444444)
            this.client.join(
              channel_key,
              'demoChannel',
              // data.resultMsg,
              null,
              (uid) => {
                // eslint-disable-next-line no-constant-condition
                if (true) {
                  var camera = videoSource.value
                  var microphone = audioSource.value
                  this.localStream = AgoraRTC.createStream({
                    streamID: uid,
                    audio: true,
                    cameraId: camera,
                    microphoneId: microphone,
                    video: true,
                    screen: false,
                  })
                  //this.localStream = AgoraRTC.createStream({streamID: uid, audio: false, cameraId: camera, microphoneId: microphone, video: false, screen: true, extensionId: 'minllpmhdgpndnkomcoccfekfegnlikg'});
                  // eslint-disable-next-line no-constant-condition
                  if (true) {
                    this.localStream.setVideoProfile('720p_3')
                  }

                  // The user has granted access to the camera and mic.
                  this.localStream.on('accessAllowed', function () {
                    console.log('localStream accessAllowed')
                  })

                  // The user has denied access to the camera and mic.
                  this.localStream.on('accessDenied', function () {
                    console.log('localStream accessDenied')
                  })
                  this.localStream.init(
                    () => {
                      this.localStream.play('agora_local')

                      this.client.publish(this.localStream, function (err) {})

                      this.client.on('stream-published', function (evt) {})
                    },
                    function (err) {}
                  )
                }
              },
              function (err) {}
            )
          })
        },
        function (err) {}
      )

      window.channelKey = ''
      this.client.on('error', (err) => {
        console.log('this.client.on error', err)
        if (err.reason === 'DYNAMIC_KEY_TIMEOUT') {
          this.client.renewChannelKey(
            channelKey,
            function () {},
            function (err) {}
          )
        }
      })

      this.client.on('stream-added', (evt) => {
        var stream = evt.stream
        $('.video-cont .pending').hide()
        $('.video-cont .chating').show()
        this.client.subscribe(stream, function (err) {})
      })
      // debugger
      this.client.on('stream-subscribed', (evt) => {
        var stream = evt.stream
        if ($('div#video #agora_remote' + stream.getId()).length === 0) {
          // 在这里调节视频窗口的尺寸
          $('div#video').append(
            '<div id="agora_remote' +
              stream.getId() +
              '" style="float:left; width:580px;height:400px;display:inline-block;"></div>'
          )
        }
        stream.play('agora_remote' + stream.getId())
      })

      this.client.on('stream-removed', function (evt) {
        var stream = evt.stream
        stream.stop()
        $('#agora_remote' + stream.getId()).remove()
      })

      this.client.on('peer-leave', (evt) => {
        var stream = evt.stream
        if (stream) {
          stream.stop()
          $('#agora_remote' + stream.getId()).remove()
          $('.video-cont').hide()
          this.leave()
        }
      })
    },
    // 挂电话
    leave() {
      console.log('leave client实例', this.client)
      document.getElementById('leave').disabled = true
      this.client.leave(
        (res) => {
          console.log('挂电话成功', res)
          this.hide()
          $('.leaflet-popup').hide()
          $('*[id^=agora_remote]').remove()
        },
        function (err) {
          console.log('挂电话', err)
        }
      )
    },
    publish() {
      document.getElementById('publish').disabled = true
      document.getElementById('unpublish').disabled = false
      this.client.publish(this.localStream, function (err) {})
    },
    unpublish() {
      document.getElementById('publish').disabled = false
      document.getElementById('unpublish').disabled = true
      this.client.unpublish(this.localStream, function (err) {})
    },

    // 视频连线插件初始化
    clientInit() {
      this.client = null
      //判断浏览器是否支持WebRTC
      if (!AgoraRTC.checkSystemRequirements()) {
        // alert("Your browser does not support WebRTC!");
      }

      this.localStream = null
      window.camera = null
      window.microphone = null

      var audioSelect = document.querySelector('select#audioSource')
      var videoSelect = document.querySelector('select#videoSource')

      function getDevices() {
        AgoraRTC.getDevices(function (devices) {
          for (var i = 0; i !== devices.length; ++i) {
            var device = devices[i]
            var option = document.createElement('option')
            option.value = device.deviceId
            if (device.kind === 'audioinput') {
              option.text = device.label || 'microphone ' + (audioSelect.length + 1)
              audioSelect.appendChild(option)
            } else if (device.kind === 'videoinput') {
              option.text = device.label || 'camera ' + (videoSelect.length + 1)
              videoSelect.appendChild(option)
            } else {
            }
          }
        })
      }

      getDevices()
    },

    zhddClick() {
      console.log(1111, this.id)
      // this.join('17798500001', '海城管理员');
      // this.join('海城管理员', this.id); //手机号
      this.join('海城管理员', '13307794956') //固定手机号黄运明北海某个网格员（正式环境）

      // $('.reject').click(() => {
      //   console.log(777777777);
      //   this.leave()
      // })
    },
  },
}
</script>

<style lang='scss' scoped>
// @font-face {
//   font-family: PingFangSC-Regular;
//   src: url(~@/assets/font/pingfang-regular.ttf);
// }

// @font-face {
//   font-family: PingFangSC-Medium;
//   src: url(~@/assets/font/pingfang-medium.ttf);
// }
#agora_local > video {
  left: 0;
}

.map_video {
  width: 700px;
  height: 640px;
  // background: rgba(15, 43, 87, 1);
  // box-shadow: 0px 0px 1.1875rem 0px rgba(134, 188, 242, 0.4);
  // border-radius: 0.125rem;
  // border: 0.125rem solid #288ae2;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999999999999;
  background: url(~@/assets/shzl/map/ai_popBg.png) no-repeat center / 100% 100%;
  background-size: 100% 100%;
  .del {
    width: 4.75rem;
    height: 3.1875rem;
    // background: url(~@/assets/img/shzl/close.png);
    position: absolute;
    right: 0;
    top: 0;
    cursor: pointer;
  }

  .title {
    // width: 47.25rem;
    // height: 1.625rem;
    background: url(~@/assets/shzl/map/title_bg.png) no-repeat center / 100% 100%;
    background-size: 100% 100%;
    //  width: 375px;
    height: 47px;
    margin: 20px auto 0;
    line-height: 47px;
    text-align: left;
    text-indent: 27px;
    font-size: 18px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #ffffff;
    position: relative;
    width: 90%;
    // background: url(~@/assets/img/shzl/jiankong_title.png);
    // border: 1px solid red;
    // margin: 4.875rem 0 0 3.625rem;

    span {
      background: linear-gradient(180deg, #ffffff 0%, #2aa6ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-left: 20px;
    }
  }

  .content {
    height: 500px;
    margin: 16px 0 0 0;
    // border: 1px solid red;
    .personVideo {
      width: 100%;
      height: 100%;
      display: flex;
      position: relative;

      .video-cont {
        position: absolute;
        // width: 230px;
        // height: 225px;
        background-size: contain;
        display: block;
        left: 50%;
        transform: translate(-50%);

        .reject {
          bottom: -10px;
          position: absolute;
          left: 50%;
          margin-left: -20.75px;
          width: 41.5px;
          height: 57.5px;
          cursor: pointer;
        }

        .pending {
          padding: 0;
          text-align: center;

          .person {
            width: 175px;
            height: 170px;
            border-radius: 8px;
          }

          .name {
            line-height: 50px;
          }
        }

        .chating {
          display: none;

          #video {
            & > div:first-of-type {
              position: absolute;
              top: 0;
              right: 0;
              width: 210px;
              height: 147px;
              display: inline-block;
              // border: 1px solid red;
            }

            & > div:nth-child(2),
            & > div:nth-child(3),
            & > div:nth-child(4) {
              div {
                width: 220px !important;
                height: 170px !important;
                position: absolute !important;
                top: 0;
              }
            }
          }
        }
      }
    }
  }

  .close_video {
    width: 60px;
    height: 60px;
    top: 510px;
    left: 320px;
    position: absolute;
    background: url(~@/assets/shzl/map/close_camera.png) no-repeat;
    // background-size: 100% 100%;
    cursor: pointer;
  }

  .close_p {
    top: 564px;
    left: 320px;
    position: absolute;
    font-size: 24px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #ffffff;
    line-height: 45px;
    cursor: pointer;
  }
}
video {
  left: 0 !important;
}
</style>
