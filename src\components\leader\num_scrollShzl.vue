<template>
  <div class="aird-digitalscroll" :style="{ lineHeight: fontSize + 'px' }">
    <p v-for="(item, index) in computeNumber" :key="index" class="aird-digitalscroll__item">
      <span
        v-if="item === '.'"
        ref="numberDom"
        :class="{ move: isMove }"
        :style="{ letterSpacing: fontSize + 'px' }"
        >..........</span
      >

      <span
        v-else-if="item === ','"
        ref="numberDom"
        :class="{ move: isMove }"
        :style="{ letterSpacing: fontSize + 'px' }"
        >,,,,,,,,,,</span
      >

      <span
        v-else
        ref="numberDom"
        :class="{ move: isMove }"
        :style="{ letterSpacing: fontSize + 'px' }"
        >0123456789</span
      >
    </p>
    <!-- <span class="unit" :style="{ color: color }">{{ unit }}</span> -->
  </div>
</template>

<script>
export default {
  //
  name: 'NumScroll',
  props: {
    number: {
      type: String,
      default: '0',
    },
    color: {
      type: String,
      default: '',
    },
    fontSize: {
      type: [String, Number],
      default: 44,
    },
    unit: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      maxLen: 10, //最大长度
      computeNumber: [], //数字补零后分割为数组，遍历
      timeTicket: null,
      isMove: false,
    }
  },
  watch: {
    number() {
      this.refresh()
    },
  },
  mounted() {
    this.refresh()
    this.increaseNumber()
  },
  beforeDestroy() {
    window.clearInterval(this.timeTicket)
    this.timeTicket = null
  },
  methods: {
    /**
     * @description: 数字补零操作，返回数组
     * @param {string} num 被操作数
     * @param {number} n 总长度
     * @return:
     */
    prefixZero(num, n) {
      return (Array(n).join('') + num).slice(-n).split('')
    },
    // 设置每一位数字的偏移
    setNumberTransform() {
      const numberItems = this.$refs.numberDom
      if (numberItems) {
        for (let index = 0; index < numberItems.length; index++) {
          const elem = numberItems[index]
          let single = Number(this.computeNumber[index]) * -10
          if (this.computeNumber[index] === '.') {
            single = -80
          } else if (this.computeNumber[index] === ',') {
            single = -90
          }
          // console.log('Number', single);
          elem.style.transform = `translate(-50%,${single}%`
        }
      }
    },
    setNumberToZero() {
      this.isMove = false
      const numberItems = this.$refs.numberDom
      for (let index = 0; index < numberItems.length; index++) {
        const elem = numberItems[index]
        elem.style.transform = `translate(-50%,-50%`
      }
    },
    // 定时增长数字
    increaseNumber() {
      this.timeTicket = setInterval(() => {
        this.setNumberToZero()
        setTimeout(() => {
          this.refresh()
        }, 15)
      }, 10000)
    },
    // 定时刷新数据
    refresh() {
      this.isMove = true
      this.computeNumber = this.prefixZero(this.number, this.maxLen)
      // console.log('this.computeNumber', this.computeNumber);
      this.$nextTick(() => {
        this.setNumberTransform()
      })
    },
  },
}
</script>

<style lang='scss' scoped>
// @font-face {
//   font-family: YouSheBiaoTiHei;
//   src: url(~@/assets/font/YouSheBiaoTiHei-2.ttf);
// }

// @font-face {
//   font-family: PingFangSC-Regular;
//   src: url(~@/assets/font/pingfang-regular.ttf);
// }

// @font-face {
//   font-family: PingFangSC-Medium;
//   src: url(~@/assets/font/pingfang-medium.ttf);
// }

.aird-digitalscroll {
  display: flex;
  justify-content: start;
  line-height: 100%;
  height: 30px;
  // margin-top: -20px;
  position: relative;
  top: 0;
  left: 33px;

  p {
    line-height: 100%;
    width: 12px;
    height: 124%;
    text-align: center;
    display: inline-block;
    position: relative;
    writing-mode: vertical-lr;
    text-orientation: upright;
    overflow: hidden;
    // font-family: DIN-BlackItalic, DIN;
    // font-weight: normal;
    // color: red;
    // line-height: 24px;
    // text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.5);
    // background: linear-gradient(180deg, #ffffff 0%, #00fffa 67%, #04a1ff 100%);
    // background-clip: text;
    // -webkit-background-clip: text;
    // -webkit-text-fill-color: transparent;

    &:last-child {
      margin-right: 0;
    }

    span {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translate(-50%, -50%);
      letter-spacing: 10px;
      // font-family: DIN-BlackItalic, DIN;
      // font-size: 20px;
      // font-family: DIN-BlackItalic, DIN;
      // font-weight: normal;
      // // color: #ffffff;
      // line-height: 24px;
      // text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.5);
      // background: linear-gradient(180deg, #ffffff 0%, #00fffa 67%, #04a1ff 100%);
      // background-clip: text;
      // -webkit-background-clip: text;
      // -webkit-text-fill-color: transparent;
    }
  }

  .unit {
    font-size: 20px;
    font-family: DIN-BlackItalic, DIN;
    line-height: 104px;
    text-align: end;
    margin-left: 3px;
  }
}

.move {
  transition: transform 2s ease;
  // font-family: DIN-BlackItalic, DIN;
  // font-weight: normal;
  // // color: #ffffff;
  // line-height: 24px;
  // text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.5);
  // background: linear-gradient(180deg, #ffffff 0%, #00fffa 67%, #04a1ff 100%);
  // background-clip: text;
  // -webkit-background-clip: text;
  // -webkit-text-fill-color: transparent;
}
</style>
