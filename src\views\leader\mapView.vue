<template>
  <div class="content">
    <!-- <gdMap v-if="!showCesiumMap" @mapclick="showCesiumMap = !showCesiumMap"></gdMap>
    <cesiumMap v-if="showCesiumMap"></cesiumMap> -->
    <!-- <marsMap v-if="!showCesiumMap"></marsMap> -->
    <!-- <LeaMap /> -->
    <SvgMap />
  </div>
</template>

<script>
// import gdMap from '../../components/leader/gdMap/gdMap.vue'
// import cesiumMap from '../../components/leader/cesiumMap/Cesium3dMap.vue'
// import marsMap from '../../components/leader/cesiumMap/mars-map.vue'
// import LeaMap from '@/components/map/LeafletMapNj.vue'
import SvgMap from '@/components/map/svgMap/index.vue'
export default {
  components: {
    // gdMap,
    // cesiumMap,
    // marsMap
    // LeaMap
    SvgMap
  },
  data() {
    return {
      showCesiumMap: false
    }
  },
  mounted() {
    console.log('地图测试页面')
  }
}
</script>

<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
}
</style>
