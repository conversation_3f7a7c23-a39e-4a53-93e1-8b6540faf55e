<template>
  <div class="event" v-if="show">
    <div class="close" @click="closeFn"></div>
    <div class="title">
      <span>确认</span>
    </div>
    <div class="content">您确认此调度请求吗?</div>
    <div class="btns">
      <!-- <div class="btn" @click="handle(0)">事件详情</div>
      <div class="btn" @click="handle(1)">一般处置</div>
      <div class="btn" @click="handle(2)">应急处置</div> -->
      <div class="btn" v-for="(item,index) in btns" :key="index" @click="handle(index)">{{item}}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EventDialog',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    list: {
      type: Object,
      default: () => {
        return {
          title: '地震灾害',
          type: '突发事件',
          time: '2023/06/02 08:56:32',
          address: '南京市鼓楼区宁海路199号',
          describe: 'XX附近发生地震伴有泥石流'
        }
      }
    },
    btns:{
      type:Array,
      default:()=>[
        '取消','确定'
      ]
    }
  },
  computed: {
    show() {
      return this.value
    }
  },
  methods: {
    closeFn() {
      this.$emit('input', false)
    },
    handle(i) {
      this.$emit('handle', i)
    }
  }
}
</script>

<style lang="less" scoped>
.event {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1111;
  width: 413px;
  height: 262px;
  padding: 20px 0 0 0;
  margin: 0 auto;
  background: url('~@/assets/map/dialog/bg8.png') no-repeat center / 100% 100%;
  box-sizing: border-box;
  .close {
    position: absolute;
    top: 31px;
    right: 43px;
    width: 24px;
    height: 24px;
    background: url('~@/assets/map/dialog/btn2.png') no-repeat;
    box-sizing: border-box;
    cursor: pointer;
  }
  .title {
    width: 375px;
    height: 47px;
    line-height: 47px;
    padding-left: 27px;
    margin: 0 auto;
    text-align: left;
    background: url('~@/assets/map/dialog/title1.png') no-repeat;
    box-sizing: border-box;
    span {
      font-size: 18px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      background: linear-gradient(180deg, #ffffff 0%, #2aa6ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .content {
    font-family: PingFangSC, PingFang SC;
    font-size: 22px;
    color: #FFFFFF;
    line-height: 26px;
    text-align: center;
    font-style: normal;
    margin-top: 38px;
  }
 .btns {
    margin-top: 44px;
    display: flex;
    justify-content: center;
    gap: 10px;
    .btn {
      width: 150px;
      height: 33px;
      line-height: 33px;
      font-size: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #ffffff;
      background: url('~@/assets/map/dialog/btn1.png') no-repeat center / 100% 100%;
      cursor: pointer;
    }
  }
}
</style>
