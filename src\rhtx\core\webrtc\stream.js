import { DEFALT_BIT_RANGE } from '../dict/index';

//流添加动作
export function useStreamAction(stream, pcInfo) {
  return {
    replaceCamera(deviceId) {
      //切换摄像头
      if (
        stream.getVideoTracks()[0] &&
        stream.getVideoTracks()[0].id === deviceId
      ) {
        return;
      }
      navigator.mediaDevices
        .getUserMedia({ video: { deviceId }, audio: false })
        .then((shareStream) => {
          let vsender = null;
          let senders = pcInfo.pc.getSenders();
          senders.forEach((sender) => {
            if (sender && sender.track && sender.track.kind === 'video') {
              vsender = sender;
              // stream.cloneVideoTrack = sender.track.clone(); //将当前视频流克隆
              stream.getVideoTracks()[0].stop();
              sender.track.stop(); //pc关闭当前视频流
              stream.removeTrack(sender.track); //流里面清除视频流
            }
          });
          if (vsender) {
            stream.addTrack(shareStream.getVideoTracks()[0]);
            vsender.replaceTrack(shareStream.getVideoTracks()[0]);
          }
        });
    },
    //禁言
    closeVoice: () => {
      stream.getAudioTracks()[0] &&
        (stream.getAudioTracks()[0].enabled = false);
    },
    //取消禁言
    openVoice: () => {
      stream.getAudioTracks()[0] && (stream.getAudioTracks()[0].enabled = true);
    },
    //关闭视频传输
    closeVideo: () => {
      stream.getVideoTracks()[0] &&
        (stream.getVideoTracks()[0].enabled = false);
    },
    //打开视频传输
    openVideo: () => {
      stream.getVideoTracks()[0] && (stream.getVideoTracks()[0].enabled = true);
    },
    //改变码率
    changeBW: (bit = DEFALT_BIT_RANGE) => {
      let vsender = null;
      let senders = pcInfo.pc.getSenders();
      senders.forEach((sender) => {
        if (sender && sender.track && sender.track.kind === 'video') {
          vsender = sender;
        }
      });
      if (!vsender) return;
      var parameters = vsender.getParameters();
      if (parameters.encodings) {
        parameters.encodings[0].maxBitrate = bit * 1000;
      }
      vsender
        .setParameters(parameters)
        .then(() => {
          console.log('Successed to set parameters!');
        })
        .catch((err) => {
          console.error(err);
        });
    },
    shareScreen: () => {
      //共享屏幕
      navigator.mediaDevices
        .getDisplayMedia({ audio: false, video: true })
        .then((shareStream) => {
          let vsender = null;
          let senders = pcInfo.pc.getSenders();
          senders.forEach((sender) => {
            if (sender && sender.track && sender.track.kind === 'video') {
              vsender = sender;
              stream.cloneVideoTrack = sender.track.clone(); //将当前视频流克隆
              sender.track.stop(); //pc关闭当前视频流
              stream.removeTrack(sender.track); //流里面清除视频流
            }
          });
          if (vsender) {
            stream.addTrack(shareStream.getVideoTracks()[0]);
            vsender.replaceTrack(shareStream.getVideoTracks()[0]);
          }
        });
    },
    cancelShareScreen() {
      //取消共享
      let vsender = null;
      let senders = pcInfo.pc.getSenders();
      senders.forEach((sender) => {
        if (sender && sender.track.kind === 'video') {
          vsender = sender;
          sender.track.stop(); //pc清除共享屏幕流
          stream.removeTrack(sender.track); //流里面清除共享屏幕流
        }
      });
      if (vsender) {
        stream.addTrack(stream.cloneVideoTrack); //流里面添加克隆的视频流
        vsender.replaceTrack(stream.cloneVideoTrack); //切换成之前的克隆流
      }
      // });
    },
    async help(targetStream, videoEl) {
      //远程协助
      let vsender = null;
      let senders = pcInfo.pc.getSenders();
      senders.forEach((sender) => {
        if (sender && sender.track && sender.track.kind === 'video') {
          vsender = sender;
          stream.cloneVideoTrack = sender.track.clone(); //将当前视频流克隆
          sender.track.stop(); //pc关闭当前视频流
          stream.removeTrack(sender.track); //流里面清除视频流
        }
      });
      pcInfo.helpObj = new RemoteCanvas({ videoEl });
      const canvasStream = await pcInfo.helpObj.init({
        stream: targetStream
      });
      pcInfo.helpObj.addAction(REMOTE_HAND);
      if (vsender) {
        stream.addTrack(canvasStream.getVideoTracks()[0]);
        vsender
          .replaceTrack(canvasStream.getVideoTracks()[0])
          .catch((e) => console.error(e));
      }
    },
    cancelHelp() {
      //取消协助
      let vsender = null;
      let senders = pcInfo.pc.getSenders();
      senders.forEach((sender) => {
        if (sender && sender.track && sender.track.kind === 'video') {
          vsender = sender;
          sender.track.stop(); //pc清除
          stream.removeTrack(sender.track); //流里面清除共享屏幕流
        }
      });
      if (vsender) {
        stream.addTrack(stream.cloneVideoTrack); //流里面添加克隆的视频流
        vsender.replaceTrack(stream.cloneVideoTrack); //切换成之前的克隆流
        stream.cloneVideoTrack = null;
      }
      fo.helpObj && pcInfo.helpObj.close();
    },
    terminate: () => {
      if (pcInfo) {
        pcInfo.close();
        pcInfo.stream.getTracks().forEach((item) => {
          item.stop();
        });
      }
    }
  };
}
