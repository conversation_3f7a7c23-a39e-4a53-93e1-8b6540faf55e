/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.96
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Matrix2-21f90abf","./PolylineVolumeGeometryLibrary-12682ca2","./defaultValue-4607806f","./ComponentDatatype-4028c72d","./PolylinePipeline-2aac2bf9","./Transforms-c450597e"],(function(a,e,n,t,r,i,s){"use strict";const o={},l=new e.Cartesian3,C=new e.Cartesian3,c=new e.Cartesian3,y=new e.Cartesian3,u=[new e.Cartesian3,new e.Cartesian3],d=new e.Cartesian3,p=new e.Cartesian3,m=new e.Cartesian3,f=new e.Cartesian3,g=new e.Cartesian3,h=new e.Cartesian3,w=new e.Cartesian3,x=new e.Cartesian3,z=new e.Cartesian3,P=new e.Cartesian3,A=new s.Quaternion,B=new e.Matrix3;function E(a,t,i,o,c){const y=e.Cartesian3.angleBetween(e.Cartesian3.subtract(t,a,l),e.Cartesian3.subtract(i,a,C)),u=o===n.CornerType.BEVELED?1:Math.ceil(y/r.CesiumMath.toRadians(5))+1,d=3*u,p=new Array(d);let m;p[d-3]=i.x,p[d-2]=i.y,p[d-1]=i.z,m=c?e.Matrix3.fromQuaternion(s.Quaternion.fromAxisAngle(e.Cartesian3.negate(a,l),y/u,A),B):e.Matrix3.fromQuaternion(s.Quaternion.fromAxisAngle(a,y/u,A),B);let f=0;t=e.Cartesian3.clone(t,l);for(let a=0;a<u;a++)t=e.Matrix3.multiplyByVector(m,t,t),p[f++]=t.x,p[f++]=t.y,p[f++]=t.z;return p}function S(a,n,t,r){let i=l;return r||(n=e.Cartesian3.negate(n,n)),i=e.Cartesian3.add(a,n,i),[i.x,i.y,i.z,t.x,t.y,t.z]}function b(a,n,t,r){const i=new Array(a.length),s=new Array(a.length),o=e.Cartesian3.multiplyByScalar(n,t,l),u=e.Cartesian3.negate(o,C);let d=0,p=a.length-1;for(let n=0;n<a.length;n+=3){const t=e.Cartesian3.fromArray(a,n,c),r=e.Cartesian3.add(t,u,y);i[d++]=r.x,i[d++]=r.y,i[d++]=r.z;const l=e.Cartesian3.add(t,o,y);s[p--]=l.z,s[p--]=l.y,s[p--]=l.x}return r.push(i,s),r}o.addAttribute=function(a,e,n,r){const i=e.x,s=e.y,o=e.z;t.defined(n)&&(a[n]=i,a[n+1]=s,a[n+2]=o),t.defined(r)&&(a[r]=o,a[r-1]=s,a[r-2]=i)};const D=new e.Cartesian3,M=new e.Cartesian3;o.computePositions=function(a){const t=a.granularity,s=a.positions,o=a.ellipsoid,C=a.width/2,c=a.cornerType,y=a.saveAttributes;let A=d,B=p,T=m,N=f,L=g,V=h,O=w,R=x,Q=z,U=P,G=[];const v=y?[]:void 0,I=y?[]:void 0;let q,j=s[0],k=s[1];B=e.Cartesian3.normalize(e.Cartesian3.subtract(k,j,B),B),A=o.geodeticSurfaceNormal(j,A),N=e.Cartesian3.normalize(e.Cartesian3.cross(A,B,N),N),y&&(v.push(N.x,N.y,N.z),I.push(A.x,A.y,A.z)),O=e.Cartesian3.clone(j,O),j=k,T=e.Cartesian3.negate(B,T);const F=[];let H;const J=s.length;for(H=1;H<J-1;H++){A=o.geodeticSurfaceNormal(j,A),k=s[H+1],B=e.Cartesian3.normalize(e.Cartesian3.subtract(k,j,B),B),L=e.Cartesian3.normalize(e.Cartesian3.add(B,T,L),L);const a=e.Cartesian3.multiplyByScalar(A,e.Cartesian3.dot(B,A),D);e.Cartesian3.subtract(B,a,a),e.Cartesian3.normalize(a,a);const d=e.Cartesian3.multiplyByScalar(A,e.Cartesian3.dot(T,A),M);e.Cartesian3.subtract(T,d,d),e.Cartesian3.normalize(d,d);if(!r.CesiumMath.equalsEpsilon(Math.abs(e.Cartesian3.dot(a,d)),1,r.CesiumMath.EPSILON7)){L=e.Cartesian3.cross(L,A,L),L=e.Cartesian3.cross(A,L,L),L=e.Cartesian3.normalize(L,L);const a=C/Math.max(.25,e.Cartesian3.magnitude(e.Cartesian3.cross(L,T,l))),r=n.PolylineVolumeGeometryLibrary.angleIsGreaterThanPi(B,T,j,o);L=e.Cartesian3.multiplyByScalar(L,a,L),r?(R=e.Cartesian3.add(j,L,R),U=e.Cartesian3.add(R,e.Cartesian3.multiplyByScalar(N,C,U),U),Q=e.Cartesian3.add(R,e.Cartesian3.multiplyByScalar(N,2*C,Q),Q),u[0]=e.Cartesian3.clone(O,u[0]),u[1]=e.Cartesian3.clone(U,u[1]),q=i.PolylinePipeline.generateArc({positions:u,granularity:t,ellipsoid:o}),G=b(q,N,C,G),y&&(v.push(N.x,N.y,N.z),I.push(A.x,A.y,A.z)),V=e.Cartesian3.clone(Q,V),N=e.Cartesian3.normalize(e.Cartesian3.cross(A,B,N),N),Q=e.Cartesian3.add(R,e.Cartesian3.multiplyByScalar(N,2*C,Q),Q),O=e.Cartesian3.add(R,e.Cartesian3.multiplyByScalar(N,C,O),O),c===n.CornerType.ROUNDED||c===n.CornerType.BEVELED?F.push({leftPositions:E(R,V,Q,c,r)}):F.push({leftPositions:S(j,e.Cartesian3.negate(L,L),Q,r)})):(Q=e.Cartesian3.add(j,L,Q),U=e.Cartesian3.add(Q,e.Cartesian3.negate(e.Cartesian3.multiplyByScalar(N,C,U),U),U),R=e.Cartesian3.add(Q,e.Cartesian3.negate(e.Cartesian3.multiplyByScalar(N,2*C,R),R),R),u[0]=e.Cartesian3.clone(O,u[0]),u[1]=e.Cartesian3.clone(U,u[1]),q=i.PolylinePipeline.generateArc({positions:u,granularity:t,ellipsoid:o}),G=b(q,N,C,G),y&&(v.push(N.x,N.y,N.z),I.push(A.x,A.y,A.z)),V=e.Cartesian3.clone(R,V),N=e.Cartesian3.normalize(e.Cartesian3.cross(A,B,N),N),R=e.Cartesian3.add(Q,e.Cartesian3.negate(e.Cartesian3.multiplyByScalar(N,2*C,R),R),R),O=e.Cartesian3.add(Q,e.Cartesian3.negate(e.Cartesian3.multiplyByScalar(N,C,O),O),O),c===n.CornerType.ROUNDED||c===n.CornerType.BEVELED?F.push({rightPositions:E(Q,V,R,c,r)}):F.push({rightPositions:S(j,L,R,r)})),T=e.Cartesian3.negate(B,T)}j=k}let K;return A=o.geodeticSurfaceNormal(j,A),u[0]=e.Cartesian3.clone(O,u[0]),u[1]=e.Cartesian3.clone(j,u[1]),q=i.PolylinePipeline.generateArc({positions:u,granularity:t,ellipsoid:o}),G=b(q,N,C,G),y&&(v.push(N.x,N.y,N.z),I.push(A.x,A.y,A.z)),c===n.CornerType.ROUNDED&&(K=function(a){let t=d,r=p,i=m,s=a[1];r=e.Cartesian3.fromArray(a[1],s.length-3,r),i=e.Cartesian3.fromArray(a[0],0,i),t=e.Cartesian3.midpoint(r,i,t);const o=E(t,r,i,n.CornerType.ROUNDED,!1),l=a.length-1,C=a[l-1];return s=a[l],r=e.Cartesian3.fromArray(C,C.length-3,r),i=e.Cartesian3.fromArray(s,0,i),t=e.Cartesian3.midpoint(r,i,t),[o,E(t,r,i,n.CornerType.ROUNDED,!1)]}(G)),{positions:G,corners:F,lefts:v,normals:I,endPositions:K}},a.CorridorGeometryLibrary=o}));
