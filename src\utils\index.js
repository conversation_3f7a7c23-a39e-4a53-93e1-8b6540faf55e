/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 * @param {*} rootId 根Id 默认 0
 */
export function handleTree(data, id, parentId, children, rootId) {
	id = id || 'id'
	parentId = parentId || 'parentId'
	children = children || 'children'
	rootId =
		rootId ||
		Math.min.apply(
			Math,
			data.map((item) => {
				return item[parentId]
			}),
		) ||
		0
	// 对源数据深度克隆
	const cloneData = JSON.parse(JSON.stringify(data))
	// 循环所有项
	const treeData = cloneData.filter((father) => {
		let branchArr = cloneData.filter((child) => {
			// 返回每一项的子级数组
			return father[id] === child[parentId]
		})
		// eslint-disable-next-line no-unused-expressions
		branchArr.length > 0 ? (father.children = branchArr) : ''
		// 返回第一层
		return father[parentId] === rootId
	})
	return treeData !== '' ? treeData : data
}

/**
 * 表单重置
 * @param {Function} func
 * @param {string} refName 表单名称
 * @return {*}
 */
export function resetForm(refName) {
	if (this.$refs[refName]) {
		this.$refs[refName].resetFields()
	}
}

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(func, wait, immediate) {
	let timeout, args, context, timestamp, result

	const later = function() {
		// 据上一次触发时间间隔
		const last = +new Date() - timestamp

		// 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
		if (last < wait && last > 0) {
			timeout = setTimeout(later, wait - last)
		} else {
			timeout = null
			// 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
			if (!immediate) {
				result = func.apply(context, args)
				if (!timeout) context = args = null
			}
		}
	}

	return function(...args) {
		context = this
		timestamp = +new Date()
		const callNow = immediate && !timeout
		// 如果延时不存在，重新设定延时
		if (!timeout) timeout = setTimeout(later, wait)
		if (callNow) {
			result = func.apply(context, args)
			context = args = null
		}

		return result
	}
}

/**
 * 日期格式化
 * @param {Function} func
 * @param {string} dateStr 日期字符串
 * * @param {number} formatType 格式类型
 * @return {*}
 */
export function formatDate(dateStr, formatType = 1) {
	if (!dateStr) return ''
	const date = new Date(dateStr)
	const y = date.getFullYear()
	const m = ('00' + (date.getMonth() + 1)).substr(-2)
	const d = ('00' + date.getDate()).substr(-2)
	const hh = ('00' + date.getHours()).substr(-2)
	const mm = ('00' + date.getMinutes()).substr(-2)
	const ss = ('00' + date.getSeconds()).substr(-2)
	if (formatType === 1) {
		return y + '-' + m + '-' + d + ' ' + hh + ':' + mm + ':' + ss
	} else if (formatType === 2) {
		return y + '-' + m + '-' + d
	} else if (formatType === 3) {
		return `${y}${m}${d}`
	}
	return ''
}

/**
 * 获取某天的前一周的日期
 * @param {Function} func
 * @param {string} date 日期字符串
 * * @param {number} formatType 格式类型
 * @return {*}
 */
export function getDateAndLastWeek(date) {
	// 解析输入的日期字符串
	const someDate = new Date(date.replace(/-/g, '/')) // 注意：有些浏览器可能不支持'-'作为日期分隔符，所以替换为'/'
	someDate.setDate(someDate.getDate() - 1) // 获取当前日期
	// 验证日期是否有效
	if (isNaN(someDate.getTime())) {
		throw new Error('日期格式不正确')
	}

	// 获取该日期前一周的日期
	const previousWeekDate = new Date(someDate.getTime() - 6 * 24 * 60 * 60 * 1000) // 减去一周的毫秒数

	// 返回两个日期的字符串
	return {
		currentDate: formatDate(someDate),
		lastWeekDate: formatDate(previousWeekDate),
	}

}

// 生成随机数
export function randomString(len = 32) {
	let res = new Date().getTime()
	const string = `ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxzy0123456789!@#$%^&*()_-=+{}?*`
	for (let i = 0; i < len; i++) {
		const index = Math.floor(Math.random() * string.length)
		res += string[index]
	}
	return res
}

function changeColorToRGBA(color) {
	const throwErr = () => {
		throw new Error(`请输入正确的颜色，例如#ff0000, rgb(255,0,0), rgba(255,0,0,1)`)
	}
	if (color.includes('rgb')) {
		const c = color.substr(0, color.length - 1).split('(')
		if (c[0] === 'rgb') {
			if (c[1].split(',').length === 3) {
				const _c = c[1].split(',')
				let flag = false
				_c.forEach((item) => {
					if (Number(item) < 0 || Number(item) > 255) {
						flag = true
					}
				})
				if (flag) {
					throwErr()
				} else {
					_c[3] = 1
					return `rgba(${_c.join(',')})`
				}
			} else {
				throwErr()
			}
		} else if (c[0] === 'rgba') {
			if (c[1].split(',').length === 4) {
				const _c = c[1].split(',')
				let flag = false
				_c.forEach((item) => {
					if (Number(item) < 0 || Number(item) > 255) {
						flag = true
					}
				})
				if (flag) {
					throwErr()
				} else {
					if (_c[3] > 1) {
						_c[3] = 1
						return `rgba(${_c.join(',')})`
					} else {
						return color
					}
				}
			} else {
				throwErr()
			}
		} else {
			throwErr()
		}
	} else {
		if (color.length !== 7 || color[0] !== '#') {
			throwErr()
		} else {
			const r = parseInt(color.slice(1, 3), 16)
			const g = parseInt(color.slice(3, 5), 16)
			const b = parseInt(color.slice(5, 7), 16)
			return `rgba(${r}, ${g}, ${b}, 1)`
		}
	}
}

//转换颜色
export function getLevelsByColor(color) {
	const baseColor = changeColorToRGBA(color)
	const rgbaNumber = baseColor
		.replace('rgba(', '')
		.replace(')', '')
		.split(',')
	const colorLeves = {
		base: [...rgbaNumber].map(Number), //  原色
		fade90: [...rgbaNumber].map(Number), // 90% 透明
		fade50: [...rgbaNumber].map(Number), // 50% 透明
		fade30: [...rgbaNumber].map(Number), // 50% 透明
		fade20: [...rgbaNumber].map(Number), // 20% 透明
		shade5: [...rgbaNumber].map(Number), // 与黑色混合 5%
		shade20: [...rgbaNumber].map(Number), // 与黑色混合 20%
		shade50: [...rgbaNumber].map(Number), // 与黑色混合 50%
		shade80: [...rgbaNumber].map(Number), // 与黑色混合 80%
		tint20: [...rgbaNumber].map(Number), // 与白色混合 20%
		tint50: [...rgbaNumber].map(Number), // 与白色混合 50%
		tint80: [...rgbaNumber].map(Number), // 与白色混合 80%
		tint90: [...rgbaNumber].map(Number), //  与白色混合 90%
	}
	colorLeves.fade90[3] = 0.9
	colorLeves.fade50[3] = 0.5
	colorLeves.fade20[3] = 0.2
	colorLeves.fade30[3] = 0.3
	// 转换rgb三个数值
	for (let i = 0; i < 3; i++) {
		colorLeves.shade5[i] = Math.ceil(colorLeves.shade5[i] - colorLeves.shade5[i] * 0.05)
		colorLeves.shade20[i] = Math.ceil(colorLeves.shade20[i] - colorLeves.shade5[i] * 0.2)
		colorLeves.shade50[i] = Math.ceil(colorLeves.shade50[i] - colorLeves.shade5[i] * 0.5)
		colorLeves.shade80[i] = Math.ceil(colorLeves.shade80[i] - colorLeves.shade5[i] * 0.8)
	}
	// 转换rgb三个数值
	for (let i = 0; i < 3; i++) {
		colorLeves.tint20[i] = Math.ceil(colorLeves.tint20[i] + 255 * 0.2 - colorLeves.tint20[i] * 0.2)
		colorLeves.tint50[i] = Math.ceil(colorLeves.tint50[i] + 255 * 0.5 - colorLeves.tint50[i] * 0.5)
		colorLeves.tint80[i] = Math.ceil(colorLeves.tint80[i] + 255 * 0.8 - colorLeves.tint80[i] * 0.8)
		colorLeves.tint90[i] = Math.ceil(colorLeves.tint90[i] + 255 * 0.9 - colorLeves.tint90[i] * 0.9)
	}
	const result = {}
	for (const key in colorLeves) {
		result[key] = 'rgba(' + colorLeves[key].join() + ')'
	}
	return result
}

export function getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, height, i) {
	// 计算
	const midRatio = (startRatio + endRatio) / 2

	let startRadian = startRatio * Math.PI * 2
	const endRadian = endRatio * Math.PI * 2
	const midRadian = midRatio * Math.PI * 2
	if (i === 1) {
		startRadian = 0
	}
	// 如果只有一个扇形，则不实现选中效果。
	if (startRatio === 0 && endRatio === 1) {
		isSelected = false
	}

	// 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
	// k = typeof k !== "undefined" ? k : 1 / 3;
	k = 1

	// 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
	const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0
	const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0

	// 计算高亮效果的放大比例（未高亮，则比例为 1）
	const hoverRate = isHovered ? 1.05 : 1

	// 返回曲面参数方程
	return {
		u: {
			min: -Math.PI,
			max: Math.PI * 3,
			step: Math.PI / 32,
		},

		v: {
			min: 0,
			max: Math.PI * 2,
			step: Math.PI / 20,
		},

		x: function(u, v) {
			if (u < startRadian) {
				return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
			}
			if (u > endRadian) {
				return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
			}
			return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate
		},

		y: function(u, v) {
			if (u < startRadian) {
				return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
			}
			if (u > endRadian) {
				return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
			}
			return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate
		},

		z: function(u, v) {
			if (u < -Math.PI * 0.5) {
				return Math.sin(u)
			}
			if (u > Math.PI * 2.5) {
				return Math.sin(u)
			}
			if (i === 0) {
				return Math.sin(v) > 0 ? 4 : 1
			}
			return Math.sin(v) > 0 ? 1 : -1
		},
	}
}
