<template>
  <div class="lineBarEcharts_box" ref="chartRef"></div>
</template>

<script>
import { getQgdWbjTj } from '@/api/hs/hs.api.js'
export default {
  name: 'lineBarEcharts',
  props: {
    data: {
      type: Array,
      default: (item) => {
        return [
          ['product', '增速', '生产总值'],
          ['2016', 40, 8],
          ['2017', 36, 7],
          ['2018', 21, 4],
          ['2019', 34, 6],
          ['2020', 42, 6],
        ]
      },
    },
  },
  data() {
    return {
      // 生产总值图表配置项
      options: {
        yNameLeft: '总量（亿元）',
        yNameRight: '增速',
        xName: '年度',
      },
      echartsData: [],
      total: 0,
    }
  },
  mounted() {
    this.getQgdWbjTjFn()
  },
  watch: {
    // data: {
    //   deep: true,
    //   immediate: true,
    //   handler(val) {
    //     this.$nextTick(() => {
    //       this.linebarChart(this.$refs.chartRef, this.data, this.options)
    //     })
    //   },
    // },
  },
  methods: {
    async getQgdWbjTjFn() {
      let res = await getQgdWbjTj()
      this.echartsData = [
        { value: Number(res.result.isEmeLevel), name: '紧急' },
        { value: Number(res.result.unEmeLevel), name: '一般' },
      ]
      console.log('this.echartsData', this.echartsData)
      this.total = this.echartsData.map(it=>it.value).reduce((prev, cur, index, arr) => {
        return prev + cur
      }, 0)
      console.log('total', this.total)
      this.$nextTick(() => {
        this.linebarChart(this.$refs.chartRef)
      })
    },
    // 柱状折线图
    linebarChart(id) {
      var scale = 1
      var echartData = this.echartsData
      var rich = {
        yellow: {
          color: '#ffc72b',
          fontSize: 16,
          padding: [5, 4],
          align: 'center',
        },
        total: {
          color: '#ffc72b',
          fontSize: 18 * scale,
          align: 'center',
        },
        white: {
          color: '#fff',
          align: 'center',
          fontSize: 14 * scale,
          padding: [0, 0, 0, 0],
        },
        blue: {
          color: '#49dff0',
          fontSize: 16,
          align: 'center',
          // padding: [20,0,0,0],
        },
        // hr: {
        //   borderColor: '#0b5263',
        //   width: '100%',
        //   borderWidth: 1,
        //   height: 0,
        // },
      }
      let option = {
        title: {
          text: this.total,
          left: '24%',
          top: '32%',
          padding: [24, 0],
          textStyle: {
            color: '#fff',
            fontSize: 18,
            align: 'center',
          },
        },
        legend: {
          selectedMode: false,
          formatter: function (name) {
            var total = 0 //各科正确率总和
            var averagePercent //综合正确率
            // echartData.forEach(function (value, index, array) {
            //   total += value.value
            // })
            // return '{total|' + total + '}'
          },
          data: [echartData[0].name],
          // data: ['高等教育学'],
          // itemGap: 50,
          left: 'center',
          top: 'center',
          icon: 'none',
          align: 'center',
          textStyle: {
            color: '#fff',
            fontSize: 14,
            rich: rich,
          },
        },
        series: [
          {
            name: '总考生数量',
            type: 'pie',
            radius: ['40%', '50%'],
            center: ['30%', '50%'],
            hoverAnimation: false,
            color: ['#deb140', '#49dff0', '#49dff0', '#034079', '#6f81da', '#00ffb4'],
            label: {
              show: false,
              normal: {
                formatter: function (params, ticket, callback) {
                  var total = 0 //考生总数量
                  var percent = 0 //考生占比
                  echartData.forEach(function (value, index, array) {
                    total += value.value
                  })
                  percent = ((params.value / total) * 100).toFixed(1)
                  return (
                    '{white|' +
                    params.name +
                    '}{yellow|' +
                    params.value +
                    '}{blue|' +
                    percent +
                    '%}'
                  )
                },
                rich: rich,
              },
            },
            labelLine: {
              normal: {
                length: 16,
                // length2: 0,
                lineStyle: {
                  color: '#0b5263',
                },
              },
            },
            data: echartData,
          },
        ],
      }
      let myChart = this.$echarts.init(id, 'ucdc')
      myChart.setOption(option)
      window.addEventListener('resize', function () {
        myChart.resize()
      })
    },
  },
}
</script>

<style lang="less" scoped>
.lineBarEcharts_box {
  width: 100%;
  height: 100%;
}
</style>