
<template>
    <div class="ai_waring">
        <template v-if="popAreaInfo&&!more" >
            <div class="ai_wrapper">
                    <div class="title">{{popAreaInfo.name}}  <img @click="closeEmitai" class="close_btn" src="@/assets/shzl/map/close_btn3.png" alt="" /></div>
                    <div class="item_list">
                        <div class="item">
                            <div class="name">户籍人口:</div>
                            <div  class="num"><span>{{ popAreaInfo.num }}</span>万人</div>
                        </div>
                        <div class="item">
                            <div class="name">生产总值:</div>
                            <div  class="num"><span>{{ popAreaInfo.product }}</span>亿元</div>
                        </div>
                    </div>
                    <!-- <div class="more-btn" @click="moreClick">更多</div> -->
            </div>
        </template>
        <template v-if="popAreaInfo&&more">
            <div  class="ai_wrapper_more">
                <div class="title">
                    <span>{{popAreaInfo.name}}</span>
                    <img @click="closeEmitai" class="close_btn" src="@/assets/shzl/map/close_btn.png" alt="" />
                </div>
                <div class="content">
                    <div class="sub_title"><span></span>基本情况</div>
                    <div class="list">
                        <div class="item" v-for="(item, index) in data1" :key="index">
                            <div class="img">
                                <img :src="item.img" alt="">
                            </div>
                            <div class="item_right">
                                <div class="num">{{ item.num }}</div>
                                <div class="name">{{ item.name }}</div>
                            </div>
                        </div>
                    </div>

                    <div class="sub_title"><span></span>人口</div>
                    <div class="list">
                        <div class="item" v-for="(item, index) in data2" :key="index">
                            <div class="img">
                                <img :src="item.img" alt="">
                            </div>
                            <div class="item_right">
                                <div class="num">{{ item.num }}</div>
                                <div class="name">{{ item.name }}</div>
                            </div>
                        </div>
                    </div>

                    <div class="sub_title"><span></span>财政、经济、贸易</div>
                    <div class="list">
                        <div class="item" v-for="(item, index) in data3" :key="index">
                            <div class="img">
                                <img :src="item.img" alt="">
                            </div>
                            <div class="item_right">
                                <div class="num">{{ item.num }}</div>
                                <div class="name">{{ item.name }}</div>
                            </div>
                        </div>
                    </div>

                    <div class="sub_title"><span></span>社会保障</div>
                    <div class="list">
                        <div class="item" v-for="(item, index) in data4" :key="index">
                            <div class="img">
                                <img :src="item.img" alt="">
                            </div>
                            <div class="item_right">
                                <div class="num">{{ item.num }}</div>
                                <div class="name">{{ item.name }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>

<script>
export default {
    props: {
        popAreaInfo: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            more: false,
            data1: [{
                img: require("@/assets/cyjj/pop/1-1.png"),
                name: '行政面积（m²）',
                num: 36
            }, {
                img: require("@/assets/cyjj/pop/1-2.png"),
                name: '街道（个）',
                num: 10
            }, {
                img: require("@/assets/cyjj/pop/1-3.png"),
                name: '社区（个）',
                num: 36
            }],
            data2: [{
                img: require("@/assets/cyjj/pop/2-1.png"),
                name: '户籍户数（户）',
                num: 36
            }, {
                img: require("@/assets/cyjj/pop/2-2.png"),
                name: '户籍人口（万人）',
                num: 16
            }],
            data3: [{
                img: require("@/assets/cyjj/pop/3-1.png"),
                name: '一般公共预算收入（万元）',
                num: 10
            }, {
                img: require("@/assets/cyjj/pop/3-2.png"),
                name: '一般公共预算支出（万元）',
                num: 10
            }, {
                img: require("@/assets/cyjj/pop/3-3.png"),
                name: '资产总额（万元）',
                num: 30
            }, {
                img: require("@/assets/cyjj/pop/3-4.png"),
                name: '工业企业数（个）',
                num: 50
            }, {
                img: require("@/assets/cyjj/pop/3-5.png"),
                name: '建筑企业数（个）',
                num: 7662
            }, {
                img: require("@/assets/cyjj/pop/3-6.png"),
                name: '住宿餐饮业企业数（个）',
                num: 400
            }],
            data4: [{
                img: require("@/assets/cyjj/pop/4-1.png"),
                name: '城乡居民基本养老保险参保人数（人）',
                num: 10
            }, {
                img: require("@/assets/cyjj/pop/4-2.png"),
                name: '城乡居民基本医疗保险参保人数（人）',
                num: 37
            }, {
                img: require("@/assets/cyjj/pop/4-3.png"),
                name: '城乡居民最低生活保险参保人数（人）',
                num: 37
            }]
        }
    },
    methods: {
        closeEmitai() {
            this.popShow = false;
            this.$emit('closeEmitai')
        },
        moreClick(){
            this.more=true;
            setTimeout(()=>{
                this.more=false;
            },2000)
        }
    },
}
</script>

<style lang='less' scoped>
.ai_wrapper {
    background: url(~@/assets/shzl/map/ai_popBg2.png) no-repeat center / 100% 100%;
    background-size: 100% 100%;
    width: 244px;
    height:156px;
    .title{
        height: 36px;
        line-height: 36px;
        background: linear-gradient(270deg, rgba(173,224,255,0) 0%, rgba(116,189,255,0.3) 100%);
        font-size: 20px;
        font-family: PangMenZhengDao;
        color: #FFFFFF;
        text-shadow: 1px 1px 0px rgba(0,23,50,0.5);
        padding-left:16px;
        text-align: left;
        position: relative;
        .close_btn {
            position: absolute;
            top: 11px;
            right: 25px;
            cursor: pointer;
        }
    }
    .more-btn{
        background: url(~@/assets/shzl/map/tip_btn1.png) no-repeat center / 100% 100%;
         background-size: 100% 100%; 
         width:96px;
         height:38px;
         line-height:38px;
         color: #FFFFFF;
         font-size: 14px;
         margin:10px auto;
    }
    .item_list{
        .item{
            display:flex;
            color:#fff;
            height:42px;
            align-items: center;
            margin:0 22px;
            border-bottom: 1px dashed rgba(151,151,151,0.2);
            justify-content: space-between;
            .name{
                color:#C2DDFC;
                font-size:14px;
            }
            .num{
                color: #ffffff;
                font-size:14px;
                span{
                    font-size: 20px;
                    font-family: DIN-BlackItalic, DIN;
                    font-weight: normal;
                    color: #FFFFFF;
                    margin-right:8px;
                }
            }
        }
    }
}

.ai_wrapper_more {
    width: 962px;
    // height:721px;
    // position: absolute;
    // left: 50%;
    // top: 60%;
    // transform: translate(-50%, -50%);
    // border: 1px solid red;
    background: url(~@/assets/shzl/map/ai_popBg1.png) no-repeat center / 100% 100%;
    background-size: 100% 100%;

    .title {
        background: url(~@/assets/shzl/map/title_bg.png) no-repeat center / 100% 100%;
        background-size: 100% 100%;
        width: 924px;
        height: 47px;
        margin: 20px auto 0;
        line-height: 47px;
        text-align: left;
        text-indent: 50px;
        font-size: 18px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        position: relative;

        span {
            background: linear-gradient(180deg, #ffffff 0%, #2aa6ff 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .close_btn {
            position: absolute;
            top: 11px;
            right: 25px;
            cursor: pointer;
        }
    }

    .content {
        .sub_title {
            font-size: 16px;
            color: #fff;
            text-align: left;
            height: 70px;
            line-height: 70px;

            span {
                width: 4px;
                height: 10px;
                background: linear-gradient(236deg, #0196FF 0%, #00FEFF 100%);
                border-radius: 1px;
                margin-right: 10px;
                display: inline-block;
            }
        }

        .list {
            display: flex;
            flex-wrap: wrap;

            .item {
                display: flex;
                margin-right: 25px;

                .img {
                    img {
                        width: 60px;
                        height: 60px;
                    }
                }

                .item_right {
                    margin-left: 11px;
                    text-align: left;

                    .num {
                        font-size: 24px;
                        font-family: YouSheBiaoTiHei;
                        color: #E3EEFF;
                        line-height: 31px;
                        text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
                    }

                    .name {
                        font-size: 14px;
                        font-family: PingFangSC-Medium, PingFang SC;
                        font-weight: 500;
                        color: #7FB2FB;
                        line-height: 20px;
                        text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
                        width: 180px;
                    }
                }
            }
        }

        .desc {
            color: #fff;
            text-align: left;
        }

        padding: 20px 50px 46px 48px;
        // border: 1px solid red;
    }
}
</style>