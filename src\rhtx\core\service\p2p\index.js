import P2P from '../../operate/p2p/index';
import P2PPop from './popSession';
import { getCallDeviceType, tplReplace } from '../../util';
import { CALL_TYPE_TEXT_MAP } from './data';
import { p2pInviteTipTpl } from './tpl';
import P2P_EVENT from '../../operate/p2p/event';
import Render from './render';
import P2PPopSession from './popSession';
import { useMeetProcess } from './process';
import { BEFORE_MINE_ANSWER } from '../../websocket/message';

export default class P2PService extends P2P {
  constructor({ userInfo, ws }) {
    super({ userInfo, ws });
    this.initWsNotice();
  }

  initWsNotice() {
    let mouted = null;
    function mountedEl(el) {
      mouted = el;
    }
    layui.use(() => {
      const { layer } = window.layui;
      window._rtc.ws.on('mine_p2p_ring', ({ user, call }) => {
        const target = this.getTarget(call);
        layer.open({
          type: 1,
          title: '来电提示',
          shade: 0,
          maxmin: true,
          content: tplReplace(p2pInviteTipTpl, {
            target: target,
            type: CALL_TYPE_TEXT_MAP[call.call_type]
          }),
          btn: ['接受', '拒绝'],
          yes: async (index, layero) => {
            await _rtc.ws.fire(BEFORE_MINE_ANSWER, {
              target,
              mountedEl,
              wsInfo: { user, call }
            });
            this.answerPop({ el: mouted, call });
            mouted = null;
            layer.close(index);
          },
          btn2: (index, layero) => {
            this.hangup(call);
            layer.close(index);
          }
        });
      });
    });
  }

  async answerPop({ el, call }) {
    const popSession = new P2PPopSession();
    const render = new Render(el);
    render.openLoading();
    const session = await this.answer(call, useMeetProcess.call(popSession));
    render.setSession(session);
    popSession.init({ session, render });
  }

  /**
   *@param {Object} options 配置对象
   * @param {Element} options.el 挂载元素
   * @param  {String} options.target 被叫号码
   * @param {'voice' | 'voice_video' | 'video'} options.callType 呼叫类型
   * @returns
   */
  async callPop({ el, target, callType = 'voice_video' }) {
    const popSession = new P2PPopSession();
    const render = new Render(el);
    render.openLoading();
    const session = await this.call(
      {
        target,
        mediaConstraints: getCallDeviceType(callType)
      },
      useMeetProcess.call(popSession)
    );
    render.setSession(session); //呼叫后 ---  对方接听前  将session传给render
    popSession.init({ session, render });
    return popSession;
  }
}
