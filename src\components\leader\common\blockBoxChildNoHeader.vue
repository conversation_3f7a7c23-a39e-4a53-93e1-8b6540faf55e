<template>
	<div class="child-block">
		<div class="content" v-if="contentDataList.length > 0">
			<slot></slot>
			<div
				class="data-item"
				v-for="(item, index) in contentDataList"
				:key="index"
				@click="
					() => {
						item.func(item.argmachType)
					}
				"
			>
				<el-tooltip effect="dark" popper-class="tooltip-item" :content="item.title" placement="top">
					<div class="data-item-title">{{ item.title }}</div>
				</el-tooltip>

				<div class="data-item-content">
					<img class="data-item-icon" :src="item.iconUrl" alt="" />
					<div class="data-item-number-box">
						<el-tooltip effect="dark" popper-class="tooltip-item" :content="item.number" placement="top">
							<div class="data-item-number">{{ item.number }}</div>
						</el-tooltip>
					</div>
				</div>
			</div>
		</div>
		<div v-else class="empty-box">
			<img class="zwsjImg" src="@/assets/bjnj/zwsj.png" />
		</div>
	</div>
</template>

<script>
export default {
	props: {
		contentDataList: {
			type: Array,
			default: () => [],
		},

		titleUnit: {
			type: String,
			default: '',
		},
	},
	data() {
		return {}
	},
	computed: {},
	methods: {},

	watch: {},
}
</script>

<style lang="less" scoped>
.child-block {
	width: 446px;
	height: 100%;
	box-sizing: border-box;
	z-index: 101;
	position: relative;
	padding-left: 20px;
	padding-right: 20px;
	padding-top: 20px;
	overflow: auto;
	&::-webkit-scrollbar {
		width: 4px; /* 滚动条宽度 */
	}

	//轨道
	&::-webkit-scrollbar-track {
		background: #04244e; /* 滚动条轨道背景 */
		border-radius: 10px;
		padding: 5px 0;
	}

	//滑块
	&::-webkit-scrollbar-thumb {
		background: #82aed0; /* 滑块背景颜色 */
		border-radius: 10px;
		height: 15px;
	}

	//悬停滑块颜色
	&::-webkit-scrollbar-thumb:hover {
		background: #ff823b; /* 鼠标悬停时滑块颜色 */
	}

	//箭头
	&::-webkit-scrollbar-button {
		display: none; /* 隐藏滚动条的箭头按钮 */
	}

	.content {
		position: relative;
		width: 100%;
		// height: calc(100% - 74px);
		display: flex;
		justify-content: flex-start;
		flex-wrap: wrap;
		align-items: center;

		&.no-active-content {
			height: 0;
			display: none;
		}

		.data-item {
			width: 134px;
			// height: 40px;
			display: flex;
			flex-direction: column;
			justify-content: flex-start;
			align-items: center;
			margin-bottom: 40px;
			cursor: pointer;

			.data-item-title {
				width: 100%;
				height: 14px;

				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #b0e0ff;
				line-height: 14px;
				text-align: left;
				font-style: normal;
				text-transform: none;

				white-space: nowrap; /* 强制文本不换行 */
				overflow: hidden; /* 超出部分隐藏 */
				text-overflow: ellipsis; /* 超出时显示省略号 */

				margin-bottom: 6px;
				line-height: 14px;
				text-align: left;
				// display: flex;
				// justify-content: flex-start;
				// align-items: center;
			}
		}

		.data-item:hover {
			// background: url(~@/assets/img/block-box/block-box-child-item-active-bg.png);
			/* 背景不重复 */
			background-repeat: no-repeat;
			/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
			background-size: cover;
			/* 背景居中显示（可选） */
			background-position: center;
		}

		.data-item-icon {
			width: 40px;
			height: 40px;
			/* 背景不重复 */
			background-repeat: no-repeat;
			/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
			background-size: cover;
			/* 背景居中显示（可选） */
			background-position: center;
			margin-right: 6px;
		}

		.data-item-content {
			width: 100%;
			height: 40px;
			display: flex;
			justify-content: center;
			align-items: flex-start;

			.data-item-number-box {
				width: 90px;
				height: 100%;
				display: flex;
				justify-content: flex-start;
				align-items: center;

				.tooltip-item {
					// margin: 4px;
				}
				.data-item-number {
					width: 100%;
					height: 20px;

					font-family: DINCond-Bold, DINCond-Bold;
					font-weight: 400;
					font-size: 18px;
					color: #4de4ff;
					line-height: 20px;
					text-align: left;
					font-style: normal;
					text-transform: none;

					white-space: nowrap; /* 强制文本不换行 */
					overflow: hidden; /* 超出部分隐藏 */
					text-overflow: ellipsis; /* 超出时显示省略号 */
				}
				.data-item-number-unit {
					height: 14px;

					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 12px;
					color: #4de4ff;
					line-height: 14px;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}
			}
		}
	}

	.empty-box {
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		.zwsjImg {
			width: 240px;
			margin: 20px 0;
		}
	}
}

.popDropDown {
	::v-deep .el-dropdown {
		color: #caecff;
		font-family: YouSheBiaoTiHei;
		font-size: 16px;
		line-height: 21px;
		text-align: right;
		font-style: normal;
		line-height: 40px;
		right: 0;
		position: absolute;
	}
	::v-deep .el-dropdown-menu {
		background-color: #00173b !important;
		border: 1px solid #2b7bbb !important;
	}
	.el-dropdown-menu__item {
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
		line-height: 40px;
		padding: 0 30px;
		&:hover {
			background: #185493;
			color: rgba(255, 255, 255, 0.8);
		}
	}
}
</style>
