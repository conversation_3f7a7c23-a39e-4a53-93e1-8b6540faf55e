<template>
  <div v-if="value" :class="['address-book', { 'address-book-full': isFull }]">
    <div class="dialog-head">
      <div class="dialog-head-left">
        <div class="invited">视频会商</div>
      </div>
      <div class="full-btns">
        <div class="full-btn" v-show="false"></div>
        <div class="full-btn" @click="closeDialog"></div>
      </div>
    </div>
    <div class="address-book-content">
      <div class="address-book-content-left">
        <div class="content-top">
          <el-input v-model="filterText" placeholder="请输入名称进行搜索">
            <i slot="suffix" class="el-input__icon el-icon-search"></i>
          </el-input>
        </div>
        <div class="area-picker">
          <div>筛选</div>
          <el-cascader
            v-model="pickerValue"
            :options="pickerOptions"
            :props="pickerProps"
            clearable>
          </el-cascader>
        </div>
        <el-tree
          class="tree-content"
          :data="treeData"
          :props="defaultProps"
          :default-expand-all="false"
          :default-expanded-keys="defaultExpandKeys"
          node-key="id"
          highlight-current
          show-checkbox
          :filter-node-method="filterNode"
          ref="tree"
          @check="handleTreeCheck"
        >
          <span class="custom-tree-node" slot-scope="{ node, data }" v-html="handleChosenText(node.label)">
          </span>
        </el-tree>
      </div>
      <div class="address-book-content-right">
        <div :class="['choosing-item', {'choosing-item-active': item.isChecked}]" v-for="(item, index) of userList" :key="'u' + index">
          <div class="choosing-item-top">
            <div class="avatar"></div>
            <div class="name">{{ item.name }}</div>
            <!-- <div class="job">{{ item.job }}</div> -->
          </div>
          <div class="choosing-item-center">
            {{ item.area + '/' + item.address }}
            <!-- {{ item.name }} -->
          </div>
          <div class="choosing-item-bottom">
            <div class="choosing-item-btn btn1" v-show="item.isPC"></div>
            <div class="choosing-item-btn btn3" v-show="!item.isPC"></div>
            <div class="choosing-item-btn btn2" v-show="item.isPhone" @click="launchPointToPoint(item.mobile, 'mobile')"></div>
            <div class="choosing-item-btn btn4" v-show="!item.isPhone"></div>
          </div>
          <div :class="['pc-status', item.isPC ? 'pc-status-online' : 'pc-status-offline']">
            {{ item.isPC ? '在线' : '离线' }}
          </div>
          <div :class="[item.isChecked ? 'chosen-icon' : 'choosing-icon']" @click="handleUserCheck(item)"></div>
        </div>
      </div>
    </div>
    <div class="address-book-chosen">
      <div class="address-book-chosen-group">
        <div class="chosen-mark">已选{{ chosenNum }}人</div>
        <div class="chosen-area-btn left-btn" @click="toggleLeft"></div>
        <div class="chosen-area" id="chosen-area">
          <div class="chosen-list" v-for="(item, index) of chosenList" :key="'c' + index">
            <div class="name">{{ item.name }}</div>
            <div class="close-icon" @click="deleteChosenItem(item, index)"></div>
          </div>
        </div>
        <div class="chosen-area-btn right-btn" @click="toggleRight"></div>
      </div>
      <div class="address-book-chosen-group">
        <div class="address-book-btn" @click="reset">重置</div>
        <div class="address-book-btn" @click="invite" v-if="type === 'invite'">邀请</div>
        <div class="address-book-btn" @click="launch" v-else>发起会议</div>
      </div>
    </div>
  </div>
</template>

<script>
import { getOrgTree } from '@/api/bjnj/zhdd.js'
export default {
  name: 'addressBook',
  props: {
    type: {
      type: String,
      default: 'invite' // invite-邀请 launch-发起
    },
    value: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      // 是否全屏
      isFull: false,
      filterText: '',
      // 树
      treeData: [],
      treeFlatData: [],
      defaultProps: {
        children: 'childList',
        label: 'name'
      },
      defaultExpandKeys: [],
      pickerValue: '',
      pickerOptions: [],
      pickerProps: {
        checkStrictly: true,
        value: 'name',
        label: 'name'
      },
      // 选择区
      userList: [
        // {
        //   id: '1',
        //   name: '夏小明夏小明夏小明夏小明',
        //   job: '普通科员',
        //   address: '北京',
        //   dept: '农业机械化服务中心',
        //   isPc: true,
        //   isPhone: true,
        //   isChecked: false
        // },
      ],
      // 已选区
      chosenList: [],
      tempFirstLevel: false
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
    pickerValue(val) {
      if(val && val.length > 0) {
        this.$refs.tree.filter(val.join("-"));
      } else {
        this.$refs.tree.filter(this.filterText)
      }
    },
    value(newVal) {
      if(newVal) {
        this.getAddressData()
        this.getTreeData()
        this.filterText = ''
        this.reset()
      }
    }
  },
  computed: {
    chosenNum() {
      return this.chosenList.length
    },
    areaId() {
      return this.$store.state.areaId
    }
  },
  methods: {
    // 获取省市区三级行政数据
    getAddressData() {
      fetch('./json/address.json').then(res => {
        // console.log('res', res);
        return res.json()
      }).then(result => {
        // console.log('区域数据', result);
        this.pickerOptions = result
      })
    },
    // 获取组织树
    getTreeData() {
      let params = {
        areaId: this.areaId
      }
      getOrgTree(params).then(res => {
        console.log('组织树数据', res);
        this.treeData = res.data
        this.treeFlat(res.data)
        this.defaultExpandKeys = [this.treeData[0].id]
        console.log('treedata', this.treeData)
      })
    },
    // 高亮选中文本
    handleChosenText(label) {
      return label.replaceAll(this.filterText, `<span style="color: #00AAFF;">${this.filterText}</span>`)
    },
    // 树节点过滤函数
    filterNode(value, data) {
      if (!value) return true;
      let valueArr = value.split('-')
      if(valueArr.length > 1) {
        if(valueArr.length >= 3) {
         if(data.name.indexOf(valueArr[0]) !== -1) {
          this.tempFirstLevel = true
         }
         return data.name.indexOf(valueArr[valueArr.length - 1]) !== -1 && this.tempFirstLevel
        } else {
          return data.name.indexOf(valueArr[valueArr.length - 1]) !== -1
        }
      } else {
        return data.name.indexOf(valueArr[0]) !== -1
      }
    },
    // 重置
    reset() {
      this.chosenList = []
      this.userList.forEach(item => {
        item.isChecked = false
      })
      // this.userList = []
    },
    // 邀请入会
    invite() {
      if(this.chosenList.length <= 0) {
        this.$message({
          type: 'error',
          message: '已选人员不能为空'
        })
      } else {
        this.$emit('inviteToJoin', this.chosenList)
      }
    },
    // 发起会议
    launch() {
      this.$emit('launchMeeting', this.chosenList)
    },
    treeFlat(data) {
      function treeToArray(tree) {
        let res = []
        for (const item of tree) {
          const { childList, ...i } = item
          if(childList && childList.length > 0) {
            res = res.concat(treeToArray(childList))
          }
          res.push(i)
        }
        return res
      }
      this.treeFlatData = treeToArray(data)
      // console.log('treeFlatData', this.treeFlatData)
    },
    getUserArea(parentId) {
      let area = null, belong = null;
      if(this.treeFlatData.length > 0) {
        this.treeFlatData.forEach(item => {
          if(item.id === parentId) {
            area = item?.area
            belong = item?.name
          }
        })
      }
      return {
        area,
        belong
      }
    },
    // 组织树某一项被选择是触发
    handleTreeCheck(checkedNodes) {
      // console.log('当前选择', checkedNodes);
      let arr = this.$refs.tree.getCheckedNodes()
      console.log('当前选择', arr);
      let allCheckedNodes = []
      arr.forEach(item => {
        if(item.deptFlag == 1) {
          allCheckedNodes.push(item)
        }
      })
      console.log('所有选中元素', allCheckedNodes)
      this.userList = allCheckedNodes.map(item => {
        let {area, belong} = this.getUserArea(item.parentId)
        return {
          id: item.id,
          area: area ? area : '暂无',
          name: item.name,
          job: '',
          address: belong ? belong : '暂无',
          dept: '',
          mobile: item.mobile,
          isPC: item.userStatus === 0 ? true : false,
          isPhone: item.mobile ? true : false,
          isChecked: item.isChecked
        }
      })
      // console.log('chosenList', this.chosenList)
      this.userList.forEach(item => {
        if(this.chosenList.length > 0) {
          if(this.chosenList.findIndex(ele => ele.id === item.id) > -1) {
            item.isChecked = true
          }
        }
      })
      console.log('userList', this.userList)
    },
    toggleLeft() {
      let chosenAreaDom = document.getElementById('chosen-area')
      if(chosenAreaDom) {
        if(chosenAreaDom.scrollLeft <= 0) {
          return
        } else if(chosenAreaDom.scrollLeft <= 106) {
          chosenAreaDom.scrollLeft = 0
        } else {
          chosenAreaDom.scrollLeft = chosenAreaDom.scrollLeft - 106
        }
      }
    },
    toggleRight() {
      let chosenAreaDom = document.getElementById('chosen-area')
      if(chosenAreaDom) {
        chosenAreaDom.scrollLeft += 106
      }
    },
    closeDialog() {
      if(this.type === 'launch') {
        this.$bus.emit('LaunchMeetingDialogClose')
      }
      this.$emit('input', false)
    },
    // 处理单独用户是否选中
    handleUserCheck(userItem) {
      userItem.isChecked = !userItem.isChecked;
      if(userItem.isChecked) {
        this.chosenList.push(userItem)
      } else {
        let index = this.chosenList.findIndex(chosenItem => {
          return chosenItem.id == userItem.id
        })
        if(index > -1) {
          this.chosenList.splice(index, 1)
        }
      }
    },
    // 删除已选区选中内容
    deleteChosenItem(chosenItem, idx) {
      if(this.userList.length > 0) {
        let index = this.userList.findIndex(item => {
          return item.id == chosenItem.id
        })
        if(index > -1) {
          this.userList[index].isChecked = false
        }
      }
      this.chosenList.splice(idx, 1)
    },
    launchPointToPoint(mobile, type) {
      if(this.type === 'launch') {
        this.$emit('launchPointToPoint', {
          mobile,
          type
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.address-book {
  width: 1601px;
  height: 713px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 5000;
  background: url('~@/assets/service/address-bg.png') no-repeat center / 100% 100%;
  .dialog-head {
    height: 55px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 40px 0 38px;
    &-left {
      display: flex;
      align-items: center;
      .invited {
        font-family: YouSheBiaoTiHei;
        font-size: 28px;
        color: #ffffff;
        line-height: 36px;
        margin-right: 40px;
      }
    }
    .full-btns {
      display: flex;
      align-items: center;
      .full-btn {
        &:not(:last-child) {
          margin-right: 10px;
        }
        width: 32px;
        height: 32px;
        cursor: pointer;
        &:nth-child(1) {
          background: url('~@/assets/service/full1.png') no-repeat center / 100% 100%;
        }
        &:nth-child(2) {
          background: url('~@/assets/service/close.png') no-repeat center / 100% 100%;
        }
      }
    }
  }
  &-content {
    display: flex;
    width: 100%;
    height: calc(100% - 135px);
    padding: 20px;
    &-left {
      height: 100%;
      width: 336px;
      background: #0a3368;
      border-radius: 5px;
      padding: 20px;
      .content-top {
        width: 100%;
        display: flex;
        justify-content: center;
        margin-bottom: 10px;
        ::v-deep {
          .el-input {
            width: 100% !important;
            .el-input__inner {
              height: 36px;
              line-height: 36px;
              border: 1px solid #cbe5ff !important;
              background-color: transparent !important;
              font-size: 16px !important;
              color: #ffffff !important;
              font-family: Source Han Sans CN;
              font-weight: 400;
            }
            .el-input__icon {
              line-height: 36px;
              color: #00aaff;
            }
          }
        }
      }
      .area-picker {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: nowrap;
        height: 36px;
        margin-bottom: 10px;
        font-size: 16px;
        color: #ffffff;
        font-family: Source Han Sans CN;
        font-weight: 400;
        ::v-deep .el-cascader {
          width: 240px;
          .el-input__inner {
            height: 36px;
            line-height: 36px;
            border: 1px solid #cbe5ff !important;
            background-color: transparent !important;
            font-size: 16px !important;
            color: #ffffff !important;
            font-family: Source Han Sans CN;
            font-weight: 400;
          }
        }
      }
      .tree-content {
        height: calc(100% - 92px);
        overflow: auto;
        // 滚动条样式
        &::-webkit-scrollbar {
          width: 4px;
          height: 4px;
        }
        &::-webkit-scrollbar-track {
          background: #719bebd6;
        }
        &::-webkit-scrollbar-thumb {
          background: #3170ed;
          border-radius: 8px;
        }
      }
      .custom-tree-node {
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #FFFFFF;
        width: 100%;
        padding: 0;
        text-align: left;
        &-label {
          white-space: nowrap;
        }
        &-num {
          font-size: 12px;
          color: #76B2FF;
        }
      }
      ::v-deep {
        .el-tree {
          background: transparent !important;
          .el-tree-node {
            &:focus > .el-tree-node__content {
              background: #1e539a !important;
            }
          }
          .el-tree-node__content {
            position: relative;
            height: 36px;
            &:not(:last-child) {
              margin-bottom: 10px;
            }
            &:hover {
              background: #1E539A !important;
              border-radius: 5px !important;
            }
            .el-tree-node__label {
              font-family: Source Han Sans CN;
              font-weight: 400;
              font-size: 16px;
              color: #FFFFFF;
            }
            .el-checkbox {
              position: absolute;
              margin: 0;
              font-size: 17px;
              top: 6px;
              right: 12px;
              .el-checkbox__inner {
                border: 1px solid #CBE5FF;
                background: transparent;
              }
              .is-checked {
                .el-checkbox__inner {
                  background-color: #409eff !important;
                }
              }
            }
          }
        }
        .el-tree--highlight-current {
          .el-tree-node.is-current>.el-tree-node__content {
            background-color: #1E539A !important;
          }
        }
      }
    }
    &-right {
      width: calc(100% - 336px);
      height: 100%;
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
      overflow-y: auto;
      padding-left: 20px;
      -ms-overflow-style: none;
      scrollbar-width: none;
      &::-webkit-scrollbar {
        display: none; /* Chrome Safari */
      }
      .choosing-item {
        width: 300px;
        height: 146px;
        background: url('~@/assets/service/choose-item-bg.png') no-repeat center / 100% 100%;
        position: relative;
        padding-left: 20px;
        margin-bottom: 20px;
        flex-shrink: 0;
        &-top {
          position: absolute;
          top: 5px;
          left: 20px;
          display: flex;
          align-items: center;
          width: 75%;
          .avatar {
            width: 32px;
            height: 32px;
            background: url('~@/assets/service/avatar.png') no-repeat center / 100% 100%;
            margin-right: 12px;
            flex-shrink: 0;
          }
          .name {
            font-family: YouSheBiaoTiHei;
            font-size: 22px;
            color: #FFFFFF;
            line-height: 24px;
            text-align: left;
            margin-right: 12px;
          }
          .job {
            font-family: Source Han Sans CN;
            font-weight: 400;
            font-size: 12px;
            color: #76B2FF;
            line-height: 28px;
          }
        }
        &-center {
          margin-top: 70px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 16px;
          color: #FFFFFF;
          text-align: left;
          max-width: 95%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-bottom: 16px;
        }
        &-bottom {
          width: 100%;
          display: flex;
          align-items: center;
          .choosing-item-btn {
            width: 28px;
            height: 28px;
            &:not(:last-child) {
              margin-right: 10px;
            }
          }
          .btn1 {
            background: url('~@/assets/service/isPC.png') no-repeat center / 100% 100%;
          }
          .btn2 {
            background: url('~@/assets/service/isPhone.png') no-repeat center / 100% 100%;
          }
          .btn3 {
            background: url('~@/assets/service/isPC1.png') no-repeat center / 100% 100%;
          }
          .btn4 {
            background: url('~@/assets/service/isPhone1.png') no-repeat center / 100% 100%;
          }
        }
        .choosing-icon {
          position: absolute;
          top: 11px;
          right: 41px;
          width: 17px;
          height: 17px;
          border-radius: 2px;
          border: 1px solid #CBE5FF;
          cursor: pointer;
        }
        .pc-status {
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 16px;
          position: absolute;
          right: 36px;
          bottom: 10px;
          white-space: nowrap;
          &-online {
            color: #03fc17;
          }
          &-offline {
            color: rgba(198, 207, 199, 0.5);
          }
        }
        .chosen-icon {
          cursor: pointer;
          position: absolute;
          top: 11px;
          right: 41px;
          width: 17px;
          height: 17px;
          background: url('~@/assets/service/chosen.png') no-repeat center / 100% 100%;
        }
      }
    }
  }
  &-chosen {
    height: 70px;
    padding: 0 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    &-group {
      display: flex;
      align-items: center;
      .chosen-mark {
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #ffffff;
        line-height: 24px;
        margin-right: 32px;
      }
      .chosen-area-btn {
        width: 27px;
        height: 36px;
      }
      .left-btn {
        margin-right: 10px;
        background: url('~@/assets/service/left-btn.png') no-repeat center / 100% 100%;
      }
      .right-btn {
        margin-left: 10px;
        background: url('~@/assets/service/right-btn.png') no-repeat center / 100% 100%;
      }
      .chosen-area {
        width: 944px;
        overflow-x: auto;
        display: flex;
        flex-wrap: nowrap;
        -ms-overflow-style: none;
        scrollbar-width: none;
        &::-webkit-scrollbar {
          display: none; /* Chrome Safari */
        }
        .chosen-list {
          width: 96px;
          height: 38px;
          background: url('~@/assets/service/chosen-item-bg.png') no-repeat center / 100% 100%;
          flex-shrink: 0;
          &:not(:last-child) {
            margin-right: 10px;
          }
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 16px;
          color: #FFFFFF;
          line-height: 24px;
          display: flex;
          align-items: center;
          position: relative;
          .name {
            max-width: 58px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .close-icon {
            position: absolute;
            right: 11px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            width: 16px;
            height: 16px;
            background: url('~@/assets/service/chosen-close.png') no-repeat center / 100% 100%;
          }
        }
      }
      .address-book-btn {
        width: 108px;
        height: 31px;
        text-align: center;
        line-height: 31px;
        background: url('~@/assets/service/address-btn-bg.png') no-repeat center / 100% 100%;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #ffffff;
        cursor: pointer;
        &:not(:last-child) {
          margin-right: 12px;
        }
      }
    }
  }
}
</style>
