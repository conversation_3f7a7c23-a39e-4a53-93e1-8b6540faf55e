<template>
	<div class="child-block">
		<slot></slot>

		<div class="content-box">
			<el-tree
				v-if="treeData.length > 0"
				class="tree-content"
				:style="{
					height: '100%',
				}"
				:data="treeData"
				:props="defaultProps"
				:default-expand-all="false"
				:default-expanded-keys="defaultExpandKeys"
				node-key="id"
				highlight-current
				:show-checkbox="false"
				:filter-node-method="filterNode"
				ref="tree"
				@node-click="handleNodeClick"
			>
				<span class="custom-tree-node" slot-scope="{ node, data }">
					<template v-if="data.deptFlag == 2">
						<span class="custom-tree-node-label" :title="node.label">{{ node.label }}</span>
					</template>
					<span class="custom-tree-node-user" v-else>
						<span :class="['custom-tree-node-icon', data.userStatus === 0 ? 'icon-online' : 'icon-offline']"></span>
						<span class="custom-tree-node-label" :title="node.label">{{ node.label }}</span>
						<!-- <span :class="['custom-tree-node-status', data.userStatus === 0 ? 'status-online' : 'status-offline']">{{
											data.userStatus === 0 ? '在线' : '离线'
										}}</span>
										<span
											:class="['custom-tree-node-status-pc', data.userStatus === 0 ? 'status-pc-online' : 'status-pc-offline']"
											@click.stop="launchVideoMeetingPC(data)"
										></span>
										<span
											:class="['custom-tree-node-status-mobile', data.mobile ? 'status-mobile-online' : 'status-mobile-offline']"
											@click="launchPointToPoint(data.mobile, 'mobile')"
										></span> -->
					</span>
				</span>
			</el-tree>
			<div v-else class="empty-box">
				<img class="zwsjImg" src="@/assets/bjnj/zwsj.png" />
			</div>
		</div>
		<div class="btns-box">
			<div class="btn-item-box" v-for="(item, index) in btns" @click="() => item.func(item)">
				<i class="icon-box" :class="item.iconClass"></i>
				{{ item.label }}
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		treeData: {
			type: Array,
			default: () => {},
		},
		btns: {
			type: Array,
			default: () => {},
		},
	},
	data() {
		return {
			defaultProps: {
				children: 'childList',
				label: 'name',
			},
			defaultExpandKeys: [],
			tempFirstLevel: false,
		}
	},
	computed: {},
	methods: {
		handleNodeClick(data, node) {
			console.log('node1111', node)
		},
		// 树节点过滤函数
		filterNode(value, data) {
			if (!value) return true
			let valueArr = value.split('-')
			if (valueArr.length > 1) {
				if (valueArr.length >= 3) {
					if (data.name.indexOf(valueArr[0]) !== -1) {
						this.tempFirstLevel = true
					}
					return data.name.indexOf(valueArr[valueArr.length - 1]) !== -1 && this.tempFirstLevel
				} else {
					return data.name.indexOf(valueArr[valueArr.length - 1]) !== -1
				}
			} else {
				return data.name.indexOf(valueArr[0]) !== -1
			}
		},
	},

	watch: {
		treeData: {
			handler(val) {
				if (val.length > 0) this.defaultExpandKeys = [val[0].id]
			},
		},
	},
}
</script>

<style lang="less" scoped>
.child-block {
	width: 451px;
	height: 100%;
	box-sizing: border-box;
	z-index: 101;
	position: relative;

	&::-webkit-scrollbar {
		width: 4px; /* 滚动条宽度 */
	}

	//轨道
	&::-webkit-scrollbar-track {
		background: #04244e; /* 滚动条轨道背景 */
		border-radius: 10px;
		padding: 5px 0;
	}

	//滑块
	&::-webkit-scrollbar-thumb {
		background: #82aed0; /* 滑块背景颜色 */
		border-radius: 10px;
		height: 15px;
	}

	//悬停滑块颜色
	&::-webkit-scrollbar-thumb:hover {
		background: #ff823b; /* 鼠标悬停时滑块颜色 */
	}

	//箭头
	&::-webkit-scrollbar-button {
		display: none; /* 隐藏滚动条的箭头按钮 */
	}

	.content-box {
		width: 100%;
		height: calc(100% - 70px);
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
		overflow: auto;

		padding-left: 0 2px;
		padding-left: 20px;

		&::-webkit-scrollbar {
			width: 4px; /* 滚动条宽度 */
		}

		//轨道
		&::-webkit-scrollbar-track {
			background: #04244e; /* 滚动条轨道背景 */
			border-radius: 10px;
			padding: 5px 0;
		}

		//滑块
		&::-webkit-scrollbar-thumb {
			background: #82aed0; /* 滑块背景颜色 */
			border-radius: 10px;
			height: 15px;
		}

		//悬停滑块颜色
		&::-webkit-scrollbar-thumb:hover {
			background: #ff823b; /* 鼠标悬停时滑块颜色 */
		}

		//箭头
		&::-webkit-scrollbar-button {
			display: none; /* 隐藏滚动条的箭头按钮 */
		}
		.tree-content {
			width: 100%;
			// transition: height 0.2s ease;
			padding-top: 10px;
			// margin-bottom: 20px;
			overflow: auto;
			&::-webkit-scrollbar {
				width: 4px; /* 滚动条宽度 */
			}

			//轨道
			&::-webkit-scrollbar-track {
				background: #04244e; /* 滚动条轨道背景 */
				border-radius: 10px;
				padding: 5px 0;
			}

			//滑块
			&::-webkit-scrollbar-thumb {
				background: #82aed0; /* 滑块背景颜色 */
				border-radius: 10px;
				height: 15px;
			}

			//悬停滑块颜色
			&::-webkit-scrollbar-thumb:hover {
				background: #ff823b; /* 鼠标悬停时滑块颜色 */
			}

			//箭头
			&::-webkit-scrollbar-button {
				display: none; /* 隐藏滚动条的箭头按钮 */
			}

			&.no-active-content {
				height: 0;
				display: none;
			}
		}

		.empty-box {
			width: 100%;
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			.zwsjImg {
				width: 180px;
				margin: 20px 0;
			}
		}

		.custom-tree-node {
			font-family: Source Han Sans CN;
			font-weight: 400;
			font-size: 16px;
			color: #ffffff;
			width: 100%;
			padding-left: 12px;
			text-align: left;
			&-label {
				display: inline-block;
				width: fit-content;
				max-width: 200px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
			&-num {
				font-size: 12px;
				color: #76b2ff;
			}
			&-user {
				display: flex;
				justify-content: flex-start;
				align-items: center;
				.custom-tree-node-icon {
					flex-shrink: 0;
					display: inline-block;
					width: 24px;
					height: 24px;
					margin-right: 8px;
				}
				.custom-tree-node-label {
					max-width: 70px;
				}
				.icon-online {
					background: url('~@/assets/service/ava_on.png') no-repeat center / 100% 100%;
				}
				.icon-offline {
					background: url('~@/assets/service/ava_off.png') no-repeat center / 100% 100%;
				}
				.custom-tree-node-status {
					flex-shrink: 0;
					display: inline-block;
					font-family: Source Han Sans CN;
					font-weight: 400;
					font-size: 10px;
					width: 38px;
					height: 16px;
					line-height: 16px;
					text-align: center;
					margin: 0 7px 0 9px;
					&-pc {
						flex-shrink: 0;
						display: inline-block;
						width: 14px;
						height: 14px;
						margin-right: 8px;
					}
					&-mobile {
						flex-shrink: 0;
						display: inline-block;
						width: 14px;
						height: 14px;
					}
				}
				.status-online {
					color: #38f539;
					background: url('~@/assets/service/status_on.png') no-repeat center / 100% 100%;
				}
				.status-offline {
					color: #c6c6c6;
					background: url('~@/assets/service/status_off.png') no-repeat center / 100% 100%;
				}
				.status-pc-online {
					background: url('~@/assets/service/isPC.png') no-repeat center / 100% 100%;
				}
				.status-pc-offline {
					background: url('~@/assets/service/isPC1.png') no-repeat center / 100% 100%;
				}
				.status-mobile-online {
					background: url('~@/assets/service/isPhone.png') no-repeat center / 100% 100%;
				}
				.status-mobile-offline {
					background: url('~@/assets/service/isPhone1.png') no-repeat center / 100% 100%;
				}
			}
		}
		::v-deep {
			.el-tree {
				background: transparent !important;
				.el-tree-node {
					&:focus > .el-tree-node__content {
						background: #1e539a !important;
					}
				}
				.el-tree-node__content {
					position: relative;
					height: 36px;
					.is-leaf {
						opacity: 0;
					}
					&:not(:last-child) {
						margin-bottom: 10px;
					}
					&:hover {
						background: #1e539a !important;
						border-radius: 5px !important;
					}
					.el-tree-node__label {
						font-family: Source Han Sans CN;
						font-weight: 400;
						font-size: 16px;
						color: #ffffff;
					}
					.el-checkbox {
						position: absolute;
						margin: 0;
						font-size: 17px;
						top: 6px;
						right: 12px;
						.el-checkbox__inner {
							border: 1px solid #cbe5ff;
							background: transparent;
						}
						.is-checked {
							.el-checkbox__inner {
								background-color: #409eff !important;
							}
						}
					}
					.el-tree-node__expand-icon {
						width: 17px;
						height: 17px;
						background: url('~@/assets/service/tree_expand.png') no-repeat center / 100% 100%;
						&::before {
							content: '';
						}
					}
					.expanded {
						transform: rotate(0deg) !important;
						background: url('~@/assets/service/tree_slide.png') no-repeat center / 100% 100% !important;
					}
				}
			}
			.el-tree--highlight-current {
				.el-tree-node.is-current > .el-tree-node__content {
					background-color: #1e539a !important;
				}
			}
		}
	}

	.btns-box {
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 20px;
		margin-top: 10px;
		.btn-item-box {
			background-color: #04244e;
			border-radius: 4px 4px 4px 4px;
			border: 1px solid #159aff;
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 8px 16px;

			font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
			font-weight: normal;
			font-size: 14px;
			color: #159aff;
			text-align: left;
			font-style: normal;
			text-transform: none;
			cursor: pointer;

			.icon-box {
				font-size: 16px;
				margin-right: 4px;
				color: #159aff;
			}

			&:hover {
				color: #fff;
				background: #159aff;
				.icon-box {
					color: #fff;
				}
			}
		}
	}
}

.popDropDown {
	::v-deep .el-dropdown {
		color: #caecff;
		font-family: YouSheBiaoTiHei;
		font-size: 16px;
		line-height: 21px;
		text-align: right;
		font-style: normal;
		line-height: 40px;
		right: 0;
		position: absolute;
	}
	::v-deep .el-dropdown-menu {
		background-color: #00173b !important;
		border: 1px solid #2b7bbb !important;
	}
	.el-dropdown-menu__item {
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
		line-height: 40px;
		padding: 0 30px;
		&:hover {
			background: #185493;
			color: rgba(255, 255, 255, 0.8);
		}
	}
}
</style>
