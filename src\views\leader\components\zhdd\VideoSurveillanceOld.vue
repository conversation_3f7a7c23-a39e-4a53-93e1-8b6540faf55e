<template>
  <div class="wrap" :style="{ padding }">
    <ul>
      <li v-for="(it, i) of videos" :key="i">
        <LivePlayer :videoUrl="it" fluent autoplay live stretch></LivePlayer>
      </li>
    </ul>
  </div>
</template>

<script>
import LivePlayer from '@liveqing/liveplayer'
export default {
  name: 'ZhddVideoSurveillance',
  props: {
    padding: {
      type: String,
      default: '23px 22px 18px'
    }
  },
  components: { LivePlayer },
  data() {
    return {
      videos: [
        require('@/assets/leader/video/video1.mp4'),
        require('@/assets/leader/video/video2.mp4'),
        require('@/assets/leader/video/video4.mp4'),
        require('@/assets/leader/video/video8.mp4')
      ]
    }
  }
}
</script>

<style lang="less" scoped>
.wrap {
  width: 100%;
  height: 100%;
  ul {
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-content: space-between;
    li {
      width: 200px;
      height: 113px;
    }
  }
}
/* 视频控件菜单栏 */
:deep(.video-js) {
  .vjs-control {
    width: 2em;
  }
  .vjs-tech {
    object-fit: fill !important;
  }
}
</style>
