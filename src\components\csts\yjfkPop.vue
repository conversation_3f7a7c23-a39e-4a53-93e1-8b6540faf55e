<template>
  <div class="ai_waring">
    <div class="title">
      <span>应急防控更多</span>
    </div>
    <img @click="closeEmit" class="close_btn" src="@/assets/shzl/map/close_btn2.png" alt="" />
    <div class="content">
      <div class="left">
        <BlockBox
          title="应急队伍"
          class="box box1"
          :isListBtns="false"
          :blockHeight="367"
        >
          <div class="box1Item">
            <img src="@/assets/csts/yjdwImg1.png" alt="">
            <div class="box1Right">
              <div class="box1Name">121名</div>
              <div class="box1Num">安全生产专家</div>
            </div>
          </div>
          <div class="box1Item">
            <img src="@/assets/csts/yjdwImg2.png" alt="">
            <div class="box1Right">
              <div class="box1Name">121名</div>
              <div class="box1Num">消防专家</div>
            </div>
          </div>
          <div class="box1Item">
            <img src="@/assets/csts/yjdwImg3.png" alt="">
            <div class="box1Right">
              <div class="box1Name">121名</div>
              <div class="box1Num">消防人员</div>
            </div>
          </div>
          <div class="box1Item">
            <img src="@/assets/csts/yjdwImg4.png" alt="">
            <div class="box1Right">
              <div class="box1Name">121辆</div>
              <div class="box1Num">消防车辆</div>
            </div>
          </div>
          <div class="box1Item">
            <img src="@/assets/csts/yjdwImg5.png" alt="">
            <div class="box1Right">
              <div class="box1Name">121支</div>
              <div class="box1Num">消防支队</div>
            </div>
          </div>
          <div class="box1Item">
            <img src="@/assets/csts/yjdwImg6.png" alt="">
            <div class="box1Right">
              <div class="box1Name">121人</div>
              <div class="box1Num">护林队伍</div>
            </div>
          </div>
          <div class="box1Item">
            <img src="@/assets/csts/yjdwImg7.png" alt="">
            <div class="box1Right">
              <div class="box1Name">121支</div>
              <div class="box1Num">公共服务大队防汛抢险队伍</div>
            </div>
          </div>
          <div class="box1Item">
            <img src="@/assets/csts/yjdwImg8.png" alt="">
            <div class="box1Right">
              <div class="box1Name">121人</div>
              <div class="box1Num">民兵防汛抢险队伍</div>
            </div>
          </div>
        </BlockBox>
        <BlockBox
          title="事故灾害起因统计"
          class="box box2"
          :isListBtns="false"
          :blockHeight="337"
        >
          <div class="box2Content">
            <pieCharts :data="data1" />
          </div>
        </BlockBox>
      </div>
      <div class="middle">
        <BlockBox
          title="消防成果"
          class="box box3"
          :isListBtns="false"
          :blockHeight="367"
        >
          <div class="box3List">
            <div class="box3Item">
              <div class="box3Num">123<span>次</span></div>
              <div class="box3Name">年度出勤</div>
            </div>
            <div class="box3Item">
              <div class="box3Num">2011<span>次</span></div>
              <div class="box3Name">成功救援</div>
            </div>
            <div class="box3Item">
              <div class="box3Num">1972<span>起</span></div>
              <div class="box3Name">扑灭火灾</div>
            </div>
            <div class="box3Item">
              <div class="box3Num">123<span>处</span></div>
              <div class="box3Name">隐患整改</div>
            </div>
            <div class="box3Item">
              <div class="box3Num">2011<span>起</span></div>
              <div class="box3Name">处理投诉</div>
            </div>
            <div class="box3Item">
              <div class="box3Num">1972<span>万元</span></div>
              <div class="box3Name">避免损失</div>
            </div>
          </div>
        </BlockBox>
        <BlockBox
          title="森林火灾起因统计"
          class="box box4"
          :isListBtns="false"
          :blockHeight="337"
        >
          <div class="box4Content">
            <PieChart3D type="2" :data="data10_2.data" :options="data10_2.options" />
          </div>
        </BlockBox>
      </div>
      <div class="right">
        <BlockBox
          title="安全生产"
          class="box box5"
          :isListBtns="false"
          :blockHeight="367"
        >
          <GroupColChart :data="qyeqData" :options="qyeqOptions"></GroupColChart>
        </BlockBox>
        <BlockBox
          title="警示案例"
          class="box box6"
          :isListBtns="false"
          :blockHeight="337"
        >
          <div class="center">
            <SwiperTable
              :titles="['事件名称', '时间', '伤亡人数', '事故原因']"
              :widths="['40%', '20%', '20%', '20%']"
              :data="zdxmData"
              :contentHeight="'220px'"
              :tabelHeight="'44px'"
            ></SwiperTable>
          </div>
        </BlockBox>
      </div>
    </div>
  </div>
</template>

<script>
import BlockBox from '@/components/leader/common/blockBox2.vue'
import pieCharts from '@/components/csts/pieChart-zwfw.vue'
import SwiperTable from '@/components/leader/leader_zwjs/SwiperTable.vue'
export default {
  name:'yjfkPop',
  data() {
    return {
      data1: [
        ['product', '事件总数'],
        ['违规用电', 1234],
        ['线路老化', 1234],
        ['操作不当', 1234],
      ],
      data10_2: {
        data: [
          ['product', '事件总数'],
          ['农事用火', 1245],
          ['烧隔离带', 1245],
          ['野外吸烟', 1245],
          ['炼山造林', 1245],
          ['施工作业', 1245],
        ],
        options: {
          colors: ['#2EF6FF', '#5AE29D', '#E6F973', '#E6F973', '#6D5AE2'],
          legend: {
            top: 15
          },
          bgImg: {
            width: '70%',
            height: '70%',
            top: '51%',
            left: '50%'
          },
          unit: '件',
          title: {
            fontSize: '16px',
            top: 70,
          },
          subtitle: {
            fontSize: '14px',
            top: 90,
          },
        }
      },
      qyeqData: [
        ['product', '重大安全事故', '中等安全事故', '一般安全事故'],
        ['浦口', 200, 160, 140],
        ['秦淮', 140, 90, 180],
        ['建邺', 102, 105, 120],
        ['江宁', 200, 90, 130],
        ['雨花', 150, 200, 150]
      ],
      qyeqOptions: {
        color: ['#01FF6C', '#8300D7', '#FF9201'],
        stack: true,
        isGradient: true,
        barMaxWidth: 10,
        gradientColors: [
          ['#01FF6C', '#00ED8B'],
          ['#8300D7', '#7800ED'],
          ['#FF9201', '#EDB000']
        ],
        isHor: false,
        barGap: '30%',
        tooltip: {
          color: '#fff',
          show: true,
          type: 'shadow'
        }
      },
      zdxmData: [
        ['栖霞区地铁1号北延吉祥庵站“2….', '1975-01-15', '61', '违规冒险作业'],
        ['栖霞区地铁1号北延吉祥庵站“2….', '2011-01-27', '94', '违规冒险作业'],
        ['栖霞区地铁1号北延吉祥庵站“2….', '1998-10-22', '62', '违规冒险作业'],
        ['栖霞区地铁1号北延吉祥庵站“2….', '2021-05-31', '71', '违规冒险作业'],
        ['栖霞区地铁1号北延吉祥庵站“2….', '1998-10-22', '62', '违规冒险作业']
      ],
    }
  },
  components: {
    BlockBox,
    pieCharts,
    SwiperTable,
  },
  methods: {
    closeEmit() {
      this.$emit('closeEmit')
    },
  },
}
</script>

<style lang='less' scoped>
@keyframes rotateS {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes rotateN {
  0% {
    transform: rotate(360deg);
  }
  100% {
    transform: rotate(0);
  }
}
@keyframes rotateY {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotateY(360deg);
  }
}
.ai_waring {
  width: 1885px;
  height: 921px;
  padding: 22px;
  // background: rgba(0,23,59,0.95);
  background: url(~@/assets/csts/xztk.png) no-repeat;
  background-size: 100% 100%;
  box-shadow: 0px 0px 22px 0px rgba(11,54,138,0.35), 0px 2px 8px 0px rgba(0,0,0,0.7), inset 0px 0px 8px 0px rgba(17,146,214,0.35);
  border-radius: 11px;
  border: 1px solid;
  border-image: linear-gradient(360deg, rgba(57, 133, 255, 0.5), rgba(0, 205, 255, 0)) 1 1;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
  .title {
    background: url(~@/assets/shzl/map/title_bg2.png) no-repeat center / 100% 100%;
    background-size: 100% 100%;
    width: 914px;
    height: 74px;
    margin: 0 auto;
    line-height: 74px;
    text-align: center;
    position: relative;
    span {
      font-size: 40px;
      font-family: YouSheBiaoTiHei;
      color: #FFFFFF;
      line-height: 52px;
      background: linear-gradient(180deg, #FFFFFF 0%, #2AEBFF 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      display: inline-block;
      margin-top: 11px;
    }
  }
  .close_btn {
    position: absolute;
    top: 38px;
    right: 43px;
  }
  .content {
    width: 1841px;
    height: 765px;
    .contentTitle {
      font-size: 18px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #ffffff;
      line-height: 25px;
      text-align: left;
      margin-top: 15px;
      span {
        float: left;
        width: 6px;
        height: 20px;
        background: #02B2CD;
        border-radius: 2px;
        margin-top: 3px;
        margin-left: 10px;
        margin-right: 8px;
      }
    }
    .left {
      width: 533px;
      float: left;
      margin-left: 67px;
      padding-top: 43px;
      .box1 {
        .box1Item {
          width: 266px;
          float: left;
          img {
            float: left;
            margin-top: 10px;
            margin-left: 37px;
          }
          .box1Right {
            float: left;
            margin-left: 14px;
            text-align: left;
            .box1Name {
              font-size: 20px;
              font-family: DINCond-Black, DINCond;
              font-weight: 900;
              color: #FFFFFF;
              line-height: 24px;
              margin-top: 16px;
            }
            .box1Num {
              width: 101px;
              height: 25px;
              background: url(~@/assets/csts/yjdwBg.png) no-repeat;
              background-size: 90% 100%;
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 25px;
              margin-top: -1px;
              padding-left: 11px;
            }
          }
        }
      }
      .box2 {
        .box2Content {
          width: 533px;
          height: 287px;
        }
      }
    }
    .middle {
      width: 533px;
      float: left;
      margin-left: 54px;
      padding-top: 43px;
      .box3 {
        .box3List{
          padding: 0 32px;
          .box3Item {
            width: 132px;
            height: 107px;
            float: left;
            margin: 36px 12px 0 12px;
            .box3Num {
              font-size: 26px;
              font-family: DINAlternate-Bold, DINAlternate;
              font-weight: bold;
              color: #FFFFFF;
              line-height: 30px;
              margin-top: 42px;
              span {
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #ffffff;
                line-height: 20px;
                margin-left: 7px;
              }
            }
            .box3Name {
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
              margin-top: 3px;
            }
            &:nth-of-type(1) {
              background: url(~@/assets/csts/xfcg1.png) no-repeat;
              background-size: 90% 100%;
            }
            &:nth-of-type(2) {
              background: url(~@/assets/csts/xfcg2.png) no-repeat;
              background-size: 90% 100%;
            }
            &:nth-of-type(3) {
              background: url(~@/assets/csts/xfcg3.png) no-repeat;
              background-size: 90% 100%;
            }
            &:nth-of-type(4) {
              background: url(~@/assets/csts/xfcg4.png) no-repeat;
              background-size: 90% 100%;
            }
            &:nth-of-type(5) {
              background: url(~@/assets/csts/xfcg5.png) no-repeat;
              background-size: 90% 100%;
            }
            &:nth-of-type(6) {
              background: url(~@/assets/csts/xfcg6.png) no-repeat;
              background-size: 90% 100%;
            }
          }
        }
      }
      .box4 {
        .box4Content {
          height: 247px;
        }
      }
    }
    .right {
      width: 533px;
      float: left;
      margin-left: 70px;
      padding-top: 43px;
      .box5 {
      }
      .box6 {
        .center {
          height: 287px;
          padding: 27px 0 0 4px;
        }
      }
    }
  }
}
</style>