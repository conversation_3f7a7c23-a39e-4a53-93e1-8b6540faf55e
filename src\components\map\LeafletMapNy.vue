<template>
  <div>
    <div ref="smLeaMap" class="mapContainer" />
  </div>
</template>

<script>
// import L from 'leaflet'
import nj from './hsJd.json'
export default {
  components: {},
  data() {
    return {
      map: null,
      mapOptions: {
        // 中心点
        center: [31.89056396484375, 118.85833740234375],
        // 当前显示层级
        zoom: 14,
        // 最小显示层级
        minZoom: 8,
        maxZoom: 17
      },
      rootLayerGroup: null,
      layerList: [],
      tdtKey: '1d109683f4d84198e37a38c442d68311',
      bounds: [118.60608, 31.884088, 119.0215, 32.123988],
      dPolyline: null,
      dPolygon: null,
       mapUrl: 'http://t1.tianditu.com/vec_c/wmts?layer=vec&style=default&tilematrixset=c&Service=WMTS&Request=GetTile&Version=1.0.0&Format=tiles&TileMatrix={z}&TileCol={x}&TileRow={y}&tk=',
      mapUrl1: 'http://t0.tianditu.com/img_c/wmts?layer=img&style=default&tilematrixset=c&Service=WMTS&Request=GetTile&Version=1.0.0&Format=tiles&TileMatrix={z}&TileCol={x}&TileRow={y}&tk='
    }
  },
  mounted() {
    this.initMap()
  },
  methods: {
    /**
     * 初始化地图
     */
    initMap() {
      this.map = L.map(this.$refs.smLeaMap, {
        crs: L.CRS.EPSG4326,
        center: this.mapOptions.center,
        zoom: this.mapOptions.zoom,
        // minZoom: this.mapOptions.minZoom,
        maxZoom: this.mapOptions.maxZoom,
        // 不添加属性说明控件
        attributionControl: false,
        zoomControl: false,
        editable: true,
        doubleClickZoom: false
      })

      // 天地图
      L.tileLayer(
        this.mapUrl1 +
          this.tdtKey,
        {
          // maxZoom: 17,
          tileSize: 256,
          zoomOffset: 1
          // minZoom: 3
        }
      ).addTo(this.map)

      // 添加根图层，方便管理其他图层
      this.rootLayerGroup = this.group ? this.group : L.featureGroup().addTo(this.map)

      this.onMapClick()
      // this.onMapZoomend();

      this.$nextTick(() => {
        // 加载南京区域
        this.loadGeoJsonLayer(nj, 'nanjing')
        // 随机生成1000个点
        // this.randomPoint('people', 10, require('@/assets/img/map/sjts_map.png'))
      })
    },
    /**
     * 地图点击
     */
    onMapClick() {
      this.map.on('click', e => {
        console.log('点击信息：', e)
        // L.popup()
        //   .setLatLng(e.latlng)
        //   .setContent('点击坐标： ' + e.latlng.toString())
        //   .openOn(this.map);
      })
    },
    /**
     * 地图缩放
     */
    onMapZoomend() {
      this.map.on('zoomend', () => {
        var zoomLevel = this.map.getZoom()

        console.log('zoomLevel', zoomLevel)
        console.log('Bounds', this.map.getBounds())
      })
    },
    /**
     * 绘制poi点
     * @param {poi点数据 数组类型} data
     * @param {图层id} layerId
     * @param {是否聚合} cluster
     * @param {是否跳转第一个点} flyFirstPoint
     */
    drawPoiMarker(data, layerId, cluster = false, flyFirstPoint = false, iconShow = false) {
      this.removeLayer(layerId)
      // 设置poi点图层
      let markerLayer
      if (cluster) {
        markerLayer = L.markerClusterGroup()
      } else {
        markerLayer = L.featureGroup()
      }

      // 保存图层方便根据id删除
      this.layerList.push({
        id: layerId,
        layer: markerLayer
      })
      // 处理点数据
      const markers = data
        .filter(it => {
          if (it == null) {
            return false
          }
          return true
        })
        .map(it => {
          // 初始化点 坐标、图标
          if(!iconShow){
              const marker = L.marker(it.latlng, {
                icon: L.icon(it.icon)
              })
               // 绑定点击事件
                marker.on('click', () => {
                  if (it.props) {
                    // 可根据传入的props属性去判断点击的是哪类点
                    this.$emit('poiClick', layerId, it)
                  }
                })
                return marker
          }else{
              // 创建divIcon来作为标记图标，同时添加图片和文字标签
              const divIcon = L.divIcon({
                  className: 'custom-div-icon',
                  html: `
                    <div class="marker-icon" style="background-image: url('${it.icon.iconUrl}')"><div>${it.props.name}</div></div>
                  `,
                  iconSize: [178, 69], // 设置图标尺寸
                  iconAnchor: [89, 89], // 设置图标的锚点，使得图标正好在点的位置上
                });

                  // 创建标记
                  const marker = L.marker(it.latlng, { icon: divIcon });

               // 绑定点击事件
                marker.on('click', () => {
                  if (it.props) {
                    // 可根据传入的props属性去判断点击的是哪类点
                    this.$emit('poiClick', layerId, it)
                  }
                })
                return marker
          }

         
        })
      // 点图层添加点
      markers.forEach(p => {
        markerLayer.addLayer(p)
      })
      // 将markerLayer添加到根图层
      this.rootLayerGroup.addLayer(markerLayer)
      // 视角跳转第一个点
      if (flyFirstPoint) {
        this.flyToPoint(markers[0].getLatLng(), 16)
      }
    },
    /**
     * 根据图层id移除图层
     * @param {*} layerId
     */
    removeLayer(layerId) {
      const layer = this.layerList.find(e => e.id == layerId)
      if (layer) {
        this.rootLayerGroup.removeLayer(layer.layer)
        this.layerList = this.layerList.filter(e => e.id != layerId)
      }
    },
    /**
     * 移除所有图层
     */
    clearLayers() {
      this.rootLayerGroup.clearLayers()
      this.layerList = []
      if (this.trackLayer) {
        this.trackLayer.clearLayers()
      }
    },
    /**
     * @description: 生成geojson
     * @param {*} coordinates
     * @param {*} properties
     * @return {*}
     */
    setGeojson(coordinates, properties) {
      // coordinates = coordinates.map(e => {
      //   console.log('saas', e);
      // });
      var geojson = {
        type: 'FeatureCollection',
        features: [
          {
            type: 'Feature',
            geometry: {
              type: 'MultiPolygon',
              coordinates: coordinates
            },
            properties: properties
          }
        ]
      }
      // console.log('geojson', geojson);
      return geojson
    },
    /**
     * @description: 加载geojson数据
     * @param {*} data
     * @param {*} layerId
     * @return {*}
     */
    loadGeoJsonLayer(data, layerId) {
      this.removeLayer(layerId)
      const geojsonLayer = L.geoJSON(data, {
        style: feature => {
          return { color: '#007FFF' }
        },
        pane: 'overlayPane',
        onEachFeature: (feature, layer) => {
          layer.on({
            click: () => {
              // alert(feature.properties.name)
              this.$emit('gridClick', feature.properties)
            }
          })

          // layer
          //   .bindTooltip(`${feature.properties.name}`, { permanent: true, direction: 'center' })
          //   .openTooltip()
          layer
            .bindTooltip(`${feature.properties.name}`, {
              permanent: true,
              direction: 'center',
              className: 'custom-tooltip' 
            })
            .openTooltip()
        }
      }).addTo(this.map)
      this.map.fitBounds(geojsonLayer.getBounds())
      this.bounds = this.getBBox(geojsonLayer.getBounds())
      this.layerList.push({
        id: layerId,
        layer: geojsonLayer
      })
      this.rootLayerGroup.addLayer(geojsonLayer)
    },
    /**
     * 定位点
     * @param {坐标点} position
     * @param {层级} zoom
     */
    flyToPoint(position, zoom) {
      this.map.flyTo(position, zoom)
    },
    /**
     * @description: 获取边界坐标
     * @return {*}
     */
    getBBox(bounds) {
      return [
        bounds._southWest.lng,
        bounds._southWest.lat,
        bounds._northEast.lng,
        bounds._northEast.lat
      ]
    },
    /**
     * @description: 生成随机点
     * @param {*} layerId 图层id
     * @param {*} number 返回随机点数
     * @param {*} img 点图标 require('@/assets/img/map/jz/jz.png')
     * @return {*}
     */
    randomPoint(layerId, number, img) {
      if (this.bounds.length === 0) {
        // alert('请选择区域范围');
        return false
      }
      var points = turf.randomPoint(number, { bbox: this.bounds })
      var features = points.features.map((e, index) => {
        let coordinates = e.geometry.coordinates.reverse()
        // var latLng = L.CRS.EPSG3857.unproject(L.point(coordinates));
        let pointInfo = {
          latlng: coordinates,
          icon: {
            iconUrl: img,
            iconSize: [34, 208]
          },
          props: {
            name: '某某poi点',
            type: layerId,
            id: index
          }
        }
        return pointInfo
      })
      console.log('随机点：', features)
      this.drawPoiMarker(features, layerId, true)

      // 单个点位
      // let features = {
      //   icon: {
      //     iconUrl: 'img/people.7eff8f7f.png',
      //     iconSize: [47, 90]
      //   },
      //   latlng: [31.80757463704106, 119.21717254828414],
      //   props: {
      //     name: '某某poi点',
      //     type: 'people',
      //     id: 1
      //   }
      // }
      // this.drawPoiMarker([features], layerId, true)
    },
    /**
     * 测量
     */
    measure(type) {
      let self = this
      this.clearMeasure()
      // 线
      if (type === 'polyline') {
        this.dPolyline = this.map.editTools.startPolyline(null, {
          // color: 'red',
          bubblingMouseEvents: false
        })
        this.dPolyline.on(
          'editable:drawing:clicked editable:vertex:dragend editable:vertex:deleted',
          function() {
            self.dPolyline.showMeasurements().updateMeasurements()
          }
        )
      }
      // 面
      if (type === 'polygon') {
        this.dPolygon = this.map.editTools.startPolygon(null, {
          color: 'red',
          bubblingMouseEvents: false
        })
        this.dPolygon.on(
          'editable:drawing:clicked editable:vertex:dragend editable:vertex:deleted',
          function() {
            self.dPolygon.showMeasurements().updateMeasurements()
          }
        )
      }
    },
    /**
     * 清除测量
     */
    clearMeasure() {
      if (this.dPolyline) {
        this.dPolyline.hideMeasurements()
        this.dPolyline.remove()
        this.dPolyline = null
      }
      if (this.dPolygon) {
        this.dPolygon.hideMeasurements()
        this.dPolygon.remove()
        this.dPolygon = null
      }
    },
    // 绘制圆
    drawCircle(center = [31.89056396484375, 118.85833740234375], r = 5000) {
      if (this.circle) {
        this.circle.remove()
      }
      // 创建圆形对象
      this.circle = L.circle(center, {
        color: '#76C4FF',
        fillColor: '#37ADFE',
        fillOpacity: 0.5,
        radius: r // 单位：米
      })

      // 将圆形添加到地图上
      this.circle.addTo(this.map)
    },
    // 改变圆的大小
    changeCircle(val) {
      // 更新圆的半径
      this.circle.setRadius(val)
    },
    removeCircle() {
      // 删除圆形
      this.circle.remove()
    },
    // 绘制轨迹
    drawLins(
      lines = [
        [51.505, -0.09],
        [51.507, -0.1],
        [51.509, -0.08]
      ]
    ) {
      this.removeLine()
      // 创建Polyline对象并添加到地图上
      this.polyline = L.polyline(lines).addTo(this.map)

      // 将地图缩放到合适的级别以包含Polyline对象
      this.map.fitBounds(this.polyline.getBounds())
    },
    // 删除轨迹
    removeLine() {
      this.polyline && this.polyline.remove()
    }
  }
}
</script>

<style lang="less" scoped>
.mapContainer {
  // width: 100%;
  // height: 100%;
  z-index: 1;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px;
}

// 修改地图滤镜
// /deep/ .leaflet-zoom-animated img {
//   -webkit-filter: sepia(100%) invert(90%) !important;
//   -ms-filter: sepia(100%) invert(90%) !important;
//   -moz-filter: sepia(100%) invert(90%) !important;
//   filter: sepia(100%) invert(90%) !important;
// }

// /deep/ .leaflet-tooltip {
//   background-color: unset;
//   color: #fff;
//   text-shadow: -1px 0 #333, 0 1px #333, 1px 0 #333, 0 -1px #333;
//   border: unset;
//   box-shadow: unset;
//   font-size: 16px;
//   // font-weight: 800;
// }

// /deep/ .my-popup {
//   .leaflet-popup-content-wrapper {
//     box-shadow: unset;
//     background: unset;
//     color: unset;

//     .leaflet-popup-content {
//       width: unset !important;
//       margin: 0px;
//     }
//   }

//   .leaflet-popup-close-button {
//     top: 16px;
//     right: 1rem;
//   }
// }

// /deep/ .iclient-leaflet-logo {
//   display: none;
// }

// /deep/ .leaflet-measure-path-measurement {
//   position: absolute;
//   font-size: 10px;
//   color: black;
//   text-shadow: -1px 0 0 white, -1px -1px 0 white, 0 -1px 0 white, 1px -1px 0 white, 1px 0 0 white,
//     1px 1px 0 white, 0 1px 0 white, -1px 1px 0 white;
//   white-space: nowrap;
//   transform-origin: 0;
//   pointer-events: none;
// }

// /deep/ .leaflet-measure-path-measurement > div {
//   position: relative;
//   margin-top: -25%;
//   left: -50%;
// }

/deep/ .custom-tooltip {
  background-color: transparent !important;
  color: white !important;
  font-weight: bold !important;
  padding: 5px !important;
  border: none;
}

/deep/ .custom-div-icon{
  display: flex;
  .marker-icon{
    color: #fff;
    width: 138px;
    height: 69px;
    background-size: 100% 100%;
    div{
      white-space: nowrap;
      margin: 18px  0 0 60px;
      font-size: 18px;
      font-family: FZSKJW--GB1-0, FZSKJW--GB1;
      font-weight: normal;
      color: #FFFFFF;
    }
  }
}
</style>
