import http from '@/utils/leader/request'

const baseUrl = 'https://dpzs.camtc.cn'
// 预约会议
export const previewMeeting = (params = {}) => {
  return http.post(baseUrl + '/api/v1/scheduleMeeting', params)
}
// 获取预约会议列表
export const getPreviewList = (params = {}) => {
  return http.post(baseUrl + '/api/v1/getAppointMeetingByUserId', params)
}
// 查询历史会议记录
export const queryHistoryMeeting = (params = {}) => {
  return http.post(baseUrl + '/api/v1/queryConferenceRooms', params)
}
// 删除已预约会议
export const delPreviewedMeeting = (params = {}) => {
  return http.post(baseUrl + '/api/v1/deleteAppointMeeting', params)
}
// 修改已预约会议
export const updatePreviewMeeting = (params = {}) => {
  return http.post(baseUrl + '/api/v1/modifyAppointMeeting', params)
}
// 删除历史会议
export const delHistoryMeeting = (params = {}) => {
  return http.post(baseUrl + '/api/v1/deleteHistoryMeeting', params)
}
// 获取会议信息
export const getMeetingData = (params = {}) => {
  return http.post(baseUrl + '/api/v1/getAppointMeetingByRoomId', params)
}
