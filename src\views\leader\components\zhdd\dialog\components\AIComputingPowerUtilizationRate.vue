<template>
  <div class="compute-power">
    <ul>
      <li v-for="(it, i) of list" :key="i" :style="{ backgroundImage: `url(${it.bg})` }">
        <div class="label">{{ it.label }}</div>
        <div class="name">{{ it.name }}</div>
        <div class="count">{{ it.count }}</div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'AIComputingPowerUtilizationRate',
  data() {
    return {
      list: [
        {
          label: '排序',
          name: '算法名称',
          count: '占用资源',
          bg: require('@/assets/zhdd/bg11.png')
        },
        {
          label: '1',
          name: '机动车违停',
          count: '12.20%',
          bg: require('@/assets/zhdd/bg12.png')
        },
        {
          label: '2',
          name: '店外经营',
          count: '12.20%',
          bg: require('@/assets/zhdd/bg13.png')
        },
        {
          label: '3',
          name: '人员滞留',
          count: '12.20%',
          bg: require('@/assets/zhdd/bg14.png')
        },
        {
          label: '4',
          name: '违禁捕鱼',
          count: '12.20%',
          bg: require('@/assets/zhdd/bg15.png')
        },
        {
          label: '5',
          name: '外来人员',
          count: '12.20%',
          bg: require('@/assets/zhdd/bg15.png')
        }
      ]
    }
  }
}
</script>

<style lang="less" scoped>
.compute-power {
  width: 100%;
  height: 100%;
  padding: 19px 23px 0;
  ul {
    display: flex;
    flex-direction: column;
    gap: 14px;
    li {
      display: flex;
      align-items: center;
      padding: 0 26px 0 20px;
      width: 412px;
      height: 35px;
      .label {
        width: 71px;
        text-align: left;
      }
      .name {
        text-align: left;
        height: 22px;
        font-size: 16px;
        font-family: Alibaba-PuHuiTi-R, Alibaba-PuHuiTi;
        font-weight: normal;
        color: #edfffd;
        line-height: 22px;
      }
      .count {
        margin-left: auto;
        height: 24px;
        font-size: 20px;
        font-family: DINAlternate-Bold, DINAlternate;
        font-weight: bold;
        color: #ffffff;
        line-height: 24px;
      }
      &:nth-child(1) {
        width: 414px;
        height: 37px;
        .label {
          height: 18px;
          font-size: 16px;
          font-family: PingFang-SC-Medium, PingFang-SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 18px;
        }
        .name {
          height: 18px;
          font-size: 16px;
          font-family: PingFang-SC-Medium, PingFang-SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 18px;
        }
        .count {
          height: 18px;
          font-size: 16px;
          font-family: PingFang-SC-Medium, PingFang-SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 18px;
        }
      }
      &:nth-child(2) {
        .label {
          height: 24px;
          font-size: 20px;
          font-family: PingFangSC, PingFang SC;
          line-height: 24px;
          background: linear-gradient(180deg, #ffffff 0%, #2aebff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
      &:nth-child(3) {
        .label {
          height: 24px;
          font-size: 20px;
          font-family: PingFangSC, PingFang SC;
          line-height: 24px;
          background: linear-gradient(180deg, #ffffff 0%, #ea4a11 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
      &:nth-child(4) {
        .label {
          height: 28px;
          font-size: 20px;
          font-family: PingFangSC, PingFang SC;
          line-height: 28px;
          background: linear-gradient(175deg, #ffffff 0%, #ffef32 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
      &:nth-child(5),
      &:nth-child(6) {
        .label {
          height: 24px;
          font-size: 20px;
          font-family: PingFangSC, PingFang SC;
          color: #edfffd;
          line-height: 24px;
        }
      }
    }
  }
}
</style>
