import http from '@/utils/special/request'
import { finaUrl, finaUrls, videoUrl } from '@/utils/special/const'

// 总体概览
export const getOverview = () => {
  return http.get(finaUrl + '/api/campusSecurity/overview')
}

// 安全事件态势-校园事件
export const getCampusSecurityEvents = () => {
  return http.get(finaUrl + '/api/campusSecurity/campusSecurityEvents')
}
// 安全事件态势-安全事件
export const getSecurityEvents = () => {
  return http.get(finaUrl + '/api/campusSecurity/securityEvents')
}
// 安全事件-高发区域分析
export const getHighIncidenceAreas = () => {
  return http.get(finaUrl + '/api/campusSecurity/highIncidenceAreas')
}
// 热点事件-热点事件推送
export const getHotEvents = () => {
  return http.get(finaUrl + '/api/campusSecurity/hotEvents')
}
// 安全事件-总体趋势
export const getSecurityIncidentTrend = () => {
  return http.get(finaUrl + '/api/campusSecurity/securityIncidentTrend')
}

// 校园安全事件详细列表
export const getOrderList = () => {
  return http.get(finaUrl + '/api/campusSecurity/orderList?type=2')
}

// 校园安全事件视频撒点
export const getVideoList = () => {
  return http.post(videoUrl + '/api/video/showVideo')
}

// 校园安全事件视频监控
export const getVideoInfo = (data) => {
  return http.post(videoUrl + '/api/video/getOneVideo',data)
}

// 校园安全事件详细列表
export const restservice = (obj) => {
  return http.get(finaUrls + '/dmp-res-api/restservice/20221202145110287189', obj)
}

// 校园安全事件详细列表
export const restservice2 = (obj) => {
  return http.get(finaUrls + '/dmp-res-api/restservice/20221202151545868137', obj)
}

// 校园安全事件详细列表
export const restservice3 = (obj) => {
  return http.get(finaUrls + '/dmp-res-api/restservice/20221103165659490964', obj)
}

// 各类型学校数量
export const restservice4 = (obj) => {
  return http.get(finaUrls + '/dmp-res-api/restservice/20221104083011413250', obj)
}

// 各类学校比例
export const restservice5 = (obj) => {
  return http.get(finaUrls + '/dmp-res-api/restservice/20221108152219588745', obj)
}
