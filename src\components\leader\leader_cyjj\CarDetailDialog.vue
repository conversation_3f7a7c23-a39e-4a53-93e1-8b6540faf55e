<template>
    <div ref="popupCar" class="car">
      <div v-if="popCarInfo" class="car-warrper">
        <div class="marker-title"><span>车辆详情</span></div>
        <div class="close" @click="closeInfoWindow" />
        <div class="item">
          <div class="lable">车牌号：</div>
          <div class="value">{{ popCarInfo.num }}</div>
        </div>
        <div class="item">
          <div class="lable">所属部门：</div>
          <div class="value">{{ popCarInfo.dept }}</div>
        </div>
        <div class="item">
          <div class="lable">车辆类型：</div>
          <div class="value">{{ popCarInfo.type }}</div>
        </div>
        <div class="item">
          <div class="lable">司机名称：</div>
          <div class="value">{{ popCarInfo.driver }}</div>
        </div>
        <div class="item">
          <div class="lable">联系电话：</div>
          <div class="value">{{ popCarInfo.tel }}</div>
        </div>
        <div class="bottom-light" />
        <div class="bottom-arrow" />
      </div>
    </div>
  </template>
  
  <script>
  export default {
    props: {
      popCarInfo: {
        type: Object,
        default: () => ({})
      }
    },
    methods: {
      closeInfoWindow() {
        this.$emit('closeInfoWindow');
      }
    }
  };
  </script>
  
  <style lang="scss" scoped>
  .car {
    width: 334px;
    height: 218px;
    background-color: rgba(27, 36, 51, 0.8);
    border-radius: 11px;
    border: 1px solid rgba(17, 234, 200, 0.3);
    justify-content: center;
    align-items: center;
    position: relative;
  
    &-warrper {
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      text-align: center;
      position: relative;
      .marker-title {
        height: 51px;
        // background: url('~@/assets/img/dialog/car/car-title.png') no-repeat center / 100% 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        font-family: YouSheBiaoTiHei;
        color: #e9fffe;
        line-height: 26px;
        span {
          background: linear-gradient(180deg, #ffffff 0%, #2aebff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
      .close {
        width: 23px;
        height: 23px;
        position: absolute;
        right: 8px;
        top: 8px;
        // background: url('~@/assets/img/dialog/car/car-close.png') no-repeat center / 100% 100%;
        cursor: pointer;
      }
      .item {
        height: 24px;
        display: flex;
        margin-top: 6px;
        margin-left: 30px;
        .lable {
          padding-left: 10px;
          position: relative;
          text-align: left;
          font-size: 16px;
          font-family: AlibabaPuHuiTiR;
          color: rgba(255, 255, 255, 0.7);
          line-height: 24px;
        }
        .value {
          font-size: 16px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #47ebff;
          line-height: 24px;
        }
      }
      .bottom-light {
        height: 24px;
        width: 172px;
        position: absolute;
        bottom: -12px;
        left: 50%;
        transform: translateX(-50%);
        // background: url('~@/assets/img/dialog/car/car-light.png') no-repeat center / 100% 100%;
      }
      .bottom-arrow {
        height: 10px;
        width: 20px;
        position: absolute;
        bottom: -12px;
        left: 50%;
        transform: translateX(-50%);
        // background: url('~@/assets/img/dialog/car/car-arrow.png') no-repeat center / 100% 100%;
      }
    }
  }
  </style>
  