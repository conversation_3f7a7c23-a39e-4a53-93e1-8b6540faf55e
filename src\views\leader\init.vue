<template>
  <div class="leader_box">
    <div class="leader_sjts">
      <div class="left">
        <div class="left1">
          <BlockBox
            title="种植业"
            subtitle="Planting"
            class="box box1"
            :isListBtns="false"
            :blockHeight="280"
          >
            <div class="cont">
              <pieChartTsgz />
            </div>
          </BlockBox>
          <BlockBox
            title="畜牧业"
            subtitle="Animal husbandry"
            class="box box2"
            :isListBtns="false"
            :blockHeight="360"
          >
            <div class="cont">
              <div class="sjzl_box">
                <div class="sjzl_box_top">
                  <ul>
                    <li v-for="(item, index) in box2TopData" :key="index">
                      <countTo
                        ref="countTo"
                        :startVal="$countTo.startVal"
                        :decimals="$countTo.decimals(item.num)"
                        :endVal="item.num"
                        :duration="$countTo.duration"
                      />
                      <img src="@/assets/shzl/sjzl_top.png" alt />
                      <span>{{ item.tit }}</span>
                    </li>
                  </ul>
                </div>
                <div class="sjzl_box_bottom">
                  <LineColumnarChart
                    :data="box2BottomData"
                    :options="box2Options"
                    :init-option="box2InitOption"
                  ></LineColumnarChart>
                </div>
              </div>
            </div>
          </BlockBox>
          <BlockBox
            title="产值分析"
            subtitle="Output value analysis"
            class="box box3"
            :isListBtns="false"
            :blockHeight="220"
          >
            <div class="cont">
              <div class="left_">
                <ul>
                  <li v-for="(item, index) in box3LeftData" :key="index">
                    <p>{{ item.name }}</p>
                    <p>{{ item.value }}</p>
                  </li>
                </ul>
              </div>
              <div class="right_">
                <ul>
                  <li v-for="(item, index) in box3RightData" :key="index">
                    <img :src="item.icon" alt="" />
                    <div>
                      <p>{{ item.value }}<span>亿元</span></p>
                      <p>{{ item.name }}</p>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </BlockBox>
        </div>
      </div>
      <div class="right">
        <div class="right1">
          <BlockBox
            title="24小时雨晴统计"
            subtitle="24Hour rain and sunny statistics"
            class="box box4"
            :isListBtns="false"
            :blockHeight="280"
            :textArr="[]"
          >
            <div class="cont">
              <TrendLineChart
                :data="box4Data"
                :options="box4Options"
                :initOption="box4InitOption"
              />
            </div>
          </BlockBox>
          <BlockBox
            title="水情监测"
            subtitle="Water monitoring"
            class="box box5"
            :isListBtns="false"
            :blockHeight="340"
          >
            <div class="cont">
              <swiper-table
                :data="box5Data"
                :titles="['类型', '组织名称', '党支部书记']"
                :widths="['100px', '200px', '138px']"
                content-height="260px"
              />
            </div>
          </BlockBox>
          <BlockBox
            title="视频监控"
            subtitle="Video surveillance"
            class="box box6"
            :showMore="true"
            :isListBtns="false"
            :blockHeight="296"
            @handleShowMore="showMoreFn(6)"
          >
            <div class="cont">
              <VideoSurveillance :cameraId="cameraId1" v-if="cameraId1" class="video_" />
              <VideoSurveillance :cameraId="cameraId2" v-if="cameraId2" class="video_" />
              <VideoSurveillance :cameraId="cameraId3" v-if="cameraId3" class="video_" />
              <VideoSurveillance :cameraId="cameraId4" v-if="cameraId4" class="video_" />
            </div>
          </BlockBox>
        </div>
      </div>
    </div>
    <!-- 侧边菜单 -->
    <ZhddAside @marker="marker" :list="leftMenuList" />
    <!-- 地图 -->
    <div class="map_box">
      <LeafletMap ref="leafletMap" @poiClick="showDialog" />
    </div>
    <!-- 视频监控 -->
    <spjkPop
      v-if="spjkShow"
      @closeEmit="spjkShow = false"
      @operate="handleSsgjOperate"
      :leftTreeData="spjkLeftTree"
    ></spjkPop>
    <!-- 地图弹窗 -->
    <infoPop v-if="isInfoPopShow" @close="isInfoPopShow = false" />
  </div>
</template>

<script>
import BlockBox from '@/components/leader/common/blockBox.vue'
import HeaderMain from '@/components/leader/common/headerMains'
import leaderMiddle from '@/components/leader/common/leaderMiddle.vue'
import SwiperTable from '@/components/djyl/SwiperTable.vue'
// 各模块
import ZhddAside from '@/components/djyl/Aside.vue'
import LeafletMap from '@/components/map/LeafletMap2.vue'
import 'swiper/css/swiper.css'
import pieChartTsgz from '@/components/tsgz/pieChartTsgz.vue'
import infoPop from '@/components/zhny/infoPop.vue'

// 接口
import { getCameraMakers, getCameraXq } from '@/api/hs/hs.api.js'

export default {
  name: 'init',
  components: {
    BlockBox,
    HeaderMain,
    SwiperTable,
    ZhddAside,
    LeafletMap,
    pieChartTsgz,
    infoPop,
  },
  data() {
    return {
      box2TopData: [
        {
          num: 6000,
          tit: '本月新增事件量',
        },
        {
          num: 2345,
          tit: '本月新增办理事件量',
        },
        {
          num: 3655,
          tit: '本月新增办结事件量',
        },
      ],
      cameraId1: '',
      cameraId2: '',
      cameraId3: '',
      cameraId4: '',
      spjkShow: false,
      box2BottomData: [
        ['product', '数量'],
        ['城市管理', 242],
        ['社会治安', 2],
        ['民生服务', 88],
        ['专项活动', 0],
        ['环境保护', 5],
        ['公共安全', 366],
        ['重要紧急', 3],
      ],
      box2Options: {
        gradientColors: [['#00A3D7', '#99E4FA']],
        yAxis: {
          unit: '',
        },
        grid: {
          top: 10,
          left: 2,
          right: 2,
          bottom: '28%',
        },
        isToolTipInterval: true,
      },
      box4Data: [
        ['product', '事件'],
        ['1月', 77],
        ['2月', 97],
        ['3月', 95],
        ['4月', 81],
        ['5月', 15],
        ['6月', 6],
        ['7月', 28],
        ['8月', 1],
        ['9月', 54],
        ['10月', 12],
        ['11月', 26],
        ['12月', 50],
      ],
      box4Options: {
        legend: {
          show: false,
        },
      },
      box4InitOption: {
        yAxis: {
          name: '降雨量/mn',
        },
      },
      box5Data: [
        [
          '紧急',
          '家庭纠纷风险',
          '居民李红女士家庭内部疑似存在纠纷',
          '未处置',
          '23-05-10  09:00:00',
        ],
        ['重要', '劳资纠纷风险', '居民王明疑似存在劳资纠纷风险', '未处置', '23-05-10  09:00:01'],
        ['普通', '征地纠纷风险', '红山村疑似存在征地纠纷风险', '未处置', '23-05-10  09:00:00'],
        ['重要', '潜在失意人员', '居民王红疑似为潜在失意人员', '未处置', '23-05-10  09:00:00'],
        [
          '普通',
          '疑似群租房',
          '保利花园1栋101室被群众举报疑似群租房',
          '未处置',
          '23-05-10  09:00:00',
        ],
        [
          '重要',
          '房地产交易纠纷',
          '居民王明疑似卷入房地产纠纷事件',
          '已处置',
          '23-05-10  09:00:00',
        ],
        [
          '普通',
          '群体性事件',
          '事件人数较多，疑似会发展成群体性事件',
          '已处置',
          '23-05-10  09:00:01',
        ],
      ],
      leftMenuList: [
        {
          bg: require('@/assets/aside/bg1.png'),
          bgActive: require('@/assets/aside/bg2.png'),
          icon: require('@/assets/zhny/map/icon1.png'),
          iconActive: require('@/assets/zhny/map/icon1_active.png'),
          label: '特色产业园',
          active: false,
        },
        {
          bg: require('@/assets/aside/bg1.png'),
          bgActive: require('@/assets/aside/bg2.png'),
          icon: require('@/assets/zhny/map/icon2.png'),
          iconActive: require('@/assets/zhny/map/icon2_active.png'),
          label: '家庭农场',
          active: false,
        },
        {
          bg: require('@/assets/aside/bg1.png'),
          bgActive: require('@/assets/aside/bg2.png'),
          icon: require('@/assets/zhny/map/icon3.png'),
          iconActive: require('@/assets/zhny/map/icon3_active.png'),
          label: '龙头企业',
          active: false,
        },
        {
          bg: require('@/assets/aside/bg1.png'),
          bgActive: require('@/assets/aside/bg2.png'),
          icon: require('@/assets/zhny/map/icon4.png'),
          iconActive: require('@/assets/zhny/map/icon4_active.png'),
          label: '农业合作社',
          active: false,
        },
      ],
      isInfoPopShow: false,
      box3LeftData: [
        { name: '当前产值 (亿元)', value: '18.43' },
        { name: '计划产值 (亿元)', value: '36.13' },
        { name: '完成率(%)', value: '51.01' },
      ],
      box3RightData: [
        { name: '种植业', value: '6.95', icon: require('@/assets/zhny/map/czfx1.png') },
        { name: '渔业', value: '2.43', icon: require('@/assets/zhny/map/czfx1.png') },
        { name: '畜牧业', value: '3.62', icon: require('@/assets/zhny/map/czfx1.png') },
        { name: '林业', value: '1.08', icon: require('@/assets/zhny/map/czfx1.png') },
      ],
      spjkLeftTree: [],
    }
  },
  created() {},
  mounted() {
    this.cameraMarker()
  },
  watch: {},
  computed: {
    formatNumber() {
      return function (param) {
        console.log(param)
        if (this.isEmpty(param) || param.length < 4 || isNaN(param)) {
          return param
        }

        let small = ''
        let isSmall = false
        if (param.split('.').length === 2) {
          // 有小数
          isSmall = true
          small = param.split('.')[1]
          param = param.split('.')[0]
        }

        let result = ''

        while (param.length > 3) {
          result = ',' + param.slice(-3) + result
          param = param.slice(0, param.length - 3)
        }

        if (param) {
          result = param + result
        }

        if (isSmall) {
          result = result + '.' + small
        }
        console.log(result)
        return result
      }
    },
    box2InitOption() {
      return {
        xAxis: {
          axisLabel: {
            formatter: function (value) {
              // 将标签中的每两个字符之间插入一个换行符
              var lines = value.replace(/(.{2})/g, '$1\n')
              return lines
            },
          },
        },
        yAxis: {
          name: '',
        },
        tooltip: {
          formatter: (params) => {
            var relVal = params[0].name
            for (var i = 0, l = params.length; i < 1; i++) {
              relVal += '<br/>' + params[i].marker + params[i].value + ' 件'
            }
            return relVal
          },
        },
      }
    },
  },
  methods: {
    marker(val, i) {
      console.log(11111, val, i)
      if (i + 1 == 1 && val) {
        this.mapMarker1()
      } else if (i + 1 == 1 && !val) {
        this.cleaerMapMarker('tscyy')
      }
      if (i + 1 == 2 && val) {
        this.mapMarker2()
      } else if (i + 1 == 2 && !val) {
        this.cleaerMapMarker('jtnc')
      }
      if (i + 1 == 3 && val) {
        this.mapMarker3()
      } else if (i + 1 == 3 && !val) {
        this.cleaerMapMarker('ltqy')
      }
      if (i + 1 == 4 && val) {
        this.mapMarker4()
      } else if (i + 1 == 4 && !val) {
        this.cleaerMapMarker('nmhzs')
      }
    },
    mapMarker(data, layerId) {
      this.$refs.leafletMap.drawPoiMarker(data, layerId, true, false, true)
    },
    mapMarker1() {
      let data = this.setMapArrayofPoint(
        [
          {
            longitude: '118.969401',
            latitude: '31.863897',
            id: '11',
            name: '特色产业园',
          },
          {
            longitude: '118.986739',
            latitude: '31.852411',
            id: '22',
            name: '特色产业园',
          },
          {
            longitude: '118.986309',
            latitude: '31.852378',
            id: '33',
            name: '特色产业园',
          },
        ],
        require('@/assets/zhny/map/map_icon1.png'),
        'tscyy'
      )
      this.mapMarker(data, 'tscyy')
    },
    mapMarker2() {
      let data = this.setMapArrayofPoint(
        [
          {
            longitude: '118.973006',
            latitude: '31.873373',
            id: '11',
            name: '家庭农场',
          },
          {
            longitude: '119.016546',
            latitude: '31.883741',
            id: '22',
            name: '家庭农场',
          },
          {
            longitude: '118.975544',
            latitude: '31.895522',
            id: '33',
            name: '家庭农场',
          },
        ],
        require('@/assets/zhny/map/map_icon2.png'),
        'jtnc'
      )
      this.mapMarker(data, 'jtnc')
    },
    mapMarker3() {
      let data = this.setMapArrayofPoint(
        [
          {
            longitude: '118.971976',
            latitude: '31.862144',
            id: '11',
            name: '龙头企业',
          },
          {
            longitude: '118.980902',
            latitude: '31.865986',
            id: '22',
            name: '龙头企业',
          },
          {
            longitude: '118.985752',
            latitude: '31.855427',
            id: '33',
            name: '龙头企业',
          },
        ],
        require('@/assets/zhny/map/map_icon3.png'),
        'ltqy'
      )
      this.mapMarker(data, 'ltqy')
    },
    mapMarker4() {
      let data = this.setMapArrayofPoint(
        [
          {
            longitude: '118.994463',
            latitude: '31.853666',
            id: '11',
            name: '农业合作社',
          },
          {
            longitude: '118.967255',
            latitude: '31.840088',
            id: '22',
            name: '农业合作社',
          },
          {
            longitude: '118.98027',
            latitude: '31.86233',
            id: '33',
            name: '农业合作社',
          },
        ],
        require('@/assets/zhny/map/map_icon4.png'),
        'nmhzs'
      )
      this.mapMarker(data, 'nmhzs')
    },
    cleaerMapMarker(type) {
      // 清除标记点
      console.log(1111)
      this.$refs.leafletMap.removeLayer(type)
    },
    // 地图弹窗
    showDialog(layerId, it) {
      const id = it.props.id
      console.log(layerId, id)
      this.isInfoPopShow = true
    },
    async cameraXq(id) {
      // console.log('id', id)
      const res = await getCameraXq(id)
      // console.log('res', res.body.data[0].url)
      if (res && res.body && res.body.data && res.body.data.length > 0 && res.body.data[0].url) {
        this.hkTestUrl = res.body.data[0].url
        this.cameraCode = id //传给hls组件，当视频播放失败时再次调用
        this.hkVideShow = true
      }
    },
    // 显示更多
    showMoreFn(i) {
      if (i === 6) {
        this.spjkShow = true
        if (this.spjkLeftTree.length == 0) {
          this.cameraMore()
        }
      }
    },
    handleSsgjOperate() {},
    async cameraMarker() {
      const res = await getCameraMakers()
      if (res && res.length > 0) {
        let filerRes = res.filter((item) => item.id)
        this.cameraId1 = filerRes[4].id
        this.cameraId2 = filerRes[5].id
        this.cameraId3 = filerRes[7].id
        this.cameraId4 = filerRes[9].id

        this.spjkLeftTree = []
        this.spjkLeftTree.unshift({ id: 'xlgcId', name: '雪亮工程', children: [] })
        this.spjkLeftTree[0].children = res
      }
    },
    setMapArrayofPoint(points, iconUrl, layerId) {
      const data = points.map((it, i) => ({
        latlng: [it.latitude || 0, it.longitude || 0],
        icon: {
          iconUrl: iconUrl,
          iconSize: [32, 42],
          iconAnchor: [16, 42],
          html: '123',
        },
        props: {
          id: it.id,
          name: it.name || '-',
          type: layerId,
        },
        info: it,
      }))
      return data
    },
    async cameraMore() {
      const res = await getCameraMakers()
      console.log('getCameraMakers', res)
      if (res && res.length > 0) {
        this.spjkLeftTree = []
        this.spjkLeftTree.unshift({ id: 'xlgcId', name: '雪亮工程', children: [] })
        this.spjkLeftTree[0].children = res
      }
    },
  },
}
</script>

<style lang="less" scoped>
@font-face {
  font-family: QuartzRegular;
  src: url(~@/assets/leader/font/QuartzRegular.ttf);
}
@keyframes rotateS {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes rotateN {
  0% {
    transform: rotate(360deg);
  }
  100% {
    transform: rotate(0);
  }
}
@keyframes rotateY {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotateY(360deg);
  }
}

.leader_sjts {
  width: 1920px;
  height: 1080px;
  position: absolute;
  top: 0;
  display: flex;
  justify-content: space-between;
  .leader_city_box {
    width: 450px;
    .leader_zt_contain {
      height: 100%;
    }
  }
  .box {
    .cont {
      width: 100%;
      height: 100%;
    }

    .ybsscont {
      height: 100%;
      padding: 14px 44px 24px 37px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: space-between;
      li {
        position: relative;
        width: 165px;
        height: 66px;
        background: url(~@/assets/shzl/ybss_bg.png) no-repeat;
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: left;
        padding-left: 92px;
        .icon {
          position: absolute;
          width: 26px;
          height: 26px;
          top: 10px;
          left: 31px;
          animation: move infinite 3s ease-in-out;
          @keyframes move {
            0% {
              transform: translateY(0px) rotateY(0);
            }
            50% {
              transform: translateY(-10px) rotateY(180deg);
            }
            100% {
              transform: translateY(0px) rotateY(360deg);
            }
          }
        }
        &:nth-child(2) {
          span {
            animation-delay: 0.3s;
          }
        }
        &:nth-child(3) {
          span {
            animation-delay: 0.6s;
          }
        }
        &:nth-child(4) {
          span {
            animation-delay: 0.9s;
          }
        }
        &:nth-child(5) {
          span {
            animation-delay: 1.2s;
          }
        }
        &:nth-child(6) {
          span {
            animation-delay: 1.5s;
          }
        }
        .tit {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 20px;
          white-space: nowrap;
        }
        .num {
          font-size: 18px;
          font-family: PingFangSC, PingFang SC;
          font-weight: normal;
          color: #ffffff;
          line-height: 22px;
        }
      }
    }
    .wrapper4 {
      width: 100%;
      height: 100%;
      padding: 20px 0;
      ul {
        width: 100%;
        height: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;
        li {
          display: flex;
          height: 49px;
          gap: 10px;
          .icon {
            width: 46px;
            height: 49px;
          }
          .info {
            margin-top: -6px;
            .label {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
              text-align: left;
              padding-left: 5px;
            }
            .line {
              width: 87px;
              height: 6px;
              background: url('~@/assets/hszl/line1.png') no-repeat;
            }
            .count {
              text-align: left;
              margin-top: 4px;
              width: 87px;
              height: 22px;
              line-height: 22px;
              background: linear-gradient(96deg, #1d4975 0%, rgba(19, 56, 87, 0.24) 100%);
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #a8b1b9;
              padding-left: 5px;
              span {
                font-size: 18px;
                font-family: PingFangSC, PingFang SC;
                font-weight: normal;
                color: #ffffff;
                line-height: 22px;
                margin-right: 3px;
              }
            }
          }
        }
      }
    }
  }
  .box1 {
    .cont {
      padding: 13px 0 23px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: space-between;
      li {
        position: relative;
        width: 222px;
        height: 61px;
        padding: 10px 7px;
        display: flex;
        background: url(~@/assets/hszl/bg2.png) no-repeat;
        .icon {
          width: 41px;
          height: 41px;
          background: url(~@/assets/hszl/bg3.png) no-repeat;
          font-size: 17px;
          img {
            animation: move infinite 3s ease-in-out;
            @keyframes move {
              0% {
                transform: rotateY(0);
              }
              50% {
                transform: rotateY(180deg);
              }
              100% {
                transform: rotateY(360deg);
              }
            }
          }
          .name {
            font-size: 10px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 11px;
            text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
          }
        }
        .line {
          position: absolute;
          left: 50px;
          top: 5px;
          width: 1px;
          height: 55px;
          border: 1px solid;
          border-image: linear-gradient(
              180deg,
              rgba(0, 83, 171, 0),
              rgba(0, 140, 213, 0.6),
              rgba(0, 83, 171, 0)
            )
            1 1;
        }
        .desc {
          position: relative;
          margin-left: 15px;
          text-align: left;
          padding-left: 18px;
          .xsj {
            position: absolute;
            width: 10px;
            height: 10px;
            left: 0;
            top: 7px;
            background: url(~@/assets/csts/xsj1.png) no-repeat;
          }
          .num {
            font-size: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 24px;
          }
          .tit {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 20px;
            white-space: nowrap;
          }
        }
      }
    }
    .chart_wrap {
      position: relative;
      width: 100%;
      height: 250px;
      .sign {
        position: absolute;
        left: 105px;
        top: 25px;
        .woman {
          position: absolute;
          width: 27px;
          height: 57px;
          left: 60px;
          top: -10px;
          background: url('~@/assets/hszl/woman.png') no-repeat;
        }
        .man {
          position: absolute;
          width: 23px;
          height: 57px;
          left: 166px;
          top: 45px;
          background: url('~@/assets/hszl/man.png') no-repeat;
        }
        .man_count {
          position: absolute;
          left: 64px;
          top: 72px;
          width: 109px;
          height: 31px;
          font-size: 20px;
          font-family: PingFangSC, PingFang SC;
          font-weight: normal;
          color: #ffffff;
          line-height: 24px;
        }
        .woman_count {
          position: absolute;
          left: 70px;
          top: 24px;
          width: 109px;
          height: 31px;
          font-size: 20px;
          font-family: PingFangSC, PingFang SC;
          font-weight: normal;
          color: #ffffff;
          line-height: 24px;
        }
      }
    }
  }
  .box2 {
    .cont {
      .sjzl_box {
        padding: 20px 22px 23px;
        .sjzl_box_top {
          margin-bottom: 23px;
          ul {
            display: flex;
            justify-content: space-between;
            li {
              display: flex;
              flex-direction: column;
              &:first-of-type {
                & span:first-of-type {
                  font-size: 20px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: normal;
                  color: #ffffff;
                  line-height: 24px;
                  // text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.5);
                  background: linear-gradient(180deg, #ffffff 0%, #00fffa 67%, #04a1ff 100%);
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                }
                & span:last-of-type {
                  font-size: 14px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: 400;
                  color: #ffffff;
                  line-height: 20px;
                }
              }
              &:nth-of-type(2) {
                & span:first-of-type {
                  font-size: 20px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: normal;
                  color: #ffffff;
                  line-height: 24px;
                  // text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.5);
                  background: linear-gradient(180deg, #ffffff 0%, #ffd500 67%, #ffb904 100%);
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                }
                & span:last-of-type {
                  font-size: 14px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: 400;
                  color: #ffffff;
                  line-height: 20px;
                }
              }
              &:nth-of-type(3) {
                & span:first-of-type {
                  font-size: 20px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: normal;
                  color: #ffffff;
                  line-height: 24px;
                  // text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.5);
                  background: linear-gradient(180deg, #ffffff 0%, #86ff00 67%, #1cff04 100%);
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                }
                & span:last-of-type {
                  font-size: 14px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: 400;
                  color: #ffffff;
                  line-height: 20px;
                }
              }
            }
          }
        }
        .sjzl_box_bottom {
          width: 406px;
          height: 220px;
        }
      }
    }
  }
  .box3 {
    .cont {
      display: flex;
      padding: 18px 4px 0;
      .left_ {
        ul {
          li {
            width: 134px;
            height: 47px;
            background: url(~@/assets/zhny/map/czfx_left.png) no-repeat center / 100% 100%;
            margin-bottom: 13px;
            & p:first-of-type {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: normal;
              color: #ffffff;
              line-height: 26px;
            }
            & p:last-of-type {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
            }
          }
        }
      }
      .right_ {
        margin-left: 25px;
        ul {
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;
          li {
            display: flex;
            &:nth-of-type(1) {
              margin-bottom: 56px;
            }
            img {
              width: 54px;
              height: 54px;
            }
            & p:first-of-type {
              font-size: 22px;
              font-family: PingFangSC, PingFang SC;
              font-weight: normal;
              color: #ffffff;
              line-height: 26px;
              span {
                font-size: 14px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                color: #ffffff;
                line-height: 20px;
              }
            }
            & p:last-of-type {
              margin-top: 8px;
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
            }
          }
        }
      }
    }
  }
  .box4 {
    .cont_img {
      // border: 1px solid red;
      padding: 10px 0 10px 0;
      .img_box {
        display: flex;
        justify-content: space-between;
        padding: 0 40px;
        .img_ {
          width: 185px;
          height: 140px;
          flex-shrink: 0;
        }
        // img {
        //   width: 100%;
        //   height: 80%;
        // }
        p {
          color: #fff;
          position: relative;
          z-index: 99;
          font-size: 14px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          top: 4px;
        }
      }
    }
    .cont_video {
      // border: 1px solid red;
      padding: 10px 0 10px 0;
      .video_box {
        display: flex;
        justify-content: center;
        .video_ {
          width: 290px;
          height: 150px;
          flex-shrink: 0;
        }
        video {
          width: 290px;
          height: 150px;
          object-fit: fill;
        }
      }
    }
  }

  .box5 {
    .cont {
      padding: 20px 2px 6px;
    }
  }
  .box6 {
    .cont {
      padding: 20px 15px 0 7px;
      overflow: hidden;
      // border: 1px solid red;
    }
  }
  .left_bg {
    width: 460px;
    height: 1033px;
    background: url(~@/assets/leader/img/component/left_bg.png) no-repeat center / 100% 100%;
    position: absolute;
    left: 50px;
    top: 25px;
    z-index: 102;
  }
  .right_bg {
    width: 520px;
    height: 1033px;
    background: url(~@/assets/leader/img/component/right_bg.png) no-repeat center / 100% 100%;
    position: absolute;
    right: 50px;
    top: 25px;
    z-index: 102;
  }
  .left {
    width: 450px;
    display: flex;
    justify-content: space-between;
    margin-top: 123px;
    margin-left: 50px;
    position: relative;
    z-index: 1003;
  }
  .right {
    width: 450px;
    margin-right: 60px;
    display: flex;
    justify-content: space-between;
    margin-top: 136px;
    position: relative;
    z-index: 1003;
  }
  .map_box {
    position: absolute;
    width: 1920px;
    height: 970px;
    top: 100px;
    left: 0;
  }
}
.jgzzBox {
  width: 109px;
  height: 170px;
  display: flex;
  flex-direction: column;
  position: absolute;
  left: 525px;
  bottom: 93px;
  z-index: 999;
  background: url(~@/assets/csts/btnBg11.png) no-repeat center / 100% 100%;
  .jgzzItem {
    width: 103px;
    height: 35px;
    margin: 2px 3px 2px 3px;
    background: url(~@/assets/csts/btn11.png) no-repeat;
    img {
      float: left;
      margin: 7px 0 0 10px;
    }
    span {
      float: left;
      font-size: 17px;
      font-family: PingFangSC, PingFang SC;
      color: #e9fffe;
      line-height: 35px;
      background: linear-gradient(180deg, #ffffff 0%, #2f8af4 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .jgzzActive {
    width: 105px;
    height: 37px;
    margin: 1px 2px 1px 2px;
    background: url(~@/assets/csts/btn12.png) no-repeat;
  }
}
.zdcsBox {
  width: 109px;
  height: 248px;
  display: flex;
  flex-direction: column;
  position: absolute;
  left: 1197px;
  bottom: 93px;
  z-index: 999;
  background: url(~@/assets/csts/btnBg12.png) no-repeat center / 100% 100%;
  .jgzzItem {
    width: 103px;
    height: 35px;
    margin: 2px 3px 2px 3px;
    background: url(~@/assets/csts/btn11.png) no-repeat;
    img {
      float: left;
      margin: 7px 0 0 10px;
    }
    span {
      float: left;
      font-size: 17px;
      font-family: PingFangSC, PingFang SC;
      color: #e9fffe;
      line-height: 35px;
      background: linear-gradient(180deg, #ffffff 0%, #2f8af4 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .jgzzActive {
    width: 105px;
    height: 37px;
    margin: 1px 2px 1px 2px;
    background: url(~@/assets/csts/btn12.png) no-repeat;
  }
}
.left,
.right {
  ::v-deep {
    .el-carousel {
      width: 450px;
      height: 920px;
      .el-carousel__indicators {
        .el-carousel__indicator {
          &.is-active {
            .el-carousel__button {
              height: 4px;
              background-color: #49e6ff;
            }
          }
          .el-carousel__button {
            height: 4px;
            background-color: rgba(255, 255, 255, 0.4);
            border-radius: 2px;
          }
        }
      }
    }
  }
}
.right {
  ::v-deep {
    .el-carousel {
      margin-left: auto;
    }
  }
}
::v-deep .el-carousel--horizontal {
  width: 450px;
  overflow-x: hidden;
}

/*先去掉默认样式*/
.swiper-button-prev:after {
  display: none;
}
.swiper-button-next:after {
  display: none;
}

/*再自定义样式*/
.swiper-button-prev {
  display: inline-block;
  width: 26px;
  height: 32px;
  background: url(~@/assets/djyl/btn-pre.png) no-repeat;
  bottom: 15px;
}
.swiper-button-next {
  display: inline-block;
  width: 26px;
  height: 32px;
  background: url(~@/assets/djyl/btn-next.png) no-repeat;
  bottom: 15px;
}

/deep/ .video-js .vjs-control {
  width: 2em;
}
/deep/ .video-js .vjs-volume-panel.vjs-volume-panel-vertical {
  width: 0em;
}

.hkVide_box {
  position: absolute;
  left: 600px;
  width: 600px;
  height: 600px;
  top: 300px;
  z-index: 999;
  .video_ {
    width: 300px;
    height: 300px;
  }
}
.link {
  padding: 0 26px;
  height: 250px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-content: space-between;
  li {
    width: 183px;
    height: 75px;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
