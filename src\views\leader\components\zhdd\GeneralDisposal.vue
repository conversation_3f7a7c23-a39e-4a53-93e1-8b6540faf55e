<template>
  <!-- 指挥调度-一般处置 -->
  <div class="general-disposal">
    <div class="left">
      <div class="back" @click="close">
        <div class="icon"></div>
        返回
      </div>
      <BlockBox
        title="事件详情"
        subtitle="Event Detail"
        class="box"
        :blockHeight="850"
        :textArr="['基本信息', '处理流程']"
        @updateChange="handleClick"
      >
        <div class="basic-info" v-if="checkedIndex === 0">
          <ul>
            <li v-for="(it, i) of infos" :key="i">
              <div class="label">{{ it.label }}</div>
              <div
                class="value"
                :title="it.value"
                :class="{
                  urgent1: it.value === '紧急',
                  urgent2: it.value === '重要',
                  urgent3: it.value === '突发',
                  urgent4: it.value === '普通'
                }"
              >
                {{ it.value }}
              </div>
            </li>
          </ul>
          <swiper class="swiper" :options="swiperOption">
            <swiper-slide v-for="(item, i) in imgList" :key="i">
              <el-image :src="item" :preview-src-list="imgList"></el-image>
            </swiper-slide>
          </swiper>
        </div>
        <div class="process-flow" v-if="checkedIndex === 1">
          <ul class="time_line">
            <li class="time_line_item" v-for="(it, i) of processList" :key="i">
              <div class="basic_info">
                <div class="name">{{ it.nodeName }}</div>
                <div class="time" v-if="it.endTime">{{ it.endTime }}</div>
                <!-- <div class="line"></div>
                <div class="grid">朱中村网格一</div>
                <div class="line"></div>
                <div class="state">办结：2022-05-05 06:02:34</div>-->
              </div>
              <div
                class="detail_info"
                v-if="!(i === processList.length - 1 && it.nodeName === '事件结案')"
              >
                <ul>
                  <li>
                    <div class="lab">操作动作：</div>
                    <div class="val">{{ it.operateName }}</div>
                  </li>
                  <li>
                    <div class="lab">经办人：</div>
                    <div class="val">{{ it.dealName }}</div>
                  </li>
                  <li>
                    <div class="lab">计划完成时间：</div>
                    <div class="val">{{ it.expectTime }}</div>
                  </li>
                  <li>
                    <div class="lab">签收时间：</div>
                    <div class="val">{{ it.claimTime }}</div>
                  </li>
                  <li>
                    <div class="lab">附件：</div>
                    <!-- <div class="val">{{ it.appendixList }}</div> -->
                    <div class="val" v-if="it.appendixList?.length === 0">无</div>
                    <div class="val imgs" v-else>
                      <span
                        v-for="(item, i) of it.appendixList?.filter(itt => itt.fileType === '1')"
                      >
                        <el-image
                          :src="item.url"
                          :preview-src-list="
                            it.appendixList?.filter(itt => itt.fileType === '1').map(itt => itt.url)
                          "
                        ></el-image>
                      </span>
                    </div>
                  </li>
                  <li>
                    <div class="lab">处理意见：</div>
                    <div class="val">{{ it.dealAdvice }}</div>
                  </li>
                </ul>
              </div>
            </li>
          </ul>
        </div>
      </BlockBox>
    </div>
    <div class="right" v-if="show">
      <BlockBox
        title="视频监控"
        subtitle="Video surveillance"
        class="box"
        :isListBtns="false"
        :showMore="true"
        :blockHeight="851"
        @handleShowMore="showMoreFn1(2)"
      >
        <div class="video-wrap">
          <ul>
            <li>
              <!-- <LivePlayer :videoUrl="it" fluent autoplay live stretch></LivePlayer> -->
              <hlsVideo
                class="video_"
                :src="cameraUrl1"
                v-if="cameraUrl1"
                :cameraCode="cameraId1"
              ></hlsVideo>
            </li>
            <li>
              <hlsVideo
                class="video_"
                :src="cameraUrl2"
                v-if="cameraUrl2"
                :cameraCode="cameraId2"
              ></hlsVideo>
            </li>
            <li>
              <hlsVideo
                class="video_"
                :src="cameraUrl3"
                v-if="cameraUrl3"
                :cameraCode="cameraId3"
              ></hlsVideo>
            </li>
            <li>
              <hlsVideo
                class="video_"
                :src="cameraUrl4"
                v-if="cameraUrl4"
                :cameraCode="cameraId4"
              ></hlsVideo>
            </li>
          </ul>
        </div>
      </BlockBox>
    </div>
  </div>
</template>

<script>
import BlockBox from '@/components/leader/common/blockBox.vue'
import LivePlayer from '@liveqing/liveplayer'
import hlsVideo from '@/components/leader/hlsVideo'
import { getCameraMakers, getCameraXq } from '@/api/hs/hs.api.js'
export default {
  name: 'GeneralDisposal',
  components: { BlockBox, LivePlayer, hlsVideo },
  props: {
    value: {
      type: Boolean,
      default: true
    },
    infos: {
      type: Array,
      default: () => {
        return [
          {
            label: '姓名',
            value: '重大交通事故'
          },
          {
            label: '事件类型',
            value: '城市管理-突发事件'
          },
          {
            label: '事件地址',
            value: '中国江苏省南京市鼓楼区华侨路街道'
          },
          {
            label: '上报时间',
            value: '2022-05-05 08:38:22'
          },
          {
            label: '事件类型',
            value: '城市管理'
          },
          {
            label: '事件来源',
            value: '12345'
          },
          {
            label: '事件编号',
            value: 'DH6101002205050065902'
          },
          {
            label: '事件状态',
            value: '待区指挥中心签收'
          },
          {
            label: '事件时间',
            value: '2022-05-05 08:38:22'
          },
          {
            label: '公开信息',
            value: '是'
          },
          {
            label: '紧急程度',
            value: '紧急'
          },
          {
            label: '归属地',
            value: '是'
          },
          {
            label: '事件内容',
            value:
              ''
          },
          {
            label: '附件',
            value: ''
          }
        ]
      }
    },
    processList: {
      type: Array,
      default: () => {
        return []
      }
    },
    imgList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  computed: {
    show() {
      console.log(this.value)
      return this.value
    }
  },
  data() {
    return {
      checkedIndex: 0,
      swiperOption: {
        slidesPerView: '2',
        autoplay: {
          // 这个参数很重要,当外力干扰轮播图时，它能够继续实现自我滚动
          disableOnInteraction: false,
          delay: 2500 //5秒切换一次
        },
        pagination: {
          el: '.swiper-pagination',
          clickable: true,
          clickableClass: 'my-pagination-clickable'
        }
      },
      videos: [
        require('@/assets/leader/video/video1.mp4'),
        require('@/assets/leader/video/video2.mp4'),
        require('@/assets/leader/video/video4.mp4'),
        require('@/assets/leader/video/video8.mp4')
      ],
      cameraUrl1: null,
      cameraUrl2: null,
      cameraUrl3: null,
      cameraUrl4: null,
      cameraId1: null,
      cameraId2: null,
      cameraId3: null,
      cameraId4: null
    }
  },
  methods: {
    handleClick(i) {
      this.checkedIndex = i
    },
    close() {
      this.$emit('close')
    },
    async cameraMarker() {
      const res = await getCameraMakers()
      if (res && res.length > 0) {
        let filerRes = res.filter(item => item.id)
        console.log('filerRes', filerRes)
        this.cameraId1 = filerRes[4].id
        this.cameraId2 = filerRes[5].id
        this.cameraId3 = filerRes[7].id
        this.cameraId4 = filerRes[9].id

        this.cameraXq1(this.cameraId1)
        this.cameraXq2(this.cameraId2)
        this.cameraXq3(this.cameraId3)
        this.cameraXq4(this.cameraId4)
      }
    },
    async cameraXq1(id) {
      const res = await getCameraXq(id)
      if (res && res.body && res.body.data && res.body.data.length > 0 && res.body.data[0].url) {
        this.cameraUrl1 = res.body.data[0].url
      }
    },
    async cameraXq2(id) {
      const res = await getCameraXq(id)
      if (res && res.body && res.body.data && res.body.data.length > 0 && res.body.data[0].url) {
        this.cameraUrl2 = res.body.data[0].url
      }
    },
    async cameraXq3(id) {
      const res = await getCameraXq(id)
      if (res && res.body && res.body.data && res.body.data.length > 0 && res.body.data[0].url) {
        this.cameraUrl3 = res.body.data[0].url
      }
    },
    async cameraXq4(id) {
      const res = await getCameraXq(id)
      if (res && res.body && res.body.data && res.body.data.length > 0 && res.body.data[0].url) {
        this.cameraUrl4 = res.body.data[0].url
      }
    }
  },
  mounted() {
    console.log(this.processList)
    this.cameraMarker()
  }
}
</script>

<style lang="less" scoped>
.general-disposal {
  width: 1920px;
  height: 989px;
  position: absolute;
  top: 91px;
  display: flex;
  justify-content: space-between;
  .left {
    width: 538px;
    display: flex;
    justify-content: space-between;
    padding-top: 67px;
    padding-left: 50px;
    position: relative;
    z-index: 1003;
    background: linear-gradient(270deg, rgba(0, 0, 0, 0.1) 0%, #060c10 100%);
    .back {
      position: absolute;
      left: 52px;
      top: 0;
      display: flex;
      align-items: center;
      gap: 14px;
      font-size: 22px;
      font-family: PingFangSC, PingFang SC;
      line-height: 25px;
      letter-spacing: 2px;
      background: linear-gradient(180deg, #ffffff 0%, #7ef5ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      cursor: pointer;
      .icon {
        width: 39px;
        height: 39px;
        background: url('~@/assets/zhdd/back.png') no-repeat;
      }
    }
  }
  .right {
    width: 538px;
    display: flex;
    justify-content: space-between;
    padding-top: 32px;
    padding-left: 28px;
    position: relative;
    z-index: 1003;
    background: linear-gradient(270deg, #060c10 0%, rgba(0, 0, 0, 0.1) 100%);
  }
  .basic-info {
    width: 100%;
    height: 100%;
    padding: 15px 10px 0 12px;
    ul {
      display: flex;
      flex-direction: column;
      li {
        position: relative;
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 44px;
        padding: 0 20px 0 32px;
        &::before {
          content: '*';
          position: absolute;
          left: 12px;
          font-size: 18px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #8bc7ff;
        }
        &:nth-child(odd) {
          background: rgba(0, 101, 183, 0.15);
        }
        &:last-child {
          height: 52px;
        }
        .label {
          width: 90px;
          text-align: left;
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #abdcf4;
        }
        .value {
          width: calc(100% - 90px);
          text-align: right;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
        }
        .urgent1 {
          width: 49px;
          height: 21px;
          text-overflow: inherit;
          color: #ff6600;
        }
        .urgent2 {
          width: 49px;
          height: 21px;
          text-overflow: inherit;
          color: #ffa061;
        }
        .urgent3 {
          width: 49px;
          height: 21px;
          text-overflow: inherit;
          color: #d8c32f;
        }
        .urgent4 {
          width: 49px;
          height: 21px;
          text-overflow: inherit;
          color: #30a1f0;
        }
      }
    }
  }
  .process-flow {
    width: 100%;
    height: 100%;
    padding: 15px 10px 0 12px;
    overflow-y: scroll;
    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 6px;
      background: transparent;
      // border: 1px solid #999;
      /*高宽分别对应横竖滚动条的尺寸*/
      // height: 1px;
    }

    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 2px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: rgb(45, 124, 228);
      height: 100px;
    }
    .time_line {
      .time_line_item {
        position: relative;
        width: 100%;
        height: auto;
        padding-left: 30px;
        padding-bottom: 10px;

        &::before {
          position: absolute;
          content: '';
          width: 10px;
          height: 10px;
          background-color: #00eaff;
          border-radius: 50%;
          left: 0;
          top: 5px;
        }

        &::after {
          position: absolute;
          content: '';
          width: 1px;
          height: calc(100% - 5px);
          left: 4.5px;
          top: 11px;
          background-color: #add3ff;
        }
        &:last-child {
          &::after {
            position: absolute;
            content: '';
            width: 1px;
            height: 0;
            left: 4.5px;
            top: 11px;
            background-color: #add3ff;
          }
        }

        .basic_info {
          display: flex;
          flex-wrap: wrap;
          line-height: 21px;
          padding-left: 4px;
          align-items: center;
          margin-bottom: 13px;

          .time {
            width: 155px;
            height: 21px;
            background: url('~@/assets/map/dialog/bg2.png') no-repeat center / 100% 100%;
            font-size: 13px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #00eaff;
            margin-right: 22px;
          }

          .name,
          .grid,
          .state {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #00eaff;
            margin-right: 22px;
          }
          .line {
            width: 1px;
            height: 12px;
            background: #375289;
            margin: 0 17px;
          }
        }

        .detail_info {
          width: 100%;
          height: auto;
          padding: 16px 0;
          background: rgba(12, 128, 196, 0)
            linear-gradient(180deg, #172639 0%, rgba(28, 44, 67, 0) 100%);
          border: 1px solid;
          border-image: linear-gradient(180deg, rgba(50, 75, 126, 1), rgba(50, 75, 126, 0)) 1 1;

          ul {
            display: flex;
            width: 100%;
            flex-wrap: wrap;
            justify-content: space-between;
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #6a92bb;
            line-height: 24px;

            li {
              padding: 0 19px;
              display: flex;
              width: 100%;
              height: 40px;
              line-height: 40px;
              justify-content: space-between;
              text-align: left;
              &:nth-child(even) {
                background: rgba(0, 101, 183, 0.15);
              }

              .lab {
                min-width: 100px;
                color: #9de2ff;
              }

              .val {
                text-align: right;
                color: #ffffff;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                &.imgs {
                  overflow: inherit;
                  cursor: pointer;
                }
              }
            }
          }
        }
      }
    }
  }
  .video-wrap {
    width: 100%;
    height: 100%;
    padding: 23px 61px 0;
    ul {
      width: 100%;
      height: 100%;
      li {
        width: 337px;
        height: 189px;
        margin-bottom: 15px;
      }
    }
  }
}
/* 附件 */
.swiper {
  margin-top: 20px;
}
.swiper-slide {
  width: 200px;
  height: 114px;
  :deep(.el-image) {
    width: 200px;
    height: 114px;
  }
}
:deep(.el-image) {
  width: 65px;
  height: 40px;
}
</style>
