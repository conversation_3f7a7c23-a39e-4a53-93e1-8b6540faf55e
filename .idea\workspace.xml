<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="c1b82499-f660-47de-aa48-8a25904744af" name="默认变更列表" comment="屏幕共享功能提交">
      <change beforePath="$PROJECT_DIR$/src/App.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/App.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/mixins/rtc.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/mixins/rtc.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/router/leader/router.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/router/leader/router.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/leader/components/service/agreeBoard.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/leader/components/service/agreeBoard.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/leader/components/service/miniumVideoDialog.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/leader/components/service/miniumVideoDialog.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/leader/components/service/multipleMeeting.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/leader/components/service/multipleMeeting.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/leader/components/service/style/multipleMeetingStyle.scss" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/leader/components/service/style/multipleMeetingStyle.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/leader/components/service/tool/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/leader/components/service/tool/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/leader/invite.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/leader/invite.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/vue.config.js" beforeDir="false" afterPath="$PROJECT_DIR$/vue.config.js" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="dev-zyjd-0915" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectId" id="2yEHJmAN4hDNsa5ZNHQtVG3iOIW" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
    <option name="showMembers" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="aspect.path.notification.shown" value="true" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$/../njdp-视频会商" />
    <property name="nodejs_package_manager_path" value="npm" />
    <property name="settings.editor.selected.configurable" value="configurable.group.appearance" />
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="c1b82499-f660-47de-aa48-8a25904744af" name="默认变更列表" comment="" />
      <created>1749392747437</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749392747437</updated>
      <workItem from="1749392751482" duration="4059000" />
      <workItem from="1749545349049" duration="2274000" />
      <workItem from="1749625600912" duration="1036000" />
      <workItem from="1749791630477" duration="1189000" />
      <workItem from="1750141754280" duration="1789000" />
      <workItem from="1750318286000" duration="770000" />
      <workItem from="1750662114871" duration="1665000" />
      <workItem from="1754291508027" duration="16000" />
      <workItem from="1754293507037" duration="49000" />
      <workItem from="1754293634843" duration="12000" />
      <workItem from="1757922754130" duration="866000" />
      <workItem from="1758000415541" duration="2834000" />
      <workItem from="1758079579684" duration="616000" />
    </task>
    <task id="LOCAL-00001" summary="111">
      <created>1749394726031</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1749394726031</updated>
    </task>
    <task id="LOCAL-00002" summary="111">
      <created>1749394900096</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1749394900096</updated>
    </task>
    <task id="LOCAL-00003" summary="111">
      <created>1749395009067</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1749395009067</updated>
    </task>
    <task id="LOCAL-00004" summary="1111222">
      <created>1749395966023</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1749395966023</updated>
    </task>
    <task id="LOCAL-00005" summary="1111222">
      <created>1749396540875</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1749396540875</updated>
    </task>
    <task id="LOCAL-00006" summary="1111222">
      <created>1749547276978</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1749547276978</updated>
    </task>
    <task id="LOCAL-00007" summary="分屏功能提交（已注，暂不启用）">
      <created>1750319191883</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1750319191883</updated>
    </task>
    <task id="LOCAL-00008" summary="屏幕共享功能提交">
      <created>1750667172261</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1750667172261</updated>
    </task>
    <option name="localTasksCounter" value="9" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
    <option name="oldMeFiltersMigrated" value="true" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="111" />
    <MESSAGE value="1111222" />
    <MESSAGE value="分屏功能提交（已注，暂不启用）" />
    <MESSAGE value="屏幕共享功能提交" />
    <option name="LAST_COMMIT_MESSAGE" value="屏幕共享功能提交" />
  </component>
</project>