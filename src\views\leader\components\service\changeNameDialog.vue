<template>
  <div class="call-board" @click.self="changeNameClose">
    <el-input v-model="name" placeholder="请输入用户名称"></el-input>
    <div class="call-board-btn" @click.stop="updateName">修改</div>
  </div>
</template>

<script>
  // import { validateCallNum } from "./tool/index"
  export default {
    name: 'changeName',
    props: {
      identity: {
        type: String,
        default: ''
      },
    },
    data() {
      return {
        name: ""
      }
    },
    methods: {
      updateName() {
        if(this.name) {
          if(this.name.length > 20) {
            this.$messageNew.message('error', {
              message: "名称最多为20个字符"
            })
          } else {
            this.$emit('updateParticipantName', this.name, this.identity)
          }
        } else {
          this.$message({
            type: 'error',
            message: '要修改的名称不能为空'
          })
        }
      },
      changeNameClose() {
        this.$emit('changeNameDialogClose')
      }
    }
  }
</script>

<style lang="scss" scoped>
  .call-board {
    width: 341px;
    height: 79px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background: url('~@/assets/service/board-bg.png') no-repeat center / 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    &-btn {
      width: 88px;
      line-height: 36px;
      text-align: center;
      background: url('~@/assets/service/board-btn-bg.png') no-repeat center / 100% 100%;
      cursor: pointer;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #FFFFFF;
    }
    ::v-deep {
      .el-input {
        width: 200px !important;
        margin-right: 10px;
        .el-input__inner {
          height: 32px;
          line-height: 32px;
          border: 1px solid #CBE5FF !important;
          background-color: transparent !important;
          font-size: 16px !important;
          color: #FFFFFF !important;
        }
      }
    }
  }
</style>