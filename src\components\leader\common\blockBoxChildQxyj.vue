<template>
	<div class="child-block">
		<slot></slot>

		<div v-if="!loading && data.length > 0" class="content-box">
			<div class="content-item-box" v-for="(it, i) in data" :key="i">
				<div class="column1-box">
					<img :src="it.weatherIConUrl" alt="" />

					<!-- <div class="column1-icon-box">{{ it.label }}</div> -->
				</div>
				<div class="column2-box">
					<div class="column2-item-box" v-for="(item_, index) in it.data" :key="index" @click="handleClickQxyjItem(i, index)">
						<div class="item-number-box">{{ item_.number }}</div>
						<div class="item-label-box" :style="{ background: item_.color }">{{ item_.label }}</div>
					</div>
				</div>
			</div>
		</div>
		<div v-else-if="loading" class="alert-empty content-box">加载中...</div>
		<div v-else-if="data.length == 0" class="alert-empty content-box">暂无数据</div>
	</div>
</template>

<script>
export default {
	props: {
		data: {
			type: Array,
			default: () => {},
		},
		loading: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {}
	},
	computed: {},
	methods: {
		handleClickQxyjItem(i1, i2) {
			this.$emit('clickAlertLevelItem', i1, i2)
		},
	},

	watch: {},
}
</script>

<style lang="less" scoped>
.child-block {
	width: 451px;
	height: 100%;
	box-sizing: border-box;
	z-index: 101;
	position: relative;
	overflow-y: auto;

	&::-webkit-scrollbar {
		width: 4px; /* 滚动条宽度 */
	}

	//轨道
	&::-webkit-scrollbar-track {
		background: #04244e; /* 滚动条轨道背景 */
		border-radius: 10px;
		padding: 5px 0;
	}

	//滑块
	&::-webkit-scrollbar-thumb {
		background: #82aed0; /* 滑块背景颜色 */
		border-radius: 10px;
		height: 15px;
	}

	//悬停滑块颜色
	&::-webkit-scrollbar-thumb:hover {
		background: #ff823b; /* 鼠标悬停时滑块颜色 */
	}

	//箭头
	&::-webkit-scrollbar-button {
		display: none; /* 隐藏滚动条的箭头按钮 */
	}

	.content-box {
		width: 100%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: flex-start;
		padding-top: 16px;
		padding-bottom: 14px;

		.content-item-box {
			width: 100%;
			height: 65px;
			padding: 5px 10px;
			padding-left: 20px;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			cursor: pointer;

			.column1-box {
				width: 52px;
				height: 52px;
				margin-right: 20px;

				img {
					width: 100%;
					height: 100%;
				}

				.column1-icon-box {
					width: 100%;
					height: 100%;
					background: skyblue;

					font-family: PingFang SC, PingFang SC;
					font-size: 14px;
					line-height: 37px;
					color: #000;
					text-align: center;
					font-style: normal;
					text-transform: none;
					overflow: hidden; /* 隐藏溢出文本 */
					display: -webkit-box; /* 使用弹性盒子模型 */
					-webkit-line-clamp: 2; /* 限制为两行 */
					-webkit-box-orient: vertical; /* 设置文本的排列方向 */
					text-overflow: ellipsis; /* 在文本溢出时显示省略号 */
				}
			}

			.column2-box {
				width: calc(100% - 37px - 20px);
				height: 100%;
				display: flex;
				justify-content: space-between;
				align-items: center;

				.column2-item-box {
					width: 70px;
					height: 100%;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;

					.item-number-box {
						width: 100%;
						font-family: DINPro, DINPro;
						// font-weight: bold;
						// font-size: 22px;
						// color: #5ee1e3;
						// line-height: 22px;
						// text-align: center;
						// font-style: normal;
						// text-transform: none;
						// margin-bottom: 10px;

						font-family: PangMenZhengDao-3, PangMenZhengDao-3;
						font-weight: 500;
						font-size: 20px;
						color: #dbf1ff;
						text-shadow: 0px 0px 10px #bddcf1;
						text-align: center;
						font-style: normal;
						text-transform: none;
					}

					.item-label-box {
						width: 100%;
						height: 26px;
						display: flex;
						justify-content: center;
						align-items: center;
						font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
						font-weight: normal;
						font-size: 14px;
						color: #ffffff;
						text-align: center;
						font-style: normal;
						text-transform: none;
					}
				}
			}
		}
	}

	.alert-empty {
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		font-family: PangMenZhengDao-3, PangMenZhengDao-3;
		font-weight: 500;
		font-size: 20px;
		color: #dbf1ff;
		text-shadow: 0px 0px 10px #bddcf1;
		text-align: center;
		font-style: normal;
		text-transform: none;
	}
}

.popDropDown {
	::v-deep .el-dropdown {
		color: #caecff;
		font-family: YouSheBiaoTiHei;
		font-size: 16px;
		line-height: 21px;
		text-align: right;
		font-style: normal;
		line-height: 40px;
		right: 0;
		position: absolute;
	}
	::v-deep .el-dropdown-menu {
		background-color: #00173b !important;
		border: 1px solid #2b7bbb !important;
	}
	.el-dropdown-menu__item {
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
		line-height: 40px;
		padding: 0 30px;
		&:hover {
			background: #185493;
			color: rgba(255, 255, 255, 0.8);
		}
	}
}
</style>
