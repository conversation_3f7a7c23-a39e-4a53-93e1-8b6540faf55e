<template>
  <div class="stlyJq">
    <div class="tkTitle">
      <span>介绍</span>
    </div>
    <img @click="closeEmitai" src="@/assets/zhny/map/tkGb.png" class="tkGb" />
    <div class="tkContent">
      <iframe class="pdf_" :src="filePdfUrl" frameborder="0"></iframe>
    </div>
  </div>
</template>

<script>
import BlockBox from '@/components/leader/common/blockBox.vue'
export default {
  components: { BlockBox },
  name: 'hswhPop',
  props: {
    filePdfUrl: {
      type: String,
      default: '',
    },
  },
  data() {
    return {}
  },
  mounted() {},
  beforeDestroy() {},
  methods: {
    closeEmitai() {
      this.$emit('close')
    },
  },
}
</script>

<style lang="less" scoped>
.stlyJq {
  width: 700px;
  height: 853px;
  // background: url(~@/assets/zhny/map/tkBg.png) no-repeat center / 100% 100%;
  background: url(~@/assets/hszl/tkBg.png) no-repeat center / 100% 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10001;
  padding: 0 43px;
  .tkTitle {
    // width: 1054px;
    height: 74px;
    background: url(~@/assets/zhny/map/tkTitle.png) no-repeat center / 100% 100%;
    margin: 0 auto;
    margin-top: 22px;
    line-height: 74px;
    span {
      font-size: 36px;
      font-family: PangMenZhengDao;
      color: #ffffff;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .tkGb {
    position: absolute;
    top: 38px;
    right: 43px;
    cursor: pointer;
  }
  .tkContent {
    width: 100%;
    height: calc(100% - 74px - 44px);
    .pdf_ {
      margin-top: 10px;
      width: 100%;
      height: 90%;
    }
  }
}
</style>
