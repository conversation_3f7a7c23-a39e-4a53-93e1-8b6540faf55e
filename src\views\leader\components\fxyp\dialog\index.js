/*
 * @Author: fanjialong <EMAIL>
 * @Date: 2023-04-27 13:23:04
 * @LastEditors: fanjialong <EMAIL>
 * @LastEditTime: 2023-04-27 13:23:13
 * @FilePath: \hs_dp\src\views\leader\components\fxyp\dialog\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%A
 */
import sjslRatePop from './sjslRatePop.vue'
import yqinfoPop from './yqinfoPop.vue'
import yjsjPop from './yjsjPop.vue'
import SocialSmallPlaceDetail from './SocialSmallPlaceDetail.vue'
export {
  sjslRatePop,
  yqinfoPop,
  yjsjPop,
  SocialSmallPlaceDetail
}
