<template>
  <div class="my_robot">
    <div class="time-box" v-show="soundFlag">
      <span class="start-taste-line">
        <hr class="hr1" />
        <hr class="hr2" />
        <hr class="hr3" />
        <hr class="hr4" />
        <hr class="hr5" />
        <hr class="hr6" />
        <hr class="hr7" />
        <hr class="hr8" />
        <hr class="hr9" />
        <hr class="hr10" />
      </span>
    </div>

    <div class="robot_pop" v-if="clNum == 1 && !getMsgResultShow">
      <div class="text-wrapper_84">
        <span class="text_205">您好:</span>
        <span class="text_206">我是数字机器人“小管”,</span>
        <span class="text_207">&nbsp;很高兴认识您!</span>
        <!-- <span class="text_208">打开页面、查看模块详情</span> -->
        <!-- <span class="text_209">,</span> -->
        <span class="text_210">&nbsp;请说出您的需求。</span>
      </div>
    </div>
    <div class="robot_pop2" v-if="clNum == 0 && !getMsgResultShow && initText">{{ initText }}</div>
    <div class="robot_pop2" v-if="clNum == 1 && getMsgResultShow && initText">{{ initText }}</div>
    <video
      ref="myVideo"
      @click="clickRobot"
      src="~@/assets/leader/img/zwfw/robot.mp4"
      muted="true"
      loop="false"
      style="
        width: 144px;
        mix-blend-mode: screen;
        position: absolute;
        z-index: 9999999;
        left: 65%;
        bottom: 156px;
      "
    ></video>
    <!-- </div> -->
    <div>
      <audio ref="myAudio" @ended="audioEnded">
        <source :src="voiceData" />
      </audio>
    </div>
    <chatH5Valiless ref="chatH5Valiless" @msgResult="getMsgResult" :soundFlag='soundFlag' :lying="lying" />
  </div>
</template>

<script>
import chatH5Valiless from '@/components/robot/old/chat_h5-valiless1.vue'
import { voiceData1, voiceData2, voiceData3 } from './robot.json'
export default {
  components: {
    chatH5Valiless,
  },
  data() {
    return {
      initFlag: true,
      showRobot: false,
      showRobot2: false,
      voiceData: voiceData2,
      // voiceFlag: true,
      initText: '',
      clNum: 0,
      soundFlag: false,
      lying: false, //录音状态
      getMsgResultShow: false, //对话返回
    }
  },
  created() {},
  mounted() {},
  methods: {
    clickRobot() {
      console.log('this.clNum', this.clNum)
      this.clNum++
      if (this.clNum == 1) {
        this.voiceData = voiceData2
        this.$nextTick(() => {
          const { myAudio } = this.$refs
          myAudio.load()
          myAudio.play()
          this.soundFlag = true
        })
        this.$refs.myVideo.play()
        this.$refs.chatH5Valiless.recOpen()
        // 调用组件的方法 开始录制 开始
        setTimeout(() => {
          this.$refs.myVideo.pause()
        }, 2000)
      } else {
        this.voiceData = voiceData3
        this.initText = '感谢使用，期待下次再见'
        this.$nextTick(() => {
          const { myAudio } = this.$refs
          myAudio.load()
          myAudio.play()
          this.soundFlag = true
        })
        this.$refs.chatH5Valiless.recClose()
        this.clNum = 0
        this.getMsgResultShow = false
        // 隐藏提示框
        setTimeout(() => {
          this.clNum = 0
          this.getMsgResultShow = false
          this.initText = ''
        }, 3000)
      }
    },
    audioEnded() {
      this.soundFlag = false
    },
    getMsgResult(data) {
      console.log('data', data)
      const { botType, answer } = data
      const { tts, msg } = answer[0]
      console.log('botType', botType)
      console.log('msg', msg)
      console.log('tts', tts)
      this.voiceData = tts
      this.initText = msg
      this.$nextTick((res) => {
        const { myAudio } = this.$refs
        myAudio.load()
        myAudio.play()
        this.soundFlag = true
        this.clNum = 1
        this.getMsgResultShow = true
      })
      if (botType === 'flow') {
        // console.log(777, msg.includes('产业经济'))
        const num = Math.floor(Math.random() * 10) + 1
        if (msg.includes('城市态势')) {
          this.$router.push('/leaderCsts')
        } else if (msg.includes('社会治理')) {
          this.$router.push('/leaderZt')
        } else if (msg.includes('产业经济')) {
          this.$router.push('/leaderCyjj')
        } else if (msg.includes('政务服务')) {
          this.$router.push('/leaderZwfw')
        } else if (msg.includes('业务能力')) {
          this.$router.push({ path: '/leaderZwfw', query: { flag: `ywnl${num}` } })
        } else if (msg.includes('挖掘关联')) {
          this.$router.push({ path: '/leaderZwfw', query: { flag: `wjgl${num}` } })
        } else if (msg.includes('主题聚焦')) {
          this.$router.push({ path: '/leaderZwfw', query: { flag: `ztjj${num}` } })
        } else if (msg.includes('运行监控')) {
          this.$router.push({ path: '/leaderZwfw', query: { flag: `yxjk${num}` } })
        } else if (msg.includes('事件总览')) {
          this.$router.push({ path: '/leaderZt', query: { flag: `sjzl${num}` } })
        } else if (msg.includes('预警事件')) {
          this.$router.push({ path: '/leaderZt', query: { flag: `yjsj${num}` } })
        } else if (msg.includes('视频监控')) {
          this.$router.push({ path: '/leaderZt', query: { flag: `spjk${num}` } })
        } else if (msg.includes('党建引领')) {
          this.$router.push({ path: '/leaderCsts', query: { flag: `djyl${num}` } })
        } else if (msg.includes('城市管理')) {
          this.$router.push({ path: '/leaderCsts', query: { flag: `csgl${num}` } })
        }
      }
    },
    closeYy() {
      this.$refs.chatH5Valiless.recClose()
    },
    lyingMethod() {
      console.log(111)
      this.clNum = 1 
      this.getMsgResultShow = true
      this.initText = ''
      this.$nextTick(() => {
        const { myAudio } = this.$refs
        myAudio.pause()
        this.soundFlag = false
      })
    },
  },
  computed: {},
}
</script>
<style lang="scss" scoped>
.my_robot {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  position: relative;
  bottom: -80px;
  height: 0;
  .robot_pop {
    position: absolute;
    left: 59%;
    bottom: 252px;
    z-index: 1200;
    background: url(~@/assets/leader/img/zwfw/bg12.png) no-repeat center / 100% 100%;
    // box-shadow: inset 0px 1px 3px 1px rgba(88, 215, 255, 0.5);
    // height: 163px;
    width: 267px;
    padding: 16px 20px;
  }
  .robot_pop2 {
    position: absolute;
    left: 61.5%;
    bottom: 280px;
    z-index: 1200;
    background: url(~@/assets/leader/img/zwfw/bg12.png) no-repeat center/100% 100%;
    // box-shadow: inset 0px 1px 3px 1px rgb(88 215 255 / 50%);
    padding: 16px 20px;
    width: 200px;
    padding: 16px 20px;
    font-size: 16px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    color: #ffffff;
    line-height: 22px;
    word-wrap: break-word;
    text-align: left;
  }
  .text-wrapper_84 {
    width: 226px;
    // height: 112px;
    overflow-wrap: break-word;
    font-size: 0;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    text-align: justify;
    line-height: 22px;
  }

  .text_205 {
    width: 226px;
    height: 112px;
    overflow-wrap: break-word;
    color: rgba(194, 221, 252, 1);
    font-size: 16px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    text-align: left;
    line-height: 22px;
  }

  .text_206 {
    width: 226px;
    height: 112px;
    overflow-wrap: break-word;
    color: rgba(0, 220, 244, 1);
    font-size: 16px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    text-align: left;
    line-height: 22px;
  }

  .text_207 {
    width: 226px;
    height: 112px;
    overflow-wrap: break-word;
    color: rgba(194, 221, 252, 1);
    font-size: 16px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    text-align: left;
    line-height: 22px;
  }

  .text_208 {
    width: 226px;
    height: 112px;
    overflow-wrap: break-word;
    color: rgba(255, 213, 94, 1);
    font-size: 16px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    text-align: left;
    line-height: 22px;
  }

  .text_209 {
    width: 226px;
    height: 112px;
    overflow-wrap: break-word;
    color: rgba(255, 211, 64, 1);
    font-size: 16px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    text-align: left;
    line-height: 22px;
  }

  .text_210 {
    width: 226px;
    height: 112px;
    overflow-wrap: break-word;
    color: rgba(194, 221, 252, 1);
    font-size: 16px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    text-align: left;
    line-height: 22px;
  }
  .time-box {
    position: absolute;
    left: 68%;
    bottom: 245px;
  }
  .time-box .start-taste-line hr {
    background-color: #18e0ff; //声波颜色
    width: 3px;
    height: 3px;
    margin: 0 0.03rem;
    display: inline-block;
    border: none;
  }
  hr {
    animation: note 0.5s ease-in-out;
    animation-iteration-count: infinite;
    animation-direction: alternate;
  }

  .hr1 {
    animation-delay: -1s;
  }

  .hr2 {
    animation-delay: -0.9s;
  }

  .hr3 {
    animation-delay: -0.8s;
  }

  .hr4 {
    animation-delay: -0.7s;
  }

  .hr5 {
    animation-delay: -0.6s;
  }

  .hr6 {
    animation-delay: -0.5s;
  }

  .hr7 {
    animation-delay: -0.4s;
  }

  .hr8 {
    animation-delay: -0.3s;
  }

  .hr9 {
    animation-delay: -0.2s;
  }

  .hr10 {
    animation-delay: -0.1s;
  }

  @keyframes note {
    from {
      transform: scaleY(1);
    }
    to {
      transform: scaleY(4);
    }
  }
}
</style>