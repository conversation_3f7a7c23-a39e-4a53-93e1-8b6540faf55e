<template>
	<div class="child-block">
		<div class="content" v-if="!loading && contentDataList.length > 0">
			<slot></slot>
			<div
				class="data-item"
				v-for="(item, index) in contentDataList"
				:key="index"
				@click="
					() => {
						item.func(item.argmachType)
					}
				"
			>
				<img class="data-item-icon" :src="item.iconUrl" alt="" />
				<div class="data-item-content">
					<div class="data-item-title">{{ item.title }}</div>
					<div class="data-item-number-box">
						<div class="data-item-number">{{ item.number }}</div>
						<div class="data-item-number-unit">{{ contentNumberUnitOne }}</div>
					</div>
				</div>
			</div>
		</div>
		<div v-else-if="loading" class="alert-empty content-box">加载中...</div>
		<div v-else-if="contentDataList.length == 0" class="alert-empty content-box">暂无数据</div>
	</div>
</template>

<script>
export default {
	props: {
		contentNumberUnitOne: {
			type: String,
			default: '万',
		},

		contentDataList: {
			type: Array,
			default: () => [],
		},
		loading: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			currentIndex: 0,
			isActive: true,
			isOpenOtherData: false,
			otherDataList: [],
			currentTabIndex: 0,
		}
	},
	computed: {
		otherArgmachNumber() {
			let otherArgmachNumber = 0

			this.otherDataList.forEach((item) => {
				otherArgmachNumber += Number(item.number)
			})
			return otherArgmachNumber
		},
	},
	methods: {
		clickchange(index) {
			this.currentIndex = index
			this.$emit('updateChange', this.currentIndex)
		},
		clickBulletFrame() {
			this.$emit('updateChange2', true)
		},
		showMoreFn() {
			this.$emit('handleShowMore', true)
		},
		showMoreFn2() {
			this.$emit('handleShowMore2', true)
		},
		handleClickOtherData() {
			this.isOpenOtherData = !this.isOpenOtherData
		},
		handleClickCloseOtherData() {
			this.isOpenOtherData = false
		},
		handleChangeTab(item, index) {
			this.currentTabIndex = index
			this.$emit('handleChangeTab', item.value)
		},
	},
}
</script>

<style lang="less" scoped>
.alert-empty {
	width: calc(100% - 20px) !important;
	height: 100%;
	display: flex;
	justify-content: center !important;
	align-items: center !important;
	font-family: PangMenZhengDao-3, PangMenZhengDao-3;
	font-weight: 500;
	font-size: 20px;
	color: #dbf1ff;
	text-shadow: 0px 0px 10px #bddcf1;
	text-align: center;
	font-style: normal;
	text-transform: none;
}
.child-block {
	width: 451px;
	height: 100%;
	box-sizing: border-box;
	z-index: 101;
	position: relative;
	padding: 16px 0 0 16px;

	.content {
		position: relative;
		width: 100%;
		height: 100%;
		// height: calc(100% - 74px);
		display: flex;
		justify-content: flex-start;
		flex-wrap: wrap;
		align-items: center;
		overflow: auto;

		&::-webkit-scrollbar {
			width: 4px; /* 滚动条宽度 */
		}

		//轨道
		&::-webkit-scrollbar-track {
			background: #04244e; /* 滚动条轨道背景 */
			border-radius: 10px;
			padding: 5px 0;
		}

		//滑块
		&::-webkit-scrollbar-thumb {
			background: #82aed0; /* 滑块背景颜色 */
			border-radius: 10px;
			height: 15px;
		}

		//悬停滑块颜色
		&::-webkit-scrollbar-thumb:hover {
			background: #ff823b; /* 鼠标悬停时滑块颜色 */
		}

		//箭头
		&::-webkit-scrollbar-button {
			display: none; /* 隐藏滚动条的箭头按钮 */
		}

		&.no-active-content {
			height: 0;
			display: none;
		}

		.data-item {
			width: 201px;
			height: 56px;
			// height: 40px;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			margin-right: 12px;
			margin-bottom: 16px;
			cursor: pointer;

			img {
				width: 48.5px;
				height: 56px;
				margin-right: 8px;
			}
		}

		.data-item:hover {
			// background: url(~@/assets/img/block-box/block-box-child-item-active-bg.png);
			/* 背景不重复 */
			background-repeat: no-repeat;
			/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
			background-size: cover;
			/* 背景居中显示（可选） */
			background-position: center;
		}

		.data-item-content {
			width: 144.5px;
			padding: 12px 18px 0 8px;
			height: 100%;
			display: flex;
			flex-direction: column;
			justify-content: flex-start;
			align-items: flex-start;

			background: url(~@/assets/img/block-box/jijv-source/jijv-source-item-bg.png);
			/* 背景不重复 */
			background-repeat: no-repeat;
			/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
			background-size: cover;
			/* 背景居中显示（可选） */
			background-position: center;

			.data-item-title {
				width: 100%;

				font-family: OPPOSans, OPPOSans;
				font-weight: 400;
				font-size: 14px;
				color: #d0deee;
				line-height: 12px;
				text-align: left;
				font-style: normal;
				text-transform: none;
				white-space: nowrap; /* 强制文本不换行 */
				overflow: hidden; /* 超出部分隐藏 */
				text-overflow: ellipsis; /* 超出时显示省略号 */

				margin-bottom: 6px;
				line-height: 14px;
				text-align: left;
				// display: flex;
				// justify-content: flex-start;
				// align-items: center;
			}

			.data-item-number-box {
				width: 84px;
				height: 20px;
				display: flex;
				justify-content: flex-start;
				align-items: flex-end;
				.data-item-number {
					height: 20px;
					font-family: DINCondensed, DINCondensed;
					font-weight: bold;
					font-size: 20px;
					// color: #24818c;
					color: #4de4ff;

					line-height: 20px;
					font-style: normal;
					margin-right: 4px;
				}
				.data-item-number-unit {
					height: 14px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 300;
					font-size: 12px;
					// color: #4e5969;
					color: #4de4ff;

					line-height: 14px;
					font-style: normal;
				}
			}
		}
	}
}

.popDropDown {
	::v-deep .el-dropdown {
		color: #caecff;
		font-family: YouSheBiaoTiHei;
		font-size: 16px;
		line-height: 21px;
		text-align: right;
		font-style: normal;
		line-height: 40px;
		right: 0;
		position: absolute;
	}
	::v-deep .el-dropdown-menu {
		background-color: #00173b !important;
		border: 1px solid #2b7bbb !important;
	}
	.el-dropdown-menu__item {
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
		line-height: 40px;
		padding: 0 30px;
		&:hover {
			background: #185493;
			color: rgba(255, 255, 255, 0.8);
		}
	}
}
</style>
