<template>
  <div class="ypyj-container">
    <div class="main-content">
      <div class="left-box">
        <!-- <BlockBox1
          class="box box3"
          :isListBtns="false"
          :showMore="true"
          :blockHeight="890"
          @handleShowMore="showMoreFn1"
        >
          <template #tab>
            <span @click.stop="clickSjtj(0)" :class="{ active: sjtjIndex === 0 }">事件数量排名</span>
            <span @click.stop="clickSjtj(1)" :class="{ active: sjtjIndex === 1 }">处置率排名</span>
          </template>
          <div class="content">
            <swiper class="swiper swiper1" :options="swiperOption1" ref="myBotSwiper1">
              <swiper-slide
                :class="`slide${index + 1}`"
                v-for="(item, index) in box2Data"
                :key="index"
              >
                <div class="box-content">
                  <div class="area-label">{{ item.areaName }}</div>
                  <div class="text-container">
                    <div class="text-item">
                      <div class="title">
                        <span class="event-icon"></span>
                        <span class="label">事件数</span>
                      </div>

                      <span class="text">{{ item.eventNum }}</span>
                    </div>
                    <div class="text-item">
                      <div class="title">
                        <span class="deal-icon"></span>
                        <span class="label">已处置</span>
                      </div>
                      <span class="text">{{ item.dealedNum }}</span>
                    </div>
                    <div class="text-item">
                      <div class="title">
                        <span class="rate-icon"></span>
                        <span class="label">处置率</span>
                      </div>
                      <span class="text">{{ item.rate }}</span>
                    </div>
                  </div>
                  <div class="sort-icon">TOP{{ index + 1 }}</div>
                </div>
              </swiper-slide>
            </swiper>
          </div>
        </BlockBox1>-->
        <BlockBox2
          class="box box2"
          style="margin-top: -55px;"
          :isListBtns="false"
          :showMore="true"
          :blockHeight="550"
          @handleShowMore="showMoreFn4"
          title="12345平台"
        >
          <div class="content_border cont">
            <blockBox5
              title="工单总览"
              :showIcon="true"
              class="box box3"
              :isListBtns="false"
              :blockHeight="110"
              :dashWidth="120"
            >
              <AroundSources style="margin-left: -5px;" :data="gdzlData" />
            </blockBox5>
            <blockBox5
              title="工单类型"
              :showIcon="true"
              class="box box3"
              :showMore="false"
              :isListBtns="false"
              :blockHeight="240"
              :dashWidth="120"
              @handleShowMore="showMoreFn5"
            >
              <!-- <StereoscopicBarChart
                :data="box1BottomData1.data"
                :options="box1BottomData1.options"
                :init-option="{ yAxis: { name: ''}}"
              />-->
              <pieChartTsgz :data="box1BottomData1.data" :color="boxData3Option" />
            </blockBox5>
            <blockBox5
              title="首次满意度"
              :showIcon="true"
              class="box box3"
              :isListBtns="false"
              :blockHeight="110"
              :dashWidth="150"
            >
              <!-- <AroundSources :data="gdzlData" /> -->
              <!-- <horStackBarChart :data="stastifiData" /> -->
              <FirstSatisfy :data="satisfaction" />
            </blockBox5>
          </div>
        </BlockBox2>
        <BlockBox2
          class="box box2"
          :isListBtns="false"
          :showMore="true"
          :blockHeight="402"
          @handleShowMore="showMoreFn1"
          title="数字城管平台"
        >
          <div class="cont content_border">
            <div style="height: 80px">
              <AroundSources :data="szcgData" />
            </div>
            <PieChart3D
              type="1"
              style="height: calc(100% - 80px)"
              :data="boxDataGh"
              :options="boxOptionsGh"
              :init-option="boxInitoptionsGh"
            />
          </div>
        </BlockBox2>
      </div>
      <div class="block_title1">智慧江宁工单平台</div>
      <div class="center-box">
        <div class="up-center-container">
          <!-- <div class="content1">
            <div class="item" v-for="(item, index) in centerData1" :key="index">
              <div class="label">{{ item.label }}</div>
              <div class="num">
                <countTo
                  ref="countTo"
                  :startVal="$countTo.startVal"
                  :decimals="$countTo.decimals(item.num)"
                  :endVal="item.num"
                  :duration="$countTo.duration"
                />
                <span class="unit">{{ item.unit }}</span>
              </div>
            </div>
          </div>-->
          <div class="content2">
            <div class="ball1">
              <!-- <div class="ball_echart" ref="ball1"></div> -->
              <div class="ball_echart1">
                <div>
                  {{ point }}
                  <span>%</span>
                </div>
                <div>办结率</div>
              </div>
              <!-- <div
                v-for="i in 10"
                :key="i"
                class="line"
                :class="`line${i}`"
                :style="{ backgroundImage: 'url(' + require('@/assets/fxyp/center_bg.png') + ')' }"
              />-->
            </div>

            <!-- <div class="item" v-for="(item, index) in centerData2" :key="index">
              <div class="num">{{ item.num > 9 ? item.num : '0' + item.num }}</div>
              <div class="label">{{ item.label }}</div>
            </div>-->
            <div class="item1" v-for="(item, index) in frameData" :key="index">
              <template v-if="index < 3">
                <img :src="item.url" alt />
                <div class="second_box">
                  <div class="item_value">{{ item.value }}</div>
                  <div class="under_line"></div>
                  <div class="item_name">{{ item.name }}</div>
                </div>
              </template>
              <template v-if="index >= 3">
                <div class="second_box" style="align-items: flex-end">
                  <div class="item_value">{{ item.value }}</div>
                  <div class="under_line"></div>
                  <div class="item_name">{{ item.name }}</div>
                </div>
                <img :src="item.url" alt />
              </template>
            </div>
          </div>
        </div>
        <div class="center-bottom-container">
          <div class="bot_title">
            <img src="@/assets/fxyp/icon_title.png" alt />
            社区排名
            <div class="box_dashed"></div>
          </div>
          <div class="content">
            <SwiperTable
              :titles="['排名', '社区名称', '事件总数', '未办结', '已办结', '办结率']"
              :widths="['10%', '18%', '18%', '18%', '18%', '18%']"
              :data="box2Data"
              :contentHeight="'220px'"
            ></SwiperTable>
          </div>
        </div>
      </div>
      <div class="right-box">
        <blockBox5
          title="工单来源"
          :showIcon="true"
          class="box box3"
          :isListBtns="false"
          :dashWidth="100"
          :blockHeight="290"
        >
          <div class="chart1">
            <PieChart3D type="1" :data="box3Data" :options="box3Options" />
          </div>
          <div class="chart2">
            <!-- <BarChart :data="box4Data" :init-option="box4InitOptions" /> -->
          </div>
        </blockBox5>
        <blockBox5
          class="box box3"
          :isListBtns="false"
          :showIcon="true"
          :dashWidth="166"
          :blockHeight="308"
          :textArr="['视频AI', '数据分析', '物联网']"
          @updateChange="clickZbtj"
        >
          <template #tab>
            <span @click.stop="clickGfsx(0)" :class="{ active: gfsxIndex === 0 }">高发事项</span>
            <span @click.stop="clickGfsx(1)" :class="{ active: gfsxIndex === 1 }">诉求类型</span>
          </template>
          <PieChart3D
            type="2"
            v-if="gfsxIndex === 0"
            :data="pieData1"
            :initOption="box5InitOptions"
            :options="box5Options"
          />
          <FlyChart v-if="gfsxIndex === 1" :data="box4Data" :init-option="box4InitOptions" />
        </blockBox5>
        <blockBox5
          title="工单趋势"
          class="box box2"
          :blockHeight="280"
          :isListBtns="true"
          :showIcon="true"
          :textArr="['全年', '近七天']"
          @updateChange="workOrder"
          :dashWidth="200"
          :blockBg="blockBg"
        >
          <TrendLineChart
            :data="gdqsChartData"
            :options="gdqsOptions"
            :initOption="gdqsInitOption"
          />
        </blockBox5>
      </div>
    </div>
    <!-- 事件数量排名更多 -->
    <sjslRatePop v-model="detailDialogShow1" />
    <!-- 预警事件更多 -->
    <yjsjPop v-model="detailDialogShow2" />
    <!-- 舆情事件更多 -->
    <yqinfoPop v-model="detailDialogShow3" />
    <!-- <div class="bottom-tabs-container"></div> -->
  </div>
</template>

<script>
import BlockBox from '@/components/leader/common/blockBox.vue'
import BlockBox1 from '@/components/leader/common/blockBox1.vue'
import BlockBox2 from '@/components/leader/common/blockBox2.vue'
import blockBox5 from '@/components/leader/common/blockBox5.vue'
import blockBoxLonger from '@/components/leader/common/blockBoxLonger.vue'
import pieChartShzl from '@/components/ypfx/pieChartShzl.vue'
import SwiperTable from '@/components/ypfx/swipertable.vue'
import pieChartZb from '@/components/ypfx/pieChartZb.vue'
import BarChartYl from '@/components/ypfx/BarChartYl.vue'
import SingleBar from '@/components/ypfx/SingleBar.vue'
// 弹窗
import { sjslRatePop, yqinfoPop, yjsjPop } from './components/fxyp/dialog'
import pieChartTsgz from '@/components/tsgz/pieChartFxyp.vue'
import AroundSources from '@/components/ypfx/AroundSources.vue'
import FirstSatisfy from '@/components/ypfx/FirstSatisfy.vue'
import {
  getOrderVo,
  getTypeOrderCount,
  getEmeOrderCount,
  getOrderCountByDate,
} from '@/api/hs/hs.api.js'
import { get12345Work, queryCountVos, queryBusinessCount, queryOrderCount, querySatisfaction, getOrderVoRank } from '@/api/hs/hs.tsgz.js'

export default {
  components: {
    BlockBox,
    BlockBox1,
    BlockBox2,
    blockBox5,
    blockBoxLonger,
    pieChartShzl,
    SwiperTable,
    pieChartZb,
    BarChartYl,
    SingleBar,
    sjslRatePop,
    yqinfoPop,
    yjsjPop,
    pieChartTsgz,
    AroundSources,
    FirstSatisfy
  },
  data () {
    return {
      satisfaction: {
        value1: 0,
        value2: 0,
        value3: 0
      },
      gfsxIndex: 0,
      centerData1: [
        {
          label: '预警总数',
          num: 471,
          unit: '/个',
        },
        {
          label: '待处置',
          num: 103,
          unit: '/个',
        },
        {
          label: '已处置',
          num: 368,
          unit: '/个',
        },
        {
          label: '处置率',
          num: 78.2,
          unit: '%',
        },
      ],
      centerData2: [
        {
          label: '突发事件',
          num: 7,
        },
        {
          label: '紧急事件',
          num: 3,
        },
        {
          label: '重要事件',
          num: 12,
        },
        {
          label: '普通事件',
          num: 49,
        },
      ],
      stastifiData: [
        [
          "product",
          "name1",
          "name2",
          "name3"
        ],
        [
          "",
          45,
          20,
          16
        ]
      ],
      frameData: [
        {
          name: '工单总数',
          value: '300',
          url: require('@/assets/fxyp/box_icon1.png'),
        },
        {
          name: '已办结',
          value: '203',
          url: require('@/assets/fxyp/box_icon2.png'),
        },
        {
          name: '未办结',
          value: '97',
          url: require('@/assets/fxyp/box_icon3.png'),
        },
        {
          name: '非常紧急',
          value: '300',
          url: require('@/assets/fxyp/box_icon4.png'),
        },
        {
          name: '紧急',
          value: '100',
          url: require('@/assets/fxyp/box_icon5.png'),
        },
        {
          name: '一般',
          value: '200',
          url: require('@/assets/fxyp/box_icon6.png'),
        },
      ],
      detailData: [
        {
          num: 218,
          text: '待处置',
        },
        {
          num: 1868,
          text: '已处置',
        },
        {
          num: 89,
          text: '处置率',
          unit: '%',
        },
      ],
      box2Data: [
        {
          areaName: '谷里社区',
          eventNum: 1920,
          dealedNum: 1876,
          rate: '87.58%',
        },
        {
          areaName: '张溪社区',
          eventNum: 2134,
          dealedNum: 1899,
          rate: '92.58%',
        },
        {
          areaName: '公塘社区',
          eventNum: 2003,
          dealedNum: 1812,
          rate: '91.23%',
        },
        {
          areaName: '向阳社区',
          eventNum: 1590,
          dealedNum: 1491,
          rate: '98.99%',
        },
        {
          areaName: '箭塘社区',
          eventNum: 1720,
          dealedNum: 1665,
          rate: '86.65%',
        },
        {
          areaName: '周村社区',
          eventNum: 1720,
          dealedNum: 1665,
          rate: '86.65%',
        },
      ],
      swiperOption1: {
        loop: true,
        direction: 'vertical',
        slidesPerView: '5.1',
        height: 844,
        autoplay: {
          // 这个参数很重要,当外力干扰轮播图时，它能够继续实现自我滚动
          disableOnInteraction: false,
          delay: 5000, //5秒切换一次
        },
      },
      fwsxOptions: {
        centerX: '50%',
        centerY: '50%',
        unit: '件',
        colors: ['#9977EF', '#27CCD8', '#3785FE', '#F7B13F', '#F16565'],
        // showLegend:false
      },
      fwsxtOption: {
        legend: {
          top: 'bottom',
          bottom: 0,
          left: 'center',
          show: false,
        },
      },
      box4Data: [
        ['product', '人口信息'],
        ['公共安全', 86],
        ['城市管理', 134],
        ['城市服务', 234],
        ['经济运行', 134],
        ['市容市貌', 134],
      ],
      box3Data: [
        ['product', '人口信息'],
        ['AI视频分析', 40],
        ['数据研判', 100],
        ['物联设备', 60],
      ],
      box3Options: {
        colors: ['#6BCBB9', '#BF609E', '#15ECFF', '#A6A535', '#7B57B3'],
        alpha: 65,
        pieSize: 220,
        pieInnerSize: 160,
        position: ['50%', '10%'],
        bgImg: {
          top: '40%',
          left: '50%',
        },
        unit: '件',
        title: {
          // fontSize: '16px',
          top: 70,
          // textColor: 'rgba(255, 255, 255, 0)'
        },
        subtitle: {
          // fontSize: '14px',
          top: 90,
          // textColor: 'rgba(255, 255, 255, 0)'
        },
        legend: {
          left: -16,
          top: 20,
          itemWidth: 14,
          itemHeight: 14,
          itemRadius: 2,
          fontSize: '14px',
          // itemWidth: 8,
        },
      },
      box5Options: {
        unit: '件',
        colors: ['#6BCBB9', '#BF609E', '#15ECFF', '#A6A535', '#7B57B3'],
        title: {
          top: 70,
        },
        subtitle: {
          top: 90,
        },
        legend: {
          left: -10,
          top: 20
        }
      },
      box5InitOptions: {
        legend: {
          left: 60,

          // itemWidth: 8,
          labelFormatter: function () {
            return (
              '<span style="color:#fff;fontSize:14px">' +
              this.name +
              ' ' +
              '<span style="color:#fff;fontSize:14px">' +
              this.y +
              '个' +
              '</span>' +
              ' ' +
              '<span style="color:#fff;fontSize:14px">' +
              Math.round(this.percentage) +
              '%' +
              '</span>'
            )
          },
        },
      },
      sewageDataList1: [
        [
          '紧急',
          '家庭纠纷风险',
          '居民李红女士家庭内部疑似存在纠纷',
          '未处置',
          '23-05-10  09:00:00',
        ],
        ['重要', '劳资纠纷风险', '居民王明疑似存在劳资纠纷风险', '未处置', '23-05-10  09:00:01'],
        ['普通', '征地纠纷风险', '红山村疑似存在征地纠纷风险', '未处置', '23-05-10  09:00:00'],
        ['重要', '潜在失意人员', '居民王红疑似为潜在失意人员', '未处置', '23-05-10  09:00:00'],
        [
          '普通',
          '疑似群租房',
          '保利花园1栋101室被群众举报疑似群租房',
          '未处置',
          '23-05-10  09:00:00',
        ],
        [
          '重要',
          '房地产交易纠纷',
          '居民王明疑似卷入房地产纠纷事件',
          '已处置',
          '23-05-10  09:00:00',
        ],
        [
          '普通',
          '群体性事件',
          '事件人数较多，疑似会发展成群体性事件',
          '已处置',
          '23-05-10  09:00:01',
        ],
      ],
      pieData1: [
        ['product', '人口信息'],
        ['人群聚集', 86],
        ['人员打架', 134],
        ['违章停车', 234],
        ['汽车追尾', 134],
      ],
      pieData2: [
        ['product', '人口信息'],
        ['重点人群', 86],
        ['重点事件', 134],
        ['预警事件', 234],
      ],
      pieData3: [
        ['product', '人口信息'],
        ['烟雾预警', 86],
        ['水电预警', 134],
        ['门禁预警', 234],
      ],
      data7: [
        ['product', '占比'],
        ['敏感', 91],
        ['非敏感', 9],
      ],
      options7: {
        color: ['rgba(0, 168, 229, 1)', 'rgba(240, 159, 66, 1)'],
      },
      wordsOptions: {
        gridSize: 20,
      },
      words: [
        ['name', 'value'],
        ['危化品', 5],
        ['就业服务', 9],
        ['交通拥堵', 1],
        ['生态环境', 8],
        ['企业服务', 3],
        ['劳资纠纷', 6],
        ['自然灾害', 1],
        ['空气质量', 3],
        ['家庭纠纷', 6],
      ],
      blockBg1: require('@/assets/fxyp/block_bg1.png'),
      blockBg2: require('@/assets/fxyp/block_bg2.png'),
      blockBg3: require('@/assets/fxyp/block_bg3.png'),
      blockBg4: require('@/assets/fxyp/block_bg4.png'),
      blockBg5: require('@/assets/fxyp/block_bg5.png'),
      detailDialogShow1: false,
      detailDialogShow2: false,
      detailDialogShow3: false,
      zbtjIndex: 0,
      yjfxIndex: 0,
      yjsjIndex: 0,
      sjtjIndex: 0,
      gdzlData: [
        {
          num: 499,
          tit: '全年总数',
          icon: require('@/assets/fxyp/icon_gh2.png'),
          name: '亿元'
        },
        {
          num: 34,
          tit: '本月新增',
          icon: require('@/assets/fxyp/icon_gh1.png'),
          name: '亿元'
        },
        {
          num: 34,
          tit: '今日新增',
          icon: require('@/assets/fxyp/icon_gh1.png'),
          name: '亿元'
        },
      ],
      box1BottomData1: {
        data: [
          ['product', '量'],
          ['03.01', 50],
          ['03.02', 90],
          ['03.03', 80],
        ],
        options: {
          gradientColor: [
            [
              "#4CECAA",
              "#2F3BED"
            ],
            [
              "#d0464b",
              "#382230",
            ],
            [
              "#e2e23e",
              "#403e2f",
            ],
            [
              "#5bb241",
              "#FF9201",
            ],
          ],
          // color: [
          //   "#4CECAA",
          //   "#00F7FF",
          //   "#FFD400",
          //   "#FF9201",
          //   "#DCFF5B",
          //   "#00A0FF"
          // ]
        },
      },
      boxData3Option: [
        "#c92009",
        "#ebef07",
        "#099317",
        "#30ff01",
        "#DCFF5B",
        "#00A0FF"
      ],
      szcgData: [
        {
          num: 499,
          tit: '全年总数',
          icon: require('@/assets/fxyp/icon_gh2.png'),
          name: '亿元'
        },
        {
          num: 34,
          tit: '本月新增',
          icon: require('@/assets/fxyp/icon_gh1.png'),
          name: '亿元'
        },
        {
          num: 34,
          tit: '今日新增',
          icon: require('@/assets/fxyp/icon_gh1.png'),
          name: '亿元'
        },
      ],
      boxDataGh: [
        ['product', '关怀对象'],
        ['吸毒人员', 1234],
        ['重点青少年', 1234],
        ['低保户', 1234],
        ['孕妇', 1234],
      ],
      boxOptionsGh: {
        unit: '人'
      },
      boxInitoptionsGh: {
        tooltip: {
          enabled: true,
          backgroundColor: 'rgba(0, 33, 59, 0.8)',
          // borderColor: 'rgba(24, 174, 236, 1)',
          padding: 10,
          style: {
            color: '#fff',
            fontSize: 14,
          },
          useHTML: true, // 允许使用HTML
          formatter: function () {
            console.log('this', this)
            const calculationResult = (this.y / this.total * 100).toFixed(2)
            return `${this.key} </br>  <span style="color: ${this.point.color}">&#9679;</span> ${this.y} 人  ${calculationResult}%`
          },
        },
      },
      gdqsChartData: [
        ['product', '事件'],
        ['1月', 77],
        ['2月', 97],
        ['3月', 95],
        ['4月', 81],
        ['5月', 15],
        ['6月', 6],
        ['7月', 28],
        ['8月', 1],
        ['9月', 54],
        ['10月', 12],
        ['11月', 26],
        ['12月', 50],
      ],
      gdqsOptions: {
        legend: {
          show: false,
        },
      },
      gdqsInitOption: {
        yAxis: {
          name: '件',
        },
      },
      point: '',
      blockBg: require('@/assets/fxyp/block_bg3.png'),
    }
  },
  computed: {
    myBotSwiper1 () {
      return this.$refs.myBotSwiper1.$swiper
    },
    box4InitOptions () {
      return {
        tooltip: {
          formatter: (params) => {
            var relVal = params[0].name
            for (var i = 0, l = params.length; i < l; i++) {
              var unit = '件'
              relVal += '<br/>' + params[i].marker + "工单数量:" + params[i].value + unit
            }
            return relVal
          },
        },
      }
    },
  },
  mounted () {
    // this.getOrderVo()
    this.getTypeOrderCount()
    // this.linebarChart(this.$refs.ball1)
    this.getEmeOrderCount()
    this.workOrder()
    this.queryOrderCount()
    this.queryBusinessCount()
    this.queryCountVos()
    this.queryCountVos1()
    this.querySatisfaction()
    this.getOrderVoRank()
  },
  methods: {
    async getOrderVoRank () {
      let res = await getOrderVoRank({ type: 0 })
      console.log(res, 123)
      this.box2Data = res.result.map((item, i) => [
        i + 1,
        item.communityName,
        item.count,
        item.unDoneCount,
        item.isDoneCount,
        item.point,
      ])
    },
    async querySatisfaction () {
      let res = await querySatisfaction()
      this.satisfaction.value1 = Number(res.result[0].value.split('%')[0])
      this.satisfaction.value2 = Number(res.result[1].value.split('%')[0])
      this.satisfaction.value3 = Number(res.result[2].value.split('%')[0])
    },
    async queryBusinessCount () {
      this.box1BottomData1.data = []
      let res = await queryBusinessCount()
      console.log(res, 'get12345Work3')
      this.box1BottomData1.data = [['product', '量']].concat(res.result.map(it => [it.label, it.count]))
    },
    async queryOrderCount () {
      let res = await queryOrderCount()
      this.gdzlData[0].num = Number(res.result[0].count)
      this.gdzlData[1].num = Number(res.result[1].count)
      this.gdzlData[2].num = Number(res.result[2].count)
    },
    async get12345Work1 () {
      this.boxData3 = []
      const params = { module: '工单满意度' }
      let res = await get12345Work(params)
      this.boxData3 = [['product', '人员分布']].concat(res.result.map(item => {
        return [
          item.name,
          item.sysvalue
        ]
      }))
    },
    async queryCountVos () {
      const params = { kind: 1 }
      let res = await queryCountVos(params)
      this.szcgData[0].num = res.result[0].count
      this.szcgData[1].num = res.result[1].count
      this.szcgData[2].num = res.result[2].count
    },
    async queryCountVos1 () {
      this.boxDataGh = []
      const params = { kind: 2 }
      let res = await queryCountVos(params)
      this.boxDataGh = [['product', '人员分布']].concat(res.result.map(item => {
        return [
          item.name,
          item.count
        ]
      }))
    },
    clickGfsx (id) {
      this.gfsxIndex = id
    },
    showMoreFn4 () {
      this.detailDialogShow4 = true
    },
    showMoreFn5 () {
      this.isgdxqPopShow = true
    },
    //占比统计切换
    clickZbtj (id) {
      this.zbtjIndex = id
    },
    //预警分析切换
    clickYjfx (id) {
      this.yjfxIndex = id
    },
    //预警事件切换
    clickYjsj (id) {
      this.yjsjIndex = id
    },
    //事件统计切换
    clickSjtj (id) {
      this.sjtjIndex = id
      this.getOrderVo()
    },
    showMoreFn1 () {
      console.log(11111)
      this.detailDialogShow1 = true
    },
    showMoreFn2 () {
      console.log(2222)
      this.detailDialogShow2 = true
    },
    showMoreFn3 () {
      console.log(33333)
      this.detailDialogShow3 = true
    },
    async getOrderVo () {
      const params = {
        type: String(this.sjtjIndex == 0 ? 0 : 1),
      }
      console.log(params)
      let res = await getOrderVo(params)
      // console.log(res);
      this.box2Data = []
      if (res?.code == '200') {
        this.box2Data = res.result.map((item) => {
          return {
            areaName: item.communityName,
            eventNum: item.count,
            dealedNum: item.isDoneCount,
            rate: item.point,
          }
        })
      }
    },
    async getTypeOrderCount () {
      let res = await getTypeOrderCount()
      // console.log('getTypeOrderCount',res);
      if (res?.result) {
        this.box3Data = []
        this.box3Data = [['product', '']].concat(
          res.result.source.map((item) => [item.label, item.count])
        )
        this.box4Data = []
        this.box4Data = [['product', '']].concat(
          res.result.businessType.map((item) => [item.label, item.count])
        )
        this.pieData1 = []
        this.pieData1 = [['product', '']].concat(
          res.result.appealType.map((item) => [item.label, item.count])
        )
      }
    },
    linebarChart (id, data) {
      var percent = data //百分数
      var color_percent0 = '',
        color_percent100 = '#66ddda',
        dotArray = []
      let options = {
        backgroundColor: 'rgba(128, 128, 128, 0)',
        title: {
          x: '50%',
          y: '45%',
          textAlign: 'center',
          top: '56%', //字体的位置
          left: '49%',
          text: '办结率',
          textStyle: {
            fontWeight: 'normal',
            color: '#56b1ea',
            fontSize: 22,
            fontFamily: 'DIN-BlackItalic',
          },
          subtextStyle: {
            //副标题的文字的样式
            fontWeight: 'bold',
            fontSize: 18,
            color: '#3ea1ff',
            fontFamily: 'DIN-BlackItalic',
          },
        },
        xAxis: {
          show: false, //是否展示x轴
          min: function (value) {
            //调整x轴上面数据的位置
            return value.min - 7
          },
          max: function (value) {
            return value.max + 7
          },
          splitLine: {
            lineStyle: {
              show: true,
              type: 'dashed',
            },
          },
          axisLabel: {
            interval: 0,
            rotate: 40,
            textStyle: {
              fontSize: 12,
              color: '#000',
            },
          },
          data: ['1', '2', '3', '4', '5'],
        },
        yAxis: {
          show: false,
          name: '万元',
          max: 200,
          splitLine: {
            lineStyle: {
              type: 'dashed',
            },
          },
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['37%', '60%'],
            avoidLabelOverlap: false,
            startAngle: 225,
            color: [
              {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0.4,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: '#182c60', // 0% 处的颜色color_percent0 '#182c60'
                  },
                  {
                    offset: 1,
                    color: '#92fcfd', // 100% 处的颜色color_percent100
                  },
                ],
                globalCoord: false, // 缺省为 false
              },
              'none',
            ],
            hoverAnimation: false, //是否开启 hover 在扇区上的放大动画效果。
            legendHoverLink: false, //是否启用图例 hover 时的联动高亮。
            label: {
              normal: {
                show: false,
                position: 'center',
              },
              emphasis: {
                show: true,
                textStyle: {
                  fontSize: '30',
                  fontWeight: 'bold',
                },
              },
            },
            labelLine: {
              normal: {
                show: false,
              },
            },
            data: [
              {
                value: 75,
                name: '1',
              },
              {
                value: 25,
                name: '2',
              },
            ],
          },
          {
            name: ' ',
            type: 'pie',
            radius: ['42%', '47%'],
            avoidLabelOverlap: false, //是否启用防止标签重叠策略
            startAngle: 225,
            hoverAnimation: false,
            legendHoverLink: false,
            label: {
              normal: {
                show: false,
                position: 'center',
              },
              emphasis: {
                show: true,
                textStyle: {
                  fontSize: '30',
                  fontWeight: 'bold',
                },
              },
            },
            labelLine: {
              normal: {
                show: false,
              },
            },
            data: [
              {
                value: 75,
                name: '1',
              },
              {
                value: 25,
                name: '2',
              },
            ],
          },
          {
            name: '',
            type: 'pie',
            radius: ['40%', '60%'],
            avoidLabelOverlap: false,
            startAngle: 315,
            color: ['rgba(34,34,34,.9)', '#ff7a00', 'transparent'],
            hoverAnimation: false,
            legendHoverLink: false,
            clockwise: false, //饼图的扇区是否是顺时针排布。
            itemStyle: {
              normal: {
                borderColor: 'transparent',
                borderWidth: '20',
              },
              emphasis: {
                borderColor: 'transparent',
                borderWidth: '20',
              },
            },
            z: 10,
            label: {
              normal: {
                show: false,
                position: 'center',
              },
            },
            labelLine: {
              normal: {
                show: false,
              },
            },
            data: [
              {
                value: ((100 - percent) * 270) / 360,
                label: {
                  normal: {
                    formatter: percent + '%',
                    position: 'center',
                    show: true,
                    textStyle: {
                      fontSize: '50',
                      fontWeight: 'normal',
                      color: '#fff',
                      fontFamily: 'DIN-BlackItalic',
                    },
                  },
                },
                name: '',
              },
              {
                value: 1,
                name: '',
              },
              {
                value: 100 - ((100 - percent) * 270) / 360,
                name: '',
              },
            ],
          },
        ],
      }
      let myChart = this.$echarts.init(id)
      myChart.setOption(options)
      window.addEventListener('resize', function () {
        myChart.resize()
      })
    },
    async getEmeOrderCount () {
      let res = await getEmeOrderCount()
      if (res?.result) {
        this.frameData[0].value = res.result.count
        this.frameData[1].value = res.result.isDoneCount
        this.frameData[2].value = res.result.unDoneCount
        this.frameData[3].value = res.result.greatEmeLevel
        this.frameData[4].value = res.result.commonEmeLevel
        this.frameData[5].value = res.result.unEmeLevel
        // this.linebarChart(this.$refs.ball1, res.result.point)
        this.point = res.result.point
        // console.log(this.frameData)
      }
    },
    workOrder (val = 0) {
      this.getOrderCountByDate(val == 0 ? 'year' : 'day')
    },
    async getOrderCountByDate (val) {
      const params = {
        type: val,
      }
      let res = await getOrderCountByDate(params)
      if (res?.result) {
        this.gdqsChartData = []
        this.gdqsChartData = [
          ['product', '事件'],
          ...res.result.map((item) => [item.name, item.count]),
        ]
      }
    },
  },
}
</script>

<style lang="less" scoped>
.ypyj-container {
  position: relative;
  width: 100%;
  height: 1080px;
  background: url('~@/assets/fxyp/page_bg1.png') no-repeat;
  background-size: 100% 100%;
  z-index: 1001;
  .box {
  }
  .box2 {
    .cont {
      // height: 100%;
    }
    .content_border {
      padding: 10px;
      height: calc(100% - 10px);
      // border: #1d4164 1px solid;
      margin: 5px 0;
      background: rgba(29, 114, 184, 0.2);
    }
    .title1 {
      font-size: 18px;
      font-family: 'PangMenZhengDao';
      font-weight: normal;
      color: #ffffff;
      line-height: 20px;
      text-align: left;
    }
    ul {
      display: flex;
      justify-content: space-around;
      li {
        height: 80px;
        width: calc(50% - 10px);
        display: flex;
      }
    }
  }
  .moreBtn {
    position: absolute;
    right: 0;
    top: 0;
    cursor: pointer;
    font-size: 18px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #00b5ff;
    line-height: 20px;
  }
  .titleTab {
    position: absolute;
    left: 55px;
    top: 0;
    height: 16px;
    font-size: 18px;
    font-family: 'FZSKJW';
    font-weight: normal;
    color: #9de2ff;
    line-height: 16px;
    span {
      margin-left: 10px;
      cursor: pointer;
    }
    .active {
      font-size: 18px;
      font-family: 'FZSKJW';
      font-weight: normal;
      color: #ffffff;
      line-height: 20px;
      letter-spacing: 1px;
    }
  }

  .bottom-tabs-container {
    position: absolute;
    bottom: -13px;
    width: 100%;
    height: 67px;
    background: url('~@/assets/fxyp/bottom_bg.png') no-repeat;
    background-size: cover;
  }
  .main-content {
    position: absolute;
    top: 152px;
    width: 1920px;
    height: 890px;
    padding: 0 50px;
    z-index: 99;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    // background: rgba(29, 114, 184, 0.1);
    .left-box {
      width: 450px;
      height: 100%;
      // background: rgba(29, 114, 184, 0.1);
      margin-right: 45px;
      :deep .block {
        width: 437px;
      }
      .content {
        width: 100%;
        height: 100%;
        overflow: hidden;
        white-space: nowrap;
        padding: 0px 18px 34px;
        .box-content {
          // display: inline-block;
          position: relative;
          width: 422px;
          height: 121px;
          background: url('~@/assets/fxyp/sjsl4.png') no-repeat;
          background-size: 100% 100%;
          margin-bottom: 15px;
          // margin-right: 16px;
          .area-label {
            // width: 17px;
            position: absolute;
            top: -12px;
            // transform: translateY(-50%);
            left: 117px;
            font-size: 20px;
            font-family: PingFangSC, PingFang SC;
            color: #ffffff;
            line-height: 23px;
            letter-spacing: 2px;
          }
          .text-container {
            position: absolute;
            top: 46px;
            // left: 45px;
            display: flex;
            width: 100%;
            justify-content: space-evenly;
            .text-item {
              margin-bottom: 8px;
              .title {
                height: 17px;
                line-height: 17px;
                margin-bottom: 4px;
                display: flex;
                align-items: center;
              }
              .event-icon {
                display: inline-block;
                width: 17px;
                height: 17px;
                background: url('~@/assets/img/syzl/ypyj/event-icon4.png') no-repeat;
                background-size: 100% 100%;
                margin-right: 5px;
                // vertical-align: middle;
              }
              .deal-icon {
                display: inline-block;
                width: 17px;
                height: 17px;
                background: url('~@/assets/img/syzl/ypyj/deal-icon4.png') no-repeat;
                background-size: 100% 100%;
                margin-right: 5px;
                vertical-align: middle;
              }
              .rate-icon {
                vertical-align: middle;
                display: inline-block;
                width: 17px;
                height: 17px;
                background: url('~@/assets/img/syzl/ypyj/rate-icon4.png') no-repeat;
                background-size: 100% 100%;
                margin-right: 5px;
              }
              .text {
                text-align: left;
                display: block;
                // vertical-align: bottom;
                font-size: 26px;
                font-family: DINAlternate-Bold, DINAlternate;
                font-weight: bold;
                color: #ffffff;
                line-height: 30px;
              }
              .label {
                height: 14px;
                font-size: 18px;
                // font-family: $fontN;
                font-weight: 400;
                color: rgba(189, 238, 255, 1);
                line-height: 17px;
                letter-spacing: 1px;
                margin-right: 28px;
              }
            }
          }
          .sort-icon {
            position: absolute;
            top: -22px;
            left: 42px;
            font-size: 24px;
            font-family: SourceHanSansCN-Medium, SourceHanSansCN;
            font-weight: 500;
            color: #ffffff;
            // line-height: 36px;
            background: linear-gradient(180deg, #6ed2ff 0%, #00a5ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
        .swiper {
          // padding-bottom: 20px;
          margin-top: 30px;
          overflow: hidden;
          :deep .swiper-wrapper {
            margin-top: 14px;
          }
          .slide1 .box-content {
            background: url('~@/assets/fxyp/sjsl1.png') no-repeat;
            background-size: 100% 100%;
            .text-container .text-item {
              .event-icon {
                background: url('~@/assets/img/syzl/ypyj/event-icon1.png') no-repeat;
                background-size: 100% 100%;
              }
              .deal-icon {
                background: url('~@/assets/img/syzl/ypyj/deal-icon1.png') no-repeat;
                background-size: 100% 100%;
              }
              .rate-icon {
                background: url('~@/assets/img/syzl/ypyj/rate-icon1.png') no-repeat;
                background-size: 100% 100%;
              }
              .label {
                color: #ffbdbd;
              }
            }
            .sort-icon {
              background: linear-gradient(180deg, #ffb4b0 0%, #ff4916 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
          .slide2 .box-content {
            background: url('~@/assets/fxyp/sjsl2.png') no-repeat;
            background-size: 100% 100%;
            .text-container .text-item {
              .event-icon {
                background: url('~@/assets/img/syzl/ypyj/event-icon2.png') no-repeat;
                background-size: 100% 100%;
              }
              .deal-icon {
                background: url('~@/assets/img/syzl/ypyj/deal-icon2.png') no-repeat;
                background-size: 100% 100%;
              }
              .rate-icon {
                background: url('~@/assets/img/syzl/ypyj/rate-icon2.png') no-repeat;
                background-size: 100% 100%;
              }
              .label {
                color: rgba(255, 241, 189, 1);
              }
            }
            .sort-icon {
              background: linear-gradient(180deg, #ffeca8 0%, #d28900 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
          .slide3 .box-content {
            background: url('~@/assets/fxyp/sjsl3.png') no-repeat;
            background-size: 100% 100%;
            .text-container .text-item {
              .event-icon {
                background: url('~@/assets/img/syzl/ypyj/event-icon3.png') no-repeat;
                background-size: 100% 100%;
              }
              .deal-icon {
                background: url('~@/assets/img/syzl/ypyj/deal-icon3.png') no-repeat;
                background-size: 100% 100%;
              }
              .rate-icon {
                background: url('~@/assets/img/syzl/ypyj/rate-icon3.png') no-repeat;
                background-size: 100% 100%;
              }
              .label {
                color: rgba(189, 242, 255, 1);
              }
            }
            .sort-icon {
              background: linear-gradient(180deg, #bffdff 0%, #00ffec 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
          .slide4 .box-content,
          .slide5 .box-content {
            background: url('~@/assets/fxyp/sjsl4.png') no-repeat;
            background-size: 100% 100%;
            .text-container .text-item {
              .event-icon {
                background: url('~@/assets/img/syzl/ypyj/event-icon4.png') no-repeat;
                background-size: 100% 100%;
              }
              .deal-icon {
                background: url('~@/assets/img/syzl/ypyj/deal-icon4.png') no-repeat;
                background-size: 100% 100%;
              }
              .rate-icon {
                background: url('~@/assets/img/syzl/ypyj/rate-icon4.png') no-repeat;
                background-size: 100% 100%;
              }
              .label {
                color: rgba(189, 238, 255, 1);
              }
            }
            .sort-icon {
              background: linear-gradient(180deg, #6ed2ff 0%, #00a5ff 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
        }
      }
    }

    .block_title1 {
      position: absolute;
      top: -55px;
      left: 545px;
      padding-left: 80px;
      width: 800px;
      height: 50px;
      font-size: 18px;
      background: url(~@/assets/fxyp/title_bg2.png) no-repeat;
      background-size: 100% 100%;
      font-size: 20px;
      font-family: PingFangSC, PingFang SC;
      color: #ffffff;
      line-height: 52px;
      letter-spacing: 2px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      &.title_bg_Btn {
        background: url(~@/assets/fxyp/title_bg2.png) no-repeat;
        background-size: 100% 100%;
      }
      & > span {
        margin-left: 64px;
      }

      .btns {
        display: flex;
        align-items: center;
        line-height: 50px;
        margin-right: 22px;
        .btnList {
          min-width: 80px;
          height: 26px;
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          color: #caecff;
          text-shadow: 0px 0px 1px #00132e;
          background: url(~@/assets/fxyp/title_bg2.png) no-repeat;
          background-size: 100% 100%;
          line-height: 26px;
          cursor: pointer;
          &.active {
            color: #45daff;
            text-shadow: 0px 0px 4px rgba(0, 175, 255, 0.5), 0px 0px 1px rgba(0, 30, 50, 0.5);
          }
          &:not(:last-of-type) {
            margin-right: 7px;
          }
        }
      }
    }

    .center-box {
      width: 802px;
      height: 100%;
      background: rgba(29, 114, 184, 0.2);
      // margin-right: 52px;
      .up-center-container {
        width: 100%;
        // height: 717px;
        position: relative;
        bottom: 60px;
        .content1 {
          width: 100%;
          height: 83px;
          display: flex;
          justify-content: center;
          .item {
            width: 156px;
            margin-right: 10px;
          }
          .label {
            width: 156px;
            height: 34px;
            background: url('~@/assets/fxyp/center_icon.png') no-repeat center / 100% 100%;
            font-size: 20px;
            font-family: PingFangSC, PingFang SC;
            color: #ffffff;
            line-height: 34px;
          }
          .num {
            font-size: 32px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 59px;
            letter-spacing: 2px;
            text-shadow: 0px 2px 2px rgba(255, 255, 255, 0.5);
            .unit {
              font-size: 18px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              color: #ffffff;
              line-height: 20px;
            }
          }
        }
        .content2 {
          background: url('~@/assets/fxyp/cenbg2.png') no-repeat;
          position: relative;
          height: 100%;
          width: 593px;
          height: 694px;
          background-size: 100% 100%;
          left: 16%;

          // @keyframes rotate3d {
          //   0% {
          //     transform: rotateZ(-30deg) rotateY(0deg);
          //   }
          //   100% {
          //     transform: rotateZ(-30deg) rotateY(360deg);
          //   }
          // }
          .ball {
            height: 161%;
            transform-style: preserve-3d;
            animation: rotate3d 20s linear infinite;
            position: relative;
            top: -146px;

            .line {
              position: absolute;
              width: 100%;
              height: 100%;
              -webkit-border-radius: 50%;
              -moz-border-radius: 50%;
              border-radius: 50%;
              // background-image: url(~@/assets/imgs/earth.png);
              background-repeat: no-repeat;
              background-size: 100%;
            }
          }

          .ball .line1 {
            transform: rotateY(0deg);
          }
          .ball .line2 {
            transform: rotateY(18deg);
          }
          .ball .line3 {
            transform: rotateY(36deg);
          }
          .ball .line4 {
            transform: rotateY(54deg);
          }
          .ball .line5 {
            transform: rotateY(72deg);
          }
          .ball .line5 {
            transform: rotateY(90deg);
          }
          .ball .line6 {
            transform: rotateY(108deg);
          }
          .ball .line7 {
            transform: rotateY(126deg);
          }
          .ball .line8 {
            transform: rotateY(144deg);
          }
          .ball .line9 {
            transform: rotateY(162deg);
          }
          .ball .line9 {
            transform: rotateY(180deg);
          }
          .item {
            position: absolute;
            width: 140px;
            height: 150px;
            background: url('~@/assets/fxyp/center_icon2.png') no-repeat center / 100% 100%;
            text-align: center;
            .label {
              font-size: 18px;
              font-family: PingFangSC, PingFang SC;
              color: #ffffff;
              line-height: 21px;
            }
            .num {
              margin-top: 35px;
              font-size: 49px;
              font-family: DINPro-Bold, DINPro;
              font-weight: bold;
              color: #ffffff;
              line-height: 63px;
              text-shadow: 0px 4px 20px rgba(255, 0, 52, 0.8);
            }
            &:nth-child(2) {
              top: 86px;
              left: 38px;
            }
            &:nth-child(3) {
              top: 86px;
              right: 38px;
            }
            &:nth-child(4) {
              top: 254px;
              left: 38px;
            }
            &:nth-child(5) {
              top: 254px;
              right: 38px;
            }
          }

          .ball1 {
            height: 100%;
            width: 100%;
            position: relative;
            // top: -146px;
          }
          .ball_echart {
            height: 80%;
            width: 80%;
            position: absolute;
            left: 50%;
            top: 48%;
            transform: translate(-50%, -50%);
          }
          .ball_echart1 {
            position: absolute;
            left: 50%;
            top: 48%;
            transform: translate(-50%, -50%);
            & > div:first-of-type {
              color: #fff;
              font-size: 36px;
              span {
                font-size: 24px;
              }
            }
            & > div:last-of-type {
              color: RGBA(32, 181, 240, 1);
              font-size: 18px;
            }
          }
          .item1 {
            height: 81px;
            width: 246px;
            padding: 6px 23px;
            position: absolute;
            display: flex;
            justify-content: space-between;
            img {
              height: 64px;
            }
            .second_box {
              height: 100%;
              width: calc(100% - 66px);
              display: flex;
              justify-content: space-between;
              flex-direction: column;
              padding: 6px 0;
              align-items: flex-start;
              .item_name {
                font-size: 18px;
                font-family: PingFang SC;
                font-weight: 400;
                color: #fff;
              }
              .under_line {
                width: 18px;
                height: 1px;
                background: rgba(255, 255, 255, 0.5);
              }
              .item_value {
                font-size: 24px;
                font-family: DIN-BlackItalic;
                font-weight: 400;
                color: #ffffff;
                line-height: 24px;
              }
            }
            &:nth-child(2) {
              left: -120px;
              top: 160px;
              background: url('~@/assets/fxyp/frame_blue.png') no-repeat center / 100% 100%;
            }
            &:nth-child(3) {
              left: -120px;
              top: 300px;
              background: url('~@/assets/fxyp/frame_blue.png') no-repeat center / 100% 100%;
            }
            &:nth-child(4) {
              left: -120px;
              top: 460px;
              background: url('~@/assets/fxyp/frame_blue.png') no-repeat center / 100% 100%;
            }
            &:nth-child(5) {
              .item_name {
                color: #fff;
              }
              top: 160px;
              right: -120px;
              background: url('~@/assets/fxyp/frame_red.png') no-repeat center / 100% 100%;
            }
            &:nth-child(6) {
              .item_name {
                color: #fff;
              }
              top: 300px;
              right: -120px;
              background: url('~@/assets/fxyp/frame_yellow.png') no-repeat center / 100% 100%;
            }
            &:nth-child(7) {
              .item_name {
                color: #fff;
              }
              top: 460px;
              right: -120px;
              background: url('~@/assets/fxyp/frame_lightblue.png') no-repeat center / 100% 100%;
            }
          }
        }
      }
      .center-bottom-container {
        position: relative;
        bottom: 144px;
        .bot_title {
          height: 33px;
          font-size: 18px;
          font-weight: normal;
          line-height: 33px;
          font-family: PingFangSC, PingFang SC;
          color: #ffbc6c;
          text-align: left;
          padding-left: 5px;
          img {
            width: 15px;
            height: 13px;
            // margin: auto 0;
            margin-top: -3px;
          }
          .box_dashed {
            display: inline-block;
            width: calc(100% - 110px);
            height: 5px;
            border-top: 1px dashed #e5e5e5;
          }
        }
        .content {
          // padding: 20px 20px 0 32px;
          .top-container {
            position: relative;
            font-size: 18px;
            font-weight: 400;
            color: #ff5656;
            line-height: 21px;
            letter-spacing: 1px;
            text-align: left;
            .label {
              display: inline-block;
              width: 43px;
              // margin-left: 20px;
            }
            .level2 {
              color: #f1842b;
            }
            .level3 {
              color: #56c0ff;
            }
            .text {
              color: #ffffff;
              margin-right: 20px;
            }
          }
          .table_box {
            overflow: hidden;
            height: 258px;
            margin-top: 8px;
          }
        }
      }
    }
    .right-box {
      width: 520px;
      height: 100%;
      padding-left: 60px;
      background: rgba(29, 114, 184, 0.2);
      .chart1 {
        width: 100%;
        height: 257px;
      }
      .chart2 {
        width: 100%;
        height: 256px;
      }
    }
  }
}
.table-container {
  padding-top: 32px;
  :deep(.el-table tr) {
    background-color: rgba(9, 19, 34, 0.95);
    border: none;
    height: 58px;
    font-size: 18px;
    font-family: Source Han Sans CN-Normal, Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
    line-height: 58px;
  }
  :deep .el-table thead tr {
    height: 30px;
    line-height: 30px;
  }
  :deep(.el-table) {
    background-color: rgba(9, 19, 34, 0.95);
  }
  :deep(.el-table tbody, .el-table) {
    background-color: rgba(9, 19, 34, 0.95);
  }
  :deep(.el-table--striped .el-table__body tr.el-table__row--striped) {
    background: url('~@/assets/img/syzl/djyl/table-bg.png');
    background-size: 100% 100%;
    border: none;
  }
  :deep(.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell) {
    background: none;
    border: none;
  }
  :deep(.el-table td.el-table__cell) {
    border: none;
  }
  :deep(.el-table th) {
    color: rgba(0, 234, 255, 1);
    background-color: rgba(9, 19, 34, 0.95);
    font-weight: bold;
    border: 1px solid rgba(41, 104, 199, 1);
  }
  :deep(.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell) {
    background-color: transparent;
  }
  :deep(.el-table__inner-wrapper::before) {
    display: none;
  }
}
.parter-content {
  display: flex;
  justify-content: space-between;
  .img-container {
    width: 133px;
    height: 186px;
    background: url('~@/assets/img/syzl/djyl/photo.png') no-repeat;
    background-size: 100% 100%;
    margin-right: 20px;
  }
  .info-container {
    width: 100%;
    padding: 0 10px;
    .info-item {
      // height: 21px;
      font-size: 18px;
      font-family: Source Han Sans CN-Normal, Source Han Sans CN;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.65);
      line-height: 16px;
      margin-bottom: 13px;
      display: flex;
      justify-content: space-between;
      .label {
        display: inline-block;
        width: 120px;
        color: rgba(106, 146, 187, 1);
      }
      .text {
        display: inline-block;
        text-align: right;
      }
    }
  }
}
.paging {
  margin-top: 23px;
}
:deep(.search-container .btns .el-button) {
  width: 76px;
}
.btnBox {
  text-align: center;
}

.tabsBox {
  margin-top: 20px;
}
:deep .el-tabs__item {
  color: #fff;
}
:deep .is-active {
  color: #409eff;
}
.tabList {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  margin-top: 10px;
  .tabItem {
    width: 233px;
    height: 46px;
    // background-image: url('~@/assets/images/syzl/tab2.png');
    background-size: 100% 100%;
    font-family: FZSKJW;
    font-size: 28px;
    color: #fff;
    text-align: center;
    line-height: 46px;
    cursor: pointer;
    margin-left: 16px;
  }
  .tabItem:first-child {
    margin-left: 0;
  }
  .tabActive {
    // background-image: url('~@/assets/images/syzl/tab1.png');
  }
}
</style>
