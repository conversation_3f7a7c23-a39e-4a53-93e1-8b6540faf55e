<template>
  <div class="ai_waring" ref="popup" style="padding-top:5px">
    <div class="ai-warrper" v-if="popZdxmInfo">
      <div class="title">
        <span>重点项目</span>
        <img @click="closeEmitai" class="close_btn" src="@/assets/shzl/map/close_btn.png" alt="" />
      </div>
      <div class="content">
        <div><span>项目名称:</span><span>{{popZdxmInfo.name }}</span></div>
        <div><span>建设单位：</span><span>{{popZdxmInfo.dept }}</span></div>
        <div><span>项目地址：</span><span>{{popZdxmInfo.address }}</span></div>
        <div><span>属地：</span><span>{{popZdxmInfo.region }}</span></div>
        <div><span>行业类别：</span><span>{{popZdxmInfo.type }}</span></div>
        <div><span>建筑总面积：</span><span>{{popZdxmInfo.area }}</span></div>
        <div><span>计划总投资：</span><span>{{popZdxmInfo.invest }}</span></div>
        <div><span>主要建设内容：</span><span></span></div>
        <div class="desc">
          {{popZdxmInfo.content }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    popZdxmInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
    }
  },
  methods: {
    closeEmitai() {
      this.$emit('closeEmitai')
    },
  },
}
</script>

<style lang='less' scoped>
.ai_waring {
  width: 453px;
  // height:491px;
  // position: absolute;
  // transform: translate(-50%, -50%);
  // border: 1px solid red;
  background: url(~@/assets/shzl/map/ai_popBg.png) no-repeat center / 100% 100%;
  background-size: 100% 100%;
  position: relative;

  .title {
    background: url(~@/assets/shzl/map/title_bg.png) no-repeat center / 100% 100%;
    background-size: 100% 100%;
    width: 375px;
    height: 47px;
    margin: 20px auto 0;
    line-height: 47px;
    text-align: left;
    text-indent: 27px;
    font-size: 18px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #ffffff;
    position: relative;

    span {
      background: linear-gradient(180deg, #ffffff 0%, #2aa6ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .close_btn {
      position: absolute;
      top: 11px;
      right: 25px;
      cursor: pointer;
    }
  }

  .content {
    .desc {
      color: #fff;
      text-align: left;
    }

    padding: 20px 50px 46px 48px;

    // border: 1px solid red;
    &>div {
      display: flex;
      justify-content: space-between;

      &:not(:last-of-type) {
        margin-bottom: 14px;
      }

      & span:first-of-type {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
      }

      & span:last-of-type {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
      }
    }

    .pic {
      margin: 18px auto 0;
      width: 316px;
      height: 169px;
    }

    .btns {
      margin: 15px auto 0;
      display: flex;
      justify-content: space-between;

      &>div {
        width: 150px;
        height: 33px;
        // border: 1px solid red;
        background: url(~@/assets/shzl/map/btn_bg.png) no-repeat center / 100% 100%;
        background-size: 100% 100%;
        line-height: 33px;
        text-align: center;
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
      }
    }
  }
}
</style>