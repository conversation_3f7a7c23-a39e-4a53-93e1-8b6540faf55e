<template>
  <div class="more-dialog" :style="offset">
    <div class="option-list">
      <div :class="['option-item', {'option-item-warning': item === '移出会议'}]" v-for="item of optionList" :key="item" @click="handleOption(item)">{{ item }}</div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'moreOptionDialog',
    props: {
      identity: {
        type: String,
        default: ''
      },
      offset: {
        type: Object,
        default: () => {
          return {
            left: '0px',
            top: '0px'
          }
        }
      },
      optionList: {
        type: Array,
        default: () => {
          return [
            '关闭摄像头',
            '修改名称',
            '移出会议'
          ]
        }
      }
    },
    methods: {
      handleOption(option) {
        if(option === '关闭摄像头') {
          this.$emit('closeCamera', this.identity)
        } else if(option === '修改名称') {
          this.$emit('changeParticipantName', this.identity)
        } else if(option === '移出会议') {
          this.$confirm('此操作将使该与会者移出会议, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$emit('removeParticipant', this.identity)
          })
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .more-dialog {
    min-width: 152px;
    max-height: 144px;
    overflow: hidden;
    padding: 0 0;
    position: absolute;
    z-index: 4000;
    transform: translateX(-50%);
    background: url('~@/assets/service/mini-bg1.png') no-repeat center / 100% 100%;
    .option-list {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      .option-item {
        white-space: nowrap;
        cursor: pointer;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #C2DDFC;
        line-height: 42px;
        &-warning {
          color: #D06C63 !important;
        }
      }
    }
  }
</style>