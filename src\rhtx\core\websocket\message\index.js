export * from './other';
export * from './p2p';
export * from './channel';
export * from './meet';
import { useP2PNotice } from './p2p';
import { useMeetNotice } from './meet';

const collectMessage = () => {
  const p2pNotice = useP2PNotice();
  const meetNotice = useMeetNotice();
  return {
    ...p2pNotice,
    ...meetNotice
  };
};

export function messageProcess(type, data, userInfo) {
  let oType = type;
  const messge = collectMessage();
  oType = (messge[type] && messge[type](data, userInfo)) || oType;
  return oType;
}

export default {
  messageProcess
};
