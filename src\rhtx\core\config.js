/*
 * @Author: fanjialong <EMAIL>
 * @Date: 2023-04-15 10:29:09
 * @LastEditors: fanjialong <EMAIL>
 * @LastEditTime: 2023-04-18 09:53:42
 * @FilePath: \hs_dp\src\rhtx\core\config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// http映射   {host:目标origin}
const API_URL_MAP = {
  'localhost:8080': 'https://**************:8000' //测试地址
  // 'localhost:8080': 'http://*************:8099', //线上地址
  // '*************': 'http://*************:8099'
  // '**************:9090': 'http://**************:8099',
  // '*************': 'http://**************:8099'

};

// ws接口映射   {host:目标ws地址}
const WS_URL_MAP = {
  'localhost:8080': 'wss://**************:8000/wss' //测试地址
  // 'localhost:8080': 'ws://*************:8099/ws', //线上地址
  // '*************': 'ws://*************:8099/ws'
  // '**************:9090': 'ws://**************:8099/ws',
  // '*************': 'ws://**************:8099/ws'
};

export {
  API_URL_MAP,
  WS_URL_MAP
};