<template>
  <div class="middle_box">
    <div class="line">
      <div
        class="line_box"
        v-for="(item, index) in middleList"
        :key="index"
      >
        <div class="num_">
          <!-- <num-scroll :number='formatNumber(item.num)' style="fontSize: 18px"></num-scroll> -->
          <countTo
            ref="countTo"
            :startVal="$countTo.startVal"
            :decimals="$countTo.decimals(item.num)"
            :endVal="item.num"
            :duration="$countTo.duration"
            class="num_num"
          />
          <span>{{ item.numUnit }}</span>
        </div>
        <div class="name_unit">
          <span class="name_">{{ item.name }}</span
          ><span class="unit_" v-if="item.nameUnit">({{ item.nameUnit }})</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import NumScroll from '@/components/leader/num_scroll';
export default {
  props: {
    middleList: {
      type: Array,
      default: () => [
        {
          name: 'GDP',
          numUnit: '',
          num: '430.5',
          nameUnit: '亿元',
        },
        {
          name: '户籍人口',
          numUnit: '',
          num: '32.6',
          nameUnit: '万人',
        },
        {
          name: '部件总数',
          numUnit: '',
          num: '97558',
          nameUnit: '件',
        },
        {
          name: '企业总数',
          numUnit: '',
          num: '2766',
          nameUnit: '个',
        },
        {
          name: '空气质量优良率',
          num: '99.5',
          numUnit: '%',
          nameUnit: '',
        },
      ],
    },
    lineMarginLeft: {
      type: Number,
      default: 59,
    },
  },
  components: {
    NumScroll,
  },
  data() {
    return {
      aaaa:'6000'
    }
  },
   computed: {
    formatNumber() {
      return function (param) {
        console.log(param)
        if (this.isEmpty(param) || param.length < 4 || isNaN(param)) {
          return param
        }

        let small = ''
        let isSmall = false
        if (param.split('.').length === 2) {
          // 有小数
          isSmall = true
          small = param.split('.')[1]
          param = param.split('.')[0]
        }

        let result = ''

        while (param.length > 3) {
          result = ',' + param.slice(-3) + result
          param = param.slice(0, param.length - 3)
        }

        if (param) {
          result = param + result
        }

        if (isSmall) {
          result = result + '.' + small
        }
        console.log(result)
        return result
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.middle_box {
  position: absolute;
  top: 130px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1001;
  .line {
    display: flex;
    justify-content: space-between;
    .line_box {
      width: 152px;
      height: 35px;
      background-size: 100% 100%;
      background: url(~@/assets/leader/img/component/middle_header/bg.png) no-repeat;
      position: relative;
      margin-right: 71px;
      &:nth-of-type(3) {
        margin-right: 18px;
      }
      .num_ {
        position: absolute;
        left: 32px;
        font-size: 36px;
        font-family: DIN-BlackItalic, DIN;
        font-weight: normal;
        color: #ffffff;
        display: flex;
        & span:last-of-type {
          font-size: 24px;
        }
        &num {
          margin-top: -20px;
        }
      }
      .name_unit {
        position: absolute;
        top: 45px;
        left: 11px;
        white-space: nowrap;
        .name_ {
          font-size: 18px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #ffffff;
        }
        .unit_ {
          margin-left: 2px;
          font-size: 14px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
      }
    }
  }
}
</style>