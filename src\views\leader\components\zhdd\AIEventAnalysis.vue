<!--
 * @Author: fanjialong <EMAIL>
 * @Date: 2023-04-18 16:48:29
 * @LastEditors: fanjialong <EMAIL>
 * @LastEditTime: 2023-05-17 16:21:12
 * @FilePath: \hs_dp\src\views\leader\components\zhdd\AIEventAnalysis.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="event-analysis">
    <div class="info">
      <div class="title">
        <countTo
          ref="countTo"
          :startVal="$countTo.startVal"
          :decimals="$countTo.decimals(358)"
          :endVal="358"
          :duration="$countTo.duration"
        />
        件
      </div>
      <div class="icon">AI事件总数</div>
    </div>
    <div class="chart-wrap">
      <RankBarChartE02 :data="chartData" :options="options" :initOption="initOption" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'AIEventAnalysis',
  data() {
    return {
      chartData: [
        ['name', 'value'],
        ['外来人员', '41'],
        ['机动车违停', '15'],
        ['店外经营', '13'],
        ['人员滞留', '11'],
        ['违禁捕鱼', '9'],
      ],
      options: {
        color: ['#F8753A', '#D28900', '#3AF8DF', '#00A5FF', '#00A5FF']
      },
      initOption: {}
    }
  }
}
</script>

<style lang="less" scoped>
.event-analysis {
  display: flex;
  gap: 8px;
  width: 100%;
  height: 100%;
  padding: 20px 20px 30px 3px;
  .info {
    width: 171px;
    height: 100%;
    padding-top: 18px;
    .title {
      display: flex;
      width: 100%;
      margin: 0 auto;
      gap: 2px;
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #ffffff;
      line-height: 30px;
      margin-left: 64px;
      span {
        height: 26px;
        font-size: 22px;
        font-family: PingFangSC, PingFang SC;
        font-weight: normal;
        color: #ffffff;
        line-height: 26px;
      }
    }
    .icon {
      width: 171px;
      height: 137px;
      background: url('~@/assets/zhdd/bg8.png') no-repeat;
      margin-top: 10px;
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #ffffff;
      line-height: 20px;
    }
  }
  .chart-wrap {
    flex: 1;
    height: 100%;
  }
}
</style>
