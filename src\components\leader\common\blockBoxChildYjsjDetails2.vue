<template>
	<div class="child-block" :style="{ height: blockHeight + 'px' }">
		<div class="content">
			<slot></slot>
			<div class="cont">
				<div class="cont1">
					<div class="cont1Item1" @click="typeSwitch2(1)">
						<img src="@/assets/bjnj/yjyl5.png" v-if="typeShow2 == 1" alt="" />
						<img src="@/assets/bjnj/yjyl1.png" v-else alt="" />
						<div class="cont1Name" :class="typeShow2 == 1 ? 'cont1Active' : ''">请求上报</div>
					</div>
					<img class="cont1Item2" src="@/assets/bjnj/ddqq7.png" alt="" />
					<div class="cont1Item3" @click="typeSwitch2(2)">
						<img src="@/assets/bjnj/yjyl6.png" v-if="typeShow2 == 2" alt="" />
						<img src="@/assets/bjnj/yjyl2.png" v-else alt="" />
						<div class="cont1Name" :class="typeShow2 == 2 ? 'cont1Active' : ''">任务下发</div>
					</div>
				</div>
				<div v-if="typeShow2 == 1">
					<div class="cont5">
						<div class="contList">
							<div class="contLeft"><span>*</span>上报部门</div>
							<div class="contRight">{{ data4[0].departName }}</div>
						</div>
						<div class="contList">
							<div class="contLeft"><span>*</span>上报人：</div>
							<div class="contRight">{{ data4[0].usernmae }}</div>
						</div>
						<div class="contList">
							<div class="contLeft"><span>*</span>受灾类型：</div>
							<div class="contRight">{{ dataObj.affectedType }}</div>
						</div>
						<div class="contList">
							<div class="contLeft"><span>*</span>受灾作物：</div>
							<div class="contRight">{{ dataObj.affectedCrop }}</div>
						</div>
						<div class="contList">
							<div class="contLeft"><span>*</span>受灾区域：</div>
							<div class="contRight">{{ dataObj.areaname }}</div>
						</div>
						<div class="contList">
							<div class="contLeft"><span>*</span>受灾面积：</div>
							<div class="contRight">{{ dataObj.affectedArea }}</div>
						</div>
						<div class="contList">
							<div class="contLeft"><span>*</span>所需农机：</div>
							<div class="contRight" :title="dataObj.itemsNew">{{ dataObj.itemsNew }}</div>
						</div>
						<div class="contList">
							<div class="contLeft"><span>*</span>任务时间：</div>
							<div class="contRight">{{ dataObj.requestDate }}</div>
						</div>
						<div class="contList">
							<div class="contLeft"><span>*</span>任务详情：</div>
						</div>
						<div class="contList1">
							<div class="contContent">{{ dataObj.comments }}</div>
						</div>
					</div>
				</div>
				<div v-if="typeShow2 == 2">
					<div class="cont2">
						<div class="contList">
							<div class="contLeft"><span>*</span>经办部门：</div>
							<div class="contRight">{{ dataObj.node.nodeName }}</div>
						</div>
						<div class="contList">
							<div class="contLeft"><span>*</span>经办人：</div>
							<div class="contRight">{{ dataObj.node.createDatetimes }}</div>
						</div>
						<div class="contList">
							<div class="contLeft"><span>*</span>下发时间：</div>
							<div class="contRight">{{ dataObj.node.createDatetime }}</div>
						</div>
					</div>
					<div class="cont3">任务下发</div>
					<div class="cont4">
						<div class="cont4List" v-for="(item, index) of data7" :key="index">
							<div class="cont4List1">
								<div class="cont4List1Left">
									<div class="cont4List1Left1">{{ item.departName }}</div>
									<div class="cont4List1Left2">
										<div class="cont4List1Left3">{{ item.usernmae }}</div>
										<div class="cont4List1Left4">{{ item.createDatetime }}</div>
									</div>
								</div>
								<div :class="item.choice == '已完成' ? 'cont4List1Right1' : 'cont4List1Right2'">
									<span>{{ item.choice }}</span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		contentNumberUnitOne: {
			type: String,
			default: '万',
		},

		contentDataList: {
			type: Array,
			default: () => [],
		},

		blockHeight: {
			type: Number,
			default: 350,
		},
		dataObj: {
			type: Object,
			default: () => {},
		},
		typeShow2: {
			type: Number,
			default: 1,
		},
		data4: {
			type: Array,
			default: () => [],
		},
		data7: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			currentIndex: 0,
			isActive: true,
			isOpenOtherData: false,
			otherDataList: [],
			currentTabIndex: 0,
		}
	},
	computed: {
		otherArgmachNumber() {
			let otherArgmachNumber = 0

			this.otherDataList.forEach((item) => {
				otherArgmachNumber += Number(item.number)
			})
			return otherArgmachNumber
		},
	},
	methods: {
		clickchange(index) {
			this.currentIndex = index
			this.$emit('updateChange', this.currentIndex)
		},
		clickBulletFrame() {
			this.$emit('updateChange2', true)
		},
		showMoreFn() {
			this.$emit('handleShowMore', true)
		},
		showMoreFn2() {
			this.$emit('handleShowMore2', true)
		},
		handleClickOtherData() {
			this.isOpenOtherData = !this.isOpenOtherData
		},
		handleClickCloseOtherData() {
			this.isOpenOtherData = false
		},
		handleChangeTab(item, index) {
			this.currentTabIndex = index
			this.$emit('handleChangeTab', item.value)
		},
		btnSwitch(item, index) {
			this.$emit('btnSwitch', item, index)
		},
		typeSwitch2(code) {
			this.$emit('typeSwitch2', code)
		},
	},
}
</script>

<style lang="less" scoped>
.child-block {
	width: 455px;
	box-sizing: border-box;
	z-index: 101;
	position: relative;
	padding: 16px 16px 0 22px;

	.content {
		position: relative;
		width: 100%;
		height: 100%;
		// height: calc(100% - 74px);
		display: flex;
		justify-content: flex-start;
		flex-wrap: wrap;
		align-items: center;
		overflow: auto;

		&::-webkit-scrollbar {
			width: 4px; /* 滚动条宽度 */
		}

		//轨道
		&::-webkit-scrollbar-track {
			background: #04244e; /* 滚动条轨道背景 */
			border-radius: 10px;
			padding: 5px 0;
		}

		//滑块
		&::-webkit-scrollbar-thumb {
			background: #82aed0; /* 滑块背景颜色 */
			border-radius: 10px;
			height: 15px;
		}

		//悬停滑块颜色
		&::-webkit-scrollbar-thumb:hover {
			background: #ff823b; /* 鼠标悬停时滑块颜色 */
		}

		//箭头
		&::-webkit-scrollbar-button {
			display: none; /* 隐藏滚动条的箭头按钮 */
		}

		&.no-active-content {
			height: 0;
			display: none;
		}

		.cont {
			width: 100%;
			height: 100%;

			.cont1 {
				width: 100%;

				display: flex;
				justify-content: center;
				align-items: center;

				.cont1Item1 {
					float: left;
					width: 64px;
					cursor: pointer;

					img {
						width: 39px;
						height: 40px;
					}

					.cont1Name {
						font-family: PingFangSC, PingFang SC;
						font-size: 16px;
						color: #45daff;
						line-height: 18px;
						text-shadow: 0px 0px 4px rgba(0, 175, 255, 0.5), 0px 0px 1px rgba(0, 30, 50, 0.5);
						text-align: center;
						font-style: normal;
						background: linear-gradient(180deg, #ffffff 0%, #00b8ff 100%);
						background-clip: text;
						-webkit-background-clip: text;
						-webkit-text-fill-color: transparent;
						margin-top: 12px;
						opacity: 0.56;
					}

					.cont1Active {
						background: linear-gradient(180deg, #ffffff 0%, #00b8ff 100%);
						background-clip: text;
						-webkit-background-clip: text;
						-webkit-text-fill-color: transparent;
						opacity: 1;
					}
				}

				.cont1Item2 {
					float: left;
					width: 106px;
					height: 10px;
					margin: 0 5px;
					margin-top: 17px;
				}

				.cont1Item3 {
					float: left;
					width: 64px;
					cursor: pointer;

					img {
						width: 39px;
						height: 40px;
					}

					.cont1Name {
						font-family: PingFangSC, PingFang SC;
						font-size: 16px;
						color: #45daff;
						line-height: 18px;
						text-shadow: 0px 0px 4px rgba(0, 175, 255, 0.5), 0px 0px 1px rgba(0, 30, 50, 0.5);
						text-align: center;
						font-style: normal;
						background: linear-gradient(180deg, #ffffff 0%, #00b8ff 100%);
						background-clip: text;
						-webkit-background-clip: text;
						-webkit-text-fill-color: transparent;
						margin-top: 12px;
						opacity: 0.56;
					}

					.cont1Active {
						background: linear-gradient(180deg, #ffffff 0%, #00b8ff 100%);
						background-clip: text;
						-webkit-background-clip: text;
						-webkit-text-fill-color: transparent;
						opacity: 1;
					}
				}
			}

			.cont2 {
				width: 100%;
				margin: 0 auto;
				margin-top: 17px;
				border: 1px solid;
				border-image: linear-gradient(270deg, rgba(5, 155, 238, 0.2), rgba(5, 155, 238, 0.5), rgba(5, 155, 238, 0.2)) 1 1;

				.contList {
					width: 100%;
					height: 56px;
					line-height: 56px;
					padding: 0 27px 0 23px;

					&:nth-child(odd) {
						background: rgba(0, 101, 183, 0.15);
					}

					.contLeft {
						height: 100%;
						float: left;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 14px;
						color: #abdcf4;
						text-align: left;
						font-style: normal;

						span {
							height: 100%;
							font-family: PingFangSC, PingFang SC;
							font-weight: 500;
							font-size: 16px;
							color: #8bc7ff;
							text-align: left;
							font-style: normal;
							margin-right: 11px;
						}
					}

					.contRight {
						width: 270px;
						height: 56px;
						float: right;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 14px;
						color: #ffffff;
						text-align: right;
						font-style: normal;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}

					.contContent {
						padding: 12px 0;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 14px;
						color: #ffffff;
						line-height: 20px;
						text-align: right;
						font-style: normal;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 14px;
						color: #ffffff;
						line-height: 20px;
						text-align: right;
						font-style: normal;
					}
				}

				.contList1 {
					width: 100%;
					height: 96px;
					padding: 16px 8px;
					display: flex;
					justify-content: flex-start;
					flex-direction: column;
					align-items: flex-start;
					background: rgba(0, 101, 183, 0.15);

					span {
						font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
						font-weight: normal;
						font-size: 14px;
						color: #d0deee;
						text-align: left;
						font-style: normal;
						text-transform: none;
						margin-bottom: 8px;
					}

					.contContent {
						width: 100%;
						font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
						font-weight: normal;
						font-size: 14px;
						color: #ffffff;
						line-height: 20px;
						padding-left: 8px;

						text-align: left;
						font-style: normal;
						text-transform: none;
					}
				}
			}

			.cont3 {
				width: 100%;
				height: 17px;
				margin: 0 auto;
				margin-top: 30px;
				background: url(~@/assets/bjnj/rwxfTitle.png) no-repeat center / 100% 100%;
				font-family: PingFangSC, PingFang SC;
				font-size: 22px;
				color: #ffbc6c;
				line-height: 17px;
				letter-spacing: 2px;
				text-align: left;
				font-style: normal;
				padding-left: 29px;
			}

			.cont4 {
				margin-top: 21px;
				width: 100%;
				height: 528px;
				overflow-y: auto;
				&::-webkit-scrollbar {
					/*滚动条整体样式*/
					width: 6px;
					background: transparent;
					// border: 1px solid #999;
					/*高宽分别对应横竖滚动条的尺寸*/
					// height: 1px;
				}
				&::-webkit-scrollbar-thumb {
					/*滚动条里面小方块*/
					border-radius: 2px;
					box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
					background: rgb(45, 124, 228);
					height: 100px;
				}

				.cont4List {
					width: 100%;
					height: 78px;
					margin: 0 auto;
					margin-bottom: 20px;

					.cont4List1 {
						width: 100%;
						height: 78px;
						background: url(~@/assets/bjnj/rwxfBg.png) no-repeat center / 100% 100%;

						.cont4List1Left {
							float: left;
							padding: 16px 0 0 27px;

							.cont4List1Left1 {
								font-family: PingFangSC, PingFang SC;
								font-size: 20px;
								color: #ffffff;
								line-height: 26px;
								text-align: left;
								font-style: normal;
							}

							.cont4List1Left2 {
								font-family: PingFangSC, PingFang SC;
								font-weight: 400;
								font-size: 14px;
								color: #adddff;
								line-height: 21px;
								text-align: left;
								font-style: normal;
								margin-top: 6px;

								.cont4List1Left3 {
									float: left;
									margin-right: 20px;
								}

								.cont4List1Left4 {
									float: left;
								}
							}
						}

						.cont4List1Right1 {
							float: right;
							width: 55px;
							height: 21px;
							background: linear-gradient(180deg, rgba(140, 2, 0, 0.09) 0%, rgba(255, 0, 0, 0.62) 100%);
							box-shadow: inset 0px 0px 11px 0px rgba(140, 70, 70, 0.5);
							border-radius: 2px;
							border: 1px solid #ffa6a6;
							margin: 28px 14px 0 0;

							span {
								font-family: PingFangSC, PingFang SC;
								font-weight: 400;
								font-size: 12px;
								color: #ffffff;
								line-height: 21px;
								text-shadow: 0px 0px 6px #e64d00;
								text-align: center;
								font-style: normal;
								background: linear-gradient(180deg, #ffffff 0%, #b32424 100%);
								background-clip: text;
								-webkit-background-clip: text;
								-webkit-text-fill-color: transparent;
							}
						}

						.cont4List1Right2 {
							float: right;
							width: 55px;
							height: 21px;
							background: linear-gradient(180deg, rgba(0, 82, 140, 0.3) 0%, #0e4d8c 100%);
							box-shadow: inset 0px 0px 15px 0px rgba(70, 111, 140, 0.5);
							border-radius: 2px;
							border: 1px solid #a6daff;
							margin: 28px 14px 0 0;

							span {
								font-family: PingFangSC, PingFang SC;
								font-weight: 400;
								font-size: 12px;
								color: #ffffff;
								line-height: 17px;
								text-shadow: 0px 0px 8px #00ace6;
								text-align: center;
								font-style: normal;
								background: linear-gradient(90deg, #e6fdff 0%, #248fb3 100%);
								background-clip: text;
								-webkit-background-clip: text;
								-webkit-text-fill-color: transparent;
							}
						}
					}

					.cont4List2 {
						width: 438px;
						height: 36px;
						background: rgba(77, 159, 252, 0.3);

						.cont4Left {
							float: left;
							width: 334px;
							height: 36px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 14px;
							color: rgba(255, 255, 255, 0.9);
							line-height: 36px;
							text-align: left;
							font-style: normal;
							padding-left: 20px;
						}

						.cont4Right {
							float: left;
							width: 104px;
							height: 36px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 14px;
							color: rgba(255, 255, 255, 0.9);
							line-height: 36px;
							text-align: left;
							font-style: normal;
						}
					}

					.cont4List3 {
						width: 438px;
						height: 36px;
						background: rgba(77, 149, 252, 0.1);
						margin-top: 6px;

						.cont4Left {
							float: left;
							width: 334px;
							height: 36px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 14px;
							color: #ffffff;
							line-height: 36px;
							text-align: left;
							font-style: normal;
							padding-left: 20px;
						}

						.cont4Right {
							float: left;
							width: 104px;
							height: 36px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 14px;
							color: #08dffd;
							line-height: 36px;
							text-align: left;
							font-style: normal;
						}
					}
				}
			}

			.cont5 {
				width: 100%;
				margin: 0 auto;
				margin-top: 17px;
				border: 1px solid;
				border-image: linear-gradient(270deg, rgba(5, 155, 238, 0.2), rgba(5, 155, 238, 0.5), rgba(5, 155, 238, 0.2)) 1 1;

				.contList {
					width: 100%;
					height: 56px;
					line-height: 56px;
					padding: 0 27px 0 23px;

					&:nth-child(odd) {
						background: rgba(0, 101, 183, 0.15);
					}

					.contLeft {
						height: 100%;
						float: left;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 14px;
						color: #abdcf4;
						text-align: left;
						font-style: normal;

						span {
							height: 100%;
							font-family: PingFangSC, PingFang SC;
							font-weight: 500;
							font-size: 16px;
							color: #8bc7ff;
							text-align: left;
							font-style: normal;
							margin-right: 11px;
						}
					}

					.contRight {
						width: 270px;
						height: 56px;
						float: right;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 14px;
						color: #ffffff;
						text-align: right;
						font-style: normal;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}

					.contContent {
						padding: 12px 0;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 14px;
						color: #ffffff;
						line-height: 20px;
						text-align: right;
						font-style: normal;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 14px;
						color: #ffffff;
						line-height: 20px;
						text-align: right;
						font-style: normal;
					}
				}

				.contList1 {
					width: 100%;
					height: 96px;
					padding: 16px 8px;
					display: flex;
					justify-content: flex-start;
					flex-direction: column;
					align-items: flex-start;
					background: rgba(0, 101, 183, 0.15);


					span {
						font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
						font-weight: normal;
						font-size: 14px;
						color: #d0deee;
						text-align: left;
						font-style: normal;
						text-transform: none;
						margin-bottom: 8px;
					}

					.contContent {
						width: 100%;
						font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
						font-weight: normal;
						font-size: 14px;
						color: #ffffff;
						line-height: 20px;
						padding-left: 8px;

						text-align: left;
						font-style: normal;
						text-transform: none;
					}
				}
			}
		}
	}
}
</style>
