import http from '@/utils/fzjc/request';
import { finaUrl, finaUrls } from '@/utils/fzjc/const';


// 标题1
export const title1Jk = () => {
  return http.get(
    finaUrl + '/api/assist/countAssistOrderVO'
  );
};
// 标题2 巡查次数
export const title2Xccs = () => {
  return http.get(
    finaUrl + '/api/assist/countPatrol?areaCode=A25015002003'
  );
};
// 标题2 事件采集
export const title2Sjcj = () => {
  return http.get(
    finaUrl + '/api/assist/countOrderInfo?areaCode=A25015002003'
  );
};

// 事件类型占比分析
export const sjlxzb = () => {
  return http.get(
    finaUrl + '/api/eventType/getData?flag=2'
  );
};

// 事件数量排名分析
export const sjslpm = () => {
  return http.get(
    finaUrl + '/api/eventArea/getData?flag=3'
  );
};

// 事件趋势分析 上报数量 办结率
export const sjqsfx = () => {
  return http.get(
    finaUrl + '/api/eventTrend/getTrend'
  );
};

// 事件高发类型
export const sjgflxTop5 = () => {
  return http.get(
    finaUrl + '/api/eventType/getData?flag=2'
  );
};

// 事件高发类型
export const getRysjCondition = () => {
  return http.post(
    finaUrls + '/bigScreenForMc/getRysjCondition?deptCode=D001'
  );
};

// 地图打点
export const getAllOrderInfo = () => {
  return http.get(
    finaUrl + '/api/orderInfo/getAllOrderInfo'
  );
};

// 地图打点
export const countAssist = () => {
  return http.get(
    finaUrl + '/api/assist/countAssist'
  );
};

// 满意度
export const getCountLevel = () => {
  return http.get(
    finaUrl + '/api/external/countLevel'
  );
};

export const getEventDetail = (data) => {
  return http.get(
    finaUrl + '/api/eventType/getData',data
  );
};

export const getTrendByArea = (data) => {
  return http.get(
    finaUrl + '/api/eventTrend/getTrendByArea',data
  );
};






// // 区域排名
// export const areaRank = () => {
//   return http.get(
//     finaUrl + '/api/civilizedCityController/civilizedCityAreaSort'
//   );
// };
// //  事件数量趋势变化
// export const sjqsSjs = () => {
//   return http.get(
//     finaUrl + '/api/civilizedCityController/civilizedCityTimeStatistics?type=1'
//   );
// };
// // 事件办结率趋势变化
// export const sjqsBjl = () => {
//   return http.get(
//     finaUrl + '/api/civilizedCityController/civilizedCityFinishPreTimeStatistics?type=1'
//   );
// };
// // 标题
// export const titleJk = () => {
//   return http.get(
//     finaUrl + '/api/civilizedCityController/civilizedCityStatistics'
//   );
// };

// // 事件类型区域占比 柱状图
// export const sjlxAreaBar = (classifyId) => {
//   return http.get(
//     finaUrl + '/api/civilizedCityController/civilizedCityAreaStatistics?type=2'+'&level=2'+'&classifyId='+classifyId
//   );
// };

// // 事件类型占比 饼图（类型）
// export const sjlxTypePie = (classifyId) => {
//   return http.get(
//     finaUrl + '/api/civilizedCityController/civilizedCityTypeStatistics?type=1'+'&level=2'+'&classifyId='+classifyId
//   );
// };

// // 事件类型占比 饼图（区域）
// export const sjlxAreaPie = (classifyId) => {
//   return http.get(
//     finaUrl + '/api/civilizedCityController/civilizedCityAreaStatistics?type=1'+'&level=2'+'&classifyId='+classifyId
//   );
// };
