<!--
 * @Author: srd18862913192 <EMAIL>
 * @Date: 2023-01-09 16:19:55
 * @LastEditors: srd18862913192 <EMAIL>
 * @LastEditTime: 2023-01-12 09:36:07
 * @FilePath: \ywtgdp\src\components\csts\djylPop.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <div class="djylPop">
    <div class="title">
      <span>党建引领更多</span>
    </div>
    <div class="close" @click="close"></div>

    <div class="wrapper">
      <div class="left">
        <BlockBox title="经济发展总体态势" class="left1" :isListBtns="false" :blockHeight="797">
          <div class="subTitle">年龄统计</div>
          <PieChart3D type="1" :data="data4" style="height:230px;"></PieChart3D>
          <div class="subTitle">学历情况统计</div>
          <div class="con">
            <div class="con_left">
              <RingChart
                :data="data5"
                style="width:100%;height:260px"
                :options="options5"
              ></RingChart>
            </div>
            <div class="con_right">
                <div class="item" v-for="(item,index) in data6" :key="index">
                     <img :src="item.url" alt="" />
                     <div class="item_right">
                        <span class="item_name">{{item.name}}</span>
                        <span class="item_num">{{item.num}}</span>
                     </div>
                </div>
            </div>
          </div>
        </BlockBox>
      </div>
      <div class="center">
        <BlockBox title="三会一课" class="center1" :isListBtns="false" :blockHeight="360">
          <StackZebraChart :data="data3" style="width:100%;height:300px" :options="options3" />
        </BlockBox>
        <BlockBox title="学习强国排名TOP5" class="center2" :isListBtns="false" :blockHeight="360">
          <RankBarChartE02 :data="data1" :options="options1"></RankBarChartE02>
        </BlockBox>
      </div>
      <div class="right">
        <BlockBox title="人才基本情况" class="right1" :isListBtns="false" :blockHeight="360">
            <div class="con">
                <div class="item" v-for="(item,index) in data7" :key="index">
                    <img :src="item.url" alt="">
                    <div class="item_right">
                        <div class="item_num">{{ item.num }}</div>
                        <div class="item_name">{{ item.name }}</div>
                    </div>
                </div>
            </div>
          <GroupColChart
            :data="data2"
            style="width:100%;height:220px"
            :options="options2"
          ></GroupColChart>
        </BlockBox>
        <BlockBox title="干部基本情况" class="right2" :isListBtns="false" :blockHeight="360">
            <div class="con">
                <div class="item" v-for="(item,index) in data8"  :key="index">
                    <div :class="'item_num'+index">{{ item.num }}</div> 
                    <div class="item_name">{{ item.name }}</div> 
                </div>
            </div>

            <pieChartShzl  style="width:100%;height:200px" :data="pieChartData"/>
            <!-- <div class="chart-5">
            <pie-charts
                :sourceData="barData7"
                :type="2"
                :text="'学历'"
                :x="'100'"
                :isOpenTimer="false"
            />
            </div> -->
        </BlockBox>
      </div>
    </div>
    </div>
    <transition name="fade">
      <div
        class="bg-header"
      ></div>
    </transition>
  </div>
</template>

<script>
import BlockBox from '@/components/leader/common/blockBox.vue'
import PieCharts from '@/components/leader/leader_city/PieCharts.vue';
import pieChartShzl from '@/components/shzl/pieChartShzl.vue'
export default {
  name: 'djylPop',
  components: {
    BlockBox,
    PieCharts,
    pieChartShzl
  },
  data() {
    return {
      data1: [
        ['name', 'value'],
        ['鼓楼区', '11'],
        ['玄武区', '15'],
        ['秦淮区', '9'],
        ['建邺区', '15'],
        ['江宁区', '8']
      ],
      data2: [
        ['product', '队伍'],
        ['大专', 800],
        ['本科', 1400],
        ['硕士', 1020],
        ['博士', 2000]
      ],
      data3: [
        ['product', '党员支部大会', '支部委员会', '党小组会', '党课'],
        ['1', 1000, 2000, 4000, 20000],
        ['2', 15000, 2000, 4000, 20000],
        ['3', 25000, 20000, 4000, 20000],
        ['4', 5000, 20000, 4000, 20000]
      ],
      options3: {
        yAxisMax: 100000,
        colors: ['#00A3D7', '#5900ED', '#0080ED', '#FF9201'],
        showBarLabel: true,
        rectWidth: 20,
        rectHeight: 5,
        rectMargin: 1
      },
      data4: [
        ['product', '城市部件'],
        ['10年以下 21.94%', 2400],
        ['10-20年 21.94%', 2100],
        ['20-40年 28.74%', 3006],
        ['40年以上 9.08%', 1000]
      ],
      options1: {
        // color: ['#FFCA62', '#FFA436', '#FFD400', '#FF9201', '#DCFF5B', '#00A0FF']
        color: ['#FF3A00', '#FFD84E', '#00FFEC', '#00A5FF', '#00A5FF']
      },
      options2: {
        color: ['#00F7FF'],
        gradientColors: [['#00F7FF', '#2AD4A6']],
        legend: {
          show: false
        }
      },
      data5: [
        ['name', 'value'],
        ['研究生', 9300],
        ['本科', 3500],
        ['大专', 7100],
        ['大专以下', 7200]
      ],
      options5: {
        "unit": "人",
        color: ['#2EF6FF ', '#6D5AE2', '#FFBA4F', '#07E936']
      },
      data6:[{
        name:'预备党员(人)',
        num:2,
        url:require('@/assets/shzl/csyj_icon1.png'),
      },{
        name:'发展对象(人)',
        num:2,
        url:require('@/assets/shzl/csyj_icon1.png'),
      },{
        name:'积极分子(人)',
        num:2,
        url:require('@/assets/shzl/csyj_icon1.png'),
      },{
        name:'正式党员(人)',
        num:2,
        url:require('@/assets/shzl/csyj_icon1.png'),
      }],
      data7:[{
        name:'人才总量（大专以上）',
        num:10,
        url:require('@/assets/shzl/csyj_icon1.png'),
      },{
        name:'硕士',
        num:10,
        url:require('@/assets/shzl/csyj_icon1.png'),
      },{
        name:'博士',
        num:10,
        url:require('@/assets/shzl/csyj_icon1.png'),
      }],
      data8:[{
        name:'干部总数(人)',
        num:34
      },{
        name:'正科级(人)',
        num:34
      },{
        name:'副科级(人)',
        num:147
      }],
      barData7: [
        { name: '中专以下', value: 21 },
        { name: '大专学历', value: 21 },
        { name: '本科学历', value: 21 },
        { name: '研究生以上', value: 21 }
      ],
      pieChartData: [
        ['product', '学历信息'],
        ['中专以下', 1234],
        ['大专学历', 234],
        ['本科学历', 1001],
        ['涉教人员', 1234],
        ['研究生以上', 1234],
      ]
    }
  },
  methods: {
    close() {
      this.$emit('closeEmit')
    }
  }
}
</script>

<style lang="scss" scoped>
 .bg-header {
    position: absolute;
    width: 100%;
    height: 1080px;
    background: rgba(2, 14, 50, 0.75);
    z-index: 999999;
    top:0;
    left:0;
  }
.djylPop {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 1300px;
  height: 820px;
  padding: 0 20px;
  background: rgba(0, 23, 59, 0.9);
  box-shadow: 0px 0px 43px 0px rgba(11, 54, 138, 0.35), 0px 3px 16px 0px rgba(0, 0, 0, 0.7),
    inset 0px 0px 16px 0px rgba(17, 146, 214, 0.35);
  border-radius: 11px;
  border: 1px solid #015c8c;
  z-index: 99999999;

  .title {
    margin: 0 auto;
    width: 634px;
    height: 74px;
    background: url(~@/assets/csts/title1.png) no-repeat;
    text-align: center;
    margin-bottom: 10px;

    span {
      display: inline-block;
      line-height: 74px;
      font-size: 40px;
      font-family: YouSheBiaoTiHei;
      color: #ffffff;
      background: linear-gradient(180deg, #ffffff 0%, #2aebff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .subTitle {
    width: 373px;
    height: 17px;
    background: url(~@/assets/shzl/map/djyl_pop1.png) no-repeat center / 100% 100%;
    background-size: 100% 100%;
    font-size: 22px;
    font-family: PangMenZhengDao;
    color: #ffbc6c;
    display: flex;
    align-items: center;
    padding-left: 29px;
    text-align: left;
    margin: 37px 0 18px 27px;
  }

  .close {
    position: absolute;
    width: 44px;
    height: 44px;
    top: 16px;
    right: 28px;
    background: url(~@/assets/csts/close1.png) no-repeat;
    cursor: pointer;
  }

  .wrapper {
    display: flex;

    .left {
      width: 400px;
      margin-left: 20px;
      .left1{
        .con{
            display:flex;
            .con_left{
                width:50%;

            }
            .con_right{
                .item{
                    margin-bottom:20px;
                    display:flex;
                    .item_right{
                        background: url(~@/assets/shzl/csyj_iconBg.png) no-repeat center / 100% 100%;
                        background-size: 100% 100%;
                        width:168px;
                        height:38px;
                        display:flex;
                        align-items: center;
                        padding:0 8px;
                        justify-content: space-between;
                        .item_name{
                           color:#C2DDFC; 
                           font-size:14px;
                        }
                        .item_num{
                           color:#fff; 
                           font-size:20px;
                           font-weight: bold;
                        }
                    }
                }
            }
        }
      }
    }

    .center {
      width: 400px;
      margin-left: 20px;
    }

    .right {
      width: 400px;
      margin-left: 20px;
      .right1{
        .con{
            display: flex;
            .item{
                display:flex;
                margin-left:16px;
                height:80px;
                align-items: center;
                img{
                    width:41px;
                    height:41px;
                }
                .item_right{
                    margin-left:10px;
                    text-align: left;
                    .item_name{
                           color:#C2DDFC; 
                           font-size:14px;
                        }
                        .item_num{
                           color:#fff; 
                           font-size:20px;
                           font-weight: bold;
                        }
                }
            }
        }
      }

      .right2{
        .con{
            display: flex;
            height: 80px;
            align-items: center;
            .item{
                margin:0 30px;
                text-align: left;
                .item_name{
                    color: #ffffff;
                    font-size:14px;
                    line-height: 20px;
                }
                .item_num0{
                    font-size:22px;
                    font-weight: bold;
                    color: #FFFFFF;
                    line-height: 26px;
                    text-shadow: 0px 0px 4px rgba(0,0,0,0.5);
                    background: linear-gradient(180deg, #FFFFFF 0%, #00FFFA 67%, #04A1FF 100%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                }
                .item_num1{
                    font-size: 22px;
                    font-family: DINAlternate-Bold, DINAlternate;
                    font-weight: bold;
                    color: #FFFFFF;
                    line-height: 26px;
                    text-shadow: 0px 0px 4px rgba(0,0,0,0.5);
                    background: linear-gradient(180deg, #FFFFFF 0%, #FFCB00 67%, #FF8704 100%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                }
                .item_num2{
                    font-size:22px;
                    font-weight: bold;
                    color: #FFFFFF;
                    line-height: 26px;
                    text-shadow: 0px 0px 4px rgba(0,0,0,0.5);
                    background: linear-gradient(180deg, #FFFFFF 0%, #00FFFA 67%, #04A1FF 100%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                }
            }
        }

        .chart-5 {
        width: 100%;
        height: 244px;
      }
      }
    }
  }
}
</style>
