<template>
  <div v-if="show">
    <div class="ai_waring">
      <div class="title">
        <span>历史作业</span>
      </div>
      <div class="close_btn" @click="closeEmitai"></div>
      <div class="content">
        <BlockBox
          title="基本信息"
          :isListBtns="false"
          class="box1"
          :blockHeight="192"
          :blockWidth="1428"
          :isLong="true"
        >
          <ul>
            <li v-for="(item, index) in infoData" :key="index">
              <span class="name">{{ item.name }}：</span>
              <span class="value">{{ item.value }}</span>
            </li>
          </ul>
        </BlockBox>
        <BlockBox
          v-if="btInfo.length"
          title="补贴信息"
          :isListBtns="false"
          class="box2"
          :blockHeight="102"
          :blockWidth="1428"
          :isLong="true"
        >
          <ul>
            <li v-for="(item, index) in btInfo" :key="index">
              <span class="name">{{ item.name }}：</span>
              <span class="value">{{ item.value }}</span>
            </li>
          </ul>
        </BlockBox>
        <BlockBox
          v-if="workData.length"
          title="工况信息"
          :isListBtns="false"
          class="box3"
          :blockHeight="142"
          :blockWidth="1428"
          :isLong="true"
        >
          <ul>
            <li v-for="(item, index) in workData" :key="index">
              <span class="name">{{ item.ocParamName }}：</span>
              <span class="value">{{  item.value && item.value != 'null' ? item.value : '-'}}{{ `(${item.ocParamDesc && item.ocParamDesc != 'null' ? item.ocParamDesc : '-'})` }}</span>
            </li>
          </ul>
        </BlockBox>

        <BlockBox
          title="运行记录"
          :isListBtns="false"
          class="box2"
          :blockHeight="319"
          :blockWidth="1428"
          :isLong="true"
        >
          <div class="check_types">
            <div class="jjcd">
              <span>作业日期：</span>
              <!-- <el-date-picker v-model="gdObj.createDatetime" type="date" placeholder="选择上报时间"></el-date-picker> -->
              <el-date-picker
                popper-class="elDatePicker"
                v-model="operateTime"
                style="border-color:#008aff;color: #008aff;"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetimerange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </div>
            <div class="type_btns">
              <div
                class="defaultBtn"
                :class="currentIndex == index ? 'currentBtn' : ''"
                v-for="(item, index) of btnList"
                :key="index"
                @click="handleClick(index)"
              >
                {{ item }}
              </div>
            </div>
          </div>
          <div class="content2">
            <el-table
              :data="tableList"
              empty-text="暂无数据"
              :height="280 + 'px'"
              :row-class-name="tableClass"
            >
              <el-table-column
                label="序号"
                type="index"
                :index="indexMethod"
                align="center"
                width="80"
              >
              </el-table-column>
              <el-table-column label="作业编号" prop="termId" align="center"></el-table-column>
              <el-table-column
                label="作业日期"
                prop="workStartTime"
                align="center"
              ></el-table-column>
              <el-table-column label="作业量(亩)" prop="workArea" align="center"></el-table-column>
              <el-table-column label="作业时长(h)" prop="workTime" align="center"></el-table-column>
              <el-table-column label="作业位置" prop="areaName" align="center"></el-table-column>
              <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                  <div class="btn" @click="operate({ name: '作业轨迹' }, scope.row)">作业轨迹</div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="footer_">
            <el-pagination
              @current-change="pageNumChange"
              :current-page="pageNum"
              :page-size="pageSize"
              layout="total, prev, pager, next"
              :total="total"
            ></el-pagination>
          </div>
        </BlockBox>
      </div>
    </div>
  </div>
</template>

<script>
import BlockBox from '@/components/leader/common/blockBox7.vue'
import SwiperTableMap from './table/historyTable.vue'
// api
import { getEventType, getUnDoneEventList, getDoneEventList, getQgdSssj } from '@/api/hs/hs.api.js'
import { myMixins } from '@/libs/myMixins.js'
import {
  ddqqScreen,
  getAgmachWorkConditionNew,
  getAgmachWorkHistory,
  querySubsidyInfo
} from '@/api/bjnj/zhdd.js'
import { agmachToTermId } from '@/api/njzl/hs.api.js'

export default {
  name: 'historyWorkPop',
  mixins: [myMixins],
  components: { SwiperTableMap, BlockBox },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default: () => {
        return []
      }
    },
    agmachId: {
      //农机基础的农机ID
      type: String,
      default: ''
    },
    termAgmachId: {
      //终端元数据的农机id
      type: String,
      default: ''
    },
    agmachInfo: {
      required: true,
      type: Object
    }
  },
  data() {
    return {
      tabActive: 0,
      currentIndex: -1,
      tabs: ['未办结', '办结'],
      btnList: ['查询', '重置'],
      btnsArr: [
        {
          name: '未办结',
          normalBg: require('@/assets/shzl/map/left_n.png'),
          activeBg: require('@/assets/shzl/map/left_a.png')
        },
        {
          name: '已办结',
          normalBg: require('@/assets/shzl/map/right_n.png'),
          activeBg: require('@/assets/shzl/map/right_a.png')
        }
      ],
      urgentOptions: [
        {
          value: '0',
          label: '普通'
        },
        {
          value: '1',
          label: '突发'
        },
        {
          value: '2',
          label: '重要'
        },
        {
          value: '3',
          label: '紧急'
        }
      ],
      eventTypeOptions: [
        {
          value: '0',
          label: '普通'
        }
      ],
      timeOutOptions: [
        {
          value: '0',
          label: '是'
        },
        {
          value: '1',
          label: '否'
        }
      ],
      proityValue: '',
      timeOutValue: '',
      settled: true,
      total: 0,
      pageNum: 1,
      pageSize: 10,
      finished: 1,
      tableList: [],
      operateTime: [],
      gdObj: {
        jjcdValue: '',
        sqlxValue: ''
      },
      infoData: [
        {
          name: '农机编号',
          value: '-'
        },
        {
          name: '农机品牌',
          value: '-'
        },
        {
          name: '农机品目',
          value: '-'
        },
        {
          name: '车牌号',
          value: '-'
        },
        {
          name: '累计作业',
          value: '-'
        },
        {
          name: '农机状态',
          value: '-'
        },
        {
          name: '联系人',
          value: '-'
        },

        {
          name: '联系方式',
          value: '-'
        },
        {
          name: '所属单位',
          value: '-'
        },
        {
          name: '所在区域',
          value: '-'
        },
        {
          name: '具体位置',
          value: '-'
        }
      ],
      btInfo: [
        {
          name: '购机人',
          value: '-'
        },
        {
          name: '购机时间',
          value: '-'
        },
        {
          name: '销售价格',
          value: '-'
        },
        {
          name: '补贴额',
          value: '-'
        },
        {
          name: '补贴办理地区',
          value: '-'
        },
        {
          name: '补贴办理状态',
          value: '-'
        }
      ],
      workData: [
        {
          agmachTypeId: '150303',
          ocParamType: 'P7',
          ocParamName: '行驶总里程',
          ocParamDesc: '-',
          status: 0,
          dataSources: 1,
          createUser: 'ZHOUZHIGAO',
          createTime: '2023-12-26 11:52:00',
          updateUser: 'ZHOUZHIGAO',
          updateTime: '2023-12-26 11:52:00',
          value: '-'
        },
        {
          agmachTypeId: '150303',
          ocParamType: 'P6',
          ocParamName: '发动机实际扭矩百分比口',
          ocParamDesc: '-',
          status: 0,
          dataSources: 1,
          createUser: 'ZHOUZHIGAO',
          createTime: '2023-12-26 11:52:00',
          updateUser: 'ZHOUZHIGAO',
          updateTime: '2023-12-26 11:52:00',
          value: '-'
        },
        {
          agmachTypeId: '150303',
          ocParamType: 'P5',
          ocParamName: '每小时油耗',
          ocParamDesc: '-',
          status: 0,
          dataSources: 1,
          createUser: 'ZHOUZHIGAO',
          createTime: '2023-12-26 11:52:00',
          updateUser: 'ZHOUZHIGAO',
          updateTime: '2023-12-26 11:52:00',
          value: '-'
        },
        {
          agmachTypeId: '150303',
          ocParamType: 'P4',
          ocParamName: '燃油消耗总量',
          ocParamDesc: '-',
          status: 0,
          dataSources: 1,
          createUser: 'ZHOUZHIGAO',
          createTime: '2023-12-26 11:52:00',
          updateUser: 'ZHOUZHIGAO',
          updateTime: '2023-12-26 11:52:00',
          value: '-'
        },
        {
          agmachTypeId: '150303',
          ocParamType: 'P3',
          ocParamName: '发动机工作时间',
          ocParamDesc: '-',
          status: 0,
          dataSources: 1,
          createUser: 'ZHOUZHIGAO',
          createTime: '2023-12-26 11:52:00',
          updateUser: 'ZHOUZHIGAO',
          updateTime: '2023-12-26 11:52:00',
          value: '-'
        },
        {
          agmachTypeId: '150303',
          ocParamType: 'P2',
          ocParamName: '机油压力',
          ocParamDesc: '-',
          status: 0,
          dataSources: 1,
          createUser: 'ZHOUZHIGAO',
          createTime: '2023-12-26 11:52:00',
          updateUser: 'ZHOUZHIGAO',
          updateTime: '2023-12-26 11:52:00',
          value: '-'
        },
        {
          agmachTypeId: '150303',
          ocParamType: 'P1',
          ocParamName: '发动机转速',
          ocParamDesc: '-',
          status: 0,
          dataSources: 1,
          createUser: 'ZHOUZHIGAO',
          createTime: '2023-12-26 11:52:00',
          updateUser: 'ZHOUZHIGAO',
          updateTime: '2023-12-26 11:52:00',
          value: '-'
        },
      ]
    }
  },
  watch: {
    qgdCodejjcdOptions(val) {
      if (this.qgdCodejjcdOptions.length > 0) {
        // this.getQgdSssj()
      }
    }
  },
  computed: {
    show() {
      return this.value
    }
  },
  mounted() {},
  methods: {
    init() {
      this.getAgmachDetail()
      this.pageNum = 1
      this.getAgmachWorkHistory()
    },

    tableClass({ row, rowIndex }) {
      if (rowIndex % 2 == 0) {
        //奇数行，序号不能被2整除
        return 'even-row'
      } else {
        return ''
      }
    },

    indexMethod(index) {
      return index + 1
    },
    // 获取历史作业数据
    getAgmachDetail() {
      let {
        agmachId,
        agmachTypeName,
        userName,
        licensePlateCode,
        agmach_status,
        phoneNo,
        brand,
        address,
        areaName
      } = this.agmachInfo
      this.infoData[0].value = agmachId && agmachId != 'null' ? agmachId : '-'
      this.infoData[1].value = brand && brand != 'null' ? brand : '-'
      this.infoData[2].value = agmachTypeName && agmachTypeName != 'null' ? agmachTypeName : '-'
      this.infoData[3].value =
        licensePlateCode && licensePlateCode != 'null' ? licensePlateCode : '-'
      this.infoData[4].value = '-'
      this.infoData[5].value = agmach_status && agmach_status != 'null' ? agmach_status : '-'
      this.infoData[6].value = userName && userName != 'null' ? userName : '-'
      this.infoData[7].value = phoneNo && phoneNo != 'null' ? phoneNo : '-'
      this.infoData[9].value = areaName && areaName != 'null' ? areaName : '-'
      this.infoData[10].value = address && address != 'null' ? address : '-'
      // this.infoData[8].value = phoneNo || '-'
      // this.infoData[9].value = phoneNo || '-'
      // this.infoData[10].value = phoneNo || '-'

      agmachToTermId({
        agmachIds: this.agmachInfo.agmachId
      }).then(res => {
        this.getGkInfo(res.data[0].term_agmach_id)
        this.getBtInfo(res.data[0].term_agmach_id)
      })
    },
    getGkInfo(term_agmach_id) {
      // 获取工况信息
      getAgmachWorkConditionNew({
        agmachId: term_agmach_id,
        agmachTypeId: this.agmachInfo.agmachTypeCode
      }).then(response => {
        console.log('response', response)
        if (response.data.length > 0) {
          this.workData = response.data[0].ocData
        } else {
          this.workData = []
        }
      })
    },
    getAgmachWorkHistory() {
      agmachToTermId({
        agmachIds: this.agmachInfo.agmachId
      }).then(info => {
        let params = {
          agmachId: info.data[0].term_agmach_id,
          // agmachId: this.agmachInfo.agmachId,
          // agmachId: 'eda8c94c6c784b58b4ee02120c521051',
          startTime: this.operateTime[0],
          endTime: this.operateTime[1],
          // pageNo: this.pageNum,
          // pageSize: this.pageSize,
          page_no: this.pageNum,
          page_size: this.pageSize
        }
        getAgmachWorkHistory(params).then(res => {
          console.log(res, 'getAgmachWorkHistory')
          // if (res && res.code == 0) {
          //   this.tableList = res.data[0].map((it, i) => [
          //     i + 1,
          //     it.workStartTime,
          //     it.workType,
          //     it.workArea,
          //     it.workTime,
          //     it,
          //   ])
          //   this.total = res.data[1][0].total
          // }
          if (res && res.code == 0) {
            this.total = res.data[0].total
            this.tableList = res.data[0].rows
          }
        })
      })
    },
    getBtInfo(term_agmach_id) {
      querySubsidyInfo({
        agmachId: term_agmach_id
      }).then(res => {
        const { code, data } = res
        if (code == 0 && data.length > 0) {
          const {
            buyer,
            purchase_time,
            sales_price,
            subsidy_amount,
            handle_area,
            handle_status
          } = data[0]
          this.btInfo[0].value = buyer && buyer != 'null' ? buyer : '-'
          this.btInfo[1].value = purchase_time && purchase_time != 'null' ? purchase_time : '-'
          this.btInfo[2].value = sales_price && sales_price != 'null' ? sales_price : '-'
          this.btInfo[3].value = subsidy_amount && subsidy_amount != 'null' ? subsidy_amount : '-'
          this.btInfo[4].value = handle_area && handle_area != 'null' ? handle_area : '-'
          this.btInfo[5].value = handle_status && handle_status != 'null' ? handle_status : '-'
          console.log('this.btInfo', this.btInfo)
        } else {
          this.btInfo = []
        }
      })
    },
    // async getCscpBasicHxItemCode1() {
    //   let res = await getCscpBasicHxItemCode('requestType')
    //   console.log(res)
    //   if (res?.code == '0') {
    //     this.qgdCodejjcdOptions = res.data
    //   }
    // },
    // async getCscpBasicHxItemCode2() {
    //   let res = await getCscpBasicHxItemCode('zstatus')
    //   console.log(res)
    //   if (res?.code == '0') {
    //     this.qgdCodesqlxOptions = res.data
    //   }
    // },
    operate(name, it) {
      const { mdlName, userName, phoneNo } = this.agmachInfo
      this.$emit('operate', name, { ...it, mdlName, userName, phoneNo })
    },
    closeEmitai() {
      this.$emit('input', false)
    },
    // 页码改变
    pageNumChange(val) {
      this.pageNum = val
      this.getAgmachWorkHistory()
    },
    async getQgdSssj() {
      this.tableList = []
      let res = await getQgdSssj({
        page: this.pageNum,
        size: this.pageSize,
        emeLevel: this.gdObj.jjcdValue,
        appealType: this.gdObj.sqlxValue
      })
      console.log('实时事件弹窗', res)
      console.log(this.qgdCodejjcdOptions)
      if (res?.code == '200' && res.result.data.length > 0) {
        console.log('res.result.data', res.result.data)
        this.tableList = res.result.data.map((it, i) => [
          this.pageSize * (this.pageNum - 1) + i + 1,
          it.orderNum,
          it.emeLevel,
          it.appealContent,
          it.appealType,
          it.community,
          it.eventDate,
          it.formStatus,
          it.eventLocation,
          it.id,
          it
        ])
        this.total = res.result.recordsTotal
      }
      // console.log('this.tableList', this.tableList)
    },
    removeEmptyValues(obj) {
      for (const key in obj) {
        if (typeof obj[key] === 'object') {
          this.removeEmptyValues(obj[key])
        }
        if (!obj[key] || (typeof obj[key] === 'object' && !Reflect.ownKeys(obj[key]).length)) {
          delete obj[key]
        }
      }
    },

    handleClick(index) {
      this.currentIndex = index
      if (index == 0) {
        this.searchBtn()
      } else {
        this.resetBtn()
      }
    },
    // 搜索
    searchBtn() {
      this.pageNum = 1
      this.getAgmachWorkHistory()
      // let data = {
      //   id: this.gdObj.id,
      //   requestType: this.gdObj.requestType,
      //   dead: this.gdObj.dead,
      //   createDatetime: this.gdObj.createDatetime ? dayjs(this.gdObj.createDatetime).format('YYYYMMDD') : '',
      //   current: this.pageNum,
      //   size: this.pageSize,
      //   taskTest: 0
      // }
      // this.removeEmptyValues(data)
      // data.taskTest = 0
      // console.log(data)
      // this.ddqqScreen(data)
    },
    // 重置
    resetBtn() {
      this.pageNum = 1
      this.operateTime = []
      this.getAgmachWorkHistory()
    }
  }
}
</script>
<style lang="less">
.elDatePicker.el-picker-panel {
  color: #fff; //设置当前面板的月份的字体为白色，记为1
  background: #002450; //定义整体面板的颜色
  border: 1px solid #1384b4; //定义整体面板的轮廓
  .el-input__inner {
    background: #002450;
    color: #fff;
  }
  .el-picker-panel__icon-btn {
    //设置年份月份调节按钮颜色，记为2
    color: #ffffff;
  }
  .el-date-picker__header-label {
    //设置年月显示颜色，记为3
    color: #ffffff;
  }
  .el-date-table th {
    //设置星期颜色，记为4
    color: #ffffff;
  }
  .el-picker-panel__footer {
    background: #002450;
  }
  .el-date-table td.in-range div {
    background: #0081ff;
  }
}
</style>
<style lang="less" scoped>
.bg-header {
  position: absolute;
  width: 100%;
  height: 1080px;
  background: rgba(2, 14, 50, 0.75);
  z-index: 999999;
  top: 0;
  left: 0;
}

/deep/ .ivu-select {
  width: 189px;
}

/deep/ .ivu-select-selection {
  width: 189px;
  height: 34px;
  background: rgba(0, 74, 143, 0.4)
    linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
}

/deep/ .ivu-select-selection .ivu-select-selected-value {
  font-size: 13px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 34px;
}

/deep/ .ivu-input {
  background: rgba(0, 74, 143, 0.4)
    linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 22px;
}

/deep/ .ivu-select-placeholder {
  height: 34px !important;
  font-size: 13px !important;
  line-height: 34px !important;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff !important;
}

/deep/ .ivu-page-item {
  background: transparent;
  color: #fff;
}

/deep/ .ivu-page-item a {
  color: #fff;
}

/deep/ .ivu-page-prev,
/deep/ .ivu-page-next {
  background: transparent;
}

/deep/ .el-input__inner {
  color: #ccf4ff;
}

.ai_waring {
  width: 1508px;
  height: 982px;
  background: rgba(9, 19, 34, 0.95);
  box-shadow: 0px 0px 22px 0px rgba(11, 54, 138, 0.35), 0px 2px 8px 0px rgba(0, 0, 0, 0.7),
    inset 0px 0px 8px 0px rgba(17, 146, 214, 0.35);
  border-radius: 11px;
  border: 1px solid #015c8c;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1500;

  .title {
    margin: 0 auto 23px;
    width: 634px;
    height: 72px;
    line-height: 72px;
    background: url(~@/assets/map/dialog/title7.png) no-repeat;
    display: grid;
    place-items: center;

    span {
      display: inline-block;
      font-size: 36px;
      font-family: PingFangSC, PingFang SC;
      background: linear-gradient(180deg, #ffffff 35%, #0079ff 70%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .close_btn {
    position: absolute;
    width: 23px;
    height: 23px;
    top: 24px;
    right: 27px;
    background: url(~@/assets/map/dialog/btn3.png) no-repeat;
    cursor: pointer;
  }

  .content {
    padding: 0 30px;
    .box1 {
      height: 200px;

      ul {
        width: 100%;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        padding-top: 20px;

        li {
          text-align: left;
          width: 33.3%;
          margin-bottom: 10px;
          padding-left: 33px;
          display: flex;
          // justify-content: space-between;
          align-items: center;
          &:last-child {
            width: 66.7%;
          }
          .name {
            height: 17px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: rgba(106, 146, 187, 1);
            line-height: 17px;
            text-align: center;
            font-style: normal;
          }

          .value {
            height: 17px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #ffffff;
            line-height: 17px;
            text-align: center;
            font-style: normal;
          }
        }
      }
    }
    .box2 {
      height: 200px;
      // overflow: scroll;
      ul {
        width: 100%;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        padding-top: 10px;

        li {
          text-align: left;
          width: 33.3%;
          margin-bottom: 10px;
          padding-left: 33px;
          display: flex;
          align-items: center;
          // justify-content: space-between;
          .name {
            height: 17px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: rgba(106, 146, 187, 1);
            line-height: 17px;
            text-align: center;
            font-style: normal;
          }

          .value {
            height: 17px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #ffffff;
            line-height: 17px;
            text-align: center;
            font-style: normal;
          }
        }
      }
      .check_types {
        text-align: left;
        display: flex;

        .jjcd {
          display: flex;
          align-items: center;
          margin-right: 24px;

          span {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
          }

          /deep/ .el-range-input {
            background: transparent !important;
            color: #fff;
          }
        }

        .report_time {
          margin-left: 26px;
          display: flex;
          align-items: center;

          span {
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #ffffff;
            line-height: 22px;
          }
        }

        .istime_out {
          margin-left: 26px;
          align-items: center;
          display: flex;

          span {
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #ffffff;
            line-height: 22px;
          }
        }

        .type_btns {
          margin-left: 27px;
          display: flex;
          align-items: center;

          .defaultBtn {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #0081ff;
            line-height: 34px;
            font-style: normal;
            text-transform: none;
            width: 58px;
            height: 34px;
            background: url(~@/assets/shzl/map/btn_act.png) no-repeat center / 100% 100%;
            background-size: 100% 100%;
            text-align: center;
            cursor: pointer;
            &:nth-of-type(2) {
              margin-left: 20px;
            }
          }
          .currentBtn {
            color: #ffffff;
            background: url(~@/assets/shzl/map/btn.png) no-repeat center / 100% 100%;
          }
        }
      }
      .content2 {
        margin-top: 15px;
        .btn {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: rgba(0, 255, 251, 0.9);
          line-height: 21px;
          text-align: center;
          font-style: normal;
          text-decoration-line: underline;
          // text-align: center;
          &:hover {
            cursor: pointer;
            opacity: 0.8;
          }
        }
      }

      .content2 ::v-deep .el-table,
      .el-table__expanded-cell {
        background-color: transparent;
      }
      .content2 ::v-deep .el-table th {
        border: none;
        background-color: rgba(77, 159, 252, 0.3) !important;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.9);
        line-height: 21px;
        text-align: center;
        font-style: normal;
      }
      .content2 ::v-deep .el-table::before {
        height: 0px;
      }
      .content2 ::v-deep .el-table--border::after {
        width: 0px;
      }
      // .content2 ::v-deep .el-table--border {
      //   border: none;
      // }
      .content2 ::v-deep .el-table tr {
        background-color: transparent;
        border: none !important;
      }
      .content2 ::v-deep .el-table--enable-row-transition .el-table__body td,
      .el-table .cell {
        // height: 30px !important;
        border: none !important;
        background-color: transparent;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.85);
      }
      /* 双数行背景颜色 */
      .content2 ::v-deep .even-row {
        background-color: rgba(255, 255, 255, 0.06) !important;
      }

      // ----------修改elementui表格的默认样式-----------
      .content2 ::v-deep .el-table__body-wrapper {
        &::-webkit-scrollbar {
          // 整个滚动条
          width: 0; // 纵向滚动条的宽度
          background: rgba(213, 215, 220, 0.3);
          border: none;
        }
        &::-webkit-scrollbar-track {
          // 滚动条轨道
          border: none;
        }
      }

      // --------------------隐藏table gutter列和内容区右侧的空白 start
      .content2 ::v-deep .el-table th.gutter {
        display: none;
        width: 0;
      }
      .content2 ::v-deep .el-table colgroup col[name='gutter'] {
        display: none;
        width: 0;
      }

      .content2 ::v-deep .el-table__body {
        width: 100% !important;
      }
      .footer_ {
        margin: 40px auto 0;
      }
      ::v-deep .el-input__inner,
      ::v-deep .el-pagination__total,
      ::v-deep .el-pagination__jump,
      ::v-deep .el-pagination button,
      ::v-deep .el-pager li {
        background-color: transparent !important;
        color: #fff !important;
      }
      ::v-deep .el-pager li.active {
        color: #409eff !important;
      }
    }
    .box3 {
      ul {
        height: 100px;
        overflow-y: scroll;
        &::-webkit-scrollbar {
          /*滚动条整体样式*/
          // width: 2px;
          background: transparent;
        }
        width: 100%;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;

        li {
          text-align: left;
          width: 33.3%;
          margin-top: 10px;
          padding-left: 33px;
          display: flex;
          align-items: center;
          .name {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: rgba(106, 146, 187, 1);
            line-height: 17px;
            text-align: center;
            font-style: normal;
          }

          .value {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #ffffff;
            line-height: 18px;
            text-align: center;
            font-style: normal;
          }
        }
      }
      .check_types {
        text-align: left;
        display: flex;

        .jjcd {
          display: flex;
          align-items: center;
          margin-right: 24px;

          span {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
          }

          /deep/ .el-range-input {
            background: transparent !important;
          }
        }

        .report_time {
          margin-left: 26px;
          display: flex;
          align-items: center;

          span {
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #ffffff;
            line-height: 22px;
          }
        }

        .istime_out {
          margin-left: 26px;
          align-items: center;
          display: flex;

          span {
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #ffffff;
            line-height: 22px;
          }
        }

        .type_btns {
          margin-left: 27px;
          display: flex;
          align-items: center;

          .defaultBtn {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #0081ff;
            line-height: 34px;
            font-style: normal;
            text-transform: none;
            width: 58px;
            height: 34px;
            background: url(~@/assets/shzl/map/btn_act.png) no-repeat center / 100% 100%;
            background-size: 100% 100%;
            text-align: center;
            cursor: pointer;
            &:nth-of-type(2) {
              margin-left: 20px;
            }
          }
          .currentBtn {
            color: #ffffff;
            background: url(~@/assets/shzl/map/btn.png) no-repeat center / 100% 100%;
          }
        }
      }
      .content2 {
        margin-top: 15px;
        .btn {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: rgba(0, 255, 251, 0.9);
          line-height: 21px;
          text-align: center;
          font-style: normal;
          text-decoration-line: underline;
          // text-align: center;
          &:hover {
            cursor: pointer;
            opacity: 0.8;
          }
        }
      }

      .content2 ::v-deep .el-table,
      .el-table__expanded-cell {
        background-color: transparent;
      }
      .content2 ::v-deep .el-table th {
        border: none;
        background-color: rgba(77, 159, 252, 0.3) !important;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.9);
        line-height: 21px;
        text-align: center;
        font-style: normal;
      }
      .content2 ::v-deep .el-table::before {
        height: 0px;
      }
      .content2 ::v-deep .el-table--border::after {
        width: 0px;
      }
      // .content2 ::v-deep .el-table--border {
      //   border: none;
      // }
      .content2 ::v-deep .el-table tr {
        background-color: transparent;
        border: none !important;
      }
      .content2 ::v-deep .el-table--enable-row-transition .el-table__body td,
      .el-table .cell {
        // height: 30px !important;
        border: none !important;
        background-color: transparent;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.85);
      }
      /* 双数行背景颜色 */
      .content2 ::v-deep .even-row {
        background-color: rgba(255, 255, 255, 0.06) !important;
      }

      // ----------修改elementui表格的默认样式-----------
      .content2 ::v-deep .el-table__body-wrapper {
        &::-webkit-scrollbar {
          // 整个滚动条
          width: 0; // 纵向滚动条的宽度
          background: rgba(213, 215, 220, 0.3);
          border: none;
        }
        &::-webkit-scrollbar-track {
          // 滚动条轨道
          border: none;
        }
      }

      // --------------------隐藏table gutter列和内容区右侧的空白 start
      .content2 ::v-deep .el-table th.gutter {
        display: none;
        width: 0;
      }
      .content2 ::v-deep .el-table colgroup col[name='gutter'] {
        display: none;
        width: 0;
      }

      .content2 ::v-deep .el-table__body {
        width: 100% !important;
      }
      .footer_ {
        margin: 40px auto 0;
      }
      ::v-deep .el-input__inner,
      ::v-deep .el-pagination__total,
      ::v-deep .el-pagination__jump,
      ::v-deep .el-pagination button,
      ::v-deep .el-pager li {
        background-color: transparent !important;
        color: #fff !important;
      }
      ::v-deep .el-pager li.active {
        color: #409eff !important;
      }
    }
    .btns {
      // border: 1px solid red;
      margin: 0 auto;
      display: flex;
      justify-content: center;

      & > div {
        width: 154px;
        height: 53px;

        span {
          font-size: 24px;
          font-family: PingFangSC, PingFang SC;
          color: #ffffff;
          line-height: 53px;
          background: linear-gradient(180deg, #ffffff 0%, #ffeba0 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
  }
}

// /deep/ .ivu-page-total {
//   font-size: 18px;
//   color: #fff;
// }
</style>
