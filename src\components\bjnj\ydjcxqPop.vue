<template>
  <div v-if="show">
    <div class="ai_waring">
      <div class="title">
        <span>应对举措详情</span>
      </div>
      <div class="close_btn" @click="closeEmitai"></div>
      <div class="label">{{list.area_name}}&nbsp;&nbsp;&nbsp;{{list.dt_day}}</div>
      <div class="content">
        <div class="content1">{{list.current_dfct_issues}}</div>
        <div class="content1">{{list.current_ctmss}}</div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  workScheduleInfo
} from '@/api/njzl/zyzx.js'

export default {
  name: 'RealTimeEventDialogQgd',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    list: {
      type: Object,
      default: () => {}
    },
  },
  data() {
    return {
      place: '',
      time: '',
      content: '',
    }
  },
  watch: {
  },
  computed: {
    show() {
      return this.value
    }
  },
  mounted() {
  },
  methods: {
    closeEmitai() {
      this.$emit('input', false)
    },
    async workScheduleInfo(params) {
      this.place = params[2]
      this.time = params[4]
      let res = await workScheduleInfo({
        id:params[5]
      })

      this.content = res.data[0]
    },
  },
}
</script>

<style lang="less" scoped>
.bg-header {
  position: absolute;
  width: 100%;
  height: 1080px;
  background: rgba(2, 14, 50, 0.75);
  z-index: 999999;
  top: 0;
  left: 0;
}


.zwsjImg{
  width: 250px;
  margin: 20px 0;
}
/deep/ .ivu-select {
  width: 189px;
}

/deep/ .ivu-select-selection {
  width: 189px;
  height: 34px;
  background: rgba(0, 74, 143, 0.4) linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
}

/deep/ .ivu-select-selection .ivu-select-selected-value {
  font-size: 13px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 34px;
}

/deep/ .ivu-input {
  background: rgba(0, 74, 143, 0.4) linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 22px;
}

/deep/ .ivu-select-placeholder {
  height: 34px !important;
  font-size: 13px !important;
  line-height: 34px !important;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff !important;
}

/deep/ .ivu-page-item {
  background: transparent;
  color: #fff;
}

/deep/ .ivu-page-item a {
  color: #fff;
}

/deep/ .ivu-page-prev,
/deep/ .ivu-page-next {
  background: transparent;
}

/deep/ .el-input__inner {
  color: #CCF4FF;
}

.ai_waring {
  width: 813px;
  background: rgba(9, 19, 34, 0.95);
  box-shadow: 0px 0px 22px 0px rgba(11, 54, 138, 0.35), 0px 2px 8px 0px rgba(0, 0, 0, 0.7),
    inset 0px 0px 8px 0px rgba(17, 146, 214, 0.35);
  border-radius: 11px;
  border: 1px solid #015c8c;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1100;

  .title {
    margin: 0 auto 18px;
    width: 634px;
    height: 73px;
    line-height: 73px;
    background: url(~@/assets/map/dialog/title7.png) no-repeat center / 100% 100%;
    display: grid;
    place-items: center;
    font-family: YouSheBiaoTiHei;

    span {
      display: inline-block;
      font-size: 36px;
      font-family: YouSheBiaoTiHei;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .close_btn {
    position: absolute;
    width: 23px;
    height: 23px;
    top: 24px;
    right: 27px;
    background: url(~@/assets/map/dialog/btn3.png) no-repeat center / 100% 100%;
    cursor: pointer;
  }

  .label {
    font-family: SourceHanSansCN, SourceHanSansCN;
    font-weight: 400;
    font-size: 16px;
    color: #FFFFFF;
    line-height: 26px;
    text-align: center;
    font-style: normal;
    text-transform: none;
    margin-bottom: 18px;
  }

  .content {
    padding: 0 36px 39px 44px;
    font-family: SourceHanSansCN, SourceHanSansCN;
    font-weight: 400;
    font-size: 16px;
    color: #FFFFFF;
    line-height: 26px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    .content1 {
      margin-bottom: 28px;
    }

    .btns {
      // border: 1px solid red;
      margin: 0 auto;
      display: flex;
      justify-content: center;

      &>div {
        width: 154px;
        height: 53px;

        span {
          font-size: 20px;
          font-family: YouSheBiaoTiHei;
          color: #ffffff;
          line-height: 53px;
          background: linear-gradient(180deg, #ffffff 0%, #ffeba0 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }

    .check_types {
      text-align: left;
      display: flex;

      .jjcd {
        display: flex;
        align-items: center;
        margin-right: 24px;

        span {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
        }

        /deep/ .el-input {
          width: 189px;
          height: 34px;

          .el-input__inner {
            width: 189px;
            height: 34px;
            background: linear-gradient(180deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #FFFFFF 100%), rgba(0, 74, 143, 0.4);
            border: 1px solid rgba(0, 162, 255, 0.6);
          }
        }
        /deep/ .el-input__inner {
          width: 378px;
        }
      }

      .report_time {
        margin-left: 26px;
        display: flex;
        align-items: center;

        span {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
      }

      .istime_out {
        margin-left: 26px;
        align-items: center;
        display: flex;

        span {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
      }

      .type_btns {
        margin-left: 27px;
        display: flex;
        align-items: center;

        & div {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 43px;
          background: url(~@/assets/shzl/map/type_btn.png) no-repeat center / 100% 100%;
          background-size: 100% 100%;
          text-align: center;
          cursor: pointer;

          &:first-of-type {
            width: 98px;
            height: 43px;
          }

          &:nth-of-type(2) {
            margin-left: 20px;
            width: 98px;
            height: 43px;
          }

          &:nth-of-type(3) {
            margin-left: 20px;
            width: 98px;
            height: 43px;
          }
        }
      }
    }

    .table_box {
      margin-top: 12px;
      overflow: hidden;
    }

    .fy_page {
      margin-top: 8px;

      /deep/ .ivu-page-total {
        margin-top: 0;
      }
    }

    .tableBox {
      height: 41px;
      margin-bottom: 21px;

      .tableItem {
        float: left;
        width: 180px;
        height: 41px;
        background: url(~@/assets/map/dialog/btn5.png) no-repeat center / 100% 100%;
        margin-right: 10px;
        font-family: PingFangSC, PingFang SC;
        font-size: 22px;
        color: #ffffff;
        line-height: 41px;
        text-align: center;
        font-style: normal;
        text-transform: none;

        &:nth-child(1),
        &:nth-child(2),
        &:nth-child(3) {
          width: 250px;
        }

        span {
          background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          font-family: YouSheBiaoTiHei;
          cursor: pointer;
        }
      }

      .tableActive {
        background: url(~@/assets/map/dialog/btn4.png) no-repeat center / 100% 100%;

        span {
          font-size: 22px;
          color: #ffffff;
          // font-weight: 600;
          background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          font-family: YouSheBiaoTiHei;
        }
      }
    }
  }
}

// /deep/ .ivu-page-total {
//   font-size: 18px;
//   color: #fff;
// }</style>
