:root {
  --search-height: 30px;
  --meet-height: 100%;
  --meet-width: 100%;
  --meet-tab-height: 5%;
  --meet-main-height: 100%;
  --meet-container-width: 75%;
  --meet-container-tab-height: 5%;
  --meet-container-main-height: 86%;
  --meet-container-bt-height: calc(100% - var(--meet-container-tab-height) - var(--meet-container-main-height));
  --meet-side-width: calc(100% - var(--meet-container-width));
  --meet-side-list-height: calc(100% - var(--search-height) - 8px);
  --meet-side-listItem-height: 52px;
}

.rtc-meet {
  background: var(--base-background);
  color: var(--base-color);
  box-sizing: border-box;
  overflow: hidden;
  border: 1px solid var(--base-line-color);
}

/* 会议弹出层样式 */
.meet-pop {
  /* min-height: 80vh;
  min-width: 80vw; */
  height: var(--meet-height);
  width: var(--meet-width);
}

/* 会议内嵌样式 */
.meet-inner {
  height: 100%;
  width: 100%;
}

.rtc-meet .meet-tab {
  position: relative;
  height: var(--meet-tab-height);
  background: var(--base-background);
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid var(--base-line-color);
}

.rtc-meet button {
  margin: 0px 4px;
}

.rtc-meet .meet-tab .actions {
  position: absolute;
  right: 4px;
}

.rtc-meet .talking {
  margin-right: 12px;
  color: #fff;
}

.rtc-meet .talking .name {
  color: var(--red-corlor);
}

.rtc-meet .meet-main {
  height: var(--meet-main-height);
  width: 100%;
  display: flex;
}

.meet-main .meet-container {
  width: var(--meet-container-width);
  flex: 1;
}

.meet-container[fullscreen='true'] {
  position: fixed;
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
}

.meet-main .meet-side {
  width: var(--meet-side-width);
}

.meet-side[showUser='false'] {
  display: none;
}

.meet-container .meet-top-tools {
  background: var(--meet-top-tools-bg);
}

.meet-container .meet-top-tools .right-info {
  display: flex;
  align-items: center;
}

.meet-container .meet-top-tools .right-info .actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.meet-top-tools .layout .active .iconfont {
  color: var(--primary-color);
}

.meet-top-tools .right-info .time {
  color: var(--base-color);
  font-size: 14px;
}

.meet-container .video-container {
  padding: 0 2px;
  position: relative;
  background: #000;
}

.meet-container .video-container video {
  height: 100%;
  width: 100%;
}

.meet-container .yuyin {
  color: #fff;
  font-size: 50px;
}

.meet-container .meet-bottom-tools {
  display: flex;
  justify-content: space-between;
  padding: 4px 10px;
  box-sizing: border-box;
}

.meet-container .meet-bottom-tools .tool-btn {
  flex: 1;
  height: 100%;
  line-height: 1;
  color: var(--gary-2);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: none;
  border-radius: var(--radius-1);
}

.meet-bottom-tools .tool-btn:hover {
  /* background: var(--gary-5); */
}

.meet-bottom-tools .tool-btn .iconfont {
  font-size: 24px;
  margin-bottom: 4px;
}

.meet-bottom-tools .tool-btn.meetOff {
  background: red;
  color: var(--reverse-base-color);
}

.rtc-meet .common-top-tools {
  height: var(--meet-container-tab-height);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 4px;
}

.common-top-tools .iconfont {
  font-size: 20px;
  color: var(--gary-1);
}

.common-container {
  height: var(--meet-container-main-height);
  box-sizing: border-box;
}

.common-bottom-tools {
  height: var(--meet-container-bt-height);
}

.meet-side .side-tools {
  border-bottom: 1px solid var(--base-line-color2);
}

.meet-side .common-container {
  padding: 4px;
}

.meet-side .common-container .search-wrap {
  height: var(--search-height);
  display: flex;
  justify-content: flex-start;
  align-items: center;
  border: 1px solid var(--gary-1);
  border-radius: 4px;
  color: var(--gary-1);
  padding: 4px;
  box-sizing: border-box;
  font-size: 14px;
}

.meet-side .search-wrap input {
  height: 100%;
  width: 100%;
  color: var(--gary-1);
}

.meet-side .user-list {
  margin-top: 8px;
  height: var(--meet-side-list-height);
  overflow: auto;
}

.meet-side .user-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: var(--meet-side-listItem-height);
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
  margin-top: 8px;
}

.meet-side .user-item .left-info {
  height: 100%;
  display: flex;
  align-items: center;
}

.meet-side .user-item .avatar {
  height: var(--meet-side-listItem-height);
  width: var(--meet-side-listItem-height);
  border-radius: 50%;
}

.meet-side .user-item .info-desc {
  height: 100%;
  margin-left: 8px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.meet-side .user-item .info-desc .desc {
  color: var(--gary-1);
  font-size: 13px;
}

.meet-side .user-item .user-btn .iconfont {
  color: var(--gary-4);
  font-size: 18px;
}

.meet-side .meet-controls {
  display: flex;
  align-items: center;
  justify-content: space-around;
  border-top: 1px solid var(--base-line-color2);
  border-left: 1px solid var(--base-line-color2);
}

.meet-side .meet-controls .actions {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.meet-side .meet-controls button {
  border: 1px solid var(--gary-4);
  /* box-shadow: 1px 1px 5px 1px var(--gary-4); */
  color: var(--primary-color);
  border-radius: 4px;
  padding: 8px 8px;
}

/* txdd样式 */
.txdd_box .meet-side .user-item .left-info.txdd_left_info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.txdd_box .meet-side .user-item .left-info.txdd_left_info .info-desc {
  height: 50px;
  align-items: center;
}

.txdd_box .meet-side .user-item.txdd_user_item {
  padding-bottom: 22px;
  border: none;
  /* margin-bottom: 30px; */
}

.txdd_box .meet-side .user-item.txdd_user_item .info-desc {
  margin-top: 5px;
}

.txdd_box .meet-side .user-item.txdd_user_item .info-desc .name {
  font-size: 18px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #FFFFFF;
  line-height: 25px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 100%;
  text-align: center;
}

.txdd_box .meet-side .user-item.txdd_user_item .info-desc .desc {
  font-size: 16px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #ABDCF4;
  line-height: 22px;
  letter-spacing: 2px;
}

