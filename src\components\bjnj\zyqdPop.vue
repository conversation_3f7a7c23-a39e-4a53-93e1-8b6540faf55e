<template>
  <div v-if="show">
    <div class="ai_waring">
      <div class="title">
        <span>资源清单</span>
      </div>
      <div class="close_btn" @click="closeEmitai"></div>
      <div class="content">
        <div class="list" v-for="(item, index) of list" :key="index">
          <div class="list1">
            <div class="list1Item1">{{item.title}}</div>
            <div class="list1Item2">
              <div class="list1Item3">联系人: {{item.name}}</div>
              <div class="list1Item4">联系方式: {{item.phone}}</div>
            </div>
          </div>
          <div class="list2">
            <SwiperTableMap
              :titles="[
                '序号',
                '农机编码',
                '农机品目',
                '农机型号',
                '车牌号',
              ]"
              :widths="['20%', '20%', '20%', '20%', '20%']"
              :data="item.lists"
              :contentHeight="'144px'"
              :settled="settled"
              @operate="operate"
            ></SwiperTableMap>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SwiperTableMap from './table/RealTimeEventTable2.vue'
// api
import { getEventType, getUnDoneEventList, getDoneEventList, getQgdSssj } from '@/api/hs/hs.api.js'
import { myMixins } from '@/libs/myMixins.js'

export default {
  name: 'RealTimeEventDialogQgd',
  mixins: [myMixins],
  components: { SwiperTableMap },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default: () => {
        return [
          {
            title: '周村应急救灾中心',
            name: '王建国',
            phone: '13808932467',
            lists: [
              [
                '1',
                '234356',
                '履带式收割机',
                'LD-1224',
                '苏A90233',
              ],
              [
                '2',
                '234356',
                '履带式收割机',
                'LD-1224',
                '苏A90233',
              ],
              [
                '3',
                '234356',
                '履带式收割机',
                'LD-1224',
                '苏A90233',
              ],
              [
                '4',
                '234356',
                '履带式收割机',
                'LD-1224',
                '苏A90233',
              ],
              [
                '5',
                '234356',
                '履带式收割机',
                'LD-1224',
                '苏A90233',
              ],
            ],
          },
          {
            title: '周村应急救灾中心',
            name: '王建国',
            phone: '13808932467',
            lists: [
              [
                '1',
                '234356',
                '履带式收割机',
                'LD-1224',
                '苏A90233',
              ],
              [
                '2',
                '234356',
                '履带式收割机',
                'LD-1224',
                '苏A90233',
              ],
              [
                '3',
                '234356',
                '履带式收割机',
                'LD-1224',
                '苏A90233',
              ],
              [
                '4',
                '234356',
                '履带式收割机',
                'LD-1224',
                '苏A90233',
              ],
              [
                '5',
                '234356',
                '履带式收割机',
                'LD-1224',
                '苏A90233',
              ],
            ],
          },
        ]
      }
    }
  },
  data() {
    return {
      tabActive: 0,
      tabs: ['未办结', '办结'],
      btnsArr: [
        {
          name: '未办结',
          normalBg: require('@/assets/shzl/map/left_n.png'),
          activeBg: require('@/assets/shzl/map/left_a.png')
        },
        {
          name: '已办结',
          normalBg: require('@/assets/shzl/map/right_n.png'),
          activeBg: require('@/assets/shzl/map/right_a.png')
        }
      ],
      urgentOptions: [
        {
          value: '0',
          label: '普通'
        },
        {
          value: '1',
          label: '突发'
        },
        {
          value: '2',
          label: '重要'
        },
        {
          value: '3',
          label: '紧急'
        }
      ],
      eventTypeOptions: [
        {
          value: '0',
          label: '普通'
        }
      ],
      timeOutOptions: [
        {
          value: '0',
          label: '是'
        },
        {
          value: '1',
          label: '否'
        }
      ],
      proityValue: '',
      timeOutValue: '',
      settled: true,
      total: 0,
      pageNum: 1,
      pageSize: 10,
      finished: 1,
      tableList: [
        [
          '1',
          '234356',
          '履带式收割机',
          'LD-1224',
          '苏A90233',
        ],
        [
          '2',
          '234356',
          '履带式收割机',
          'LD-1224',
          '苏A90233',
        ],
        [
          '3',
          '234356',
          '履带式收割机',
          'LD-1224',
          '苏A90233',
        ],
        [
          '4',
          '234356',
          '履带式收割机',
          'LD-1224',
          '苏A90233',
        ],
        [
          '5',
          '234356',
          '履带式收割机',
          'LD-1224',
          '苏A90233',
        ],
      ],
      gdObj: {
        jjcdValue: '',
        sqlxValue: ''
      }
    }
  },
  watch: {
    qgdCodejjcdOptions(val) {
      if (this.qgdCodejjcdOptions.length > 0) {
        // this.getQgdSssj()
      }
    }
  },
  computed: {
    show() {
      return this.value
    }
  },
  methods: {
    operate(i, id, it) {
      this.$emit('operate', i, id, it)
    },
    closeEmitai() {
      this.$emit('input', false)
    },
    pageNumChange(val) {
      this.pageNum = val
      this.getQgdSssj()
    },
    async getQgdSssj() {
      this.tableList = []
      let res = await getQgdSssj({
        // street: '1649962979288023040',
        page: this.pageNum,
        size: this.pageSize,
        emeLevel: this.gdObj.jjcdValue,
        appealType: this.gdObj.sqlxValue
      })
      console.log('实时事件弹窗', res)
      console.log(this.qgdCodejjcdOptions)
      if (res?.code == '200' && res.result.data.length > 0) {
        console.log('res.result.data', res.result.data)
        this.tableList = res.result.data.map((it, i) => [
          this.pageSize * (this.pageNum - 1) + i + 1,
          it.orderNum,
          it.emeLevel,
          it.appealContent,
          it.appealType,
          it.community,
          it.eventDate,
          it.formStatus,
          it.eventLocation,
          it.id,
          it
        ])
        this.total = res.result.recordsTotal
      }
      // console.log('this.tableList', this.tableList)
    },
    searchBtn() {
      this.getQgdSssj()
    },
    resetBtn() {
      this.gdObj = {}
      this.getQgdSssj()
    }
  },
  mounted() {}
}
</script>

<style lang="less" scoped>
.bg-header {
  position: absolute;
  width: 100%;
  height: 1080px;
  background: rgba(2, 14, 50, 0.75);
  z-index: 999999;
  top: 0;
  left: 0;
}
/deep/ .ivu-select {
  width: 168px;
}
/deep/ .ivu-select-selection {
  width: 168px;
  height: 34px;
  background: rgba(0, 74, 143, 0.4)
    linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
}
/deep/ .ivu-select-selection .ivu-select-selected-value {
  font-size: 13px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 34px;
}

/deep/ .ivu-input {
  background: rgba(0, 74, 143, 0.4)
    linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
  border: 1px solid rgba(0, 162, 255, 0.6);
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff;
  line-height: 22px;
}
/deep/ .ivu-select-placeholder {
  font-size: 13px !important;
  line-height: 34px !important;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ccf4ff !important;
}
/deep/ .ivu-page-item {
  background: transparent;
  color: #fff;
}
/deep/ .ivu-page-item a {
  color: #fff;
}
/deep/ .ivu-page-prev,
/deep/ .ivu-page-next {
  background: transparent;
}
.ai_waring {
  width: 751px;
  background: rgba(9,19,34,0.9);
  box-shadow: 0px 0px 22px 0px rgba(11,54,138,0.35), 0px 2px 8px 0px rgba(0,0,0,0.7), inset 0px 0px 8px 0px rgba(17,146,214,0.35);
  border-radius: 11px;
  border: 1px solid #015C8C;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1100;
  .title {
    margin: 0 auto 23px;
    width: 634px;
    height: 73px;
    line-height: 73px;
    background: url(~@/assets/map/dialog/title7.png) no-repeat center / 100% 100%;
    display: grid;
    place-items: center;
    span {
      display: inline-block;
      font-size: 36px;
      font-family: PingFangSC, PingFang SC;
      background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .close_btn {
    position: absolute;
    width: 23px;
    height: 23px;
    top: 25px;
    right: 27px;
    background: url(~@/assets/map/dialog/btn3.png) no-repeat;
    cursor: pointer;
  }
  .content {
    padding: 0 44px 32px 46px;
    .list {
      margin-bottom: 26px;
      .list1 {
        width: 661px;
        height: 74px;
        background: url(~@/assets/map/dialog/title6.png) no-repeat center / 100% 100%;
        padding: 12px 0 0 31px;
        .list1Item1 {
          font-family: PingFangSC, PingFang SC;
          font-size: 20px;
          color: #FFFFFF;
          line-height: 26px;
          text-align: left;
          font-style: normal;
        }
        .list1Item2 {
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: 400;
          font-size: 14px;
          color: #ADDDFF;
          line-height: 21px;
          text-align: left;
          font-style: normal;
          margin-top: 3px;
          .list1Item3 {
            float: left;
            margin-right: 36px;
          }
          .list1Item4 {
            float: left;
          }
        }
      }
      .list2 {
        width: 652px;
        height: 144px;
        margin: 0 auto;
      }
    }
  }
}
// /deep/ .ivu-page-total {
//   font-size: 18px;
//   color: #fff;
// }
</style>
