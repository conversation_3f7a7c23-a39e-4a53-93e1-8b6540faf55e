/*
 * @Author: kaix
 * @Date: 2023-05-28 11:04:56
 * @LastEditTime: 2023-05-29 10:06:10
 * @LastEditors: kaix
 * @Description: 大屏 scale 适配方案
 */
let currRenderDom = null;
const autofit = {
  init(options = {}, isShowInitTip = true) {
    return new Promise((resolve, reject) => {
      console.log('autofit init')
      if (isShowInitTip) {
        console.log(
          `%c` + `autofit.js` + ` is running`,
          `font-weight: bold; color: #ffb712; background:linear-gradient(-45deg, #bd34fe 50%, #47caff 50% );background: -webkit-linear-gradient( 120deg, #bd34fe 30%, #41d1ff );background-clip: text;-webkit-background-clip: text; -webkit-text-fill-color:linear-gradient( -45deg, #bd34fe 50%, #47caff 50% ); padding: 8px 12px; border-radius: 4px;`
        );
      }
      let designWidth = options.designWidth || 1920;
      let designHeight = options.designHeight || 929;
      let renderDom = options.renderDom || '#app';
      let resize = options.resize || true;
      let ignore = options.ignore || [];
      let isEqal = options.isEqal;
      currRenderDom = renderDom;
      let dom = document.querySelector(renderDom);
      const style = document.createElement('style');
      style.lang = 'text/css';
      style.id = 'autofit-style';
      style.innerHTML = `
      body {
        overflow: hidden;
      }
    `;
      dom.appendChild(style);
      dom.style.height = `${designHeight}px`;
      dom.style.width = `${designWidth}px`;
      dom.style.transformOrigin = `0 0`;
      keepFit(designWidth, designHeight, dom, ignore, isEqal);
      resize &&
      (window.onresize = () => {
        keepFit(designWidth, designHeight, dom, ignore, isEqal);
      });


      resolve()

    })
  },
  off(renderDom = '#app') {
    window.onresize = null;
    document.querySelector('#autofit-style').remove();
    document.querySelector(currRenderDom ? currRenderDom : renderDom).style = '';
    console.log(
      `%c` + `autofit.js` + ` is off`,
      `font-weight: bold;color: #707070; background: #c9c9c9; padding: 8px 12px; border-radius: 4px;`
    );
  }
};

function keepFit(designWidth, designHeight, dom, ignore, isEqal) {
  console.log("加载 Keepfit")
  let clientHeight = document.documentElement.clientHeight;
  let clientWidth = document.documentElement.clientWidth;
  let scale = 1;
  if (isEqal) {
    scale =
      clientWidth / clientHeight < designWidth / designHeight
        ? clientWidth / designWidth
        : clientHeight / designHeight;
    dom.style.height = `${clientHeight / scale}px`;
    dom.style.width = `${clientWidth / scale}px`;
    dom.style.transform = `scale(${scale})`;
  } else {
    let scaleWidth = clientWidth / designWidth;
    let scaleHeight = clientHeight / designHeight;
    dom.style.height = `${clientHeight / scaleHeight}px`;
    dom.style.width = `${clientWidth / scaleWidth}px`;
    dom.style.transform = `scale(${scaleWidth}, ${scaleHeight})`;
  }
  //

  for (let item of ignore) {
    console.log("ignore");
    console.log("item", item);

    let realScale = item.scale ? item.scale : 1 / scale;
    let realFontSize = realScale != scale ? item.fontSize : 'autofit';
    let realWidth = realScale != scale ? item.width : 'autofit';
    let realHeight = realScale != scale ? item.height : 'autofit';
    document.querySelector('#autofit-style').innerHTML += `${item}{
      transform: scale(${realScale})!important;
      transform-origin: 0 0;
      width: ${realWidth}px!important;
      height: ${realHeight}px!important;
    }`;
    document.querySelector(
      '#autofit-style'
    ).innerHTML += `${item} div ,${item} span,${item} a,${item} *{
    font-size: ${realFontSize}px;
    }`;
  }


}

export default autofit;
