<template>
	<div class="child-block" :style="{ height: blockHeight + 'px' }">
		<slot></slot>

		<div class="content">
			<div class="header-box">
				<div class="header-item-box" v-for="(item, index) in totalData" :key="index">
					<img :src="item.iconUrl" alt="" />
					<div class="header-number-box">
						<div class="number-box">
							<div class="number">{{ item.number }}</div>
							<div class="number-unit">次</div>
						</div>
						<div class="label">{{ item.label }}</div>
					</div>
				</div>
			</div>
			<div class="content-box">
				<div class="content-item-box" v-for="(item, index) in tableData" :key="index" @click.stop="handleItemClick">
					<div class="line1-box">
						<div class="left-box">
							<img :src="item.iconUrl" alt="" />
							<div class="line1-label">{{ item.label }}</div>
						</div>
						<div class="right-box">{{ item.date }}</div>
					</div>
					<div class="line2-box">{{ item.content || '--' }}</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		totalData: {
			type: Array,
			default: () => [],
		},
		tableData: {
			type: Array,
			default: () => [],
		},
		blockHeight: {
			type: Number,
			default: 206,
		},
	},
	data() {
		return {}
	},
	computed: {},
	methods: {
		handleItemClick(){
			this.$emit('handleItemClick')
		}
	},

	watch: {},
}
</script>

<style lang="less" scoped>
.child-block {
	width: 455px;
	box-sizing: border-box;
	z-index: 101;
	position: relative;

	&::-webkit-scrollbar {
		width: 4px; /* 滚动条宽度 */
	}

	//轨道
	&::-webkit-scrollbar-track {
		background: #04244e; /* 滚动条轨道背景 */
		border-radius: 10px;
		padding: 5px 0;
	}

	//滑块
	&::-webkit-scrollbar-thumb {
		background: #82aed0; /* 滑块背景颜色 */
		border-radius: 10px;
		height: 15px;
	}

	//悬停滑块颜色
	&::-webkit-scrollbar-thumb:hover {
		background: #ff823b; /* 鼠标悬停时滑块颜色 */
	}

	//箭头
	&::-webkit-scrollbar-button {
		display: none; /* 隐藏滚动条的箭头按钮 */
	}

	.content {
		position: relative;
		width: 100%;
		// height: calc(100% - 74px);
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
		padding: 28px 0 0 0;

		.header-box {
			width: 100%;
			height: 97px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding-left: 8px;
		}

		.header-item-box {
			width: 168.5px;
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: flex-start;
			position: relative;
			img {
				width: 100%;
				height: 100%;
			}

			.header-number-box {
				position: absolute;
				top: -5px;
				height: 100%;
				display: flex;
				flex-direction: column;
				justify-content: flex-start;
				align-items: center;

				.number-box {
					height: 27px;
					display: flex;
					justify-content: center;
					align-items: center;
					.number {
						height: 100%;
						font-family: PangMenZhengDao-3, PangMenZhengDao-3;
						font-weight: 400;
						font-size: 24px;
						color: #dbf1ff;
						text-shadow: 0px 0px 10px #27a6ff;
						text-align: left;
						font-style: normal;
						text-transform: none;
						margin-right: 3px;
						display: flex;
						justify-content: center;
						align-items: center;
					}

					.number-unit {
						height: 100%;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 16px;
						color: #ffffff;
						text-align: left;
						font-style: normal;
						text-transform: none;
						display: flex;
						justify-content: center;
						align-items: flex-end;
					}
				}

				.devider {
					width: 100%;
					border: 1px dashed #1f2c3f;
					margin: 3px 0;
				}

				.label {
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 14px;
					color: #e2e5e7;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}
			}
		}

		.content-box {
			width: 100%;
			height: 213px;
			display: flex;
			flex-direction: column;
			justify-content: flex-start;
			align-items: flex-start;
			padding: 0 15px;
			padding-left: 22px;

			.content-item-box {
				width: 100%;
				padding: 18px;
				border-top: 1px solid #222b38;
				cursor: pointer;
				.line1-box {
					width: 100%;
					height: 20px;
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 5px;
					margin-left: -2px;
					.left-box {
						height: 100%;
						display: flex;
						justify-content: flex-start;
						align-items: flex-start;

						img {
							width: 22px;
							height: 22px;
							margin-right: 5px;
							margin-top: 3px;
						}

						.line1-label {
							font-family: PingFang SC, PingFang SC;
							font-weight: bold;
							font-size: 14px;
							color: #d65c36;
							text-align: left;
							font-style: normal;
							text-transform: none;
						}
					}

					.right-box {
						height: 100%;
						display: flex;
						justify-content: flex-end;
						align-items: center;
						font-family: PingFang SC, PingFang SC;
						font-weight: bold;
						font-size: 13px;
						color: #abd0f2;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}
				}

				.line2-box {
					width: 100%;
					height: 40px;
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 14px;
					color: #abd0f2;

					text-align: left;
					overflow: hidden; /* 隐藏溢出文本 */
					display: -webkit-box; /* 使用弹性盒子模型 */
					-webkit-line-clamp: 2; /* 限制为两行 */
					-webkit-box-orient: vertical; /* 设置文本的排列方向 */
					text-overflow: ellipsis; /* 在文本溢出时显示省略号 */
				}
			}
		}
	}
}

.popDropDown {
	::v-deep .el-dropdown {
		color: #caecff;
		font-family: YouSheBiaoTiHei;
		font-size: 16px;
		line-height: 21px;
		text-align: right;
		font-style: normal;
		line-height: 40px;
		right: 0;
		position: absolute;
	}
	::v-deep .el-dropdown-menu {
		background-color: #00173b !important;
		border: 1px solid #2b7bbb !important;
	}
	.el-dropdown-menu__item {
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
		line-height: 40px;
		padding: 0 30px;
		&:hover {
			background: #185493;
			color: rgba(255, 255, 255, 0.8);
		}
	}
}
</style>
