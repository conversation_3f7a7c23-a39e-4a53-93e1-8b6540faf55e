<template>
  <div class="ai_waring">
    <div class="title">
      <span>文化旅游更多</span>
    </div>
    <img @click="closeEmit" class="close_btn" src="@/assets/shzl/map/close_btn2.png" alt="" />
    <div class="content">
      <div class="left">
        <BlockBox
          title="文旅资源"
          class="box box1"
          :isListBtns="false"
          :blockHeight="319"
        >
          <div class="ggcxfw">
            <img class="fy_out" src="@/assets/csts/fy_out2.png" alt="" />
            <img class="fy_in" src="@/assets/csts/fy_in12.png" alt="" />
            <div class="ggcxfwItem1">
              <div class="num">23,789</div>
              <div class="name">停车场（个）</div>
            </div>
            <div class="ggcxfwItem2">
              <div class="num">2,078</div>
              <div class="name">共享单车（个）</div>
            </div>
            <div class="ggcxfwItem3">
              <div class="num">578</div>
              <div class="name">公共停车位（位）</div>
            </div>
            <div class="ggcxfwItem4">
              <div class="num">100</div>
              <div class="name">共享单车投放（个）</div>
            </div>
          </div>
        </BlockBox>
        <BlockBox
          title="景点监测"
          class="box box2"
          :isListBtns="false"
          :blockHeight="353"
        >
          <div class="box2Content1">
            <div class="box2Left">
              <div class="box2Name">今日客流量（万人）</div>
              <div class="box2Num">97</div>
            </div>
            <div class="box2Right">
              <div class="box2Name">近30日累计客流量（万人）</div>
              <div class="box2Num">97</div>
            </div>
          </div>
          <div class="box2Content2">
            <GroupColChart :data="qyeqData2" :options="qyeqOptions2"></GroupColChart>
          </div>
        </BlockBox>
      </div>
      <div class="middle">
        <BlockBox
          title="文化产业"
          class="box box3"
          :isListBtns="false"
          :blockHeight="319"
        >
          <div class="box3Content1">
            <div class="contentTitle"><span></span>文化产业增加值（21年）</div>
            <div class="box3item1">
              <img class="box3Img" src="@/assets/csts/whcyImg1.png" alt="">
              <div class="box3Right">
                <div class="box3Name">
                  <img src="@/assets/csts/xsj1.png" alt="">文化产业增加值
                </div>
                <div class="box3Num">50<span>亿元</span></div>
              </div>
            </div>
            <div class="box3item2">
              <img class="box3Img" src="@/assets/csts/whcyImg2.png" alt="">
              <div class="box3Right">
                <div class="box3Name">
                  <img src="@/assets/csts/xsj1.png" alt="">占GDP比重
                </div>
                <div class="box3Num">50<span>%</span></div>
              </div>
            </div>
          </div>
          <div class="box3Content2">
            <div class="contentTitle"><span></span>旅游收入（21年）</div>
            <div class="box3Item">
              <div class="box3Name">旅游总收入</div>
              <div class="box3Num">4,621</div>
              <img src="@/assets/csts/whcyImg3.png" alt="">
            </div>
          </div>
        </BlockBox>
        <BlockBox
          title="住店监测"
          class="box box4"
          :isListBtns="false"
          :blockHeight="353"
        >
          <div class="box4Content1">
            <div class="box4Item">
              <img src="@/assets/csts/zdjc1.png" alt="">
              <div class="box4Right">
                <div class="box4Num">23<span>%</span></div>
                <div class="box4Name">最近入住率</div>
              </div>
            </div>
            <div class="box4Item">
              <img src="@/assets/csts/zdjc1.png" alt="">
              <div class="box4Right">
                <div class="box4Num">2<span>%</span></div>
                <div class="box4Name">本月平均入住率</div>
              </div>
            </div>
          </div>
          <div class="box4Content2">
            <TrendLineChart
              :options="xczfData.options"
              :data="xczfData.data"
              :init-option="{
                yAxis: {
                  name: '',
                },
              }"
            />
          </div>
        </BlockBox>
      </div>
      <div class="right">
        <BlockBox
          title="惠民服务"
          class="box box5"
          :isListBtns="false"
          :blockHeight="319"
        >
          <div class="box5Content1">
            <div class="box5Item">
              <div class="box5Name">上月活动总数</div>
              <div class="box5Num">97场</div>
            </div>
          </div>
          <div class="box5Content2">
            <PieChart3D type="2" :data="wsb.data" :options="wsb.options" />
          </div>
        </BlockBox>
        <BlockBox
          title="游客画像"
          class="box box6"
          :isListBtns="false"
          :blockHeight="353"
        >
          <PieChart3D type="1" :data="data8.data" :options="data8.options" />
        </BlockBox>
      </div>
    </div>
  </div>
</template>

<script>
import BlockBox from '@/components/leader/common/blockBox2.vue'
export default {
  name:'whlyPop',
  data() {
    return {
      qyeqData2: [
        ['product', ''],
        ['北京市', 200],
        ['上海市', 140],
        ['山东省', 102],
        ['河南省', 200],
        ['浙江省', 150],
        ['江苏省', 170]
      ],
      qyeqOptions2: {
        color: ['#00A3D7'],
        stack: true,
        isGradient: true,
        barMaxWidth: 10,
        gradientColors: [
          ['#00A3D7', '#0080ED'],
        ],
        isHor: false,
        barGap: '30%',
        tooltip: {
          color: '#fff',
          show: true,
          type: 'shadow'
        }
      },
      xczfData: {
        data: [
          ['product', ''],
          ['1月', 20],
          ['2月', 30],
          ['3月', 20],
          ['4月', 60],
          ['5月', 40],
          ['6月', 50],
          ['7月', 70],
        ],
        options: {
          smooth: true,
          colors: ['rgba(0, 155, 255, 1)'],
          isToolTipInterval: true,
          toolTipIntervalTime: 5000,
        },
      },
      data8: {
        data: [
          ['product', '年总数'],
          ['70后', 2456],
          ['80后', 1200],
          ['90后', 2000],
          ['00后', 1800],
          ['其他', 2211],
        ],
        options: {
          colors: ['#2EF6FF', '#E6F973', '#6D5AE2', '#5AE29D', '#FFBA4F', '#E05165'],
          alpha: 65,
          pieSize: 220,
          pieInnerSize: 160,
          position: ['50%', '35%'],
          bgImg: {
            top: '40%',
            left: '50%'
          },
          unit: '',
          title: {
            fontSize: '16px',
            top: 110,
          },
          subtitle: {
            fontSize: '14px',
            top: 130,
          },
        }
      },
      wsb: {
        data: [
          ['product', '事件总数'],
          ['培训', 1245],
          ['活动', 1445],
          ['展览', 1100],
          ['演出', 1000],
        ],
        options: {
          // position: ['30%', '10%'],
          legend: {
            top: 0,
            left: 160,
            orient: 'vertical',
          },
          title: {
            fontSize: '18px',
            top: 70,
          },
          subtitle: {
            top: 90,
            fontSize: '12px',
            fontWeight: 400,
            color: '#C2DDFC',
          },
          bgImg: {
            width: '56%',
            height: '90%',
            top: '60%',
            left: '50%',
          },
          unit: '',
        },
      },
    }
  },
  components: {
    BlockBox,
  },
  methods: {
    closeEmit() {
      this.$emit('closeEmit')
    },
  },
}
</script>

<style lang='less' scoped>
@keyframes rotateS {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes rotateN {
  0% {
    transform: rotate(360deg);
  }
  100% {
    transform: rotate(0);
  }
}
@keyframes rotateY {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotateY(360deg);
  }
}
.ai_waring {
  width: 1885px;
  height: 883px;
  padding: 22px;
  // background: rgba(0,23,59,0.95);
  background: url(~@/assets/csts/xztk.png) no-repeat;
  background-size: 100% 100%;
  box-shadow: 0px 0px 22px 0px rgba(11,54,138,0.35), 0px 2px 8px 0px rgba(0,0,0,0.7), inset 0px 0px 8px 0px rgba(17,146,214,0.35);
  border-radius: 11px;
  border: 1px solid;
  border-image: linear-gradient(360deg, rgba(57, 133, 255, 0.5), rgba(0, 205, 255, 0)) 1 1;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
  .title {
    background: url(~@/assets/shzl/map/title_bg2.png) no-repeat center / 100% 100%;
    background-size: 100% 100%;
    width: 914px;
    height: 74px;
    margin: 0 auto;
    line-height: 74px;
    text-align: center;
    position: relative;
    span {
      font-size: 40px;
      font-family: YouSheBiaoTiHei;
      color: #FFFFFF;
      line-height: 52px;
      background: linear-gradient(180deg, #FFFFFF 0%, #2AEBFF 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      display: inline-block;
      margin-top: 11px;
    }
  }
  .close_btn {
    position: absolute;
    top: 38px;
    right: 43px;
  }
  .content {
    width: 1841px;
    height: 765px;
    .contentTitle {
      font-size: 18px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #ffffff;
      line-height: 25px;
      text-align: left;
      margin-top: 15px;
      span {
        float: left;
        width: 6px;
        height: 20px;
        background: #02B2CD;
        border-radius: 2px;
        margin-top: 3px;
        margin-left: 10px;
        margin-right: 8px;
      }
    }
    .left {
      width: 533px;
      float: left;
      margin-left: 67px;
      padding-top: 43px;
      .box1 {
        .ggcxfw {
          width: 475px;
          height: 248px;
          background: url(~@/assets/csts/bg13.png) no-repeat;
          background-size: 100% 100%;
          margin: 13px 0 0 24px;
          position: relative;
          .fy_out {
            position: absolute;
            top: 8%;
            left: 28%;
            z-index: 99;
            transform: translate(-50%, -50%);
            animation: rotateS infinite 12s linear;
          }
          .fy_in {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
          .ggcxfwItem1 {
            width: 170px;
            height: 124px;
            float: left;
            .num {
              font-size: 22px;
              font-family: DINAlternate-Bold, DINAlternate;
              font-weight: bold;
              color: #FFFFFF;
              line-height: 30px;
              margin-top: 37px;
            }
            .name {
              font-size: 18px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
              margin-top: 8px;
            }
          }
          .ggcxfwItem2 {
            width: 170px;
            height: 124px;
            float: right;
            .num {
              font-size: 22px;
              font-family: DINAlternate-Bold, DINAlternate;
              font-weight: bold;
              color: #FFFFFF;
              line-height: 30px;
              margin-top: 37px;
            }
            .name {
              font-size: 18px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
              margin-top: 8px;
            }
          }
          .ggcxfwItem3 {
            width: 170px;
            height: 124px;
            float: left;
            .num {
              font-size: 22px;
              font-family: DINAlternate-Bold, DINAlternate;
              font-weight: bold;
              color: #FFFFFF;
              line-height: 30px;
              margin-top: 37px;
            }
            .name {
              font-size: 18px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
              margin-top: 8px;
            }
          }
          .ggcxfwItem4 {
            width: 170px;
            height: 124px;
            float: right;
            .num {
              font-size: 22px;
              font-family: DINAlternate-Bold, DINAlternate;
              font-weight: bold;
              color: #FFFFFF;
              line-height: 30px;
              margin-top: 37px;
            }
            .name {
              font-size: 18px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
              margin-top: 8px;
            }
          }
        }
      }
      .box2 {
        .box2Content1 {
          width: 533px;
          height: 61px;
          .box2Left {
            width: 243px;
            height: 44px;
            margin-right: 23px;
            margin-top: 17px;
            background: url(~@/assets/csts/jdjcBg.png) no-repeat;
            background-size: 100% 100%;
            float: left;
            .box2Name {
              float: left;
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 44px;
              margin-left: 28px;
            }
            .box2Num {
              float: right;
              font-size: 20px;
              font-family: DINAlternate-Bold, DINAlternate;
              font-weight: bold;
              color: #FFFFFF;
              line-height: 44px;
              margin-right: 21px;
            }
          }
          .box2Right {
            width: 243px;
            height: 44px;
            margin-right: 23px;
            margin-top: 17px;
            background: url(~@/assets/csts/jdjcBg.png) no-repeat;
            background-size: 100% 100%;
            float: left;
            .box2Name {
              float: left;
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 44px;
              margin-left: 28px;
            }
            .box2Num {
              float: right;
              font-size: 20px;
              font-family: DINAlternate-Bold, DINAlternate;
              font-weight: bold;
              color: #FFFFFF;
              line-height: 44px;
              margin-right: 19px;
            }
          }
        }
        .box2Content2 {
          width: 533px;
          height: 242px;
        }
      }
    }
    .middle {
      width: 533px;
      float: left;
      margin-left: 54px;
      padding-top: 43px;
      .box3 {
        .box3Content1 {
          width: 266px;
          height: 269px;
          float: left;
          .box3item1 {
            width: 223px;
            height: 86px;
            margin-top: 20px;
            margin-left: 18px;
            background: url(~@/assets/csts/whcyBg1.png) no-repeat;
            background-size: 100% 100%;
            .box3Img {
              float: left;
              margin: 9px 0 0 11px;
            }
            .box3Right {
              float: left;
              .box3Name {
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #ffffff;
                line-height: 20px;
                margin-top: 17px;
                img {
                  float: left;
                  margin-top: 6px;
                  margin-left: 14px;
                  margin-right: 4px;
                }
              }
              .box3Num {
                font-size: 20px;
                font-family: DINAlternate-Bold, DINAlternate;
                font-weight: bold;
                color: #FFFFFF;
                line-height: 24px;
                margin-top: 8px;
                span {
                  font-size: 12px;
                  font-family: PingFangSC-Regular, PingFang SC;
                  font-weight: 400;
                  color: #ffffff;
                  line-height: 17px;
                }
              }
            }
          }
          .box3item2 {
            width: 223px;
            height: 86px;
            margin-top: 9px;
            margin-left: 18px;
            background: url(~@/assets/csts/whcyBg1.png) no-repeat;
            background-size: 100% 100%;
            .box3Img {
              float: left;
              margin: 9px 0 0 11px;
            }
            .box3Right {
              float: left;
              .box3Name {
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #ffffff;
                line-height: 20px;
                margin-top: 17px;
                img {
                  float: left;
                  margin-top: 6px;
                  margin-left: 14px;
                  margin-right: 4px;
                }
              }
              .box3Num {
                font-size: 20px;
                font-family: DINAlternate-Bold, DINAlternate;
                font-weight: bold;
                color: #FFFFFF;
                line-height: 24px;
                margin-top: 8px;
                span {
                  font-size: 12px;
                  font-family: PingFangSC-Regular, PingFang SC;
                  font-weight: 400;
                  color: #ffffff;
                  line-height: 17px;
                }
              }
            }
          }
        }
        .box3Content2 {
          width: 266px;
          height: 269px;
          float: left;
          .box3Item {
            width: 217px;
            height: 162px;
            margin-top: 24px;
            margin-left: 17px;
            background: url(~@/assets/csts/whcyBg2.png) no-repeat;
            background-size: 100% 100%;
            padding-top: 11px;
            .box3Name {
              width: 161px;
              height: 25px;
              background: linear-gradient(270deg, rgba(9,91,161,0) 0%, #0D5DA2 51%, rgba(11,94,162,0) 100%);
              border: 1px solid;
              border-image: linear-gradient(270deg, rgba(14, 116, 199, 0), rgba(14, 116, 199, 1), rgba(14, 116, 199, 0)) 1 1;
              font-size: 18px;
              font-family: PangMenZhengDao;
              color: #EDFBFF;
              line-height: 21px;
              letter-spacing: 1px;
              text-shadow: 0px 1px 2px rgba(0,0,0,0.15);
              margin: 0 auto;
            }
            .box3Num {
              font-size: 32px;
              font-family: PangMenZhengDao;
              color: #FFFFFF;
              line-height: 37px;
              letter-spacing: 2px;
              text-shadow: 0px 0px 7px rgba(73,140,255,0.85);
              background: linear-gradient(180deg, #00FAFF 0%, #00B1FF 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              margin-top: 13px;
            }
            img {
              margin-top: 8px;
            }
          }
        }
      }
      .box4 {
        .box4Content1 {
          width: 533px;
          height: 57px;
          .box4Item {
            width: 266px;
            height: 57px;
            float: left;
            img {
              float: left;
              margin-top: 5px;
            }
            .box4Right {
              float: left;
              margin-left: 5px;
              .box4Num {
                text-align: left;
                font-size: 22px;
                font-family: DINAlternate-Bold, DINAlternate;
                font-weight: bold;
                color: #FFFFFF;
                line-height: 26px;
                text-shadow: 0px 0px 4px rgba(0,0,0,0.5);
                background: linear-gradient(180deg, #FFFFFF 0%, #00FFFA 67%, #04A1FF 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                margin-top: 8px;
                span {
                  font-size: 14px;
                }
              }
              .box4Name {
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #ffffff;
                line-height: 20px;
              }
            }
          }
        }
        .box4Content2 {
          width: 533px;
          height: 246px;
        }
      }
    }
    .right {
      width: 533px;
      float: left;
      margin-left: 70px;
      padding-top: 43px;
      .box5 {
        .box5Content1 {
          width: 533px;
          height: 53px;
          padding-top: 9px;
          .box5Item {
            width: 243px;
            height: 44px;
            background: url(~@/assets/csts/jdjcBg.png) no-repeat;
            background-size: 100% 100%;
            margin: 0 auto;
            .box5Name {
              float: left;
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 44px;
              margin-left: 28px;
            }
            .box5Num {
              float: right;
              font-size: 20px;
              font-family: DINAlternate-Bold, DINAlternate;
              font-weight: bold;
              color: #FFFFFF;
              line-height: 44px;
              margin-right: 29px;
            }
          }
        }
        .box5Content2 {
          width: 433px;
          height: 166px;
        }
      }
      .box6 {
      }
    }
  }
}
</style>