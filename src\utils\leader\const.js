/*
 * @Author: fanjialong <EMAIL>
 * @Date: 2023-04-15 10:29:09
 * @LastEditors: luyu
 * @LastEditTime: 2024-03-22 17:31:37
 * @FilePath: /sjcz_nj_dp/src/utils/leader/const.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const env = window['$env'] ? window['$env'] : process.env.NODE_ENV
// export const finaUrl =
//   env === 'development' ? 'http://**********:8090/sjcz' : `http://**********:8090/sjcz`
// // 摄像头点位地址
// export const cameraMarkerUrl =
//   env === 'development' ? 'http://**************:9008' : `http://*************:9008`
// // 摄像头详情地址
// export const cameraXqUrl =
//   env === 'development' ? 'http://**************:9090' : `http://**************:9090`
// // 无人机地址
// export const droneUrl =
//   env === 'development' ? 'https://www.uavyun.cn/prod-api' : `https://www.uavyun.cn/prod-api`
// export const location = '南京'
// // webSocket事件推送
// export const webSocketUrl = env === 'development' ? '**********:9008' : `**********:9008`

// 判断是172的地址
console.log('window.location.origin', window.location.origin, window.location.origin.includes('//172'))

export const finaUrl = window.location.origin.includes('//172') ? 'http://*************:9008' : `http://10.1.91.33:9008`
// 摄像头点位地址
export const cameraMarkerUrl = window.location.origin.includes('//172')
	? 'http://*************:9008'
	: // : `http://**************:9008`
	  `http://*************:9008`
// 摄像头详情地址
export const cameraXqUrl = window.location.origin.includes('//172') ? 'http://**************:9090' : `http://**************:9090`
// 无人机地址
export const droneUrl = env === 'development' ? 'https://www.uavyun.cn/prod-api' : `https://www.uavyun.cn/prod-api`
export const location = '南京'
// webSocket事件推送
export const webSocketUrl = window.location.origin.includes('//172') ? '*************:9008' : `10.1.189.67:9008`
// tymhIndex 统一门户跳转地址
export const tymhUrl = window.location.origin.includes('//172')
	? 'http://*************:8090/ump/index'
	: `http://**********:8090/ump/index`
// 融合通信地址
export const rhtxUrl = window.location.origin.includes('//172') ? 'http://172.17.60.179:8099' : `https://58.213.148.112:8000`

// 系统运营地址
export const xtyyUrl = window.location.origin.includes('//172')
	? 'http://*************:8090/xtyy'
	: `http://*************:18981/xtyyhs`

// 实时监测地址
export const ssjcUrl = window.location.origin.includes('//172') ? `http://*************:9005` : 'http://*************:18981/jcyjhs'

// export const jcyjUrl = window.location.origin.includes('//172')
//   ? 'http://*************:9005'
//   : 'http://*************:18981/jcyjhs'

// export const baseUrl = window.location.origin.includes('//36') ? 'http://**************:8023/iip-gateway/siiri-daas-service' : 'http://njzhddqd.devops.com/sjczcq'
// export const baseUrl = 'https://agrimachcloud.com/iip-gateway/siiri-daas-service'
export const baseUrl = 'https://dpzs.camtc.cn/iip-gateway/siiri-daas-service' //正式环境
// export const baseUrl = 'http://*************/iip-gateway/siiri-daas-service' //互联测试环境
// export const baseUrl = 'http://***************:9019/iip-gateway/siiri-daas-service' //互联测试环境 新地址

// export const baseUrls = 'https://agrimachcloud.com/api-integration-service'
export const baseUrls = 'https://dpzs.camtc.cn/iip-gateway/api-integration-service' //正式环境
// export const baseUrls = 'http://*************/iip-gateway/api-integration-service' //互联测试环境
// export const baseUrls = 'http://***************:9019/iip-gateway/api-integration-service' //互联测试环境 新地址

// export const baseUrl2 = 'https://agrimachcloud.com/iip-gateway'
export const baseUrl2 = 'https://dpzs.camtc.cn/iip-gateway' //正式环境
// export const baseUrl2 = 'http://*************/iip-gateway' //互联测试环境
// export const baseUrl2 = 'http://***************:9019/iip-gateway' //互联测试环境 新地址

// export const baseUrl21 = 'https://dpzs.camtc.cn/iip-gateway/siiri-daas-service' //生产调用
export const baseUrl21 = 'https://agrimachcloud.com/iip-gateway/siiri-daas-service' //本地调用
// export const baseUrl21 = 'http://*************/iip-gateway/siiri-daas-service' //互联测试环境
// export const baseUrl21 = 'http://***************:9019/iip-gateway/siiri-daas-service' //互联测试环境 新地址

export const devUrl = ''
export const baseTokenUrl = 'https://agrimachcloud.com/idaas-service' //生产调用
// export const baseTokenUrl = 'http://*************:9019' //本地调用

// export const bjnjUrl = 'http://njzhddqd.devops.com/sjczcq'
// export const bjnjUrl = 'http://***********:9019'
export const bjnjUrl = `https://dpzs.camtc.cn/sjczcq` //正式环境
// export const bjnjUrl = `http://*************/sjczcq` //互联测试环境
// export const bjnjUrl = `http://***************:9019/sjczcq` //互联测试环境 新地址


/**
 * 在线沟通相关Url
 */
export const messageGroupTokenUrl = `http://***************:9019/convert-service` //正式环境
// export const messageGroupTokenUrl = `http://***************:9019/convert-service` //互联测试环境
// export const messageGroupTokenUrl = `http://***************:9019/convert-service` //互联测试环境 新地址

export const messageGroupUrl = `http://************:8811/ima-business` //正式环境
// export const messageGroupUrl = `http://************:8811/ima-business` //互联测试环境
// export const messageGroupUrl = `http://************:8811/ima-business` //互联测试环境 新地址

export const appServiceInitAddressUrl = 'http://***************:8811' //正式环境(地址未修改)
// export const appServiceInitAddressUrl = 'http://************:8811' //互联测试环境
// export const appServiceInitAddressUrl = 'http://***************:8811' //互联测试环境 新地址


// export const bjnjUrl = 'https://zhdd.camtc.cn/sjczcq'

export const zbxtUrl = 'http://**************:8089'
