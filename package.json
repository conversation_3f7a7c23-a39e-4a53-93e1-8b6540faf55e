{"name": "syzl-ui", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "inspect": "vue-cli-service inspect"}, "dependencies": {"@liveqing/liveplayer": "^2.5.3", "@turf/helpers": "^7.2.0", "@turf/turf": "^7.2.0", "aegis-web-sdk": "^1.35.26", "axios": "^0.21.1", "core-js": "^3.6.5", "crypto-js": "^4.1.1", "dayjs": "^1.10.7", "echarts": "^4.8.0", "echarts-gl": "^1.1.2", "echarts-liquidfill": "^2.0.6", "element-resize-detector": "^1.2.1", "element-ui": "^2.14.1", "esri-leaflet": "^3.0.0", "ezuikit-js": "^8.1.10", "file-saver": "^2.0.5", "gcoord": "^1.0.5", "geotiff": "^2.1.3", "get-blob-duration": "^1.2.0", "grib2json": "^1.0.2", "heatmap.js": "^2.0.5", "highcharts": "^10.2.1", "iview": "^3.5.4", "jquery": "^3.5.1", "js-export-excel": "^1.1.4", "js-md5": "^0.8.3", "jsencrypt": "^3.3.1", "leaflet": "^1.9.4", "leaflet-canvas-marker-xrr2021": "^1.0.2", "leaflet-providers": "^1.13.0", "leaflet.markercluster": "^1.4.1", "less": "^3.8.1", "less-loader": "^4.1.0", "lodash": "^4.17.20", "maptalks": "^0.49.1", "maptalks.three": "^0.12.4", "maptalks.wmts": "^1.0.3", "mitt": "^3.0.1", "plotty": "^0.4.9", "proj4": "^2.19.7", "proj4leaflet": "^1.0.2", "recorder-core": "^1.2.22080700", "shzl-datav": "^0.1.52", "swiper": "^5.4.1", "three": "^0.104.0", "trtc-sdk-v5": "^5.10.1", "v-click-outside": "^3.1.2", "video.js": "^7.10.2", "vue": "^2.6.11", "vue-amap": "^0.5.10", "vue-awesome-swiper": "^4.1.1", "vue-bus": "^1.2.1", "vue-create-api": "^0.2.3", "vue-drag-resize": "^1.5.4", "vue-marquee-text-component": "^1.2.0", "vue-pdf": "^4.3.0", "vue-router": "^3.2.0", "vue-video-player": "^5.0.2", "vue2-leaflet": "^2.7.1", "vuescroll": "^4.17.5", "vuex": "^3.4.0", "xlsx": "^0.18.5", "xlsx-style": "^0.8.13"}, "devDependencies": {"@types/leaflet": "^1.9.3", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "^10.1.0", "copy-webpack-plugin": "4.6.0", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^6.2.2", "prettier": "^1.19.1", "sass": "^1.26.5", "sass-loader": "^8.0.2", "shzl-datav": "^0.1.52", "vue-template-compiler": "^2.6.11", "webpack": "^4.42.0"}}