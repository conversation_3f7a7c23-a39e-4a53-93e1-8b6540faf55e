<!-- 站内资源 -->
<template>
	<div v-if="show">
		<div class="ai_waring">
			<div class="fh_btn" @click="fhEmitai">
				<img src="@/assets/bjnj/fh.png" alt="" />
			</div>
			<div class="title">
				<span>{{ title }}</span>
			</div>
			<div class="close_btn" @click="closeEmitai"></div>
			<div class="content">
				<div class="tableBox">
					<div
						class="tableItem"
						:class="tableShow == index ? 'tableActive' : ''"
						v-for="(item, index) of btnList"
						:key="index"
						@click="tableswitch(index)"
					>
						<span>{{ item.name }}</span>
					</div>
				</div>
				<div v-if="tableShow == 0">
					<div class="check_types">
						<div class="jjcd">
							<span>姓名：</span>
							<el-input v-model="gdObj.name" placeholder="请输入成员姓名"></el-input>
						</div>
						<!-- <div class="jjcd">
							<span>联系方式：</span>
							<el-input v-model="gdObj.phone" placeholder="请输入联系方式"></el-input>
						</div> -->
						<div class="jjcd">
							<span>所属区域：</span>
							<el-cascader
								v-model="gdObj.areaId"
								:options="options"
								:props="{ value: 'areaId', label: 'areaName', children: 'sonArea', checkStrictly: true, emitPath: false }"
								@change="handleChange"
							>
							</el-cascader>
						</div>

						<div class="jjcd">
							<span>职务：</span>
							<el-input v-model="gdObj.duty" placeholder="请输入职务名称"></el-input>
						</div>

						<div class="type_btns">
							<div @click="searchBtn">查询</div>
							<div @click="resetBtn">重置</div>
						</div>
					</div>
					<div class="table_box">
						<SwiperTableMap
							:titles="['序号', '成员名称', '联系方式', '职务']"
							:widths="['10%', '26%', '30%', '34%']"
							:data="tableList1"
							:contentHeight="'520px'"
							:settled="false"
							:isNeedOperate="false"
							@operate="operate"
						></SwiperTableMap>
					</div>
				</div>
				<div v-if="tableShow == 1">
					<div class="check_types">
						<div class="jjcd">
							<span>农机编号：</span>
							<el-input v-model="gdObj.agmachId" placeholder="请输入农机编号"></el-input>
						</div>
						<!-- <div class="jjcd">
              <span>车牌号：</span>
              <el-input v-model="gdObj.licensePlateCode" placeholder="请输入车牌号"></el-input>
            </div> -->
						<div class="jjcd">
							<!-- <span>农机品目：</span>
							<Select v-model="gdObj.sqlxValue">
								<Option v-for="item in qgdCodesqlxOptions" :value="item.dicValue" :key="item.dicValue">
									{{ item.dicLabel }}
								</Option>
							</Select> -->
							<span>农机品目：</span>
							<el-select v-model="gdObj.agmachTypeId" placeholder="请选择作业类型">
								<el-option v-for="item in agmachTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
							</el-select>
						</div>
						<div class="type_btns">
							<div @click="searchBtn">查询</div>
							<div @click="resetBtn">重置</div>
						</div>
					</div>
					<div class="table_box">
						<!-- <SwiperTableMap
              :titles="[
                '序号',
                '农机品目',
                '型号',
                '车牌号',
                '联系人',
                '农机位置',
              ]"
              :widths="['6%', '20%', '20%', '22%','22%', '14%']"
              :data="tableList2"
              :contentHeight="'520px'"
              :settled="settled"
              @operate="operate"
            ></SwiperTableMap> -->
						<SwiperTableMap
							:titles="['序号', '农机品目', '型号', '车牌号', '联系人']"
							:widths="['6%', '24%', '24%', '25%', '25%']"
							:data="tableList2"
							:contentHeight="'520px'"
							:isNeedOperate="false"
							@operate="operate"
						></SwiperTableMap>
					</div>
				</div>
				<!-- <div v-if="tableShow == 2">
          <div class="check_types">
            <div class="jjcd">
              <span>站点名称：</span>
              <el-input v-model="gdObj.jjcdValue" placeholder="请输入站点名称"></el-input>
            </div>
            <div class="jjcd">
              <span>所属区域：</span>
              <Select v-model="gdObj.sqlxValue">
                <Option
                  v-for="item in qgdCodesqlxOptions"
                  :value="item.dicValue"
                  :key="item.dicValue"
                >
                  {{
                  item.dicLabel
                  }}
                </Option>
              </Select>
            </div>
            <div class="type_btns">
              <div @click="searchBtn">查询</div>
              <div @click="resetBtn">重置</div>
            </div>
          </div>
          <div class="table_box">
            <SwiperTableMap
              :titles="[
                '序号',
                '农机编号',
                '农机品目',
                '型号',
                '车牌号',
                '状态',
              ]"
              :widths="['6%', '16%', '12%', '16%', '24%', '26%']"
              :data="tableList3"
              :contentHeight="'520px'"
              :settled="settled"
              :isNeedOperate="false"
              @operate="operate"
            ></SwiperTableMap>
          </div>
          <div class="fy_page">
            <Page :total="total" @on-change="pageNumChange" show-total></Page>
          </div>
        </div>
        <div v-if="tableShow == 3">
          <div class="check_types">
            <div class="jjcd">
              <span>物资名称：</span>
              <el-input v-model="gdObj.jjcdValue" placeholder="请输入物资名称"></el-input>
            </div>
            <div class="jjcd">
              <span>所属区域：</span>
              <Select v-model="gdObj.sqlxValue">
                <Option
                  v-for="item in qgdCodesqlxOptions"
                  :value="item.dicValue"
                  :key="item.dicValue"
                >
                  {{
                  item.dicLabel
                  }}
                </Option>
              </Select>
            </div>
            <div class="type_btns">
              <div @click="searchBtn">查询</div>
              <div @click="resetBtn">重置</div>
            </div>
          </div>
          <div class="table_box">
            <SwiperTableMap
              :titles="[
                '序号',
                '物资名称',
                '物资类型',
                '物资数量',
                '计量单位',
                '所属单位',
                '现状态',
                '物资照片',
              ]"
              :widths="['10%', '15%', '10%', '10%', '10%', '15%', '15%', '15%']"
              :data="tableList4"
              :contentHeight="'480px'"
              :settled="settled"
              @operate="operate"
            ></SwiperTableMap>
          </div>
          <div class="fy_page">
            <Page :total="total" @on-change="pageNumChange" show-total></Page>
          </div>
        </div>-->
			</div>
			<div class="fy_page">
				<Page :total="total" :page-size="pageSize" v-model="pageNum" @on-change="pageNumChange" show-total></Page>
			</div>
		</div>
	</div>
</template>

<script>
import SwiperTableMap from './table/RealTimeEventTable8.vue'
// api
import { getEventType, getUnDoneEventList, getDoneEventList, getQgdSssj } from '@/api/hs/hs.api.js'
import { myMixins } from '@/libs/myMixins.js'
import { queryPageByTeamId, queryPageById } from '@/api/njzl/hs.api.js'
import { queryPageTEmergencyCenterByCondition, areaTree, queryPageTDryingCenterByCondition } from '@/api/bjnj/zhdd.js'
import { getCscpBasicHxItemCode } from '@/api/bjnj/zhdd.js'
export default {
	name: 'znzyPop',
	mixins: [myMixins],
	components: { SwiperTableMap },
	props: {
		value: {
			type: Boolean,
			default: false,
		},
		list: {
			type: Array,
			default: () => {
				return []
			},
		},
		basicInfomationId: {
			type: String,
			default: '',
		},
		userAreaId: {
			type: String,
			default: '',
		},
	},
	data() {
		return {
			title: '基本情况',
			options: [],
			btnList: [
				{
					name: '人员信息',
				},
				{
					name: '机具信息',
				},
			],
			tableShow: 0,
			tabs: ['未办结', '办结'],
			btnsArr: [
				{
					name: '未办结',
					normalBg: require('@/assets/shzl/map/left_n.png'),
					activeBg: require('@/assets/shzl/map/left_a.png'),
				},
				{
					name: '已办结',
					normalBg: require('@/assets/shzl/map/right_n.png'),
					activeBg: require('@/assets/shzl/map/right_a.png'),
				},
			],
			urgentOptions: [
				{
					value: '0',
					label: '普通',
				},
				{
					value: '1',
					label: '突发',
				},
				{
					value: '2',
					label: '重要',
				},
				{
					value: '3',
					label: '紧急',
				},
			],
			eventTypeOptions: [
				{
					value: '0',
					label: '普通',
				},
			],
			timeOutOptions: [
				{
					value: '0',
					label: '是',
				},
				{
					value: '1',
					label: '否',
				},
			],
			proityValue: '',
			timeOutValue: '',
			settled: true,
			total: 0,
			pageNum: 1,
			pageSize: 8,
			finished: 1,
			tableList1: [],
			tableList2: [],
			tableList3: [],
			tableList4: [],
			gdObj: {},
			agmachTypeOptions: [],
		}
	},
	watch: {
		// qgdCodejjcdOptions (val) {
		//   if (this.qgdCodejjcdOptions.length > 0) {
		//     // this.getQgdSssj()
		//   }
		// },
		// 防止出现获取不到annexNum
		userAreaId: {
			immediate: true,
			handler(annexNum) {
				// console.log('annexNum', annexNum)
				this.userAreaId = annexNum + ''
				this.areaTree()
				// if (annexNum) {
				//   this.queryAnnexByAnnexNum();
				// }
			},
		},
		basicInfomationId() {
			this.getData()
		},
		value: {
			handler(nv, ov) {
				if (nv) {
					if (this.tableShow == 0) {
						this.queryPageByTeamId()
					}
				}
			},
		},
	},
	computed: {
		show() {
			return this.value
		},
	},
	mounted() {
		// this.areaTree()
		// this.queryPageTEmergencyCenterByCondition({
		//   area: this.gdObj.area,
		//   current: this.pageNum,
		//   name: this.gdObj.name,
		//   size: this.pageSize,
		//   type: this.tableShow + 1,
		// })
		this.tableswitch(0)
	},
	methods: {
		async getCscpBasicHxItemCode1(data) {
			let res = await getCscpBasicHxItemCode(data)
			if (res?.code == '0') {
				if (data == 'agmachTypeId') {
					this.agmachTypeOptions = res.data.map((item) => {
						return {
							value: item.itemCode,
							label: item.itemValue,
						}
					})
				}
			}
		},

		async queryPageByTeamId() {
			let params = {
				name: this.gdObj.name,
				phone: this.gdObj.phone,
				size: this.pageSize,
				current: this.pageNum,
				teamId: this.basicInfomationId,
				areaId: this.gdObj.areaId,
				duty: this.gdObj.duty,
			}
			let res = await queryPageByTeamId(params)
			this.tableList1 = []
			this.tableList1 = [].concat(
				res.data.records.map((it, index) => [this.pageSize * (this.pageNum - 1) + index + 1, it.name, it.phone, it.duty]),
			)
			this.total = res.data.total
		},
		async queryPageById() {
			let params = {
				size: this.pageSize,
				current: this.pageNum,
				agmachId: this.gdObj.agmachId,
				licensePlateCode: this.gdObj.licensePlateCode,
				teamId: this.basicInfomationId,
				agmachTypeId: this.gdObj.agmachTypeId,
			}
			let res = await queryPageById(params)
			// if (this.tableShow == 0) {
			this.tableList2 = []
			this.tableList2 = [].concat(
				res.data.records.map((it, index) => [
					this.pageSize * (this.pageNum - 1) + index + 1,
					it.agmachType,
					it.mdlName,
					it.licensePlateCode,
					it.contact + '-' + it.phone,
				]),
			)
			this.total = res.data.total
			// } else if (this.tableShow == 1) {
			//   this.tableList2 = []
			//   this.tableList2 = [].concat(
			//     res.data.records.map((it, index) => [this.pageSize * (this.pageNum - 1) + index + 1, it.name, it.contact, it.phone, it.address, it.service])
			//   )
			// }
		},
		async areaTree(data) {
			// let res = await areaTree(data)
			// console.log(res)
			// this.options = res

			let res = await areaTree(data)
			const fullAreaTree = [res]

			// 根据用户区域信息，在区域树中查找子树
			const getUserAreaTree = (areaList) => {
				if (!areaList) return []
				const userArea = areaList.find((area) => area.areaId === this.userAreaId)
				if (userArea) return [userArea]
				return areaList.flatMap((area) => getUserAreaTree(area.sonArea))
			}
			const userAreaTree = getUserAreaTree(fullAreaTree)

			// 处理区域树数据
			const parseAreaNode = (area) => {
				area.areaName = area.areaName.trim()
				if (area.sonArea) {
					area.sonArea.forEach(parseAreaNode)
				}
			}
			userAreaTree.forEach(parseAreaNode)
			this.options = userAreaTree
		},
		async queryPageTEmergencyCenterByCondition(data) {
			let res = await queryPageTEmergencyCenterByCondition(data)
			if (this.tableShow == 0) {
				this.tableList1 = []
				this.tableList1 = [].concat(
					res.data.records.map((it, index) => [
						this.pageSize * (this.pageNum - 1) + index + 1,
						it.name,
						it.contact,
						it.phone,
						it.address,
						it.service,
					]),
				)
			} else if (this.tableShow == 1) {
				this.tableList2 = []
				this.tableList2 = [].concat(
					res.data.records.map((it, index) => [
						this.pageSize * (this.pageNum - 1) + index + 1,
						it.name,
						it.contact,
						it.phone,
						it.address,
						it.service,
					]),
				)
			}
		},
		async queryPageTDryingCenterByCondition(data) {
			let res = await queryPageTDryingCenterByCondition(data)
			console.log(res)
			this.tableList3 = []
			this.tableList3 = [].concat(
				res.data.records.map((it, index) => [
					this.pageSize * (this.pageNum - 1) + index + 1,
					it.name,
					it.type,
					it.talent,
					it.contact,
					it.phone,
					it.address,
					it.status,
				]),
			)
		},
		handleChange(value) {
			console.log(value)
		},
		tableswitch(index) {
			this.pageNum = 1
			this.total = 0
			this.tableShow = index
			if (this.tableShow == 0) {
				this.areaTree()
			} else if (this.tableShow == 1) {
				this.getCscpBasicHxItemCode1('agmachTypeId')
			}
			this.getData()
		},
		getData() {
			if (this.tableShow == 0) {
				this.queryPageByTeamId()
			} else {
				this.queryPageById()
			}
		},
		operate(i, id, it) {
			this.$emit('operate', i, id, it)
		},
		fhEmitai() {
			this.$emit('fhEmitai', false)
		},
		closeEmitai() {
			this.$emit('closeEmitai', false)
		},
		pageNumChange(val) {
			this.pageNum = val
			this.getData()
		},
		searchBtn() {
			this.getData()
		},
		resetBtn() {
			this.pageNum = 1
			this.gdObj = {}
			this.getData()
		},
	},
}
</script>

<style lang="less" scoped>
.bg-header {
	position: absolute;
	width: 100%;
	height: 1080px;
	background: rgba(2, 14, 50, 0.75);
	z-index: 999999;
	top: 0;
	left: 0;
}
/deep/ .ivu-select {
	width: 189px;
}
/deep/ .ivu-select-selection {
	width: 189px;
	height: 34px;
	background: rgba(0, 74, 143, 0.4) linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
	border: 1px solid rgba(0, 162, 255, 0.6);
}
/deep/ .ivu-select-selection .ivu-select-selected-value {
	font-size: 13px;
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	color: #ccf4ff;
	line-height: 34px;
}

/deep/ .ivu-input {
	background: rgba(0, 74, 143, 0.4) linear-gradient(360deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%);
	border: 1px solid rgba(0, 162, 255, 0.6);
	font-size: 16px;
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	color: #ccf4ff;
	line-height: 22px;
}
/deep/ .ivu-select-placeholder {
	height: 34px !important;
	font-size: 13px !important;
	line-height: 34px !important;
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	color: #ccf4ff !important;
}
/deep/ .ivu-page-item {
	background: transparent;
	color: #fff;
}
/deep/ .ivu-page-item a {
	color: #fff;
}
/deep/ .ivu-page-prev,
/deep/ .ivu-page-next {
	background: transparent;
}
/deep/ .el-input__inner {
	color: #ccf4ff;
}
.ai_waring {
	width: 1525px;
	height: 850px;
	background: rgba(9, 19, 34, 0.95);
	box-shadow: 0px 0px 22px 0px rgba(11, 54, 138, 0.35), 0px 2px 8px 0px rgba(0, 0, 0, 0.7),
		inset 0px 0px 8px 0px rgba(17, 146, 214, 0.35);
	border-radius: 11px;
	border: 1px solid #015c8c;
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	z-index: 1200;
	.title {
		margin: 0 auto 36px;
		width: 100%;
		height: 72px;
		line-height: 72px;
		background: url(~@/assets/map/dialog/title7.png) no-repeat center / 100% 100%;
		display: grid;
		place-items: center;
		span {
			display: inline-block;
			font-size: 36px;
			font-family: PingFangSC, PingFang SC;
			background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
			background-clip: text;
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}
	}
	.fh_btn {
		width: 39px;
		height: 39px;
		position: absolute;
		top: 24px;
		left: 27px;
		img {
			width: 39px;
			height: 39px;
		}
	}
	.close_btn {
		position: absolute;
		width: 23px;
		height: 23px;
		top: 24px;
		right: 27px;
		background: url(~@/assets/map/dialog/btn3.png) no-repeat center / 100% 100%;
		cursor: pointer;
	}
	.content {
		padding: 0 30px;
		.btns {
			// border: 1px solid red;
			margin: 0 auto;
			display: flex;
			justify-content: center;
			& > div {
				width: 154px;
				height: 53px;
				span {
					font-size: 24px;
					font-family: PingFangSC, PingFang SC;
					color: #ffffff;
					line-height: 53px;
					background: linear-gradient(180deg, #ffffff 0%, #ffeba0 100%);
					background-clip: text;
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
				}
			}
		}
		.check_types {
			text-align: left;
			display: flex;
			.jjcd {
				display: flex;
				align-items: center;
				margin-right: 24px;
				span {
					font-size: 14px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #ffffff;
				}
				/deep/ .el-input {
					width: 189px;
					height: 34px;
					.el-input__inner {
						width: 189px;
						height: 34px;
						background: linear-gradient(180deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%),
							rgba(0, 74, 143, 0.4);
						border: 1px solid rgba(0, 162, 255, 0.6);
					}
				}
			}
			.report_time {
				margin-left: 26px;
				display: flex;
				align-items: center;
				span {
					font-size: 16px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					color: #ffffff;
					line-height: 22px;
				}
			}
			.istime_out {
				margin-left: 26px;
				align-items: center;
				display: flex;
				span {
					font-size: 16px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					color: #ffffff;
					line-height: 22px;
				}
			}
			.type_btns {
				margin-left: 27px;
				display: flex;
				align-items: center;
				& div {
					font-size: 16px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					color: #ffffff;
					line-height: 43px;
					background: url(~@/assets/shzl/map/type_btn.png) no-repeat center / 100% 100%;
					background-size: 100% 100%;
					text-align: center;
					cursor: pointer;
					&:first-of-type {
						width: 98px;
						height: 43px;
					}
					&:nth-of-type(2) {
						margin-left: 20px;
						width: 98px;
						height: 43px;
					}
					&:nth-of-type(3) {
						margin-left: 20px;
						width: 98px;
						height: 43px;
					}
				}
			}
		}
		.table_box {
			margin-top: 12px;
			overflow: hidden;
		}
		.fy_page {
			margin-top: 8px;
			/deep/ .ivu-page-total {
				margin-top: 0;
			}
		}
		.tableBox {
			height: 41px;
			margin-bottom: 21px;
			.tableItem {
				float: left;
				width: 152px;
				height: 41px;
				background: url(~@/assets/map/dialog/btn5.png) no-repeat center / 100% 100%;
				margin-right: 10px;
				font-family: PingFangSC, PingFang SC;
				font-size: 22px;
				color: #ffffff;
				line-height: 41px;
				text-align: center;
				font-style: normal;
				text-transform: none;
				span {
					background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
					background-clip: text;
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
				}
			}
			.tableActive {
				background: url(~@/assets/map/dialog/btn4.png) no-repeat center / 100% 100%;
				span {
					font-size: 22px;
					color: #ffffff;
					font-weight: 600;
					background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
					background-clip: text;
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
				}
			}
		}
	}
}
// /deep/ .ivu-page-total {
//   font-size: 18px;
//   color: #fff;
// }
</style>
