<template>
  <footer>
    <ul>
      <li
        v-for="(it, i) of btns"
        :key="i"
        :style="{
          background:
            activeList.findIndex(item => item === i) !== -1
              ? `url(${require('@/assets/bjnj/btn2.png')}) no-repeat center`
              : `url(${require('@/assets/bjnj/btn1.png')}) no-repeat center`
        }"
        :class="{ active: activeList.findIndex(item => item === i) !== -1 }"
      >
        <img :src="activeList.findIndex(item => item === i) !== -1 ? it.activeBg : it.normalBg" alt />
        <span @click="handleClick(i)">{{ it.name }}</span>
        <div class="popups" v-show="false"></div>
      </li>
    </ul>
    <!-- <div class="footerImg"></div> -->
  </footer>
</template>

<script>
export default {
  name: 'LeaderFooter',
  props: {
    btns: {
      type: Array,
      default () {
        return [
          {
            normalBg: require('@/assets/csts/n_btn11.png'),
            activeBg: require('@/assets/csts/a_btn11.png'),
            name: '机构组织',
            children: [{}]
          },
          {
            normalBg: require('@/assets/csts/n_btn12.png'),
            activeBg: require('@/assets/csts/a_btn12.png'),
            name: '人员力量'
          },
          {
            normalBg: require('@/assets/csts/n_btn13.png'),
            activeBg: require('@/assets/csts/a_btn13.png'),
            name: '视频监控'
          },
          {
            normalBg: require('@/assets/csts/n_btn14.png'),
            activeBg: require('@/assets/csts/a_btn14.png'),
            name: '城市部件'
          },
          {
            normalBg: require('@/assets/csts/n_btn15.png'),
            activeBg: require('@/assets/csts/a_btn15.png'),
            name: '城市事件'
          },
          {
            normalBg: require('@/assets/csts/n_btn16.png'),
            activeBg: require('@/assets/csts/a_btn16.png'),
            name: '重点场所'
          }
        ]
      }
    }
  },
  data () {
    return {
      activaIdx: 0,
      activeList: [],
    }
  },
  methods: {
    handleClick (i) {
      const markIndex = this.activeList.findIndex(item => item === i)
      console.log(markIndex)
      if (markIndex == -1) {
        this.activeList.push(i)
      } else {
        this.activeList.splice(markIndex, 1)
      }
      console.log(this.activeList)
      this.$emit('mark', i , this.activeList)
      // if (i === this.activaIdx) {
      //   this.activaIdx = -1
      //   this.$emit('mark', -1)
      // } else {
      //   this.$emit('mark', i)
      //   this.activaIdx = i
      // }
    }
  }
}
</script>

<style lang="less" scoped>
footer {
  display: grid;
  place-items: center;
  width: 1920px;
  height: 107px;
  position: absolute;
  left: 0;
  bottom: 0;
  background: url(~@/assets/img/footer_zz.png) no-repeat center / cover;
  z-index: 1002;
  ul {
    display: flex;
    gap: 29px;
    li {
      position: relative;
      width: 157px;
      height: 46px;
      line-height: 46px;
      margin-top: -16px;
      span {
        padding-left: 7px;
        font-family: PingFangSC, PingFang SC;
        font-size: 20px;
        font-weight: bold;
        color: #ffffff;
        line-height: 26px;
        letter-spacing: 2px;
        font-style: normal;
        background: linear-gradient(180deg, #ffffff 0%, #009fff 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        cursor: pointer;
      }
      img {
        vertical-align: middle;
        margin-top: -6px;
      }
      &.active {
        span {
          color: #edfffd;
          background: linear-gradient(180deg, #fdfeff 0%, #cbedff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        // &::after {
        //   content: '';
        //   position: absolute;
        //   width: 66px;
        //   height: 20px;
        //   left: 50%;
        //   transform: translateX(-50%);
        //   bottom: -15px;
        //   background: url(~@/assets/csts/btn_light2.png) no-repeat;
        // }
      }
      .popups {
        position: absolute;
        left: 50%;
        top: -264px;
        transform: translateX(-50%);
        width: 156px;
        height: 243px;
        background: url(~@/assets/csts/popups.png) no-repeat;
        transition: all 2s;
      }
    }
  }
  .footerImg {
    width: 1920px;
    height: 24px;
    background: url(~@/assets/img/footer_bg2.png) no-repeat center / cover;
    position: absolute;
    bottom: 0;
  }
}
</style>
