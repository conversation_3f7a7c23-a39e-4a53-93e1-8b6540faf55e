<template>
  <video
    class="video-js vjs-default-skin vjs-big-play-centered vjs-fluid vjs-16-9"
    controls
    :playsinline="true"
    preload="auto"
  ></video>
</template>

<script>
import Video from "video.js";
import "video.js/dist/video-js.css";

export default {
  name: "HlsCamera",
  props: {
    src: {
      type: String,
      default: "",
    },
  },
  beforeDestroy() {
    this.player && this.player.stop();
    this.player && this.player.dispose();
  },
  mounted() {
    this.$nextTick(() => {
      this.initVideo();
    });
  },
  methods: {
    initVideo() {
      //初始化视频方法
      this.player = Video(this.$el, {
        //确定播放器是否具有用户可以与之交互的控件。没有控件，启动视频播放的唯一方法是使用autoplay属性或通过Player API。
        controls: true,
        //自动播放属性,muted:静音播放
        autoplay: "muted",
        //建议浏览器是否应在<video>加载元素后立即开始下载视频数据。
        preload: "auto",
      });
      this.playFromSrc(this.src);
      this.player.duration();
    },
    // 播放
    playFromSrc(src) {
      this.player.src({
        src,
        type: "application/x-mpegURL",
      });
    },
  },
  watch: {
    src(val) {
      this.playFromSrc(val);
    },
  },
};
</script>

<style lang="scss" scoped>
video {
  width: 100%;
  height: 100%;
  object-fit: fill;
}
</style>
