<template>
  <div class="event" v-if="show">
    <div class="close" @click="closeFn"></div>
    <div class="title">
      <span>演练完成</span>
    </div>
    <div class="content">
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item label="是否合格">
          <el-radio-group v-model="form.isgood">
            <el-radio label="1">合格</el-radio>
            <el-radio label="0">不合格</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="评价内容">
          <el-input type="textarea" v-model="form.evaluate"></el-input>
        </el-form-item>
        <el-form-item label="附件" prop="annexnum">
          <el-input v-model="form.annexnum" style="display: none"></el-input>
          <imgUpload5 @getAnnexNum="getAnnexNum" :annexNum.sync="form.annexnum"></imgUpload5>
        </el-form-item>
      </el-form>
    </div>
    <div class="btns">
      <div class="btn" @click="handle(0)">取消</div>
      <div class="btn" @click="handle(1)">确定</div>
    </div>
  </div>
</template>

<script>
import imgUpload5 from "@/components/bjnj/imgUpload5.vue";
export default {
  name: 'EventDialog',
  components: {
    imgUpload5
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    list: {
      type: Object,
      default: () => {
        return {
          title: '救灾请求',
          type: '暴雨',
          time: '小麦',
          address: '100亩',
          describe: '江苏省南京市浦口区',
          describe2: '2024-02-20',
          describe3: '联合收割机10台',
          describe4: '待处置',
        }
      }
    },
    dwType: {
      type: String,
      default: ''
    },
    btns:{
      type:Array,
      default:()=>[
        '查看详情'
      ]
    }
  },
  data() {
    return {
      form: {
        isgood: '',
        evaluate: '',
        annexnum: '',
      }
    }
  },
  computed: {
    show() {
      return this.value
    }
  },
  methods: {
    getAnnexNum(annexNum) {
      this.form.annexnum = annexNum
    },
    closeFn() {
      this.$emit('input', false)
    },
    handle(i) {
      this.$emit('handle', i, this.form)
    }
  }
}
</script>

<style lang="less" scoped>
.event {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  width: 813px;
  height: 522px;
  padding: 20px 0 0 0;
  margin: 0 auto;
  background: url('~@/assets/map/dialog/bg7.png') no-repeat center / 100% 100%;
  box-sizing: border-box;
  .close {
    position: absolute;
    top: 33px;
    right: 57px;
    width: 24px;
    height: 24px;
    background: url('~@/assets/map/dialog/btn2.png') no-repeat center / 100% 100%;
    box-sizing: border-box;
    cursor: pointer;
  }
  .title {
    width: 720px;
    height: 47px;
    line-height: 47px;
    padding-left: 37px;
    margin: 0 auto;
    text-align: left;
    background: url('~@/assets/map/dialog/title1.png') no-repeat center / 100% 100%;
    box-sizing: border-box;
    span {
      font-size: 18px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      background: linear-gradient(180deg, #ffffff 0%, #2aa6ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .content {
    width: 700px;
    margin: 0 auto;
    font-family: SourceHanSansCN, SourceHanSansCN;
    font-weight: 400;
    font-size: 14px;
    color: #FF1313;
    line-height: 21px;
    text-align: right;
    font-style: normal;
    text-transform: none;
    ::v-deep .el-radio-group {
      float: left;
      margin-top: 14px;
    }
    ::v-deep .el-textarea__inner {
      background: linear-gradient(180deg , rgba(0,162,255,0) 0%, rgba(0,162,255,0.08) 100%, rgba(0,162,255,0.3) 200%), rgba(0,74,143,0.4);
      border: 1px solid rgba(0,162,255,0.6);
    }
  }
  ul {
    display: flex;
    flex-direction: column;
    gap: 14px;
    margin-top: 20px;
    padding: 0 50px 0 48px;
    box-sizing: border-box;
    li {
      display: flex;
      justify-content: space-between;
      box-sizing: border-box;
      .label {
        // width: 70px;
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
        flex-shrink: 0;
      }
      .value {
        width: 225px;
        text-align: right;
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
 .btns {
    margin-top: 23px;
    display: flex;
    justify-content: center;
    gap: 10px;
    .btn {
      width: 150px;
      height: 33px;
      line-height: 33px;
      font-size: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #ffffff;
      background: url('~@/assets/map/dialog/btn1.png') no-repeat center / 100% 100%;
      cursor: pointer;
    }
  }
}
</style>
