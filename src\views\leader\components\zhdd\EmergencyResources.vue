<template>
  <ul class="wrap">
    <li v-for="(it, i) of list" :key="i">
      <div class="icon" :style="{ backgroundImage: `url(${it.icon})` }"></div>
      <div class="desc">
        <countTo
          ref="countTo"
          :startVal="$countTo.startVal"
          :decimals="$countTo.decimals(it.num)"
          :endVal="it.num"
          :duration="$countTo.duration"
        />
        <div class="tit">{{ it.tit }}</div>
      </div>
    </li>
  </ul>
</template>

<script>
export default {
  name: 'EmergencyResources',
  data() {
    return {
      list: [
        {
          num: 499,
          tit: '人员(人)',
          img: require('@/assets/csts/icon.png'),
          url: require('@/assets/zhdd/icon5.png'),
          icon: require('@/assets/zhdd/icon21.png'),
          name: '亿元'
        },
        {
          num: 34,
          tit: '物资(件)',
          img: require('@/assets/csts/icon.png'),
          url: require('@/assets/zhdd/icon6.png'),
          icon: require('@/assets/zhdd/icon22.png'),
          name: '亿元'
        },
        {
          num: 15,
          tit: '车辆(辆)',
          img: require('@/assets/csts/icon.png'),
          url: require('@/assets/zhdd/icon7.png'),
          icon: require('@/assets/zhdd/icon23.png'),
          name: '亿元'
        },
        {
          num: 10,
          tit: '医疗机构(个)',
          img: require('@/assets/csts/icon.png'),
          url: require('@/assets/zhdd/icon8.png'),
          icon: require('@/assets/zhdd/icon24.png'),
          name: '亿元'
        },
        {
          num: 1,
          tit: '避难场所(个)',
          img: require('@/assets/csts/icon.png'),
          url: require('@/assets/zhdd/icon9.png'),
          icon: require('@/assets/zhdd/icon25.png'),
          name: '元'
        },
        {
          num: 1056,
          tit: '视频监控(个)',
          img: require('@/assets/csts/icon.png'),
          url: require('@/assets/zhdd/icon10.png'),
          icon: require('@/assets/zhdd/icon26.png'),
          name: '元'
        }
      ]
    }
  }
}
</script>

<style lang="less" scoped>
.wrap {
  width: 100%;
  height: 100%;
  padding: 34px 0 52px 29px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-content: space-between;
  li {
    width: 143px;
    position: relative;
    display: flex;
    gap: 12px;
    .icon {
      width: 47px;
      height: 47px;
    }
    .desc {
      display: flex;
      flex-direction: column;
      padding-top: 4px;
      gap: 3px;
      text-align: left;
      span {
        height: 20px;
        font-size: 22px;
        font-family: DINOT-Black, DINOT;
        font-weight: 900;
        color: #ffffff;
        line-height: 28px;
      }
      .tit {
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
        white-space: nowrap;
      }
    }
  }
}
</style>
