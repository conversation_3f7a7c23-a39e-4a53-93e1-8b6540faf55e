import TRTC from 'trtc-sdk-v5'
// userId = user_手机号_main_参会名_Order-指令名
import { isMobile } from '@/utils/utils'

export default {
	data() {
		return {
			trtc: null,
			remoteUsersViews: [],
			remoteScreenViews: [],
			isMutedVideo: false,
			isMutedAudio: false,
			// status
			camStatus: 'stopped', // stopped, starting, started, stopping
			micStatus: 'stopped',
			roomStatus: 'exited', // exited, exiting, entering, entered
			shareStatus: 'stopped', // stopping, stopped, sharing, shared
			remoteStopVideoUserIdList: new Set(),
			remoteStartVideoUserIdList: new Set(),
			remoteStartAudioUserIdList: new Set(),
			remoteStopAudioUserIdList: new Set(),
			messageList: [],

			isBeiTi: false, //是否被踢
		}
	},

	methods: {
		initTRTC() {
			if (this.trtc) return
			this.trtc = TRTC.create()
		},

		async enterRoom() {
			this.roomStatus = 'entering'
			this.initTRTC()
			try {
				await this.trtc.enterRoom({
					roomId: this.roomId,
					sdkAppId: parseInt(this.sdkAppId, 10),
					userId: this.userId,
					userSig: this.userSig,
				})
				this.roomStatus = 'entered'

				this.installEventHandlers()
				this.startGetAudioLevel()
				this.reportSuccessEvent('enterRoom')
			} catch (error) {
				this.roomStatus = 'exited'
				console.error('enterRoom room failed', error)
				this.reportFailedEvent('enterRoom', error)
				// throw error
			}
		},

		async handleStartLocalAudio() {
			this.micStatus = 'starting'
			this.initTRTC()
			let dd = await TRTC.getMicrophoneList()
			try {
				await this.trtc.startLocalAudio({
					option: {
						// microphoneId: this.microphoneId,
						microphoneId: dd[0].deviceId,
					},
				})
				this.isMutedAudio = false
				this.micStatus = 'started'
				this.reportSuccessEvent('startLocalAudio')
			} catch (error) {
				this.micStatus = 'stopped'
				this.reportFailedEvent('startLocalAudio', error.message)
				// throw error
			}
		},

		async handleStopLocalAudio() {
			if (this.micStatus !== 'started') {
				return
			}
			this.micStatus = 'stopping'
			this.initTRTC()
			try {
				await this.trtc.stopLocalAudio()
				this.micStatus = 'stopped'
				this.reportSuccessEvent('stopLocalAudio')
			} catch (error) {
				this.micStatus = 'started'
				this.reportFailedEvent('stopLocalAudio', error.message)
				// throw error
			}
		},

		async handleStartLocalVideo() {
			this.camStatus = 'starting'
			this.initTRTC()

			let dd = await TRTC.getCameraList()
			console.log(this.camStatus, dd[0], 'ooooooooooooo')
			try {
				await this.trtc.startLocalVideo({
					view: 'local',
					option: {
						// cameraId: this.cameraId,
						cameraId: dd[0].deviceId,
						profile: '720p',
						mirror: false,
					},
				})
				this.camStatus = 'started'
				console.log(this.camStatus, 'uuuuuuuuuuu')
				this.isMutedVideo = false
				this.reportSuccessEvent('startLocalVideo')
			} catch (error) {
				console.log(this.camStatus, 'pppppppppp')
				this.camStatus = 'stopped'
				this.reportFailedEvent('startLocalVideo', error.message)
				// throw error
			}
		},

		async handleStopLocalVideo() {
			if (this.camStatus !== 'started') {
				return
			}
			this.camStatus = 'stopping'
			this.initTRTC()
			try {
				await this.trtc.stopLocalVideo()
				this.camStatus = 'stopped'
				this.reportSuccessEvent('stopLocalVideo')
			} catch (error) {
				this.camStatus = 'started'
				this.reportFailedEvent('stopLocalVideo', error.message)
				// throw error
			}
		},

		async exitRoom() {
			if (this.roomStatus !== 'entered') {
				return
			}
			this.roomStatus = 'exiting'
			this.stopGetAudioLevel()

			try {
				await this.trtc.exitRoom()
				this.roomStatus = 'exited'
				this.remoteUsersViews = []
				this.remoteStopVideoUserIdList.clear()
				this.remoteStopAudioUserIdList.clear()
				this.uninstallEventHandlers()

				this.reportSuccessEvent('exitRoom')
			} catch (error) {
				this.roomStatus = 'entered'
				this.reportFailedEvent('exitRoom', error)
				// throw error
			}

			if (this.micStatus === 'started') this.handleStopLocalAudio()
			if (this.camStatus === 'started') this.handleStopLocalVideo()
			if (this.shareStatus === 'shared') this.handleStopScreenShare()
			if (this.remoteScreenViews.length != 0) this.remoteScreenViews = []
		},

		async muteVideo() {
			try {
				await this.trtc.updateLocalVideo({ mute: true })
				this.isMutedVideo = true
			} catch (error) {}
		},

		async muteAudio() {
			try {
				await this.trtc.updateLocalAudio({ mute: true })
				this.isMutedAudio = true
			} catch (error) {}
		},

		async unmuteVideo() {
			try {
				await this.trtc.updateLocalVideo({ mute: false })
				this.isMutedVideo = false
			} catch (error) {}
		},

		async unmuteAudio() {
			try {
				await this.trtc.updateLocalAudio({ mute: false })
				this.isMutedAudio = false
			} catch (error) {}
		},

		async switchDevice(type, deviceId) {
			try {
				if (type === 'video' && this.camStatus === 'started') {
					await this.trtc.updateLocalVideo({
						option: { cameraId: deviceId },
					})
				}
				if (type === 'audio' && this.micStatus === 'started') {
					await this.trtc.updateLocalAudio({
						option: { microphoneId: deviceId },
					})
				}
			} catch (error) {
				console.error('switchDevice failed', error)
			}
		},

		startGetAudioLevel() {
			this.trtc.on(TRTC.EVENT.AUDIO_VOLUME, (event) => {
				event.result.forEach(({ userId, volume }) => {
					const isMe = userId === ''
					if (isMe) {
						console.log(`my volume: ${volume}`)
					} else {
						console.log(`user: ${userId} volume: ${volume}`)
					}
				})
			})
			this.trtc.enableAudioVolumeEvaluation(2000)
		},

		stopGetAudioLevel() {
			this.trtc && this.trtc.enableAudioVolumeEvaluation(-1)
		},

		installEventHandlers() {
			this.trtc.on(TRTC.EVENT.ERROR, this.handleError)
			this.trtc.on(TRTC.EVENT.KICKED_OUT, this.handleKickedOut)
			this.trtc.on(TRTC.EVENT.REMOTE_USER_ENTER, this.handleRemoteUserEnter)
			this.trtc.on(TRTC.EVENT.REMOTE_USER_EXIT, this.handleRemoteUserExit)
			this.trtc.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, this.handleRemoteVideoAvailable)
			this.trtc.on(TRTC.EVENT.REMOTE_VIDEO_UNAVAILABLE, this.handleRemoteVideoUnavailable)
			this.trtc.on(TRTC.EVENT.REMOTE_AUDIO_UNAVAILABLE, this.handleRemoteAudioUnavailable)
			this.trtc.on(TRTC.EVENT.REMOTE_AUDIO_AVAILABLE, this.handleRemoteAudioAvailable)
			this.trtc.on(TRTC.EVENT.CUSTOM_MESSAGE, this.handleRemotereceiveMessage)
			this.trtc.on(TRTC.EVENT.SCREEN_SHARE_STOPPED, this.handleScreenShareStopped)
		},

		uninstallEventHandlers() {
			this.trtc.off(TRTC.EVENT.ERROR, this.handleError)
			this.trtc.off(TRTC.EVENT.KICKED_OUT, this.handleKickedOut)
			this.trtc.off(TRTC.EVENT.REMOTE_USER_ENTER, this.handleRemoteUserEnter)
			this.trtc.off(TRTC.EVENT.REMOTE_USER_EXIT, this.handleRemoteUserExit)
			this.trtc.off(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, this.handleRemoteVideoAvailable)
			this.trtc.off(TRTC.EVENT.REMOTE_VIDEO_UNAVAILABLE, this.handleRemoteVideoUnavailable)
			this.trtc.off(TRTC.EVENT.REMOTE_AUDIO_UNAVAILABLE, this.handleRemoteAudioUnavailable)
			this.trtc.off(TRTC.EVENT.REMOTE_AUDIO_AVAILABLE, this.handleRemoteAudioAvailable)
			this.trtc.off(TRTC.EVENT.CUSTOM_MESSAGE, this.handleRemotereceiveMessage)
			this.trtc.off(TRTC.EVENT.SCREEN_SHARE_STOPPED, this.handleScreenShareStopped)
		},

		handleError(error) {
			alert(error)
		},

		async handleKickedOut(event) {
			this.trtc = null
			this.remoteStopVideoUserIdList.clear()
			this.remoteStopAudioUserIdList.clear()
			await this.exitRoom()
		},

		handleRemoteUserEnter(event) {
			const { userId } = event
			this.remoteUsersViews.push(`${userId}_main`)
			this.remoteStopVideoUserIdList.add(event.userId)
			this.remoteStopAudioUserIdList.add(event.userId)
			console.log(event, this.remoteUsersViews, '远程有人进来了！')
		},

		handleRemoteUserExit(event) {
			console.log(event, this.remoteUsersViews, '远程有人滚蛋了！')
			if (this.remoteStopVideoUserIdList.has(event.userId)) this.remoteStopVideoUserIdList.delete(event.userId)
			if (this.remoteStopAudioUserIdList.has(event.userId)) this.remoteStopAudioUserIdList.delete(event.userId)
			if (this.remoteStartVideoUserIdList.has(event.userId)) this.remoteStartVideoUserIdList.delete(event.userId)
			if (this.remoteStartAudioUserIdList.has(event.userId)) this.remoteStartAudioUserIdList.delete(event.userId)
			this.remoteUsersViews = this.remoteUsersViews.filter((userId) => userId !== `${event.userId}_main`)
		},

		handleRemoteVideoAvailable(event) {
			console.log(event, this.remoteUsersViews, '远程操作打开摄像头')
			const { userId, streamType } = event
			if (streamType === TRTC.TYPE.STREAM_TYPE_MAIN) {
				this.remoteStartVideoUserIdList.add(event.userId)
				this.remoteStopVideoUserIdList.delete(event.userId)
				if (!this.remoteUsersViews.includes(`${userId}_main`)) {
					this.remoteUsersViews.push(`${userId}_main`)
				}
				this.$nextTick(async () => {
					// 根据当前显示状态决定绑定到哪个容器
					const viewId = this.getVideoViewId(userId)
					await this.trtc.startRemoteVideo({ userId, streamType, view: viewId })
				})
			} else {
				if (!this.remoteScreenViews.includes(`${userId}_screen`)) {
					this.remoteScreenViews.push(`${userId}_screen`)
				}
				this.$nextTick(async () => {
					await this.trtc.startRemoteVideo({ userId, streamType, view: `${userId}_screen` })
				})
			}
		},

		// 根据当前显示状态获取视频容器ID
		getVideoViewId(userId) {
			console.log(this.showRightCam, this.isExpand, 'kkkkkkk')
			// 如果右侧摄像头显示且展开，则使用右侧容器
			if (this.showRightCam && this.isExpand) {
				return `right-video-${userId}_main`
			}
			// 否则使用中间容器
			return `${userId}_main`
		},

		handleRemoteVideoUnavailable(event) {
			const { streamType } = event
			console.log(event, this.remoteUsersViews, '远程操作关闭摄像头')
			this.trtc.stopRemoteVideo({ userId: event.userId, streamType })
			if (streamType === TRTC.TYPE.STREAM_TYPE_MAIN) {
				this.remoteStartVideoUserIdList.delete(event.userId)
				this.remoteStopVideoUserIdList.add(event.userId)
				// this.remoteUsersViews = this.remoteUsersViews.filter((userId) => userId !== `${event.userId}_main`)
			} else {
				this.remoteScreenViews = this.remoteScreenViews.filter((userId) => userId !== `${event.userId}_screen`)
			}
		},

		handleRemoteAudioUnavailable(event) {
			this.remoteStopAudioUserIdList.add(event.userId)
			this.remoteStartAudioUserIdList.delete(event.userId)
			console.log(event, this.remoteUsersViews, '远程操作关闭麦克风')
		},

		handleRemoteAudioAvailable(event) {
			this.remoteStopAudioUserIdList.delete(event.userId)
			this.remoteStartAudioUserIdList.add(event.userId)
			console.log(event, this.remoteUsersViews, '远程操作打开麦克风')
		},
		async handleRemotereceiveMessage(event) {
			console.log(event, '收到了事件消息')
			let obj = event
			obj.data = new TextDecoder().decode(event.data)
			obj.timestamp = new Date()
			if (event.cmdId == 1) this.messageList.push(obj)
			else if (obj.data.split('_')[1] == this.userId.split('_')[1]) {
				switch (event.cmdId) {
					case 2: //关摄像头
						await this.handleStopLocalVideo()
						break
					case 3: //被改名字
						/* let newName = obj.data.split('_main_')[1];
						this.remoteUsersViews.some(d=>{
							if(d.indexOf(this.userId)>-1){

							}
						}) */
						break
					case 4: //被踢出去
						this.isBeiTi = true
						await this.exitRoom()
						if (isMobile) {
							alert('您被' + event.userId.split('_')[2] + '请出了房间')
						} else {
							if (location.href.indexOf('PCmeeting').indexOf > -1) {
								this.$message('您被' + event.userId.split('_')[2] + '请出了房间，此窗口将在3秒钟后关闭')
							} else {
								this.$message('您被' + event.userId.split('_')[2] + '请出了房间')
							}
						}
						break
					case 5: //被关麦克风
						await this.handleStopLocalAudio()
						break
					case 6: //开麦克风
						if (isMobile) {
							alert('房主请求与会者打开麦克风')
						} else {
							this.$message('房主请求与会者打开麦克风')
						}
						break
					case 7: //开摄像头
						if (isMobile) {
							alert('房主请求与会者打开摄像头')
						} else {
							this.$message('房主请求与会者打开摄像头')
						}
						break
					default:
						break
				}
			}
			console.log(event, this.messageList, '远程操作有人说话')
		},
		handleScreenShareStopped() {
			this.shareStatus = 'stopped'
		},
	},
}
