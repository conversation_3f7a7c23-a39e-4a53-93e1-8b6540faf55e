<template>
	<div class="leader_box">
		<!-- 左侧和相关农机数据可视化 -->
		<div class="leader_sjts">
			<div class="left" :class="mkdx ? 'mkdxLeft' : ''">
				<!-- 左侧农机数据可视化 -->
				<div class="left1">
					<BlockBox
						leftTitle="农机数据"
						style="z-index: 999"
						rightTitle="NONGJISHUJV"
						class="box box1"
						:isListBtns="false"
						:blockHeight="687"
						:blockBackgroundImage="block1BackImg"
					>
						<BlockBoxChild
							title="当日作业"
							style="z-index: 999"
							:blockboxIcon="currentWorkIcon"
							:contentNumberUnitOne="contentNumberUnitOne"
							:contentNumberOne="currentWorkTotal"
							:contentNumberTwo="currentWorkPercent"
							:contentDataList="currentWorkDataList"
						></BlockBoxChild>
						<BlockBoxChild
							v-if="areaLevel == 0"
							title="跨区作业"
							style="z-index: 998"
							:blockboxIcon="crossWorkIcon"
							:contentNumberUnitOne="contentNumberUnitOne"
							:contentNumberOne="crossWorkTotal"
							:contentNumberTwo="crossWorkPercent"
							:contentDataList="crossWorkDataList"
						></BlockBoxChild>
						<BlockBoxChild
							v-else
							:tabs="[
								{ label: '流入', value: 'in' },
								{ label: '流出', value: 'out' },
							]"
							style="z-index: 998"
							:contentNumberUnitOne="contentNumberUnitOne"
							:blockboxIcon="crossWorkIcon"
							:contentNumberOne="crossWorkTotal"
							:contentNumberTwo="crossWorkPercent"
							:contentDataList="crossWorkDataList"
							@handleChangeTab="handleChangeTab"
						></BlockBoxChild>
						<BlockBoxChild
							title="网联农机"
							style="z-index: 997"
							:contentNumberUnitOne="contentNumberUnitOne"
							:blockboxIcon="zxnjIcon"
							:contentNumberOne="zxnjTotal"
							:contentNumberTwo="zxnjPercent"
							:contentDataList="zxnjDataList"
						></BlockBoxChild>
					</BlockBox>
				</div>
			</div>
			<div class="top-middle" :class="mkdx ? 'mkdx-top-middle' : ''">
				<top-header-descripte :dataList="topDescripteDataList" :unitName="contentNumberUnitOne"></top-header-descripte>
			</div>
			<div class="right" :class="mkdx ? 'mkdxRight' : ''">
				<!-- 右侧农机数据可视化 -->
				<div class="right1">
					<BlockBox
						:leftTitle="zxnjChartTitle"
						style="z-index: 998"
						rightTitle="QIRIZAIXIAN"
						subtitle="Party Building Information"
						class="box box7"
						:isListBtns="false"
						:blockHeight="336"
						:blockBackgroundImage="block2BackImg"
					>
						<!-- 3、七日在线 -->
						<DrawDataBar
							:textArr2="argmachTypeList"
							:argmachType="zxnj7CurrentArgmachTypeSelected"
							:minHeight="275"
							:yUnitName="zxnjChartTitle"
							:yUnit="contentNumberUnitOne"
							:xData="zxnj7XData"
							:filterMarginTop="10"
							:marginTop="40"
							xDataType="date"
							:yDatas="zxnj7YDatas"
							itemColor="18,135,122"
							formatterType="qrzx"
							:tooltipTitleName="zxnjTooltipTitleName"
							:showMonthTypeSelect="true"
							:chartsLegend="zxnj7ChartsLegend"
							@handleSearch="handleSearchZxnjNData"
						></DrawDataBar>
					</BlockBox>

					<BlockBox
						:leftTitle="workChartTitle"
						rightTitle="QIRIZUOYE"
						style="z-index: 998"
						subtitle="Full Factor Grid Events"
						class="box box8"
						:showMore3="false"
						:isListBtns="false"
						:blockHeight="336"
						:blockBackgroundImage="block3BackImg"
					>
						<!-- itemColor="208,222,238" -->

						<!-- 4、七日作业-->
						<DrawDataBar
							:textArr2="argmachTypeList"
							:argmachType="work7CurrenArgmachTypeSelected"
							:yUnit="contentNumberUnitOne"
							:minHeight="275"
							:yUnitName="workChartTitle"
							:xData="work7XData"
							:filterMarginTop="10"
							:marginTop="40"
							xDataType="date"
							formatterType="qrzy"
							:yDatas="work7YDatas"
							itemColor="121, 218, 239"
							:showMonthTypeSelect="true"
							:tooltipTitleName="workTooltipTitleName"
							:chartsLegend="work7ChartsLegend"
							@handleSearch="handleSearchWorkNData"
						></DrawDataBar>
					</BlockBox>
				</div>
			</div>
		</div>
		<!-- 底部农机数据可视化 -->
		<div class="middle" :class="mkdx ? 'mkdxMiddle' : ''">
			<BlockBox
				leftTitle="农机数据"
				rightTitle="NONGJISHUJV"
				class="box box10"
				useHeaderBgType="bottom"
				:isListBtns="false"
				:blockWidth="1890"
				:blockHeight="249"
				:blockBackgroundImage="block4BackImg"
			>
				<!-- 4、各农机在各省的数据-->
				<DrawDataBar
					:xData="argmachByProvinceXData"
					:yUnit="contentNumberUnitOne"
					:minHeight="186"
					:showBarBg="true"
					:marginTop="10"
					dataType="njData"
					top="26"
					bottom="0"
					minWidth="100%"
					:yDatas="argmachByProvinceYData"
				></DrawDataBar>
			</BlockBox>
		</div>
		<!-- 地图组件 -->
		<div class="map_box">
			<LeaMap
				ref="leafletMap"
				@poiClick="poiClick"
				@gridClick="gridClick"
				@operate="operate"
				@qctc="qctc"
				:clear2="true"
				:back2="true"
			/>
		</div>
		<!-- 搜索地方 -->
		<div class="ssPop" v-if="sspopShow" :style="{ bottom: operateBoxBottom + 38 + 'px', right: operateBoxRight + 'px' }">
			<div class="ssPop1">
				<input type="text" v-model="ssnr" @change="ssqy" />
				<div class="ssImgBox" @click="ssqy">
					<img src="@/assets/bjnj/ss.png" alt="" />
				</div>
			</div>
			<div class="ssPop2" v-if="sspopList">
				<div class="ssList" v-for="(item, index) in ssList" :key="index" @click="ssxz(item)">{{ item.areaName }}</div>
			</div>
		</div>
		<!-- 图例 -->
		<div class="njtlList" :style="{ bottom: mapBtnBottom + 110 + 'px', right: mapBtnRight - 5 + 'px' }" v-if="isnjtlListShow">
			<div class="njtlItem">
				<!-- <img src="@/assets/bjnj/njtlImg2.png" alt="" /> -->
				<img src="@/assets/mskx/current-online-icon.png" alt="" />
				<div class="njtlName">当前在线</div>
			</div>
			<div class="njtlItem">
				<!-- <img src="@/assets/bjnj/njtlImg1.png" alt="" /> -->
				<img src="@/assets/mskx/current-day-online-icon.png" alt="" />
				<div class="njtlName">当日在线</div>
			</div>
			<div class="njtlItem">
				<!-- <img src="@/assets/bjnj/njtlImg3.png" alt="" /> -->
				<img src="@/assets/mskx/offline-icon.png" alt="" />
				<div class="njtlName">离线</div>
			</div>
		</div>

		<!-- 需要在地图上显示什么 -->
		<div class="mapBtn" :style="{ bottom: mapBtnBottom + 'px', right: mapBtnRight + 'px' }">
			<div class="mapItem" :class="mapShow == 3 ? 'active-item' : ''" @click="mapActive(3)">
				<span>农机分布图</span>
			</div>
			<div class="mapItem" :class="mapShow == 4 ? 'active-item' : ''" @click="mapActive(4)">
				<span>在线农机图</span>
			</div>
			<!-- <div class="mapItem" :class="openFromVideoDialog ? 'active-item' : ''" @click="openFormVideo">
				<span>农场监控</span>
			</div> -->
			<!-- <div v-if="showWorkHeat" class="mapItem" :class="mapShow == 7 ? 'active-item' : ''" @click="mapActive(7)">
				<span>作业热力图</span>
			</div> -->
		</div>

		<!-- 选择农机在地图上显示相应数据 -->
		<div
			v-if="(mapShow == 3 || mapShow == 4 || mapShow == 7) && njfbShow"
			:style="{ bottom: mapBtnBottom + 35 + 'px', right: mapBtnRight + 106 + 'px' }"
			class="njfbBox"
			:class="mapShow == 4 ? 'njfbBox2' : ''"
		>
			<div class="zyfbItem" :class="resourceTypes == 100000 ? 'jgzzActive' : ''" @click="njlxqh(100000)">
				<span>全部</span>
			</div>
			<div class="zyfbItem" :class="resourceTypes == 510101 ? 'jgzzActive' : ''" @click="njlxqh(510101)">
				<span>轮式拖拉机</span>
			</div>
			<div class="zyfbItem" :class="resourceTypes == 150105 ? 'jgzzActive' : ''" @click="njlxqh(150105)">
				<span>谷物联合收割机</span>
			</div>
			<div class="zyfbItem" :class="resourceTypes == 150106 ? 'jgzzActive' : ''" @click="njlxqh(150106)">
				<span>玉米收获机</span>
			</div>
			<div class="zyfbItem" :class="resourceTypes == 120401 ? 'jgzzActive' : ''" @click="njlxqh(120401)">
				<span>水稻插秧机</span>
			</div>
			<div class="zyfbItem" :class="resourceTypes == 150302 ? 'jgzzActive' : ''" @click="njlxqh(150302)">
				<span>花生收获机</span>
			</div>
			<div class="zyfbItem" :class="resourceTypes == 150303 ? 'jgzzActive' : ''" @click="njlxqh(150303)">
				<span>油菜籽收获机</span>
			</div>
			<div class="zyfbItem" :class="resourceTypes == 150403 ? 'jgzzActive' : ''" @click="njlxqh(150403)">
				<span>甘蔗联合收获机</span>
			</div>
			<div class="zyfbItem" :class="resourceTypes == '000000' ? 'jgzzActive' : ''" @click="njlxqh('000000')">
				<span>其他</span>
			</div>
		</div>

		<!-- 地图相关的操作按钮 -->
		<div class="map-operate-box" :style="{ bottom: operateBoxBottom + 'px', right: operateBoxRight + 'px' }">
			<div
				class="operate-item-box"
				v-show="!operateItem.hide"
				v-for="(operateItem, index) in operateBtnList"
				:key="index"
				@click="operateItem.func()"
			>
				<img :src="operateItem.iconUrl" alt="" />
				<div class="operate-title">{{ operateItem.title }}</div>
				<div class="devider"></div>
			</div>
		</div>
		<!-- 基础信息弹窗 -->
		<BasicInformation
			v-model="basicInfomationShow"
			:title="basicInfomationTitle"
			:list="basicInfomationList"
			:btnShow="basicInfomationBtnShow"
			:btns="basicInfomationBtns"
			@handle="clickBasicInformationBtn"
		/>

		<!-- 农机基本信息弹窗 -->
		<njInfo
			v-if="njInfoShow"
			:realTimeButtonShow="realTimeButtonShow"
			:agmachInfo="currentAgmachInfo"
			@handleClick="handleNjInfo"
			@closeEmitai="njInfoShow = false"
			@showRealTimeTrack="showRealTimeTrack"
		/>

		<!-- 历史作业弹窗 -->
		<historyWorkPop
			ref="historyWorkPop"
			v-model="workPopShow"
			:agmachInfo="currentAgmachInfo"
			:agmachId="agmachId"
			@operate="eventInfoBtnClick15"
		/>

		<!-- 作业轨迹弹窗 -->
		<mapPop ref="mapPop" v-model="isMapShow" />
		<RealTimeMapPop ref="mapRealTimePop" :startRunTime="startRunTime" v-model="isRealTimeMapShow" />
		<!-- <div class="ai_waring" v-if="ncjkDialogShow">
			<div class="title">
				<span>监控查看</span>
			</div>
			<div class="close_btn" @click="ncjkDialogShow = false"></div>
			<div class="content">
				<div id="ezuikit-player" style="width: 100%;height: 100%;"></div>
			</div>
		</div> -->
	</div>
</template>

<script>
const DataVUrl = 'https://geo.datav.aliyun.com/areas_v3/bound/'
import EZUIKit from 'ezuikit-js'

import BlockBox from '@/components/leader/common/blockBox6.vue'
import BlockBox8 from '@/components/leader/common/blockBox8.vue'
import BlockBoxChild from '@/components/leader/common/blockBoxChild.vue'

import HeaderMain from '@/components/leader/common/headerMain.vue'
import effectRankBarSwiper from './components/effectRankBar-swiper.vue'
import particle from './components/particle.vue'
import leaderMiddle from '@/components/leader/common/leaderMiddle.vue'
// import gdMap from '@/components/leader/gdMap/gdMap.vue'
// import cesiumMap from '@/components/leader/cesiumMap/Cesium3dMap.vue'
// import cssjPoint from '@/assets/json/csts/cssjPoint.json'
// import spjkPoint from '@/assets/json/csts/spjkPoint.json'
// import csbjPoint from '@/assets/json/csts/csbjPoint.json'
// import ryllPoint from '@/assets/json/csts/ryllPoint.json'
import xzqhPoint from '@/assets/json/csts/xzqhPoint.json'
// import dzzPoint from '@/assets/json/csts/dzzPoint.json'
// import zwzxPoint from '@/assets/json/csts/zwzxPoint.json'
// import zzzxPoint from '@/assets/json/csts/zzzxPoint.json'
// import zdqyPoint from '@/assets/json/csts/zdqyPoint.json'
// import xxPoint from '@/assets/json/csts/xxPoint.json'
// import wbPoint from '@/assets/json/csts/wbPoint.json'
// import ylcsPoint from '@/assets/json/csts/ylcsPoint.json'
// import hczPoint from '@/assets/json/csts/hczPoint.json'
// import dyyPoint from '@/assets/json/csts/dyyPoint.json'
import yjzyPop from '@/components/bjnj/yjzyPop.vue' //应急资源
import myRobot from '@/components/robot/myRobot.vue'
import NumScroll from '@/components/leader/num_scrolls'
import Area from '@/components/leader/leader_csts/area.vue'
import svgMap from '@/components/common/svgMap.vue'
import zlBg from '@/components/common/zlBg.vue'
import LivePlayer from '@liveqing/liveplayer'
import SwiperTable from '@/components/bjnj/SwiperTable.vue'
import LeaMap from '@/components/map/LeafletMapNj.vue'
import {
	cscpCurrentUserDetails,
	getAgmachGuiJiInfo,
	queryByList,
	getDistributionCount,
	getZxnjDistributionCount,
	getAgmachRealTimeGuiJiInfo,
} from '@/api/bjnj/zhdd.js'
import { agmachToTermId } from '@/api/njzl/hs.api.js'

import { BasicInformation } from './components/zhdd/dialog'
import AmachTypeSwiper from './components/zhdd/AmachTypeSwiper.vue'
import znzyPop from '@/components/bjnj/znzyPop.vue'
import njInfo from '@/components/bjnj/njInfo.vue'
import historyWorkPop from '@/components/bjnj/historyWorkPop.vue'
import mapPop from '@/components/bjnj/mapPop.vue'
import RealTimeMapPop from '@/components/bjnj/realTimeMapPop.vue'

import kqzyPop from '@/components/bjnj/kqzyPop.vue'
import province from '@/components/map/province.json'

import rtc from '@/rhtx/core/index'
import { aqjgGetToken } from '@/api/hs/hs_aqjg.api.js'
import dayjs from 'dayjs'
import {
	getNjCount3,
	getNjTodayCount2,
	getNjCurrent7Count,
	getYesterdayNjCurrent7Count,
	getNjAreaDayType,
	getNjAreaDayType2,
	getNjLocation,
	getNjLocations,
	getNjAreaDayTypes2,
	getAgmachTypeList,
	getNjAreaDayTypes,
	locHistory2,
	agmachCount,
	cqCount,
	agmachtypeCount,
	agmachtypeLocList,
	work,
	identification,
	count,
	onlineCounts,
	exit,
	onlyExit,
	exitCount,
	geojson,

	//新优化
	api_getCurrentStatisticInternetMachine,
	api_getCurrentInternetMachine,
	api_getCurrentWorkStatisticData,
	api_getCurrentWorkMachine,
	api_getCrossWorkStatisticMachine,
	api_getCrossWorkMachine,
	api_getZxnjNData,
	api_getWorkNData,
	distributionhour,
	api_getCurrentWorkArgmachByRegionData,
	api_getCrossWorArgmachByRegionData,
	api_getInternetByRegionData,
	getNjTodayCount,
	getNjOnlineCount,
	getYesterdayNjOnlineCount,
	getNcjkToken,
	getNcjkUrl,
} from '@/api/njzl/hs.api.js'

//新优化
import zxnjData from '@/assets/json/nongjiData/zxnjData'
import currentWorkData from '@/assets/json/nongjiData/currentWorkData'
import crossWorkData from '@/assets/json/nongjiData/crossWorkData'

import zxnj7Data from '@/assets/json/nongjiData/zxnj7Data'
import work7Data from '@/assets/json/nongjiData/work7Data'
import argmachByProvinceData from '@/assets/json/nongjiData/argmachByProvinceData'
import argmachByProvinceDataA from '@/assets/json/nongjiData/argmachByProvinceDataA'

//新优化
import DrawDataBar from '@/components/ssts/draw-data-bar.vue'
import TopHeaderDescripte from '@/components/leader/common/top-header-descripte.vue'
import 'swiper/css/swiper.css'
export default {
	name: 'Hszl',
	components: {
		BlockBox,
		BlockBox8,
		BlockBoxChild,
		HeaderMain,
		effectRankBarSwiper,
		particle,
		leaderMiddle,
		// gdMap,
		// cesiumMap,
		myRobot,
		Area,
		NumScroll,
		// LandUse,
		// FullFactorGridEvents,
		// StreetSurvey,
		// jjfztk,
		// hswhtk,
		// stjstk,
		// wgzltk,
		svgMap,
		zlBg,
		LivePlayer,
		SwiperTable,
		LeaMap,
		BasicInformation,
		znzyPop,
		njInfo,
		historyWorkPop,
		yjzyPop,
		mapPop,
		AmachTypeSwiper,
		//新优化
		DrawDataBar,
		TopHeaderDescripte,
		RealTimeMapPop,
	},
	data() {
		return {
			ssnr: '',
			ssList: [],
			ssboxShow: true,
			sspopShow: false,
			sspopList: false,
			startDate: '',
			endDate: '',

			contentNumberUnitOne: '万台',

			dqbf: dayjs()
				.subtract(1, 'year')
				.format('YYYY'),
			dqyf: '',
			dqyfs: '',

			groupLevel: 2,
			njfbShow: false,
			mapShow: 4, //3:农机分布；4:在线农机；7:作业热力；
			//本次的地图绘制是否和上次绘制的一样
			sameMapShow: false,

			time: 0,
			dwType: '',
			mkdx: false,
			agmachId: '',
			areaId: '100000',
			areaLevel_init: 0,
			//areaLevel 0：全国，1：省份，2：地市，3：区县
			areaLevel: 0,
			trajectoryMark: '',
			resourceTypes: -1,

			workPopShow: false,
			njInfoShow: false,
			isnjtlListShow: false,
			isMapShow: false,
			// 控制基本信息弹窗显示
			basicInfomationShow: false,
			// 基础信息标题
			basicInfomationTitle: '',
			// 基础信息内容
			basicInfomationList: [],
			// 控制基础信息按钮显示
			basicInfomationBtnShow: false,
			// 基础信息按钮
			basicInfomationBtns: [],

			activaIdx: 4, //当前地图中渲染的数据是什么数据

			currentAgmachInfo: {},
			realTimeButtonShow: false,

			amachTypeList: [],
			agmachTypeCode: '',
			agmachTypeName: '',
			lastAreaLevel: [],

			//新优化
			zxnjTotal: 0,
			zxnjPercent: 0,
			zxnjDataList: [],

			currentWorkTotal: 0,
			currentWorkPercent: 0,
			currentWorkDataList: [],

			crossWorkTotal: 0,
			crossWorkPercent: 0,
			crossWorkDataList: [],
			zxnj7CurrentArgmachTypeSelected: '510101',
			zxnj7CurrentMonthTypeSelected: '1',

			zxnj7XData: [],
			zxnj7YDatas: {},
			argmachTypeList: [],
			zxnj7ChartsLegend: [],

			work7CurrenArgmachTypeSelected: '510101',
			work7CurrenMonthTypeSelected: '1',

			work7XData: [],
			work7YDatas: {},
			work7ChartsLegend: [],

			argmachByProvinceXData: [],
			argmachByProvinceYData: {},
			currentClickArgmachType: '510101',

			mapBtnBottom: 289,
			mapBtnRight: 507,
			operateBoxBottom: 289,
			operateBoxRight: 637,
			showWorkHeat: true,
			currentTabValue: 'in',
			topDescripteDataList: [],
			block1BackImg: require('@/assets/img/block-box/block-left-nongjishujv-bg.png'),
			block2BackImg: require('@/assets/img/block-box/block-qirizaixian-bg.png'),
			block3BackImg: require('@/assets/img/block-box/block-qirizuoye-bg.png'),
			block4BackImg: require('@/assets/img/block-box/block-ssts-nongjishujv-bg.png'),
			startRunTime: '',
			isRealTimeMapShow: false,
			// openFromVideoDialog: false,
			// ncjkDialogShow: false,
			zxnjChartTitle: '七日在线',
			workChartTitle: '七日作业',
			zxnjTooltipTitleName: '数量',
			workTooltipTitleName: '数量',
		}
	},
	beforeDestroy() {
		this.timer && clearInterval(this.timer)
		this.timer1 && clearInterval(this.timer1)
	},
	mounted() {
		// 获取滚动容器和文本元素
		// const scrollingText = document.querySelector('.introduce')
		// const text = document.querySelector('.introduce p')
		// // 获取文本元素的高度
		// const textHeight = text.offsetHeight

		// 定义滚动函数
		// function scroll() {
		//   // 如果文本元素已经完全滚动出去，则将其重置到滚动容器的顶部
		//   if (scrollingText.scrollTop >= 96) {
		//     scrollingText.scrollTop = 0
		//   }
		//   // 否则，将滚动容器向上滚动一个像素的距离
		//   else {
		//     scrollingText.scrollTop += 1
		//   }
		// }
		// 获取安全监管系统的token
		this.getNjAreaDayType()

		// 每隔20毫秒调用一次滚动函数
		// this.timer = window.setInterval(scroll, 100)

		// this.aqjgGetTokenFn()
		this.$nextTick(() => {
			this.cscpCurrentUserDetails()
		}, 500)
	},
	watch: {},
	computed: {
		formatNumber() {
			return function(param) {
				if (this.isEmpty(param) || param.length < 4 || isNaN(param)) {
					return param
				}

				let small = ''
				let isSmall = false
				if (param.split('.').length === 2) {
					// 有小数
					isSmall = true
					small = param.split('.')[1]
					param = param.split('.')[0]
				}

				let result = ''

				while (param.length > 3) {
					result = ',' + param.slice(-3) + result
					param = param.slice(0, param.length - 3)
				}

				if (param) {
					result = param + result
				}

				if (isSmall) {
					result = result + '.' + small
				}
				return result
			}
		},
		initOptions1() {
			return {
				yAxis: {
					name: '台/天',
					nameTextStyle: {
						fontSize: 12,
						padding: [30, 0, 0, 0],
					},
					axisLabel: {
						textStyle: {
							fontSize: 12,
						},
					},
				},
				xAxis: {
					axisLabel: {
						show: true,
						textStyle: {
							fontSize: 12,
						},
					},
				},
				// dataZoom: [
				//   {
				//     type: 'inside', // 内置型式的 dataZoom 组件
				//     xAxisIndex: 0, // 对应 x 轴的索引，默认为 0
				//     start: 0, // 起始位置的百分比
				//     end: 12, // 结束位置的百分比
				//     realtime: true // 启用实时滚动
				//   }
				// ]
			}
		},
		initOption2() {
			return {
				yAxis: [
					{
						minInterval: 1,
					},
					{
						minInterval: 1,
					},
				],
				tooltip: {
					formatter: (params) => {
						let relVal = params[0].name
						for (let i = 0, l = params.length; i < l; i++) {
							let seriesName = params[i].seriesName + ':'
							let unit = params[i].seriesType == 'bar' ? '亩' : '台'
							relVal += '<br/>' + params[i].marker + seriesName + params[i].value + unit
						}
						return relVal
					},
				},
				series: [
					{
						barWidth: 30,
					},
				],
			}
		},
		barInitOptions() {
			return {
				yAxis: {
					nameTextStyle: {
						width: 200,
					},
				},
				xAxis: [
					{
						show: false,
						axisTick: {
							show: false, // 不显示坐标轴刻度线
						},
						axisLine: {
							show: false, // 不显示坐标轴线
						},
						axisLabel: {
							show: false, // 不显示坐标轴上的文字
						},
						// max: Math.max(...this.barChartData.map(item => { return item[1] } )),
						nameTextStyle: {
							width: 200,
						},
					},
				],
				grid: {
					right: '10%',
					left: '5%',
				},
				tooltip: {
					show: true,
					triggerOn: 'mousemove',
					formatter: (params) => {
						var relVal = ''
						for (var i = 0, l = params.length; i < l; i++) {
							var unit = '台'
							relVal += params[i].marker + params[i].value + unit
						}
						return relVal
					},
				},
				// dataZoom: [
				//   {
				//     type: 'inside', // 内置型式的 dataZoom 组件
				//     yAxisIndex: [0], // 对应 x 轴的索引，默认为 0
				//     start: 0, // 起始位置的百分比
				//     end: 40, // 结束位置的百分比
				//     realtime: true,// 启用实时滚动
				//     zoomOnMouseWheel: false,  // 关闭滚轮缩放
				//     moveOnMouseWheel: true, // 开启滚轮平移
				//     moveOnMouseMove: true  // 鼠标移动能触发数据窗口平移
				//   },
				//   {
				//     type: 'slider',
				//     realtime: true,
				//     startValue: 0,
				//     endValue: 5,
				//     width: '2',
				//     height: '100%',
				//     yAxisIndex: [0], // 控制y轴滚动
				//     fillerColor: 'rgba(154, 181, 215, 1)', // 滚动条颜色
				//     borderColor: 'rgba(17, 100, 210, 0.12)',
				//     backgroundColor: '#cfcfcf', //两边未选中的滑动条区域的颜色
				//     handleSize: 0, // 两边手柄尺寸
				//     showDataShadow: false, //是否显示数据阴影 默认auto
				//     showDetail: false, // 拖拽时是否展示滚动条两侧的文字
				//     top: '1%',
				//     right: '5'
				//   }
				// ],
			}
		},

		//新优化
		zxnjIcon() {
			return require('@/assets/img/block-box/zxnj-icon.png')
			// return require('../../assets/img/block-box/zxnj-icon.png')
			// assets/img/block-box/zxnj-icon.png')
		},
		currentWorkIcon() {
			return require('@/assets/img/block-box/current-work-icon.png')
		},
		crossWorkIcon() {
			return require('@/assets/img/block-box/cross-work-icon.png')
		},

		operateBtnList() {
			return [
				{
					iconClass: 'el-icon-back',
					title: '返回上级',
					iconUrl: require('@/assets/img/operate/return.png'),
					func: () => {
						//返回上一级函数
						this.$refs.leafletMap.backLayers()
					},
				},
				{
					iconClass: 'el-icon-full-screen',
					title: '页面全屏',
					iconUrl: require('@/assets/img/operate/all-screen.png'),

					func: () => {
						//页面全屏
						this.mkssBtn()
					},
				},
				{
					iconClass: 'el-icon-refresh-right',
					title: '刷新内容',
					iconUrl: require('@/assets/img/operate/refresh.png'),

					func: () => {
						//刷新内容
						this.njlxqh(this.resourceTypes)

						this.sxTk()
					},
				},
				{
					iconClass: 'el-icon-menu',
					title: '图例',
					iconUrl: require('@/assets/img/operate/example-icon.png'),
					func: () => {
						//图例
						this.njtlTk()
					},
				},
				{
					iconClass: 'el-icon-view',
					title: '隐藏标记',
					iconUrl: require('@/assets/img/operate/show-operate.png'),
					func: () => {
						if (!this.$store.state.mapLoaded) {
							this.$message.warning('请等待地图加载完成！')
							return
						}
						//隐藏标记
						this.mapShow = -1
						this.resourceTypes = -1
						this.$refs.leafletMap.clearLayers()
					},
				},
				{
					iconClass: 'el-icon-sort',
					type: 'switch',
					title: '切换地图',
					iconUrl: require('@/assets/img/operate/switch-map.png'),

					func: () => {
						if (!this.$store.state.mapLoaded) {
							this.$message.warning('请等待地图加载完成！')
							return
						}
						//切换地图
						this.$refs.leafletMap.switchMapType(this.areaLevel)
					},
				},
				{
					iconClass: 'el-icon-search',
					title: '点击搜索地方',
					hide: !this.ssboxShow,
					iconUrl: require('@/assets/img/operate/search-location.png'),

					func: () => {
						if (!this.$store.state.mapLoaded) {
							this.$message.warning('请等待地图加载完成！')
							return
						}
						//点击搜索地方
						this.ssmkQhs()
					},
				},
			]
		},
	},
	methods: {
		//新优化
		async getArgmachTypeList() {
			let params = {
				dataSources: 1,
				areaId: this.areaId == 100000 ? '' : this.areaId,
				// typeLevel: 3,
				// day: dayjs(new Date()).subtract(1, 'days').format("YYYY-MM-DD"),
			}
			let res = await agmachtypeCount(params)
			let amachTypeList_ = []
			if (res.data?.length > 0) {
				res.data.map((item) => {
					if (item.agmachTypeName == '插秧机') {
						item.agmachTypeName = '水稻插秧机'
					}

					if (item.agmachTypeCode !== '000000') {
						amachTypeList_.push({
							label: item.agmachTypeName,
							value: item.agmachTypeCode,
						})
					}
				})
				amachTypeList_.push({ label: '其他', value: '000000' })
			}
			this.argmachTypeList = amachTypeList_

			this.zxnj7CurrentArgmachTypeSelected = this.argmachTypeList.length > 1 ? this.argmachTypeList[0].value : ''
			this.work7CurrenArgmachTypeSelected = this.argmachTypeList.length > 1 ? this.argmachTypeList[0].value : ''

			// //获取农机七日在线数据
			this.handleSearchZxnjNData({ argmachType: this.zxnj7CurrentArgmachTypeSelected, monthType: '1' })
			// //获取农机七日作业数据
			this.handleSearchWorkNData({ argmachType: this.work7CurrenArgmachTypeSelected, monthType: '1' })
		},

		handleSearchZxnjNData(obj) {
			const { argmachType, monthType } = obj
			this.zxnj7CurrentArgmachTypeSelected = argmachType || ''
			this.zxnj7CurrentMonthTypeSelected = monthType || ''
			if (this.zxnj7CurrentArgmachTypeSelected) {
				const zxnjTooltipFilter =
					this.argmachTypeList.filter((item) => {
						if (item.value == this.zxnj7CurrentArgmachTypeSelected) {
							return true
						}
					}) || []

				this.zxnjTooltipTitleName = zxnjTooltipFilter.length > 0 ? zxnjTooltipFilter[0].label : ''
			}
			if (monthType == '1') {
				this.zxnjChartTitle = '七日在线'
			} else if (monthType == '2') {
				this.zxnjChartTitle = '一月在线'
			} else if (monthType == '3') {
				this.zxnjChartTitle = '一年在线'
			}
			let argmachTypeNames = []
			this.argmachTypeList.forEach((item) => {
				if (item.value == this.zxnj7CurrentArgmachTypeSelected) {
					argmachTypeNames.push(item.label)
				}
			})
			// this.zxnj7ChartsLegend = argmachTypeNames
			this.getzxnj7Data()
		},

		handleSearchWorkNData(obj) {
			const { argmachType, monthType } = obj

			this.work7CurrenArgmachTypeSelected = argmachType || ''
			this.work7CurrenMonthTypeSelected = monthType || ''
			if (this.work7CurrenArgmachTypeSelected) {
				const workTooltipFilter =
					this.argmachTypeList.filter((item) => {
						if (item.value == this.work7CurrenArgmachTypeSelected) {
							return true
						}
					}) || []

				this.workTooltipTitleName = workTooltipFilter.length > 0 ? workTooltipFilter[0].label : ''
			}

			if (monthType == '1') {
				this.workChartTitle = '七日作业'
			} else if (monthType == '2') {
				this.workChartTitle = '一月作业'
			} else if (monthType == '3') {
				this.workChartTitle = '一年作业'
			}
			let argmachTypeNames = []
			this.argmachTypeList.forEach((item) => {
				// this.work7CurrenArgmachTypeSelected.forEach((code) => {
				// 	if (item.value == code) {
				// 		argmachTypeNames.push(item.label)
				// 	}
				// })

				if (item.value == this.work7CurrenArgmachTypeSelected) {
					argmachTypeNames.push(item.label)
				}
			})

			// this.work7ChartsLegend = argmachTypeNames
			this.getWork7Data()
		},
		toFixed(number, unit = 10000) {
			if (number === undefined || number === null || isNaN(number)) {
				return 0
			}

			// 将数字除以 10000
			let result = number / unit

			// 如果结果是整数，则返回整数
			if (Number.isInteger(result)) {
				return Number(result)
			} else {
				// 否则，保留两位小数
				return Number(result.toFixed(2))
			}
		},

		//获取当前日期的前一周的日期
		getCurrentDateAndLastWeek() {
			let currentDate = new Date()
			currentDate.setDate(currentDate.getDate() - 1) // 获取当前日期
			let lastWeekDate = new Date(currentDate)
			lastWeekDate.setDate(lastWeekDate.getDate() - 6) // 减去7天获取前一周的日期

			let currentDateFormatted = this.getFormattedDate(currentDate)
			let lastWeekDateFormatted = this.getFormattedDate(lastWeekDate)

			return {
				endDate: currentDateFormatted,
				startDate: lastWeekDateFormatted,
			}
		},
		//获取当前日期的前一月的日期
		getCurrentDateAndLastMonth() {
			// 获取当前日期
			const currentDate = new Date()

			// 获取前一天的日期
			const previousDay = new Date(currentDate)
			previousDay.setDate(currentDate.getDate() - 1)

			// 获取前一天日期的前一月
			const previousMonthDate = new Date(previousDay)
			previousMonthDate.setMonth(previousDay.getMonth() - 1)

			let currentDateFormatted = this.getFormattedDate(previousDay)
			let lastMonthDateFormatted = this.getFormattedDate(previousMonthDate)

			return {
				endDate: currentDateFormatted,
				startDate: lastMonthDateFormatted,
			}
		},
		//获取当前日期的前一年的日期
		getCurrentDateAndLastYear() {
			// 获取当前日期
			const currentDate = new Date()

			// 获取前一天的日期
			const previousDay = new Date(currentDate)
			previousDay.setDate(currentDate.getDate() - 1)

			// 获取前一天日期的前一年
			const previousYearDate = new Date(previousDay)
			previousYearDate.setFullYear(previousDay.getFullYear() - 1)

			let currentDateFormatted = this.getFormattedDate(previousDay)
			let lastYearDateFormatted = this.getFormattedDate(previousYearDate)

			return {
				endDate: currentDateFormatted,
				startDate: lastYearDateFormatted,
			}
		},
		getFormattedDate(date) {
			let year = date
				.getFullYear()
				.toString()
				.padStart(4, '0')
			let month = (date.getMonth() + 1).toString().padStart(2, '0') // 注意月份是从0开始的，所以要+1
			let day = date
				.getDate()
				.toString()
				.padStart(2, '0')
			return `${year}-${month}-${day}`
		},

		async getzxnj7Data() {
			const currentDateObj =
				this.zxnj7CurrentMonthTypeSelected == '1'
					? this.getCurrentDateAndLastWeek()
					: this.zxnj7CurrentMonthTypeSelected == '2'
					? this.getCurrentDateAndLastMonth()
					: this.zxnj7CurrentMonthTypeSelected == '3'
					? this.getCurrentDateAndLastYear()
					: { currentDate: '', lastWeekDate: '' }

			const { startDate, endDate } = currentDateObj

			const params = {
				startDate: startDate,
				endDate: endDate,
				areaId: this.areaId == 100000 ? '' : this.areaId,
				level: this.areaLevel == 0 ? '-1' : this.areaLevel,
				agmachTypeCode: this.zxnj7CurrentArgmachTypeSelected,
			}

			try {
				let res = await api_getZxnjNData(params)
				console.log('api_getZxnjNDataRes--==', res)
				const data_ = res.data

				this.zxnj7XData = []
				this.zxnj7YDatas = {}
				let zxnj7YDatas_ = {}
				if (data_.length > 0) {
					let temp = []
					data_.forEach((item) => {
						let date_ = ''
						const dateSplitArray = item.date.split('-')
						if (dateSplitArray.length > 2) {
							date_ = `${dateSplitArray[0]}-${dateSplitArray[1]}-${dateSplitArray[2]}`
						}
						this.zxnj7XData.push(date_)
						temp.push(this.areaLevel == 0 || this.areaLevel == 1 ? this.toFixed(item.count) : item.count)
					})
					try {
						const legendName = this.argmachTypeList.filter((item) => {
							if (item.value == this.zxnj7CurrentArgmachTypeSelected) {
								return true
							}
						})[0].label

						zxnj7YDatas_[legendName] = temp
					} catch {
						const legendName = '数量'

						zxnj7YDatas_[legendName] = temp
					}
					this.zxnj7YDatas = zxnj7YDatas_
				}
			} catch (error) {
				this.zxnj7XData = []
				this.zxnj7YDatas = {}
			}
		},

		async getWork7Data() {
			const currentDateObj =
				this.work7CurrenMonthTypeSelected == '1'
					? this.getCurrentDateAndLastWeek()
					: this.work7CurrenMonthTypeSelected == '2'
					? this.getCurrentDateAndLastMonth()
					: this.work7CurrenMonthTypeSelected == '3'
					? this.getCurrentDateAndLastYear()
					: { currentDate: '', lastWeekDate: '' }

			const { startDate, endDate } = currentDateObj

			const params = {
				// groupLevel: 3,
				// areaId: this.areaId == 100000 ? '' : this.areaId,
				// startDate: startDate,
				// endDate: endDate,
				agmachTypeCode: this.work7CurrenArgmachTypeSelected,
				startDate: startDate,
				endDate: endDate,
				areaId: !this.areaId || this.areaId == '100000' ? '' : this.areaId,
				level: this.areaLevel == 0 ? '-1' : this.areaLevel,
			}
			let res = await api_getWorkNData(params)
			this.work7XData = []
			this.work7YDatas = {}
			let work7YDatas_ = {}
			const data_ = res.data
			if (data_.length > 0) {
				let temp = []
				data_.forEach((item) => {
					let date_ = ''
					const dateSplitArray = item.date.split('-')
					if (dateSplitArray.length > 2) {
						date_ = `${dateSplitArray[0]}-${dateSplitArray[1]}-${dateSplitArray[2]}`
					}
					this.work7XData.push(date_)
					temp.push(this.areaLevel == 0 || this.areaLevel == 1 ? this.toFixed(item.count) : item.count)
				})
				try {
					const legendName = this.argmachTypeList.filter((item) => {
						if (item.value == this.zxnj7CurrentArgmachTypeSelected) {
							return true
						}
					})[0].label

					work7YDatas_[legendName] = temp
				} catch {
					const legendName = '数量'

					work7YDatas_[legendName] = temp
				}
				this.work7YDatas = work7YDatas_
			}
		},

		async getArgmachByRegionData(currentClickArgmachType, type) {
			console.log('获取该农机类型在各省的农机数据--==', this.areaLevel)

			if (this.areaLevel == 3) {
				this.argmachByProvinceXData = []
				this.argmachByProvinceYData = {}
			}
			if (type == 'currentWork') {
				//获取当日作业的各区域农机数据
				const params = {
					agmachTypeCode: currentClickArgmachType,
					parentAreaId: !this.areaId || this.areaId == '100000' ? '' : this.areaId,
				}

				const res = await api_getCurrentWorkArgmachByRegionData(params)
				const data_ = res.data

				if (data_.length > 0) {
					const data__ = data_.sort((a, b) => b.agmachNum - a.agmachNum)
					this.argmachByProvinceXData = []
					this.argmachByProvinceYData = {}
					let argmachByProvinceYData_ = []

					data__.forEach((item) => {
						this.argmachByProvinceXData.push(item.areaName)
						argmachByProvinceYData_.push(this.areaLevel == 0 || this.areaLevel == 1 ? this.toFixed(item.agmachNum) : item.agmachNum)
					})
					this.argmachByProvinceYData['农机数据'] = argmachByProvinceYData_
				}
			} else if (type == 'crossWork') {
				//获取跨区作业的各区域农机数据
				const params = {
					agmachTypeCode: currentClickArgmachType,
					parentAreaId: !this.areaId || this.areaId == '100000' ? '' : this.areaId,
				}

				const res = await api_getCrossWorArgmachByRegionData(params)
				const data_ = res.data
				if (data_.length > 0) {
					const data__ = data_.sort((a, b) => b.agmachNum - a.agmachNum)
					this.argmachByProvinceXData = []
					this.argmachByProvinceYData = {}
					let argmachByProvinceYData_ = []

					data__.forEach((item) => {
						this.argmachByProvinceXData.push(item.areaName)
						argmachByProvinceYData_.push(this.areaLevel == 0 || this.areaLevel == 1 ? this.toFixed(item.agmachNum) : item.agmachNum)
					})
					this.argmachByProvinceYData['农机数据'] = argmachByProvinceYData_
				}
			} else if (type == 'zxnj') {
				//获取网联农机的各区域农机数据
				const params = {
					level: this.areaLevel == 0 ? -1 : this.areaLevel,
					agmachTypeCode: currentClickArgmachType,
					parentAreaId: !this.areaId || this.areaId == '100000' ? '' : this.areaId,
				}

				const res = await api_getInternetByRegionData(params)
				if (res.data.length > 0) {
					const data_ = res.data
					if (data_.length > 0) {
						const data__ = data_.sort((a, b) => b.agmachCount - a.agmachCount)
						this.argmachByProvinceXData = []
						this.argmachByProvinceYData = {}
						let argmachByProvinceYData_ = []

						data__.forEach((item) => {
							this.argmachByProvinceXData.push(item.areaName)
							argmachByProvinceYData_.push(
								this.areaLevel == 0 || this.areaLevel == 1 ? this.toFixed(item.agmachCount) : item.agmachCount,
							)
						})
						this.argmachByProvinceYData['农机数据'] = argmachByProvinceYData_
					}
				}
			}
		},
		// 搜索地方
		ssqy() {
			this.ssList = []
			if (this.ssnr) {
				this.sspopList = true
				this.queryByList(this.ssnr)
			} else {
				this.sspopList = false
			}
		},
		async queryByList() {
			this.ssList = []
			let res = await queryByList({
				areaName: this.ssnr,
			})
			if (res.code == 0) {
				this.sspopList = true
				this.ssList = []
				this.ssList = res.data
			} else if (res.code == -1) {
				this.sspopList = false
				this.ssList = []
				this.$message({
					message: res.msg,
					type: 'warning',
				})
			}
		},
		ssxz(item) {
			this.areaLevel = item.level
			this.areaId = item.areaId
			this.$store.commit('invokerightShow5', item)
			if (this.$refs.leafletMap.lastAreaCode.indexOf(item.areaId) == -1) {
				this.$refs.leafletMap.lastAreaCode.push(item.areaId)
			}
			if (item.level == '3') {
				//区县级别
				setTimeout(() => {
					this.areaLevel = 3
					this.$refs.leafletMap.isChangeMapBg = true
					this.$refs.leafletMap.currentMapLevel = 'district'
					this.$refs.leafletMap.loadGeoJsonLayerFromUrl(
						`https://geo.datav.aliyun.com/areas_v3/bound/${item.areaId}.json`,
						'geojson',
						false,
					)
					// this.$refs.leafletMap.loadGeoJsonLayerFromUrl(`https://dpzs.camtc.cn/areas_v3/bound/${item.areaId}.json`, 'geojson', false)
				}, 300)
			} else {
				setTimeout(() => {
					this.$refs.leafletMap.isChangeMapBg = true
					this.$refs.leafletMap.loadGeoJsonLayerFromUrl(
						`https://geo.datav.aliyun.com/areas_v3/bound/${item.areaId}_full.json`,
						'geojson',
						item.level == 0 ? true : false,
					)
					// this.$refs.leafletMap.loadGeoJsonLayerFromUrl(`https://dpzs.camtc.cn/areas_v3/bound/${item.areaId}_full.json`, 'geojson', item.level == 0 ? true : false)
				}, 300)
			}
			this.removeAllPoi()
			if (this.areaLevel != 2 && this.areaLevel != 3) {
				this.contentNumberUnitOne = '万台'
			} else {
				this.contentNumberUnitOne = '台'
			}
			this.activaIdx = -1
			this.mapShow = -1
			this.getInitData()
			this.ssboxShow = false
			this.sspopShow = false
			this.sspopList = false
			this.ssnr = ''
			this.ssList = []
		},
		async njlxqh(agmachTypeId) {
			if (!this.$store.state.mapLoaded) {
				this.$message.warning('请等待地图加载完成！')
				return
			}
			this.njfbShow = false
			if (agmachTypeId == -1) {
				return
			}
			this.removeAllPoi()

			if (this.areaLevel == 0) {
				this.showWorkHeat = true
			} else {
				this.showWorkHeat = false
			}
			if (this.sameMapShow) {
				//判断本次的绘制分类参数是否和上次的一致，若一致则清除掉上次的绘制
				if (this.resourceTypes == agmachTypeId) {
					this.resourceTypes = -1
					this.removeAllPoi()
					this.currentDrawClass = ''
					this.$refs.leafletMap.showHeat = false
					this.mapShow = -1
					this.$refs.leafletMap.redrawGeoJSON()
				} else {
					this.resourceTypes = agmachTypeId
					//若绘制的是农机分布则执行
					if (this.mapShow == 3) {
						this.agmachDistribution('njfbType')
					} else if (this.mapShow == 4) {
						this.agmachDistribution('zxnjType')
					} else if (this.mapShow == 7 && this.showWorkHeat) {
						this.workHeat()
					}
				}
			} else {
				this.resourceTypes = agmachTypeId
				if (this.mapShow == 3) {
					this.agmachDistribution('njfbType')
				} else if (this.mapShow == 4) {
					this.agmachDistribution('zxnjType')
				} else if (this.mapShow == 7 && this.showWorkHeat) {
					this.workHeat()
				}
			}
		},

		async agmachDistribution(drawClass) {
			this.currentDrawClass = drawClass
			//区县级不绘制热力图和大数据量聚合，仅绘制农机坐标点，区县级以外绘制热力图和大数据量聚合
			//若当前的areaLevel是区县级
			if (this.areaLevel >= 2) {
				if (drawClass == 'njfbType') {
					this.getDistributionCount(this.resourceTypes, drawClass)
				} else {
					this.identification(drawClass)
				}
			} else {
				this.getDistributionCount(this.resourceTypes, drawClass)
			}
		},
		mapActive(index) {
			// if (this.mapShow != index) {
			//   this.removeAllPoi()
			// }
			if (!this.$store.state.mapLoaded) {
				this.$message.warning('请等待地图加载完成！')
				return
			}

			if (this.mapShow == index && this.njfbShow) {
				this.njfbShow = false
				this.mapShow = -1
				return
			}

			if (this.mapShow == index) {
				this.sameMapShow = true
			} else {
				this.sameMapShow = false
			}
			this.mapShow = index

			if (this.mapShow == 3) {
				this.njfbShow = true
				this.activaIdx = -1
			}
			if (this.mapShow == 4) {
				this.njfbShow = true
				this.activaIdx = -1
			}

			if (this.mapShow == 7) {
				this.njfbShow = true
				this.activaIdx = -1
			}

			if (this.activaIdx == index) {
				this.activaIdx = -1
				this.removeAllPoi()
				if (this.$refs.leafletMap.polygon) {
					this.$refs.leafletMap.polygon.remove()
				}
			} else {
				this.activaIdx = index
			}
			// this.openFromVideoDialog = false
		},

		//农场监控
		// openFormVideo() {
		// this.removeAllPoi()
		// this.openFromVideoDialog = !this.openFromVideoDialog
		// if (this.openFromVideoDialog) {
		// 	this.mapShow = -1
		// 	this.resourceTypes = -1
		// }
		// this.$refs.leafletMap.drawPoiMarkerCustomCluster(
		// 	[
		// 		{
		// 			latlng: ['33.866940', '115.229080'],
		// 			icon: {
		// 				iconUrl: require('@/assets/mskx/icon_shhfwzx_b.png'),
		// 				iconSize: [48, 62],
		// 				iconAnchor: [16, 42],
		// 			},
		// 			props: {
		// 				id: 'ncjkId',
		// 				type: 'ncjkType',
		// 				info: { deviceSerial: 'FX6180183' },
		// 			},
		// 		},
		// 	],
		// 	'ncjkType',
		// 	true,
		// 	false,
		// 	false,
		// 	{
		// 		largeIconUrl: './imgs/cluster/qy1.png',
		// 		mediumIconUrl: './imgs/cluster/qy2.png',
		// 		smallIconUrl: './imgs/cluster/qy3.png',
		// 	},
		// )
		// },

		async workHeat() {
			let res = await distributionhour({
				// time: this.dayjs()
				// 	.subtract(1, 'days')
				// 	.format('YYYY-MM-DD hh:mm:ss'),
				time: '2025-04-25 12:00:00',
				level: 2,
				areaId: this.areaId == '100000' ? '' : this.areaId,
				agmachTypeCode: !this.resourceTypes || this.resourceTypes == 100000 || this.resourceTypes == -1 ? '' : this.resourceTypes,
			})
			let data = res.data.filter((item) => {
				return Number(item.agmachNum) > 0
			})
			this.$refs.leafletMap.loadHeatLayer('heat', data)
			this.$store.commit('updateMapLoaded', true)
		},

		// 当前层级非全国级、省、市级，即区县级
		//#region
		// async identification(drawClass) {
		// 	let data = []
		// 	let clusterData = []
		// 	if (drawClass == 'njfbType') {
		// 		let res = await agmachtypeLocList({
		// 			areaId: this.areaId == 100000 ? '' : this.areaId,
		// 			agmachTypeId: !this.resourceTypes || this.resourceTypes == 100000 || this.resourceTypes == -1 ? '' : this.resourceTypes,
		// 		})

		// 		data = res.data.map((item) => {
		// 			return {
		// 				latlng: [item.lat, item.lon],
		// 				icon: {
		// 					iconUrl: require('@/assets/mskx/icon_nj_b.png'),
		// 					iconSize: [50, 60],
		// 					iconAnchor: [25, 30],
		// 				},

		// 				props: {
		// 					id: 'agmachId',
		// 					type: 'njfbType',
		// 					info: { ...item },
		// 				},
		// 			}
		// 		})

		// 		clusterData = [].concat(
		// 			res.data.map((item) => {
		// 				// return [item.agmachId || '', '-1', '-1', item.lon, item.lat]
		// 				return {
		// 					latlng: [item.lat, item.lon],
		// 					icon: {
		// 						iconUrl: require('@/assets/mskx/icon_nj_b.png'),
		// 						iconSize: [48, 62],
		// 						iconAnchor: [16, 42],
		// 					},

		// 					props: {
		// 						id: 'agmachId',
		// 						type: 'njfbType',
		// 						info: { ...item },
		// 					},
		// 				}
		// 			}),
		// 		)
		// 		this.$refs.leafletMap.removeLayer('zxnjType')
		// 	} else if (drawClass == 'zxnjType') {
		// 		let res = await identification({
		// 			areaId: this.areaId == '100000' ? '' : this.areaId,
		// 			dataSources: 1,
		// 			agmachTypeId: this.resourceTypes == '100000' || this.resourceTypes == '-1' ? '' : this.resourceTypes,
		// 		})
		// 		data = res.data.map((item) => {
		// 			return {
		// 				latlng: [item.lat, item.lon],
		// 				icon: {
		// 					iconUrl: require('@/assets/mskx/icon_nj_b.png'),
		// 					iconSize: [50, 60],
		// 					iconAnchor: [25, 30],
		// 				},

		// 				props: {
		// 					id: 'agmachId',
		// 					type: 'zxnjType',
		// 					info: { ...item },
		// 				},
		// 			}
		// 		})
		// 		clusterData = [].concat(
		// 			res.data.map((item) => {
		// 				// return [item.agmachId || '', '-1', '-1', item.lon, item.lat]
		// 				return {
		// 					latlng: [item.lat, item.lon],
		// 					icon: {
		// 						iconUrl: require('@/assets/mskx/icon_nj_b.png'),
		// 						iconSize: [48, 62],
		// 						iconAnchor: [16, 42],
		// 					},

		// 					props: {
		// 						id: 'agmachId',
		// 						type: 'zxnjType',
		// 						info: { ...item },
		// 					},
		// 				}
		// 			}),
		// 		)

		// 		// 需要清除农机分布的地图渲染数据
		// 		this.$refs.leafletMap.removeLayer('njfbType')
		// 	} else {
		// 		return
		// 	}
		// 	//绘制散点
		// 	if (drawClass == 'njfbType') {
		// 		this.$refs.leafletMap.drawSstsBigDataClusterPoint(
		// 			clusterData,
		// 			require('@/assets/mskx/icon_nj_b.png'),
		// 			[48, 62],
		// 			{
		// 				largeIconUrl: './imgs/cluster/nj1.png',
		// 				mediumIconUrl: './imgs/cluster/nj2.png',
		// 				smallIconUrl: './imgs/cluster/nj3.png',
		// 			},
		// 			drawClass,
		// 		)
		// 	} else {
		// 		// 绘制聚合点
		// 		if (this.areaLevel == 3) {
		// 			this.$refs.leafletMap.drawSstsPoiMarker(data, drawClass, false)
		// 		} else {
		// 			this.$refs.leafletMap.drawSstsBigDataClusterPoint(
		// 				clusterData,
		// 				require('@/assets/mskx/icon_nj_b.png'),
		// 				[48, 62],
		// 				{
		// 					largeIconUrl: './imgs/cluster/nj1.png',
		// 					mediumIconUrl: './imgs/cluster/nj2.png',
		// 					smallIconUrl: './imgs/cluster/nj3.png',
		// 				},
		// 				drawClass,
		// 			)
		// 		}
		// 	}
		// },
		//#endregion

		async identification(drawClass) {
			let data = []
			let clusterData = []
			let res = null
			if (drawClass == 'njfbType') {
				res = await agmachtypeLocList({
					areaId: this.areaId == 100000 ? '' : this.areaId,
					agmachTypeId: !this.resourceTypes || this.resourceTypes == 100000 || this.resourceTypes == -1 ? '' : this.resourceTypes,
				})
				this.$refs.leafletMap.removeLayer('zxnjType')
			} else if (drawClass == 'zxnjType') {
				res = await identification({
					areaId: this.areaId == 100000 ? '' : this.areaId,
					dataSources: 1,
					agmachTypeId: !this.resourceTypes || this.resourceTypes == 100000 || this.resourceTypes == -1 ? '' : this.resourceTypes,
				})

				// 需要清除农机分布的地图渲染数据
				this.$refs.leafletMap.removeLayer('njfbType')
			} else {
				return
			}
			if (res) {
				data = res.data.map((item) => {
					return {
						latlng: [item.lat, item.lon],
						icon: {
							// iconUrl: require('@/assets/mskx/icon_nj_b.png'),
							// iconSize: [50, 60],
							// iconAnchor: [25, 30],

							iconUrl: require('@/assets/mskx/current-online-icon.png'),

							// iconSize: [50, 60],
							// iconAnchor: [25, 30],
							iconSize: [8, 8],
							iconAnchor: [5, 6],
						},

						props: {
							id: 'agmachId',
							type: drawClass,
							info: { ...item },
						},
					}
				})

				// data.push({
				// 	latlng: [31.230195, 121.474192], //上海经纬度
				// 	icon: {
				// 		// iconUrl: require('@/assets/mskx/icon_nj_b.png'),
				// 		// iconSize: [50, 60],
				// 		// iconAnchor: [25, 30],

				// 		iconUrl: require('@/assets/mskx/current-online-icon.png'),

				// 		// iconSize: [50, 60],
				// 		// iconAnchor: [25, 30],

				// 		iconSize: [8, 8],
				// 		iconAnchor: [5, 6],
				// 	},

				// 	props: {
				// 		id: 'agmachId',
				// 		type: drawClass,
				// 		info: {},
				// 	},
				// })
			}

			//绘制散点
			this.$refs.leafletMap.drawSstsPoiMarker(data, drawClass, false).then(() => {
				this.$store.commit('updateMapLoaded', true)
				setTimeout(() => {
					this.$refs.leafletMap.map.fitBounds(this.$refs.leafletMap.geojsonLayer.getBounds())
				}, 500)
			})
		},

		//获取流入流出数据
		handleChangeTab(val) {
			if (this.areaLevel !== 0) {
				this.crossWorkTotal = 0
				this.crossWorkPercent = 0
				this.crossWorkDataList = []
				this.currentTabValue = val
				const params = {
					areaId: this.areaId,
					status: val == 'in' ? 0 : 1,
				}
				this.getCrossWorkDataByInOrOut(params)
			}
		},
		async getTopDescripteDataList() {
			console.log('执行了！！')
			// const res = api_getSstsTopDescriptionData({areaId: this.areaId})
			this.topDescripteDataList = [
				// { title: '今日在线', number: this.areaLevel == 0 || this.areaLevel == 1 ? 2.1 : 21000, unitName: '万台' },
				// { title: '今日作业', number: this.areaLevel == 0 || this.areaLevel == 1 ? 1.6 : 16000, unitName: '万台' },
				// { title: '昨日在线', number: this.areaLevel == 0 || this.areaLevel == 1 ? 2.3 : 23000, unitName: '万台' },
				// { title: '昨日作业', number: this.areaLevel == 0 || this.areaLevel == 1 ? 1.3 : 13000, unitName: '万台' },
				{ title: '今日在线', number: 0, unitName: this.areaLevel != 2 && this.areaLevel != 3 ? '万台' : '台', toolTip: '' },
				{ title: '今日作业', number: 0, unitName: this.areaLevel != 2 && this.areaLevel != 3 ? '万台' : '台', toolTip: '' },
				{ title: '昨日在线', number: 0, unitName: this.areaLevel != 2 && this.areaLevel != 3 ? '万台' : '台', toolTip: '' },
				{ title: '昨日作业', number: 0, unitName: this.areaLevel != 2 && this.areaLevel != 3 ? '万台' : '台', toolTip: '' },
			]
			const currentOnlineDataRes = await getNjTodayCount({ areaId: this.areaId == 100000 ? '' : this.areaId })
			if (this.areaLevel == 0 || this.areaLevel == 1) {
				this.topDescripteDataList[0].number = Number(this.toFixed(currentOnlineDataRes.data[0].count))
			} else {
				this.topDescripteDataList[0].number = Number(currentOnlineDataRes.data[0].count)
			}
			this.topDescripteDataList[0].toolTip = String(this.topDescripteDataList[0].number)

			const currentWorkStasticRes = await api_getCurrentWorkStatisticData({ areaId: this.areaId == 100000 ? '' : this.areaId })
			if (currentWorkStasticRes.data.length > 0) {
				if (this.areaLevel == 0 || this.areaLevel == 1) {
					this.topDescripteDataList[1].number = Number(this.toFixed(currentWorkStasticRes.data[0].agmachNum))
				} else {
					this.topDescripteDataList[1].number = Number(currentWorkStasticRes.data[0].agmachNum)
				}
			}

			this.topDescripteDataList[1].toolTip = String(this.topDescripteDataList[1].number)

			let params = {
				startDate: dayjs(new Date())
					.subtract(1, 'days')
					.format('YYYY-MM-DD'),
				endDate: dayjs(new Date())
					.subtract(1, 'days')
					.format('YYYY-MM-DD'),
				areaId: this.areaId == 100000 ? '' : this.areaId,
				level: this.areaLevel == 0 ? -1 : this.areaLevel,
			}
			let yestardayOnlineDataRes = await getYesterdayNjOnlineCount(params)
			if (this.areaLevel == 0 || this.areaLevel == 1) {
				this.topDescripteDataList[2].number = Number(this.toFixed(yestardayOnlineDataRes.data[0].count))
			} else {
				this.topDescripteDataList[2].number = Number(yestardayOnlineDataRes.data[0].count)
			}
			this.topDescripteDataList[2].toolTip = String(this.topDescripteDataList[2].number)

			let yestardayWorkDataRes = await getYesterdayNjCurrent7Count(params)
			if (this.areaLevel == 0 || this.areaLevel == 1) {
				this.topDescripteDataList[3].number = Number(this.toFixed(yestardayWorkDataRes.data[0].count))
			} else {
				this.topDescripteDataList[3].number = Number(yestardayWorkDataRes.data[0].count)
			}
			this.topDescripteDataList[3].toolTip = String(this.topDescripteDataList[3].number)

			// const res = await api_getTopDescripteDataList()
		},
		async getCrossWorkDataByInOrOut(params) {
			const crossWorkStasticRes = await api_getCrossWorkStatisticMachine(params)
			const crossWorkRes = await api_getCrossWorkMachine(params)
			//----------------获取跨区作业农机统计------------------
			if (crossWorkStasticRes.data.length > 0) {
				const data_ = crossWorkStasticRes.data[0]
				if (this.areaLevel == 0 || this.areaLevel == 1) {
					this.crossWorkTotal = Number(this.toFixed(data_.agmachNum))
				} else {
					this.crossWorkTotal = Number(data_.agmachNum)
				}
				this.crossWorkPercent = data_.rate
			}
			//获取跨区作业农机数量
			if (crossWorkRes.data.length > 0) {
				const data_ = crossWorkRes.data
				this.crossWorkDataList = data_.map((item) => {
					let icon = ''
					// 					agmach_type_id
					// agmach_type_name
					switch (item.agmach_type_id) {
						case '510101':
							//轮式拖拉机
							icon = require('@/assets/img/block-box/lstlj-icon.png')
							break
						case '150105':
							icon = require('@/assets/img/block-box/gwlhsgj-icon.png')
							break
						case '150106':
							icon = require('@/assets/img/block-box/ymshj-icon.png')
							break
						case '150107':
							icon = require('@/assets/img/block-box/bzj-icon.png')
							break
						case '120401':
							icon = require('@/assets/img/block-box/cyj-icon.png')
							break
						default:
							icon = require('@/assets/img/block-box/other-argmach-icon.png')
							break
					}
					return {
						title: item.agmach_type_name,
						number: this.areaLevel == 0 || this.areaLevel == 1 ? this.toFixed(item.agmachNum) : item.agmachNum,
						iconUrl: icon,
						argmachType: item.agmach_type_id,
						func: (argmachType) => {
							//获取该农机类型在各省的农机数据
							this.getArgmachByRegionData(argmachType, 'crossWork')
						},
					}
				})
			}
		},
		/* 单位w */
		exchangeUnit(num, fixedNum = 2) {
			num = Number(num)
			if (num >= 10000) {
				var digit = num / 10000
				var count = digit.toFixed(2)
				count = Math.max(count)
				return count
			} else {
				return num
			}
		},
		//获取农机分布/在线农机的聚合和热力图数据
		async getDistributionCount(agmachTypeCode, drawClass) {
			let res = null
			try {
				res =
					drawClass == 'njfbType'
						? this.areaLevel == 0
							? await getDistributionCount({
									level: this.areaLevel == 0 ? '-1' : this.areaLevel,
									parentAreaId: this.areaId == '100000' ? '' : this.areaId,
									agmachTypeCode: agmachTypeCode == '100000' || agmachTypeCode == '-1' ? '' : agmachTypeCode,
							  })
							: await agmachtypeLocList({
									areaId: this.areaId == 100000 ? '' : this.areaId,
									agmachTypeId:
										!this.resourceTypes || this.resourceTypes == 100000 || this.resourceTypes == -1 ? '' : this.resourceTypes,
							  })
						: drawClass == 'zxnjType'
						? await identification({
								areaId: this.areaId == '100000' ? '' : this.areaId,
								dataSources: 1,
								agmachTypeId:
									!this.resourceTypes || this.resourceTypes == 100000 || this.resourceTypes == -1 ? '' : this.resourceTypes,
						  })
						: null
			} catch {
				res = {
					data: [],
				}
			}

			let numData = []
			let heatData = []

			if (res.data.length == 0) {
				// this.$refs.leafletMap.map.fitBounds(this.$refs.leafletMap.geojsonLayer.getBounds())
				this.$store.commit('updateMapLoaded', true)

				setTimeout(() => {
					this.$refs.leafletMap.map.setView(this.$refs.leafletMap.mapOptions.center, this.$refs.leafletMap.mapOptions.zoom)
				}, 500)

				return
			} else {
				if (drawClass == 'njfbType') {
					numData =
						this.areaLevel == 0
							? [].concat(
									res.data.map((item) => {
										return {
											latlng: [item.lat, item.lon],
											props: {
												name: item.areaName,
												num: this.getFormateAgmachCount(item.agmachCount),
												adcode: item.areaId,
												level: 'province',
											},
										}
									}),
							  )
							: [].concat(
									res.data.map((item) => {
										return {
											latlng: [item.lat, item.lon],
											props: {
												id: 'agmachId',
												type: drawClass,
												info: { ...item, agmachId: item.agmach_id },
											},
										}
									}),
							  )
					if (this.areaLevel == 0) {
						this.$refs.leafletMap.drawSstsNumberMarker(numData, drawClass).then(() => {
							this.$store.commit('updateMapLoaded', true)

							setTimeout(() => {
								this.$refs.leafletMap.map.setView(this.$refs.leafletMap.mapOptions.center, this.$refs.leafletMap.mapOptions.zoom)
							}, 500)
						})
					} else {
						heatData = [].concat(
							res.data.map((item) => {
								return {
									latitude: item.lat,
									longitude: item.lon,
								}
							}),
						)
						this.$refs.leafletMap
							.drawSstsBigDataClusterPoint(
								numData,
								// require('@/assets/mskx/icon_nj_b.png'),
								// [48, 62],
								require('@/assets/mskx/current-online-icon.png'),
								[8, 8],
								false,
								drawClass,
							)
							.then(() => {
								this.$store.commit('updateMapLoaded', true)

								setTimeout(() => {
									this.$refs.leafletMap.map.fitBounds(this.$refs.leafletMap.geojsonLayer.getBounds())
								}, 500)
							})
						if (this.areaLevel !== 3) {
							this.$refs.leafletMap.loadHeatLayer('heat', heatData)
						}
					}
				} else {
					numData = [].concat(
						res.data.map((item) => {
							return {
								latlng: [item.lat, item.lon],
								props: {
									id: 'agmachId',
									type: drawClass,
									info: { ...item },
								},
							}
						}),
					)
					heatData = [].concat(
						res.data.map((item) => {
							return {
								latitude: item.lat,
								longitude: item.lon,
							}
						}),
					)
					this.$refs.leafletMap
						.drawSstsBigDataClusterPoint(
							numData,
							// require('@/assets/mskx/icon_nj_b.png'),
							// [48, 62],
							require('@/assets/mskx/current-online-icon.png'),
							[8, 8],
							false,
							drawClass,
						)
						.then(() => {
							this.$store.commit('updateMapLoaded', true)

							setTimeout(() => {
								if (this.areaLevel == 0) {
									this.$refs.leafletMap.map.setView(this.$refs.leafletMap.mapOptions.center, this.$refs.leafletMap.mapOptions.zoom)
								} else {
									this.$refs.leafletMap.map.fitBounds(this.$refs.leafletMap.geojsonLayer.getBounds())
								}
							}, 500)
						})
					this.$refs.leafletMap.loadHeatLayer('heat', heatData)
				}

				// this.$refs.leafletMap.drawSstsNumberMarker(numData, drawClass)
			}
		},
		getFormateAgmachCount(num) {
			if (num / 1000 > 0) {
				return (num / 1000).toFixed(1) + 'k'
			} else {
				return num
			}
		},
		//打开/关闭图例
		njtlTk() {
			this.isnjtlListShow = !this.isnjtlListShow
		},
		//刷新内容
		sxTk() {
			this.getInitData()
		},
		//打开/关闭搜索地方的弹窗
		ssmkQhs() {
			this.sspopShow = !this.sspopShow
			this.sspopList = false
			this.ssList = []
			this.ssnr = ''
		},
		qctc() {
			this.activaIdx = -1
			this.mapShow = -1
		},

		//点击作业轨迹打开轨迹弹窗
		eventInfoBtnClick15(i, form) {
			this.getAgmachGuiJiInfo(
				{
					agmachId: form.agmachId,
					startTime: form.workStartTime,
					endTime: form.workEndTime,
				},
				form,
			)
		},
		//页面全屏
		async mkssBtn() {
			this.mkdx = !this.mkdx
			await this.$store.commit('invokerightShow4', this.mkdx)
			if (!this.mkdx) {
				// this.mapBtnBottom = 260
				this.mapBtnRight = 507
				this.operateBoxBottom = 289
				// this.operateBoxRight = 637
			} else {
				this.mapBtnRight = 10
				this.operateBoxBottom = 10
			}

			// this.$store.commit('invokerightShow', !this.mkdx)
		},
		//调用作业轨迹的接口
		async getAgmachGuiJiInfo(data, infoData) {
			let res = await getAgmachGuiJiInfo(data)
			if (res?.code == '0') {
				// this.workPopShow = false
				let trackData = []
				trackData = [].concat(res.data.map((it) => [it.lon, it.lat]))
				// this.$refs.leafletMap.loadTrackLayer('trcak', trackData)
				this.isMapShow = true
				this.$nextTick(() => {
					this.$refs.mapPop.track(trackData)
					this.$refs.mapPop.getNjInfo(infoData)
				})
			}
		},

		async getAgmachRealTimeGuiJiInfo(data, infoData) {
			let res = await getAgmachRealTimeGuiJiInfo(data)
			if (res?.code == '0') {
				// this.workPopShow = false
				let trackData = []
				trackData = [].concat(res.data.map((it) => [it.lon, it.lat]))
				this.startRunTime = res.data.length > 0 ? res.data[0].locTime : '-'

				// this.$refs.leafletMap.loadTrackLayer('trcak', trackData)
				this.isRealTimeMapShow = true
				this.$nextTick(() => {
					this.$refs.mapRealTimePop.track(trackData)
					this.$refs.mapRealTimePop.getNjInfo(infoData)
				})
			}
		},

		async cscpCurrentUserDetails(data) {
			let res = await cscpCurrentUserDetails(data)
			if (res?.code == '0') {
				this.roleNames = res.data.roleNames
				if (res.data.areaId) {
					this.areaId = res.data.areaId == '0' ? 100000 : res.data.areaId
					this.areaLevel_init = res.data.areaLevel
					this.areaLevel = res.data.areaLevel
					this.$refs.leafletMap.lastAreaCode.push(Number(res.data.areaId))
					this.lastAreaLevel.push(res.data.areaLevel)
					if (res.data.areaId != res.data.areaIdNew) {
						//区县级别
						setTimeout(() => {
							this.areaLevel = 3
							this.$refs.leafletMap.isChangeMapBg = true
							this.$refs.leafletMap.currentMapLevel = 'district'

							this.$refs.leafletMap.loadGeoJsonLayerFromUrl(
								`https://geo.datav.aliyun.com/areas_v3/bound/${res.data.areaId}.json`,
								'geojson',
								false,
							)
							// this.$refs.leafletMap.loadGeoJsonLayerFromUrl(`https://dpzs.camtc.cn/areas_v3/bound/${res.data.areaId}.json`, 'geojson', false)
						}, 300)

						setTimeout(() => {
							this.njlxqh(100000)
						}, 500)
					} else {
						setTimeout(() => {
							this.$refs.leafletMap.isChangeMapBg = true
							this.$refs.leafletMap.loadGeoJsonLayerFromUrl(
								`https://geo.datav.aliyun.com/areas_v3/bound/${res.data.areaId}_full.json`,
								'geojson',
								res.data.areaLevel == 0 ? true : false,
							)
							// this.$refs.leafletMap.loadGeoJsonLayerFromUrl(`https://dpzs.camtc.cn/areas_v3/bound/${res.data.areaId}_full.json`, 'geojson', res.data.areaLevel == 0 ? true : false)
						}, 300)

						setTimeout(() => {
							if (this.areaLevel != 2 && this.areaLevel != 3) {
								this.contentNumberUnitOne = '万台'
							} else {
								this.contentNumberUnitOne = '台'
							}
							this.njlxqh(100000)
							//获取农机列表数据
							this.getArgmachTypeList()
							this.getData()
						}, 500)
					}
				}
			}
		},
		getData() {
			if (this.areaLevel == 3) {
				this.argmachByProvinceXData = []
				this.argmachByProvinceYData = {}
			}
			this.getNjAreaDayType()
			this.getNjCurrent7Count()

			let time = this.dqbf + '-' + this.dqyfs
			if (this.groupLevel == 2) {
				this.startDate = dayjs(this.dqbf + '-')
					.startOf('year')
					.format('YYYY-MM-DD')
				this.endDate = dayjs(this.dqbf + '-')
					.endOf('year')
					.format('YYYY-MM-DD')
			} else if (this.groupLevel == 3) {
				this.startDate = dayjs(time)
					.startOf('month')
					.format('YYYY-MM-DD')
				this.endDate = dayjs(time)
					.endOf('month')
					.format('YYYY-MM-DD')
			}
			//新优化
			//获取左侧农机数据
			// this.getNjData()

			this.getCurrentWorkNjData()
			this.getCrossWorkNjData()
			this.getZxnjNjData()

			//获取农机七日在线数据
			this.handleSearchZxnjNData({ argmachType: this.zxnj7CurrentArgmachTypeSelected, monthType: '1' })
			console.log('执行了！！')
			// //获取农机七日作业数据
			this.handleSearchWorkNData({ argmachType: this.work7CurrenArgmachTypeSelected, monthType: '1' })

			//获取流入流出数据
			this.handleChangeTab('in')

			//获取顶部的描述数据
			this.getTopDescripteDataList()
		},
		async getCurrentWorkNjData() {
			const params = {
				areaId: this.areaId == 100000 ? '' : this.areaId,
			}
			this.currentWorkTotal = 0
			//---------------获取当日作业农机统计数据----------------
			const currentWorkStasticRes = await api_getCurrentWorkStatisticData(params)
			const currentWorkRes = await api_getCurrentWorkMachine(params)
			if (currentWorkStasticRes.data.length > 0) {
				const dataItem_ = currentWorkStasticRes.data[0]
				if (this.areaLevel == 0 || this.areaLevel == 1) {
					this.currentWorkTotal = Number(this.toFixed(dataItem_.agmachNum))
				} else {
					this.currentWorkTotal = Number(dataItem_.agmachNum)
				}
				this.currentWorkPercent = dataItem_.rate
			}
			//获取当日作业农机数量
			if (currentWorkRes.data.length > 0) {
				const data_ = currentWorkRes.data
				console.log('当日作业data_--==', data_)
				this.currentWorkDataList = data_.map((item) => {
					let icon = ''
					switch (item.agmach_type_id + '') {
						case '510101':
							//轮式拖拉机
							icon = require('@/assets/img/block-box/lstlj-icon.png')
							break
						case '150105':
							icon = require('@/assets/img/block-box/gwlhsgj-icon.png')
							break
						case '150106':
							icon = require('@/assets/img/block-box/ymshj-icon.png')
							break
						case '150107':
							icon = require('@/assets/img/block-box/bzj-icon.png')
							break
						case '120401':
							icon = require('@/assets/img/block-box/cyj-icon.png')
							break
						case '150303':
							icon = require('@/assets/img/block-box/yczshj-icon.png')
							break
						case '150302':
							icon = require('@/assets/img/block-box/hsshj-icon.png')
							break
						case '150403':
							icon = require('@/assets/img/block-box/gzshj-icon.png')
							break
						default:
							icon = require('@/assets/img/block-box/other-argmach-icon.png')
							break
					}
					return {
						title: item.agmach_type_name || '--',
						number: this.areaLevel == 0 || this.areaLevel == 1 ? this.toFixed(item.sum) : item.sum,
						iconUrl: icon,
						argmachType: item.agmach_type_id,
						func: (argmachType) => {
							//获取该农机类型在各省的农机数据
							this.getArgmachByRegionData(argmachType, 'currentWork')
						},
					}
				})

				if (this.currentWorkDataList.length > 0) {
					this.getArgmachByRegionData(this.currentWorkDataList[0].argmachType, 'currentWork')
				}
			}
			//-------------------------------------------
		},

		async getCrossWorkNjData() {
			const params = {
				areaId: this.areaId == 100000 ? '' : this.areaId,
			}
			//----------------获取跨区作业农机统计------------------
			const crossWorkStasticRes = await api_getCrossWorkStatisticMachine({
				...params,
				status: this.areaLevel == 0 ? '' : this.currentTabValue == 'in' ? 0 : 1,
			})
			const crossWorkRes = await api_getCrossWorkMachine({
				...params,
				status: this.areaLevel == 0 ? '' : this.currentTabValue == 'in' ? 0 : 1,
			})
			if (crossWorkStasticRes.data.length > 0) {
				const data_ = crossWorkStasticRes.data[0]
				if (this.areaLevel == 0 || this.areaLevel == 1) {
					this.crossWorkTotal = Number(this.toFixed(data_.agmachNum))
				} else {
					this.crossWorkTotal = Number(data_.agmachNum)
				}
				this.crossWorkPercent = data_.rate
			}
			//获取跨区作业农机数量
			if (crossWorkRes.data.length > 0) {
				const data_ = crossWorkRes.data
				console.log('跨区作业data_--==', data_)

				this.crossWorkDataList = data_.map((item) => {
					let icon = ''
					// 					agmach_type_id
					// agmach_type_name
					switch (item.agmach_type_id + '') {
						case '510101':
							//轮式拖拉机
							icon = require('@/assets/img/block-box/lstlj-icon.png')
							break
						case '150105':
							icon = require('@/assets/img/block-box/gwlhsgj-icon.png')
							break
						case '150106':
							icon = require('@/assets/img/block-box/ymshj-icon.png')
							break
						case '150107':
							icon = require('@/assets/img/block-box/bzj-icon.png')
							break
						case '120401':
							icon = require('@/assets/img/block-box/cyj-icon.png')
							break
						case '150303':
							icon = require('@/assets/img/block-box/yczshj-icon.png')
							break
						case '150302':
							icon = require('@/assets/img/block-box/hsshj-icon.png')
							break
						case '150403':
							icon = require('@/assets/img/block-box/gzshj-icon.png')
							break
						default:
							icon = require('@/assets/img/block-box/other-argmach-icon.png')
							break
					}
					return {
						title: item.agmach_type_name || '--',
						number: this.areaLevel == 0 || this.areaLevel == 1 ? this.toFixed(item.agmachNum) : item.agmachNum,
						iconUrl: icon,
						argmachType: item.agmach_type_id,
						func: (argmachType) => {
							//获取该农机类型在各省的农机数据
							this.getArgmachByRegionData(argmachType, 'crossWork')
						},
					}
				})
			}
			//-------------------------------------------
		},

		async getZxnjNjData() {
			const params = {
				areaId: this.areaId == 100000 ? '' : this.areaId,
			}
			//----------------获取网联农机统计------------------
			const currentZxnjStasticRes = await api_getCurrentStatisticInternetMachine(params)
			const currentZxnjRes = await api_getCurrentInternetMachine({ ...params, dataSource: 1 })

			if (currentZxnjStasticRes.data.length > 0) {
				const data_ = currentZxnjStasticRes.data[0]
				if (this.areaLevel == 0 || this.areaLevel == 1) {
					this.zxnjTotal = Number(this.toFixed(data_.count))
				} else {
					this.zxnjTotal = Number(data_.count)
				}
				this.zxnjPercent = data_.rate
			}

			//获取网联农机数量
			if (currentZxnjRes.data.length > 0) {
				const data_ = currentZxnjRes.data
				console.log('网联农机data_--==', data_)
				this.zxnjDataList = []
				for (let index = 0; index < data_.length; index++) {
					const item = data_[index]
					if (item.agmachTypeCode != '000000') {
						let icon = ''
						switch (item.agmachTypeCode + '') {
							case '510101':
								//轮式拖拉机
								icon = require('@/assets/img/block-box/lstlj-icon.png')
								break
							case '150105':
								icon = require('@/assets/img/block-box/gwlhsgj-icon.png')
								break
							case '150106':
								icon = require('@/assets/img/block-box/ymshj-icon.png')
								break
							case '150107':
								icon = require('@/assets/img/block-box/bzj-icon.png')
								break
							case '120401':
								icon = require('@/assets/img/block-box/cyj-icon.png')
								break
							case '150303':
								icon = require('@/assets/img/block-box/yczshj-icon.png')
								break
							case '150302':
								icon = require('@/assets/img/block-box/hsshj-icon.png')
								break
							case '150403':
								icon = require('@/assets/img/block-box/gzshj-icon.png')
								break
							default:
								icon = require('@/assets/img/block-box/other-argmach-icon.png')
								break
						}
						this.zxnjDataList.push({
							title: item.agmachTypeName || '--',
							number: this.areaLevel == 0 || this.areaLevel == 1 ? this.toFixed(item.count) : item.count,
							iconUrl: icon,
							argmachType: item.agmachTypeCode,
							func: (argmachType) => {
								//获取该农机类型在各省的农机数据
								this.getArgmachByRegionData(argmachType, 'zxnj')
							},
						})
					} else {
					}
				}
			}
		},

		handleNjInfo() {
			if (this.dwType == 'njfbType2') {
				if (this.trajectoryMark == 1) {
					// this.njInfoShow = false
					this.workPopShow = true
					this.$refs.historyWorkPop.init()
				} else {
					this.$message('这台农机无轨迹')
				}
			} else {
				// this.njInfoShow = false
				this.workPopShow = true
				this.$refs.historyWorkPop.init()
			}
		},

		async showRealTimeTrack() {
			console.log('this.currentAgmachInfo--==', this.currentAgmachInfo)

			const info = await agmachToTermId({
				agmachIds: this.currentAgmachInfo.agmachId,
			})

			const { startTime, endTime } = getCurrentTimeRange()
			this.getAgmachRealTimeGuiJiInfo(
				{
					agmachId: info.data[0].term_agmach_id,
					startTime: startTime,
					endTime: endTime,
				},
				this.currentAgmachInfo,
			)

			function getCurrentTimeRange() {
				// 获取当前时间
				const now = new Date()

				// 获取当天0点
				const startOfDay = new Date(now)
				startOfDay.setHours(0, 0, 0, 0)

				// 格式化时间为 YYYY-MM-DD HH:mm:ss
				function formatDate(date) {
					const year = date.getFullYear()
					const month = String(date.getMonth() + 1).padStart(2, '0')
					const day = String(date.getDate()).padStart(2, '0')
					const hours = String(date.getHours()).padStart(2, '0')
					const minutes = String(date.getMinutes()).padStart(2, '0')
					const seconds = String(date.getSeconds()).padStart(2, '0')
					return `${year}-${month}-${day}+${hours}:${minutes}:${seconds}`
				}

				return {
					startTime: formatDate(startOfDay),
					endTime: formatDate(now),
					toString: function() {
						return `${this.startTime} ~ ${this.endTime}`
					},
				}
			}
		},

		// 标记点基础信息弹窗内按钮点击事件
		clickBasicInformationBtn(i, name) {
			if (name == '区域农机社会化服务中心详情') {
				this.basicInfomationShow = false
			}
			if (name == '农机应急作业服务队详情') {
				this.basicInfomationShow = false
			}
			if (name == '区域农业应急救灾中心详情') {
				this.basicInfomationShow = false
			}
		},

		async aqjgGetTokenFn() {
			const res = await aqjgGetToken()
			if (res?.data) {
				localStorage.setItem('aqjgToken', res.data.access_token)
			}
		},

		async getNjCurrent7Count() {
			let params = {
				startDate: dayjs()
					.subtract(7, 'days')
					.format('YYYY-MM-DD'),
				endDate: dayjs()
					.subtract(1, 'days')
					.format('YYYY-MM-DD'),
				areaId: this.areaId == 100000 ? '' : this.areaId,
				level: this.areaLevel == 0 ? '-1' : this.areaLevel,
			}
			this.gdqsChartData = [['product', '在线量']]
			let res = await getNjCurrent7Count(params)
			if (res.data?.length > 0) {
				this.gdqsChartData = [['product', '在线量']].concat(
					res.data.map((item) => {
						return [item.date.substring(5, 19), item.count]
					}),
				)
			}
		},

		async getNjAreaDayType() {},

		async getNjLocation() {
			let res = await getNjLocation({ page_no: 1, page_size: 400000 })
			let data = res.data[0].rows.map((item) => {
				return {
					latlng: [item.lat, item.lon],
					icon: {
						iconUrl: require('@/assets/mskx/icon_nj_b.png'),
						iconSize: [48, 62],
						iconAnchor: [16, 42],
					},
					props: {
						id: 'njfbId',
						type: 'njfbType',
						info: { ...item },
					},
				}
			})
			this.$refs.leafletMap.drawPoiMarker(data, 'njfbType', true)
		},

		getInitData() {
			this.getData()
		},
		//点击某个行政区划 所获取的数据
		gridClick(properties) {
			// mapShow:[3]:农机分布；[4]在线农机；[7]作业热力图
			this.$store.commit('invokerightShow2', properties)

			this.$store.commit('updateMapLoaded', false)

			if (properties.level == 'province') {
				this.$refs.leafletMap.currentMapLevel = 'province'
				this.areaLevel = 1
				this.lastAreaLevel.push(this.areaLevel)
				this.ssboxShow = true
			} else if (properties.level == 'city') {
				this.$refs.leafletMap.currentMapLevel = 'city'
				this.areaLevel = 2
				this.lastAreaLevel.push(this.areaLevel)
				this.ssboxShow = true
			} else if (properties.level == 'district' && this.areaLevel != 3) {
				this.$refs.leafletMap.currentMapLevel = 'district'
				this.areaLevel = 3
				this.lastAreaLevel.push(this.areaLevel)
				this.ssboxShow = false
			}
			if (this.areaLevel == 0) {
				this.showWorkHeat = true
			} else {
				this.showWorkHeat = false
			}
			if (this.areaLevel != 2 && this.areaLevel != 3) {
				this.contentNumberUnitOne = '万台'
			} else {
				this.contentNumberUnitOne = '台'
			}
			this.$refs.leafletMap.mapLevel = this.areaLevel
			this.sspopShow = false
			this.sspopList = false
			this.ssnr = ''
			this.ssList = []
			this.areaId = properties.adcode
			// this.resourceTypes = '-1'

			this.removeAllPoi()
			this.getInitData()

			if (this.mapShow == 3) {
				this.agmachDistribution('njfbType')
			} else if (this.mapShow == 4) {
				this.agmachDistribution('zxnjType')
			} else if (this.mapShow == 7 && this.showWorkHeat) {
				this.workHeat()
			} else {
				this.$store.commit('updateMapLoaded', true)
			}
		},
		//返回上一层地图后所获取的数据
		async operate(areaId, areaId2) {
			this.ssboxShow = true
			this.sspopShow = false
			this.sspopList = false
			this.ssnr = ''
			this.ssList = []
			this.areaId = areaId
			this.$store.commit('updateMapLoaded', false)

			this.$store.commit('invokerightShow3', areaId)
			if (this.areaId) {
				if (this.areaId == 100000) {
					this.areaLevel = 0
				} else {
					this.areaLevel = this.areaLevel_init + this.$refs.leafletMap.lastAreaCode.indexOf(areaId)
				}
			} else {
				this.areaLevel = 0
			}

			if (this.areaLevel == 0) {
				this.showWorkHeat = true
			} else {
				this.showWorkHeat = false
			}
			if (this.areaLevel != 2 && this.areaLevel != 3) {
				this.contentNumberUnitOne = '万台'
			} else {
				this.contentNumberUnitOne = '台'
			}

			this.$refs.leafletMap.mapLevel = this.areaLevel

			await this.removeAllPoi()
			await this.getInitData()

			this.removeAllPoi()
			if (this.mapShow == 3) {
				this.agmachDistribution('njfbType')
			} else if (this.mapShow == 4) {
				this.agmachDistribution('zxnjType')
			} else if (this.mapShow == 7 && this.areaLevel == 0 && this.showWorkHeat) {
				this.workHeat()
			} else {
				this.$store.commit('updateMapLoaded', true)
			}
		},
		//农机图标点击
		poiClick(layerId, it) {
			if (it.trajectoryMark) {
				this.trajectoryMark = it.trajectoryMark
			}

			if (this.areaLevel == 0) {
				this.ssboxShow = true
			} else {
				this.ssboxShow = false
			}
			this.sspopShow = false
			this.sspopList = false
			this.ssnr = ''
			this.ssList = []
			// this.agmachId = it[0]
			this.agmachId = it.props.info.agmachId
			this.dwType = layerId
			this.realTimeButtonShow = false
			if (layerId == 'njfbType') {
				this.locHistory2(it.props.info.agmachId)
			} else if (layerId == 'zxnjType') {
				this.realTimeButtonShow = true
				this.locHistory2(it.props.info.agmachId)
			} else if (layerId == 'njfbType2') {
				this.trajectoryMark = it[2]
				if (it[1] == 1) {
					this.locHistory2(it[0])
				} else {
					this.$message('这台农机无基本信息')
				}
			} else if (layerId == 'ncjkType') {
				// getNcjkToken().then((rel) => {
				// 	// this.ncjkDialogShow = true
				// 	getNcjkUrl('FX6180183').then(async (res) => {
				// 		const UIKitDEMO = await new EZUIKit.EZUIKitPlayer({
				// 			id: `ezuikit-player`,
				// 			url: res.result,
				// 			accessToken: rel.result.accessToken,
				// 		})
				// 	})
				// })
			}
		},

		//获取农机历史数据
		async locHistory2(data) {
			let res = await locHistory2({
				agmachId: data,
				// page_no: 1,
				// page_size: 10
				pageNo: 1,
				pageSize: 10,
			})
			this.njInfoShow = true
			// this.currentAgmachInfo = res.data[0].rows[0]
			if (res.data) {
				this.currentAgmachInfo = res.data[0]
			} else {
				this.currentAgmachInfo = {}
			}
		},
		//移除地图中的所有覆盖物
		removeAllPoi() {
			this.$refs.leafletMap.removeLayer('njfbType')
			this.$refs.leafletMap.removeLayer('zxnjType')
			this.$refs.leafletMap.removeLayer('heat')
			this.$refs.leafletMap.removeLayer('hgzxType')
			this.$refs.leafletMap.removeLayer('yjwzType')
			this.$refs.leafletMap.removeLayer('yjjzType')
			this.$refs.leafletMap.removeLayer('fwdType')
			this.$refs.leafletMap.removeLayer('ncjkType')
			this.$refs.leafletMap.removeLayer('numberMarker')
			this.$refs.leafletMap.clearCluster()
			if (this.$refs.leafletMap.polygon) {
				this.$refs.leafletMap.polygon.remove()
			}
		},
	},
}
</script>

<style lang="less" scoped>
@font-face {
	font-family: QuartzRegular;
	src: url(~@/assets/leader/font/QuartzRegular.ttf);
}

@font-face {
	/*给字体命名*/
	font-family: 'YouSheBiaoTiHei';
	/*引入字体文件*/
	src: url(~@/assets/font/youshebiaotihei.ttf);
	font-weight: normal;
	font-style: normal;
}

@font-face {
	/*给字体命名*/
	font-family: 'AlibabaPuHuiTi';
	/*引入字体文件*/
	src: url(~@/assets/font/AlibabaPuHuiTi-2-85-Bold.ttf);
	font-weight: normal;
	font-style: normal;
}

@keyframes rotateS {
	0% {
		transform: rotate(0);
	}

	100% {
		transform: rotate(360deg);
	}
}

@keyframes rotateN {
	0% {
		transform: rotate(360deg);
	}

	100% {
		transform: rotate(0);
	}
}

@keyframes rotateY {
	0% {
		transform: rotate(0);
	}

	100% {
		transform: rotateY(360deg);
	}
}

.leader_sjts {
	width: 1920px;
	height: 1080px;
	position: absolute;
	top: 0;
	display: flex;
	justify-content: space-between;

	.leader_city_box {
		width: 450px;

		.leader_zt_contain {
			height: 100%;
		}
	}

	.box {
		.cont {
			width: 100%;
			height: 100%;
			position: relative;
		}

		.wrapper1 {
			width: 100%;
			height: 100%;

			.introduce_video {
				width: 459px;
				height: 192px;

				::v-deep .video-wrapper {
					padding-bottom: 41.25% !important;
				}
			}

			.introduce {
				margin-top: 11px;
				width: 100%;
				height: 120px;
				padding: 18px 26px;
				background: url(~@/assets/hszl/bg1.png) no-repeat;
				background-size: 100% 100%;
				font-size: 16px;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #4fddff;
				line-height: 22px;
				letter-spacing: 2px;
				text-align: left;
			}
		}

		.wrapper4 {
			width: 100%;
			height: 100%;
			padding: 30px 0 30px 8px;

			ul {
				width: 100%;
				height: 100%;
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;
				align-content: space-between;

				li {
					display: flex;
					height: 49px;
					gap: 10px;

					.icon {
						width: 46px;
						height: 49px;
					}

					.info {
						margin-top: -6px;

						.label {
							font-size: 14px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #ffffff;
							line-height: 20px;
							text-align: left;
							padding-left: 5px;
						}

						.line {
							width: 87px;
							height: 6px;
							background: url('~@/assets/hszl/line1.png') no-repeat;
						}

						.count {
							text-align: left;
							margin-top: 4px;
							width: 87px;
							height: 22px;
							line-height: 22px;
							background: linear-gradient(96deg, #1d4975 0%, rgba(19, 56, 87, 0.24) 100%);
							font-size: 14px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #a8b1b9;
							padding-left: 5px;

							span {
								font-size: 18px;
								font-family: PingFangSC, PingFang SC;
								font-weight: normal;
								color: #ffffff;
								line-height: 22px;
								margin-right: 3px;
							}
						}
					}
				}
			}
		}

		.wrapper5 {
			width: 100%;
			height: 100%;
			padding-top: 31px;

			.info {
				width: 100%;
				height: 71px;
				padding-left: 8px;
				display: flex;
				justify-content: space-between;

				.total {
					width: 130px;
					height: 100%;
					background: url('~@/assets/hszl/bg4.png') no-repeat;
					display: grid;
					place-items: center;

					.cont {
						padding-top: 13px;

						.label {
							font-size: 14px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #ffffff;
							line-height: 20px;
						}

						.value {
							font-size: 18px;
							font-family: PingFangSC, PingFang SC;
							font-weight: normal;
							color: #ffffff;
							line-height: 22px;
							background: linear-gradient(180deg, #ffffff 0%, #00fffa 67%, #04a1ff 100%);
							background-clip: text;
							-webkit-background-clip: text;
							-webkit-text-fill-color: transparent;

							span {
								font-size: 12px;
							}
						}
					}
				}

				.counts {
					width: 312px;
					height: 100%;
					background: url('~@/assets/hszl/bg5.png') no-repeat;
					display: flex;
					padding: 0 9px;

					.men {
						text-align: left;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						margin-right: 4px;

						.count {
							height: 21px;
							font-size: 16px;
							font-family: PingFangSC, PingFang SC;
							font-weight: normal;
							line-height: 19px;
							background: linear-gradient(180deg, #ffffff 0%, #00ceff 67%, #04a1ff 100%);
							background-clip: text;
							-webkit-background-clip: text;
							-webkit-text-fill-color: transparent;

							span {
								font-size: 14px;
							}
						}

						.chart {
							display: flex;
							gap: 4px;

							span {
								width: 10px;
								height: 25px;
								background-repeat: no-repeat;
							}
						}
					}

					.women {
						text-align: right;
						display: flex;
						flex-direction: column;
						justify-content: space-between;

						.count {
							height: 21px;
							font-size: 16px;
							font-family: PingFangSC, PingFang SC;
							font-weight: normal;
							line-height: 19px;
							background: linear-gradient(180deg, #ffffff 0%, #f076ff 66%, #ff04ff 100%);
							background-clip: text;
							-webkit-background-clip: text;
							-webkit-text-fill-color: transparent;

							span {
								font-size: 14px;
							}
						}

						.chart {
							display: flex;
							gap: 4px;

							span {
								width: 12px;
								height: 25px;
								background-repeat: no-repeat;
							}
						}
					}
				}
			}

			.chart_wrap {
				position: relative;
				width: 100%;
				height: 198px;
				top: 41px;

				.sign {
					position: absolute;
					left: 105px;
					top: 25px;

					.woman {
						position: absolute;
						width: 27px;
						height: 57px;
						left: 0;
						top: 0;
						background: url('~@/assets/hszl/woman.png') no-repeat;
					}

					.man {
						position: absolute;
						width: 23px;
						height: 57px;
						left: 95px;
						top: 47px;
						background: url('~@/assets/hszl/man.png') no-repeat;
					}

					.man_count {
						position: absolute;
						left: 10px;
						top: 76px;
						width: 109px;
						height: 31px;
						font-size: 20px;
						font-family: PingFangSC, PingFang SC;
						font-weight: normal;
						color: #ffffff;
						line-height: 24px;
					}

					.woman_count {
						position: absolute;
						left: 10px;
						top: 36px;
						width: 109px;
						height: 31px;
						font-size: 20px;
						font-family: PingFangSC, PingFang SC;
						font-weight: normal;
						color: #ffffff;
						line-height: 24px;
					}
				}
			}
		}
	}

	.box1 {
		.cout {
			width: 100%;
			height: 100%;
		}
		.cont1 {
			width: 100%;
			height: 60px;
			display: flex;
			justify-content: space-around;

			.contList {
				.contNum {
					width: 124px;
					height: 24px;
					margin-top: 10px;

					span {
						display: inline-block;
						width: 100%;
						height: 24px;
						font-family: PingFangSC, PingFang SC;
						font-weight: bold;
						font-size: 20px;
						color: #ffffff;
						line-height: 24px;
						text-shadow: 0px 0px 4px rgba(0, 0, 0, 0);
						text-align: center;
						font-style: italic;
						background: linear-gradient(180deg, #ffffff 0%, #00fffa 67%, #04a1ff 100%);
						-webkit-background-clip: text;
						-webkit-text-fill-color: transparent;
					}
				}

				&:nth-of-type(2) {
					.contNum {
						width: 124px;
						height: 24px;

						span {
							display: inline-block;
							width: 100%;
							height: 24px;
							font-family: PingFangSC, PingFang SC;
							font-weight: bold;
							font-size: 20px;
							color: #ffffff;
							line-height: 24px;
							text-shadow: 0px 0px 4px rgba(0, 0, 0, 0);
							text-align: center;
							font-style: italic;
							background: linear-gradient(180deg, #ffffff 0%, #ffd500 67%, #ffb904 100%);
							-webkit-background-clip: text;
							-webkit-text-fill-color: transparent;
						}
					}
				}

				&:nth-of-type(3) {
					.contNum {
						width: 124px;
						height: 24px;

						span {
							display: inline-block;
							width: 100%;
							height: 24px;
							font-family: PingFangSC, PingFang SC;
							font-weight: bold;
							font-size: 20px;
							color: #ffffff;
							line-height: 24px;
							text-shadow: 0px 0px 4px rgba(0, 0, 0, 0);
							text-align: center;
							font-style: italic;
							background: linear-gradient(180deg, #ffffff 0%, #86ff00 67%, #1cff04 100%);
							-webkit-background-clip: text;
							-webkit-text-fill-color: transparent;
						}
					}
				}

				img {
					display: block;
					width: 124px;
					height: 4px;
					margin-top: 4px;
				}

				.contName {
					width: 124px;
					height: 20px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 14px;
					color: #ffffff;
					line-height: 20px;
					text-align: center;
					font-style: normal;
				}
			}
		}

		.cont2 {
			width: 100%;
			height: 170px;
		}
	}

	.box2 {
		margin-top: 22px;
		.contLeft {
			position: absolute;
			left: 20px;
			top: 10px;
			text-align: left;
			z-index: 99;
		}

		.contRight {
			position: absolute;
			right: 20px;
			top: 10px;
			text-align: right;
			z-index: 99;
		}

		.contList {
			margin-top: 18px;
			cursor: pointer;
			.contNum {
				font-family: PingFangSC, PingFang SC;
				font-weight: bold;
				font-size: 24px;
				color: #ffffff;
				line-height: 28px;
				letter-spacing: 2px;
				font-style: normal;

				span {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 14px;
					color: #cbe5ff;
					line-height: 20px;
					letter-spacing: 1px;
					text-align: right;
					font-style: normal;
					margin-left: 4px;
				}
			}

			.contName {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #cbe5ff;
				line-height: 20px;
				letter-spacing: 1px;
				font-style: normal;
			}

			&:nth-of-type(1) {
				margin-top: 7px;
			}
		}

		.contImg {
			width: 405px;
			height: 197px;
			position: absolute;
			top: 52px;
			left: 20px;
		}

		.contImg2 {
			width: 59px;
			height: 61px;
			position: absolute;
			top: 93px;
			left: 194px;
		}
	}

	.box3 {
		.cont {
			padding: 12px 22px 0;

			video {
				width: 100%;
				height: 100%;
				object-fit: cover;

				&:not(:root):fullscreen {
					object-fit: contain;
				}
			}
		}
	}

	.box4 {
		.cont {
			padding: 15px 0;
			display: flex;
			flex-wrap: wrap;
			justify-content: space-evenly;

			li {
				position: relative;
				width: 200px;
				height: 66px;
				background: url(~@/assets/csts/bg2.png) no-repeat;
				display: flex;
				flex-direction: column;
				justify-content: center;
				text-align: left;
				padding-left: 92px;

				.icon {
					position: absolute;
					width: 26px;
					height: 26px;
					top: 10px;
					left: 31px;
					animation: move infinite 3s ease-in-out;

					@keyframes move {
						0% {
							transform: translateY(0px) rotateY(0);
						}

						50% {
							transform: translateY(-10px) rotateY(180deg);
						}

						100% {
							transform: translateY(0px) rotateY(360deg);
						}
					}
				}

				&:nth-child(2) {
					span {
						animation-delay: 0.3s;
					}
				}

				&:nth-child(3) {
					span {
						animation-delay: 0.6s;
					}
				}

				&:nth-child(4) {
					span {
						animation-delay: 0.9s;
					}
				}

				&:nth-child(5) {
					span {
						animation-delay: 1.2s;
					}
				}

				&:nth-child(6) {
					span {
						animation-delay: 1.5s;
					}
				}

				.tit {
					font-size: 14px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #ffffff;
					line-height: 20px;
				}

				.num {
					font-size: 18px;
					font-family: PingFangSC, PingFang SC;
					font-weight: normal;
					color: #ffffff;
					line-height: 22px;
				}
			}
		}
	}

	.box5 {
		.cont {
			display: flex;
			flex-direction: column;

			.bar_chart {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-evenly;

				li {
					position: relative;
					display: flex;
					align-items: center;
					padding: 0 30px 0 23px;

					.icon {
						width: 20px;
						height: 22px;
						margin-right: 7px;
					}

					.icon2 {
						width: 10px;
						height: 12px;
						position: absolute;
						left: 28px;
						top: 5px;
						animation: move infinite 3s ease-in-out;

						@keyframes move {
							0% {
								transform: translateY(0px) rotateY(0);
							}

							50% {
								transform: translateY(0px) rotateY(180deg);
							}

							100% {
								transform: translateY(0px) rotateY(360deg);
							}
						}
					}

					.lab {
						font-size: 14px;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						color: #ffffff;
						margin-right: 8px;
					}

					.progress {
						display: block;
						flex: 1;
						height: 100%;
						background: rgba(0, 201, 255, 0.14);

						.cur {
							width: 100%;
							border: 1px solid;
							height: 100%;
							overflow: hidden;
							transition: width 0.5s;
						}
					}

					.num {
						font-size: 16px;
						font-family: PingFangSC, PingFang SC;
						font-weight: normal;
						color: #ffffff;
						margin-left: 9px;
					}

					.percent {
						font-size: 16px;
						font-family: PingFangSC, PingFang SC;
						font-weight: normal;
						color: #ffffff;
						margin-left: 14px;
					}
				}
			}

			.pie_chart {
				height: 123px;
				display: flex;
				justify-content: space-evenly;

				.warp {
					width: 107px;
					height: 107px;
					padding-top: 0;

					/deep/.ring {
						width: 100% !important;
						height: 100% !important;
					}

					/deep/.label {
						margin-top: -65px;

						.name {
							font-size: 13px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #ffffff;
						}
					}
				}
			}
		}
	}

	.box6 {
		margin-top: 12px;

		.cont {
			.select_month {
				position: absolute;
				right: 0;
				top: -20px;

				::v-deep button {
					width: 56px;
					height: 24px;
					right: -46px;
					top: -28px;
					border-radius: 12px;
					background: url(~@/assets/shzl/select_bg.png) no-repeat center / 100% 100%;
					border: none;
					padding: 0;
					// background: linear-gradient(180deg, #0e63ac 0%, #253c69 100%);
					// border-radius: 2px;
					// border: 2px solid;
					// border-image: linear-gradient(180deg, rgba(63, 148, 224, 0.2), rgba(218, 250, 255, 0.3))
					//   2 2;
				}
			}

			// padding: 14px 28px 0;
			// .swiper-slide {
			//   height: 100%;
			// }
			// .swiper-pagination {
			//   text-align: right;
			//   bottom: 3px;
			// }
			// /deep/.swiper-pagination-bullet-active {
			//   background-color: rgba(255, 255, 255, 0.8);
			// }
			// .content {
			//   width: 100%;
			//   height: 100%;
			//   position: relative;
			//   img {
			//     position: absolute;
			//     width: 100%;
			//     height: 100%;
			//     top: 0;
			//     left: 0;
			//   }
			//   .cont {
			//     width: 100%;
			//     height: 30px;
			//     padding: 5px 10px;
			//     position: absolute;
			//     bottom: 0;
			//     font-size: 14px;
			//     font-family: PingFangSC, PingFang SC;
			//     font-weight: 500;
			//     color: #ffffff;
			//     line-height: 22px;
			//     text-align: left;
			//     background-color: rgba(0, 0, 0, 0.35);
			//   }
			// }
		}
	}

	.box7 {
		/deep/.el-select > .el-input {
			height: 100%;
		}
		.cont {
			display: grid;
			place-items: center;
			position: relative;

			.fy_out {
				position: absolute;
				top: 3%;
				left: 27%;
				z-index: 99;
				transform: translate(-50%, -50%);
				animation: rotateS infinite 12s linear;
			}

			.fy_in {
				position: absolute;
				top: 44%;
				left: 50%;
				transform: translate(-50%, -50%);
			}

			.wrap {
				position: relative;
				width: 393px;
				height: 205px;
				background: url(~@/assets/csts/bg3.png) no-repeat;

				li {
					position: absolute;
					text-align: left;

					.num {
						font-size: 20px;
						font-family: PingFangSC, PingFang SC;
						font-weight: normal;
						color: #ffffff;
						line-height: 24px;
					}

					.tit {
						font-size: 14px;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						color: #ffffff;
						line-height: 17px;
					}

					&:nth-child(1) {
						top: 28px;
						left: 24px;
					}

					&:nth-child(2) {
						top: 28px;
						left: 295px;
					}

					&:nth-child(3) {
						bottom: 32px;
						left: 24px;
					}

					&:nth-child(4) {
						bottom: 32px;
						left: 295px;
					}
				}
			}
		}
	}

	.box8 {
		/deep/.el-select > .el-input {
			height: 100%;
		}
		margin-top: 16px;
		position: relative;
		.njfbDw {
			position: absolute;
			left: 50px;
			top: 20px;
			color: #ffffff;
			font-size: 14px;
			font-family: PingFangSC, PingFang SC;
			font-weight: 300;
		}
		.cout {
			width: 100%;
			height: 100%;
		}
	}

	.box9 {
		.cont {
			padding: 16px 23px 0;

			.title {
				width: 100%;
				height: 44px;
				line-height: 44px;
				display: flex;
				justify-content: space-between;
				padding: 0 24px;

				span {
					font-size: 14px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #ffffff;
				}
			}

			.detail {
				position: relative;
				width: 404px;
				height: 156px;
				background: url(~@/assets/csts/bg4.png) no-repeat center;
				display: flex;
				justify-content: space-between;

				.center_out {
					position: absolute;
					left: 29%;
					top: -2%;
					transform: translate(-50%, -50%);
					z-index: 99;
					animation: rotateS infinite 12s linear;
				}

				.center_in {
					position: absolute;
					left: 29%;
					top: 2px;
					transform: translate(-50%, -50%);
					z-index: 99;
					animation: rotateN infinite 12s linear;
				}

				.fs {
					display: flex;
					flex-direction: column;
					justify-content: space-evenly;
					gap: 9px;

					li {
						display: flex;
						justify-content: space-between;
						align-items: center;

						.icon {
							width: 13px;
							height: 16px;
						}

						.lab {
							font-size: 14px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #ffffff;
							line-height: 20px;
							padding-left: 6px;
							padding-right: 22px;
						}

						.num {
							font-size: 10px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #ffffff;

							span {
								font-size: 18px;
								font-family: PingFangSC, PingFang SC;
								font-weight: normal;
								color: #ffffff;
								line-height: 22px;
							}
						}
					}
				}

				.fq {
					display: flex;
					flex-direction: column;
					justify-content: space-evenly;
					gap: 9px;

					li {
						display: flex;
						justify-content: space-between;
						align-items: center;

						.icon {
							width: 14px;
							height: 14px;
						}

						.lab {
							font-size: 14px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #ffffff;
							line-height: 20px;
							padding-left: 22px;
							padding-right: 6px;
						}

						.num {
							font-size: 10px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #ffffff;

							span {
								font-size: 18px;
								font-family: PingFangSC, PingFang SC;
								font-weight: normal;
								color: #ffffff;
								line-height: 22px;
							}
						}
					}
				}

				.zdqy {
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);

					.num {
						font-size: 20px;
						font-family: PingFangSC, PingFang SC;
						font-weight: normal;
						color: #ffffff;
						line-height: 24px;
					}

					.lab {
						font-size: 18px;
						font-family: PingFangSC, PingFang SC;
						color: #ffffff;
						line-height: 21px;
						letter-spacing: 1px;
					}
				}
			}
		}
	}

	.box12 {
		position: relative;

		.gajq_lz {
			width: 100%;
			height: 100%;
			position: absolute;
			top: 0;

			img {
				width: 100%;
				height: 100%;
			}
		}
	}

	.left_bg {
		width: 460px;
		height: 1033px;
		background: url(~@/assets/leader/img/component/left_bg.png) no-repeat center / 100% 100%;
		position: absolute;
		left: 50px;
		top: 25px;
		z-index: 102;
	}

	.right_bg {
		width: 520px;
		height: 1033px;
		background: url(~@/assets/leader/img/component/right_bg.png) no-repeat center / 100% 100%;
		position: absolute;
		right: 50px;
		top: 25px;
		z-index: 102;
	}

	.left {
		// width: 450px;
		// height: calc(100vh - 216px);
		height: 687px;
		display: flex;
		justify-content: space-between;
		margin-top: 104px;

		margin-left: 24px;
		position: relative;
		left: 0;
		opacity: 1;
		z-index: 1003;
		-webkit-transition: all 0.5s ease-in;
		-moz-transition: all 0.5s ease-in;
		transition: all 0.5s ease-in;
	}

	.top-middle {
		position: relative;
		top: 110px;
		left: 0;
		width: calc(100% - 950px);
		height: 72px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		z-index: 1001;
		-webkit-transition: all 0.5s ease-in;
		-moz-transition: all 0.5s ease-in;
		transition: all 0.5s ease-in;
	}

	.mkdx-top-middle {
		top: -97px;
	}

	.mkdxLeft {
		left: -450px;
		opacity: 0;
	}

	.right {
		width: 450px;
		// height: calc(100vh - 165px - 216px);
		height: 687px;
		display: flex;
		justify-content: space-between;
		margin-top: 104px;
		margin-right: 32px;
		position: relative;
		right: 0;
		z-index: 1003;
		-webkit-transition: all 0.5s ease-in;
		-moz-transition: all 0.5s ease-in;
		transition: all 0.5s ease-in;

		.njlx_box {
			&::-webkit-scrollbar {
				width: 6px;
				background: transparent;
			}

			&::-webkit-scrollbar-thumb {
				/*滚动条里面小方块*/
				border-radius: 2px;
				box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
				background: rgb(45, 124, 228);
				height: 100px;
				width: 20px;
			}
		}

		.njlx_box {
			display: flex;
			flex-wrap: wrap;
			margin: 10px auto 0;
			text-align: left;
			width: 100%;
			height: calc(100% - 30px);
			overflow-y: scroll;
			-ms-overflow-style: none;
			/* IE 10+ */
			scrollbar-width: none;
			/* Firefox */

			.njlx_item {
				display: inline-block;
				margin: 0 3% 7px 3%;
				width: 27%;
				// height: 93px;
				// height:45%;
				// height:30%;
				// background-image: url(~@/assets/bjnj/njlx_bgImg.png);
				// background-size: 100% 100%;
				// background-repeat: no-repeat;
				// margin-bottom: 7px;
				// padding-top: 8px;
				box-sizing: border-box;
				text-align: center;

				.njlx_title {
					margin: 0 auto;
					max-width: fit-content;
					min-width: 100px;
					background-image: url(~@/assets/bjnj/njlx_titleImg.png);
					background-size: 100% 18px;
					background-repeat: no-repeat;
					// height: 18px;
					font-family: PingFangSC, PingFang SC;
					font-size: 16px;
					color: #edfbff;
					line-height: 18px;
					letter-spacing: 1px;
					text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.15);
					font-style: normal;
					text-align: center;
				}

				.njlx_number {
					margin: 8px auto;
					width: fit-content;
					height: 24px;
					font-family: PangMenZhengDao;
					font-size: 24px;
					color: #ffffff;
					line-height: 24px;
					letter-spacing: 1px;
					text-shadow: 0px 0px 4px rgba(73, 140, 255, 0.85);
					text-align: left;
					font-style: normal;
					background: linear-gradient(90deg, #00faff 0%, #00b1ff 100%);
					-webkit-background-clip: text;
					/*将设置的背景颜色限制在文字中*/
					-webkit-text-fill-color: transparent;
					/*给文字设置成透明*/
				}
			}
			.njlx_item2 {
				display: inline-block;
				margin: 0 10px;
				width: 28%;
				// height: 93px;
				height: 30%;
				// background-image: url(~@/assets/bjnj/njlx_bgImg2.png);
				background-size: 100% 100%;
				background-repeat: no-repeat;
				margin-bottom: 7px;
				// padding-top: 8px;
				box-sizing: border-box;
				text-align: center;

				.njlx_title2 {
					margin: 0 auto;
					max-width: fit-content;
					min-width: 100px;
					background-image: url(~@/assets/bjnj/njlx_titleImg2.png);
					background-size: 100% 100%;
					background-repeat: no-repeat;
					height: 18px;
					font-family: PingFangSC, PingFang SC;
					font-size: 16px;
					color: #edfbff;
					line-height: 18px;
					letter-spacing: 1px;
					text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.15);
					font-style: normal;
					text-align: center;
				}

				.njlx_number {
					margin: 8px auto;
					width: fit-content;
					height: 24px;
					font-family: PangMenZhengDao;
					font-size: 24px;
					color: #ffffff;
					line-height: 24px;
					letter-spacing: 1px;
					text-shadow: 0px 0px 4px rgba(255, 164, 73, 0.85);
					text-align: left;
					font-style: normal;
					background: linear-gradient(90deg, #ff9800 0%, #ffcb00 100%);
					-webkit-background-clip: text;
					/*将设置的背景颜色限制在文字中*/
					-webkit-text-fill-color: transparent;
					/*给文字设置成透明*/
				}
			}
		}
	}

	.mkdxRight {
		right: -450px;
		opacity: 0;
	}
	.zwsjImg {
		height: 80%;
		margin: 0 auto;
		margin-top: 36px;
	}
}
.middle {
	width: 100%;
	height: 249px;
	position: absolute;
	box-sizing: border-box;
	left: 50%;
	bottom: 24px;
	transform: translateX(-50%);
	// background: url(~@/assets/bjnj/dbmc.png) no-repeat center / 100% 100%;
	z-index: 1002;
	-webkit-transition: all 0.5s ease-in;
	-moz-transition: all 0.5s ease-in;
	transition: all 0.5s ease-in;
	padding: 0 20px;
	.box10 {
		.cont {
			width: 100%;
			height: 100%;
			margin: 0 auto;
			position: relative;
			.couts {
				width: 100%;
				height: 100%;
				.zwsjImg {
					height: 80%;
					margin: 0 auto;
					margin-top: 45px;
				}
			}
			.nfxz {
				position: absolute;
				left: 57.5%;
				top: 12px;
				z-index: 1003;
				width: 100px;
				height: 20px;
				::v-deep .el-input {
					font-size: 12px;
				}
				::v-deep .el-input__inner {
					color: #ccf4ff;
					height: 20px;
					line-height: 20px;
					background: linear-gradient(180deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%),
						rgba(0, 74, 143, 0.4);
					border: 1px solid rgba(0, 162, 255, 0.6);
				}
				::v-deep .el-input__icon {
					line-height: 20px;
				}
			}
			.yfxz {
				position: absolute;
				left: 63.5%;
				top: 12px;
				z-index: 1003;
				width: 100px;
				height: 20px;
				::v-deep .el-input {
					font-size: 12px;
				}
				::v-deep .el-input__inner {
					color: #ccf4ff;
					height: 20px;
					line-height: 20px;
					background: linear-gradient(180deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%),
						rgba(0, 74, 143, 0.4);
					border: 1px solid rgba(0, 162, 255, 0.6);
				}
				::v-deep .el-input__icon {
					line-height: 20px;
				}
			}
			.fhBox {
				width: 80px;
				border: 1px solid rgba(0, 162, 255, 0.6);
				border-radius: 5px;
				position: absolute;
				left: 69.5%;
				top: 12px;
				z-index: 1003;
				font-size: 12px;
				height: 20px;
				color: #7e94a9;
				padding-top: 1px;
				display: flex;
				align-items: center;
				padding: 0 10px;
				cursor: pointer;
				.fh {
					height: 16px;
					margin-right: 4px;
				}
			}
			.fhBox:active {
				color: #ccf4ff;
			}
		}
	}
}
.mkdxMiddle {
	bottom: -325px;
	opacity: 0;
}

.jgzzBox {
	width: 156px;
	height: 163px; // 204
	display: flex;
	flex-direction: column;
	position: absolute;
	left: 980px;
	bottom: 93px;
	z-index: 999;
	background: url(~@/assets/mskx/bg_resource.png) no-repeat center / 100% 100%;

	.jgzzItem {
		width: 153px;
		height: 36px;
		margin: 1px 2px 1px 2px;
		cursor: pointer;

		// background: url(~@/assets/csts/btn11.png) no-repeat;
		img {
			width: 22px;
			height: 22px;
			float: left;
			margin: 7px 3px 0 10px;
		}

		span {
			float: left;
			font-size: 17px;
			font-family: PingFangSC, PingFang SC;
			color: #e9fffe;
			line-height: 36px;
			background: linear-gradient(180deg, #ffffff 0%, #2f8af4 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}
	}

	.jgzzActive {
		width: 153px;
		height: 36px;
		margin: 1px 2px 1px 2px;
		// background: url(~@/assets/csts/btn12.png) no-repeat;
		background: rgba(108, 163, 255, 0.3);
	}
}

.zdcsBox {
	width: 109px;
	height: 248px;
	display: flex;
	flex-direction: column;
	position: absolute;
	left: 1197px;
	bottom: 93px;
	z-index: 999;
	background: url(~@/assets/csts/btnBg12.png) no-repeat center / 100% 100%;

	.jgzzItem {
		width: 103px;
		height: 35px;
		margin: 2px 3px 2px 3px;
		background: url(~@/assets/csts/btn11.png) no-repeat;

		img {
			width: 22px;
			height: 22px;
			float: left;
			margin: 7px 0 0 10px;
		}

		span {
			float: left;
			font-size: 17px;
			font-family: PingFangSC, PingFang SC;
			color: #e9fffe;
			line-height: 35px;
			background: linear-gradient(180deg, #ffffff 0%, #2f8af4 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}
	}

	.jgzzActive {
		width: 156px;
		height: 35px;
		margin: 1px 2px 1px 2px;
		background: url(~@/assets/csts/btn12.png) no-repeat;
	}
}
.tjxxBox {
	width: 800px;
	height: 74px;
	position: absolute;
	top: 124px;
	left: 50%;
	transform: translateX(-50%);
	z-index: 1003;
	.tjxxItem {
		width: 180px;
		height: 74px;
		margin: 0 10px;
		float: left;
		.tjxxName {
			width: 180px;
			height: 36px;
			background: url(~@/assets/bjnj/tjxxBg.png) no-repeat center / 100% 100%;
			font-family: AlibabaPuHuiTi;
			font-size: 18px;
			color: #dff3ff;
			line-height: 36px;
			text-align: center;
			font-style: normal;
		}
		.tjxxNum {
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 26px;
			color: #ffffff;
			line-height: 38px;
			text-shadow: 0px 0px 1px rgba(0, 18, 57, 0.84);
			text-align: center;
			font-style: normal;
			span {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #ffffff;
				line-height: 20px;
				text-shadow: 0px 0px 1px rgba(0, 18, 57, 0.84);
				text-align: left;
				font-style: normal;
			}
		}
	}
}
.mapBtn {
	width: 106px;
	position: absolute;
	z-index: 1003;

	-webkit-transition: all 0.5s ease-in;
	-moz-transition: all 0.5s ease-in;
	transition: all 0.5s ease-in;
	.mapItem {
		width: 106px;
		height: 34px;
		margin-bottom: 20px;
		padding: 2px;
		// background: #e1fff8;
		background: #04244e;

		cursor: pointer;
		span {
			width: 100%;
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 16px;
			// color: #24818c;
			color: #b0e0ff;
			letter-spacing: 1px;
			font-style: normal;
			// border: 1px solid #76bcb5;
			border: 1px solid rgba(0, 163, 255, 0.5);
		}

		&:last-child {
			margin-bottom: 0;
		}

		&.active-item {
			span {
				color: #ffffff;
				border: 1px solid #f7d7b3;
			}
			background: #fc973d;
		}
	}
	.mapItem2 {
		width: 160px;
		height: 40px;
		margin: 8px 0;
		background: url(~@/assets/bjnj/btn2.png) no-repeat center / 100% 100%;
		img {
			vertical-align: middle;
			margin-top: -10px;
		}
		span {
			margin-left: 4px;
			font-family: YouSheBiaoTiHei;
			font-size: 20px;
			color: #ffffff;
			line-height: 40px;
			text-align: left;
			font-style: normal;
			background: linear-gradient(180deg, #ffffff 0%, #ffca00 100%);
			background-clip: text;
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			cursor: pointer;
		}
	}
}

.map-operate-box {
	height: 32px;
	position: absolute;
	z-index: 1002;
	display: flex;
	justify-content: center;
	align-items: center;

	// background: rgba(0, 163, 255, 0.05);
	background: linear-gradient(90deg, rgba(42, 130, 255, 0.5) 0%, rgba(0, 98, 234, 0.9) 100%);

	border-radius: 4px;

	border: 1px solid rgba(0, 163, 255, 0.5);

	-webkit-transition: all 0.5s ease-in;
	-moz-transition: all 0.5s ease-in;
	transition: all 0.5s ease-in;

	.operate-item-box {
		// width: 107px;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		cursor: pointer;
		padding: 0 13px;
		padding-right: 0;

		img {
			width: 16px;
			height: 16px;
			margin-right: 6px;
		}

		.operate-icon {
			font-size: 15px;
			color: #30a0a9;

			/* 背景不重复 */
			background-repeat: no-repeat;
			/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
			background-size: cover;
			/* 背景居中显示（可选） */
			background-position: center;
			margin-right: 6px;
			padding-left: 13px;
		}

		.icon-rotate {
			// transform: rotate(90deg);
			// transform-origin: center; /* 旋转的中心点为元素的中心 */
		}

		.operate-title {
			padding-right: 13px;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 14px;
			color: #b0e0ff;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}

		&:hover {
			background: rgba(255, 128, 12, 0.8);

			.operate-title {
				color: #fff;
			}
		}

		.devider {
			width: 1px;
			height: 12px;
			background: #848991;
		}

		&:last-child {
			.devider {
				display: none;
			}
		}
	}
}

.zyfbBox {
	width: 240px;
	height: 120px;
	background: url(~@/assets/bjnj/mapBg.png) no-repeat center / 100% 100%;
	position: absolute;
	top: 220px;
	left: 665px;
	z-index: 1003;
	padding: 3px 0 0 24px;
	.zyfbItem {
		width: 210px;
		height: 36px;
		cursor: pointer;
		img {
			width: 22px;
			height: 22px;
			float: left;
			margin: 6px 8px 0 7px;
		}

		span {
			display: inline-block;
			width: 100%;
			text-align: center;
			float: left;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 16px;
			color: #ffffff;
			line-height: 36px;
			font-style: normal;
		}
	}
	.zyfbItem:hover {
		background: rgba(108, 163, 255, 0.3);
	}
	.jgzzActive {
		background: rgba(108, 163, 255, 0.3);
	}
}
.njfbBox {
	width: 168px;
	height: 336px;
	// background: url(~@/assets/bjnj/mapBg2.png) no-repeat center / 100% 100%;
	position: absolute;
	// top: 302px;
	// left: 665px;
	z-index: 1003;
	padding: 3px;
	// background: #d3e7e9;
	// border: 1px solid #389892;
	background: #04244e;

	border: 1px solid rgba(0, 163, 255, 0.5);

	.zyfbItem {
		width: 100%;
		height: 36px;
		color: #4e5969;
		cursor: pointer;
		img {
			width: 22px;
			height: 22px;
			float: left;
			margin: 6px 8px 0 7px;
		}

		span {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 16px;
			// color: #ffffff;
			// color: #4e5969;
			color: #b0e0ff;

			line-height: 36px;
			text-align: center;
			font-style: normal;
		}
	}
	.zyfbItem:hover {
		// background: rgba(108, 163, 255, 0.3);
		// background: #30a0b5;
		background: #178dfa;

		span {
			color: #fff;
		}
	}
	.jgzzActive {
		// background: rgba(108, 163, 255, 0.3);
		// background: #30a0b5;
		background: #178dfa;

		span {
			color: #fff;
		}
	}
}
.njfbBox2 {
	width: 168px;
	height: 336px;
	// background: url(~@/assets/bjnj/mapBg2.png) no-repeat center / 100% 100%;
	position: absolute;
	// top: 346px;
	// left: 665px;
	z-index: 1003;
	padding: 3px;
	// background: #d3e7e9;
	// border: 1px solid #389892;
	background: #04244e;

	border: 1px solid rgba(0, 163, 255, 0.5);

	.zyfbItem {
		width: 100%;
		height: 36px;
		cursor: pointer;
		img {
			width: 22px;
			height: 22px;
			float: left;
			margin: 6px 8px 0 7px;
		}

		span {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 16px;
			// color: #ffffff;
			// color: #4e5969;
			color: #b0e0ff;

			line-height: 36px;
			text-align: center;
			font-style: normal;
		}
	}
	.zyfbItem:hover {
		// background: rgba(108, 163, 255, 0.3);
		// background: #30a0b5;
		background: #178dfa;

		span {
			color: #fff;
		}
	}
	.jgzzActive {
		// background: rgba(108, 163, 255, 0.3);
		// background: #30a0b5;
		background: #178dfa;

		span {
			color: #fff;
		}
	}
}

.left,
.right {
	::v-deep {
		.el-carousel {
			width: 450px;
			height: 920px;

			.el-carousel__indicators {
				.el-carousel__indicator {
					&.is-active {
						.el-carousel__button {
							height: 4px;
							background-color: #49e6ff;
						}
					}

					.el-carousel__button {
						height: 4px;
						background-color: rgba(255, 255, 255, 0.4);
						border-radius: 2px;
					}
				}
			}
		}
	}
}

.right {
	::v-deep {
		.el-carousel {
			margin-left: auto;
		}
	}
}

::v-deep .el-carousel--horizontal {
	width: 450px;
	overflow-x: hidden;
}

.map_box {
	position: absolute;
	width: 1920px;
	height: 1080px;
	top: 0px;
	left: 0;
	z-index: 999;
	// border: 1px solid red;
}

.mkss {
	width: 44px;
	height: 44px;
	position: absolute;
	bottom: 735px;
	right: 540px;
	z-index: 999;
	background: url(~@/assets/map/ss2.png) no-repeat center / 100% 100%;

	.mkss2 {
		font-size: 16px;
		color: #fff;
		opacity: 0;
	}
}

.mkss1 {
	width: 44px;
	height: 44px;
	position: absolute;
	bottom: 735px;
	right: 540px;
	z-index: 999;
	background: url(~@/assets/map/ss1.png) no-repeat center / 100% 100%;

	.mkss2 {
		font-size: 16px;
		color: #fff;
		opacity: 0;
	}
}
/* 样式用于控制滚动容器的高度、宽度、边框和滚动效果等 */
.introduce {
	// width: 500px;
	// height: 200px;
	overflow: hidden;
	position: relative;
}

/* 样式用于控制滚动文本的位置和颜色 */
.introduce p {
	position: absolute;
	top: 0;
	left: 0;
	margin: 0;
	padding: 10px;
	font-size: 16px;
}

.njtlBox {
	width: 44px;
	height: 44px;
	position: absolute;
	bottom: 685px;
	right: 539px;
	z-index: 1099;
	img {
		width: 44px;
		height: 44px;
	}
}
.ssBox {
	width: 44px;
	height: 44px;
	position: absolute;
	bottom: 585px;
	right: 539px;
	z-index: 1099;
	img {
		width: 44px;
		height: 44px;
	}
}
.sxBox {
	width: 44px;
	height: 44px;
	position: absolute;
	bottom: 635px;
	right: 539px;
	z-index: 1099;
	img {
		width: 44px;
		height: 44px;
	}
}
.ssPop {
	width: 308px;
	position: absolute;
	// top: 401px;
	// right: 589px;
	z-index: 1099;
	.ssPop1 {
		width: 308px;
		height: 43px;
		input {
			float: left;
			width: 256px;
			height: 43px;
			// background: linear-gradient(180deg, rgba(17, 77, 168, 0) 0%, rgba(36, 132, 212, 0.5) 100%), rgba(2, 52, 114, 0.8);
			background: #fff;

			box-shadow: inset 0px 0px 2px 0px #57afff;
			border-radius: 2px;
			// border: 1px solid #0a5eaa;
			border: 1px solid #a6d2d3;

			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 16px;
			color: #5e5d5d;
			line-height: 43px;
			// text-shadow: 0px 2px 3px rgba(0, 48, 72, 0.5);
			text-shadow: 0px 2px 3px #a6d2d3;

			text-align: left;
			font-style: normal;
			padding-left: 20px;
		}
		.ssImgBox {
			float: left;
			width: 52px;
			height: 43px;
			line-height: 43px;
			// background: linear-gradient(180deg, rgba(17, 77, 168, 0) 0%, rgba(36, 132, 212, 0.5) 100%), #1373d9;
			background: #fff;
			border-radius: 2px;
			// border: 1px solid #0a5eaa;
			border: 1px solid #a6d2d3;

			img {
				vertical-align: middle;
				margin-top: -3px;
			}
		}
	}
	.ssPop2 {
		width: 257px;
		height: 270px;
		// background: linear-gradient(180deg, rgba(17, 77, 168, 0) 0%, rgba(36, 132, 212, 0.5) 100%), rgba(2, 52, 114, 0.8);
		background: #fff;

		box-shadow: inset 0px 0px 2px 0px #57afff;
		border-radius: 2px;
		// border: 1px solid #0a5eaa;
		border: 1px solid #a6d2d3;

		overflow: auto;
		/* 隐藏全局滚动条 */
		&::-webkit-scrollbar {
			display: none;
		}

		.ssList {
			width: 257px;
			height: 45px;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 16px;
			// color: #c2ddfc;
			color: #5e5d5d;
			line-height: 40px;
			// text-shadow: 0px 2px 3px rgba(0, 48, 72, 0.5);
			text-shadow: 0px 2px 3px #a6d2d3;

			text-align: left;
			font-style: normal;
			padding-left: 20px;
		}
	}
}
.njtlList {
	// width: 275px;
	// height: 196px;
	position: absolute;
	bottom: 305px;
	right: 505px;
	z-index: 1099;
	// background: url(~@/assets/bjnj/njtlBg.png) no-repeat center / 100% 100%;
	// padding-top: 33px;
	padding: 10px;

	// background: #d3e7e9;
	// background: #bfe2e4;

	background: #04244e;

	// border: 2px solid #dcedef;
	border: 2px solid rgba(0, 163, 255, 0.5);
	border-radius: 20px;

	-webkit-transition: all 0.5s ease-in;
	-moz-transition: all 0.5s ease-in;
	transition: all 0.5s ease-in;

	.njtlItem {
		width: 100%;
		height: 30px;
		margin-bottom: 10px;
		img {
			width: 30px;
			height: 30px;
			float: left;
		}
		.njtlName {
			float: left;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			// color: #ffffff;
			font-size: 16px;
			// color: #30a0a9;
			color: #b0e0ff;
			line-height: 30px;
			text-align: left;
			font-style: normal;
			margin-left: 5px;
		}

		&:last-child {
			margin-bottom: 0;
		}
	}
}
.wgsj_fx {
	width: 100%;

	// height: 100%;
	.select_m {
		position: absolute;
		right: 0;
		top: -50px;
		line-height: 30px;
		font-size: 16px;
		font-family: PingFangSC, PingFang SC;
		color: #caecff;
		text-shadow: 0px 0px 1px #00132e;

		::v-deep .el-dropdown button {
			font-family: PingFangSC, PingFang SC;
			font-size: 16px;
			color: #caecff;
			line-height: 21px;
			text-align: center;
			font-style: normal;

			// width: 100px;
			height: 30px;
			// background: url(~@/assets/mskx/icon_drop.png) no-repeat center / 100% 100%;
			background: transparent;
			border: none;
			padding: 0 0 0 5px;
		}

		::v-deep .el-icon-arrow-down:before {
			// content: '\e790' !important;
			color: #caecff;
		}

		::v-deep .el-popper[x-placement^='bottom'] .popper__arrow:after {
			display: none;
		}

		::v-deep .el-popper .popper__arrow:after {
			border-bottom-color: #185493 !important;
		}
	}
}
::v-deep .el-dropdown {
	color: #caecff;
	font-family: YouSheBiaoTiHei;
	font-size: 16px;
	line-height: 21px;
	text-align: right;
	font-style: normal;
	line-height: 40px;
	right: 0;
	position: absolute;
}
::v-deep .el-dropdown-menu {
	// background: #00173b;
	position: absolute;
	left: 0;
	z-index: 10;
	padding: 10px 0;
	margin: 5px 0;
	// border: 1px solid #2b7bbb;
	border-radius: 4px;
	background-color: #c6e2e7 !important;
	border: 1px solid #d2f2ea !important;
}
.ai_waring {
	width: 1525px;
	height: 850px;
	background: rgba(9, 19, 34, 0.95);
	box-shadow: 0px 0px 22px 0px rgba(11, 54, 138, 0.35), 0px 2px 8px 0px rgba(0, 0, 0, 0.7),
		inset 0px 0px 8px 0px rgba(17, 146, 214, 0.35);
	border-radius: 11px;
	border: 1px solid #015c8c;
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	z-index: 1200;
	.title {
		margin: 0 auto 36px;
		width: 100%;
		height: 72px;
		line-height: 72px;
		background: url(~@/assets/map/dialog/title7.png) no-repeat center / 100% 100%;
		display: grid;
		place-items: center;
		span {
			display: inline-block;
			font-size: 36px;
			font-family: PingFangSC, PingFang SC;
			background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
			background-clip: text;
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}
	}

	.close_btn {
		position: absolute;
		width: 23px;
		height: 23px;
		top: 24px;
		right: 27px;
		background: url(~@/assets/map/dialog/btn3.png) no-repeat center / 100% 100%;
		cursor: pointer;
	}
	.content {
		padding: 0 30px;
		/deep/ #ezuikit-player-wrap {
			min-height: 600px;
		}
		.table_box {
			margin-top: 12px;
			overflow: hidden;
		}

		.tableBox {
			height: 41px;
			margin-bottom: 21px;
			.tableItem {
				float: left;
				width: 152px;
				height: 41px;
				background: url(~@/assets/map/dialog/btn5.png) no-repeat center / 100% 100%;
				margin-right: 10px;
				font-family: PingFangSC, PingFang SC;
				font-size: 22px;
				color: #ffffff;
				line-height: 41px;
				text-align: center;
				font-style: normal;
				text-transform: none;
				span {
					background: linear-gradient(180deg, #ffffff 0%, #0079ff 100%);
					background-clip: text;
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
				}
			}
		}
	}
}
</style>
