<template>
	<div class="multi-dialog">
		<div class="dialog-head">
			<div class="dialog-head-left">
				<div class="theme">主题</div>
				<div class="invited" @mouseover="meetingBoardShow = true" @mouseleave="meetingBoardShow = false">
					<span>{{ roomName1 }}</span>
					<div class="meeting-show-board" v-show="meetingBoardShow">
						<div class="meeting-show-board-title">{{ roomName1 }}</div>
						<div class="meeting-show-board-item">
							<div class="meeting-show-board-item-title">会议ID</div>
							<div class="meeting-show-board-item-value">
								<span id="roomNum">{{ roomId }}</span>
								<i class="icon clip-icon" @click="clipTextToBoard"></i>
							</div>
						</div>
						<div class="meeting-show-board-item">
							<div class="meeting-show-board-item-title">组织者</div>
							<div class="meeting-show-board-item-value">
								<span>{{ mobile }}</span>
							</div>
						</div>
					</div>
				</div>
				<div class="call-duration">
					[通话时长<span style="color: #1EFF00;">{{ meetingDuration }}</span
					>]
				</div>
			</div>
			<!-- <div class="full-btns">
				<div class="full-btn" @click="minumDialog"></div>
				<div class="full-btn" v-show="false"></div>
				<div class="full-btn" @click="hangOff"></div>
			</div> -->
		</div>
		<div class="dialog-contain">
			<div id="room-contain" :class="['meeting-contain', layout]">
				<!-- <div v-show="camStatus === 'started'" class="local-stream-container participant">
				</div> -->
				<div id="local" class="local-stream-content participant">
					<div v-if="!localCameraShow" class="board">
						<div class="board-img"></div>
					</div>
					<div :id="`describe-${tel}`" class="describe">
						<div :id="`microphone-${tel}`" :class="['microphone', microphonePic]"></div>
						<div :id="`${tel}`" class="identity">{{ username }}(我)</div>
					</div>
				</div>
				<!-- <div class="remote-container participant" v-if="remoteUsersViews && remoteUsersViews.length != 0"> -->
				<div v-for="item in remoteUsersViews" :key="item" :id="item" class="remote-stream-container participant">
					<div v-if="remoteStopVideoUserIdList.has(item.split('_main')[0])" class="board">
						<div class="board-img"></div>
					</div>
					<div :id="`describe-${item.split('_')[1]}`" class="describe">
						<div
							:id="`microphone-${item.split('_')[1]}`"
							:class="[
								'microphone',
								remoteStartAudioUserIdList.has(item.split('_main')[0]) ? 'microphone-active' : 'microphone-inactive',
							]"
						></div>
						<div :id="`${item.split('_')[1]}`" class="identity">{{ item.split('_')[2] }}</div>
					</div>
				</div>
				<!-- </div> -->
			</div>
			<div class="member-manage" v-if="isExpand">
				<div class="member-manage-top">
					<el-input v-model="filterText" placeholder="请输入名字进行搜索">
						<i slot="suffix" class="el-input__icon el-icon-search"></i>
					</el-input>
				</div>
				<div class="member-manage-contain">
					<div class="member-list">
						<div class="member-list-group">
							<div class="avatar"></div>
							<div class="name" :title="username">{{ username }}</div>
						</div>
						<div class="member-list-group">
							<div
								:class="['member-icon', { 'mic-on': localMicrophoneShow }, { 'mic-off': !localMicrophoneShow }]"
								@click="changeLocalMicroStatus"
							></div>

							<div class="member-icon"></div>
						</div>
					</div>
					<div class="member-list" v-for="(item, index) of tongxunluList" :key="'m' + index">
						<div class="member-list-group">
							<div class="avatar"></div>
							<div class="name" :title="item.split('_')[2]">{{ item.split('_')[2] }}</div>
						</div>
						<div class="member-list-group" v-if="role == 'host'">
							<div
								:class="[
									'member-icon',
									{ 'mic-on': remoteStartAudioUserIdList.has(item.split('_main')[0]) },
									{ 'mic-off': !remoteStartAudioUserIdList.has(item.split('_main')[0]) },
								]"
								@click="changeParticipantMicrophone(item)"
							></div>
							<div class="member-icon more" @click.stop="openMoreDialog(item, $event)"></div>
						</div>
					</div>
				</div>
				<!-- <div class="member-manage-contain">
					<div class="member-list" v-for="(item, index) of memberList" :key="'m' + index">
						<div class="member-list-group">
							<div class="avatar"></div>
							<div class="name" :title="item.name">{{ item.name }}</div>
						</div>
						<div class="member-list-group">
							<div
								:class="['member-icon', { 'mic-on': item.isMicrophoneEnabled }, { 'mic-off': !item.isMicrophoneEnabled }]"
								@click="changeParticipantMicrophone(item)"
							></div>
							<div
								class="member-icon more"
								v-if="isMoreOptionShow(item.identity)"
								@click.stop="openMoreDialog(item.identity, $event)"
							></div>
							<div class="member-icon" v-else></div>
						</div>
					</div>
				</div> -->
				<div class="member-manage-bottom" v-if="role === 'host'">
					<div class="member-manage-btn" @click="muteAllMicrophone">全员静音</div>
					<div class="member-manage-btn" @click="closeAllCamera">全员关闭摄像头</div>
				</div>
				<moreOptionDialog
					v-if="moreDialogShow"
					:optionList="moreDialogOptionList"
					:identity="optionIdentity"
					:offset="optionDialogOffset"
					@closeCamera="closeParticipantCamera"
					@changeParticipantName="changeParticipantName"
					@removeParticipant="removeParticipant"
				></moreOptionDialog>
			</div>
			<InstantMessage
				v-if="isInstantMessageShow"
				ref="chatComponent"
				:messageList="messageList"
				:chatStyle="chatStyle"
				:isMonitorIn="true"
				@messageSend="sendMessage"
			></InstantMessage>
		</div>
		<div class="dialog-foot">
			<div class="point-foot-group">
				<div class="foot-btn" v-if="localMicrophoneShow" @click.stop="changeLocalMicroStatus">
					<div class="foot-btn-icon mic-on"></div>
					<div>麦克风</div>
				</div>
				<div class="foot-btn" v-else @click.stop="changeLocalMicroStatus">
					<div class="foot-btn-icon mic-off"></div>
					<div>麦克风</div>
				</div>
				<div class="foot-btn" v-if="localCameraShow" @click.stop="changeLocalCameraStatus">
					<div class="foot-btn-icon cam-on"></div>
					<div>摄像头</div>
				</div>
				<div class="foot-btn" v-else @click.stop="changeLocalCameraStatus">
					<div class="foot-btn-icon cam-off"></div>
					<div>摄像头</div>
				</div>
				<div class="foot-btn" @click.stop="isExpand = !isExpand">
					<div class="foot-btn-icon member"></div>
					<div>成员管理</div>
				</div>
				<div class="foot-btn" @click.stop="chooseInviteWay($event)">
					<div class="foot-btn-icon invite"></div>
					<div>邀请</div>
				</div>
			</div>
			<div class="point-foot-group">
				<div class="foot-btn" @click="hangOff">
					<div class="foot-btn-icon hang-off"></div>
					<div>挂断</div>
				</div>
			</div>
			<inviteOptionDialog
				v-if="inviteOptionShow"
				:isFull="isFull"
				@openAddressBook="openAddressBook"
				@openMessageBoard="openMessageBoard"
			></inviteOptionDialog>
		</div>
		<changeNameDialog
			v-if="changeNameShow"
			:identity="changingIdentity"
			@updateParticipantName="updateParticipantName(arguments)"
			@changeNameDialogClose="changeNameShow = false"
		></changeNameDialog>
		<messageInviteDialog
			v-if="messageInviteShow"
			@sendMessageInvite="sendMessageInvite"
			@messageInviteClose="messageInviteShow = false"
		></messageInviteDialog>
		<addressBook v-model="addressBookShow" type="invite" @inviteToJoin="inviteToJoin"></addressBook>
		<!-- 请求入会弹窗 -->
		<AgreeBoard v-model="AgreeBoardShow" :inviteBoardData="AgreeInfo" @refuse="refuseAgree" @accept="acceptAgree" v-drag></AgreeBoard>
	</div>
</template>

<script>
import { sendInviteMessage, generateWxLink, getSig, joinRoom, saveRoom, getRoom, endRoom, quitRoom } from '@/api/bjnj/zhdd.js'
import util from '@/libs/util.js'
import rtc from '@/components/mixins/rtc.js'
import TRTC from 'trtc-sdk-v5'
import LibGenerateTestUserSig from '@/utils/lib-generate-test-usersig.min.js'
import AgreeBoard from '@/views/leader/components/service/agreeBoard.vue'
import { isMobile } from '@/utils/utils'
let meetingInterval = null
export default {
	name: 'multipleMeeting',
	mixins: [rtc],
	components: {
		moreOptionDialog: () => import('./components/service/moreOptionDialog.vue'),
		inviteOptionDialog: () => import('./components/service/inviteOptionDialog.vue'),
		changeNameDialog: () => import('./components/service/changeNameDialog.vue'),
		messageInviteDialog: () => import('./components/service/messageInviteDialog.vue'),
		addressBook: () => import('./components/service/addressBook.vue'),
		InstantMessage: () => import('./components/service/instantMessage.vue'),
		AgreeBoard,
	},
	props: {},
	data() {
		return {
			// 用户角色 host-会议创建者 user-普通入会者
			role: 'user',
			// 通话时长
			duration: 0,
			// 房间号
			roomNum: '',
			isExpand: false,
			isFull: false,
			// 房间名
			roomName: '',
			// 组件内client实例
			liveClient: null,
			liveClient2: null,
			// 本地用户设备状态
			localMicrophoneShow: true,
			localCameraShow: true,
			// 成员管理
			filterText: '',
			moreDialogShow: false,
			optionDialogOffset: {},
			optionIdentity: '',
			// 邀请
			inviteOptionShow: false,
			inviteOptionDialogOffset: {},
			// 修改用户名称
			changeNameShow: false,
			changingIdentity: '',
			// 发送短信邀请
			messageInviteShow: false,
			// 打开通讯录
			addressBookShow: false,
			// 即时消息
			// messageList: [
			// 	{
			// 		fromName: '系统消息',
			// 		content: '会议内文本聊天已启用',
			// 	},
			// ],
			chatStyle: {
				width: '295px',
				height: '184px',
			},
			isInstantMessageShow: false,
			meetingBoardShow: false,
			messageReceivedHandlerBinder: null,
			roomId: 0,
			sdkAppId: 1600089532,
			sdkSecretKey: '3a6edb36029177424289091eb1d729befde4297a6426c3d6f9e8df97ada4cb0b',
			// userId: 'user_651265',
			userSig: null,
			AgreeInfo: '',
			AgreeBoardShow: false,
			realRoomId: NaN,
			previewTimeInterval: null,
			previewTimeInterval2: null,
			isInvite: false,
			isJoin: false,
			roomName1: '',
			microphoneStatus: false,
			cameraStatus: false,
			launchInviteList: [],
			username: '',
			mobile: '',
			userId: '',
		}
	},
	mounted() {
		window.addEventListener('beforeunload', () => {
			this.hangOff()
			localStorage.setItem('PCmeetingClosed', Date.now())
		})
		this.roomId = Number(this.getUrlParam('Id')) ? Number(this.getUrlParam('Id')) : parseInt(Math.random() * 1000000, 10)
		this.roomName1 = this.getUrlParam('N') || ''
		this.username = this.getUrlParam('U') || ''
		this.mobile = this.getUrlParam('T') || ''
		this.userId = 'user_' + this.mobile + '_' + this.username
		this.isInvite = this.getUrlParam('I') == 0 ? false : true
		this.isJoin = this.getUrlParam('J') == 0 ? false : true
		this.microphoneStatus = this.getUrlParam('m') == 'true' ? true : false
		this.cameraStatus = this.getUrlParam('c') == 'true' ? true : false
		this.launchInviteList = JSON.parse(sessionStorage.getItem('launchInviteList'))
		sessionStorage.removeItem('launchInviteList') // 使用后清除
		this.trtc = TRTC.create()
		this.startMeeting()
	},
	computed: {
		microphonePic() {
			return this.localMicrophoneShow ? 'microphone-active' : 'microphone-inactive'
		},
		tel() {
			return this.$store.state.mobile
		},
		// userId() {
		// 	return 'user_' + this.$store.state.mobile + '_' + this.$store.state.username
		// },
		// roomId() {
		// 	return this.meetingData.isInvite
		// 		? Number(this.meetingData.roomId)
		// 		: this.meetingData.isJoin
		// 		? Number(this.meetingData.roomNum)
		// 		: parseInt(Math.random() * 1000000, 10)
		// },
		meetingDuration() {
			let minute, second
			if (Math.floor(this.duration / 60) > 0) {
				minute = Math.floor(this.duration / 60) <= 9 ? '0' + Math.floor(this.duration / 60) : Math.floor(this.duration / 60)
			} else {
				minute = '00'
			}
			if (Math.floor(this.duration % 60) > 0) {
				second = Math.floor(this.duration % 60) <= 9 ? '0' + Math.floor(this.duration % 60) : Math.floor(this.duration % 60)
			} else {
				second = '00'
			}
			return `${minute}分${second}秒`
		},
		layout() {
			console.log(this.remoteUsersViews.length, '来了几个人？？', this.remoteUsersViews)
			if (this.remoteUsersViews.length <= 1) {
				return 'layout1'
			} /* else if (this.remoteUsersViews.length <= 2) {
				return 'layout2'
			} */ else if (this.remoteUsersViews.length <= 3) {
				return 'layout3'
			} else if (this.remoteUsersViews.length <= 5) {
				return 'layout4'
			} else {
				return 'layout5'
			}
			// if (this.participants.length <= 1) {
			//   return 'layout1'
			// } else if (this.participants.length <= 2) {
			//   return 'layout2'
			// } else if (this.participants.length <= 4) {
			//   return 'layout3'
			// } else if (this.participants.length <= 6) {
			//   return 'layout4'
			// } else {
			//   return 'layout5'
			// }
		},
		moreDialogOptionList() {
			if (this.role === 'host') {
				// return ['关闭摄像头', '修改名称', '移出会议'] //修改名称的功能暂时不做
				return ['关闭摄像头', '移出会议']
			} else {
				// return ['修改名称']
				return []
			}
		},

		// username() {
		// 	return this.$store.state.username
		// },
		// mobile() {
		// 	return this.$store.state.mobile
		// },
		tongxunluList() {
			return this.remoteUsersViews.filter((d) => {
				return d.indexOf(this.filterText) > -1
			})
		},
	},
	watch: {
		async value(newVal) {
			if (newVal) {
				this.duration = 0
				this.roomNum = ''
				this.roomName = ''
				this.participants = []
				this.messageList = []
				this.filterText = ''
				this.optionIdentity = ''
				this.messageReceivedHandlerBinder = null

				this.startMeeting()
			}
		},
		camStatus(newVal) {
			if (newVal === 'stopped') this.localCameraShow = false
		},
		micStatus(newVal) {
			if (newVal === 'stopped') this.localMicrophoneShow = false
		},
		roomStatus(newVal) {
			if (newVal === 'exited') {
				this.closeDialog()
				this.isInstantMessageShow = false
			}
			if (this.isBeiTi) {
				quitRoom({ roomNumber: this.realRoomId, mobile: this.$store.state.mobile }).then(async (res) => {
					this.isBeiTi = false
					this.closeDialog()
					this.isInstantMessageShow = false
					if (this.allTracks && this.allTracks.length != 0) {
						this.$emit('clearTracks')
					}
					if (this.previewTimeInterval) {
						await clearInterval(this.previewTimeInterval)
						this.previewTimeInterval = null
					}
					if (this.previewTimeInterval2) {
						await clearInterval(this.previewTimeInterval2)
						this.previewTimeInterval2 = null
					}
					setTimeout(() => {
						window.close()
					}, 3000)
				})
			}
		},
	},
	// beforeDestroy() {
	//   if(this.liveClient) {
	//     this.clearAllInterval()
	//     this.clearTrack();
	//     this.dispatchLiveClientEvent()
	//     this.leaveRoom()
	//   }
	// },
	methods: {
		getUrlParam(key) {
			const url = decodeURI(window.location.href.replace(/^[^?]*\?/, ''))
			const regexp = new RegExp(`(^|&)${key}=([^&#]*)(&|$|)`, 'i')
			const paramMatch = url.match(regexp)

			return paramMatch ? paramMatch[2] : null
		},
		async startMeeting(e) {
			console.log(this.roomId, 'RoomId', this.launchInviteList)
			this.realRoomId = this.roomId
			if (!this.isInvite) {
				getSig({
					userid: this.userId,
					expire: 1800, //链接失效时间单位s
					roomid: this.roomId,
					privilegeMap: 255,
				}).then(async (res) => {
					this.userSig = res.data
					console.log(this.cameraStatus, this.microphoneStatus, 'this.cameraStatus')
					this.localCameraShow = this.cameraStatus
					this.localMicrophoneShow = this.microphoneStatus

					await this.enterRoom()
					if (this.cameraStatus) this.handleStartLocalVideo()
					if (this.microphoneStatus) this.handleStartLocalAudio()
					if (!this.isJoin) {
						this.liveClient = new WebSocket(
							'wss://dpzs.camtc.cn/ws/meeting-websocket/' + this.roomId + '?phone=' + this.$store.state.mobile,
							// 'ws://172.16.50.211:9019/meeting-websocket/' + this.roomId + '?phone=' + this.$store.state.mobile,
						)
						this.initClientEvent()
						if (this.previewTimeInterval) {
							clearInterval(this.previewTimeInterval)
							this.previewTimeInterval = null
						}
						this.previewTimeInterval = setInterval(() => {
							console.log('9885')
							let data = { acceptUser: 'root', type: 0 }
							this.liveClient.send(JSON.stringify(data))
						}, 20000)
						saveRoom({
							roomNumber: this.roomId,
							roomName: this.roomName1,
							mobile: this.$store.state.mobile,
						}).then((res) => {
							this.role = 'host'
						})
					} else {
						this.role = 'user'
						getRoom(this.roomId).then((res) => {
							this.roomName1 = res.data.roomName
						})
						joinRoom({
							roomNumber: this.roomId,
							mobile: this.$store.state.mobile,
							name: this.$store.state.username,
						}).then((res) => {})
					}
					this.generateInviteLink()
					meetingInterval = setInterval(() => {
						this.duration++
					}, 1000)
					if (this.launchInviteList.length != 0) {
						//e是电话号码
						console.log('16565454564')
						this.launchInviteList.forEach((d) => {
							if (d.mobile) {
								// const useSig = userSigGenerator.genTestUserSig('user_' + d.mobile)
								let params = {
									toUser: d.mobile,
									// toUser: '17634406498',
									message: encodeURI(
										`${location.origin}${location.pathname}#/invite?roomId=${this.roomId}&userId=user_${d.mobile}&roomName=${this.roomName1}`,
									),
								}
								sendInviteMessage(params).then((res) => {})
							}
						})
						let data = {
							userIds: this.launchInviteList.map((d) => {
								return d.mobile
							}),
							message: this.roomId + '+-' + this.username + '+-' + this.roomName1,
						}
						this.$emit('sendNotice', JSON.stringify(data))
					}
				})
			} else {
				getSig({
					userid: this.userId,
					expire: 1800,
					roomid: this.roomId,
					privilegeMap: 255,
				}).then(async (res) => {
					console.log('您您你你你你你你你你你那那那', this.roomId, res)
					this.userSig = res.data
					console.log(this.userId, this.userSig, this.roomId, this.roomName1, 'this.cameraStatus')

					this.role = 'user'
					this.localCameraShow = this.cameraStatus
					this.localMicrophoneShow = this.microphoneStatus
					await joinRoom({
						roomNumber: this.roomId,
						mobile: this.$store.state.mobile,
						name: this.$store.state.username,
					}).then((res) => {
						this.liveClient2 = new WebSocket(
							'wss://dpzs.camtc.cn/ws/meeting-websocket/' + this.roomId + '?phone=' + this.$store.state.mobile,
							// 'ws://172.16.50.211:9019/meeting-websocket/' + this.roomId + '?phone=' + this.$store.state.mobile,
						)
						this.initClientEvent2()
						if (this.previewTimeInterval2) {
							clearInterval(this.previewTimeInterval2)
							this.previewTimeInterval2 = null
						}
						this.previewTimeInterval2 = setInterval(() => {
							console.log('9885')
							let data = { acceptUser: 'root', type: 0 }
							this.liveClient2.send(JSON.stringify(data))
						}, 20000)
					})
					await this.enterRoom()
					if (this.cameraStatus) this.handleStartLocalVideo()
					if (this.microphoneStatus) this.handleStartLocalAudio()
					meetingInterval = setInterval(() => {
						this.duration++
					}, 1000)
				})
			}
			this.isInstantMessageShow = true
		},
		initClientEvent2() {
			if (!this.liveClient2) {
				console.log('客户端未初始化')
				return
			}
			// 连接打开时触发
			this.liveClient2.onopen = (event) => {
				console.log('连接已建立')
			}
			// 接收消息时触发
			this.liveClient2.onmessage = (event) => {
				console.log('收到消息:', event)
				if (event.data.indexOf('disband') > -1) {
					this.outroom()
					this.$message({
						message: '房主解散了会议',
						type: 'warning',
					})
				} else if (event.data == 'agree') {
					this.joinMeetingShow = false
					this.multipleMeetingShow = true
				} else if (event.data == 'refuse') {
					this.$message({
						message: '管理员拒绝了您的入会请求！',
						type: 'warning',
					})
					this.joinMeetingShow = false
				}
			}
			this.liveClient2.onclose = (event) => {
				this.liveClient2 = null
			}
		},
		generateInviteLink() {
			let sdkAppId = 1600089532
			let sdkSecretKey = '3a6edb36029177424289091eb1d729befde4297a6426c3d6f9e8df97ada4cb0b'
			const inviteUserId2 = `user_17634406496`
			const inviteUserId3 = `user_17634406495`
			const inviteUserId4 = `user_17634406494`
			const userSigGenerator = new LibGenerateTestUserSig(sdkAppId, sdkSecretKey, 604800)
			const inviteUserSig2 = userSigGenerator.genTestUserSig(inviteUserId2)
			const inviteUserSig3 = userSigGenerator.genTestUserSig(inviteUserId3)
			const inviteUserSig4 = userSigGenerator.genTestUserSig(inviteUserId4)
			console.log(
				encodeURI(
					`${location.origin}${location.pathname}#/invite?sdkAppId=${sdkAppId}&userSig=${inviteUserSig2}&roomId=${this.roomId}&userId=${inviteUserId2}&roomName=${this.roomName1}`,
				),
				encodeURI(
					`${location.origin}${location.pathname}#/invite?sdkAppId=${sdkAppId}&userSig=${inviteUserSig3}&roomId=${this.roomId}&userId=${inviteUserId3}&roomName=${this.roomName1}`,
				),
				encodeURI(
					`${location.origin}${location.pathname}#/invite?sdkAppId=${sdkAppId}&userSig=${inviteUserSig4}&roomId=${this.roomId}&userId=${inviteUserId4}&roomName=${this.roomName1}`,
				),
				'ttttttttttttt',
			)
		},
		initClientEvent() {
			if (!this.liveClient) {
				console.log('客户端未初始化')
				return
			}
			// 连接打开时触发
			this.liveClient.onopen = (event) => {
				console.log('连接已建立')
				// 发送消息
				// this.liveClient.send('Hello Server!')
			}
			// 接收消息时触发
			this.liveClient.onmessage = (event) => {
				console.log('收到消息:', event)
				this.AgreeInfo = event.data
				this.AgreeBoardShow = true
			}
			this.liveClient.onclose = (event) => {
				this.liveClient = null
			}
		},
		sendMessage(e) {
			this.messageList.push({
				// sender: 'me',
				// content: this.newMessage,
				// timestamp: new Date(),
				cmdId: 1,
				data: e,
				seq: 11,
				userId: this.userId,
				timestamp: new Date(),
			})
			this.trtc.sendCustomMessage({
				cmdId: 1,
				data: new TextEncoder().encode(e).buffer,
				timestamp: new Date(),
			})
		},
		isMoreOptionShow(identity) {
			if (this.role === 'host') {
				return true
			} else {
				return false
			}
		},

		// 修改本地与会者状态
		async changeLocalMicroStatus() {
			// await this.liveClient.changeMicrophoneStatus()

			const microPhoneList = await TRTC.getMicrophoneList()
			if (microPhoneList[0]) {
				if (this.localMicrophoneShow) this.handleStopLocalAudio()
				else this.handleStartLocalAudio()
				this.localMicrophoneShow = !this.localMicrophoneShow
			} else {
				this.$messageNew.message('warning', {
					message: '未检测到设备存在麦克风',
				})
			}
		},
		async changeLocalCameraStatus() {
			// await this.liveClient.changeCameraStatus()
			const cameraList = await TRTC.getCameraList()
			if (cameraList[0]) {
				if (this.localCameraShow) this.handleStopLocalVideo()
				else this.handleStartLocalVideo()
				this.localCameraShow = !this.localCameraShow
			} else {
				this.$messageNew.message('warning', {
					message: '未检测到设备存在摄像头',
				})
			}
		},

		// 判断与会者浏览器是否有对应权限
		async hasRelatedPermissions() {
			if (navigator?.permissions) {
				try {
					let cameraPermissionStatus = null,
						microphonePermissionStatus = null
					cameraPermissionStatus = await navigator.permissions.query({
						name: 'camera',
					})
					microphonePermissionStatus = await navigator.permissions.query({
						name: 'microphone',
					})
					console.log('cameraPermission', cameraPermissionStatus.state)
					console.log('microphonePermission', microphonePermissionStatus.state)
					if (cameraPermissionStatus.state === 'granted' && microphonePermissionStatus.state === 'granted') {
						console.log('关键权限已授权')
						return true
					} else {
						console.log('关键权限未授权')
						return false
					}
				} catch (error) {
					console.log('当前浏览器不支持permissionAPI')
					return true
				}
			} else {
				return true
			}
		},

		reportSuccessEvent(name) {},
		reportFailedEvent(name, error, type = 'rtc') {},
		// 通讯录邀请
		inviteToJoin(e) {
			if (e.length > 0) {
				//e是电话号码
				console.log('16565454564', e)
				e.forEach((d) => {
					if (d.mobile) {
						let params = {
							toUser: d.mobile,
							// toUser: '17634406498',
							message: encodeURI(
								`${location.origin}${location.pathname}#/invite?roomId=${this.roomId}&userId=user_${d.mobile}&roomName=${this.roomName1}`,
							),
						}
						sendInviteMessage(params).then((res) => {
							// this.$messageNew.message('success', {
							// 	message: '短信发送成功',
							// })
							// this.messageInviteShow = false
						})
					}
				})
				// sendNotice({
				// 	userIds: this.launchInviteList.map((d) => {
				// 		return d.mobile
				// 	}),
				// 	message: this.roomId + '+-' + this.username + '+-' + this.roomName1,
				// }).then(async (res) => {
				// 	console.log('成功调用通知接口')
				// })

				let data = {
					userIds: e.map((d) => {
						return d.mobile
					}),
					message: this.roomId + '+-' + this.username + '+-' + this.roomName1,
				}
				// this.$emit('sendNotice', JSON.stringify(data))
				this.$bus.emit('PCActiveInvite', JSON.stringify(data))
			}
			this.addressBookShow = false
		},
		// 清除组件中的定时器
		clearAllInterval() {
			if (meetingInterval) {
				clearInterval(meetingInterval)
				meetingInterval = null
			}
		},
		// 清除组件中绑定的音视频轨道
		clearTrack() {
			// if (this.trackList.length > 0) {
			// 	this.trackList.forEach((track) => {
			// 		track.detach()
			// 		track.stop()
			// 	})
			// }
		},
		// 用户点击挂断
		async hangOff() {
			if (this.role == 'host')
				endRoom(this.realRoomId + '/1').then(async (res) => {
					let data = {
						type: '2', //0心跳 1申请入会，2被解散
						message: 'disband',
						sendUser: this.$store.state.mobile,
						acceptUser: '',
					}
					console.log(data)
					await this.liveClient.send(JSON.stringify(data))

					this.closeDialog()
					this.isInstantMessageShow = false
					if (this.allTracks && this.allTracks.length != 0) {
						this.$emit('clearTracks')
					}
					if (this.previewTimeInterval) {
						clearInterval(this.previewTimeInterval)
						this.previewTimeInterval = null
					}
					if (this.previewTimeInterval2) {
						clearInterval(this.previewTimeInterval2)
						this.previewTimeInterval2 = null
					}
					await this.exitRoom()
				})
			else {
				quitRoom({ roomNumber: this.realRoomId, mobile: this.$store.state.mobile }).then(async (res) => {
					this.closeDialog()
					this.isInstantMessageShow = false
					if (this.allTracks && this.allTracks.length != 0) {
						this.$emit('clearTracks')
					}
					if (this.previewTimeInterval) {
						clearInterval(this.previewTimeInterval)
						this.previewTimeInterval = null
					}
					if (this.previewTimeInterval2) {
						clearInterval(this.previewTimeInterval2)
						this.previewTimeInterval2 = null
					}
					await this.exitRoom()
				})
			}
			window.close()
		},
		outroom() {
			this.closeDialog()
			this.isInstantMessageShow = false
			if (this.allTracks && this.allTracks.length != 0) {
				this.$emit('clearTracks')
			}
			if (this.previewTimeInterval) {
				clearInterval(this.previewTimeInterval)
				this.previewTimeInterval = null
			}
			if (this.previewTimeInterval2) {
				clearInterval(this.previewTimeInterval2)
				this.previewTimeInterval2 = null
			}
			this.exitRoom()
		},
		// 用户管理
		// 控制指定用户麦克风
		changeParticipantMicrophone(e) {
			if (this.remoteStartAudioUserIdList.has(e.split('_main')[0])) {
				this.trtc.sendCustomMessage({
					cmdId: 5, // 1是聊天内容  5关麦克风
					data: new TextEncoder().encode(e).buffer,
				})
			} else {
				this.trtc.sendCustomMessage({
					cmdId: 6, // 1是聊天内容  6开麦克风
					data: new TextEncoder().encode(e).buffer,
				})
			}
		},
		// 点击更多按钮
		openMoreDialog(identity, e) {
			console.log('更多按钮点击', e)
			this.optionDialogOffset = {
				left: e.target.offsetLeft - 32 + 'px',
				top: e.target.offsetTop + 16 + 'px',
			}
			if (this.optionIdentity === identity && this.moreDialogShow) {
				this.moreDialogShow = false
			} else {
				this.moreDialogShow = false
				this.optionIdentity = identity
				this.moreDialogShow = true
			}
		},
		// 修改与会者名称
		changeParticipantName(e) {
			this.moreDialogShow = false
			this.changingIdentity = e
			this.changeNameShow = true
		},
		async updateParticipantName(arg) {
			let [name, identity] = arg
			this.trtc.sendCustomMessage({
				cmdId: 3, // 1是聊天内容  3改名字
				data: new TextEncoder().encode(identity + '_' + name).buffer,
			})
			this.changeNameShow = false
		},
		// 移除与会者
		async removeParticipant(e) {
			this.trtc.sendCustomMessage({
				cmdId: 4, // 1是聊天内容  4踢人
				data: new TextEncoder().encode(e).buffer,
			})
			this.moreDialogShow = false
		},
		// 关闭与会者摄像头
		closeParticipantCamera(e) {
			if (this.remoteStartVideoUserIdList.has(e.split('_main')[0])) {
				this.trtc.sendCustomMessage({
					cmdId: 2, // 1是聊天内容  其余都是指令 2关闭摄像头
					data: new TextEncoder().encode(e).buffer,
				})
			} else {
				this.trtc.sendCustomMessage({
					cmdId: 7, // 1是聊天内容  其余都是指令 7开摄像头
					data: new TextEncoder().encode(e).buffer,
				})
			}
		},
		// 全员禁言
		muteAllMicrophone() {
			// this.remoteStopAudioUserIdList.clear()
			this.remoteStartAudioUserIdList.clear()
			this.remoteUsersViews.length > 0 &&
				this.remoteUsersViews.forEach((d) => {
					this.remoteStopAudioUserIdList.add(d.split('_main')[0])
					this.trtc.sendCustomMessage({
						cmdId: 5, // 1是聊天内容  5关麦克风
						data: new TextEncoder().encode(d).buffer,
					})
				})
		},
		// 全员关闭摄像头
		async closeAllCamera() {
			// this.remoteStopVideoUserIdList.clear()
			this.remoteStartVideoUserIdList.clear()
			if (this.remoteUsersViews.length > 0)
				await this.remoteUsersViews.forEach((d) => {
					this.remoteStopAudioUserIdList.add(d.split('_main')[0])
					this.trtc.sendCustomMessage({
						cmdId: 2, // 1是聊天内容  5关麦克风
						data: new TextEncoder().encode(d).buffer,
					})
				})
			console.log(this.remoteStopVideoUserIdList, this.remoteUsersViews)
		},
		// 点击邀请按钮
		chooseInviteWay() {
			this.inviteOptionShow = !this.inviteOptionShow
		},
		// 打开通讯录
		openAddressBook() {
			this.inviteOptionShow = false
			this.addressBookShow = true
		},
		// 打开短信邀请弹窗
		openMessageBoard() {
			this.inviteOptionShow = false
			this.messageInviteShow = true
		},
		// 发送短信邀请
		sendMessageInvite(e) {
			//e是电话号码
			let params = {
				toUser: e,
				message: encodeURI(
					`${location.origin}${location.pathname}#/invite?roomId=${this.roomId}&userId=user_${e}&roomName=${this.roomName1}`,
				),
			}
			sendInviteMessage(params).then((res) => {
				this.$messageNew.message('success', {
					message: '短信发送成功',
				})
				this.messageInviteShow = false
			})
		},

		async closeDialog() {
			console.log('关闭多人会议弹窗')
			if (this.liveClient) {
				await this.liveClient.close()
			}
			if (this.liveClient2) {
				await this.liveClient2.close()
			}
			this.clearAllInterval()
			this.clearTrack()

			this.$emit('multiMeetingClose')
			this.$emit('input', false)
		},
		clipTextToBoard() {
			let dom = document.getElementById('roomNum')
			if (dom) {
				if (navigator.clipboard) {
					let content = dom.innerHTML
					navigator.clipboard
						.writeText(content)
						.then(() => {
							this.$messageNew.message('success', {
								message: '已复制到剪贴板',
							})
						})
						.catch((err) => {
							console.error('复制失败', err)
						})
				} else {
					this.$messageNew.message('warning', {
						message: '当前浏览器不支持复制内容',
					})
				}
			}
		},
		refuseAgree() {
			let data = {
				type: '1', //1申请入会，2被解散
				message: 'refuse',
				sendUser: this.$store.state.mobile,
				acceptUser: this.AgreeInfo.split('_')[0],
			}
			this.liveClient.send(JSON.stringify(data))
			this.AgreeBoardShow = false
		},
		acceptAgree() {
			let data = {
				type: '1', //1申请入会，2被解散
				message: 'agree',
				sendUser: this.$store.state.mobile,
				acceptUser: this.AgreeInfo.split('_')[0],
			}
			this.liveClient.send(JSON.stringify(data))
			this.AgreeBoardShow = false
		},
	},
}
</script>

<style lang="scss" scoped>
.multi-dialog {
	width: 1920px;
	height: 1080px;
	position: absolute;
	// z-index: 3000;
	left: 0;
	top: 0;
	background: url('~@/assets/service/multi1.png') no-repeat center / 100% 100%;
	&-expand {
		width: 1522px !important;
		background: url('~@/assets/service/multi2.png') no-repeat center / 100% 100%;
	}
	.dialog-head {
		height: 77px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 40px 0 38px;
		&-left {
			display: flex;
			align-items: center;
			.theme {
				width: 72px;
				line-height: 32px;
				text-align: center;
				background: #59a1ff;
				border-radius: 16px;
				font-family: Source Han Sans CN;
				font-weight: 400;
				font-size: 20px;
				color: #ffffff;
				margin-right: 16px;
			}
			.invited {
				font-family: YouSheBiaoTiHei;
				font-size: 28px;
				color: #ffffff;
				line-height: 36px;
				margin-right: 38px;
				padding-right: 21px;
				position: relative;
				cursor: pointer;
				&::after {
					content: '';
					position: absolute;
					right: 0;
					top: 17px;
					width: 12px;
					height: 6px;
					background: url('~@/assets/service/to-bottom.png') no-repeat center / 100% 100%;
				}
				.meeting-show-board {
					position: absolute;
					z-index: 5000;
					left: 0;
					top: 36px;
					width: 392px;
					border-radius: 8px;
					background: linear-gradient(to bottom, rgba(66, 130, 208, 0.9) 0%, rgba(17, 48, 88, 0.9) 100%);
					padding: 21px 31px;
					&-title {
						font-family: YouSheBiaoTiHei;
						font-size: 28px;
						color: #ffffff;
						line-height: 36px;
						text-align: left;
						margin-bottom: 18px;
					}
					&-item {
						width: 100%;
						display: flex;
						align-items: flex-start;
						flex-wrap: nowrap;
						font-family: Source Han Sans CN;
						font-weight: 400;
						font-size: 14px;
						color: #ffffff;
						line-height: 21px;
						margin-bottom: 30px;
						&-title {
							flex-shrink: 0;
							width: 72px;
							text-align: left;
						}
						&-value {
							flex-shrink: 0;
							width: calc(100% - 72px);
							text-align: left;
							.icon {
								display: inline-block;
								width: 12px;
								height: 12px;
								cursor: pointer;
								margin-left: 10px;
							}
							.clip-icon {
								background: url('~@/assets/service/clip-icon.png') no-repeat center / 100% 100%;
							}
						}
					}
				}
			}
			.call-duration {
				font-family: Source Han Sans CN;
				font-weight: 400;
				font-size: 14px;
				color: #cbe5ff;
				line-height: 21px;
			}
		}
		.full-btns {
			display: flex;
			align-items: center;
			.full-btn {
				&:not(:last-child) {
					margin-right: 10px;
				}
				width: 32px;
				height: 32px;
				cursor: pointer;
				&:nth-child(1) {
					background: url('~@/assets/service/slide.png') no-repeat center / 100% 100%;
				}
				&:nth-child(2) {
					background: url('~@/assets/service/full1.png') no-repeat center / 100% 100%;
				}
				&:nth-child(3) {
					background: url('~@/assets/service/close.png') no-repeat center / 100% 100%;
				}
			}
		}
	}
	.dialog-contain {
		height: calc(100% - 150px);
		display: flex;
		flex-wrap: nowrap;
		position: relative;
		overflow: hidden;
		.meeting-contain {
			width: 100%;
			height: 100%;
			padding: 8px;
			-ms-overflow-style: none;
			scrollbar-width: none;
			&::-webkit-scrollbar {
				display: none; /* Chrome Safari */
			}
		}
		.member-manage {
			margin-right: 13px;
			width: calc(100% - 1576px);
			height: 100%;
			background: #0a3368;
			border-radius: 5px;
			padding: 20px;
			&-top {
				width: 100%;
				display: flex;
				justify-content: center;
				margin-bottom: 16px;
				::v-deep {
					.el-input {
						width: 237px !important;
						.el-input__inner {
							height: 36px;
							line-height: 36px;
							border: 1px solid #cbe5ff !important;
							background-color: transparent !important;
							font-size: 16px !important;
							color: #ffffff !important;
							font-family: Source Han Sans CN;
							font-weight: 400;
						}
						.el-input__icon {
							line-height: 36px;
							color: #00aaff;
						}
					}
				}
			}
			&-contain {
				width: 100%;
				height: calc(100% - 100px);
				overflow-y: auto;
				&::-webkit-scrollbar {
					/*滚动条整体样式*/
					width: 0px;
					background: transparent;
				}
				&::-webkit-scrollbar-thumb {
					/*滚动条里面小方块*/
					border-radius: 2px;
					box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
					background: rgb(45, 124, 228);
					height: 100px;
				}
				.member-list {
					width: 100%;
					display: flex;
					align-items: center;
					justify-content: space-between;
					&-group {
						display: flex;
						align-items: center;
						flex-wrap: nowrap;
						.avatar {
							width: 32px;
							height: 32px;
							background: url('~@/assets/service/avatar.png') no-repeat center / 100% 100%;
							margin-right: 10px;
						}
						.name {
							font-family: Source Han Sans CN;
							font-weight: 400;
							font-size: 16px;
							color: #ffffff;
							line-height: 24px;
							white-space: nowrap;
							width: 140px;
							overflow: hidden;
							text-align: left;
							text-overflow: ellipsis;
						}
						.member-icon {
							width: 16px;
							height: 16px;
							cursor: pointer;
							&:not(:last-child) {
								margin-right: 8px;
							}
						}
						.mic-on {
							background: url('~@/assets/service/mic-on.png') no-repeat center / 100% 100%;
						}
						.mic-off {
							background: url('~@/assets/service/mic-off.png') no-repeat center / 100% 100%;
						}
						.more {
							background: url('~@/assets/service/more.png') no-repeat center / 100% 100%;
						}
					}
					&:not(:last-of-type) {
						margin-bottom: 20px;
					}
				}
			}
			&-bottom {
				width: 100%;
				height: 50px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				.member-manage-btn {
					line-height: 36px;
					padding: 0 10px;
					background: url('~@/assets/service/board-btn-bg.png') no-repeat center / 100% 100%;
					cursor: pointer;
					font-family: Source Han Sans CN;
					font-weight: 400;
					font-size: 16px;
					color: #ffffff;
				}
			}
		}
	}
	.dialog-foot {
		height: 50px;
		margin-bottom: 5px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 43px 0 20px;
		position: relative;
		.point-foot-group {
			display: flex;
			align-items: center;
			flex-wrap: nowrap;
			.foot-btn {
				display: flex;
				align-items: center;
				padding: 0 32px;
				font-family: Source Han Sans CN;
				font-weight: 400;
				font-size: 14px;
				color: #ffffff;
				cursor: pointer;
				&:not(:last-of-type) {
					border-right: 1px solid;
					border-image: linear-gradient(180deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.1)) 1 1;
				}
				&-icon {
					width: 28px;
					height: 28px;
					margin-right: 6px;
				}
				.mic-on {
					background: url('~@/assets/service/mic-on.png') no-repeat center / 100% 100%;
				}
				.mic-off {
					background: url('~@/assets/service/mic-off.png') no-repeat center / 100% 100%;
				}
				.cam-on {
					background: url('~@/assets/service/cam-on.png') no-repeat center / 100% 100%;
				}
				.cam-off {
					background: url('~@/assets/service/cam-off.png') no-repeat center / 100% 100%;
				}
				.member {
					background: url('~@/assets/service/member.png') no-repeat center / 100% 100%;
				}
				.invite {
					background: url('~@/assets/service/invite.png') no-repeat center / 100% 100%;
				}
				.hang-off {
					background: url('~@/assets/service/hang-off.png') no-repeat center / 100% 100%;
				}
			}
		}
	}
}
.local-stream-content {
	width: 100%;
	height: 100%;
}
.remote-stream-container {
	width: 100%;
	height: 100%;
	// width: 320px;
	// height: 240px;
	// margin: 0 10px 10px 0;
}
</style>
<style lang="scss">
@import './components/service/style/multipleMeetingStyle.scss';
</style>
