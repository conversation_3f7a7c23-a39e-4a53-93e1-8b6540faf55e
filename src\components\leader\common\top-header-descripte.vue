<template>
	<div class="descripte-box">
		<div class="descripte-item" v-for="(item, index) in dataList" :key="index">
			<div class="item-top">
				<div class="devider"></div>
				<div class="title">{{ item.title }}</div>
				<div v-if="item.statusIcon" class="status-icon"></div>
				<div class="status-number">{{ item.statusNumber || '' }}</div>
			</div>
			<div class="item-bottom">
				<el-tooltip effect="dark" popper-class="tooltip-item" :content="String(item.number)" placement="top">
					<div class="number-text">{{ item.number }}</div>
				</el-tooltip>

				<div class="unit-text">{{ item.unitName }}</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		dataList: {
			type: Array,
			default: () => [],
		},
		unitName: {
			type: String,
			default: '',
		},
	},
	data() {
		return {}
	},
	computed: {},
	methods: {},

	watch: {},
}
</script>

<style lang="less" scoped>
.descripte-box {
	width: 100%;
	height: 72px;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	z-index: 1001;

	.descripte-item {
		height: 72px;
		margin-left: 30px;

		.item-top {
			width: 100%;
			height: 32px;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			.devider {
				width: 12px;
				height: 12px;
				background: url(~@/assets/img/top-dedscripte-devider.png);
				/* 背景不重复 */
				background-repeat: no-repeat;
				/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
				background-size: cover;
				/* 背景居中显示（可选） */
				background-position: center;
				margin-right: 8px;
			}
			.title {
				height: 32px;

				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 20px;
				color: #b0e0ff;
				line-height: 32px;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}
			.status-icon {
				margin-left: 8px;
				width: 19.5px;
				height: 16px;

				background: url(~@/assets/img/status-up-icon.png);
				/* 背景不重复 */
				background-repeat: no-repeat;
				/* 背景填充整个容器（按比例缩放并裁剪覆盖） */
				background-size: cover;
				/* 背景居中显示（可选） */
				background-position: center;
			}
			.status-number {
				margin-left: 2px;

				font-family: DINCond-Bold, DINCond-Bold;
				font-weight: 400;
				font-size: 20px;
				color: #dbf1ff;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}
		}

		.item-bottom {
			width: 100%;
			height: 40px;
			padding-left: 20px;
			display: flex;
			justify-content: flex-start;
			align-items: flex-end;
			.number-text {
				max-width: 170px;
				height: 40px;
				font-family: DINCond-Bold, DINCond-Bold;
				font-weight: 400;
				font-size: 32px;
				color: #00bbff;
				line-height: 40px;
				text-align: right;
				font-style: normal;
				text-transform: none;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
			.unit-text {
				height: 32px;

				margin-left: 6px;

				font-family: PingFang SC, PingFang SC;
				font-weight: 300;
				font-size: 18px;
				color: #00bbff;
				line-height: 32px;
				text-align: right;
				font-style: normal;
				text-transform: none;
			}
		}
		&:nth-child(1) {
			.number-text,
			.unit-text {
				color: #00bbff;
			}
		}
		&:nth-child(2) {
			.number-text,
			.unit-text {
				color: #14cc8f;
			}
		}
		&:nth-child(3) {
			.number-text,
			.unit-text {
				color: #ff8a00;
			}
		}
	}
}
</style>
