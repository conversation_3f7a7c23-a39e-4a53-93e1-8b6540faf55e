<template>
	<div class="leader_box">
		<div class="leader_sjts">
			<!-- <div class="left_bg"></div>
      <div class="right_bg"></div>-->
			<div class="left" :class="mkdx ? 'mkdxLeft' : ''">
				<div class="left1">
					<BlockBox title="农机数量" subtitle="Introduction to Hushu" class="box box1" :isListBtns="false" :blockHeight="300">
						<div class="cont">
							<div class="cont1">
								<div class="contList" v-for="(it, i) of data3" :key="i">
									<div class="contNum">
										<span>{{ it.num }}</span>
									</div>
									<img src="@/assets/bjnj/njjrImg1.png" alt />
									<div class="contName">{{ it.name }}</div>
								</div>
							</div>
							<div class="cont2">
								<TrendLineChart :data="gdqsChartData" :options="gdqsOptions" :initOption="gdqsInitOption" />
							</div>
						</div>
					</BlockBox>
					<BlockBox
						title="服务组织及应急机具"
						subtitle="Economic Overview"
						class="box box2"
						:showMore="true"
						:isListBtns="false"
						:blockHeight="324"
						@handleShowMore="showMoreFn1"
					>
						<div class="cont">
							<div class="contLeft">
								<div class="contList" v-for="(it, i) of data5_1" :key="i" @click="handleItemClick(it)">
									<div class="contNum">
										{{ it.num }}
										<span>{{ it.unit }}</span>
									</div>
									<div class="contName">{{ it.name }}</div>
								</div>
							</div>
							<div class="contRight">
								<div class="contList" v-for="(it, i) of data5_2" :key="i" @click="handleItemClick(it)">
									<div class="contNum">
										{{ it.num }}
										<span>{{ it.unit }}</span>
									</div>
									<div class="contName">{{ it.name }}</div>
								</div>
							</div>
							<img class="contImg" src="@/assets/bjnj/zyjrImg2.png" alt />
							<img class="contImg2" src="@/assets/bjnj/zyjrImg1.png" alt />
						</div>
					</BlockBox>
				</div>
			</div>
			<div class="right" :class="mkdx ? 'mkdxRight' : ''">
				<div class="right1">
					<BlockBox title="农机类型" subtitle="Party Building Information" class="box box7" :isListBtns="false" :blockHeight="302">
						<div class="njlx_box">
							<div
								v-for="(item, index) in chartData"
								:key="index"
								class="njlx_item"
								:class="activeAmachType == item[2] ? 'njlx_item2' : ''"
								@click="amachTypeClick(item)"
							>
								<div class="njlx_number">{{ item[1] }}</div>
								<div :class="activeAmachType == item[2] ? 'njlx_title2' : 'njlx_title'">{{ item[0] }}</div>
							</div>
						</div>
					</BlockBox>

					<BlockBox
						title="昨日在线农机"
						subtitle="Full Factor Grid Events"
						class="box box8"
						:showMore3="true"
						:textArr2="amachTypeListText"
						:isListBtns="false"
						:blockHeight="326"
						@updateChange="updateChange"
					>
						<div class="cont" v-show="onlineData.length > 0">
							<TrendLineChart :data="onlineData" :options="onLineOptions" :initOption="onlineInitOption" />
						</div>
						<div class="cout" v-show="onlineData.length == 0">
							<img class="zwsjImg" src="@/assets/bjnj/zwsj.png" alt="" />
						</div>
					</BlockBox>
				</div>
			</div>
		</div>
		<div class="middle" :class="mkdx ? 'mkdxMiddle' : ''">
			<BlockBox8
				title="农机作业统计"
				subtitle="Introduction to Hushu"
				class="box box10"
				:isListBtns="true"
				:textArr="['农机作业时间趋势图', '各地农机生产作业统计']"
				@updateChange="handleChange"
				:blockHeight="290"
			>
				<div class="cont">
					<AmachTypeSwiper @updateChange="updateAmachTypeCode" :amachTypeList="amachTypeArr" />
					<el-select class="nfxz" v-model="dqbf" placeholder="年份" @change="dqbfSelect">
						<el-option
							v-for="item in qgdCodejjcdOptions1"
							:key="Number(item.itemCode)"
							:label="item.itemValue"
							:value="Number(item.itemCode)"
						></el-option>
					</el-select>
					<el-select class="yfxz" v-model="dqyf" placeholder="月份" @change="dqyfSelect">
						<el-option
							v-for="item in qgdCodejjcdOptions2"
							:key="Number(item.itemCode)"
							:label="item.itemValue"
							:value="Number(item.itemCode)"
						></el-option>
					</el-select>
					<BarLineChart
						v-show="sczzData1[0].data.length > 0"
						:data="sczzData1"
						:options="sczzOptions2"
						:init-option="initOption2"
						@click="njzytj"
					></BarLineChart>
					<div class="fhBox" @click="njzytjFh">
						<img class="fh" src="@/assets/bjnj/njzytjFh.png" alt="" />
						返回
					</div>
					<div class="couts" v-show="sczzData1[0].data.length == 0">
						<img class="zwsjImg" src="@/assets/bjnj/zwsj.png" alt="" />
					</div>
				</div>
			</BlockBox8>
		</div>
		<div class="map_box">
			<!-- <zlBg @emitMenu="emitMenu" /> -->
			<!-- <svgMap /> -->
			<LeaMap
				ref="leafletMap"
				@poiClick="poiClick"
				@gridClick="gridClick"
				@operate="operate"
				@qctc="qctc"
				:clear2="true"
				:back2="true"
			/>
		</div>
		<LeaderFooter ref="LeaderFooter" :btns="btns" :activaIdx="activaIdx" @mark="mark" />
		<div class="mkss" :class="!mkdx ? 'mkss1' : ''" @click="mkssBtn">
			<div class="mkss2">收缩</div>
		</div>
		<div class="njtlBox" @click="njtlTk">
			<img v-if="isnjtlListShow" src="@/assets/bjnj/njtlBtn2.png" alt="" />
			<img v-else src="@/assets/bjnj/njtlBtn1.png" alt="" />
		</div>
		<div class="ssBox" v-if="ssboxShow" @click="ssmkQhs">
			<img src="@/assets/bjnj/ssBtn.png" alt="" />
		</div>
		<div class="sxBox" @click="sxTk">
			<img src="@/assets/bjnj/sxan.png" alt="" />
		</div>
		<div class="ssPop" v-if="sspopShow">
			<div class="ssPop1">
				<input type="text" v-model="ssnr" @change="ssqy" />
				<div class="ssImgBox" @click="ssqy">
					<img src="@/assets/bjnj/ss.png" alt="" />
				</div>
			</div>
			<div class="ssPop2" v-if="sspopList">
				<div class="ssList" v-for="(item, index) in ssList" :key="index" @click="ssxz(item)">{{ item.areaName }}</div>
			</div>
		</div>
		<div class="njtlList" v-if="isnjtlListShow">
			<div class="njtlItem">
				<img src="@/assets/bjnj/njtlImg2.png" alt="" />
				<div class="njtlName">有基本信息有轨迹的农机</div>
			</div>
			<div class="njtlItem">
				<img src="@/assets/bjnj/njtlImg1.png" alt="" />
				<div class="njtlName">有基本信息无轨迹的农机</div>
			</div>
			<div class="njtlItem">
				<img src="@/assets/bjnj/njtlImg3.png" alt="" />
				<div class="njtlName">无基本信息无轨迹的农机</div>
			</div>
		</div>
		<!-- <div class="jgzzBox" v-if="activaIdx == 1">
      <div class="jgzzItem" :class="resourceType == 0 ? 'jgzzActive' : ''" @click="jgzz(0, true)">
        <img src="@/assets/head/icon_njfb_a.png" v-if="resourceType == 0" />
        <img src="@/assets/head/icon_njfb.png" v-else />
        <span>农机分布</span>
      </div>
      <div class="jgzzItem" :class="resourceType == 1 ? 'jgzzActive' : ''" @click="jgzz(1, true)">
        <img src="@/assets/head/icon_shhfwzx_a.png" v-if="resourceType == 1" />
        <img src="@/assets/head/icon_shhfwzx.png" v-else />
        <span>区域农机社会化服务中心</span>
      </div>
      <div class="jgzzItem" :class="resourceType == 2 ? 'jgzzActive' : ''" @click="jgzz(2, true)">
        <img src="@/assets/head/icon_jyfwd_a.png" v-if="resourceType == 2" />
        <img src="@/assets/head/icon_jyfwd.png" v-else />
        <span>农机应急作业服务队</span>
      </div>
      <div class="jgzzItem" :class="resourceType == 3 ? 'jgzzActive' : ''" @click="jgzz(3, true)">
        <img src="@/assets/head/icon_jzzx_a.png" v-if="resourceType == 3" />
        <img src="@/assets/head/icon_jzzx.png" v-else />
        <span>区域农业应急救灾中心</span>
      </div>
    </div> -->
		<div class="zdcsBox" v-if="zdcsShow">
			<div class="jgzzItem" :class="zdcsActive == 0 ? 'jgzzActive' : ''" @click="zdcs(0)">
				<img src="@/assets/csts/img131.png" alt v-if="zdcsActive == 0" />
				<img src="@/assets/csts/img121.png" alt v-else />
				<span>机具保有量</span>
			</div>
			<div class="jgzzItem" :class="zdcsActive == 1 ? 'jgzzActive' : ''" @click="zdcs(1)">
				<img src="@/assets/csts/img132.png" alt v-if="zdcsActive == 1" />
				<img src="@/assets/csts/img122.png" alt v-else />
				<span>学校</span>
			</div>
			<div class="jgzzItem" :class="zdcsActive == 2 ? 'jgzzActive' : ''" @click="zdcs(2)">
				<img src="@/assets/csts/img133.png" alt v-if="zdcsActive == 2" />
				<img src="@/assets/csts/img123.png" alt v-else />
				<span>网吧</span>
			</div>
			<div class="jgzzItem" :class="zdcsActive == 3 ? 'jgzzActive' : ''" @click="zdcs(3)">
				<img src="@/assets/csts/img134.png" alt v-if="zdcsActive == 3" />
				<img src="@/assets/csts/img124.png" alt v-else />
				<span>娱乐场所</span>
			</div>
			<div class="jgzzItem" :class="zdcsActive == 4 ? 'jgzzActive' : ''" @click="zdcs(4)">
				<img src="@/assets/csts/img135.png" alt v-if="zdcsActive == 4" />
				<img src="@/assets/csts/img125.png" alt v-else />
				<span>火车站</span>
			</div>
			<div class="jgzzItem" :class="zdcsActive == 5 ? 'jgzzActive' : ''" @click="zdcs(5)">
				<img src="@/assets/csts/img136.png" alt v-if="zdcsActive == 5" />
				<img src="@/assets/csts/img126.png" alt v-else />
				<span>电影院</span>
			</div>
		</div>
		<div class="tjxxBox">
			<div class="tjxxItem" v-for="(item, index) of tjxxList" :key="index">
				<div class="tjxxName">{{ item.name }}</div>
				<div class="tjxxNum">
					{{ item.value }}<span>{{ item.unit }}</span>
				</div>
			</div>
		</div>
		<div class="mapBtn">
			<div class="mapItem" :class="activaIdx == 1 ? 'mapItem2' : ''" @click="mapActive(1)">
				<img v-if="activaIdx == 1" src="@/assets/bjnj/btnImg2.png" alt="" />
				<img v-else src="@/assets/bjnj/btnImg1.png" alt="" />
				<span>天气预警</span>
			</div>
			<div class="mapItem" :class="mapShow == 2 ? 'mapItem2' : ''" @click="mapActive(2)">
				<img v-if="mapShow == 2" src="@/assets/bjnj/btnImg4.png" alt="" />
				<img v-else src="@/assets/bjnj/btnImg3.png" alt="" />
				<!-- <span>资源分布</span> -->
				<span>服务组织</span>
			</div>
			<div class="mapItem" :class="mapShow == 3 ? 'mapItem2' : ''" @click="mapActive(3)">
				<img v-if="mapShow == 3" src="@/assets/bjnj/btnImg6.png" alt="" />
				<img v-else src="@/assets/bjnj/btnImg5.png" alt="" />
				<span>农机分布</span>
			</div>
			<div class="mapItem" :class="activaIdx == 4 ? 'mapItem2' : ''" @click="mapActive(4)">
				<img v-if="activaIdx == 4" src="@/assets/bjnj/btnImg8.png" alt="" />
				<img v-else src="@/assets/bjnj/btnImg7.png" alt="" />
				<span>在线农机</span>
			</div>
			<div class="mapItem" :class="activaIdx == 5 ? 'mapItem2' : ''" @click="mapActive(5)">
				<img v-if="activaIdx == 5" src="@/assets/bjnj/btnImg10.png" alt="" />
				<img v-else src="@/assets/bjnj/btnImg9.png" alt="" />
				<span>昨日跨区统计</span>
			</div>
			<div class="mapItem" :class="activaIdx == 6 ? 'mapItem2' : ''" @click="mapActive(6)" v-if="areaLevel == 3">
				<img v-if="activaIdx == 6" src="@/assets/bjnj/btnImg10.png" alt="" />
				<img v-else src="@/assets/bjnj/btnImg9.png" alt="" />
				<span>进度统计</span>
			</div>
		</div>
		<div class="zyfbBox" v-if="mapShow == 2 && zyfbShow">
			<div class="zyfbItem" :class="resourceType == 1 ? 'jgzzActive' : ''" @click="jgzz(1, true)">
				<!-- <img src="@/assets/head/icon_shhfwzx_a.png" v-if="resourceType == 1" />
        <img src="@/assets/head/icon_shhfwzx.png" v-else /> -->
				<span>区域农机社会化服务中心</span>
			</div>
			<div class="zyfbItem" :class="resourceType == 2 ? 'jgzzActive' : ''" @click="jgzz(2, true)">
				<!-- <img src="@/assets/head/icon_jyfwd_a.png" v-if="resourceType == 2" />
        <img src="@/assets/head/icon_jyfwd.png" v-else /> -->
				<span>农机应急作业服务队</span>
			</div>
			<div class="zyfbItem" :class="resourceType == 3 ? 'jgzzActive' : ''" @click="jgzz(3, true)">
				<!-- <img src="@/assets/head/icon_jzzx_a.png" v-if="resourceType == 3" />
        <img src="@/assets/head/icon_jzzx.png" v-else /> -->
				<span>区域农业应急救灾中心</span>
			</div>
		</div>
		<div class="njfbBox" :class="mapShow == 4 ? 'njfbBox2' : ''" v-if="(mapShow == 3 || mapShow == 4) && njfbShow">
			<div class="zyfbItem" :class="resourceTypes == 100000 ? 'jgzzActive' : ''" @click="njlxqh(100000)">
				<span>全部</span>
			</div>
			<div class="zyfbItem" :class="resourceTypes == 150105 ? 'jgzzActive' : ''" @click="njlxqh(150105)">
				<span>谷物联合收割机</span>
			</div>
			<div class="zyfbItem" :class="resourceTypes == 510101 ? 'jgzzActive' : ''" @click="njlxqh(510101)">
				<span>轮式拖拉机</span>
			</div>
			<div class="zyfbItem" :class="resourceTypes == 150106 ? 'jgzzActive' : ''" @click="njlxqh(150106)">
				<span>玉米收获机</span>
			</div>
			<div class="zyfbItem" :class="resourceTypes == 120401 ? 'jgzzActive' : ''" @click="njlxqh(120401)">
				<span>水稻插秧机</span>
			</div>
			<div class="zyfbItem" :class="resourceTypes == 150302 ? 'jgzzActive' : ''" @click="njlxqh(150302)">
				<span>花生收获机</span>
			</div>
			<div class="zyfbItem" :class="resourceTypes == 150303 ? 'jgzzActive' : ''" @click="njlxqh(150303)">
				<span>油菜籽收获机</span>
			</div>
			<div class="zyfbItem" :class="resourceTypes == 150403 ? 'jgzzActive' : ''" @click="njlxqh(150403)">
				<span>甘蔗联合收获机</span>
			</div>
			<div class="zyfbItem" :class="resourceTypes == '000000' ? 'jgzzActive' : ''" @click="njlxqh('000000')">
				<span>其他</span>
			</div>
		</div>

		<leftTooltip
			v-if="toolTipShow"
			:name1="toolName1"
			:name2="toolName2"
			:typeOptions1="typeOptions1"
			:typeOptions2="typeOptions2"
			@change="tooltipChange"
		/>

		<EventDetail v-if="isShowEventDetail" @close="isShowEventDetail = false" />

		<CountryPart :isShow="isShowCountryPart" @close="closeCountryPart" @check="checkTree" />

		<yjPop v-if="yjPopShow" @close="yjPopShow = false" />
		<yjxqPop v-model="isyjxqShow" :list="yjxqList" @handle="eventInfoBtnClick5" />

		<!-- 基础信息 -->
		<BasicInformation
			v-model="basicInfomationShow"
			:title="basicInfomationTitle"
			:list="basicInfomationList"
			:btnShow="basicInfomationBtnShow"
			:btns="basicInfomationBtns"
			@handle="clickBasicInformationBtn"
		/>

		<znzyPop
			ref="znzyPop"
			v-model="znzyPopShow"
			:userAreaId="areaId"
			@fhEmitai="fhEmitai"
			@closeEmitai="znzyPopShow = false"
			:basicInfomationId="basicInfomationId"
		/>

		<njInfo v-if="njInfoShow" :agmachInfo="currentAgmachInfo" @handleClick="handleNjInfo" @closeEmitai="njInfoShow = false" />

		<historyWorkPop
			ref="historyWorkPop"
			v-model="workPopShow"
			:agmachInfo="currentAgmachInfo"
			:agmachId="agmachId"
			@operate="eventInfoBtnClick15"
		/>
		<yjzyPop
			v-model="isyjzyShow"
			@handle="eventInfoBtnClick7"
			@setCurrentTabIndex="setCurrentTabIndex"
			@setIsJzAmach="setIsJzAmach"
			:isJzAmach="isJzAmach"
			:userAreaId="areaId"
			:currentTabIndex="currentTabIndex"
		/>
		<mapPop ref="mapPop" v-model="isMapShow" />
		<kqzyPop ref="kqzyPop" v-model="iskqzyShow" @input="activaIdx = -1" @poiClick="poiClick" />

		<!-- <spjkPop v-if="isXlgcShow" @closeEmit="isXlgcShow = false" @moreXl="isXlgcShow1 = true" />
    <szyyPop v-if="isXlgcShow1" @closeEmit="isXlgcShow1 = false" />
    <wgyPop v-if="isWgllShow" @closeEmit="isWgllShow = false" />
    <dzzPop v-if="isdzzShow" @closeEmit="isdzzShow = false" />
    <xzqyPop v-if="isxzqyShow" @closeEmit="isxzqyShow = false" />
    <zwzxPop v-if="iszwzxShow" @closeEmit="iszwzxShow = false" />
    <zhzxPop v-if="iszhzxShow" @closeEmit="iszhzxShow = false" />
    <csglgdPop v-if="iscsglgdShow" @closeEmit="iscsglgdShow = false" />
    <shaqPop v-if="isshaqShow" @closeEmit="isshaqShow = false" />
    <sthbPop v-if="issthbShow" @closeEmit="issthbShow = false" />
    <whlyPop v-if="iswhlyShow" @closeEmit="iswhlyShow = false" />
    <yjfkPop v-if="isyjfkShow" @closeEmit="isyjfkShow = false" />
    <ggjtPop v-if="isggjtShow" @closeEmit="isggjtShow = false" />
    <zdqyPop v-if="iszdqyShow" @closeEmit="iszdqyShow = false" />
    <xxPop v-if="isxxShow" @closeEmit="isxxShow = false" />
    <wbPop v-if="iswbShow" @closeEmit="iswbShow = false" />
    <ylcsPop v-if="isylcsShow" @closeEmit="isylcsShow = false" />
    <hczPop v-if="ishczShow" @closeEmit="ishczShow = false" />
    <dyyPop v-if="isdyyShow" @closeEmit="isdyyShow = false" />
    <djylPop v-if="djylShow" @closeEmit="djylShow = false" />
    <bjxqPop v-if="bjxqShow" @closeEmit="bjxqShow = false" />
    <Area ref="area" v-show="showArea" :pop-area-info="popAreaInfo"></Area>
    <jjfztk v-if="jjfztkShow" @closeEmitai="jjfztkShow = false"></jjfztk>
    <hswhtk v-if="hswhtkShow" @closeEmitai="hswhtkShow = false"></hswhtk>
    <stjstk v-if="stjstkShow" @closeEmitai="stjstkShow = false"></stjstk>
    <wgzltk v-if="wgzltkShow" @closeEmitai="wgzltkShow = false"></wgzltk>-->
	</div>
</template>

<script>
import testData from '@/assets/json/cyjjPoint'
import BlockBox from '@/components/leader/common/blockBox6.vue'
import BlockBox8 from '@/components/leader/common/blockBox8.vue'
import HeaderMain from '@/components/leader/common/headerMain.vue'
import effectRankBarSwiper from './components/effectRankBar-swiper.vue'
import particle from './components/particle.vue'
import EventDetail from './components/EventDetail.vue'
import CountryPart from './components/CountryPart.vue'
import leaderMiddle from '@/components/leader/common/leaderMiddle.vue'
// import gdMap from '@/components/leader/gdMap/gdMap.vue'
// import cesiumMap from '@/components/leader/cesiumMap/Cesium3dMap.vue'
// import cssjPoint from '@/assets/json/csts/cssjPoint.json'
// import spjkPoint from '@/assets/json/csts/spjkPoint.json'
// import csbjPoint from '@/assets/json/csts/csbjPoint.json'
// import ryllPoint from '@/assets/json/csts/ryllPoint.json'
import xzqhPoint from '@/assets/json/csts/xzqhPoint.json'
// import dzzPoint from '@/assets/json/csts/dzzPoint.json'
// import zwzxPoint from '@/assets/json/csts/zwzxPoint.json'
// import zzzxPoint from '@/assets/json/csts/zzzxPoint.json'
// import zdqyPoint from '@/assets/json/csts/zdqyPoint.json'
// import xxPoint from '@/assets/json/csts/xxPoint.json'
// import wbPoint from '@/assets/json/csts/wbPoint.json'
// import ylcsPoint from '@/assets/json/csts/ylcsPoint.json'
// import hczPoint from '@/assets/json/csts/hczPoint.json'
// import dyyPoint from '@/assets/json/csts/dyyPoint.json'
import yjzyPop from '@/components/bjnj/yjzyPop.vue' //应急资源
import LeaderFooter from '@/components/leader/common/leaderFooter3.vue'
import myRobot from '@/components/robot/myRobot.vue'
import NumScroll from '@/components/leader/num_scrolls'
import Area from '@/components/leader/leader_csts/area.vue'
import svgMap from '@/components/common/svgMap.vue'
import zlBg from '@/components/common/zlBg.vue'
import LivePlayer from '@liveqing/liveplayer'
import SwiperTable from '@/components/bjnj/SwiperTable.vue'
import LeaMap from '@/components/map/LeafletMapNj.vue'
import {
	getResourceList,
	getAllAlarmWithGPS,
	getCscpBasicHxItemCode,
	cscpCurrentUserDetails,
	getAgmachGuiJiInfo,
	queryByList,
	getDistributionCount,
	getZxnjDistributionCount,
} from '@/api/bjnj/zhdd.js'
import leftTooltip from '@/views/leader/components/mskx/leftTooltip.vue'
import yjPop from '@/components/bjnj/yjPop.vue'
import yjxqPop from '@/components/bjnj/yjxqPop.vue' //预警详情
import { BasicInformation } from './components/zhdd/dialog'
import AmachTypeSwiper from './components/zhdd/AmachTypeSwiper.vue'
import znzyPop from '@/components/bjnj/znzyPop.vue'
import njInfo from '@/components/bjnj/njInfo.vue'
import historyWorkPop from '@/components/bjnj/historyWorkPop.vue'
import mapPop from '@/components/bjnj/mapPop.vue'
import kqzyPop from '@/components/bjnj/kqzyPop.vue'
import clusterData from '@/components/map/clusterData.json'
import province from '@/components/map/province.json'
// import clusterData1 from '@/components/map/clusterData1.json'
// import clusterData2 from '@/components/map/clusterData2.json'
// import clusterData3 from '@/components/map/clusterData3.json'

import rtc from '@/rhtx/core/index'
import { aqjgGetToken } from '@/api/hs/hs_aqjg.api.js'
import dayjs from 'dayjs'
import {
	getNjCount,
	getNjCount3,
	getNjTodayCount,
	getNjTodayCount2,
	getNjOnlineCount,
	getNjCurrent7Count,
	getVehiclesCount,
	getNjAreaDayType,
	getNjAreaDayType2,
	getAreaDistribute,
	getNjTrend,
	getNjLocation,
	getDryingMap,
	getEmergencyMap,
	getNjLocations,
	getNjAreaDayTypes2,
	getAgmachTypeList,
	getNjAreaDayTypes,
	locHistory2,
	agmachCount,
	cqCount,
	agmachtypeCount,
	agmachtypeLocList,
	workTrend,
	work,
	identification,
	count,
	onlineCounts,
	exit,
	onlyExit,
	exitCount,
	levelCount,
	geojson,
} from '@/api/njzl/hs.api.js'

import 'swiper/css/swiper.css'
export default {
	name: 'Hszl',
	components: {
		BlockBox,
		BlockBox8,
		HeaderMain,
		effectRankBarSwiper,
		particle,
		leaderMiddle,
		// gdMap,
		// cesiumMap,
		LeaderFooter,
		EventDetail,
		CountryPart,
		myRobot,
		Area,
		NumScroll,
		// LandUse,
		// FullFactorGridEvents,
		// StreetSurvey,
		// jjfztk,
		// hswhtk,
		// stjstk,
		// wgzltk,
		svgMap,
		zlBg,
		LivePlayer,
		SwiperTable,
		LeaMap,
		leftTooltip,
		yjPop,
		BasicInformation,
		znzyPop,
		njInfo,
		historyWorkPop,
		yjxqPop,
		yjzyPop,
		mapPop,
		kqzyPop,
		AmachTypeSwiper,
	},
	data() {
		return {
			allowClicks: true,
			ssnr: '',
			ssList: [],
			ssboxShow: true,
			sspopShow: false,
			sspopList: false,
			startDate: '',
			endDate: '',
			amachTypeListText: [['', '全部']],
			onlineData: [],
			onLineOptions: {
				legend: {
					show: false,
				},
				smooth: false,
				colors: [['#327348', '#327348']],
			},
			onlineInitOption: {
				yAxis: {
					name: '台',
					nameTextStyle: {
						padding: [0, 0, 0, -30],
					},
				},
				xAxis: {
					axisLabel: {
						interval: 3,
						textStyle: {
							fontSize: 12,
						},
						formatter: (value) => {
							return `${value}`
						},
					},
				},
			},
			dqbf: dayjs()
				.subtract(1, 'year')
				.format('YYYY'),
			dqyf: '',
			dqyfs: '',
			qgdCodejjcdOptions1: [
				{
					itemCode: dayjs().format('YYYY'),
					itemValue: dayjs().format('YYYY'),
				},
				{
					itemCode: dayjs()
						.subtract(1, 'year')
						.format('YYYY'),
					itemValue: dayjs()
						.subtract(1, 'year')
						.format('YYYY'),
				},
				{
					itemCode: dayjs()
						.subtract(2, 'year')
						.format('YYYY'),
					itemValue: dayjs()
						.subtract(2, 'year')
						.format('YYYY'),
				},
				{
					itemCode: dayjs()
						.subtract(3, 'year')
						.format('YYYY'),
					itemValue: dayjs()
						.subtract(3, 'year')
						.format('YYYY'),
				},
				{
					itemCode: dayjs()
						.subtract(4, 'year')
						.format('YYYY'),
					itemValue: dayjs()
						.subtract(4, 'year')
						.format('YYYY'),
				},
			],
			qgdCodejjcdOptions2: [
				{
					itemCode: '01',
					itemValue: '一月',
				},
				{
					itemCode: '02',
					itemValue: '二月',
				},
				{
					itemCode: '03',
					itemValue: '三月',
				},
				{
					itemCode: '04',
					itemValue: '四月',
				},
				{
					itemCode: '05',
					itemValue: '五月',
				},
				{
					itemCode: '06',
					itemValue: '六月',
				},
				{
					itemCode: '07',
					itemValue: '七月',
				},
				{
					itemCode: '08',
					itemValue: '八月',
				},
				{
					itemCode: '09',
					itemValue: '九月',
				},
				{
					itemCode: '10',
					itemValue: '十月',
				},
				{
					itemCode: '11',
					itemValue: '十一月',
				},
				{
					itemCode: '12',
					itemValue: '十二月',
				},
			],
			groupLevel: 2,
			njzytjBtn: 0,
			agmachTypeCode2: '',
			zyfbShow: false,
			njfbShow: false,
			mapShow: 4,
			//本次的地图绘制是否和上次绘制的一样
			sameMapShow: false,
			sczzData1: [
				{
					name: '作业面积',
					data: [
						// {
						//   name: 2018,
						//   value: 163,
						// },
						// {
						//   name: 2019,
						//   value: 130,
						// },
						// {
						//   name: 2020,
						//   value: 125,
						// },
						// {
						//   name: 2021,
						//   value: 146,
						// },
						// {
						//   name: 2022,
						//   value: 130,
						// },
					],
					type: 'bar',
					barWidth: 5,
					yAxisIndex: 0,
					color: ['#4CECAA', '#2F3BED'],
				},
				{
					name: '台数',
					data: [
						// {
						//   name: 2018,
						//   value: 83,
						// },
						// {
						//   name: 2019,
						//   value: 99,
						// },
						// {
						//   name: 2020,
						//   value: 81,
						// },
						// {
						//   name: 2021,
						//   value: 94,
						// },
						// {
						//   name: 2022,
						//   value: 92,
						// },
					],
					type: 'line',
					yAxisIndex: 1,
				},
			],
			sczzOptions1: {
				barWidth: 10,
				barColor: ['#F3DB60', '#FAE699'],
				areaColor: '#fff',
				lineColor: '#FAE699',
				leftYaxisName: '作业面积：亩',
				rightYaxisName: '台数',
				tooltip: {
					show: true,
					type: 'shadow',
				},
				// yAxis: {
				//   min: 0,
				//   splitNumber: 5,
				//   unit: '亿元',
				// },
				legend: {
					show: false,
				},
			},
			sczzOptions2: {
				grid: [
					{
						top: '25%',
						bottom: '15%',
						left: '5%',
						right: '5%',
					},
				],
				yAxis: [
					{
						minInterval: 1,
					},
					{
						minInterval: 1,
					},
				],
				barWidth: 5,
				barColor: ['rgba(64, 172, 255, 0.80)', 'rgba(64, 172, 255, 0.80)'],
				areaColor: '#fff',
				lineColor: 'rgba(0, 255, 255, 0.90)',
				leftYaxisName: '作业面积：亩',
				rightYaxisName: '台数',
				tooltip: {
					show: true,
					type: 'shadow',
				},
				// yAxis: {
				//   min: 0,
				//   splitNumber: 5,
				//   unit: '亿元',
				// },
				legend: {
					show: true,
					padding: [20, 80, 0, 0],
				},
				// // "openDataZoom": true
				// // 是否开启轮播
				// isSeriesScorll: true,
				// // 轮播的间隔时间
				// scorllTimes: 3000,
				// // 超过多少的数量轮播
				// dataZoomNum: 16,
			},
			tjxxList: [
				{
					name: '农机总量',
					value: 0,
					unit: '台',
				},
				{
					name: '本年作业面积',
					value: 0,
					unit: '亩',
				},
				{
					name: '近七日作业面积',
					value: 0,
					unit: '亩',
				},
				{
					name: '昨日作业面积',
					value: 0,
					unit: '亩',
				},
			],
			time: 0,
			barChartDataTrue: [],
			dwType: '',
			activeAmachType: '',
			loadFlag: true,
			mkdx: false,
			clusterData: [],
			agmachId: '',
			areaId: '100000',
			areaIdList: [],
			areaLevel_init: 0,
			//areaLevel 0：全国，1：省份，2：地市，3：区县
			areaLevel: 0,
			areaIdNew: 0,
			trajectoryMark: '',
			resourceType: 0,
			resourceTypes: -1,
			sourceType: false,
			// resourceType: [
			//   {
			//     type: 'njfbType',
			//     flag: true
			//   },
			//   {
			//     type: 'yjjzType',
			//     flag: true
			//   },
			//   {
			//     type: 'fwdType',
			//     flag: true
			//   },
			//   {
			//     type: 'hgzxType',
			//     flag: true
			//   }
			//   // {
			//   //   type: 'yjwzType',
			//   //   flag: true
			//   // }
			// ],
			zyjdBarOptions: {
				gradientColor: ['#00B5FF', '#BFE3FF'],
			},
			isyjzyShow: false,
			workPopShow: false,
			njInfoShow: false,
			znzyPopShow: false,
			isnjtlListShow: false,
			isMapShow: false,
			iskqzyShow: false,
			// 控制基本信息弹窗显示
			basicInfomationShow: false,
			// 基础信息标题
			basicInfomationTitle: '',
			basicInfomationId: '',
			// 基础信息内容
			basicInfomationList: [],
			// 控制基础信息按钮显示
			basicInfomationBtnShow: false,
			// 基础信息按钮
			basicInfomationBtns: [],
			yjPopShow: false,
			toolTipShow: false,
			toolName1: '预警类型',
			toolName2: '预警等级',
			typeOptions1: [],
			typeOptions2: [],
			btns: [],
			gdqsChartData1: [
				// ['product', '事件'],
				// ['1.1', 77],
				// ['1.2', 97],
				// ['1.3', 95],
				// ['1.4', 81],
				// ['1.5', 15],
				// ['1.6', 6]
			],
			gdqsChartData: [
				// ['product', '事件'],
				// ['1.1', 77],
				// ['1.2', 97],
				// ['1.3', 95],
				// ['1.4', 81],
				// ['1.5', 15],
				// ['1.6', 6]
			],
			gdqsOptions: {
				legend: {
					show: false,
				},
				colors: [['#009BFF', '#26B0FF']],
				// unit: '台',
			},
			gdqsInitOption: {
				yAxis: {
					name: '台',
					nameTextStyle: {
						padding: [10, 0, 0, -30],
					},
				},
				xAxis: {
					axisLabel: {
						// interval: 1,
						// rotate: 40,
						textStyle: {
							fontSize: 12,
							// color: '#000',
						},
					},
				},
			},
			sewageDataList1: [
				// ['1', '光明农机公司', '4672', 22.87],
				// ['2', '新希望农机公司', '4672', 18.87],
				// ['3', '人本股份有限公司', '4672', 14.87],
				// ['4', '新希望农机公司', '4672', 12.87],
				// ['5', '光明农机公司', '4672', 22.87],
				// ['6', '新希望农机公司', '4672', 18.87],
				// ['7', '光明农机公司', '4672', 22.87],
				// ['8', '新希望农机公司', '4672', 18.87],
				// ['9', '人本股份有限公司', '4672', 14.87],
				// ['10', '新希望农机公司', '4672', 12.87],
				// ['11', '光明农机公司', '4672', 22.87],
				// ['12', '新希望农机公司', '4672', 18.87]
			],
			chartData: [
				// ['product', '农机类型'],
				// ['轮式拖拉机', 100],
				// ['手扶拖拉机', 200],
				// ['手扶拖拉机运输组', 150],
				// ['履带拖拉机', 100],
				// ['轮式拖拉机运输组', 200],
				// ['轮式联合收割机', 150],
				// ['履带式联合收割机', 150]
			],
			chartDataOptions: {
				legend: {
					top: 2,
					itemWidth: 10,
					itemHeight: 10,
					itemRadius: 2,
					itemPadding: 5,
					itemDistance: 0,
					itemMarginTop: 12,
					textColor: 'rgba(255, 255, 255, 0.85)',
					fontWeight: '400',
					fontSize: '12px',
					fontFamily: 'DINPro-Medium',
					letterSpacing: '0',
				},
				position: ['50%', '10%'],
				bgImg: {
					width: '60%',
					height: '60%',
					top: '42%',
					left: '50%',
				},
				unit: '台',
				title: {
					fontSize: '16px',
					top: 60,
				},
				subtitle: {
					fontSize: '14px',
					top: 80,
				},
			},
			box1BottomOptions: {
				unit: '台/天',
				gradientColor: [
					['#338dd3', '#022448'],
					['#338dd3', '#022448'],
					['#338dd3', '#022448'],
					['#338dd3', '#022448'],
					['#338dd3', '#022448'],
					['#338dd3', '#022448'],
					['#338dd3', '#022448'],
					['#338dd3', '#022448'],
					['#338dd3', '#022448'],
					['#338dd3', '#022448'],
					['#338dd3', '#022448'],
					['#338dd3', '#022448'],
				],
				// 是否开启轮播
				isSeriesScorll: true,
				// 轮播的间隔时间
				scorllTimes: 1500,
				// 超过多少的数量轮播
				dataZoomNum: 4,
			},
			box1BottomData: [
				// ['product', '数量'],
				// ['安徽', 50],
				// ['河南', 90],
				// ['湖南', 80],
				// ['山东', 20],
				// ['江西', 30],
				// ['浙江', 40]
			],
			jjfztkShow: false,
			hswhtkShow: false,
			stjstkShow: false,
			wgzltkShow: false,
			man_active: require('@/assets/hszl/man_active.png'),
			man_normal: require('@/assets/hszl/man_normal.png'),
			woman_normal: require('@/assets/hszl/woman_normal.png'),
			woman_active: require('@/assets/hszl/woman_active.png'),
			jgzzShow: false,
			zdcsShow: false,
			djylShow: false,
			bjxqShow: false,
			showArea: false,
			isyjxqShow: false,
			yjxqList: [],
			popAreaInfo: {},
			areaPointList: [],
			jgzzActive: 0,
			zdcsActive: 0,
			csglTitleBtns: ['事件', '部件'],
			shbzTitleBtns: ['社会保险', '住房保险'],
			showCesiumMap: false,
			videoUrl: require('@/assets/leader/video/bg1.mp4'),
			data1Url: {
				gdp: require('@/assets/hszl/icon1.png'),
				ybggys: require('@/assets/hszl/icon2.png'),
				gdzctz: require('@/assets/hszl/icon3.png'),
				gysczz: require('@/assets/hszl/icon4.png'),
				xflsze: require('@/assets/hszl/icon5.png'),
				fwyysr: require('@/assets/hszl/icon6.png'),
			},
			data3: [
				{
					name: '农机总量',
					num: '0',
				},
				{
					name: '在线农机',
					num: '0',
				},
				{
					name: '活跃农机',
					num: '0',
				},
			],
			data4Url: {
				djxxdw: require('@/assets/hszl/icon8.png'),
				djxxdy: require('@/assets/hszl/icon9.png'),
				djxxdzz: require('@/assets/hszl/icon10.png'),
				zsdzz: require('@/assets/hszl/icon11.png'),
				djxxdzb: require('@/assets/hszl/icon12.png'),
				djxxejdzb: require('@/assets/hszl/icon13.png'),
			},
			// data5_1: [
			//   {
			//     name: '区域农机社会化服务中心',
			//     num: '0',
			//     unit: '家'
			//   },
			//   {
			//     name: '救灾机具',
			//     num: '0',
			//     unit: '台'
			//   },
			//   {
			//     name: '机库棚',
			//     num: '0',
			//     unit: '个'
			//   }
			// ],
			// data5_2: [
			//   {
			//     name: '农机应急作业服务队',
			//     num: '0',
			//     unit: '支'
			//   },
			//   {
			//     name: '区域农业应急救灾中心',
			//     num: '0',
			//     unit: '支'
			//   },
			//   {
			//     name: '机具保有量',
			//     num: '0',
			//     unit: '台'
			//   }
			// ],
			data5_1: [
				{
					code: 0,
					name: '农机应急作业服务队',
					num: '0',
					unit: '支',
				},
				{
					code: 1,
					name: '区域农机社会化服务中心',
					num: '0',
					unit: '家',
				},
				{
					code: 2,
					name: '区域农业应急救灾中心',
					num: '0',
					unit: '家',
				},
			],
			data5_2: [
				{
					code: 3,
					name: '机具保有量',
					num: '0',
					unit: '台',
				},
				{
					code: 4,
					name: '救灾机具',
					num: '0',
					unit: '台',
				},
				{
					code: 5,
					name: '机库棚',
					num: '0',
					unit: '个',
				},
			],
			// data8: {
			//   data: [
			//     ['product', '年总数'],
			//     ['0-18', 10000],
			//     ['19-25', 10000],
			//     ['25-40', 10000],
			//     ['41-60', 10000],
			//     ['60岁以上', 10000]
			//   ],
			//   options: {
			//     colors: ['#2EF6FF', '#6D5AE2', '#2B8EF3', '#F7B13F', '#F5616F'],
			//     alpha: 65,
			//     pieSize: 220,
			//     pieInnerSize: 160,
			//     position: ['35%', '-50%'],
			//     bgImg: {
			//       top: '60%',
			//       left: '37%'
			//     },
			//     unit: '',
			//     title: {
			//       fontSize: '16px',
			//       top: 70,
			//       left: -20
			//       // textColor: 'rgba(255, 255, 255, 0)',
			//     },
			//     subtitle: {
			//       fontSize: '14px',
			//       top: 90
			//       // textColor: 'rgba(255, 255, 255, 0)',
			//     },
			//     legend: {
			//       orient: 'vertical',
			//       align: 'center',
			//       verticalAlign: 'bottom',
			//       top: -10,
			//       left: 160
			//     }
			//   }
			// },
			swiperOption: {
				observer: true,
				observeParents: true,
				autoHeight: true,
				spaceBetween: 0,
				autoplay: {
					delay: 3500,
					disableOnInteraction: false,
				},
				slidesPerView: 'auto',
				grabCursor: true,
				autoplayDisableOnInteraction: false,
				mousewheelControl: true,
				pagination: {
					el: '.swiper-pagination',
				},
			},
			activaIdx: 4,
			isShowEventDetail: false,
			isShowCountryPart: false,
			isXlgcShow: false,
			isXlgcShow1: false,
			isWgllShow: false,
			isdzzShow: false,
			isxzqyShow: false,
			iszwzxShow: false,
			iszhzxShow: false,
			iscsglgdShow: false,
			isshaqShow: false,
			issthbShow: false,
			iswhlyShow: false,
			isyjfkShow: false,
			isggjtShow: false,
			iszdqyShow: false,
			isxxShow: false,
			iswbShow: false,
			isylcsShow: false,
			ishczShow: false,
			isdyyShow: false,
			introUrl: '',
			// introWord: '',
			// peoTotal: '',
			// maleNum: '',
			// femaleNum: '',
			// maleNumRate: '',
			// femaleNumRate: '',
			// qyswgTotal: 0,
			wgcurrentMonth: 8,
			wgsjmonthList: [],
			currentAgmachInfo: {},
			weatherAlarmType: '',
			weatherAlarmLevel: '',
			amachTypeList: [],
			agmachTypeCode: '',
			agmachTypeName: '',
			lastAreaLevel: [],
			amachTypeArr: [],
			currentTabIndex: 0,
			isJzAmach: false,
			currentMapLevel: 'country',
		}
	},
	created() {
		// this.getHsjjVideoFn()
		// this.getHsjjFn()
		// this.getJjglFn()
		// this.getKqzlFn()
		// this.getDjxxFn()
		// this.getRkglFn()
		// this.getRkglAgeFn()
		// this.getQyswgsjFn()
	},
	beforeDestroy() {
		this.timer && clearInterval(this.timer)
		this.timer1 && clearInterval(this.timer1)
	},
	mounted() {
		// 获取滚动容器和文本元素
		// const scrollingText = document.querySelector('.introduce')
		// const text = document.querySelector('.introduce p')
		// // 获取文本元素的高度
		// const textHeight = text.offsetHeight

		// 定义滚动函数
		// function scroll() {
		//   // console.log('scrollingText.scrollTop',scrollingText.scrollTop);
		//   // 如果文本元素已经完全滚动出去，则将其重置到滚动容器的顶部
		//   if (scrollingText.scrollTop >= 96) {
		//     scrollingText.scrollTop = 0
		//   }
		//   // 否则，将滚动容器向上滚动一个像素的距离
		//   else {
		//     scrollingText.scrollTop += 1
		//   }
		// }

		// 每隔20毫秒调用一次滚动函数
		// this.timer = window.setInterval(scroll, 100)

		// 获取安全监管系统的token
		this.getNjAreaDayType()
		// this.aqjgGetTokenFn()

		this.cscpCurrentUserDetails()
		// this.getAgmachTypeList();

		// this.getCscpBasicHxItemCode1('weatherAlarmType')
		// this.getCscpBasicHxItemCode2('weatherAlarmLevel')

		// this.clusterData = clusterData1.concat(clusterData2, clusterData3)
		this.clusterData = clusterData
	},
	watch: {},
	computed: {
		formatNumber() {
			return function(param) {
				console.log(param)
				if (this.isEmpty(param) || param.length < 4 || isNaN(param)) {
					return param
				}

				let small = ''
				let isSmall = false
				if (param.split('.').length === 2) {
					// 有小数
					isSmall = true
					small = param.split('.')[1]
					param = param.split('.')[0]
				}

				let result = ''

				while (param.length > 3) {
					result = ',' + param.slice(-3) + result
					param = param.slice(0, param.length - 3)
				}

				if (param) {
					result = param + result
				}

				if (isSmall) {
					result = result + '.' + small
				}
				console.log(result)
				return result
			}
		},
		initOptions1() {
			return {
				yAxis: {
					name: '台/天',
					nameTextStyle: {
						fontSize: 12,
						padding: [30, 0, 0, 0],
					},
					axisLabel: {
						textStyle: {
							fontSize: 12,
						},
					},
				},
				xAxis: {
					axisLabel: {
						show: true,
						textStyle: {
							fontSize: 12,
						},
					},
				},
				// dataZoom: [
				//   {
				//     type: 'inside', // 内置型式的 dataZoom 组件
				//     xAxisIndex: 0, // 对应 x 轴的索引，默认为 0
				//     start: 0, // 起始位置的百分比
				//     end: 12, // 结束位置的百分比
				//     realtime: true // 启用实时滚动
				//   }
				// ]
			}
		},
		initOption2() {
			return {
				yAxis: [
					{
						minInterval: 1,
					},
					{
						minInterval: 1,
					},
				],
				tooltip: {
					formatter: (params) => {
						let relVal = params[0].name
						for (let i = 0, l = params.length; i < l; i++) {
							let seriesName = params[i].seriesName + ':'
							let unit = params[i].seriesType == 'bar' ? '亩' : '台'
							relVal += '<br/>' + params[i].marker + seriesName + params[i].value + unit
						}
						return relVal
					},
				},
				series: [
					{
						barWidth: 30,
					},
				],
			}
		},
		barInitOptions() {
			return {
				yAxis: {
					nameTextStyle: {
						width: 200,
					},
				},
				xAxis: [
					{
						show: false,
						axisTick: {
							show: false, // 不显示坐标轴刻度线
						},
						axisLine: {
							show: false, // 不显示坐标轴线
						},
						axisLabel: {
							show: false, // 不显示坐标轴上的文字
						},
						// max: Math.max(...this.barChartData.map(item => { return item[1] } )),
						nameTextStyle: {
							width: 200,
						},
					},
				],
				grid: {
					right: '10%',
					left: '5%',
				},
				tooltip: {
					show: true,
					triggerOn: 'mousemove',
					formatter: (params) => {
						var relVal = ''
						for (var i = 0, l = params.length; i < l; i++) {
							var unit = '台'
							relVal += params[i].marker + params[i].value + unit
						}
						return relVal
					},
				},
				// dataZoom: [
				//   {
				//     type: 'inside', // 内置型式的 dataZoom 组件
				//     yAxisIndex: [0], // 对应 x 轴的索引，默认为 0
				//     start: 0, // 起始位置的百分比
				//     end: 40, // 结束位置的百分比
				//     realtime: true,// 启用实时滚动
				//     zoomOnMouseWheel: false,  // 关闭滚轮缩放
				//     moveOnMouseWheel: true, // 开启滚轮平移
				//     moveOnMouseMove: true  // 鼠标移动能触发数据窗口平移
				//   },
				//   {
				//     type: 'slider',
				//     realtime: true,
				//     startValue: 0,
				//     endValue: 5,
				//     width: '2',
				//     height: '100%',
				//     yAxisIndex: [0], // 控制y轴滚动
				//     fillerColor: 'rgba(154, 181, 215, 1)', // 滚动条颜色
				//     borderColor: 'rgba(17, 100, 210, 0.12)',
				//     backgroundColor: '#cfcfcf', //两边未选中的滑动条区域的颜色
				//     handleSize: 0, // 两边手柄尺寸
				//     showDataShadow: false, //是否显示数据阴影 默认auto
				//     showDetail: false, // 拖拽时是否展示滚动条两侧的文字
				//     top: '1%',
				//     right: '5'
				//   }
				// ],
			}
		},
	},
	methods: {
		handleItemClick(it, location) {
			const { code } = it
			switch (code) {
				case 0:
					this.currentTabIndex = 1
					this.isyjzyShow = true
					this.isJzAmach = false
					break
				case 1:
					this.currentTabIndex = 0
					this.isyjzyShow = true
					this.isJzAmach = false
					break
				case 2:
					this.currentTabIndex = 2
					this.isyjzyShow = true
					this.isJzAmach = false
					break
				case 3:
					this.currentTabIndex = 3
					this.isyjzyShow = true
					this.isJzAmach = false

					break
				case 4:
					this.currentTabIndex = 3
					this.isyjzyShow = true
					this.isJzAmach = true
					// this.$store.commit('setHeaderMainCurrentIndex', 0)
					break
				case 5:
					this.currentTabIndex = 0
					this.isyjzyShow = false
					this.isJzAmach = false
					// console.log('执行了')
					// this.$store.commit('setHeaderMainCurrentIndex', 0)
					break
				default:
					break
			}
		},
		ssqy() {
			console.log(this.ssnr)
			this.ssList = []
			if (this.ssnr) {
				this.sspopList = true
				this.queryByList(this.ssnr)
			} else {
				this.sspopList = false
			}
		},
		async queryByList() {
			this.ssList = []
			let res = await queryByList({
				areaName: this.ssnr,
			})
			console.log(res)
			if (res.code == 0) {
				this.sspopList = true
				this.ssList = []
				this.ssList = res.data
			} else if (res.code == -1) {
				this.sspopList = false
				this.ssList = []
				this.$message({
					message: res.msg,
					type: 'warning',
				})
			}
		},
		ssxz(item) {
			console.log(item)
			this.areaLevel = item.level
			this.areaId = item.areaId
			this.$store.commit('invokerightShow5', item)
			if (this.$refs.leafletMap.lastAreaCode.indexOf(item.areaId) == -1) {
				this.$refs.leafletMap.lastAreaCode.push(item.areaId)
			}
			if (item.level == '3') {
				//区县级别
				setTimeout(() => {
					this.$refs.leafletMap.loadGeoJsonLayerFromUrl(
						`https://geo.datav.aliyun.com/areas_v3/bound/${item.areaId}.json`,
						'geojson',
						false,
					)
					// this.$refs.leafletMap.loadGeoJsonLayerFromUrl(`https://dpzs.camtc.cn/areas_v3/bound/${item.areaId}.json`, 'geojson', false)
				}, 300)
			} else {
				setTimeout(() => {
					this.$refs.leafletMap.loadGeoJsonLayerFromUrl(
						`https://geo.datav.aliyun.com/areas_v3/bound/${item.areaId}_full.json`,
						'geojson',
						item.level == 0 ? true : false,
					)
					// this.$refs.leafletMap.loadGeoJsonLayerFromUrl(`https://dpzs.camtc.cn/areas_v3/bound/${item.areaId}_full.json`, 'geojson', item.level == 0 ? true : false)
				}, 300)
			}
			this.removeAllPoi()
			this.activaIdx = -1
			this.mapShow = -1
			this.getInitData()
			this.ssboxShow = false
			this.sspopShow = false
			this.sspopList = false
			this.ssnr = ''
			this.ssList = []
		},
		updateChange(e) {
			let agmachTypeCode = this.amachTypeListText[e][0]
			this.getOnlineCounts(agmachTypeCode)
		},
		async getOnlineCounts(e) {
			this.onlineData = []
			let res = await onlineCounts({
				areaId: this.areaId == '100000' ? '' : this.areaId,
				day: dayjs(new Date())
					.subtract(1, 'days')
					.format('YYYY-MM-DD'),
				agmachTypeId: e || '',
			})
			if (res.data?.length > 0) {
				console.log('res.data--==', res.data)

				this.onlineData = [['product', '在线量']].concat(
					res.data.map((item) => {
						// return [item.timeDot.substring(0, 2), item.COUNT]
						return [item.timeDot, item.COUNT]
					}),
				)
			}
		},
		dqbfSelect() {
			let time = this.dqbf + '-' + this.dqyfs
			if (this.njzytjBtn == 0) {
				if (this.groupLevel == 2) {
					this.startDate = dayjs(this.dqbf + '-')
						.startOf('year')
						.format('YYYY-MM-DD')
					this.endDate = dayjs(this.dqbf + '-')
						.endOf('year')
						.format('YYYY-MM-DD')
					this.workTrend()
				} else if (this.groupLevel == 3) {
					this.startDate = dayjs(time)
						.startOf('month')
						.format('YYYY-MM-DD')
					this.endDate = dayjs(time)
						.endOf('month')
						.format('YYYY-MM-DD')
					this.workTrend2()
				}
			} else if (this.njzytjBtn == 1) {
				if (this.dqyfs) {
					this.startDate = dayjs(time)
						.startOf('month')
						.format('YYYY-MM-DD')
					this.endDate = dayjs(time)
						.endOf('month')
						.format('YYYY-MM-DD')
					this.work(
						dayjs(time)
							.startOf('month')
							.format('YYYY-MM-DD'),
						dayjs(time)
							.endOf('month')
							.format('YYYY-MM-DD'),
					)
				} else {
					this.startDate = dayjs(this.dqbf + '-')
						.startOf('year')
						.format('YYYY-MM-DD')
					this.endDate = dayjs(this.dqbf + '-')
						.endOf('year')
						.format('YYYY-MM-DD')
					this.work(
						dayjs(this.dqbf + '-')
							.startOf('year')
							.format('YYYY-MM-DD'),
						dayjs(this.dqbf + '-')
							.endOf('year')
							.format('YYYY-MM-DD'),
					)
				}
			}
		},
		dqyfSelect() {
			this.dqyfs = this.dqyf
			this.groupLevel = 3
			let time = this.dqbf + '-' + this.dqyfs
			if (this.njzytjBtn == 0) {
				if (this.groupLevel == 2) {
					this.startDate = dayjs(this.dqbf + '-')
						.startOf('year')
						.format('YYYY-MM-DD')
					this.endDate = dayjs(this.dqbf + '-')
						.endOf('year')
						.format('YYYY-MM-DD')
					this.workTrend()
				} else if (this.groupLevel == 3) {
					this.startDate = dayjs(time)
						.startOf('month')
						.format('YYYY-MM-DD')
					this.endDate = dayjs(time)
						.endOf('month')
						.format('YYYY-MM-DD')
					this.workTrend2()
				}
			} else if (this.njzytjBtn == 1) {
				if (this.dqyfs) {
					this.startDate = dayjs(time)
						.startOf('month')
						.format('YYYY-MM-DD')
					this.endDate = dayjs(time)
						.endOf('month')
						.format('YYYY-MM-DD')
					this.work(
						dayjs(time)
							.startOf('month')
							.format('YYYY-MM-DD'),
						dayjs(time)
							.endOf('month')
							.format('YYYY-MM-DD'),
					)
				} else {
					this.startDate = dayjs(this.dqbf + '-')
						.startOf('year')
						.format('YYYY-MM-DD')
					this.endDate = dayjs(this.dqbf + '-')
						.endOf('year')
						.format('YYYY-MM-DD')
					this.work(
						dayjs(this.dqbf + '-')
							.startOf('year')
							.format('YYYY-MM-DD'),
						dayjs(this.dqbf + '-')
							.endOf('year')
							.format('YYYY-MM-DD'),
					)
				}
			}
		},

		async njlxqh(agmachTypeId) {
			this.njfbShow = false
			if (!this.sourceType) {
				this.sourceType = true
				this.removeAllPoi()
				if (this.sameMapShow) {
					//判断本次的绘制分类参数是否和上次的一致，若一致则清除掉上次的绘制
					if (this.resourceTypes == agmachTypeId) {
						this.resourceTypes = -1
						this.sourceType = false
						this.activeAmachType = ''
						this.removeAllPoi()
						this.currentDrawClass = ''
						// if (this.amachTypeList.length) {
						//   this.$refs.leafletMap.clusterMarkers();
						// }
					} else {
						this.resourceTypes = agmachTypeId
						this.sourceType = false
						//若绘制的是农机分布则执行
						if (this.mapShow == 3) {
							this.agmachDistribution('njfbType')
						} else if (this.mapShow == 4) {
							this.agmachDistribution('zxnjType')
						}
					}
				} else {
					this.resourceTypes = agmachTypeId
					this.sourceType = false
					if (this.mapShow == 3) {
						this.agmachDistribution('njfbType')
					} else if (this.mapShow == 4) {
						this.agmachDistribution('zxnjType')
					}
				}
			}
		},

		async agmachDistribution(drawClass) {
			this.currentDrawClass = drawClass
			//若当前的areaLevel是全国级
			console.log('this.areaLevel', this.areaLevel)
			if (this.areaLevel == 0) {
				this.getDistributionCount(this.resourceTypes, drawClass)
			} else {
				this.identification(drawClass)
			}
		},

		updateAmachTypeCode(e) {
			console.log('e', e)
			this.agmachTypeCode2 = e
			if (this.njzytjBtn == 0) {
				if (this.groupLevel == 2) {
					this.workTrend()
				} else if (this.groupLevel == 3) {
					this.workTrend2()
				}
			} else if (this.njzytjBtn == 1) {
				if (this.dqyfs) {
					let time = this.dqbf + '-' + this.dqyfs
					this.work(
						dayjs(time)
							.startOf('month')
							.format('YYYY-MM-DD'),
						dayjs(time)
							.endOf('month')
							.format('YYYY-MM-DD'),
					)
				} else {
					this.work(
						dayjs(this.dqbf + '-')
							.startOf('year')
							.format('YYYY-MM-DD'),
						dayjs(this.dqbf + '-')
							.endOf('year')
							.format('YYYY-MM-DD'),
					)
				}
			}
		},
		mapActive(index) {
			// if (this.mapShow != index) {
			//   this.removeAllPoi()
			// }
			if (this.mapShow == index) {
				this.sameMapShow = true
			} else {
				this.sameMapShow = false
			}
			this.mapShow = index
			if (this.mapShow == 2) {
				this.zyfbShow = true
				this.njfbShow = false
				this.iskqzyShow = false
			}
			if (this.mapShow == 3) {
				this.zyfbShow = false
				this.njfbShow = true
				this.iskqzyShow = false
				this.activeAmachType = -1
				this.activaIdx = -1
			}
			if (this.mapShow == 4) {
				this.zyfbShow = false
				this.njfbShow = true
				this.iskqzyShow = false
				this.activeAmachType = -1
				this.activaIdx = -1
			}
			if (this.mapShow == 5) {
				this.removeAllPoi()
				this.zyfbShow = false
				this.njfbShow = false
				this.iskqzyShow = true
				if (this.areaLevel != 0) {
					this.levelCount()
					this.exit(0)
					this.exit(1)
				} else {
					this.exitCount()
					this.onlyExit()
				}
			}

			this.jgzzShow = false
			if (this.activaIdx == index) {
				this.activaIdx = -1
				this.removeAllPoi()
				if (this.$refs.leafletMap.polygon) {
					this.$refs.leafletMap.polygon.remove()
				}
			} else {
				this.activaIdx = index
			}
			this.toolTipShow = false
			// for (let key in this.resourceType) {
			//   this.resourceType[key].flag = true
			// }

			if (this.activaIdx == 1) {
				this.removeAllPoi()
				this.getAllAlarmWithGPS()
			} else if (this.activaIdx == 2) {
				// if (this.resourceType == 1) {
				//   this.getEmergencyMap(1)
				// }
				// if (this.resourceType == 2) {
				//   this.getEmergencyMap(2)
				// }
				// if (this.resourceType == 3) {
				//   this.getEmergencyMap2()
				// }
			} else if (this.activaIdx == 3) {
			} else if (this.activaIdx == 4) {
				// this.removeAllPoi()
				// this.identification()
			} else if (this.activaIdx == 6) {
				this.removeAllPoi()
				this.geojson()
			}
		},
		async geojson() {
			let res = await geojson({
				areaId: this.areaId,
			})
			console.log(res)
			this.$refs.leafletMap.serefresh(res.data)
		},
		async exit(status) {
			let res = await exit({
				startDate: dayjs()
					.subtract(1, 'days')
					.format('YYYY-MM-DD'),
				endDate: dayjs()
					.subtract(1, 'days')
					.format('YYYY-MM-DD'),
				areaId: this.areaId,
				level: this.areaLevel,
				status: status,
			})
			if (res.data?.length > 0) {
				if (status == 0) {
					this.$refs.kqzyPop.track1(res.data)
				} else {
					this.$refs.kqzyPop.track2(res.data)
				}
			}
		},
		async onlyExit() {
			let res = await onlyExit({
				startDate: dayjs()
					.subtract(1, 'days')
					.format('YYYY-MM-DD'),
				endDate: dayjs()
					.subtract(1, 'days')
					.format('YYYY-MM-DD'),
				areaId: this.areaId,
				level: this.areaLevel,
			})
			if (res.data?.length > 0) {
				this.$refs.kqzyPop.track2(res.data)
			}
		},
		async exitCount() {
			let res = await exitCount({
				startDate: dayjs()
					.subtract(1, 'days')
					.format('YYYY-MM-DD'),
				endDate: dayjs()
					.subtract(1, 'days')
					.format('YYYY-MM-DD'),
				areaId: this.areaId == '100000' ? '' : this.areaId,
				level: this.areaLevel,
			})
			if (res.data?.length > 0) {
				this.$refs.kqzyPop.exitCount(res.data[0], this.areaLevel)
			}
		},
		async levelCount() {
			let res = await levelCount({
				startDate: dayjs()
					.subtract(1, 'days')
					.format('YYYY-MM-DD'),
				endDate: dayjs()
					.subtract(1, 'days')
					.format('YYYY-MM-DD'),
				areaId: this.areaId == '100000' ? '' : this.areaId,
				level: this.areaLevel,
			})
			if (res.data?.length > 0) {
				this.$refs.kqzyPop.exitCount(res.data[0], this.areaLevel)
			}
		},
		// 当前层级非省、市级，即区县级
		async identification(drawClass) {
			let data = []
			let clusterData = []
			if (drawClass == 'njfbType') {
				let res = await agmachtypeLocList({
					areaId: this.areaId == 100000 ? '' : this.areaId,
					// dataSources: 1,
					agmachTypeId: this.resourceTypes == 100000 ? '' : this.resourceTypes,
				})

				data = res.data.map((item) => {
					return {
						latlng: [item.lat, item.lon],
						icon: {
							iconUrl: require('@/assets/mskx/icon_hgzx_b.png'),
							iconSize: [48, 62],
							iconAnchor: [16, 42],
						},
						// props: {
						// 	id: 'hgzxId',
						// 	type: 'hgzxType',
						// 	info: { ...item },
						// },
						props: {
							id: 'agmachId',
							type: 'njfbType',
							info: { ...item },
						},
					}
				})
				console.log('data--==', data)
				clusterData = [].concat(
					res.data.map((item) => {
						// return [item.agmachId || '', '-1', '-1', item.lon, item.lat]
						return {
							latlng: [item.lat, item.lon],
							icon: {
								iconUrl: require('@/assets/mskx/icon_hgzx_b.png'),
								iconSize: [48, 62],
								iconAnchor: [16, 42],
							},
							// props: {
							// 	id: 'hgzxId',
							// 	type: 'hgzxType',
							// 	info: { ...item },
							// },
							props: {
								id: 'agmachId',
								type: 'njfbType',
								info: { ...item },
							},
						}
					}),
				)
				this.$refs.leafletMap.removeLayer('zxnjType')
			} else if (drawClass == 'zxnjType') {
				let res = await identification({
					areaId: this.areaId == '100000' ? '' : this.areaId,
					dataSources: 1,
					agmachTypeId: this.resourceTypes == '100000' || this.resourceTypes == '-1' ? '' : this.resourceTypes,
				})
				data = res.data.map((item) => {
					return {
						latlng: [item.lat, item.lon],
						icon: {
							iconUrl: require('@/assets/mskx/icon_hgzx_b.png'),
							iconSize: [48, 62],
							iconAnchor: [16, 42],
						},
						// props: {
						// 	id: 'hgzxId',
						// 	type: 'hgzxType',
						// 	info: { ...item },
						// },
						props: {
							id: 'agmachId',
							type: 'zxnjType',
							info: { ...item },
						},
					}
				})
				clusterData = [].concat(
					res.data.map((item) => {
						// return [item.agmachId || '', '-1', '-1', item.lon, item.lat]
						return {
							latlng: [item.lat, item.lon],
							icon: {
								iconUrl: require('@/assets/mskx/icon_hgzx_b.png'),
								iconSize: [48, 62],
								iconAnchor: [16, 42],
							},
							// props: {
							// 	id: 'hgzxId',
							// 	type: 'hgzxType',
							// 	info: { ...item },
							// },
							props: {
								id: 'agmachId',
								type: 'zxnjType',
								info: { ...item },
							},
						}
					}),
				)

				// 需要清除农机分布的地图渲染数据
				this.$refs.leafletMap.removeLayer('njfbType')
			} else {
				return
			}
			//绘制散点

			// 绘制聚合点
			if (this.areaLevel == 3) {
				this.$refs.leafletMap.drawSstsPoiMarker(data, drawClass, false)
			} else {
				this.$refs.leafletMap.drawSstsBigDataClusterPoint(
					clusterData,
					require('@/assets/mskx/icon_nj_b.png'),
					[48, 62],
					{
						largeIconUrl: './imgs/cluster/nj1.png',
						mediumIconUrl: './imgs/cluster/nj2.png',
						smallIconUrl: './imgs/cluster/nj3.png',
					},
					drawClass,
				)
			}

			// console.log(res)
			// let clusterData = [].concat(
			// 	res.data.map((item) => {
			// 		return [item.agmachId || '', '-1', '-1', item.lon, item.lat]
			// 	}),
			// )

			// this.$refs.leafletMap.drawBigDataClusterPoint(
			// 	clusterData,
			// 	require('@/assets/mskx/icon_nj_b.png'),
			// 	[48, 62],
			// 	{
			// 		largeIconUrl: './imgs/cluster/nj1.png',
			// 		mediumIconUrl: './imgs/cluster/nj2.png',
			// 		smallIconUrl: './imgs/cluster/nj3.png',
			// 	},
			// 	'zxnjType',
			// )
		},
		/* 单位w */
		exchangeUnit(num, fixedNum = 2) {
			num = Number(num)
			if (num >= 10000) {
				var digit = num / 10000
				var count = digit.toFixed(2)
				count = Math.max(count)
				return count
			} else {
				return num
			}
		},
		async count1(startDate, endDate) {
			let res = await count({
				areaId: this.areaId == '100000' ? '' : this.areaId,
				agmachTypeCode: this.resourceTypes == '100000' || this.resourceTypes == '-1' ? '' : this.resourceTypes,
				startDate: startDate,
				endDate: endDate,
			})
			console.log(res)
			this.tjxxList[1].value = this.exchangeUnit(res.data[0].workArea)
			this.tjxxList[1].unit = res.data[0].workArea >= 10000 ? '万亩' : '亩'
		},
		async count2(startDate, endDate) {
			let res = await count({
				areaId: this.areaId == '100000' ? '' : this.areaId,
				agmachTypeCode: this.resourceTypes == '100000' || this.resourceTypes == '-1' ? '' : this.resourceTypes,
				startDate: startDate,
				endDate: endDate,
			})
			console.log(res)
			this.tjxxList[2].value = this.exchangeUnit(res.data[0].workArea)
			this.tjxxList[2].unit = res.data[0].workArea >= 10000 ? '万亩' : '亩'
		},
		async count3(startDate, endDate) {
			let res = await count({
				areaId: this.areaId == '100000' ? '' : this.areaId,
				agmachTypeCode: this.resourceTypes == '100000' || this.resourceTypes == '-1' ? '' : this.resourceTypes,
				startDate: startDate,
				endDate: endDate,
			})
			console.log(res)
			this.tjxxList[3].value = this.exchangeUnit(res.data[0].workArea)
			this.tjxxList[3].unit = res.data[0].workArea >= 10000 ? '万亩' : '亩'
		},
		njzytj(e) {
			if (this.njzytjBtn == 0) {
				this.dqyf = this.qgdCodejjcdOptions2[this.qgdCodejjcdOptions2.findIndex((v) => v.itemCode == e.name.substr(5, 10))].itemValue
				this.dqyfs = e.name.substr(5, 10)
				if (e.name) {
					if (!(e.name == '作业面积：亩' || e.name == '台数')) {
						if (this.groupLevel == 2) {
							this.groupLevel = 3
							this.startDate = dayjs(e.name)
								.startOf('month')
								.format('YYYY-MM-DD')
							this.endDate = dayjs(e.name)
								.endOf('month')
								.format('YYYY-MM-DD')
							this.workTrend2()
						}
					}
				}
			} else if (this.njzytjBtn == 1) {
				if (e.name) {
					if (!(e.name == '作业面积：亩' || e.name == '台数')) {
						let areaId = this.sczzData1[0].data.filter((item) => item.name == e.name)
						// this.areaId = areaId[0].areaId
						this.areaIdList.push(areaId[0].areaId)
						console.log('this.areaIdList', this.areaIdList)
						if (this.dqyfs) {
							let time = this.dqbf + '-' + this.dqyfs
							this.work(
								dayjs(time)
									.startOf('month')
									.format('YYYY-MM-DD'),
								dayjs(time)
									.endOf('month')
									.format('YYYY-MM-DD'),
							)
						} else {
							this.work(
								dayjs(this.dqbf + '-')
									.startOf('year')
									.format('YYYY-MM-DD'),
								dayjs(this.dqbf + '-')
									.endOf('year')
									.format('YYYY-MM-DD'),
							)
						}
					}
				}
			}
		},
		njzytjFh() {
			if (this.njzytjBtn == 0 && this.groupLevel != 2) {
				this.dqyf = ''
				this.groupLevel = 2
				this.startDate = dayjs(this.dqbf + '-')
					.startOf('year')
					.format('YYYY-MM-DD')
				this.endDate = dayjs(this.dqbf + '-')
					.endOf('year')
					.format('YYYY-MM-DD')
				this.workTrend()
			} else if (this.njzytjBtn == 1) {
				this.areaIdList.splice(this.areaIdList.length - 1, 1)
				console.log('this.areaIdList', this.areaIdList)
				if (this.dqyfs) {
					let time = this.dqbf + '-' + this.dqyfs
					this.work(
						dayjs(time)
							.startOf('month')
							.format('YYYY-MM-DD'),
						dayjs(time)
							.endOf('month')
							.format('YYYY-MM-DD'),
					)
				} else {
					this.work(
						dayjs(this.dqbf + '-')
							.startOf('year')
							.format('YYYY-MM-DD'),
						dayjs(this.dqbf + '-')
							.endOf('year')
							.format('YYYY-MM-DD'),
					)
				}
			}
		},
		handleChange(i) {
			console.log(i)
			this.njzytjBtn = i
			if (this.njzytjBtn == 0) {
				// this.startDate = dayjs().subtract(11, 'month').startOf('month').format('YYYY-MM-DD')
				// this.endDate = dayjs().format('YYYY-MM-DD')
				if (this.groupLevel == 2) {
					this.workTrend()
				} else if (this.groupLevel == 3) {
					this.workTrend2()
				}
			} else if (this.njzytjBtn == 1) {
				if (this.dqyfs) {
					let time = this.dqbf + '-' + this.dqyfs
					this.work(
						dayjs(time)
							.startOf('month')
							.format('YYYY-MM-DD'),
						dayjs(time)
							.endOf('month')
							.format('YYYY-MM-DD'),
					)
				} else {
					this.work(
						dayjs(this.dqbf + '-')
							.startOf('year')
							.format('YYYY-MM-DD'),
						dayjs(this.dqbf + '-')
							.endOf('year')
							.format('YYYY-MM-DD'),
					)
				}
			}
		},
		move(arr, index, upDown, swapIndex = index + 1) {
			console.log(arr)
			console.log(index)
			console.log(upDown)
			console.log(swapIndex)
			if (upDown === 'up') {
				arr.splice(index - 1, 0, arr[index])
				arr.splice(index + 1, 1)
			} else if (upDown === 'down') {
				arr.splice(index + 2, 0, arr[index])
				arr.splice(index, 1)
			} else if (upDown === 'start') {
				arr.splice(0, 0, arr[index])
				arr.splice(index + 1, 1)
			} else if (upDown === 'end') {
				arr.splice(arr.length, 0, arr[index])
				arr.splice(index, 1)
			} else if (upDown === 'swap') {
				arr[swapIndex] = arr.splice(index, 1, arr[swapIndex])[0]
			}
		},
		// async amachTypeClick(e) {
		//   if (this.allowClicks) {
		//     this.allowClicks = false
		//     this.activaIdx = -1;
		//     console.log('this.activaIdx',this.activaIdx)
		//     this.$refs.LeaderFooter.activaIdx = -1
		//     this.$refs.LeaderFooter.activeList = []
		//     this.removeAllPoi()

		//     if (this.activeAmachType != e[2]) {
		//       this.activeAmachType = e[2];
		//       this.resourceTypes = e[2]
		//       let res = await agmachtypeLocList({
		//         areaId: this.areaId == 100000 ? "" : this.areaId,
		//         dataSources: 1,
		//         agmachTypeId: e[2]
		//       })
		//       // let clusterData = [].concat(
		//       //   res.data.map(item => {
		//       //     return [item.agmachId || '', '-1', '-1', item.lon, item.lat]
		//       //   })
		//       // )
		//       let clusterData = []
		//       for (let i=0;i<res.data.length;i++) {
		//         if (res.data[i].lon&&res.data[i].lat) {
		//           clusterData.push([res.data[i].agmachId||'','-1', '-1', res.data[i].lon, res.data[i].lat])
		//         }
		//       }
		//       // clusterData = clusterData.filter(item => item[3] && item[4])
		//       this.$refs.leafletMap.drawBigDataClusterPoint(
		//         clusterData,
		//         require('@/assets/mskx/icon_nj_b.png'),
		//         [48, 62],
		//         {
		//           largeIconUrl: './imgs/cluster/nj1.png',
		//           mediumIconUrl: './imgs/cluster/nj2.png',
		//           smallIconUrl: './imgs/cluster/nj3.png'
		//         },
		//         'njfbType',
		//         true
		//       )
		//       this.allowClicks = true
		//     } else {
		//       this.activeAmachType="";
		//       this.resourceTypes = -1
		//       if (this.amachTypeList.length) {
		//         this.$refs.leafletMap.clusterMarkers();
		//         this.allowClicks = true
		//       }
		//     }
		//   }
		// },
		async amachTypeClick(e) {
			if (this.allowClicks) {
				this.allowClicks = false
				this.activaIdx = -1
				console.log('this.activaIdx', this.activaIdx)
				this.$refs.LeaderFooter.activaIdx = -1
				this.$refs.LeaderFooter.activeList = []
				this.removeAllPoi()

				if (this.activeAmachType != e[2]) {
					this.activeAmachType = e[2]
					this.resourceTypes = e[2]
					if (this.areaLevel == 0 || this.areaLevel == 1) {
						this.getDistributionCount(e[2])
					} else {
						this.activeAmachType = e[2]
						this.resourceTypes = e[2]
						let res = await agmachtypeLocList({
							areaId: this.areaId == 100000 ? '' : this.areaId,
							// dataSources: 1,
							agmachTypeId: e[2] == 100000 ? '' : e[2],
						})
						// let clusterData = [].concat(
						//   res.data.map(item => {
						//     return [item.agmachId || '', '-1', '-1', item.lon, item.lat]
						//   })
						// )
						let clusterData = []
						for (let i = 0; i < res.data.length; i++) {
							if (res.data[i].lon && res.data[i].lat) {
								clusterData.push([res.data[i].agmachId || '', '-1', '-1', res.data[i].lon, res.data[i].lat])
							}
						}
						// clusterData = clusterData.filter(item => item[3] && item[4])
						this.$refs.leafletMap.drawBigDataClusterPoint(
							clusterData,
							require('@/assets/mskx/icon_nj_b.png'),
							[48, 62],
							{
								largeIconUrl: './imgs/cluster/nj1.png',
								mediumIconUrl: './imgs/cluster/nj2.png',
								smallIconUrl: './imgs/cluster/nj3.png',
							},
							'njfbType',
							true,
						)
					}
					this.allowClicks = true
				} else {
					this.activeAmachType = ''
					this.resourceTypes = -1
					this.removeAllPoi()
					this.allowClicks = true
					// if (this.amachTypeList.length) {
					//   this.$refs.leafletMap.clusterMarkers();
					//   this.allowClicks = true
					// }
				}
			}
		},
		async getDistributionCount(agmachTypeCode, drawClass) {
			let res =
				drawClass == 'njfbType'
					? await getDistributionCount({
							level: this.areaLevel == 0 ? '-1' : this.areaLevel,
							parentAreaId: this.areaId == '100000' ? '' : this.areaId,
							agmachTypeCode: agmachTypeCode == '100000' || agmachTypeCode == '-1' ? '' : agmachTypeCode,
					  })
					: drawClass == 'zxnjType'
					? await getZxnjDistributionCount({
							level: this.areaLevel == 0 ? '-1' : this.areaLevel,
							parentAreaId: this.areaId == '100000' ? '' : this.areaId,
							agmachTypeCode: agmachTypeCode == '100000' || agmachTypeCode == '-1' ? '' : agmachTypeCode,
					  })
					: null
			let numData = []
			if (!res) {
				return
			} else {
				console.log('res.data--==', res.data)
				numData = [].concat(
					res.data.map((item) => {
						return {
							latlng: [item.lat, item.lon],
							props: {
								name: item.areaName,
								num: item.agmachCount,
								adcode: item.areaId,
								level: 'province',
							},
						}
					}),
				)
				this.$refs.leafletMap.drawSstsNumberMarker(numData, drawClass)
			}
		},
		showMoreFn1() {
			this.isyjzyShow = true
		},
		fhEmitai() {
			this.isyjzyShow = true
			this.znzyPopShow = false
		},
		eventInfoBtnClick7(info) {
			console.log('info', info)
			this.isyjzyShow = false
			this.znzyPopShow = true
			this.basicInfomationId = info.id
			this.$refs.znzyPop.title = info.name + '基本情况'
		},
		setCurrentTabIndex(value) {
			this.currentTabIndex = value
		},
		setIsJzAmach(value) {
			this.isJzAmach = value
		},
		njtlTk() {
			this.isnjtlListShow = !this.isnjtlListShow
		},
		sxTk() {
			this.getInitData()
		},
		ssmkQhs() {
			this.sspopShow = !this.sspopShow
			this.sspopList = false
			this.ssList = []
			this.ssnr = ''
		},
		eventInfoBtnClick8(info) {},
		qctc() {
			this.loadFlag = true
			this.activaIdx = -1
			this.mapShow = -1
			this.$refs.LeaderFooter.activaIdx = -1
			this.$refs.LeaderFooter.activeList = []
			this.$refs.LeaderFooter.handleClick()
		},
		handleAmachTypeDrop(e) {
			this.agmachTypeName = e.agmachTypeName
			this.agmachTypeCode = e.agmachTypeCode
			this.getNjTrend()
		},
		eventInfoBtnClick15(i, form) {
			this.getAgmachGuiJiInfo(
				{
					agmachId: form.agmachId,
					startTime: form.workStartTime,
					endTime: form.workEndTime,
				},
				form,
			)
		},
		mkssBtn() {
			this.mkdx = !this.mkdx
			this.$store.commit('invokerightShow4', this.mkdx)
			// this.$store.commit('invokerightShow', !this.mkdx)
		},
		async workTrend() {
			let res = await workTrend({
				groupLevel: this.groupLevel,
				areaId: this.areaId == '100000' ? '' : this.areaId,
				agmachTypeCode: this.agmachTypeCode2,
				startDate: this.startDate,
				endDate: this.endDate,
			})
			console.log(res)
			this.sczzData1[0].data = [].concat(
				res.data[0].map((it) => ({
					name: it.date,
					value: it.workArea,
				})),
			)
			this.sczzData1[1].data = [].concat(
				res.data[0].map((it) => ({
					name: it.date,
					value: it.agmachNum,
				})),
			)
		},
		async workTrend2() {
			let res = await workTrend({
				groupLevel: this.groupLevel,
				areaId: this.areaId == '100000' ? '' : this.areaId,
				agmachTypeCode: this.agmachTypeCode2,
				startDate: this.startDate,
				endDate: this.endDate,
			})
			console.log(res)
			this.sczzData1[0].data = [].concat(
				res.data[0].map((it) => ({
					name: it.date.substr(5, 10),
					value: it.workArea,
				})),
			)
			this.sczzData1[1].data = [].concat(
				res.data[0].map((it) => ({
					name: it.date,
					value: it.agmachNum,
				})),
			)
		},
		async work(startDate, endDate) {
			let res = await work({
				parentAreaId: this.areaIdList[this.areaIdList.length - 1] == '100000' ? '' : this.areaIdList[this.areaIdList.length - 1],
				agmachTypeCode: this.agmachTypeCode2,
				startDate: startDate,
				endDate: endDate,
			})
			console.log(
				'province',
				province.findIndex((v) => v.name == 'it.areaName'),
			)
			this.sczzData1[0].data = [].concat(
				res.data.map((it) => ({
					name:
						province.findIndex((v) => v.regionName == it.areaName) == -1
							? it.areaName
							: province[province.findIndex((v) => v.regionName == it.areaName)].customName,
					value: it.workArea,
					areaId: it.areaId,
				})),
			)
			this.sczzData1[1].data = [].concat(
				res.data.map((it) => ({
					name:
						province.findIndex((v) => v.regionName == it.areaName) == -1
							? it.areaName
							: province[province.findIndex((v) => v.regionName == it.areaName)].customName,
					value: it.agmachNum,
					areaId: it.areaId,
				})),
			)
		},
		async getAgmachGuiJiInfo(data, infoData) {
			let res = await getAgmachGuiJiInfo(data)
			console.log(res)
			if (res?.code == '0') {
				// this.workPopShow = false
				let trackData = []
				trackData = [].concat(res.data.map((it) => [it.lon, it.lat]))
				// this.$refs.leafletMap.loadTrackLayer('trcak', trackData)
				this.isMapShow = true
				this.$nextTick(() => {
					this.$refs.mapPop.track(trackData)
					this.$refs.mapPop.getNjInfo(infoData)
				})
			}
		},
		async getAgmachTypeList() {
			let res = await getAgmachTypeList({
				// parentTypeId:110000,
				subLevel: 1,
				status: 0,
			})
			if (res?.code === 0) {
				this.amachTypeList = res.data
				this.agmachTypeId = res.data[0].agmachTypeId
				this.agmachTypeName = res.data[0].agmachTypeName
			}
			this.getNjTrend()
		},
		async cscpCurrentUserDetails(data) {
			let res = await cscpCurrentUserDetails(data)
			console.log(res)
			if (res?.code == '0') {
				this.roleNames = res.data.roleNames
				if (res.data.areaId) {
					this.areaId = res.data.areaId
					this.areaIdList[0] = res.data.areaId
					this.areaIdNew = res.data.areaIdNew
					this.areaLevel_init = res.data.areaLevel
					this.areaLevel = res.data.areaLevel
					this.$refs.leafletMap.lastAreaCode.push(Number(res.data.areaId))
					this.lastAreaLevel.push(res.data.areaLevel)
					if (res.data.areaId != res.data.areaIdNew) {
						//区县级别
						setTimeout(() => {
							this.$refs.leafletMap.loadGeoJsonLayerFromUrl(
								`https://geo.datav.aliyun.com/areas_v3/bound/${res.data.areaId}.json`,
								'geojson',
								false,
							)
							// this.$refs.leafletMap.loadGeoJsonLayerFromUrl(`https://dpzs.camtc.cn/areas_v3/bound/${res.data.areaId}.json`, 'geojson', false)
						}, 300)
					} else {
						setTimeout(() => {
							this.$refs.leafletMap.loadGeoJsonLayerFromUrl(
								`https://geo.datav.aliyun.com/areas_v3/bound/${res.data.areaId}_full.json`,
								'geojson',
								res.data.areaLevel == 0 ? true : false,
							)
							// this.$refs.leafletMap.loadGeoJsonLayerFromUrl(`https://dpzs.camtc.cn/areas_v3/bound/${res.data.areaId}_full.json`, 'geojson', res.data.areaLevel == 0 ? true : false)
						}, 300)
					}
					// this.getAllAlarmWithGPS();
					this.njlxqh(100000)
					this.getData()
				}
			}
		},
		getData() {
			this.count1(
				dayjs()
					.startOf('year')
					.format('YYYY-MM-DD'),
				dayjs()
					.subtract(1, 'days')
					.format('YYYY-MM-DD'),
			)
			this.count2(
				dayjs()
					.subtract(7, 'days')
					.format('YYYY-MM-DD'),
				dayjs()
					.subtract(1, 'days')
					.format('YYYY-MM-DD'),
			)
			this.count3(
				dayjs()
					.subtract(1, 'days')
					.format('YYYY-MM-DD'),
				dayjs()
					.subtract(1, 'days')
					.format('YYYY-MM-DD'),
			)
			this.getResourceList()
			this.getNjAreaDayType()
			this.getNjCount()
			this.getVehiclesCount()
			this.getNjOnlineCount()
			this.getNjTodayCount()
			this.getNjCurrent7Count()
			this.getAreaDistribute()
			this.getNjTrend()
			this.getOnlineCounts()
			let time = this.dqbf + '-' + this.dqyfs
			if (this.groupLevel == 2) {
				this.startDate = dayjs(this.dqbf + '-')
					.startOf('year')
					.format('YYYY-MM-DD')
				this.endDate = dayjs(this.dqbf + '-')
					.endOf('year')
					.format('YYYY-MM-DD')
				this.workTrend()
			} else if (this.groupLevel == 3) {
				this.startDate = dayjs(time)
					.startOf('month')
					.format('YYYY-MM-DD')
				this.endDate = dayjs(time)
					.endOf('month')
					.format('YYYY-MM-DD')
				this.workTrend2()
			}
		},
		async getCscpBasicHxItemCode1(data) {
			let res = await getCscpBasicHxItemCode(data)
			console.log(res)
			if (res?.code == '0') {
				this.typeOptions1 = res.data
			}
		},
		async getCscpBasicHxItemCode2(data) {
			let res = await getCscpBasicHxItemCode(data)
			console.log(res)
			if (res?.code == '0') {
				this.typeOptions2 = res.data
			}
		},
		async getDryingMap() {
			let res = await getDryingMap()
			let data = res.data.map((item) => {
				return {
					latlng: [item.latitude, item.longitude],
					icon: {
						iconUrl: require('@/assets/mskx/icon_hgzx_b.png'),
						iconSize: [48, 62],
						iconAnchor: [16, 42],
					},
					props: {
						id: 'hgzxId',
						type: 'hgzxType',
						info: { ...item },
					},
				}
			})
			this.$refs.leafletMap.drawPoiMarker(data, 'hgzxType', false)
			// this.$refs.leafletMap.drawPoiMarkerCustomCluster(data, 'hgzxType', true, false, false, {
			//   largeIconUrl: './imgs/cluster/large-icon.png',
			//   mediumIconUrl: './imgs/cluster/medium-icon.png',
			//   smallIconUrl: './imgs/cluster/small-icon.png'
			// })
		},
		async getEmergencyMap(val = 1) {
			let res = await getEmergencyMap({ type: val, area: this.areaId })
			this.sourceType = false
			if (val == 1) {
				// 救灾中心
				let data = res.data.map((item) => {
					return {
						latlng: [item.latitude, item.longitude],
						icon: {
							iconUrl: require('@/assets/mskx/icon_shhfwzx_b.png'),
							iconSize: [48, 62],
							iconAnchor: [16, 42],
						},
						props: {
							id: 'yjjzId',
							type: 'yjjzType',
							info: { ...item },
						},
					}
				})
				// this.$refs.leafletMap.drawPoiMarker(data, 'yjjzType', true, false, true, {
				//   largeIconUrl: './imgs/cluster/qy1.png',
				//   mediumIconUrl: './imgs/cluster/qy2.png',
				//   smallIconUrl: './imgs/cluster/qy3.png'
				// })
				this.$refs.leafletMap.drawPoiMarkerCustomCluster(data, 'yjjzType', true, false, false, {
					largeIconUrl: './imgs/cluster/qy1.png',
					mediumIconUrl: './imgs/cluster/qy2.png',
					smallIconUrl: './imgs/cluster/qy3.png',
				})
			} else if (val == 2) {
				// 服务队
				let data = res.data.map((item) => {
					return {
						latlng: [item.latitude, item.longitude],
						icon: {
							iconUrl: require('@/assets/mskx/icon_fwd_b.png'),
							iconSize: [48, 62],
							iconAnchor: [16, 42],
						},
						props: {
							id: 'fwdId',
							type: 'fwdType',
							info: { ...item },
						},
					}
				})
				// this.$refs.leafletMap.drawPoiMarker(data, 'fwdType', true, false, true, {
				//   largeIconUrl: './imgs/cluster/zyd1.png',
				//   mediumIconUrl: './imgs/cluster/zyd2.png',
				//   smallIconUrl: './imgs/cluster/zyd3.png'
				// })
				this.$refs.leafletMap.drawPoiMarkerCustomCluster(data, 'fwdType', true, false, false, {
					largeIconUrl: './imgs/cluster/zyd1.png',
					mediumIconUrl: './imgs/cluster/zyd2.png',
					smallIconUrl: './imgs/cluster/zyd3.png',
				})
			}
		},
		async getEmergencyMap2() {
			let res = await getEmergencyMap({ type: 1, level: 1, area: this.areaId })
			this.sourceType = false
			// 救灾中心
			let data = res.data.map((item) => {
				return {
					latlng: [item.latitude, item.longitude],
					icon: {
						iconUrl: require('@/assets/mskx/icon_yjjz_b.png'),
						iconSize: [48, 62],
						iconAnchor: [16, 42],
					},
					props: {
						id: 'yjjzId',
						type: 'hgzxType',
						info: { ...item },
					},
				}
			})
			// this.$refs.leafletMap.drawPoiMarker(data, 'hgzxType', true, false, true, {
			//   largeIconUrl: './imgs/cluster/zx1.png',
			//   mediumIconUrl: './imgs/cluster/zx2.png',
			//   smallIconUrl: './imgs/cluster/zx3.png'
			// })
			this.$refs.leafletMap.drawPoiMarkerCustomCluster(data, 'hgzxType', true, false, false, {
				largeIconUrl: './imgs/cluster/zx1.png',
				mediumIconUrl: './imgs/cluster/zx2.png',
				smallIconUrl: './imgs/cluster/zx3.png',
			})
		},
		handleNjInfo() {
			if (this.dwType == 'njfbType2') {
				if (this.trajectoryMark == 1) {
					this.njInfoShow = false
					// this.znzyPopShow = true
					this.workPopShow = true
					this.$refs.historyWorkPop.init()
				} else {
					this.$message('这台农机无轨迹')
				}
			} else {
				this.njInfoShow = false
				// this.znzyPopShow = true
				this.workPopShow = true
				this.$refs.historyWorkPop.init()
			}
		},
		// 标记点基础信息弹窗内按钮点击事件
		clickBasicInformationBtn(i, name) {
			if (name == '区域农机社会化服务中心详情') {
				this.basicInfomationShow = false
				this.znzyPopShow = true
			}
			if (name == '农机应急作业服务队详情') {
				this.basicInfomationShow = false
				this.znzyPopShow = true
			}
			if (name == '区域农业应急救灾中心详情') {
				this.basicInfomationShow = false
				this.znzyPopShow = true
			}
		},
		async getResourceList() {
			let res = await getResourceList({
				areaId: this.areaId,
			})
			if (res?.code == '0') {
				// this.data5_1[0].num = res.data.centerNum
				// this.data5_1[1].num = res.data.emergencyMachineryNum
				// this.data5_1[2].num = res.data.warehouseNum
				// this.data5_2[0].num = res.data.serveNum
				// this.data5_2[1].num = res.data.emergencyCenterNum
				// this.data5_2[2].num = res.data.machineryNum
				this.data5_1[0].num = res.data.serveNum
				this.data5_1[1].num = res.data.centerNum
				this.data5_1[2].num = res.data.emergencyCenterNum
				this.data5_2[0].num = res.data.machineryNum
				this.data5_2[1].num = res.data.emergencyMachineryNum
				this.data5_2[2].num = res.data.warehouseNum
			}
		},
		async aqjgGetTokenFn() {
			const res = await aqjgGetToken()
			if (res?.data) {
				localStorage.setItem('aqjgToken', res.data.access_token)
			}
		},
		async getNjCount() {
			// let res = await getNjCount()
			// this.data3[0].num = res.data[0].agmachCount
			// let res = await agmachCount({
			//   areaId: this.areaId == 100000 ? "" : this.areaId,
			// })
			let res = await getNjCount3({
				areaId: this.areaId == 100000 ? '' : this.areaId,
			})
			this.data3[0].num = res.data[0].count
			this.tjxxList[0].value = res.data[0].count
		},
		async getNjTodayCount() {
			let params = {}
			if (this.areaId != 100000) {
				params['areaId'] = this.areaId
			}
			let res = await getNjTodayCount(params)
			this.data3[1].num = res.data[0].count
		},
		async getNjOnlineCount() {
			let params = {
				startDate: dayjs()
					.subtract(7, 'days')
					.format('YYYY-MM-DD'),
				endDate: dayjs()
					.subtract(1, 'days')
					.format('YYYY-MM-DD'),
				areaId: this.areaId == 100000 ? '' : this.areaId,
				level: this.areaLevel == 0 ? -1 : this.areaLevel,
			}
			let res = await getNjOnlineCount(params)
			this.data3[2].num = res.data[0].count
		},
		async getNjCurrent7Count() {
			let params = {
				startDate: dayjs()
					.subtract(7, 'days')
					.format('YYYY-MM-DD'),
				endDate: dayjs()
					.subtract(1, 'days')
					.format('YYYY-MM-DD'),
				areaId: this.areaId == 100000 ? '' : this.areaId,
				level: this.areaLevel == 0 ? '-1' : this.areaLevel,
			}
			this.gdqsChartData = [['product', '在线量']]
			let res = await getNjCurrent7Count(params)
			if (res.data?.length > 0) {
				this.gdqsChartData = [['product', '在线量']].concat(
					res.data.map((item) => {
						return [item.date.substring(5, 19), item.count]
					}),
				)
			}
		},
		async getVehiclesCount() {
			// this.sewageDataList1 = []
			// let res = await getVehiclesCount()
			// let count = res.data.slice(0, 40)
			// this.sewageDataList1 = count.map((item, index) => {
			//   return [index + 1, item.proEntName, item.totalCount, item.rate]
			// })
			this.sewageDataList11 = []
			let res = await cqCount({
				areaId: this.areaId == 100000 ? '' : this.areaId,
				dataSources: 1,
			})
			let count = res.data.slice(0, 40)
			this.sewageDataList1 = count.map((item, index) => {
				return [index + 1, item.proEntName, item.totalCount, item.rate]
			})
		},
		async getNjAreaDayType() {
			let amachTypeArr = [{ agmachTypeName: '全部', agmachTypeCode: '' }]
			let amachTypeList = [{ agmachTypeName: '全部', agmachTypeCode: '' }]
			let amachTypeListText = [['', '全部']]
			let params = {
				dataSources: 1,
				areaId: this.areaId == 100000 ? '' : this.areaId,
				// typeLevel: 3,
				// day: dayjs(new Date()).subtract(1, 'days').format("YYYY-MM-DD"),
			}

			let res = await agmachtypeCount(params)
			if (res.data?.length > 0) {
				this.chartData = res.data.map((item) => {
					if (item.agmachTypeName == '插秧机') {
						item.agmachTypeName = '水稻插秧机'
					}
					amachTypeListText.push([item.agmachTypeCode, item.agmachTypeName])
					return [item.agmachTypeName, item.count, item.agmachTypeCode]
				})
				const moveItemToLast = (array, itemName) => {
					// 使用filter将不包含特定项的其他项分离出来
					const otherItems = array.filter((item) => item[0] !== itemName)
					// 找到需要移动到最后的项
					const itemToMove = array.find((item) => item[0] === itemName)
					// 如果找到了该项，将其与其余项连接并返回新数组
					console.log('otherItems.length - 1', otherItems.length - 1)
					console.log('itemToMove', itemToMove)
					if (itemToMove) {
						otherItems.push(itemToMove)
						return otherItems
					}
					// 如果没有找到，返回原数组
					return array
				}
				let index = moveItemToLast(this.chartData, '其他')
				amachTypeArr = amachTypeArr.concat(res.data)
				amachTypeList = amachTypeList.concat(res.data)
				const moveItemToArr = (array, itemName) => {
					const otherItems = array.filter((item) => item.agmachTypeName !== itemName)
					const itemToMove = array.find((item) => item.agmachTypeName === itemName)
					if (itemToMove) {
						otherItems.push(itemToMove)
						return otherItems
					}
					return array
				}
				amachTypeArr = moveItemToArr(amachTypeArr, '其他')
				amachTypeList = moveItemToArr(amachTypeList, '其他')
				this.chartData = index
				console.log('this.chartData', this.chartData)
				this.amachTypeListText = amachTypeListText
				this.amachTypeList = amachTypeList

				this.agmachTypeCode = res.data[0].agmachTypeCode
				this.agmachTypeName = res.data[0].agmachTypeName
				this.amachTypeArr = amachTypeArr
				this.getNjTrend()
			}
		},
		async getAreaDistribute(data) {
			let params = {
				level: this.areaLevel + 1,
				parentAreaId: this.areaId == 100000 ? '' : this.areaId,
			}
			console.log('areaLevel', this.areaLevel)
			if (this.areaLevel == 3) {
				//区县显示市级
				console.log('lastAreaCode', this.$refs.leafletMap.lastAreaCode)
				let idx = this.$refs.leafletMap.lastAreaCode.indexOf(Number(this.areaId))
				console.log('idx', idx)
				if (idx <= 0) {
					params = {
						level: this.areaLevel,
						parentAreaId: this.areaIdNew,
					}
				} else {
					params = {
						level: this.areaLevel,
						parentAreaId: this.$refs.leafletMap.lastAreaCode[idx - 1],
					}
				}
			}
			let res = await getAreaDistribute(params)
			// this.box1BottomData = [].concat(
			//   res.data.map(item => {
			//     return [item.areaName, item.agmachCount]
			//   })
			// )
			this.box1BottomData = []
			let arr = res.data.map((item) => [item.areaName, item.agmachCount])
			this.box1BottomData.push(...arr)
			this.forBarChart()
		},
		forBarChart() {
			const getBarChartDataTrue = () => {
				this.barChartDataTrue = this.box1BottomData.slice(this.time, this.time + 10)
				this.time += 1
				if (this.time + 10 > this.box1BottomData.length) {
					this.time = 0
				}
			}
			this.timer1 = setInterval(getBarChartDataTrue, 2000)
		},
		// 近30日的趋势
		async getNjTrend() {
			let params = {
				startDate: dayjs()
					.subtract(7, 'days')
					.format('YYYY-MM-DD'),
				endDate: dayjs()
					.subtract(1, 'days')
					.format('YYYY-MM-DD'),
				areaId: this.areaId == 100000 ? '' : this.areaId,
				level: this.areaLevel == 0 ? '-1' : this.areaLevel,
				agmachTypeCode: this.agmachTypeCode,
			}
			this.gdqsChartData1 = [['product', '在线量']]
			let res = await getNjCurrent7Count(params)
			if (res.data?.length > 0) {
				this.gdqsChartData1 = [['product', '在线量']].concat(
					res.data.map((item) => {
						return [item.date.substring(5, 19), item.count]
					}),
				)
			}
		},
		async getNjLocation() {
			let res = await getNjLocation({ page_no: 1, page_size: 400000 })
			console.log(res)
			let data = res.data[0].rows.map((item) => {
				return {
					latlng: [item.lat, item.lon],
					icon: {
						iconUrl: require('@/assets/mskx/icon_nj_b.png'),
						iconSize: [48, 62],
						iconAnchor: [16, 42],
					},
					props: {
						id: 'njfbId',
						type: 'njfbType',
						info: { ...item },
					},
				}
			})
			this.$refs.leafletMap.drawPoiMarker(data, 'njfbType', true)
		},
		jgzz(index, sourceType) {
			this.zyfbShow = false
			this.removeAllPoi()
			this.jgzzActive = indexedDB
			this.toolTipShow = false
			if (!this.sourceType) {
				this.sourceType = true
				if (this.resourceType == index) {
					this.resourceType = -1
					this.sourceType = false
				} else {
					this.resourceType = index
				}
				if (this.resourceType == 1) {
					this.getEmergencyMap(1)
				}
				if (this.resourceType == 2) {
					this.getEmergencyMap(2)
				}
				if (this.resourceType == 3) {
					this.getEmergencyMap2()
				}
				if (this.resourceType == 0) {
					this.sourceType = false
					if (this.areaId == '100000') {
						this.$refs.leafletMap.drawBigDataClusterPoint(
							this.clusterData,
							require('@/assets/mskx/icon_nj_b.png'),
							[48, 62],
							{
								largeIconUrl: './imgs/cluster/nj1.png',
								mediumIconUrl: './imgs/cluster/nj2.png',
								smallIconUrl: './imgs/cluster/nj3.png',
							},
							'njfbType2',
							true,
						)
					} else {
						this.getNjAreaDayTypes2()
					}
				}
			}
		},
		getInitData() {
			this.getData()
		},
		gridClick(properties) {
			console.log('properties', properties)
			// mapShow:[3]:农机分布；[4]在线农机
			console.log('this.mapShow', this.mapShow)
			this.$store.commit('invokerightShow2', properties)
			this.currentMapLevel = properties.level
			if (properties.level == 'province') {
				this.areaLevel = 1
				this.lastAreaLevel.push(this.areaLevel)
			} else if (properties.level == 'city') {
				this.areaLevel = 2
				this.lastAreaLevel.push(this.areaLevel)
			} else if (properties.level == 'district' && this.areaLevel != 3) {
				this.areaLevel = 3
				this.lastAreaLevel.push(this.areaLevel)
			}
			this.ssboxShow = false
			this.sspopShow = false
			this.sspopList = false
			this.ssnr = ''
			this.ssList = []
			this.areaId = properties.adcode
			this.activeAmachType = ''
			this.resourceTypes = '-1'

			// this.removeAllPoi()
			this.getInitData()
			if (this.activaIdx == 1) {
				this.getAllAlarmWithGPS()
			}
			if (this.mapShow == 3) {
				// this.mapShow = -1
				this.removeAllPoi()
			}
			if (this.activaIdx == 2) {
				this.resourceTypes = -1
				this.removeAllPoi()
			}
			if (this.activaIdx == 4) {
				this.removeAllPoi()
				this.identification(this.currentDrawClass)
			}
			if (this.resourceTypes !== -1) {
				this.removeAllPoi()
			}
		},
		operate(areaId, areaId2) {
			this.ssboxShow = true
			this.sspopShow = false
			this.sspopList = false
			this.ssnr = ''
			this.ssList = []
			this.areaId = areaId
			this.$store.commit('invokerightShow3', areaId)
			console.log('this.areaId--==', this.areaId)
			console.log('lastAreaCode', this.$refs.leafletMap.lastAreaCode)
			if (this.areaId) {
				if (this.areaId == 100000) {
					this.areaLevel = 0
				} else {
					this.areaLevel = this.areaLevel_init + this.$refs.leafletMap.lastAreaCode.indexOf(areaId)
				}
			} else {
				this.areaLevel = 0
			}
			this.removeAllPoi()
			this.getInitData()
			if (this.activaIdx == 1) {
				this.getAllAlarmWithGPS()
			}
			if (this.activaIdx == 2) {
				this.resourceTypes = -1
				this.removeAllPoi()
			}
			if (this.activaIdx == 4) {
				this.removeAllPoi()
				// this.identification(this.currentDrawClass)
				if (this.mapShow == 3) {
					this.agmachDistribution('njfbType')
				} else if (this.mapShow == 4) {
					this.agmachDistribution('zxnjType')
				}
				// this.resourceTypes
			}
			if (this.resourceTypes !== -1) {
				this.removeAllPoi()
			}
		},

		async getNjAreaDayTypes2() {
			if (this.loadFlag) {
				this.loadFlag = false
				let res = await getNjAreaDayTypes2({
					areaId: this.areaId,
				})
				this.sourceType = false
				this.loadFlag = true
				let clusterData = [].concat(
					res.data.map((item) => {
						return [item.agmachId || '', '-1', '-1', item.lon, item.lat]
					}),
				)
				this.$refs.leafletMap.drawBigDataClusterPoint(
					clusterData,
					require('@/assets/mskx/icon_nj_b.png'),
					[48, 62],
					{
						largeIconUrl: './imgs/cluster/nj1.png',
						mediumIconUrl: './imgs/cluster/nj2.png',
						smallIconUrl: './imgs/cluster/nj3.png',
					},
					'njfbType',
				)
			}
		},
		eventInfoBtnClick5(i) {
			console.log(i)
		},
		poiClick(layerId, it) {
			console.log(layerId, it)
			if (it.trajectoryMark) {
				this.trajectoryMark = it.trajectoryMark
			}
			this.ssboxShow = false
			this.sspopShow = false
			this.sspopList = false
			this.ssnr = ''
			this.ssList = []
			// this.agmachId = it[0]
			this.agmachId = it.props.info.agmachId
			this.dwType = layerId
			if (layerId == 'jcdwType') {
				this.yjxqList = [
					0,
					it.props.info.time,
					it.props.info.type,
					it.props.info.area,
					it.props.info.level,
					it.props.info.detail,
					it.props.info.title,
				]
				this.isyjxqShow = true
				// this.yjPopShow = true
			} else if (layerId == 'njfbType') {
				// this.locHistory2(it[0])
				this.locHistory2(it.props.info.agmachId)
			} else if (layerId == 'kqzyType1') {
				// this.njInfoShow = true
				// this.currentAgmachInfo = it
				// this.getNjAreaDayTypes(it.agmachId)
				this.locHistory2(it.props.info.agmachId)
			} else if (layerId == 'kqzyType2') {
				// this.njInfoShow = true
				// this.currentAgmachInfo = it
				// this.getNjAreaDayTypes(it.agmachId)
				this.locHistory2(it.props.info.agmachId)
			} else if (layerId == 'zxnjType') {
				// this.njInfoShow = true
				// this.currentAgmachInfo = it
				// this.getNjAreaDayTypes(it.agmachId)
				// this.locHistory2(it[0])
				this.locHistory2(it.props.info.agmachId)
			} else if (layerId == 'njfbType2') {
				// this.njInfoShow = true
				// this.currentAgmachInfo = it
				console.log('it[1]', it[1])
				this.trajectoryMark = it[2]
				console.log('this.trajectoryMark', this.trajectoryMark)
				if (it[1] == 1) {
					// this.getNjAreaDayTypes(it.agmachId)
					this.locHistory2(it[0])
				} else {
					this.$message('这台农机无基本信息')
				}
			} else if (layerId == 'yjjzType') {
				this.basicInfomationShow = true
				this.basicInfomationTitle = '区域农机社会化服务中心详情'
				this.basicInfomationId = it.props.info.id
				console.log('区域农机社会化服务中心详情', it.props.info)
				this.basicInfomationList = [
					{
						label: '中心名称：',
						value: it.props.info.name || '-',
					},
					{
						label: '联系人：',
						value: it.props.info.contact || '-',
					},
					{
						label: '联系电话：',
						value: it.props.info.phone || '-',
					},
					{
						label: '所属区域：',
						value: it.props.info.area || '-',
					},
					{
						label: '地址：',
						value: it.props.info.address || '-',
					},
				]
				this.basicInfomationBtns = ['人员机具明细']
				this.basicInfomationBtnShow = true
			} else if (layerId == 'fwdType') {
				this.basicInfomationShow = true
				this.basicInfomationTitle = '农机应急作业服务队详情'
				this.basicInfomationId = it.props.info.id
				this.basicInfomationList = [
					{
						label: '中心名称：',
						value: it.props.info.name || '-',
					},
					{
						label: '联系人：',
						value: it.props.info.contact || '-',
					},
					{
						label: '联系电话：',
						value: it.props.info.phone || '-',
					},
					{
						label: '队伍人数：',
						value: it.props.info.num || '-',
					},
					{
						label: '所属区域：',
						value: it.props.info.area || '-',
					},
					{
						label: '地址：',
						value: it.props.info.address || '-',
					},
				]
				this.basicInfomationBtns = ['人员机具明细']
				this.basicInfomationBtnShow = true
			} else if (layerId == 'hgzxType') {
				this.basicInfomationShow = true
				this.basicInfomationTitle = '区域农业应急救灾中心详情'
				this.basicInfomationId = it.props.info.id
				this.basicInfomationList = [
					{
						label: '站点名称：',
						value: it.props.info.name || '-',
					},
					{
						label: '烘干能力：',
						value: it.props.info.talent || '-',
					},
					{
						label: '联系人：',
						value: it.props.info.contact || '-',
					},
					{
						label: '联系电话：',
						value: it.props.info.phone || '-',
					},
					{
						label: '所属区域：',
						value: it.props.info.area || '-',
					},
					{
						label: '地址：',
						value: it.props.info.area || '-',
					},
				]
				this.basicInfomationBtns = ['人员机具明细']
				this.basicInfomationBtnShow = true
			} else if (layerId == 'yjwzType') {
				this.basicInfomationShow = true
				this.basicInfomationTitle = '机具保有量详情'
				this.basicInfomationList = [
					{
						label: '中心名称：',
						value: '光明区域农机社会化服务中心',
					},
					{
						label: '联系人：',
						value: '王建国',
					},
					{
						label: '联系电话：',
						value: '16788865445',
					},
					{
						label: '所属区域：',
						value: 'xxxxxx',
					},
					{
						label: '地址：',
						value: 'xxxxxx',
					},
				]
				this.basicInfomationBtns = ['人员机具明细']
				this.basicInfomationBtnShow = true
			}
		},
		async locHistory2(data) {
			console.log('data', data)
			let res = await locHistory2({
				agmachId: data,
				// page_no: 1,
				// page_size: 10
				pageNo: 1,
				pageSize: 10,
			})
			console.log('locHistory2-res', res)
			this.njInfoShow = true
			// this.currentAgmachInfo = res.data[0].rows[0]
			if (res.data) {
				this.currentAgmachInfo = res.data[0]
			} else {
				this.currentAgmachInfo = {}
			}
		},

		removeAllPoi() {
			this.$refs.leafletMap.removeLayer('jcdwType')
			this.$refs.leafletMap.removeLayer('njfbType')
			this.$refs.leafletMap.removeLayer('zxnjType')
			this.$refs.leafletMap.removeLayer('hgzxType')
			this.$refs.leafletMap.removeLayer('yjwzType')
			this.$refs.leafletMap.removeLayer('yjjzType')
			this.$refs.leafletMap.removeLayer('fwdType')
			this.$refs.leafletMap.removeLayer('numberMarker')
			this.$refs.leafletMap.clearCluster()
			if (this.$refs.leafletMap.polygon) {
				this.$refs.leafletMap.polygon.remove()
			}
		},
		mark(i) {
			console.log(i)
			this.jgzzShow = false
			if (this.activaIdx == i) {
				this.activaIdx = -1
			} else {
				this.activaIdx = i
			}
			this.toolTipShow = false
			this.removeAllPoi()
			// for (let key in this.resourceType) {
			//   this.resourceType[key].flag = true
			// }

			if (this.activaIdx == 0) {
				this.getAllAlarmWithGPS()
			} else if (this.activaIdx == 1) {
				this.jgzzShow = true
				// for (let key in this.resourceType) {
				//   if (this.resourceType[key].flag) {
				//     this.jgzz(key, false)
				//   }
				// }
				// this.jgzz(1, false)
				// this.jgzz(2, false)
				// this.jgzz(3, false)
				if (this.resourceType == 0) {
					this.sourceType = false
					if (this.areaId == '100000') {
						this.$refs.leafletMap.drawBigDataClusterPoint(
							this.clusterData,
							require('@/assets/mskx/icon_nj_b.png'),
							[48, 62],
							{
								largeIconUrl: './imgs/cluster/nj1.png',
								mediumIconUrl: './imgs/cluster/nj2.png',
								smallIconUrl: './imgs/cluster/nj3.png',
							},
							'njfbType2',
							true,
						)
					} else {
						this.getNjAreaDayTypes2()
					}
				}
				console.log('this.resourceType', this.resourceType)
			}
		},
		tooltipChange(e) {
			if (e.type == 'weatherAlarmType') {
				this.weatherAlarmType = e.value
			} else if (e.type == 'weatherAlarmLevel') {
				this.weatherAlarmLevel = e.value
			}
			this.removeAllPoi()
			this.getAllAlarmWithGPS()
		},
		async getAllAlarmWithGPS() {
			let res = await getAllAlarmWithGPS({
				type: this.weatherAlarmType,
				level: this.weatherAlarmLevel,
				regionCode: this.areaId == 100000 ? '' : this.areaId,
			})
			console.log(res)
			if (!res.data.length) {
				this.$message({
					message: '暂无天气预警信息',
					offset: 100,
				})
			}
			let data = res.data.map((item) => {
				return {
					latlng: [item.latitude, item.longitude],
					icon: {
						iconUrl: require('@/assets/mskx/icon_yj_y.png'),
						iconSize: [100, 100],
						iconAnchor: [50, 100],
					},
					props: {
						id: 'jcdwId',
						type: 'jcdwType',
						info: { ...item },
					},
				}
			})
			this.$refs.leafletMap.drawPoiMarkerCustomCluster(data, 'jcdwType', true, false, false, {
				largeIconUrl: './imgs/cluster/yj1.png',
				mediumIconUrl: './imgs/cluster/yj2.png',
				smallIconUrl: './imgs/cluster/yj3.png',
			})
		},
		mapLoad() {
			this.loadCarLayer('area')
			this.$refs.map.autoShowInfoWindow(this.areaPointList)
		},

		// 加载重点项目
		loadCarLayer(layerId) {
			if (layerId == 'area') {
				this.areaPointList = testData.markersArea.map((item) => ({
					...item,
					popup: this.$refs.area.$el,
					onclick: () => {
						this.popAreaInfo = {
							...item.properties,
						}
						this.showArea = true
					},
				}))
				this.$refs.map.addMarkers(
					this.areaPointList,
					require('@/assets/map/point/point2.png'),
					// [34, 84],
					[1, 1],
					layerId,
				)
			}
		},
		closeCountryPart() {
			this.isShowCountryPart = false
			if (this.activaIdx === 3) this.activaIdx = -1
		},
		checkTree(e) {
			console.log(e)
			// 地图打点
			// let data = csbjPoint.features.map(e => {
			//   return {
			//     position: e.geometry.coordinates.concat(100),
			//     properties: e.properties,
			//     img: e.img
			//   }
			// })
			// this.$refs.CesiumMapRef.loadPoints1(data, 'csbjPoint')
		},
		emitMenu(val) {
			console.log(val)
			switch (val) {
				case 1:
					this.stjstkShow = true
					break
				case 2:
					this.jjfztkShow = true
					break
				case 3:
					this.wgzltkShow = true
					break
				case 4:
					this.hswhtkShow = true
					break

				default:
					break
			}
		},
	},
}
</script>

<style lang="less" scoped>
@font-face {
	font-family: QuartzRegular;
	src: url(~@/assets/leader/font/QuartzRegular.ttf);
}

@font-face {
	/*给字体命名*/
	font-family: 'YouSheBiaoTiHei';
	/*引入字体文件*/
	src: url(~@/assets/font/youshebiaotihei.ttf);
	font-weight: normal;
	font-style: normal;
}

@font-face {
	/*给字体命名*/
	font-family: 'AlibabaPuHuiTi';
	/*引入字体文件*/
	src: url(~@/assets/font/AlibabaPuHuiTi-2-85-Bold.ttf);
	font-weight: normal;
	font-style: normal;
}

@keyframes rotateS {
	0% {
		transform: rotate(0);
	}

	100% {
		transform: rotate(360deg);
	}
}

@keyframes rotateN {
	0% {
		transform: rotate(360deg);
	}

	100% {
		transform: rotate(0);
	}
}

@keyframes rotateY {
	0% {
		transform: rotate(0);
	}

	100% {
		transform: rotateY(360deg);
	}
}

.leader_sjts {
	width: 1920px;
	height: 1080px;
	position: absolute;
	top: 0;
	display: flex;
	justify-content: space-between;

	.leader_city_box {
		width: 450px;

		.leader_zt_contain {
			height: 100%;
		}
	}

	.box {
		.cont {
			width: 100%;
			height: 100%;
			position: relative;
		}

		.wrapper1 {
			width: 100%;
			height: 100%;

			.introduce_video {
				width: 459px;
				height: 192px;

				::v-deep .video-wrapper {
					padding-bottom: 41.25% !important;
				}
			}

			.introduce {
				margin-top: 11px;
				width: 100%;
				height: 120px;
				padding: 18px 26px;
				background: url(~@/assets/hszl/bg1.png) no-repeat;
				background-size: 100% 100%;
				font-size: 16px;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #4fddff;
				line-height: 22px;
				letter-spacing: 2px;
				text-align: left;
			}
		}

		.wrapper4 {
			width: 100%;
			height: 100%;
			padding: 30px 0 30px 8px;

			ul {
				width: 100%;
				height: 100%;
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;
				align-content: space-between;

				li {
					display: flex;
					height: 49px;
					gap: 10px;

					.icon {
						width: 46px;
						height: 49px;
					}

					.info {
						margin-top: -6px;

						.label {
							font-size: 14px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #ffffff;
							line-height: 20px;
							text-align: left;
							padding-left: 5px;
						}

						.line {
							width: 87px;
							height: 6px;
							background: url('~@/assets/hszl/line1.png') no-repeat;
						}

						.count {
							text-align: left;
							margin-top: 4px;
							width: 87px;
							height: 22px;
							line-height: 22px;
							background: linear-gradient(96deg, #1d4975 0%, rgba(19, 56, 87, 0.24) 100%);
							font-size: 14px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #a8b1b9;
							padding-left: 5px;

							span {
								font-size: 18px;
								font-family: PingFangSC, PingFang SC;
								font-weight: normal;
								color: #ffffff;
								line-height: 22px;
								margin-right: 3px;
							}
						}
					}
				}
			}
		}

		.wrapper5 {
			width: 100%;
			height: 100%;
			padding-top: 31px;

			.info {
				width: 100%;
				height: 71px;
				padding-left: 8px;
				display: flex;
				justify-content: space-between;

				.total {
					width: 130px;
					height: 100%;
					background: url('~@/assets/hszl/bg4.png') no-repeat;
					display: grid;
					place-items: center;

					.cont {
						padding-top: 13px;

						.label {
							font-size: 14px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #ffffff;
							line-height: 20px;
						}

						.value {
							font-size: 18px;
							font-family: PingFangSC, PingFang SC;
							font-weight: normal;
							color: #ffffff;
							line-height: 22px;
							background: linear-gradient(180deg, #ffffff 0%, #00fffa 67%, #04a1ff 100%);
							background-clip: text;
							-webkit-background-clip: text;
							-webkit-text-fill-color: transparent;

							span {
								font-size: 12px;
							}
						}
					}
				}

				.counts {
					width: 312px;
					height: 100%;
					background: url('~@/assets/hszl/bg5.png') no-repeat;
					display: flex;
					padding: 0 9px;

					.men {
						text-align: left;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						margin-right: 4px;

						.count {
							height: 21px;
							font-size: 16px;
							font-family: PingFangSC, PingFang SC;
							font-weight: normal;
							line-height: 19px;
							background: linear-gradient(180deg, #ffffff 0%, #00ceff 67%, #04a1ff 100%);
							background-clip: text;
							-webkit-background-clip: text;
							-webkit-text-fill-color: transparent;

							span {
								font-size: 14px;
							}
						}

						.chart {
							display: flex;
							gap: 4px;

							span {
								width: 10px;
								height: 25px;
								background-repeat: no-repeat;
							}
						}
					}

					.women {
						text-align: right;
						display: flex;
						flex-direction: column;
						justify-content: space-between;

						.count {
							height: 21px;
							font-size: 16px;
							font-family: PingFangSC, PingFang SC;
							font-weight: normal;
							line-height: 19px;
							background: linear-gradient(180deg, #ffffff 0%, #f076ff 66%, #ff04ff 100%);
							background-clip: text;
							-webkit-background-clip: text;
							-webkit-text-fill-color: transparent;

							span {
								font-size: 14px;
							}
						}

						.chart {
							display: flex;
							gap: 4px;

							span {
								width: 12px;
								height: 25px;
								background-repeat: no-repeat;
							}
						}
					}
				}
			}

			.chart_wrap {
				position: relative;
				width: 100%;
				height: 198px;
				top: 41px;

				.sign {
					position: absolute;
					left: 105px;
					top: 25px;

					.woman {
						position: absolute;
						width: 27px;
						height: 57px;
						left: 0;
						top: 0;
						background: url('~@/assets/hszl/woman.png') no-repeat;
					}

					.man {
						position: absolute;
						width: 23px;
						height: 57px;
						left: 95px;
						top: 47px;
						background: url('~@/assets/hszl/man.png') no-repeat;
					}

					.man_count {
						position: absolute;
						left: 10px;
						top: 76px;
						width: 109px;
						height: 31px;
						font-size: 20px;
						font-family: PingFangSC, PingFang SC;
						font-weight: normal;
						color: #ffffff;
						line-height: 24px;
					}

					.woman_count {
						position: absolute;
						left: 10px;
						top: 36px;
						width: 109px;
						height: 31px;
						font-size: 20px;
						font-family: PingFangSC, PingFang SC;
						font-weight: normal;
						color: #ffffff;
						line-height: 24px;
					}
				}
			}
		}
	}

	.box1 {
		.cout {
			width: 100%;
			height: 100%;
		}
		.cont1 {
			width: 100%;
			height: 60px;
			display: flex;
			justify-content: space-around;

			.contList {
				.contNum {
					width: 124px;
					height: 24px;
					margin-top: 10px;

					span {
						display: inline-block;
						width: 100%;
						height: 24px;
						font-family: PingFangSC, PingFang SC;
						font-weight: bold;
						font-size: 20px;
						color: #ffffff;
						line-height: 24px;
						text-shadow: 0px 0px 4px rgba(0, 0, 0, 0);
						text-align: center;
						font-style: italic;
						background: linear-gradient(180deg, #ffffff 0%, #00fffa 67%, #04a1ff 100%);
						-webkit-background-clip: text;
						-webkit-text-fill-color: transparent;
					}
				}

				&:nth-of-type(2) {
					.contNum {
						width: 124px;
						height: 24px;

						span {
							display: inline-block;
							width: 100%;
							height: 24px;
							font-family: PingFangSC, PingFang SC;
							font-weight: bold;
							font-size: 20px;
							color: #ffffff;
							line-height: 24px;
							text-shadow: 0px 0px 4px rgba(0, 0, 0, 0);
							text-align: center;
							font-style: italic;
							background: linear-gradient(180deg, #ffffff 0%, #ffd500 67%, #ffb904 100%);
							-webkit-background-clip: text;
							-webkit-text-fill-color: transparent;
						}
					}
				}

				&:nth-of-type(3) {
					.contNum {
						width: 124px;
						height: 24px;

						span {
							display: inline-block;
							width: 100%;
							height: 24px;
							font-family: PingFangSC, PingFang SC;
							font-weight: bold;
							font-size: 20px;
							color: #ffffff;
							line-height: 24px;
							text-shadow: 0px 0px 4px rgba(0, 0, 0, 0);
							text-align: center;
							font-style: italic;
							background: linear-gradient(180deg, #ffffff 0%, #86ff00 67%, #1cff04 100%);
							-webkit-background-clip: text;
							-webkit-text-fill-color: transparent;
						}
					}
				}

				img {
					display: block;
					width: 124px;
					height: 4px;
					margin-top: 4px;
				}

				.contName {
					width: 124px;
					height: 20px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 14px;
					color: #ffffff;
					line-height: 20px;
					text-align: center;
					font-style: normal;
				}
			}
		}

		.cont2 {
			width: 100%;
			height: 170px;
		}
	}

	.box2 {
		margin-top: 22px;
		.contLeft {
			position: absolute;
			left: 20px;
			top: 10px;
			text-align: left;
			z-index: 99;
		}

		.contRight {
			position: absolute;
			right: 20px;
			top: 10px;
			text-align: right;
			z-index: 99;
		}

		.contList {
			margin-top: 18px;
			cursor: pointer;
			.contNum {
				font-family: PingFangSC, PingFang SC;
				font-weight: bold;
				font-size: 24px;
				color: #ffffff;
				line-height: 28px;
				letter-spacing: 2px;
				font-style: normal;

				span {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 14px;
					color: #cbe5ff;
					line-height: 20px;
					letter-spacing: 1px;
					text-align: right;
					font-style: normal;
					margin-left: 4px;
				}
			}

			.contName {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #cbe5ff;
				line-height: 20px;
				letter-spacing: 1px;
				font-style: normal;
			}

			&:nth-of-type(1) {
				margin-top: 7px;
			}
		}

		.contImg {
			width: 405px;
			height: 197px;
			position: absolute;
			top: 52px;
			left: 20px;
		}

		.contImg2 {
			width: 59px;
			height: 61px;
			position: absolute;
			top: 93px;
			left: 194px;
		}
	}

	.box3 {
		.cont {
			padding: 12px 22px 0;

			video {
				width: 100%;
				height: 100%;
				object-fit: cover;

				&:not(:root):fullscreen {
					object-fit: contain;
				}
			}
		}
	}

	.box4 {
		.cont {
			padding: 15px 0;
			display: flex;
			flex-wrap: wrap;
			justify-content: space-evenly;

			li {
				position: relative;
				width: 200px;
				height: 66px;
				background: url(~@/assets/csts/bg2.png) no-repeat;
				display: flex;
				flex-direction: column;
				justify-content: center;
				text-align: left;
				padding-left: 92px;

				.icon {
					position: absolute;
					width: 26px;
					height: 26px;
					top: 10px;
					left: 31px;
					animation: move infinite 3s ease-in-out;

					@keyframes move {
						0% {
							transform: translateY(0px) rotateY(0);
						}

						50% {
							transform: translateY(-10px) rotateY(180deg);
						}

						100% {
							transform: translateY(0px) rotateY(360deg);
						}
					}
				}

				&:nth-child(2) {
					span {
						animation-delay: 0.3s;
					}
				}

				&:nth-child(3) {
					span {
						animation-delay: 0.6s;
					}
				}

				&:nth-child(4) {
					span {
						animation-delay: 0.9s;
					}
				}

				&:nth-child(5) {
					span {
						animation-delay: 1.2s;
					}
				}

				&:nth-child(6) {
					span {
						animation-delay: 1.5s;
					}
				}

				.tit {
					font-size: 14px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #ffffff;
					line-height: 20px;
				}

				.num {
					font-size: 18px;
					font-family: PingFangSC, PingFang SC;
					font-weight: normal;
					color: #ffffff;
					line-height: 22px;
				}
			}
		}
	}

	.box5 {
		.cont {
			display: flex;
			flex-direction: column;

			.bar_chart {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-evenly;

				li {
					position: relative;
					display: flex;
					align-items: center;
					padding: 0 30px 0 23px;

					.icon {
						width: 20px;
						height: 22px;
						margin-right: 7px;
					}

					.icon2 {
						width: 10px;
						height: 12px;
						position: absolute;
						left: 28px;
						top: 5px;
						animation: move infinite 3s ease-in-out;

						@keyframes move {
							0% {
								transform: translateY(0px) rotateY(0);
							}

							50% {
								transform: translateY(0px) rotateY(180deg);
							}

							100% {
								transform: translateY(0px) rotateY(360deg);
							}
						}
					}

					.lab {
						font-size: 14px;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						color: #ffffff;
						margin-right: 8px;
					}

					.progress {
						display: block;
						flex: 1;
						height: 100%;
						background: rgba(0, 201, 255, 0.14);

						.cur {
							width: 100%;
							border: 1px solid;
							height: 100%;
							overflow: hidden;
							transition: width 0.5s;
						}
					}

					.num {
						font-size: 16px;
						font-family: PingFangSC, PingFang SC;
						font-weight: normal;
						color: #ffffff;
						margin-left: 9px;
					}

					.percent {
						font-size: 16px;
						font-family: PingFangSC, PingFang SC;
						font-weight: normal;
						color: #ffffff;
						margin-left: 14px;
					}
				}
			}

			.pie_chart {
				height: 123px;
				display: flex;
				justify-content: space-evenly;

				.warp {
					width: 107px;
					height: 107px;
					padding-top: 0;

					/deep/.ring {
						width: 100% !important;
						height: 100% !important;
					}

					/deep/.label {
						margin-top: -65px;

						.name {
							font-size: 13px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #ffffff;
						}
					}
				}
			}
		}
	}

	.box6 {
		margin-top: 12px;

		.cont {
			.select_month {
				position: absolute;
				right: 0;
				top: -20px;

				::v-deep button {
					width: 56px;
					height: 24px;
					right: -46px;
					top: -28px;
					border-radius: 12px;
					background: url(~@/assets/shzl/select_bg.png) no-repeat center / 100% 100%;
					border: none;
					padding: 0;
					// background: linear-gradient(180deg, #0e63ac 0%, #253c69 100%);
					// border-radius: 2px;
					// border: 2px solid;
					// border-image: linear-gradient(180deg, rgba(63, 148, 224, 0.2), rgba(218, 250, 255, 0.3))
					//   2 2;
				}
			}

			// padding: 14px 28px 0;
			// .swiper-slide {
			//   height: 100%;
			// }
			// .swiper-pagination {
			//   text-align: right;
			//   bottom: 3px;
			// }
			// /deep/.swiper-pagination-bullet-active {
			//   background-color: rgba(255, 255, 255, 0.8);
			// }
			// .content {
			//   width: 100%;
			//   height: 100%;
			//   position: relative;
			//   img {
			//     position: absolute;
			//     width: 100%;
			//     height: 100%;
			//     top: 0;
			//     left: 0;
			//   }
			//   .cont {
			//     width: 100%;
			//     height: 30px;
			//     padding: 5px 10px;
			//     position: absolute;
			//     bottom: 0;
			//     font-size: 14px;
			//     font-family: PingFangSC, PingFang SC;
			//     font-weight: 500;
			//     color: #ffffff;
			//     line-height: 22px;
			//     text-align: left;
			//     background-color: rgba(0, 0, 0, 0.35);
			//   }
			// }
		}
	}

	.box7 {
		.cont {
			display: grid;
			place-items: center;
			position: relative;

			.fy_out {
				position: absolute;
				top: 3%;
				left: 27%;
				z-index: 99;
				transform: translate(-50%, -50%);
				animation: rotateS infinite 12s linear;
			}

			.fy_in {
				position: absolute;
				top: 44%;
				left: 50%;
				transform: translate(-50%, -50%);
			}

			.wrap {
				position: relative;
				width: 393px;
				height: 205px;
				background: url(~@/assets/csts/bg3.png) no-repeat;

				li {
					position: absolute;
					text-align: left;

					.num {
						font-size: 20px;
						font-family: PingFangSC, PingFang SC;
						font-weight: normal;
						color: #ffffff;
						line-height: 24px;
					}

					.tit {
						font-size: 14px;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						color: #ffffff;
						line-height: 17px;
					}

					&:nth-child(1) {
						top: 28px;
						left: 24px;
					}

					&:nth-child(2) {
						top: 28px;
						left: 295px;
					}

					&:nth-child(3) {
						bottom: 32px;
						left: 24px;
					}

					&:nth-child(4) {
						bottom: 32px;
						left: 295px;
					}
				}
			}
		}
	}

	.box8 {
		margin-top: 22px;
		position: relative;
		.njfbDw {
			position: absolute;
			left: 50px;
			top: 20px;
			color: #ffffff;
			font-size: 14px;
			font-family: PingFangSC, PingFang SC;
			font-weight: 300;
		}
		.cout {
			width: 100%;
			height: 100%;
		}
	}

	.box9 {
		.cont {
			padding: 16px 23px 0;

			.title {
				width: 100%;
				height: 44px;
				line-height: 44px;
				display: flex;
				justify-content: space-between;
				padding: 0 24px;

				span {
					font-size: 14px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #ffffff;
				}
			}

			.detail {
				position: relative;
				width: 404px;
				height: 156px;
				background: url(~@/assets/csts/bg4.png) no-repeat center;
				display: flex;
				justify-content: space-between;

				.center_out {
					position: absolute;
					left: 29%;
					top: -2%;
					transform: translate(-50%, -50%);
					z-index: 99;
					animation: rotateS infinite 12s linear;
				}

				.center_in {
					position: absolute;
					left: 29%;
					top: 2px;
					transform: translate(-50%, -50%);
					z-index: 99;
					animation: rotateN infinite 12s linear;
				}

				.fs {
					display: flex;
					flex-direction: column;
					justify-content: space-evenly;
					gap: 9px;

					li {
						display: flex;
						justify-content: space-between;
						align-items: center;

						.icon {
							width: 13px;
							height: 16px;
						}

						.lab {
							font-size: 14px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #ffffff;
							line-height: 20px;
							padding-left: 6px;
							padding-right: 22px;
						}

						.num {
							font-size: 10px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #ffffff;

							span {
								font-size: 18px;
								font-family: PingFangSC, PingFang SC;
								font-weight: normal;
								color: #ffffff;
								line-height: 22px;
							}
						}
					}
				}

				.fq {
					display: flex;
					flex-direction: column;
					justify-content: space-evenly;
					gap: 9px;

					li {
						display: flex;
						justify-content: space-between;
						align-items: center;

						.icon {
							width: 14px;
							height: 14px;
						}

						.lab {
							font-size: 14px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #ffffff;
							line-height: 20px;
							padding-left: 22px;
							padding-right: 6px;
						}

						.num {
							font-size: 10px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #ffffff;

							span {
								font-size: 18px;
								font-family: PingFangSC, PingFang SC;
								font-weight: normal;
								color: #ffffff;
								line-height: 22px;
							}
						}
					}
				}

				.zdqy {
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);

					.num {
						font-size: 20px;
						font-family: PingFangSC, PingFang SC;
						font-weight: normal;
						color: #ffffff;
						line-height: 24px;
					}

					.lab {
						font-size: 18px;
						font-family: PingFangSC, PingFang SC;
						color: #ffffff;
						line-height: 21px;
						letter-spacing: 1px;
					}
				}
			}
		}
	}

	.box12 {
		position: relative;

		.gajq_lz {
			width: 100%;
			height: 100%;
			position: absolute;
			top: 0;

			img {
				width: 100%;
				height: 100%;
			}
		}
	}

	.left_bg {
		width: 460px;
		height: 1033px;
		background: url(~@/assets/leader/img/component/left_bg.png) no-repeat center / 100% 100%;
		position: absolute;
		left: 50px;
		top: 25px;
		z-index: 102;
	}

	.right_bg {
		width: 520px;
		height: 1033px;
		background: url(~@/assets/leader/img/component/right_bg.png) no-repeat center / 100% 100%;
		position: absolute;
		right: 50px;
		top: 25px;
		z-index: 102;
	}

	.left {
		width: 450px;
		display: flex;
		justify-content: space-between;
		margin-top: 87px;
		margin-left: 32px;
		position: relative;
		left: 0;
		opacity: 1;
		z-index: 1003;
		-webkit-transition: all 0.5s ease-in;
		-moz-transition: all 0.5s ease-in;
		transition: all 0.5s ease-in;
	}
	.mkdxLeft {
		left: -450px;
		opacity: 0;
	}

	.right {
		width: 450px;
		display: flex;
		justify-content: space-between;
		margin-top: 87px;
		margin-right: 32px;
		position: relative;
		right: 0;
		z-index: 1003;
		-webkit-transition: all 0.5s ease-in;
		-moz-transition: all 0.5s ease-in;
		transition: all 0.5s ease-in;

		.njlx_box {
			&::-webkit-scrollbar {
				width: 6px;
				background: transparent;
			}

			&::-webkit-scrollbar-thumb {
				/*滚动条里面小方块*/
				border-radius: 2px;
				box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
				background: rgb(45, 124, 228);
				height: 100px;
				width: 20px;
			}
		}

		.njlx_box {
			display: flex;
			flex-wrap: wrap;
			margin: 10px auto 0;
			text-align: left;
			width: 100%;
			height: calc(100% - 30px);
			overflow-y: scroll;
			-ms-overflow-style: none;
			/* IE 10+ */
			scrollbar-width: none;
			/* Firefox */

			.njlx_item {
				display: inline-block;
				margin: 0 3% 7px 3%;
				width: 27%;
				// height: 93px;
				// height:45%;
				// height:30%;
				// background-image: url(~@/assets/bjnj/njlx_bgImg.png);
				// background-size: 100% 100%;
				// background-repeat: no-repeat;
				// margin-bottom: 7px;
				// padding-top: 8px;
				box-sizing: border-box;
				text-align: center;

				.njlx_title {
					margin: 0 auto;
					max-width: fit-content;
					min-width: 100px;
					background-image: url(~@/assets/bjnj/njlx_titleImg.png);
					background-size: 100% 18px;
					background-repeat: no-repeat;
					// height: 18px;
					font-family: PingFangSC, PingFang SC;
					font-size: 16px;
					color: #edfbff;
					line-height: 18px;
					letter-spacing: 1px;
					text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.15);
					font-style: normal;
					text-align: center;
				}

				.njlx_number {
					margin: 8px auto;
					width: fit-content;
					height: 24px;
					font-family: PangMenZhengDao;
					font-size: 24px;
					color: #ffffff;
					line-height: 24px;
					letter-spacing: 1px;
					text-shadow: 0px 0px 4px rgba(73, 140, 255, 0.85);
					text-align: left;
					font-style: normal;
					background: linear-gradient(90deg, #00faff 0%, #00b1ff 100%);
					-webkit-background-clip: text;
					/*将设置的背景颜色限制在文字中*/
					-webkit-text-fill-color: transparent;
					/*给文字设置成透明*/
				}
			}
			.njlx_item2 {
				display: inline-block;
				margin: 0 10px;
				width: 28%;
				// height: 93px;
				height: 30%;
				// background-image: url(~@/assets/bjnj/njlx_bgImg2.png);
				background-size: 100% 100%;
				background-repeat: no-repeat;
				margin-bottom: 7px;
				// padding-top: 8px;
				box-sizing: border-box;
				text-align: center;

				.njlx_title2 {
					margin: 0 auto;
					max-width: fit-content;
					min-width: 100px;
					background-image: url(~@/assets/bjnj/njlx_titleImg2.png);
					background-size: 100% 100%;
					background-repeat: no-repeat;
					height: 18px;
					font-family: PingFangSC, PingFang SC;
					font-size: 16px;
					color: #edfbff;
					line-height: 18px;
					letter-spacing: 1px;
					text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.15);
					font-style: normal;
					text-align: center;
				}

				.njlx_number {
					margin: 8px auto;
					width: fit-content;
					height: 24px;
					font-family: PangMenZhengDao;
					font-size: 24px;
					color: #ffffff;
					line-height: 24px;
					letter-spacing: 1px;
					text-shadow: 0px 0px 4px rgba(255, 164, 73, 0.85);
					text-align: left;
					font-style: normal;
					background: linear-gradient(90deg, #ff9800 0%, #ffcb00 100%);
					-webkit-background-clip: text;
					/*将设置的背景颜色限制在文字中*/
					-webkit-text-fill-color: transparent;
					/*给文字设置成透明*/
				}
			}
		}
	}

	.mkdxRight {
		right: -450px;
		opacity: 0;
	}
	.zwsjImg {
		height: 80%;
		margin: 0 auto;
		margin-top: 36px;
	}
}
.middle {
	width: 1856px;
	height: 290px;
	position: absolute;
	left: 50%;
	bottom: 33px;
	transform: translateX(-50%);
	background: url(~@/assets/bjnj/dbmc.png) no-repeat center / 100% 100%;
	z-index: 1003;
	-webkit-transition: all 0.5s ease-in;
	-moz-transition: all 0.5s ease-in;
	transition: all 0.5s ease-in;
	.box10 {
		.cont {
			width: 100%;
			height: 100%;
			margin: 0 auto;
			position: relative;
			.couts {
				width: 100%;
				height: 100%;
				.zwsjImg {
					height: 80%;
					margin: 0 auto;
					margin-top: 45px;
				}
			}
			.nfxz {
				position: absolute;
				left: 57.5%;
				top: 12px;
				z-index: 1003;
				width: 100px;
				height: 20px;
				::v-deep .el-input {
					font-size: 12px;
				}
				::v-deep .el-input__inner {
					color: #ccf4ff;
					height: 20px;
					line-height: 20px;
					background: linear-gradient(180deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%),
						rgba(0, 74, 143, 0.4);
					border: 1px solid rgba(0, 162, 255, 0.6);
				}
				::v-deep .el-input__icon {
					line-height: 20px;
				}
			}
			.yfxz {
				position: absolute;
				left: 63.5%;
				top: 12px;
				z-index: 1003;
				width: 100px;
				height: 20px;
				::v-deep .el-input {
					font-size: 12px;
				}
				::v-deep .el-input__inner {
					color: #ccf4ff;
					height: 20px;
					line-height: 20px;
					background: linear-gradient(180deg, rgba(181, 223, 248, 0) 0%, rgba(29, 172, 255, 0.29) 100%, #ffffff 100%),
						rgba(0, 74, 143, 0.4);
					border: 1px solid rgba(0, 162, 255, 0.6);
				}
				::v-deep .el-input__icon {
					line-height: 20px;
				}
			}
			.fhBox {
				width: 80px;
				border: 1px solid rgba(0, 162, 255, 0.6);
				border-radius: 5px;
				position: absolute;
				left: 69.5%;
				top: 12px;
				z-index: 1003;
				font-size: 12px;
				height: 20px;
				color: #7e94a9;
				padding-top: 1px;
				display: flex;
				align-items: center;
				padding: 0 10px;
				cursor: pointer;
				.fh {
					height: 16px;
					margin-right: 4px;
				}
			}
			.fhBox:active {
				color: #ccf4ff;
			}
		}
	}
}
.mkdxMiddle {
	bottom: -325px;
	opacity: 0;
}

.jgzzBox {
	width: 156px;
	height: 163px; // 204
	display: flex;
	flex-direction: column;
	position: absolute;
	left: 980px;
	bottom: 93px;
	z-index: 999;
	background: url(~@/assets/mskx/bg_resource.png) no-repeat center / 100% 100%;

	.jgzzItem {
		width: 153px;
		height: 36px;
		margin: 1px 2px 1px 2px;
		cursor: pointer;

		// background: url(~@/assets/csts/btn11.png) no-repeat;
		img {
			width: 22px;
			height: 22px;
			float: left;
			margin: 7px 3px 0 10px;
		}

		span {
			float: left;
			font-size: 17px;
			font-family: PingFangSC, PingFang SC;
			color: #e9fffe;
			line-height: 36px;
			background: linear-gradient(180deg, #ffffff 0%, #2f8af4 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}
	}

	.jgzzActive {
		width: 153px;
		height: 36px;
		margin: 1px 2px 1px 2px;
		// background: url(~@/assets/csts/btn12.png) no-repeat;
		background: rgba(108, 163, 255, 0.3);
	}
}

.zdcsBox {
	width: 109px;
	height: 248px;
	display: flex;
	flex-direction: column;
	position: absolute;
	left: 1197px;
	bottom: 93px;
	z-index: 999;
	background: url(~@/assets/csts/btnBg12.png) no-repeat center / 100% 100%;

	.jgzzItem {
		width: 103px;
		height: 35px;
		margin: 2px 3px 2px 3px;
		background: url(~@/assets/csts/btn11.png) no-repeat;

		img {
			width: 22px;
			height: 22px;
			float: left;
			margin: 7px 0 0 10px;
		}

		span {
			float: left;
			font-size: 17px;
			font-family: PingFangSC, PingFang SC;
			color: #e9fffe;
			line-height: 35px;
			background: linear-gradient(180deg, #ffffff 0%, #2f8af4 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}
	}

	.jgzzActive {
		width: 156px;
		height: 35px;
		margin: 1px 2px 1px 2px;
		background: url(~@/assets/csts/btn12.png) no-repeat;
	}
}
.tjxxBox {
	width: 800px;
	height: 74px;
	position: absolute;
	top: 124px;
	left: 50%;
	transform: translateX(-50%);
	z-index: 1003;
	.tjxxItem {
		width: 180px;
		height: 74px;
		margin: 0 10px;
		float: left;
		.tjxxName {
			width: 180px;
			height: 36px;
			background: url(~@/assets/bjnj/tjxxBg.png) no-repeat center / 100% 100%;
			font-family: AlibabaPuHuiTi;
			font-size: 18px;
			color: #dff3ff;
			line-height: 36px;
			text-align: center;
			font-style: normal;
		}
		.tjxxNum {
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 26px;
			color: #ffffff;
			line-height: 38px;
			text-shadow: 0px 0px 1px rgba(0, 18, 57, 0.84);
			text-align: center;
			font-style: normal;
			span {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #ffffff;
				line-height: 20px;
				text-shadow: 0px 0px 1px rgba(0, 18, 57, 0.84);
				text-align: left;
				font-style: normal;
			}
		}
	}
}
.mapBtn {
	width: 160px;
	position: absolute;
	top: 201px;
	left: 504px;
	z-index: 1003;
	.mapItem {
		width: 160px;
		height: 40px;
		margin: 8px 0;
		background: url(~@/assets/bjnj/btn1.png) no-repeat center / 100% 100%;
		img {
			vertical-align: middle;
			margin-top: -10px;
		}
		span {
			margin-left: 4px;
			font-family: YouSheBiaoTiHei;
			font-size: 20px;
			color: #ffffff;
			line-height: 40px;
			text-align: left;
			font-style: normal;
			background: linear-gradient(180deg, #ffffff 0%, #009fff 100%);
			background-clip: text;
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			cursor: pointer;
		}
	}
	.mapItem2 {
		width: 160px;
		height: 40px;
		margin: 8px 0;
		background: url(~@/assets/bjnj/btn2.png) no-repeat center / 100% 100%;
		img {
			vertical-align: middle;
			margin-top: -10px;
		}
		span {
			margin-left: 4px;
			font-family: YouSheBiaoTiHei;
			font-size: 20px;
			color: #ffffff;
			line-height: 40px;
			text-align: left;
			font-style: normal;
			background: linear-gradient(180deg, #ffffff 0%, #ffca00 100%);
			background-clip: text;
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			cursor: pointer;
		}
	}
}
.zyfbBox {
	width: 240px;
	height: 120px;
	background: url(~@/assets/bjnj/mapBg.png) no-repeat center / 100% 100%;
	position: absolute;
	top: 220px;
	left: 665px;
	z-index: 1003;
	padding: 3px 0 0 24px;
	.zyfbItem {
		width: 210px;
		height: 36px;
		cursor: pointer;
		img {
			width: 22px;
			height: 22px;
			float: left;
			margin: 6px 8px 0 7px;
		}

		span {
			display: inline-block;
			width: 100%;
			text-align: center;
			float: left;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 16px;
			color: #ffffff;
			line-height: 36px;
			font-style: normal;
		}
	}
	.zyfbItem:hover {
		background: rgba(108, 163, 255, 0.3);
	}
	.jgzzActive {
		background: rgba(108, 163, 255, 0.3);
	}
}
.njfbBox {
	width: 168px;
	height: 336px;
	background: url(~@/assets/bjnj/mapBg2.png) no-repeat center / 100% 100%;
	position: absolute;
	top: 302px;
	left: 665px;
	z-index: 1003;
	padding: 3px 0 0 18px;
	.zyfbItem {
		width: 145px;
		height: 36px;
		cursor: pointer;
		img {
			width: 22px;
			height: 22px;
			float: left;
			margin: 6px 8px 0 7px;
		}

		span {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 16px;
			color: #ffffff;
			line-height: 36px;
			text-align: center;
			font-style: normal;
		}
	}
	.zyfbItem:hover {
		background: rgba(108, 163, 255, 0.3);
	}
	.jgzzActive {
		background: rgba(108, 163, 255, 0.3);
	}
}
.njfbBox2 {
	width: 168px;
	height: 336px;
	background: url(~@/assets/bjnj/mapBg2.png) no-repeat center / 100% 100%;
	position: absolute;
	top: 346px;
	left: 665px;
	z-index: 1003;
	padding: 3px 0 0 18px;
	.zyfbItem {
		width: 145px;
		height: 36px;
		cursor: pointer;
		img {
			width: 22px;
			height: 22px;
			float: left;
			margin: 6px 8px 0 7px;
		}

		span {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 16px;
			color: #ffffff;
			line-height: 36px;
			text-align: center;
			font-style: normal;
		}
	}
	.zyfbItem:hover {
		background: rgba(108, 163, 255, 0.3);
	}
	.jgzzActive {
		background: rgba(108, 163, 255, 0.3);
	}
}

.left,
.right {
	::v-deep {
		.el-carousel {
			width: 450px;
			height: 920px;

			.el-carousel__indicators {
				.el-carousel__indicator {
					&.is-active {
						.el-carousel__button {
							height: 4px;
							background-color: #49e6ff;
						}
					}

					.el-carousel__button {
						height: 4px;
						background-color: rgba(255, 255, 255, 0.4);
						border-radius: 2px;
					}
				}
			}
		}
	}
}

.right {
	::v-deep {
		.el-carousel {
			margin-left: auto;
		}
	}
}

::v-deep .el-carousel--horizontal {
	width: 450px;
	overflow-x: hidden;
}

.map_box {
	position: absolute;
	width: 1920px;
	height: 1080px;
	top: 0px;
	left: 0;
	z-index: 999;
	// border: 1px solid red;
}

.mkss {
	width: 44px;
	height: 44px;
	position: absolute;
	bottom: 735px;
	right: 540px;
	z-index: 999;
	background: url(~@/assets/map/ss2.png) no-repeat center / 100% 100%;

	.mkss2 {
		font-size: 16px;
		color: #fff;
		opacity: 0;
	}
}

.mkss1 {
	width: 44px;
	height: 44px;
	position: absolute;
	bottom: 735px;
	right: 540px;
	z-index: 999;
	background: url(~@/assets/map/ss1.png) no-repeat center / 100% 100%;

	.mkss2 {
		font-size: 16px;
		color: #fff;
		opacity: 0;
	}
}
/* 样式用于控制滚动容器的高度、宽度、边框和滚动效果等 */
.introduce {
	// width: 500px;
	// height: 200px;
	overflow: hidden;
	position: relative;
}

/* 样式用于控制滚动文本的位置和颜色 */
.introduce p {
	position: absolute;
	top: 0;
	left: 0;
	margin: 0;
	padding: 10px;
	font-size: 16px;
}

.njtlBox {
	width: 44px;
	height: 44px;
	position: absolute;
	bottom: 685px;
	right: 539px;
	z-index: 1099;
	img {
		width: 44px;
		height: 44px;
	}
}
.ssBox {
	width: 44px;
	height: 44px;
	position: absolute;
	bottom: 585px;
	right: 539px;
	z-index: 1099;
	img {
		width: 44px;
		height: 44px;
	}
}
.sxBox {
	width: 44px;
	height: 44px;
	position: absolute;
	bottom: 635px;
	right: 539px;
	z-index: 1099;
	img {
		width: 44px;
		height: 44px;
	}
}
.ssPop {
	width: 308px;
	position: absolute;
	top: 401px;
	right: 589px;
	z-index: 1099;
	.ssPop1 {
		width: 308px;
		height: 43px;
		input {
			float: left;
			width: 256px;
			height: 43px;
			background: linear-gradient(180deg, rgba(17, 77, 168, 0) 0%, rgba(36, 132, 212, 0.5) 100%), rgba(2, 52, 114, 0.8);
			box-shadow: inset 0px 0px 2px 0px #57afff;
			border-radius: 2px;
			border: 1px solid #0a5eaa;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 16px;
			color: #c2ddfc;
			line-height: 43px;
			text-shadow: 0px 2px 3px rgba(0, 48, 72, 0.5);
			text-align: left;
			font-style: normal;
			padding-left: 20px;
		}
		.ssImgBox {
			float: left;
			width: 52px;
			height: 43px;
			line-height: 43px;
			background: linear-gradient(180deg, rgba(17, 77, 168, 0) 0%, rgba(36, 132, 212, 0.5) 100%), #1373d9;
			border-radius: 2px;
			border: 1px solid #0a5eaa;
			img {
				vertical-align: middle;
				margin-top: -3px;
			}
		}
	}
	.ssPop2 {
		width: 257px;
		height: 270px;
		background: linear-gradient(180deg, rgba(17, 77, 168, 0) 0%, rgba(36, 132, 212, 0.5) 100%), rgba(2, 52, 114, 0.8);
		box-shadow: inset 0px 0px 2px 0px #57afff;
		border-radius: 2px;
		border: 1px solid #0a5eaa;
		overflow: auto;
		/* 隐藏全局滚动条 */
		&::-webkit-scrollbar {
			display: none;
		}

		.ssList {
			width: 257px;
			height: 45px;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 16px;
			color: #c2ddfc;
			line-height: 40px;
			text-shadow: 0px 2px 3px rgba(0, 48, 72, 0.5);
			text-align: left;
			font-style: normal;
			padding-left: 20px;
		}
	}
}
.njtlList {
	width: 275px;
	height: 196px;
	position: absolute;
	bottom: 305px;
	right: 505px;
	z-index: 1099;
	background: url(~@/assets/bjnj/njtlBg.png) no-repeat center / 100% 100%;
	padding-top: 33px;
	.njtlItem {
		width: 100%;
		height: 38px;
		margin-bottom: 10px;
		padding-left: 35px;
		img {
			width: 31px;
			height: 38px;
			float: left;
		}
		.njtlName {
			float: left;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 14px;
			color: #ffffff;
			line-height: 38px;
			text-align: left;
			font-style: normal;
			margin-left: 16px;
		}
	}
}
.wgsj_fx {
	width: 100%;

	// height: 100%;
	.select_m {
		position: absolute;
		right: 0;
		top: -50px;
		line-height: 30px;
		font-size: 16px;
		font-family: PingFangSC, PingFang SC;
		color: #caecff;
		text-shadow: 0px 0px 1px #00132e;

		::v-deep .el-dropdown button {
			font-family: PingFangSC, PingFang SC;
			font-size: 16px;
			color: #caecff;
			line-height: 21px;
			text-align: center;
			font-style: normal;

			// width: 100px;
			height: 30px;
			// background: url(~@/assets/mskx/icon_drop.png) no-repeat center / 100% 100%;
			background: transparent;
			border: none;
			padding: 0 0 0 5px;
		}

		::v-deep .el-icon-arrow-down:before {
			// content: '\e790' !important;
			color: #caecff;
		}

		::v-deep .el-popper[x-placement^='bottom'] .popper__arrow:after {
			display: none;
		}

		::v-deep .el-popper .popper__arrow:after {
			border-bottom-color: #185493 !important;
		}
	}
}
::v-deep .el-dropdown {
	color: #caecff;
	font-family: YouSheBiaoTiHei;
	font-size: 16px;
	line-height: 21px;
	text-align: right;
	font-style: normal;
	line-height: 40px;
	right: 0;
	position: absolute;
}
::v-deep .el-dropdown-menu {
	// background: #00173b;
	position: absolute;
	left: 0;
	z-index: 10;
	padding: 10px 0;
	margin: 5px 0;
	// border: 1px solid #2b7bbb;
	border-radius: 4px;

	background-color: #c6e2e7 !important;
	border: 1px solid #d2f2ea !important;
	
}
</style>
