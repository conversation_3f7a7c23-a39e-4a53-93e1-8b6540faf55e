<template>
  <div class="ai_waring">
    <div class="title">
      <span>庙湾浜-水质监测</span>
    </div>
    <img @click="closeEmitai" class="close_btn" src="@/assets/shzl/map/close_btn.png" alt="" />
    <p class="warn" v-if="false">警告：数值异常</p>
    <div class="content">
      <div class="top">
        <div class="icon"></div>
        <ul>
          <li>
            <div class="label">信号强度：</div>
            <div class="value">144 RSSI</div>
          </li>
          <li>
            <div class="label">水中导电率：</div>
            <div class="value">2000 us/cm</div>
          </li>
          <li>
            <div class="label">浊度：</div>
            <div class="value">erroor1</div>
          </li>
          <li>
            <div class="label">水中PH：</div>
            <div class="value">5.25 pH</div>
          </li>
          <li>
            <div class="label">溶解氧：</div>
            <div class="value">1.24 mg/L</div>
          </li>
          <li>
            <div class="label">水量：</div>
            <div class="value">26.2 ­°C</div>
          </li>
        </ul>
      </div>
      <div class="mid">
        <div class="item">
          <div class="label">设备地址：</div>
          <div class="value">庙湾浜</div>
        </div>
        <div class="item">
          <div class="label">设备类型：</div>
          <div class="value">水质监测</div>
        </div>
      </div>
      <div class="foot" v-if="false">
        <div class="label">异常记录</div>
        <div class="cont">
          <div class="table_box">
            <SwiperTableWlsb
              :titles="['时间', '监测指标', '阈值', '异常值']"
              :widths="['40%', '20%', '20%', '20%']"
              :data="sjtjData"
              :contentHeight="'90px'"
              :tabelHeight="'30px'"
            ></SwiperTableWlsb>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SwiperTableWlsb from '@/components/shzl/SwiperTableAnomaly.vue'
export default {
  components: { SwiperTableWlsb },
  props: {
    popInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      sjtjData: [
        ['2023-3-23 09:09:00', '烟雾度', '0.5', '0.6'],
        ['2023-3-23 08:09:00', '烟雾度', '0.5', '0.55']
      ],
      aTabIdx: 0
    }
  },
  methods: {
    closeEmitai() {
      this.$emit('closeEmitai')
    }
  }
}
</script>

<style lang="less" scoped>
.ai_waring {
  position: relative;
  width: 586px;
  padding-bottom: 20px;
  background: rgba(0, 23, 59, 0.75);
  box-shadow: 0px 0px 43px 0px rgba(11, 54, 138, 0.35), 0px 3px 16px 0px rgba(0, 0, 0, 0.7),
    inset 0px 0px 16px 0px rgba(17, 146, 214, 0.35);
  border-radius: 11px;
  border: 1px solid #015c8c;
  z-index: 1000;
  .close_btn {
    position: absolute;
    top: 20px;
    right: 25px;
    cursor: pointer;
  }
  .warn {
    position: absolute;
    font-size: 14px;
    font-family: SourceHanSansCN-Normal, SourceHanSansCN;
    font-weight: 400;
    color: #df0404;
    line-height: 21px;
    top: 85px;
    right: 40px;
  }

  .title {
    position: absolute;
    background: url(~@/assets/jcyj/title.png) no-repeat;
    width: 494px;
    height: 97px;
    left: 50%;
    top: -23px;
    transform: translateX(-50%);
    display: grid;
    place-items: center;

    span {
      font-size: 27px;
      font-family: FZSKJW--GB1-0, FZSKJW--GB1;
      font-weight: normal;
      line-height: 36px;
      background: linear-gradient(180deg, #ffffff 0%, #2aebff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-top: 18px;
    }
  }

  .content {
    width: 100%;
    margin-top: 100px;
    padding: 0 52px;
    .top {
      width: 100%;
      display: flex;
      gap: 47px;
      .icon {
        width: 165px;
        height: 217px;
        background: url(~@/assets/jcyj/icon20.png) no-repeat;
      }
      ul {
        padding: 10px 0;
        display: flex;
        flex-direction: column;
        gap: 15px;
        li {
          display: flex;
          gap: 30px;
          text-align: left;
          .label {
            width: 90px;
            font-size: 14px;
            font-family: SourceHanSansCN-Normal, SourceHanSansCN;
            font-weight: 400;
            color: #6a92bb;
            line-height: 21px;
          }
          .value {
            font-size: 14px;
            font-family: SourceHanSansCN-Normal, SourceHanSansCN;
            font-weight: 400;
            color: #00eaff;
            line-height: 21px;
          }
          .active {
            color: #df0404;
          }
        }
      }
    }
    .mid {
      display: flex;
      flex-direction: column;
      padding: 0 40px;
      margin-top: 10px;
      gap: 18px;
      .item {
        display: flex;
        gap: 60px;
        .label {
          width: 73px;
          display: flex;
          font-size: 14px;
          font-family: SourceHanSansCN-Normal, SourceHanSansCN;
          font-weight: 400;
          color: #6a92bb;
          line-height: 21px;
        }
        .value {
          font-size: 14px;
          font-family: SourceHanSansCN-Normal, SourceHanSansCN;
          font-weight: 400;
          color: #ffffff;
          line-height: 21px;
        }
      }
    }
    .foot {
      margin-top: 20px;
      .label {
        text-align: left;
        font-size: 18px;
        font-family: SourceHanSansCN-Normal, SourceHanSansCN;
        font-weight: 400;
        color: #ffffff;
        line-height: 21px;
        margin-bottom: 15px;
      }
    }
  }
}
</style>
