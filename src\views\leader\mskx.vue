<template>
	<div class="leader_box">
		<div class="leader_sjts">
			<!-- <div class="select_head">
        <el-dropdown @command="handleHeadDrop">
          <el-button class="select_" type="primary">
            {{ headName }}
            <i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu class="select_head" slot="dropdown">
            <el-dropdown-item v-for="item in headList" :key="item" :command="item">{{ item }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>-->
			<div class="left" :class="mkdx ? 'mkdxLeft' : ''">
				<div class="left1">
					<BlockBox title="农机投入趋势" class="box box1" :isListBtns="false" :blockHeight="288">
						<div class="wgsj_fx">
							<div class="select_trend">
								<!-- <el-dropdown @command="handleTrendDrop">
									<el-button class="select_" type="primary">
										{{ trendName }}
										<i class="el-icon-arrow-down el-icon--right"></i>
									</el-button>
									<el-dropdown-menu class="select_trend" slot="dropdown">
										<el-dropdown-item v-for="item in trendList" :key="item" :command="item">{{ item }}</el-dropdown-item>
									</el-dropdown-menu>
								</el-dropdown> -->
								<IDateSelect v-show="dateSelectShow" :date="date_" @changeDate="handleInvestmentChangeDate"></IDateSelect>
							</div>
						</div>
						<div class="cout" v-show="njtrData.length > 0">
							<TrendLineChart :options="njtrOptions" :data="njtrData" :init-option="initOptions4" />
						</div>
						<div class="cout" v-show="njtrData.length == 0">
							<img class="zwsjImg" src="@/assets/bjnj/zwsj.png" alt="" />
						</div>
					</BlockBox>
					<BlockBox title="作业面积趋势" class="box box2" :isListBtns="false" :blockHeight="300">
						<div class="wgsj_fx">
							<div class="select_t">
								<!-- <el-dropdown @command="handleDrop">
									<el-button class="select_" type="primary">
										{{ efficiencyName }}
										<i class="el-icon-arrow-down el-icon--right"></i>
									</el-button>
									<el-dropdown-menu class="select_t" slot="dropdown">
										<el-dropdown-item v-for="item in trendList" :key="item" :command="item">{{ item }}</el-dropdown-item>
									</el-dropdown-menu>
								</el-dropdown> -->
								<IDateSelect v-show="dateSelectShow" :date="date_" @changeDate="handleWorkFaceChangeDate"></IDateSelect>
							</div>
						</div>
						<div class="cout" v-show="zymjData.length > 0">
							<TrendLineChart :options="zymjOptions" :data="zymjData" :init-option="initOptions5" />
						</div>
						<div class="cout" v-show="zymjData.length == 0">
							<img class="zwsjImg" src="@/assets/bjnj/zwsj.png" alt="" />
						</div>
					</BlockBox>
					<BlockBox title="作业效率分析" class="box" :isListBtns="false" :blockHeight="340">
						<div class="wgsj_fx">
							<div class="select_s">
								<!-- <el-dropdown @command="wgsjYearMethod">
									<el-button class="select_" type="primary">
										{{ gdCommunityName }}
										<i class="el-icon-arrow-down el-icon--right"></i>
									</el-button>
									<el-dropdown-menu class="select_s" slot="dropdown">
										<el-dropdown-item v-for="item in trendList" :key="item" :command="item">{{ item }}</el-dropdown-item>
									</el-dropdown-menu>
								</el-dropdown> -->
								<IDateSelect v-show="dateSelectShow" :date="date_" @changeDate="handleWorkEfficiencyChangeDate"></IDateSelect>
							</div>
						</div>
						<GroupColChart v-show="barData1.length > 0" :data="barData1" :init-option="initOptions5_1" :options="barOptions" />
						<div class="cout" v-show="barData1.length == 0">
							<img class="zwsjImg" src="@/assets/bjnj/zwsj.png" alt="" />
						</div>
					</BlockBox>
				</div>
			</div>
			<!-- <div class="right" :class="mkdx ? 'mkdxRight' : ''">
				<div class="right1">
					<BlockBox title="机手收获作业面积TOP5" class="box" :isListBtns="false" :blockHeight="970">
						<div class="box4">
							<RealTimeEventQgd
								ref="RealTimeEventQgd"
								contentHeight="866px"
								:data="realTimeEventList"
								@clickRealtimeEvent="clickRealtimeEventQgd"
								@getTrackHistroy="getTrackHistroy"
							/>
						</div>
					</BlockBox>
				</div>
			</div> -->
		</div>
		<div class="map_box">
			<!-- <zlBg @emitMenu="emitMenu" /> -->
			<!-- <svgMap /> -->
			<LeaMap ref="leafletMap" :haveRight="false" @gridClick="gridClick" @operate="operate" @qctc="qctc" @poiClick="poiClick" />
		</div>
		<!-- <div class="dataSource_bottom">数据来源:中国农业大学北斗团队</div> -->
		<!-- <leaderFooter ref="LeaderFooter" :btns="btns" :activaIdx="activaIdx" @mark="mark" /> -->
		<div class="mkss" :class="!mkdx ? 'mkss1' : ''" @click="mkssBtn">
			<div class="mkss2">收缩</div>
		</div>
		<div class="sxBox" @click="sxTk">
			<img src="@/assets/bjnj/sxan.png" alt="" />
		</div>
		<!-- <menuFooter :btns="btns" :activaIdx="activaIdx" @mark="mark" /> -->
		<amachTypeTip v-if="toolTipShow" :name="toolName" :value="agmachTypeCode" :typeOptions="typeOptions" @change="tooltipChange" />
		<zyzxPop v-model="iszyzxShow" :list="zyzxList" @handle="eventInfoBtnClick" />

		<div class="mapBtn">
			<div class="mapItem" :class="mapShow == 1 ? 'mapItem2' : ''" @click="mapActive(1)" v-if="qxShow">
				<img v-if="mapShow == 1" src="@/assets/bjnj/btnImg2.png" alt="" />
				<img v-else src="@/assets/bjnj/btnImg1.png" alt="" />
				<span>作业热点</span>
			</div>
			<div class="mapItem" :class="mapShow == 2 ? 'mapItem2' : ''" @click="mapActive(2)" v-if="qxShow">
				<img v-if="mapShow == 2" src="@/assets/bjnj/btnImg4.png" alt="" />
				<img v-else src="@/assets/bjnj/btnImg3.png" alt="" />
				<span>作业重心</span>
			</div>
		</div>

		<mapPop ref="mapPop" v-model="isMapShow" :njInfoShow="false" />
		<menuFooter :btns="btns2" />
	</div>
</template>

<script>
import BlockBox from '@/components/leader/common/blockBox6.vue'
import RealTimeEventQgd from '@/views/leader/components/mskx/RealTimeEventQgd.vue'
import leaderFooter from '@/components/leader/common/leaderFooter3.vue'
import LeaMap from '@/components/map/LeafletMapNj.vue'
import menuFooter from '@/components/leader/common/leaderFooter2.vue'
import zyzxPop from '@/components/bjnj/zyzxPop.vue' //作业中心
import dayjs from 'dayjs'
import testData from '@/components/map/testData.json'
import { cscpCurrentUserDetails, getCscpBasicHxItemCode, queryWorkAreaTop5 } from '@/api/bjnj/zhdd.js'
import amachTypeTip from '@/views/leader/components/mskx/amachTypeTip.vue'
import mapPop from '@/components/bjnj/mapPop.vue'
import IDateSelect from '@/components/i-month-select/index.vue'
import { getDateAndLastWeek } from '@/utils/index.js'
import {
	getRankTop5,
	getNjCurrent7Count,
	getStatistics,
	importantArea,
	distributionhour,
	locHistory,
	agmachtypeCount,
} from '@/api/njzl/hs.api.js'

export default {
	components: {
		BlockBox,
		RealTimeEventQgd,
		leaderFooter,
		LeaMap,
		menuFooter,
		amachTypeTip,
		zyzxPop,
		mapPop,
		IDateSelect,
	},
	props: {},
	data() {
		return {
			btns2: [],
			qxShow: true,
			mapShow: 1,
			iszyzxShow: false,
			zyzxList: {},
			mkdx: false,
			toolName: '农机类型',
			toolTipShow: true,
			areaIdTop: '',
			areaId: '100000',
			areaFlag: 0,
			areaLevel: '0',
			activaIdx: 2,
			btns: [
				{
					normalBg: require('@/assets/bjnj/btnImg1.png'),
					activeBg: require('@/assets/bjnj/btnImg2.png'),
					name: '作业热点',
				},
				{
					normalBg: require('@/assets/bjnj/btnImg3.png'),
					activeBg: require('@/assets/bjnj/btnImg4.png'),
					name: '作业重心',
				},
			],
			pieData1: [
				// ['product', '人口信息'],
				// ['机耕面积', 86],
				// ['机种面积', 134],
				// ['机收面积', 234]
			],
			realTimeEventList: [],
			barData1: [],
			barOptions: {
				unit: '亩',
				color: ['#4CECAA', '#00F7FF', '#FFD400', '#FF9201', '#DCFF5B', '#00A0FF'],
				stack: false,
				isGradient: true,
				barMaxWidth: 15,
				gradientColors: [
					['#469dcf', '#2F3BED'],
					['#00F7FF', '#2AD4A6'],
					['#FFD400', '#ADCC66'],
					['#FF9201', '#00C692'],
					['#DCFF5B', '#8A9FCF'],
					['#00A0FF', '#ADCC66'],
				],
				isHor: false,
				barGap: '30%',
				tooltip: {
					color: '#fff',
					show: true,
					type: 'shadow',
				},
				xAxis: {
					showLabel: true,
					rotate: 0,
					fontSize: 12,
					padding: [4, 0, 0, 0],
					interval: 0,
					showLine: true,
					showSplitLine: true,
				},
				yAxis: {
					color: '#fff',
					fontSize: 12,
					splitNumber: 6,
					unit: '',
					showSplitLine: true,
					splitLineType: 'dashed',
				},
				legend: {
					icon: 'rect',
					itemWidth: 10,
					align: 'auto',
					orient: 'horizontal',
					padding: 5,
					itemGap: 15,
					textColor: '#fff',
					textfontSize: 12,
				},
			},
			njtrData: [
				// ['product', ''],
				// ['2020', 75],
				// ['2021', 100],
				// ['2022', 125],
				// ['2023', 100],
				// ['2024', 200],
			],
			njtrOptions: {
				smooth: false,
				colors: [['#327348', '#327348']],
			},
			zymjData: [
				// ['product', ''],
				// ['2020', 75],
				// ['2021', 200],
				// ['2022', 85],
				// ['2023', 130],
				// ['2024', 110],
			],
			zymjOptions: {
				smooth: false,
				colors: [['#009BFF', '#0077FF']],
			},
			gdCommunityName: '2023年当期',
			communityList: [1, 2, 3],
			efficiencyName: '2023年当期',
			efficiencyList: [1, 2, 3],
			trendName: '2024年当期',
			trendList: [],
			headName: '2023年当期',
			headList: ['2023年当期', '2023年同期', '2022年同期', '2021年同期', '2020年同期'],
			typeOptions: [],
			agmachTypeCode: '150105',
			areaLevel_init: 0,
			lastAreaLevel: [],
			timer: null,
			isMapShow: false,
			dateSelectShow: true,
			date_: this.getFormattedDate(),
		}
	},
	created() {},
	watch: {},
	computed: {
		initOptions4() {
			return {
				yAxis: {
					name: '台',
					nameTextStyle: {
						padding: [30, 0, 0, 0],
					},
				},
				xAxis: {
					axisLabel: {
						interval: 0,
						// rotate: 40,
						textStyle: {
							fontSize: 12,
							// color: '#000',
						},
					},
				},
				tooltip: {
					formatter: (params) => {
						var relVal = params[0].name
						for (var i = 0, l = params.length; i < l; i++) {
							var unit = '台'
							relVal += '<br/>' + params[i].marker + params[i].value + unit
						}
						return relVal
					},
				},
			}
		},
		initOptions5() {
			return {
				yAxis: {
					name: '亩',
				},
				xAxis: {
					axisLabel: {
						interval: 0,
					},
				},
				tooltip: {
					formatter: (params) => {
						var relVal = params[0].name
						for (var i = 0, l = params.length; i < l; i++) {
							var unit = '亩'
							relVal += '<br/>' + params[i].marker + params[i].value + unit
						}
						return relVal
					},
				},
			}
		},
		initOptions5_1() {
			return {
				yAxis: {
					name: '亩/h',
				},
				xAxis: {
					axisLabel: {
						interval: 0,
					},
				},
				tooltip: {
					formatter: (params) => {
						console.log('params', params)
						var relVal = params[0].name
						for (var i = 0, l = params.length; i < params.length; i++) {
							var unit = '亩/h'
							relVal += '<br/>' + params[i].marker + params[i].value || '' + unit
						}
						return relVal
					},
				},
			}
		},
	},
	mounted() {
		this.cscpCurrentUserDetails()
		for (let i = 0; i < 4; i++) {
			var str = ''
			if (i == 0) {
				str = '年当期'
			} else {
				str = '年同期'
			}
			let year = new Date().getFullYear() - i
			this.trendList.push(year + str)
		}
		// this.communityList = [...this.trendList]
		// this.efficiencyList = [...this.trendList]
		this.trendName = this.trendList[0]
		this.efficiencyName = this.trendList[0]
		this.gdCommunityName = this.trendList[0]
		this.agmachtypelist()
		this.$store.commit('invokerightShow', false)
	},
	methods: {
		getFormattedDate() {
			const currentDate = dayjs()
			let year = currentDate.year()
			let month = currentDate.month() + 1 // 注意月份是从0开始的，所以要+1
			let day = currentDate.date()
			return `${year}-${month}-${day}`
		},
		handleInvestmentChangeDate(dateVal) {
			const { currentDate, lastWeekDate } = getDateAndLastWeek(dateVal)
			this.getNjCurrent7Count(lastWeekDate, currentDate)
		},
		handleWorkFaceChangeDate(dateVal) {
			const { currentDate, lastWeekDate } = getDateAndLastWeek(dateVal)
			this.getStatistics(lastWeekDate, currentDate)
		},
		handleWorkEfficiencyChangeDate(dateVal) {
			const { currentDate, lastWeekDate } = getDateAndLastWeek(dateVal)
			this.getNjxlData(lastWeekDate, currentDate)
		},
		getSevenDate(year) {
			let mm_dd = dayjs()
				.subtract(1, 'days')
				.format('MM-DD') //当天日期前推一天
			let endDate = `${year}-${mm_dd}`
			let startDate = dayjs(endDate)
				.subtract(6, 'days')
				.format('YYYY-MM-DD')
			return {
				startDate,
				endDate,
			}
		},
		eventInfoBtnClick() {
			this.iszyzxShow = false
		},
		poiClick(layerId, it) {
			if (layerId == 'zyzx') {
				this.iszyzxShow = true
				this.zyzxList = it
			}
		},
		mkssBtn() {
			this.mkdx = !this.mkdx
			this.$store.commit('invokerightShow4', this.mkdx)
			// if (this.$store.state.currentIndex == 1) {
			// 	this.$store.commit('invokerightShow', false)
			// }
		},
		sxTk() {
			this.$refs.RealTimeEventQgd.activeIndex = 0
			this.distributionhour()
			this.getStatistics()
			this.getNjxlData()
			this.getNjCurrent7Count()
			// this.getRankTop5()
			this.queryWorkAreaTop5()
		},
		gridClick(properties) {
			this.qxShow = false
			console.log('properties', properties)
			this.areaId = properties.adcode
			if (properties.level == 'province') {
				this.areaLevel = 1
				this.lastAreaLevel.push(this.areaLevel)
			} else if (properties.level == 'city') {
				this.areaLevel = 2
				this.lastAreaLevel.push(this.areaLevel)
			} else if (properties.level == 'district' && this.areaLevel != 3) {
				this.areaLevel = 3
				this.lastAreaLevel.push(this.areaLevel)
			}
			this.$store.commit('invokerightShow2', properties)
			this.activaIdx = -1
			// this.$refs.LeaderFooter.activaIdx = -1;
			this.mapShow = -1
			this.$refs.leafletMap.removeCicleList()
			this.$refs.leafletMap.removeLayer('zyzx')
			this.$refs.leafletMap.removeLayer('heat')
			this.getData()
		},
		getData() {
			this.getStatistics()
			this.getNjxlData()
			this.getNjCurrent7Count()
			// this.getRankTop5()
			this.queryWorkAreaTop5()
		},
		async agmachtypelist() {
			let res = await agmachtypeCount({ dataSources: 1, areaId: '' })
			let typeOptions = res.data
			const moveItemToLast = (array, itemName) => {
				const otherItems = array.filter((item) => item.agmachTypeName !== itemName)
				const itemToMove = array.find((item) => item.agmachTypeName === itemName)
				if (itemToMove) {
					otherItems.push(itemToMove)
					return otherItems
				}
				return array
			}
			typeOptions = moveItemToLast(typeOptions, '其他')
			this.typeOptions = typeOptions
		},
		operate(areaId, areaId2) {
			console.log('areaId', areaId)
			this.$store.commit('invokerightShow3', areaId2)
			if (areaId == '100000') {
				this.qxShow = true
				this.areaId = this.areaIdTop
				this.areaLevel = this.areaLevel_init
			} else {
				this.areaId = areaId
				this.areaLevel = this.$refs.leafletMap.lastAreaCode.indexOf(areaId)
			}
			this.getData()
		},
		qctc() {
			// this.toolTipShow=false;
			this.activaIdx = -1
			this.$refs.LeaderFooter.activaIdx = -1
			this.$refs.LeaderFooter.activeList = []
		},
		async getTrackHistroy(e) {
			let res = await locHistory({
				agmachId: e.agmachId,
				startTime: e.date + ' 00:00:00',
				endTime: e.date + ' 23:59:59',
			})
			// let trackData=[];
			// res.data.map((item,index)=>{
			//   trackData.push([Number(item.lon),Number(item.lat)])
			// })
			// // this.$refs.leafletMap.loadTrackLayer('trcak', trackData)
			// this.isMapShow=true;
			// this.$nextTick(()=>{
			//   this.$refs.mapPop.track(trackData);
			//   this.$refs.mapPop.getNjInfo(infoData)
			// })

			this.isMapShow = true
			this.$nextTick(() => {
				this.$refs.mapPop.track2(res.data)
				this.$refs.mapPop.getNjInfo(e)
			})
		},
		async cscpCurrentUserDetails(data) {
			let res = await cscpCurrentUserDetails(data)
			if (res?.code == '0') {
				this.roleNames = res.data.roleNames
				if (res.data.areaId) {
					this.areaIdTop = res.data.areaId
					this.areaLevel_init = res.data.areaLevel
					this.areaId = res.data.areaId
					this.areaLevel = res.data.areaLevel
					this.$refs.leafletMap.lastAreaCode.push('100000')
					this.lastAreaLevel.push(res.data.areaLevel)
					// if(res.data.areaId!=res.data.areaIdNew){ //区县级别
					//   this.$refs.leafletMap.loadGeoJsonLayerFromUrl(`https://geo.datav.aliyun.com/areas_v3/bound/${res.data.areaId}.json`, 'geojson', false)
					// }else{
					//   this.$refs.leafletMap.loadGeoJsonLayerFromUrl(`https://geo.datav.aliyun.com/areas_v3/bound/${res.data.areaId}_full.json`, 'geojson', res.data.areaLevel==0?true:false)
					// }
					this.distributionhour()
					this.getStatistics()
					this.getNjxlData()
					this.getNjCurrent7Count()
					// this.getRankTop5()
					this.queryWorkAreaTop5()
				}
			}
		},
		mapActive(i) {
			this.$refs.leafletMap.removeCicleList()
			this.$refs.leafletMap.removeLayer('zyzx')
			this.$refs.leafletMap.removeLayer('heat')
			if (i == this.mapShow) {
				this.mapShow = -1
			} else {
				this.mapShow = i
			}
			if (i == 1) {
				this.distributionhour()
			} else if (i == 2) {
				this.importantArea()
			}
		},
		mark(i) {
			this.activaIdx = i
			// this.$refs.leafletMap.removeLayer('heat')
			// this.toolTipShow=false;
			this.$refs.leafletMap.removeCicleList()
			this.$refs.leafletMap.removeLayer('zyzx')
			if (i == 0) {
				// const heatData = testData.heat
				// this.$refs.leafletMap.loadHeatLayer('heat', heatData)
				this.distributionhour()
			} else if (i == 1) {
				// this.toolTipShow=true;
				this.importantArea()
			}
		},
		async distributionhour() {
			let res = await distributionhour({
				time: this.dayjs()
					.subtract(1, 'days')
					.format('YYYY-MM-DD hh:mm:ss'),
				level: 2,
				areaId: this.areaId == '100000' ? '' : this.areaId,
				agmachTypeCode: this.agmachTypeCode,
			})
			let data = res.data.filter((item) => {
				return Number(item.agmachNum) > 0
			})
			// let zyzxData=[];
			// data.map(item=>{
			//   zyzxData.push({
			//     name:item.areaName,
			//     date:item.date||'-',
			//     latlon:[Number(item.latitude),Number(item.longitude)]
			//   })
			// })
			// this.$refs.leafletMap.drawCircle(data.slice(0,40),50000,"rgba(255,215,0)");
			this.$refs.leafletMap.loadHeatLayer('heat', data)
			// this.$refs.leafletMap.loadZyzxLayer('zyzx', zyzxData)
		},
		async importantArea() {
			let res = await importantArea({
				level: 3,
				endDate: this.dayjs()
					.subtract(1, 'days')
					.format('YYYY-MM-DD'),
				startDate: this.dayjs()
					.subtract(7, 'days')
					.format('YYYY-MM-DD'),
				areaId: this.areaId == '100000' ? '' : this.areaId,
				agmachTypeCode: this.agmachTypeCode,
			})
			let data = res.data
			// let data = res.data.filter(item => {
			//   return item.agmachNum > 0
			// })
			let zyzxData = []
			data.map((item) => {
				zyzxData.push({
					name: item.areaName || '',
					date: item.date || '-',
					latlon: [Number(item.latitude), Number(item.longitude)],
				})
			})

			const map = new Map()
			// zyzxData = zyzxData.filter(v => !map.has(v.name) && map.set(v.name, v));
			// this.$refs.leafletMap.drawCircle(data);
			this.$refs.leafletMap.loadZyzxLayer('zyzx', zyzxData)
		},
		tooltipChange(e) {
			this.agmachTypeCode = e
			this.getStatistics()
			this.getNjxlData()
			this.getNjCurrent7Count()
			// this.getRankTop5();
			this.queryWorkAreaTop5()
			// if (this.activaIdx == 0) {
			//   this.distributionhour()
			// }else if(this.activaIdx == 1){
			//   this.importantArea();
			// }
			if (this.mapShow == 1) {
				this.distributionhour()
			} else if (this.mapShow == 2) {
				this.importantArea()
			}
		},
		async getRankTop5(val = 10.0) {
			let params = {
				day: this.dayjs()
					.subtract(1, 'days')
					.format('YYYY-MM-DD'),
				feedRate: val.toFixed(2),
				areaId: this.areaId == '100000' ? '' : this.areaId,
				level: this.areaId == '100000' ? -1 : this.areaLevel,
			}
			this.realTimeEventList = []
			clearTimeout(this.timer)
			let res = await getRankTop5(params)
			if (res.data?.length > 0) {
				this.realTimeEventList = res.data
			}
			// this.timer=setTimeout(()=>{
			//   this.$refs.RealTimeEventQgd.nextHandler();
			// },5000)
		},
		async queryWorkAreaTop5(val = 10.0) {
			let params = {
				day: this.dayjs()
					.subtract(1, 'days')
					.format('YYYY-MM-DD'),
				feedRate: val.toFixed(2),
				areaId: this.areaId == '100000' ? '' : this.areaId,
				level: this.areaId == '100000' ? -1 : this.areaLevel,
			}
			this.realTimeEventList = []
			clearTimeout(this.timer)
			let res = await queryWorkAreaTop5(params)
			if (res.data?.length > 0) {
				this.realTimeEventList = res.data
			}
			// this.timer=setTimeout(()=>{
			//   this.$refs.RealTimeEventQgd.nextHandler();
			// },5000)
		},
		// async getNjCurrent7Count(year = dayjs(new Date()).format('YYYY')) {
		async getNjCurrent7Count(startDate, endDate) {
			let startDate_ = ''
			let endDate_ = ''
			const currentYear = dayjs(new Date()).format('YYYY')
			if (!startDate || !endDate) {
				startDate_ = this.getSevenDate(currentYear).startDate
				endDate_ = this.getSevenDate(currentYear).endDate
			} else {
				startDate_ = startDate
				endDate_ = endDate
			}
			let params = {
				startDate: startDate_,
				endDate: endDate_,
				areaId: this.areaId == '100000' ? '' : this.areaId,
				level: this.areaId == '100000' ? -1 : this.areaLevel,
				agmachTypeCode: this.agmachTypeCode,
			}
			this.njtrData = []
			let res = await getNjCurrent7Count(params)
			if (res.data?.length > 0) {
				this.njtrData = [['product', '']].concat(
					res.data.map((item) => {
						return [this.dayjs(item.date).format('MM-DD'), item.count]
					}),
				)
			}
		},
		async getStatistics(startDate, endDate) {
			let startDate_ = ''
			let endDate_ = ''
			const currentYear = dayjs(new Date()).format('YYYY')
			if (!startDate || !endDate) {
				startDate_ = this.getSevenDate(currentYear).startDate
				endDate_ = this.getSevenDate(currentYear).endDate
			} else {
				startDate_ = startDate
				endDate_ = endDate
			}
			let params = {
				startDate: startDate_,
				endDate: endDate_,
				areaId: this.areaId == '100000' ? '' : this.areaId,
				agmachTypeCode: this.agmachTypeCode,
			}
			this.zymjData = []
			let res = await getStatistics(params)
			if (res.data?.length > 0) {
				this.zymjData = [['product', '']].concat(
					res.data.map((item) => {
						return [this.dayjs(item.date).format('MM-DD'), item.workArea]
					}),
				)
			}
		},
		async getNjxlData(startDate, endDate) {
			let startDate_ = ''
			let endDate_ = ''
			const currentYear = dayjs(new Date()).format('YYYY')
			if (!startDate || !endDate) {
				startDate_ = this.getSevenDate(currentYear).startDate
				endDate_ = this.getSevenDate(currentYear).endDate
			} else {
				startDate_ = startDate
				endDate_ = endDate
			}
			let params = {
				startDate: startDate_,
				endDate: endDate_,
				areaId: this.areaId == '100000' ? '' : this.areaId,
				agmachTypeCode: this.agmachTypeCode,
			}
			this.barData1 = []
			let res = await getStatistics(params)
			if (res.data?.length > 0) {
				this.barData1 = [['product', '']].concat(
					res.data.map((item) => {
						return [this.dayjs(item.date).format('MM-DD'), item.workTime == 0 ? 0 : (item.workArea / item.workTime).toFixed(0)]
					}),
				)
			}
		},
		clickRealtimeEventQgd(item) {
			// this.getRankTop5(item)
			this.queryWorkAreaTop5(item)
		},
		wgsjYearMethod(item) {
			this.gdCommunityName = item
			let num = this.splitNum(item)
			this.getNjxlData(num)
		},
		handleDrop(item) {
			this.efficiencyName = item
			let num = this.splitNum(item)
			this.getStatistics(num)
		},
		handleTrendDrop(item) {
			this.trendName = item
			let num = this.splitNum(item)
			// if (this.areaId == '100000') {
			//   this.getNjCurrent7Count( num)
			// } else {
			//   this.getNjCurrent7Count( num)
			// }
			this.getNjCurrent7Count(num)
		},
		handleHeadDrop(item) {
			this.headName = item
		},
		splitNum(str) {
			let r2 = str.match(/\d/g).join('')
			return r2
		},
	},
}
</script>
<style lang="less" scoped>
@font-face {
	font-family: QuartzRegular;
	src: url(~@/assets/leader/font/QuartzRegular.ttf);
}

@keyframes rotateS {
	0% {
		transform: rotate(0);
	}

	100% {
		transform: rotate(360deg);
	}
}

@keyframes rotateN {
	0% {
		transform: rotate(360deg);
	}

	100% {
		transform: rotate(0);
	}
}

@keyframes rotateY {
	0% {
		transform: rotate(0);
	}

	100% {
		transform: rotateY(360deg);
	}
}

.map_box {
	position: absolute;
	width: 1920px;
	height: 1080px;
	top: 0px;
	left: 0;
	z-index: 999;
	// border: 1px solid red;
	/deep/ .clear2 {
		left: 95% !important;
	}
	/deep/ .back2 {
		left: 95% !important;
	}
}

.leader_sjts {
	width: 1920px;
	height: 1080px;
	position: absolute;
	top: 0;
	display: flex;
	justify-content: space-between;

	.leader_city_box {
		width: 450px;

		.leader_zt_contain {
			height: 100%;
		}
	}

	.left_bg {
		width: 460px;
		height: 1033px;
		background: url(~@/assets/leader/img/component/left_bg.png) no-repeat center / 100% 100%;
		position: absolute;
		left: 50px;
		top: 25px;
		z-index: 102;
	}

	.right_bg {
		width: 520px;
		height: 1033px;
		background: url(~@/assets/leader/img/component/right_bg.png) no-repeat center / 100% 100%;
		position: absolute;
		right: 50px;
		top: 25px;
		z-index: 102;
	}

	.select_head {
		position: absolute;
		left: 600px;
		top: 150px;
		width: 100px;
		height: 30px;
		line-height: 30px;
		font-size: 16px;
		z-index: 1001;
		font-family: PingFangSC, PingFang SC;
		color: #caecff;
		text-shadow: 0px 0px 1px #00132e;

		::v-deep .el-dropdown button {
			width: 180px;
			height: 43px;
			background: url(~@/assets/mskx/bg_select.png) no-repeat center / 100% 100%;
			// background: transparent;
			border: none;
			padding: 0 0 0 5px;

			font-family: PingFangSC, PingFang SC;
			font-size: 16px;
			color: #caecff;
			line-height: 21px;
			text-align: center;
			font-style: normal;
		}

		::v-deep .el-icon-arrow-down:before {
			// content: '\e790' !important;
			color: #caecff;
		}

		::v-deep .el-popper[x-placement^='bottom'] .popper__arrow:after {
			border-bottom-color: #185493 !important;
		}
	}

	.box {
		.cout {
			width: 100%;
			height: 100%;
		}

		.box4 {
			padding: 20px;
		}
	}

	.left {
		width: 450px;
		display: flex;
		justify-content: space-between;
		margin-top: 87px;
		margin-left: 32px;
		position: relative;
		left: 0;
		opacity: 1;
		z-index: 1003;
		-webkit-transition: all 0.5s ease-in;
		-moz-transition: all 0.5s ease-in;
		transition: all 0.5s ease-in;
		.left1 {
			.box {
				margin-bottom: 20px;
			}
		}
	}
	.mkdxLeft {
		left: -450px;
		opacity: 0;
	}

	.right {
		width: 450px;
		display: flex;
		justify-content: space-between;
		margin-top: 87px;
		margin-right: 32px;
		position: relative;
		right: 0;
		opacity: 1;
		z-index: 1003;
		-webkit-transition: all 0.5s ease-in;
		-moz-transition: all 0.5s ease-in;
		transition: all 0.5s ease-in;
	}

	.mkdxRight {
		right: -450px;
		opacity: 0;
	}
	.zwsjImg {
		height: 80%;
		margin: 0 auto;
		margin-top: 36px;
	}
}

.mkss {
	width: 44px;
	height: 44px;
	position: absolute;
	bottom: 735px;
	// right: 540px;
	left: 95%;

	z-index: 999;
	background: url(~@/assets/map/ss2.png) no-repeat center / 100% 100%;

	.mkss2 {
		font-size: 16px;
		color: #fff;
		opacity: 0;
	}
}

.mkss1 {
	width: 44px;
	height: 44px;
	position: absolute;
	bottom: 735px;
	// right: 540px;
	left: 95%;

	z-index: 999;
	background: url(~@/assets/map/ss1.png) no-repeat center / 100% 100%;

	.mkss2 {
		font-size: 16px;
		color: #fff;
		opacity: 0;
	}
}
.sxBox {
	width: 44px;
	height: 44px;
	position: absolute;
	bottom: 685px;
	// right: 539px;
	left: 95%;
	z-index: 1099;
	img {
		width: 44px;
		height: 44px;
	}
}
.wgsj_fx {
	width: 100%;

	// height: 100%;
	.select_s {
		position: absolute;
		right: 0;
		top: -40px;
		line-height: 30px;
		font-size: 16px;
		font-family: PingFangSC, PingFang SC;
		color: #caecff;
		text-shadow: 0px 0px 1px #00132e;

		::v-deep .el-dropdown button {
			font-family: PingFangSC, PingFang SC;
			font-size: 16px;
			color: #caecff;
			line-height: 21px;
			text-align: center;
			font-style: normal;

			// width: 100px;
			height: 30px;
			// background: url(~@/assets/mskx/icon_drop.png) no-repeat center / 100% 100%;
			background: transparent;
			border: none;
			padding: 0 0 0 5px;
		}

		::v-deep .el-icon-arrow-down:before {
			// content: '\e790' !important;
			color: #caecff;
		}

		::v-deep .el-popper[x-placement^='bottom'] .popper__arrow:after {
			display: none;
		}

		::v-deep .el-popper .popper__arrow:after {
			border-bottom-color: #185493 !important;
		}
	}

	.select_t {
		position: absolute;
		right: 0;
		top: -40px;
		line-height: 30px;
		font-size: 16px;
		font-family: PingFangSC, PingFang SC;
		color: #caecff;
		text-shadow: 0px 0px 1px #00132e;

		::v-deep .el-dropdown button {
			// width: 100px;
			height: 30px;
			// background: url(~@/assets/fxyp/icon_drop.png) no-repeat center / 100% 100%;
			background: transparent;
			border: none;
			padding: 0 0 0 5px;

			font-family: PingFangSC, PingFang SC;
			font-size: 16px;
			color: #caecff;
			line-height: 21px;
			text-align: center;
			font-style: normal;
		}

		::v-deep .el-icon-arrow-down:before {
			// content: '\e790' !important;
			color: #caecff;
		}

		::v-deep .el-popper[x-placement^='bottom'] .popper__arrow:after {
			border-bottom-color: #185493 !important;
		}
	}

	.select_trend {
		position: absolute;
		right: 0;
		top: -40px;
		line-height: 30px;
		font-size: 16px;
		font-family: PingFangSC, PingFang SC;
		color: #caecff;
		text-shadow: 0px 0px 1px #00132e;

		::v-deep .el-dropdown button {
			// width: 100px;
			height: 30px;
			// background: url(~@/assets/mskx/bg_select.png) no-repeat center / 100% 100%;
			background: transparent;
			border: none;
			padding: 0 0 0 5px;

			font-family: PingFangSC, PingFang SC;
			font-size: 16px;
			color: #caecff;
			line-height: 21px;
			text-align: center;
			font-style: normal;
		}

		::v-deep .el-icon-arrow-down:before {
			// content: '\e790' !important;
			color: #caecff;
		}

		::v-deep .el-popper[x-placement^='bottom'] .popper__arrow:after {
			border-bottom-color: #185493 !important;
		}
	}
}
.dataSource_bottom {
	position: fixed;
	z-index: 999999;
	bottom: 0;
	height: 107px;
	line-height: 90px;
	left: 500px;
	color: #ffff00;
	font-size: 21px;
	font-family: PingFangSC, PingFang SC;
}
.mapBtn {
	width: 160px;
	position: absolute;
	top: 201px;
	left: 504px;
	z-index: 1003;
	.mapItem {
		width: 160px;
		height: 40px;
		margin: 8px 0;
		background: url(~@/assets/bjnj/btn1.png) no-repeat center / 100% 100%;
		img {
			vertical-align: middle;
			margin-top: -10px;
		}
		span {
			margin-left: 4px;
			font-family: YouSheBiaoTiHei;
			font-size: 20px;
			color: #ffffff;
			line-height: 40px;
			text-align: left;
			font-style: normal;
			background: linear-gradient(180deg, #ffffff 0%, #009fff 100%);
			background-clip: text;
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			cursor: pointer;
		}
	}
	.mapItem2 {
		width: 160px;
		height: 40px;
		margin: 8px 0;
		background: url(~@/assets/bjnj/btn2.png) no-repeat center / 100% 100%;
		img {
			vertical-align: middle;
			margin-top: -10px;
		}
		span {
			margin-left: 4px;
			font-family: YouSheBiaoTiHei;
			font-size: 20px;
			color: #ffffff;
			line-height: 40px;
			text-align: left;
			font-style: normal;
			background: linear-gradient(180deg, #ffffff 0%, #ffca00 100%);
			background-clip: text;
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			cursor: pointer;
		}
	}
}
</style>
