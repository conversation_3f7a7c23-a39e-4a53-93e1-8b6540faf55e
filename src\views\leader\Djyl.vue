<template>
  <div class="leader_box">
    <div class="leader_sjts">
      <div class="left">
        <div class="left1">
          <BlockBox
            title="党建概览"
            subtitle="Party organization information"
            class="box"
            :isListBtns="false"
            :blockHeight="241"
          >
            <ul class="ybsscont">
              <li v-for="(it, index) of ybssData" :key="index">
                <span class="icon" :style="{ background: `url(${it.img}) no-repeat` }"></span>
                <div class="tit">{{ it.tit }}</div>
                <div class="num">
                  <countTo
                    ref="countTo"
                    :startVal="$countTo.startVal"
                    :decimals="$countTo.decimals(it.num)"
                    :endVal="it.num"
                    :duration="$countTo.duration"
                  />
                  <span>{{ it.unit }}</span>
                </div>
              </li>
            </ul>
          </BlockBox>
          <BlockBox
            title="党员画像"
            subtitle="Portraits of party members"
            class="box box1"
            :isListBtns="true"
            :blockHeight="323"
            :textArr="['党龄', '学历', '类型']"
            @updateChange="handleChange"
          >
            <div class="chart_wrap">
              <!-- <PieChart3D type="1" :data="data2.data" :options="data2.options" /> -->
              <PieChart3D
                type="2"
                :data="box2Data"
                :initOption="box2InitOptions"
                :options="box2Options"
              />
              <!-- <div class="sign">
                <div class="man"></div>
                <div class="woman"></div>
                <div class="man_count">674</div>
                <div class="woman_count">3567</div>
              </div> -->
            </div>
          </BlockBox>
          <BlockBox
            title="非公党支部信息"
            subtitle="Non-public Party branch information"
            class="box box3"
            :isListBtns="false"
            :blockHeight="320"
            :showMore="true"
            @handleShowMore="showMoreFn3"
          >
            <div class="cont">
              <swiper-table
                :data="sewageDataList1"
                :titles="['类型', '组织名称', '党支部书记']"
                :widths="['100px', '200px', '138px']"
                content-height="260px"
              />
            </div>
          </BlockBox>
        </div>
      </div>
      <div class="right">
        <div class="right1">
          <BlockBox
            title="党建风采"
            subtitle="Party building style"
            class="box box4"
            :isListBtns="true"
            :blockHeight="213"
            :textArr="['图片', '视频']"
            @updateChange="changeTitleBtn4"
          >
            <div class="cont_img" v-if="djfcCurrent == 0">
              <swiper class="swiper" :options="swiperOption1" ref="myBotSwiper1">
                <swiper-slide v-for="item in 10" :key="item">
                  <div class="img_box">
                    <div class="img_">
                      <!-- <img src="@/assets/djyl/djfc1.png" alt /> -->
                      <el-image
                        style="width: 100%; height: 80%"
                        :src="srcList[0]"
                        :preview-src-list="srcList"
                      ></el-image>
                      <p>杨柳湖社区：党建引领聚合力 结对共建促发展</p>
                    </div>
                    <div class="img_">
                      <!-- <img src="@/assets/djyl/djfc2.png" alt /> -->
                      <el-image
                        style="width: 100%; height: 80%"
                        :src="srcList[1]"
                        :preview-src-list="srcList"
                      ></el-image>
                      <p>东阳社区：坚定初心跟党走 砥砺奋进新征程</p>
                    </div>
                  </div>
                </swiper-slide>
                <div class="swiper-button-prev" slot="button-prev"></div>
                <div class="swiper-button-next" slot="button-next"></div>
              </swiper>
            </div>
            <div class="cont_video" v-if="djfcCurrent == 1">
              <swiper
                class="swiper"
                v-if="djfcCurrent == 1"
                :options="swiperOption1"
                ref="myBotSwiper1"
              >
                <swiper-slide v-for="item in 10" :key="item">
                  <div class="video_box">
                    <div class="video_">
                      <LivePlayer :videoUrl="video2" fluent autoplay live stretch></LivePlayer>
                    </div>
                  </div>
                </swiper-slide>
                <!-- <div class="swiper-pagination" slot="pagination"></div> -->
                <div class="swiper-button-prev" slot="button-prev"></div>
                <div class="swiper-button-next" slot="button-next"></div>
              </swiper>
            </div>
          </BlockBox>
          <BlockBox
            title="党组织信息"
            subtitle="Party organization information"
            class="box box5"
            :isListBtns="false"
            :blockHeight="354"
          >
            <div class="cont">
              <swiper-table
                :data="box6Data"
                :titles="['名称', '类别', '联系电话', '详细地址']"
                :widths="['25%', '15%', '30%', '30%']"
                content-height="260px"
              />
              <!-- <GroupColChart
                :data="box5Data"
                :options="box5Options"
                :init-option="{
                  yAxis: {
                    name: '',
                  },
                }"
              ></GroupColChart> -->
            </div>
          </BlockBox>
          <BlockBox
            title="文化湖熟"
            subtitle="Cultural Lake Cooked"
            class="box box6"
            :isListBtns="false"
            :blockHeight="317"
            :textArr="[]"
            @updateChange="handleChange6"
          >
            <div class="cont">
              <swiper class="swiper" :options="swiperOptionZy" ref="myBotSwiper4">
                <swiper-slide v-for="(item, index) in box7Data" :key="index" >
                  <div class="line">
                    <el-image
                      style="width: 100px; height: 100px; flex-shrink: 0; margin-right: 20px"
                      :src="
                        item.tbAppendixList && item.tbAppendixList.length > 0
                          ? item.tbAppendixList[0].url
                          : ''
                      "
                      :preview-src-list="item.tbAppendixList.map((it) => it.url)"
                    >
                    </el-image>
                    <div @click="showWh(item)">
                      <div class="title" :title="item.articleTitle">{{ item.articleTitle }}</div>
                      <div class="content" :title="item.articleContent">
                        {{ item.articleContent }}
                      </div>
                    </div>
                  </div>
                </swiper-slide>
              </swiper>
              <!-- <swiper-table
                v-if="show6"
                :data="sewageDataList2"
                :titles="['类型', '标题']"
                :widths="['120px', '318px']"
                content-height="260px"
              />
              <ul class="link" v-else>
                <li v-for="(it, i) of links" :key="i">
                  <img :src="it.icon" alt="" />
                </li>
              </ul> -->
            </div>
          </BlockBox>
        </div>
      </div>
    </div>
    <!-- <leaderMiddle /> -->
    <!-- 侧边菜单 -->
    <ZhddAside @marker="marker" :list="leftMenuList" />
    <div class="map_box">
      <LeafletMap ref="leafletMap" @poiClick="showDialog" />
    </div>

    <EventDetail v-if="isShowEventDetail" @close="isShowEventDetail = false" />
    <CountryPart :isShow="isShowCountryPart" @close="closeCountryPart" @check="checkTree" />
    <spjkPop v-if="isXlgcShow" @closeEmit="isXlgcShow = false" @moreXl="isXlgcShow1 = true" />
    <sphsPop v-if="isXlgcShow1" @closeEmit="isXlgcShow1 = false" :leftTreeData="sphsData">
      <template v-slot:title1>视频会商</template>
      <template v-slot:title2>会议接入</template>
      <template v-slot:title3>重置</template>
      <template v-slot:title4>加入会议</template>
    </sphsPop>
    <!-- <spjkPop></spjkPop> -->
    <!-- <txddPop></txddPop> -->
    <wgyPop v-if="isWgllShow" @closeEmit="isWgllShow = false" />
    <dzzPop v-if="isdzzShow" @closeEmit="isdzzShow = false" />
    <xzqyPop v-if="isxzqyShow" @closeEmit="isxzqyShow = false" />
    <zwzxPop v-if="iszwzxShow" @closeEmit="iszwzxShow = false" />
    <zhzxPop v-if="iszhzxShow" @closeEmit="iszhzxShow = false" />
    <csglgdPop v-if="iscsglgdShow" @closeEmit="iscsglgdShow = false" />
    <shaqPop v-if="isshaqShow" @closeEmit="isshaqShow = false" />
    <sthbPop v-if="issthbShow" @closeEmit="issthbShow = false" />
    <whlyPop v-if="iswhlyShow" @closeEmit="iswhlyShow = false" />
    <yjfkPop v-if="isyjfkShow" @closeEmit="isyjfkShow = false" />
    <ggjtPop v-if="isggjtShow" @closeEmit="isggjtShow = false" />
    <zdqyPop v-if="iszdqyShow" @closeEmit="iszdqyShow = false" />
    <xxPop v-if="isxxShow" @closeEmit="isxxShow = false" />
    <wbPop v-if="iswbShow" @closeEmit="iswbShow = false" />
    <ylcsPop v-if="isylcsShow" @closeEmit="isylcsShow = false" />
    <hczPop v-if="ishczShow" @closeEmit="ishczShow = false" />
    <dyyPop v-if="isdyyShow" @closeEmit="isdyyShow = false" />
    <djylPop v-if="djylShow" @closeEmit="djylShow = false" />
    <bjxqPop v-if="bjxqShow" @closeEmit="bjxqShow = false" />
    <Area ref="area" v-show="showArea" :pop-area-info="popAreaInfo"></Area>

    <div class="hkVide_box" v-if="hkVideShow">
      <hlsVideo class="video_" :src="hkTestUrl" :cameraCode="cameraCode"></hlsVideo>
    </div>
    <!-- 基层党委弹窗 -->
    <jcdwPop ref="jcdwPopRef" />
    <!-- 党总支弹窗 -->
    <dzzPop ref="dzzPopRef" />
    <!-- 党支部弹窗 -->
    <dzbPop ref="dzbPopRef" />
    <!-- 党员弹窗 -->
    <dyPop ref="dyPopRef" />
    <PartyBranchInfo v-model="partyBranchInfoShow" />
    <!-- 基础信息 -->
    <BasicInformation
      v-model="basicInfomationShow"
      :title="basicInfomationTitle"
      :list="basicInfomationList"
      :btnShow="basicInfomationBtnShow"
      :btns="basicInfomationBtns"
    />
    <hswhPop :hswhData="hswhData" v-if="isHswhPopShow" @close="isHswhPopShow = false" />
  </div>
</template>

<script>
import testData from '@/assets/json/cyjjPoint'
import BlockBox from '@/components/leader/common/blockBox.vue'
import HeaderMain from '@/components/leader/common/headerMains'
import effectRankBarSwiper from './components/effectRankBar-swiper.vue'
import particle from './components/particle.vue'
import EventDetail from './components/EventDetail.vue'
import CountryPart from './components/CountryPart.vue'
import leaderMiddle from '@/components/leader/common/leaderMiddle.vue'
import gdMap from '@/components/leader/gdMap/gdMap.vue'
import cesiumMap from '@/components/leader/cesiumMap/Cesium3dMap.vue'
import cssjPoint from '@/assets/json/csts/cssjPoint.json'
import spjkPoint from '@/assets/json/csts/spjkPoint.json'
import csbjPoint from '@/assets/json/csts/csbjPoint.json'
import ryllPoint from '@/assets/json/csts/ryllPoint.json'
import xzqhPoint from '@/assets/json/csts/xzqhPoint.json'
import dzzPoint from '@/assets/json/csts/dzzPoint.json'
import zwzxPoint from '@/assets/json/csts/zwzxPoint.json'
import zzzxPoint from '@/assets/json/csts/zzzxPoint.json'
import zdqyPoint from '@/assets/json/csts/zdqyPoint.json'
import xxPoint from '@/assets/json/csts/xxPoint.json'
import wbPoint from '@/assets/json/csts/wbPoint.json'
import ylcsPoint from '@/assets/json/csts/ylcsPoint.json'
import hczPoint from '@/assets/json/csts/hczPoint.json'
import dyyPoint from '@/assets/json/csts/dyyPoint.json'
import LeaderFooter from '@/components/leader/common/leaderFooter.vue'
import myRobot from '@/components/robot/myRobot.vue'
import NumScroll from '@/components/leader/num_scrolls'
import Area from '@/components/leader/leader_csts/area.vue'
import { LandUse, FullFactorGridEvents } from './components/hszl'
import SwiperTable from '@/components/djyl/SwiperTable.vue'
import LivePlayer from '@liveqing/liveplayer'
import hlsVideo from '@/components/leader/hlsVideo'

// 各模块
import ZhddAside from '@/components/djyl/Aside.vue'
import LeafletMap from '@/components/map/LeafletMap2.vue'
import BasicInformation from '@/views/leader/components/zhdd/dialog/BasicInformation.vue'
import hswhPop from '@/components/djyl/hswhPop.vue'

import 'swiper/css/swiper.css'

import { PartyBranchInfo } from './components/aqjg/dialog'

// 接口
import { getCameraMakers, getCameraXq, getWrjMarker } from '@/api/hs/hs.api.js'
import {
  getDgwMap,
  getDzzInfo,
  getDyhxDl,
  getDyhxXl,
  getDyhxLx,
  getFgdzbPage,
  getShyk,
  getDzzxx,
  getWhhs,
} from '@/api/hs/hs.djyl.js'

export default {
  name: 'Djyl',
  components: {
    BlockBox,
    HeaderMain,
    effectRankBarSwiper,
    particle,
    leaderMiddle,
    gdMap,
    cesiumMap,
    LeaderFooter,
    EventDetail,
    CountryPart,
    myRobot,
    Area,
    NumScroll,
    LandUse,
    FullFactorGridEvents,
    SwiperTable,
    LivePlayer,
    ZhddAside,
    LeafletMap,
    hlsVideo,
    PartyBranchInfo,
    BasicInformation,
    hswhPop,
  },
  data() {
    return {
      partyBranchInfoShow: false,
      show6: true,
      links: [
        {
          icon: require('@/assets/djyl/pic1.png'),
        },
        {
          icon: require('@/assets/djyl/pic2.png'),
        },
        {
          icon: require('@/assets/djyl/pic3.png'),
        },
        {
          icon: require('@/assets/djyl/pic4.png'),
        },
        {
          icon: require('@/assets/djyl/pic5.png'),
        },
        {
          icon: require('@/assets/djyl/pic6.png'),
        },
      ],
      man_active: require('@/assets/hszl/man_active.png'),
      man_normal: require('@/assets/hszl/man_normal.png'),
      woman_normal: require('@/assets/hszl/woman_normal.png'),
      woman_active: require('@/assets/hszl/woman_active.png'),
      jgzzShow: false,
      zdcsShow: false,
      djylShow: false,
      bjxqShow: false,
      showArea: false,
      popAreaInfo: {},
      areaPointList: [],
      jgzzActive: 0,
      zdcsActive: 0,
      csglTitleBtns: ['事件', '部件'],
      shbzTitleBtns: ['社会保险', '住房保险'],
      showCesiumMap: false,
      videoUrl: require('@/assets/leader/video/bg1.mp4'),
      // videoUrl: require('@/assets/leader/video/bg.mp4'),
      ybssData: [
        {
          num: 1,
          tit: '党工委数(个)',
          img: require('@/assets/shzl/dzz1.png'),
        },
        {
          num: 11,
          tit: '基层党委数(人)',
          img: require('@/assets/shzl/dzz2.png'),
        },
        {
          num: 11,
          tit: '党总支数(个)',
          img: require('@/assets/shzl/dzz3.png'),
        },
        {
          num: 92,
          tit: '党支部数(个)',
          img: require('@/assets/shzl/dzz4.png'),
        },
      ],
      ybssDataPic: [
        require('@/assets/shzl/dzz1.png'),
        require('@/assets/shzl/dzz2.png'),
        require('@/assets/shzl/dzz3.png'),
        require('@/assets/shzl/dzz4.png'),
      ],
      data1: [
        {
          num: 55.08,
          tit: 'GDP地区生产总值',
          img: require('@/assets/csts/icon.png'),
          url: require('@/assets/hszl/icon1.png'),
          name: '亿元',
        },
        {
          num: 2.34,
          tit: '一般公共预算收入',
          img: require('@/assets/csts/icon.png'),
          url: require('@/assets/hszl/icon2.png'),
          name: '亿元',
        },
        {
          num: 11.09,
          tit: '全社会固定资产投资',
          img: require('@/assets/csts/icon.png'),
          url: require('@/assets/hszl/icon3.png'),
          name: '亿元',
        },
        {
          num: 40.62,
          tit: '规上工业生产总值',
          img: require('@/assets/csts/icon.png'),
          url: require('@/assets/hszl/icon4.png'),
          name: '亿元',
        },
        {
          num: 18.26,
          tit: '社会消费品零售总额',
          img: require('@/assets/csts/icon.png'),
          url: require('@/assets/hszl/icon5.png'),
          name: '元',
        },
        {
          num: 6.64,
          tit: '规上服务物业营业收入',
          img: require('@/assets/csts/icon.png'),
          url: require('@/assets/hszl/icon6.png'),
          name: '元',
        },
      ],

      data4: [
        {
          icon: require('@/assets/hszl/icon8.png'),
          label: '党委',
          count: 11,
        },
        {
          icon: require('@/assets/hszl/icon9.png'),
          label: '党员',
          count: 3810,
        },
        {
          icon: require('@/assets/hszl/icon10.png'),
          label: '党总支',
          count: 17,
        },
        {
          icon: require('@/assets/hszl/icon11.png'),
          label: '直属党组织',
          count: 13,
        },
        {
          icon: require('@/assets/hszl/icon12.png'),
          label: '党支部',
          count: 149,
        },
        {
          icon: require('@/assets/hszl/icon13.png'),
          label: '二级党支部',
          count: 92,
        },
      ],
      data5_1: [
        {
          img: require('@/assets/csts/icon29.png'),
          icon: require('@/assets/csts/icon31.png'),
          tit: '常驻人口',
          num: 64,
          pct: 82.7,
          color: '#00C6FA',
          dotNum: 50,
        },
        {
          img: require('@/assets/csts/icon30.png'),
          icon: require('@/assets/csts/icon32.png'),
          tit: '流动人口',
          num: 32,
          pct: 41.3,
          color: '#3ADCD6',
          dotNum: 50,
        },
      ],
      data5_2: [
        {
          data: {
            label: '人口就业率',
            value: 52,
          },
          options: {
            // 圆环颜色
            color: '#D7BC2F',
            // 圆环背景颜色
            bgColor: 'rgba(0, 52, 100, 1)',
            // 是否顺时针 旋转
            clockwise: true,
            // 圆环端点的形状 ['round', 'square', 'butt']
            strokeLinecap: 'round',
            // 圆环大小 单位是 px
            size: 120,
            // 圆环的宽度 相对单位
            barWidth: 80,
            // 是否开启 轮播动画
            showMotion: true,
            // 动画的 间隔时间
            times: 10000,
            // 圆环类型
            type: 2,
          },
        },
        {
          data: {
            label: '人口老龄化',
            value: 54,
          },
          options: {
            // 圆环颜色
            color: '#28C7FF',
            // 圆环背景颜色
            bgColor: 'rgba(0, 52, 100, 1)',
            // 是否顺时针 旋转
            clockwise: true,
            // 圆环端点的形状 ['round', 'square', 'butt']
            strokeLinecap: 'round',
            // 圆环大小 单位是 px
            size: 140,
            // 圆环的宽度 相对单位
            barWidth: 80,
            // 是否开启 轮播动画
            showMotion: true,
            // 动画的 间隔时间
            times: 10000,
            // 圆环类型
            type: 0,
          },
        },
        {
          data: {
            label: '贫困人口率',
            value: 68,
          },
          options: {
            // 圆环颜色
            color: '#FF8943',
            // 圆环背景颜色
            bgColor: 'rgba(0, 52, 100, 1)',
            // 是否顺时针 旋转
            clockwise: true,
            // 圆环端点的形状 ['round', 'square', 'butt']
            strokeLinecap: 'round',
            // 圆环大小 单位是 px
            size: 140,
            // 圆环的宽度 相对单位
            barWidth: 80,
            // 是否开启 轮播动画
            showMotion: true,
            // 动画的 间隔时间
            times: 10000,
            // 圆环类型
            type: 4,
          },
        },
      ],
      data6: [
        {
          url: require('@/assets/csts/photo1.png'),
          title: '南京鸡鸣寺',
        },
        {
          url: require('@/assets/csts/photo1.png'),
          title: '南京鸡鸣寺',
        },
        {
          url: require('@/assets/csts/photo1.png'),
          title: '南京鸡鸣寺',
        },
      ],
      data7: [
        {
          num: 8,
          tit: '党工委 (个)',
        },
        {
          num: 10,
          tit: '党总支 (个)',
        },
        {
          num: 12,
          tit: '党支部 (个)',
        },
        {
          num: 2468,
          tit: '党员总数 (人)',
        },
      ],
      data2: {
        data: [
          ['product', ''],
          ['30-40年  21.94%', 2456],
          ['20-30年  28.74%', 1200],
          ['10-20年  21.94%', 2000],
          ['10年 7.56%', 1800],
          ['40-50年  9.08%', 2211],
        ],
        options: {
          colors: ['#2EF6FF', '#6D5AE2', '#2B8EF3', '#F7B13F', '#F5616F'],
          alpha: 65,
          pieSize: 220,
          pieInnerSize: 160,
          position: ['50%', '35%'],
          bgImg: {
            top: '40%',
            left: '50%',
          },
          unit: '',
          title: {
            fontSize: '16px',
            top: 70,
            textColor: 'rgba(255, 255, 255, 0)',
          },
          subtitle: {
            fontSize: '14px',
            top: 90,
            textColor: 'rgba(255, 255, 255, 0)',
          },
          legend: {
            // orient: 'vertical',
            // align: 'center',
            // verticalAlign: 'bottom',
            top: 20,
            left: -20,
          },
        },
      },
      data8: {
        data: [
          ['product', ''],
          ['30-40年  21.94%', 2456],
          ['20-30年  28.74%', 1200],
          ['10-20年  21.94%', 2000],
          ['10年 7.56%', 1800],
          ['40-50年  9.08%', 2211],
        ],
        // options: {
        //   colors: ['#2EF6FF', '#E6F973', '#6D5AE2', '#5AE29D', '#FFBA4F', '#E05165'],
        //   alpha: 65,
        //   pieSize: 220,
        //   pieInnerSize: 160,
        //   position: ['35%', '-50%'],
        //   bgImg: {
        //     top: '60%',
        //     left: '37%'
        //   },
        //   unit: '',
        //   title: {
        //     fontSize: '16px',
        //     top: 70,
        //     textColor: 'rgba(255, 255, 255, 0)'
        //   },
        //   subtitle: {
        //     fontSize: '14px',
        //     top: 90,
        //     textColor: 'rgba(255, 255, 255, 0)'
        //   },
        //   legend: {
        //     orient: 'vertical',
        //     align: 'center',
        //     verticalAlign: 'bottom',
        //     top: -10,
        //     left: 160
        //   }
        // }
        options: {
          colors: ['#2EF6FF', '#E6F973', '#6D5AE2', '#5AE29D', '#FFBA4F'],
          alpha: 65,
          pieSize: 220,
          pieInnerSize: 160,
          position: ['50%', '35%'],
          bgImg: {
            top: '40%',
            left: '50%',
          },
          unit: '人',
          title: {
            fontSize: '16px',
            top: 70,
          },
          subtitle: {
            fontSize: '14px',
            top: 90,
          },
        },
      },
      swiperOption: {
        observer: true,
        observeParents: true,
        autoHeight: true,
        spaceBetween: 0,
        autoplay: {
          delay: 3500,
          disableOnInteraction: false,
        },
        slidesPerView: 'auto',
        grabCursor: true,
        autoplayDisableOnInteraction: false,
        mousewheelControl: true,
        pagination: {
          el: '.swiper-pagination',
        },
      },
      data9_1: [
        {
          lab: 'PM2.5',
          num: 22,
          img: require('@/assets/csts/icon16.png'),
        },
        {
          lab: 'SO2',
          num: 2,
          img: require('@/assets/csts/icon17.png'),
        },
        {
          lab: 'CO',
          num: 0.7,
          img: require('@/assets/csts/icon18.png'),
        },
      ],
      data9_2: [
        {
          lab: 'NO2',
          num: 28,
          img: require('@/assets/csts/icon19.png'),
        },
        {
          lab: 'PM10',
          num: 30,
          img: require('@/assets/csts/icon20.png'),
        },
        {
          lab: 'O3',
          num: 64,
          img: require('@/assets/csts/icon21.png'),
        },
      ],
      data10_1: [
        {
          lab: '警员(人)',
          num: 51,
          img: require('@/assets/csts/icon22.png'),
        },
        {
          lab: '辅警(人)',
          num: 549,
          img: require('@/assets/csts/icon23.png'),
        },
        {
          lab: '警车',
          num: 368,
          img: require('@/assets/csts/icon24.png'),
        },
      ],
      data10_2: {
        data: [
          ['product', '事件总数'],
          ['群体性事件', 1245],
          ['恐怖袭击事件', 1245],
          ['刑事案件', 1245],
          ['信息安全事件', 1245],
        ],
        options: {
          colors: ['#80CF83', '#CDCF72', '#0280DF', '#13A4C0'],
          legend: {
            top: 15,
          },
          bgImg: {
            width: '70%',
            height: '70%',
            top: '51%',
            left: '50%',
          },
          unit: '件',
          title: {
            fontSize: '16px',
            top: 70,
          },
          subtitle: {
            fontSize: '14px',
            top: 90,
          },
        },
      },
      data11: [
        ['product', '系列名'],
        ['当前车流量', 23],
        ['当前拥堵路段', 9],
        ['当前违章数', 18],
        ['当前警情', 11],
      ],
      data12: {
        data: [
          ['product', '救援成功', '隐患整改'],
          ['2017', 500, 310],
          ['2018', 310, 102],
          ['2019', 320, 190],
          ['2020', 350, 176],
          ['2021', 360, 290],
          ['2022', 250, 161],
        ],
        options: {
          // 颜色数据
          color: ['#ffd11a', 'rgba(46,200,207,1)'],
          // 柱子的宽度
          barWidth: 20,
          // 柱子X轴位置
          symbolOffsetX: ['-30%', '30%'],
          // 顶部的大小
          symbolSize: [20, 11],
        },
      },
      activaIdx: -1,
      isShowEventDetail: false,
      isShowCountryPart: false,
      isXlgcShow: false,
      isXlgcShow1: false,
      isWgllShow: false,
      isdzzShow: false,
      isxzqyShow: false,
      iszwzxShow: false,
      iszhzxShow: false,
      iscsglgdShow: false,
      isshaqShow: false,
      issthbShow: false,
      iswhlyShow: false,
      isyjfkShow: false,
      isggjtShow: false,
      iszdqyShow: false,
      isxxShow: false,
      iswbShow: false,
      isylcsShow: false,
      ishczShow: false,
      isdyyShow: false,
      sewageDataList1: [
        ['集体企业', '南京市雁塔区枫林华府铭蓝幼儿园支部委员会', '王刚'],
        ['集体企业', '南京市高陵区玉祥天然气有限公司', '魏雪红'],
        ['其他企业', '中共衡正国际工程咨询有限公司党支部', '白富琨'],
        ['其他企业', '高陵区绪昌面粉有限公司党支部', '李海鹏'],
        ['其他企业', '南京蓝剑保安服务有限公司党支部', '韩梅'],
        ['其他企业', '南京中惠建筑工程公司党支部', '周晓宇'],
        ['集体企业', '中共南京市雁塔区枫林华府铭蓝幼儿园支部委员会', '王刚'],
        ['集体企业', '南京市高陵区玉祥天然气有限公司', '魏雪红'],
        ['其他企业', '中共衡正国际工程咨询有限公司党支部', '白富琨'],
        ['其他企业', '高陵区绪昌面粉有限公司党支部', '李海鹏'],
        ['其他企业', '南京蓝剑保安服务有限公司党支部', '韩梅'],
        ['其他企业', '南京中惠建筑工程公司党支部', '周晓宇'],
      ],
      sewageDataList2: [
        ['党务公开', '湖熟街道召开2022年度基层党组织书记抓基层党建述职评议会议'],
        ['党务公开', '湖熟街道召开2022—2023年度党员冬训动员大会'],
        ['党风建设', '湖熟街道党员冬训进行时，有“声”更有“深”！'],
        ['党风建设', '学习党的二十大精神 奋进担当向未来'],
        ['党风建设', '湖熟街道开展庆祝建党百年主题党日活动'],
        ['党务公开', '湖熟街道召开2022年度基层党组织书记抓基层党建述职评议会议'],
        ['党务公开', '湖熟街道召开2022—2023年度党员冬训动员大会'],
        ['党风建设', '湖熟街道党员冬训进行时，有“声”更有“深”！'],
        ['党风建设', '学习党的二十大精神 奋进担当向未来'],
        ['党风建设', '湖熟街道开展庆祝建党百年主题党日活动'],
      ],
      swiperOption1: {
        loop: false,
        // slidesPerView: 'auto',
        autoplay: {
          // 这个参数很重要,当外力干扰轮播图时，它能够继续实现自我滚动
          disableOnInteraction: false,
          delay: 5000, //5秒切换一次
        },
        pagination: {
          el: '.swiper-pagination',
          clickable: true,
          clickableClass: 'my-pagination-clickable',
        },
        // 设置点击箭头
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
        },
      },
      video1: require('@/assets/videos/video1.mp4'),
      video2: require('@/assets/djyl/video/djfc.mp4'),
      video8: require('@/assets/videos/video8.mp4'),
      hkTestUrl: 'https://service.uavyun.cn:10801/hls/stream_2/stream_2_live.m3u8',
      hkVideShow: false,
      cameraCode: null,
      box5Data: [
        // ['product', '党员支部大会', '支部委员会', '党小组会', '党课'],
        // ['湖熟社区', 200, 160, 140, 160],
        // ['河南社区', 140, 90, 180, 90],
        // ['和进社区', 102, 105, 120, 105],
        // ['龙都社区', 200, 90, 130, 90],
        // ['周岗社区', 150, 200, 150, 200],
        // ['徐幕社区', 200, 90, 130, 90],
      ],
      box6Data: [],
      box7Data: [],
      box5Options: {
        color: ['#01FF6C', '#8300D7', '#FF9201', '#00A3D7FF'],
        stack: true,
        isGradient: true,
        barMaxWidth: 10,
        gradientColors: [
          ['#01FF6C', '#00ED8B'],
          ['#8300D7', '#7800ED'],
          ['#FF9201', '#EDB000'],
        ],
        isHor: false,
        barGap: '30%',
        tooltip: {
          color: '#fff',
          show: true,
          type: 'shadow',
        },
      },
      leftMenuList: [
        {
          bg: require('@/assets/aside/bg1.png'),
          bgActive: require('@/assets/aside/bg2.png'),
          icon: require('@/assets/djyl/map/icon1.png'),
          iconActive: require('@/assets/djyl/map/icon1_active.png'),
          label: '党工委',
          active: false,
        },
        {
          bg: require('@/assets/aside/bg1.png'),
          bgActive: require('@/assets/aside/bg2.png'),
          icon: require('@/assets/djyl/map/icon2.png'),
          iconActive: require('@/assets/djyl/map/icon2_active.png'),
          label: '党总支',
          active: false,
        },
        // {
        //   bg: require('@/assets/aside/bg1.png'),
        //   bgActive: require('@/assets/aside/bg2.png'),
        //   icon: require('@/assets/djyl/map/icon3.png'),
        //   iconActive: require('@/assets/djyl/map/icon3_active.png'),
        //   label: '党支部',
        //   active: false,
        // },
        {
          bg: require('@/assets/aside/bg1.png'),
          bgActive: require('@/assets/aside/bg2.png'),
          icon: require('@/assets/djyl/map/icon4.png'),
          iconActive: require('@/assets/djyl/map/icon4_active.png'),
          label: '党委',
          active: false,
        },
      ],
      jcdwMarks1: [],
      jcdwMarks2: [],
      jcdwMarks3: [],
      jcdwMarks4: [],
      cameraMarkers: [],
      sphsData: [
        { id: '4000', label: '和进网格员1' },
        { id: '4001', label: '和进网格员2' },
        { id: '4002', label: '和进网格员3' },
        { id: '4003', label: '和进网格员4' },
        { id: '4004', label: '和进网格员5' },
        { id: '4005', label: '和进网格员6' },
      ],
      jcdwPopShow: false,
      djfcCurrent: 0,
      srcList: [require('@/assets/djyl/djfc1.png'), require('@/assets/djyl/djfc2.png')],
      basicInfomationShow: false,
      basicInfomationTitle: '',
      basicInfomationList: [],
      basicInfomationBtnShow: false,
      basicInfomationBtns: [],
      box2Options: {
        unit: '人',
        colors: ['#6BCBB9', '#BF609E', '#15ECFF', '#A6A535', '#7B57B3'],
        title: {
          top: 70,
        },
        subtitle: {
          top: 90,
        },
      },
      box2Data: [],
      box2InitOptions: {
        legend: {
          left: -20,
          // itemWidth: 8,
          labelFormatter: function () {
            return (
              '<span style="color:#fff;fontSize:14px">' +
              this.name +
              ' ' +
              '<span style="color:#fff;fontSize:14px">' +
              this.y +
              '人' +
              '</span>' +
              ' ' +
              '<span style="color:#fff;fontSize:14px">' +
              Math.round(this.percentage) +
              '%' +
              '</span>'
            )
          },
        },
      },
      swiperOptionZy: {
        // loop: true,
        direction: 'vertical',
        autoplay: {
          // 这个参数很重要,当外力干扰轮播图时，它能够继续实现自我滚动
          disableOnInteraction: false,
          delay: 5000, //5秒切换一次
        },
        loop: false,
        // direction: 'vertical',
        slidesPerView: '2',
        // height: 715,
        // autoplay: {
        //   disableOnInteraction: false,
        //   delay: 5000 //5秒切换一次
        // },
      },
      isHswhPopShow: false,
      hswhData: {},
    }
  },
  created() {
    // this.watchFlag()
  },
  mounted() {
    this.getDzzInfoFn() //党组织
    this.getDyhxDlFn() //企业画像
    this.getFgdzbPageFn() //非公党支部
    // this.getShykFn() //三会一课
    this.getDzzxxFn() //党组织信息
    this.getWhhsFn() //文化湖熟
  },
  // watch: {
  //   '$route.params'(newval, oldval) {
  //     this.watchFlag()
  //   },
  // },
  computed: {
    formatNumber() {
      return function (param) {
        console.log(param)
        if (this.isEmpty(param) || param.length < 4 || isNaN(param)) {
          return param
        }

        let small = ''
        let isSmall = false
        if (param.split('.').length === 2) {
          // 有小数
          isSmall = true
          small = param.split('.')[1]
          param = param.split('.')[0]
        }

        let result = ''

        while (param.length > 3) {
          result = ',' + param.slice(-3) + result
          param = param.slice(0, param.length - 3)
        }

        if (param) {
          result = param + result
        }

        if (isSmall) {
          result = result + '.' + small
        }
        console.log(result)
        return result
      }
    },
  },
  methods: {
    handleChange(i) {
      if (i === 0) {
        this.getDyhxDlFn()
      } else if (i === 1) {
        this.getDyhxXlFn()
      } else if (i === 2) {
        this.getDyhxLxFn()
      }
    },
    handleChange6(i) {
      console.log(i)
      i === 0 ? (this.show6 = true) : (this.show6 = false)
    },
    watchFlag() {
      let flag = this.$route.query.flag?.substr(0, 4)
      this.djylShow = false
      this.iscsglgdShow = false
      switch (flag) {
        case 'djyl':
          this.djylShow = true
          break
        case 'csgl':
          this.iscsglgdShow = true
          break
        default:
          break
      }
    },
    csglBulletFrame(val) {
      this.iscsglgdShow = val
    },
    shaqBulletFrame(val) {
      this.isshaqShow = val
    },
    sthbBulletFrame(val) {
      this.issthbShow = val
    },
    djylBulletFrame(val) {
      this.djylShow = val
    },
    whlyBulletFrame(val) {
      this.iswhlyShow = val
    },
    yjfkBulletFrame(val) {
      this.isyjfkShow = val
    },
    ggjtBulletFrame(val) {
      this.isggjtShow = val
    },
    jgzz(index) {
      this.isWgllShow = false
      this.isdzzShow = false
      this.isxzqyShow = false
      this.iszwzxShow = false
      this.iszhzxShow = false
      this.isShowEventDetail = false
      this.isXlgcShow = false
      this.iszdqyShow = false
      this.isxxShow = false
      this.iswbShow = false
      this.isylcsShow = false
      this.ishczShow = false
      this.isdyyShow = false
      this.bjxqShow = false
      this.jgzzActive = index
      this.$refs.CesiumMapRef.removeAllLayer() //移除所有点位
      if (index == 0) {
        // 地图打点
        let data = xzqhPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'xzqhPoint')
      } else if (index == 1) {
        // 地图打点
        let data = dzzPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'dzzPoint')
      } else if (index == 2) {
        // 地图打点
        let data = zwzxPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'zwzxPoint')
      } else if (index == 3) {
        // 地图打点
        let data = zzzxPoint.features.map((e) => {
          return {
            position: e.geometry.coordinates.concat(100),
            properties: e.properties,
            img: e.img,
          }
        })
        this.$refs.CesiumMapRef.loadPoints1(data, 'zzzxPoint')
      }
    },

    closeCountryPart() {
      this.isShowCountryPart = false
      if (this.activaIdx === 3) this.activaIdx = -1
    },
    checkTree(e) {
      console.log(e)
      // 地图打点
      let data = csbjPoint.features.map((e) => {
        return {
          position: e.geometry.coordinates.concat(100),
          properties: e.properties,
          img: e.img,
        }
      })
      this.$refs.CesiumMapRef.loadPoints1(data, 'csbjPoint')
    },
    marker(val, i) {
      // console.log(11111, val, i)
      if (i + 1 == 1 && val) {
        this.mapMarker1()
      } else if (i + 1 == 1 && !val) {
        this.cleaerMapMarker('dgwType')
      }
      if (i + 1 == 2 && val) {
        this.mapMarker2()
      } else if (i + 1 == 2 && !val) {
        this.cleaerMapMarker('dzzType')
      }
      // if (i + 1 == 3 && val) {
      //   this.mapMarker3()
      // } else if (i + 1 == 3 && !val) {
      //   this.cleaerMapMarker('dzbType')
      // }
      if (i + 1 == 3 && val) {
        this.mapMarker3()
      } else if (i + 1 == 3 && !val) {
        this.cleaerMapMarker('dwType')
      }
    },
    setMapArrayofPoint(points, iconUrl, layerId) {
      const data = points.map((it, i) => ({
        latlng: [it.latitude || 0, it.longitude || 0],
        icon: {
          iconUrl: iconUrl,
          iconSize: [32, 42],
          iconAnchor: [16, 42],
          html: '123',
        },
        props: {
          id: it.id,
          name: it.name || '-',
          type: layerId,
        },
        info: it,
      }))
      return data
    },
    mapMarker(data, layerId) {
      this.$refs.leafletMap.drawPoiMarker(data, layerId, true, false, false)
    },
    async mapMarker1() {
      let res = await getDgwMap(0)
      if (res?.code == '200') {
        let data = this.setMapArrayofPoint(
          res.result,
          require('@/assets/djyl/map/jcdw1_map.png'),
          'dgwType'
        )
        this.mapMarker(data, 'dgwType')
      }
      // this.jcdwMarks1 = [
      //   {
      //     latlng: [31.852411, 118.986739],
      //     icon: {
      //       iconUrl: require('@/assets/djyl/map/jcdw1_map.png'),
      //       iconSize: [32, 42],
      //       iconAnchor: [16, 42],
      //     },
      //     props: {
      //       id: 'jcdwId',
      //       type: 'dgwType',
      //     },
      //   },
      //   {
      //     latlng: [31.852378, 118.986309],
      //     icon: {
      //       iconUrl: require('@/assets/djyl/map/jcdw1_map.png'),
      //       iconSize: [32, 42],
      //       iconAnchor: [16, 42],
      //     },
      //     props: {
      //       id: 'jcdwId',
      //       type: 'dgwType',
      //     },
      //   },
      //   {
      //     latlng: [31.863897, 118.969401],
      //     icon: {
      //       iconUrl: require('@/assets/djyl/map/jcdw1_map.png'),
      //       iconSize: [32, 42],
      //       iconAnchor: [16, 42],
      //     },
      //     props: {
      //       id: 'jcdwId',
      //       type: 'dgwType',
      //     },
      //   },
      // ]
      // this.$refs.leafletMap.drawPoiMarker(this.jcdwMarks1, 'dgwType', true)
    },
    async mapMarker2() {
      let res = await getDgwMap(1)
      if (res?.code == '200') {
        let data = this.setMapArrayofPoint(
          res.result,
          require('@/assets/djyl/map/jcdw2_map.png'),
          'dzzType'
        )
        this.mapMarker(data, 'dzzType')
      }
      // this.jcdwMarks2 = [
      //   {
      //     latlng: [31.873373, 118.973006],
      //     icon: {
      //       iconUrl: require('@/assets/djyl/map/jcdw2_map.png'),
      //       iconSize: [32, 42],
      //       iconAnchor: [16, 42],
      //     },
      //     props: {
      //       id: 'dzzId',
      //       type: 'dzzType',
      //     },
      //   },
      //   {
      //     latlng: [31.883741, 119.016546],
      //     icon: {
      //       iconUrl: require('@/assets/djyl/map/jcdw2_map.png'),
      //       iconSize: [32, 42],
      //       iconAnchor: [16, 42],
      //     },
      //     props: {
      //       id: 'dzzId',
      //       type: 'dzzType',
      //     },
      //   },
      //   {
      //     latlng: [31.895522, 118.975544],
      //     icon: {
      //       iconUrl: require('@/assets/djyl/map/jcdw2_map.png'),
      //       iconSize: [32, 42],
      //       iconAnchor: [16, 42],
      //     },
      //     props: {
      //       id: 'dzzId',
      //       type: 'dzzType',
      //     },
      //   },
      // ]
      // this.$refs.leafletMap.drawPoiMarker(this.jcdwMarks2, 'dzzType', true)
    },
    async mapMarker3() {
      let res = await getDgwMap(3)
      if (res?.code == '200') {
        let data = this.setMapArrayofPoint(
          res.result,
          require('@/assets/djyl/map/jcdw3_map.png'),
          'dwType'
        )
        this.mapMarker(data, 'dwType')
      }
      // this.jcdwMarks3 = [
      //   {
      //     latlng: [31.862144, 118.971976],
      //     icon: {
      //       iconUrl: require('@/assets/djyl/map/jcdw3_map.png'),
      //       iconSize: [32, 42],
      //       iconAnchor: [16, 42],
      //     },
      //     props: {
      //       id: 'dzbId',
      //       type: 'dzbType',
      //     },
      //   },
      //   {
      //     latlng: [31.865986, 118.980902],
      //     icon: {
      //       iconUrl: require('@/assets/djyl/map/jcdw3_map.png'),
      //       iconSize: [32, 42],
      //       iconAnchor: [16, 42],
      //     },
      //     props: {
      //       id: 'dzbId',
      //       type: 'dzbType',
      //     },
      //   },
      //   {
      //     latlng: [31.855427, 118.985752],
      //     icon: {
      //       iconUrl: require('@/assets/djyl/map/jcdw3_map.png'),
      //       iconSize: [32, 42],
      //       iconAnchor: [16, 42],
      //     },
      //     props: {
      //       id: 'dzbId',
      //       type: 'dzbType',
      //     },
      //   },
      // ]
      // this.$refs.leafletMap.drawPoiMarker(this.jcdwMarks3, 'dzbType', true)
    },
    mapMarker4() {
      // this.cameraMarker()
      // this.wrjMarker()
      this.jcdwMarks4 = [
        {
          latlng: [31.853666, 118.994463],
          icon: {
            iconUrl: require('@/assets/djyl/map/jcdw4_map.png'),
            iconSize: [32, 42],
            iconAnchor: [16, 42],
          },
          props: {
            id: 'dyId',
            type: 'dwType',
          },
        },
        {
          latlng: [31.840088, 118.967255],
          icon: {
            iconUrl: require('@/assets/djyl/map/jcdw4_map.png'),
            iconSize: [32, 42],
            iconAnchor: [16, 42],
          },
          props: {
            id: 'dyId',
            type: 'dwType',
          },
        },
        {
          latlng: [31.86233, 118.98027],
          icon: {
            iconUrl: require('@/assets/djyl/map/jcdw4_map.png'),
            iconSize: [32, 42],
            iconAnchor: [16, 42],
          },
          props: {
            id: 'dyId',
            type: 'dwType',
          },
        },
      ]
      this.$refs.leafletMap.drawPoiMarker(this.jcdwMarks4, 'dwType', true)
    },
    cleaerMapMarker(type) {
      // 清除标记点
      console.log(1111)
      this.$refs.leafletMap.removeLayer(type)
    },
    closeAllPop() {
      this.$refs.jcdwPopRef.closeFn()
      this.$refs.dzzPopRef.closeFn()
      this.$refs.dzbPopRef.closeFn()
      this.$refs.dyPopRef.closeFn()
    },
    // 地图弹窗
    showDialog(layerId, it) {
      const id = it.props.id
      const info = it.info
      console.log(layerId, id)
      switch (layerId) {
        case 'dgwType':
          this.basicInfomationTitle = '党工委'
          break
        case 'dzzType':
          this.basicInfomationTitle = '党总支'
          break
        case 'dwType':
          this.basicInfomationTitle = '党委'
          break
        default:
          break
      }
      this.basicInfomationList = [
        {
          label: '名称：',
          value: info.name,
        },
        {
          label: '地址：',
          value: info.address,
        },
        {
          label: '介绍：',
          value: info.synopsis,
        },
      ]
      this.basicInfomationBtnShow = false
      this.basicInfomationShow = true
    },
    async cameraMarker() {
      const res = await getCameraMakers()
      // console.log('res', res)
      if (res && res.length > 0) {
        this.cameraMarkers = res.map((it) => ({
          latlng: [it.latitude || 0, it.longitude || 0],
          icon: {
            iconUrl: require('@/assets/map/point/point1.png'),
            iconSize: [32, 42],
            iconAnchor: [16, 42],
          },
          props: {
            id: it.id,
            type: 'hkcamera',
          },
        }))
        this.$refs.leafletMap.drawPoiMarker(this.cameraMarkers, 'hkcamera', true)
      } else {
        this.$message({
          message: '未查询到事件信息',
          type: 'warning',
        })
      }
    },
    async cameraXq(id) {
      // console.log('id', id)
      const res = await getCameraXq(id)
      // console.log('res', res.body.data[0].url)
      if (res && res.body && res.body.data && res.body.data.length > 0 && res.body.data[0].url) {
        this.hkTestUrl = res.body.data[0].url
        this.cameraCode = id //传给hls组件，当视频播放失败时再次调用
        this.hkVideShow = true
      }
    },
    changeTitleBtn4(val) {
      console.log(111, val)
      this.djfcCurrent = val
    },
    showMoreFn3() {
      console.log(333)
      this.partyBranchInfoShow = true
    },
    // wrjMarker(){
    //    const res = await getWrjMarker()
    // }
    async getDzzInfoFn() {
      let res = await getDzzInfo()
      if (res?.code == '200') {
        this.ybssData = []
        this.ybssData = res.result.map((it, i) => {
          return {
            num: Number(it.sysvalue),
            tit: it.name,
            img: this.ybssDataPic[i],
          }
        })
      }
    },
    async getDyhxDlFn() {
      let res = await getDyhxDl()
      if (res?.code == '200') {
        this.box2Data = []
        this.box2Data = [
          ['product', ''],
          ...res.result.map((it, i) => [it.name, Number(it.sysvalue)]),
        ]
      }
    },
    async getDyhxXlFn() {
      let res = await getDyhxXl()
      if (res?.code == '200') {
        this.box2Data = []
        this.box2Data = [
          ['product', ''],
          ...res.result.map((it, i) => [it.name, Number(it.sysvalue)]),
        ]
      }
    },
    async getDyhxLxFn() {
      let res = await getDyhxLx()
      if (res?.code == '200') {
        this.box2Data = []
        this.box2Data = [
          ['product', ''],
          ...res.result.map((it, i) => [it.name, Number(it.sysvalue)]),
        ]
      }
    },
    async getFgdzbPageFn() {
      let res = await getFgdzbPage('', 1, 10)
      if (res?.code == '200') {
        this.sewageDataList1 = []
        this.sewageDataList1 = res.result.data.map((it, i) => [it.type, it.name, it.secretaryName])
      }
    },
    async getShykFn() {
      let res = await getShyk()
      if (res?.code == '200') {
        this.box5Data = []
        this.box5Data = [
          ['product', '党员支部大会', '支部委员会', '党小组会', '党课'],
          ...res.result.map((it, i) => [
            it.name,
            Number(it.value1),
            Number(it.value2),
            Number(it.value3),
            Number(it.value4),
          ]),
        ]
      }
    },
    async getDzzxxFn() {
      let res = await getDzzxx()
      if (res?.code == '200') {
        this.box6Data = []
        this.box6Data = res.result.data
          .slice(0, 20)
          .map((it, i) => [it.name, it.type, it.tel || '-', it.address])
      }
    },
    async getWhhsFn() {
      let res = await getWhhs()
      if (res?.code == '200') {
        this.box7Data = []
        this.box7Data = res.result.data
      }
    },
    showWh(item) {
      this.hswhData = item
      this.isHswhPopShow = true
    },
  },
}
</script>

<style lang="less" scoped>
@font-face {
  font-family: QuartzRegular;
  src: url(~@/assets/leader/font/QuartzRegular.ttf);
}
@keyframes rotateS {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes rotateN {
  0% {
    transform: rotate(360deg);
  }
  100% {
    transform: rotate(0);
  }
}
@keyframes rotateY {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotateY(360deg);
  }
}

.leader_sjts {
  width: 1920px;
  height: 1080px;
  position: absolute;
  top: 0;
  display: flex;
  justify-content: space-between;
  .leader_city_box {
    width: 450px;
    .leader_zt_contain {
      height: 100%;
    }
  }
  .box {
    .cont {
      width: 100%;
      height: 100%;
    }

    .ybsscont {
      height: 100%;
      padding: 14px 44px 24px 37px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: space-between;
      li {
        position: relative;
        width: 165px;
        height: 66px;
        background: url(~@/assets/shzl/ybss_bg.png) no-repeat;
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: left;
        padding-left: 92px;
        .icon {
          position: absolute;
          width: 26px;
          height: 26px;
          top: 10px;
          left: 31px;
          animation: move infinite 3s ease-in-out;
          @keyframes move {
            0% {
              transform: translateY(0px) rotateY(0);
            }
            50% {
              transform: translateY(-10px) rotateY(180deg);
            }
            100% {
              transform: translateY(0px) rotateY(360deg);
            }
          }
        }
        &:nth-child(2) {
          span {
            animation-delay: 0.3s;
          }
        }
        &:nth-child(3) {
          span {
            animation-delay: 0.6s;
          }
        }
        &:nth-child(4) {
          span {
            animation-delay: 0.9s;
          }
        }
        &:nth-child(5) {
          span {
            animation-delay: 1.2s;
          }
        }
        &:nth-child(6) {
          span {
            animation-delay: 1.5s;
          }
        }
        .tit {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 20px;
          white-space: nowrap;
        }
        .num {
          font-size: 18px;
          font-family: PingFangSC, PingFang SC;
          font-weight: normal;
          color: #ffffff;
          line-height: 22px;
        }
      }
    }
    .wrapper4 {
      width: 100%;
      height: 100%;
      padding: 20px 0;
      ul {
        width: 100%;
        height: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;
        li {
          display: flex;
          height: 49px;
          gap: 10px;
          .icon {
            width: 46px;
            height: 49px;
          }
          .info {
            margin-top: -6px;
            .label {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 20px;
              text-align: left;
              padding-left: 5px;
            }
            .line {
              width: 87px;
              height: 6px;
              background: url('~@/assets/hszl/line1.png') no-repeat;
            }
            .count {
              text-align: left;
              margin-top: 4px;
              width: 87px;
              height: 22px;
              line-height: 22px;
              background: linear-gradient(96deg, #1d4975 0%, rgba(19, 56, 87, 0.24) 100%);
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #a8b1b9;
              padding-left: 5px;
              span {
                font-size: 18px;
                font-family: PingFangSC, PingFang SC;
                font-weight: normal;
                color: #ffffff;
                line-height: 22px;
                margin-right: 3px;
              }
            }
          }
        }
      }
    }
  }
  .box1 {
    .cont {
      padding: 13px 0 23px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: space-between;
      li {
        position: relative;
        width: 222px;
        height: 61px;
        padding: 10px 7px;
        display: flex;
        background: url(~@/assets/hszl/bg2.png) no-repeat;
        .icon {
          width: 41px;
          height: 41px;
          background: url(~@/assets/hszl/bg3.png) no-repeat;
          font-size: 17px;
          img {
            animation: move infinite 3s ease-in-out;
            @keyframes move {
              0% {
                transform: rotateY(0);
              }
              50% {
                transform: rotateY(180deg);
              }
              100% {
                transform: rotateY(360deg);
              }
            }
          }
          .name {
            font-size: 10px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 11px;
            text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
          }
        }
        .line {
          position: absolute;
          left: 50px;
          top: 5px;
          width: 1px;
          height: 55px;
          border: 1px solid;
          border-image: linear-gradient(
              180deg,
              rgba(0, 83, 171, 0),
              rgba(0, 140, 213, 0.6),
              rgba(0, 83, 171, 0)
            )
            1 1;
        }
        .desc {
          position: relative;
          margin-left: 15px;
          text-align: left;
          padding-left: 18px;
          .xsj {
            position: absolute;
            width: 10px;
            height: 10px;
            left: 0;
            top: 7px;
            background: url(~@/assets/csts/xsj1.png) no-repeat;
          }
          .num {
            font-size: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 24px;
          }
          .tit {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 20px;
            white-space: nowrap;
          }
        }
      }
    }
    .chart_wrap {
      position: relative;
      width: 100%;
      height: 250px;
      .sign {
        position: absolute;
        left: 105px;
        top: 25px;
        .woman {
          position: absolute;
          width: 27px;
          height: 57px;
          left: 60px;
          top: -10px;
          background: url('~@/assets/hszl/woman.png') no-repeat;
        }
        .man {
          position: absolute;
          width: 23px;
          height: 57px;
          left: 166px;
          top: 45px;
          background: url('~@/assets/hszl/man.png') no-repeat;
        }
        .man_count {
          position: absolute;
          left: 64px;
          top: 72px;
          width: 109px;
          height: 31px;
          font-size: 20px;
          font-family: PingFangSC, PingFang SC;
          font-weight: normal;
          color: #ffffff;
          line-height: 24px;
        }
        .woman_count {
          position: absolute;
          left: 70px;
          top: 24px;
          width: 109px;
          height: 31px;
          font-size: 20px;
          font-family: PingFangSC, PingFang SC;
          font-weight: normal;
          color: #ffffff;
          line-height: 24px;
        }
      }
    }
  }
  .box2 {
    .cont {
      padding: 14px 28px 24px;
      display: flex;
      gap: 5px;
      .cont_item {
        position: relative;
        width: 128px;
        height: 176px;
        padding: 7px 15px 0 16px;
        box-shadow: inset 0px 1px 12px 2px rgba(44, 165, 235, 0.2);
        .icon {
          width: 97px;
          height: 99px;
        }
        .lzdx {
          width: 97px;
          height: 99px;
          position: absolute;
          top: 7px;
          left: 16px;
        }
        .tit {
          position: absolute;
          top: 97px;
          left: 50%;
          width: 100%;
          transform: translateX(-50%);
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #caecff;
          line-height: 20px;
          text-shadow: 0px 0px 1px #00132e;
        }
        ol {
          width: 100%;
          height: 40px;
          margin-top: 21px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          padding-left: 20px;
          & > li {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: normal;
            color: #ffffff;
            line-height: 22px;
            text-align: left;
            list-style: disc;
            &::marker {
              color: #38deff;
            }
            .unit {
              font-size: 12px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #caecff;
              line-height: 17px;
              text-shadow: 0px 0px 1px #00132e;
            }
          }
        }
      }
    }
  }
  .box3 {
    .cont {
      padding: 23px 15px 0 7px;
      overflow: hidden;
    }
  }
  .box4 {
    .cont_img {
      // border: 1px solid red;
      padding: 10px 0 10px 0;
      .img_box {
        display: flex;
        justify-content: space-between;
        padding: 0 40px;
        .img_ {
          width: 185px;
          height: 140px;
          flex-shrink: 0;
        }
        // img {
        //   width: 100%;
        //   height: 80%;
        // }
        p {
          color: #fff;
          position: relative;
          z-index: 99;
          font-size: 14px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          top: 4px;
        }
      }
    }
    .cont_video {
      // border: 1px solid red;
      padding: 10px 0 10px 0;
      .video_box {
        display: flex;
        justify-content: center;
        .video_ {
          width: 290px;
          height: 150px;
          flex-shrink: 0;
        }
        video {
          width: 290px;
          height: 150px;
          object-fit: fill;
        }
      }
    }
  }

  .box5 {
    .cont {
      padding: 10px 2px 6px;
      // border: 1px solid red;
    }
  }
  .box6 {
    .cont {
      padding: 20px 15px 0 7px;
      overflow: hidden;
      // :deep(.swiper-slide) {
      //   height: 120px;
      //   border-bottom: 1px dashed #979797;
      //   box-sizing: border-box;
      //   padding-top: 14px;
      //   &:last-child {
      //     border-bottom: none;
      //   }
      // }
      .swiper {
        width: 100%;
        height: 100%;
      }
      .line {
        height: 120px;
        display: flex;
        justify-content: space-between;
        // img {
        //   width: 100px;
        //   height: 100px;
        //   flex-shrink: 0;
        //   margin-right: 20px;
        // }
        .title {
          font-size: 18px;
          color: #fff;
          text-align: left;
          font-weight: 600;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 310px;
          white-space: nowrap;
        }
        .content {
          font-size: 16px;
          color: #eee;
          text-align: left;
          height: 74px;
          white-space: pre-line;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
        }
      }
    }
  }
  .left_bg {
    width: 460px;
    height: 1033px;
    background: url(~@/assets/leader/img/component/left_bg.png) no-repeat center / 100% 100%;
    position: absolute;
    left: 50px;
    top: 25px;
    z-index: 102;
  }
  .right_bg {
    width: 520px;
    height: 1033px;
    background: url(~@/assets/leader/img/component/right_bg.png) no-repeat center / 100% 100%;
    position: absolute;
    right: 50px;
    top: 25px;
    z-index: 102;
  }
  .left {
    width: 450px;
    display: flex;
    justify-content: space-between;
    margin-top: 123px;
    margin-left: 50px;
    position: relative;
    z-index: 1003;
  }
  .right {
    width: 450px;
    margin-right: 60px;
    // border: 1px solid red;
    display: flex;
    justify-content: space-between;
    margin-top: 136px;
    position: relative;
    z-index: 1003;
  }
  .map_box {
    position: absolute;
    width: 1920px;
    height: 970px;
    top: 100px;
    left: 0;
  }
}
.jgzzBox {
  width: 109px;
  height: 170px;
  display: flex;
  flex-direction: column;
  position: absolute;
  left: 525px;
  bottom: 93px;
  z-index: 999;
  background: url(~@/assets/csts/btnBg11.png) no-repeat center / 100% 100%;
  .jgzzItem {
    width: 103px;
    height: 35px;
    margin: 2px 3px 2px 3px;
    background: url(~@/assets/csts/btn11.png) no-repeat;
    img {
      float: left;
      margin: 7px 0 0 10px;
    }
    span {
      float: left;
      font-size: 17px;
      font-family: PingFangSC, PingFang SC;
      color: #e9fffe;
      line-height: 35px;
      background: linear-gradient(180deg, #ffffff 0%, #2f8af4 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .jgzzActive {
    width: 105px;
    height: 37px;
    margin: 1px 2px 1px 2px;
    background: url(~@/assets/csts/btn12.png) no-repeat;
  }
}
.zdcsBox {
  width: 109px;
  height: 248px;
  display: flex;
  flex-direction: column;
  position: absolute;
  left: 1197px;
  bottom: 93px;
  z-index: 999;
  background: url(~@/assets/csts/btnBg12.png) no-repeat center / 100% 100%;
  .jgzzItem {
    width: 103px;
    height: 35px;
    margin: 2px 3px 2px 3px;
    background: url(~@/assets/csts/btn11.png) no-repeat;
    img {
      float: left;
      margin: 7px 0 0 10px;
    }
    span {
      float: left;
      font-size: 17px;
      font-family: PingFangSC, PingFang SC;
      color: #e9fffe;
      line-height: 35px;
      background: linear-gradient(180deg, #ffffff 0%, #2f8af4 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .jgzzActive {
    width: 105px;
    height: 37px;
    margin: 1px 2px 1px 2px;
    background: url(~@/assets/csts/btn12.png) no-repeat;
  }
}
.left,
.right {
  ::v-deep {
    .el-carousel {
      width: 450px;
      height: 920px;
      .el-carousel__indicators {
        .el-carousel__indicator {
          &.is-active {
            .el-carousel__button {
              height: 4px;
              background-color: #49e6ff;
            }
          }
          .el-carousel__button {
            height: 4px;
            background-color: rgba(255, 255, 255, 0.4);
            border-radius: 2px;
          }
        }
      }
    }
  }
}
.right {
  ::v-deep {
    .el-carousel {
      margin-left: auto;
    }
  }
}
::v-deep .el-carousel--horizontal {
  width: 450px;
  overflow-x: hidden;
}

/*先去掉默认样式*/
.swiper-button-prev:after {
  display: none;
}
.swiper-button-next:after {
  display: none;
}

/*再自定义样式*/
.swiper-button-prev {
  display: inline-block;
  width: 26px;
  height: 32px;
  background: url(~@/assets/djyl/btn-pre.png) no-repeat;
  bottom: 15px;
}
.swiper-button-next {
  display: inline-block;
  width: 26px;
  height: 32px;
  background: url(~@/assets/djyl/btn-next.png) no-repeat;
  bottom: 15px;
}

/deep/ .video-js .vjs-control {
  width: 2em;
}
/deep/ .video-js .vjs-volume-panel.vjs-volume-panel-vertical {
  width: 0em;
}

.hkVide_box {
  position: absolute;
  left: 600px;
  width: 600px;
  height: 600px;
  top: 300px;
  z-index: 999;
  .video_ {
    width: 300px;
    height: 300px;
  }
}
.link {
  padding: 0 26px;
  height: 250px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-content: space-between;
  li {
    width: 183px;
    height: 75px;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
